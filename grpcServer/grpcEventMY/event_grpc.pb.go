// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcEventMY

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	eventMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/eventMessage"
	users "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// EventClient is the client API for Event service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EventClient interface {
	TrackingCleverTapPayComboSuccess(ctx context.Context, in *eventMessage.TrackingCleverTapPayComboMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapPayComboPaymentError(ctx context.Context, in *eventMessage.TrackingCleverTapPayComboMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerAcceptTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerCancelTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerWithdrawTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerDoneTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerRating(ctx context.Context, in *eventMessage.TrackingCleverTapRatingMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerFinishTraining(ctx context.Context, in *eventMessage.TrackingCleverTapTrainingMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	AddAskerDoneBookingReferralGift(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*empty.Empty, error)
	AddAskerSignUpReferralGift(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskExpired(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
}

type eventClient struct {
	cc grpc.ClientConnInterface
}

func NewEventClient(cc grpc.ClientConnInterface) EventClient {
	return &eventClient{cc}
}

func (c *eventClient) TrackingCleverTapPayComboSuccess(ctx context.Context, in *eventMessage.TrackingCleverTapPayComboMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventMY.Event/TrackingCleverTapPayComboSuccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventClient) TrackingCleverTapPayComboPaymentError(ctx context.Context, in *eventMessage.TrackingCleverTapPayComboMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventMY.Event/TrackingCleverTapPayComboPaymentError", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventClient) TrackingCleverTapTaskerAcceptTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventMY.Event/TrackingCleverTapTaskerAcceptTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventClient) TrackingCleverTapTaskerCancelTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventMY.Event/TrackingCleverTapTaskerCancelTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventClient) TrackingCleverTapTaskerWithdrawTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventMY.Event/TrackingCleverTapTaskerWithdrawTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventClient) TrackingCleverTapTaskerDoneTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventMY.Event/TrackingCleverTapTaskerDoneTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventClient) TrackingCleverTapTaskerRating(ctx context.Context, in *eventMessage.TrackingCleverTapRatingMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventMY.Event/TrackingCleverTapTaskerRating", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventClient) TrackingCleverTapTaskerFinishTraining(ctx context.Context, in *eventMessage.TrackingCleverTapTrainingMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventMY.Event/TrackingCleverTapTaskerFinishTraining", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventClient) AddAskerDoneBookingReferralGift(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventMY.Event/AddAskerDoneBookingReferralGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventClient) AddAskerSignUpReferralGift(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventMY.Event/AddAskerSignUpReferralGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventClient) TrackingCleverTapTaskExpired(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventMY.Event/TrackingCleverTapTaskExpired", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EventServer is the server API for Event service.
// All implementations must embed UnimplementedEventServer
// for forward compatibility
type EventServer interface {
	TrackingCleverTapPayComboSuccess(context.Context, *eventMessage.TrackingCleverTapPayComboMessage) (*empty.Empty, error)
	TrackingCleverTapPayComboPaymentError(context.Context, *eventMessage.TrackingCleverTapPayComboMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerAcceptTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerCancelTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerWithdrawTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerDoneTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerRating(context.Context, *eventMessage.TrackingCleverTapRatingMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerFinishTraining(context.Context, *eventMessage.TrackingCleverTapTrainingMessage) (*empty.Empty, error)
	AddAskerDoneBookingReferralGift(context.Context, *users.Users) (*empty.Empty, error)
	AddAskerSignUpReferralGift(context.Context, *users.Users) (*empty.Empty, error)
	TrackingCleverTapTaskExpired(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	mustEmbedUnimplementedEventServer()
}

// UnimplementedEventServer must be embedded to have forward compatible implementations.
type UnimplementedEventServer struct {
}

func (UnimplementedEventServer) TrackingCleverTapPayComboSuccess(context.Context, *eventMessage.TrackingCleverTapPayComboMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapPayComboSuccess not implemented")
}
func (UnimplementedEventServer) TrackingCleverTapPayComboPaymentError(context.Context, *eventMessage.TrackingCleverTapPayComboMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapPayComboPaymentError not implemented")
}
func (UnimplementedEventServer) TrackingCleverTapTaskerAcceptTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerAcceptTask not implemented")
}
func (UnimplementedEventServer) TrackingCleverTapTaskerCancelTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerCancelTask not implemented")
}
func (UnimplementedEventServer) TrackingCleverTapTaskerWithdrawTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerWithdrawTask not implemented")
}
func (UnimplementedEventServer) TrackingCleverTapTaskerDoneTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerDoneTask not implemented")
}
func (UnimplementedEventServer) TrackingCleverTapTaskerRating(context.Context, *eventMessage.TrackingCleverTapRatingMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerRating not implemented")
}
func (UnimplementedEventServer) TrackingCleverTapTaskerFinishTraining(context.Context, *eventMessage.TrackingCleverTapTrainingMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerFinishTraining not implemented")
}
func (UnimplementedEventServer) AddAskerDoneBookingReferralGift(context.Context, *users.Users) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAskerDoneBookingReferralGift not implemented")
}
func (UnimplementedEventServer) AddAskerSignUpReferralGift(context.Context, *users.Users) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAskerSignUpReferralGift not implemented")
}
func (UnimplementedEventServer) TrackingCleverTapTaskExpired(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskExpired not implemented")
}
func (UnimplementedEventServer) mustEmbedUnimplementedEventServer() {}

// UnsafeEventServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EventServer will
// result in compilation errors.
type UnsafeEventServer interface {
	mustEmbedUnimplementedEventServer()
}

func RegisterEventServer(s *grpc.Server, srv EventServer) {
	s.RegisterService(&_Event_serviceDesc, srv)
}

func _Event_TrackingCleverTapPayComboSuccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapPayComboMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventServer).TrackingCleverTapPayComboSuccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventMY.Event/TrackingCleverTapPayComboSuccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventServer).TrackingCleverTapPayComboSuccess(ctx, req.(*eventMessage.TrackingCleverTapPayComboMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _Event_TrackingCleverTapPayComboPaymentError_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapPayComboMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventServer).TrackingCleverTapPayComboPaymentError(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventMY.Event/TrackingCleverTapPayComboPaymentError",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventServer).TrackingCleverTapPayComboPaymentError(ctx, req.(*eventMessage.TrackingCleverTapPayComboMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _Event_TrackingCleverTapTaskerAcceptTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventServer).TrackingCleverTapTaskerAcceptTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventMY.Event/TrackingCleverTapTaskerAcceptTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventServer).TrackingCleverTapTaskerAcceptTask(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _Event_TrackingCleverTapTaskerCancelTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventServer).TrackingCleverTapTaskerCancelTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventMY.Event/TrackingCleverTapTaskerCancelTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventServer).TrackingCleverTapTaskerCancelTask(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _Event_TrackingCleverTapTaskerWithdrawTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventServer).TrackingCleverTapTaskerWithdrawTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventMY.Event/TrackingCleverTapTaskerWithdrawTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventServer).TrackingCleverTapTaskerWithdrawTask(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _Event_TrackingCleverTapTaskerDoneTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventServer).TrackingCleverTapTaskerDoneTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventMY.Event/TrackingCleverTapTaskerDoneTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventServer).TrackingCleverTapTaskerDoneTask(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _Event_TrackingCleverTapTaskerRating_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapRatingMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventServer).TrackingCleverTapTaskerRating(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventMY.Event/TrackingCleverTapTaskerRating",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventServer).TrackingCleverTapTaskerRating(ctx, req.(*eventMessage.TrackingCleverTapRatingMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _Event_TrackingCleverTapTaskerFinishTraining_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTrainingMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventServer).TrackingCleverTapTaskerFinishTraining(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventMY.Event/TrackingCleverTapTaskerFinishTraining",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventServer).TrackingCleverTapTaskerFinishTraining(ctx, req.(*eventMessage.TrackingCleverTapTrainingMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _Event_AddAskerDoneBookingReferralGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(users.Users)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventServer).AddAskerDoneBookingReferralGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventMY.Event/AddAskerDoneBookingReferralGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventServer).AddAskerDoneBookingReferralGift(ctx, req.(*users.Users))
	}
	return interceptor(ctx, in, info, handler)
}

func _Event_AddAskerSignUpReferralGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(users.Users)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventServer).AddAskerSignUpReferralGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventMY.Event/AddAskerSignUpReferralGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventServer).AddAskerSignUpReferralGift(ctx, req.(*users.Users))
	}
	return interceptor(ctx, in, info, handler)
}

func _Event_TrackingCleverTapTaskExpired_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventServer).TrackingCleverTapTaskExpired(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventMY.Event/TrackingCleverTapTaskExpired",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventServer).TrackingCleverTapTaskExpired(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

var _Event_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcEventMY.Event",
	HandlerType: (*EventServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TrackingCleverTapPayComboSuccess",
			Handler:    _Event_TrackingCleverTapPayComboSuccess_Handler,
		},
		{
			MethodName: "TrackingCleverTapPayComboPaymentError",
			Handler:    _Event_TrackingCleverTapPayComboPaymentError_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerAcceptTask",
			Handler:    _Event_TrackingCleverTapTaskerAcceptTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerCancelTask",
			Handler:    _Event_TrackingCleverTapTaskerCancelTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerWithdrawTask",
			Handler:    _Event_TrackingCleverTapTaskerWithdrawTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerDoneTask",
			Handler:    _Event_TrackingCleverTapTaskerDoneTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerRating",
			Handler:    _Event_TrackingCleverTapTaskerRating_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerFinishTraining",
			Handler:    _Event_TrackingCleverTapTaskerFinishTraining_Handler,
		},
		{
			MethodName: "AddAskerDoneBookingReferralGift",
			Handler:    _Event_AddAskerDoneBookingReferralGift_Handler,
		},
		{
			MethodName: "AddAskerSignUpReferralGift",
			Handler:    _Event_AddAskerSignUpReferralGift_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskExpired",
			Handler:    _Event_TrackingCleverTapTaskExpired_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "event.proto",
}
