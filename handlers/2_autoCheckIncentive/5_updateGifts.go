package autoCheckIncentive

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.mongodb.org/mongo-driver/bson"
)

func updateGifts(incentiveIds []string) {
	if len(incentiveIds) == 0 {
		return
	}
	// Update gift to inactive
	globalDataAccess.UpdateAllByQuery(
		globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE],
		bson.M{
			"_id":     bson.M{"$in": incentiveIds},
			"from":    globalConstant.GIFT_SOURCE_SYSTEM_WITH_PARTNER,
			"partner": "UR_BOX",
			"status":  "ACTIVE",
		},
		bson.M{
			"$set": bson.M{
				"status":    "INACTIVE",
				"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
				"updatedBy": "SYNCEDRON",
			},
			"$unset": bson.M{"isSkipAutoInactive": 1},
		},
	)
}
