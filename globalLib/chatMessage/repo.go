package chatMessage

import (
	"strings"
	"time"

	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

type Repo interface {
	GetChatConversationWithMessages(isoCode string, query, fields bson.M) (*modelChatMessage.ChatMessage, error)
	GetChatConversation(isoCode string, query, fields bson.M) (*modelChatMessage.ChatMessage, error)
	GetChatConversations(isoCode string, query, fields bson.M) ([]*modelChatMessage.ChatMessage, error)
	GetChatMessages(isoCode string, query, fields bson.M) (messages []*modelChatMessage.ChatMessageMessages, err error)
	GetChatMessage(isoCode string, query, fields bson.M) (message *modelChatMessage.ChatMessageMessages, err error)
	GetChatMessageMap(isoCode string, query, fields bson.M) (message map[string]interface{}, err error)
	GetChatMessagesSort(isoCode string, query, fields, sort bson.M) (messages []*modelChatMessage.ChatMessageMessages, err error)
	GetChatMessagesSkipLimitSort(isoCode string, query, fields bson.M, skip, limit int64, sort bson.M) (messages []*modelChatMessage.ChatMessageMessages, err error)
	UpdateChatMessages(isoCode string, query, fields bson.M) (int64, error)
	UpdateChatMessage(isoCode string, query, fields bson.M) (int64, error)
	UpdateChatConversation(isoCode string, query, fields bson.M) (int64, error)
	SendMessageToConversation(isoCode string, chatId string, messages []*modelChatMessage.ChatMessageMessages) error
	SendMessageMapToConversation(isoCode string, chatId string, messages []map[string]interface{}) error
	CreateChatConversation(isoCode string, chatMessage *modelChatMessage.ChatMessage) error
	DeleteChatMessages(isoCode string, query bson.M) error
	GetChatMessagesPagingSort(isoCode string, query, fields bson.M, page, limit int64, sort bson.M) ([]*modelChatMessage.ChatMessage, error)
	InsertChatMessage(isoCode string, chatMessage interface{}) error
	CountMessagesByQuery(isoCode string, query bson.M) (int, error)
	SendMessageToConversations(isoCode string, chatIds []string, messages []*modelChatMessage.ChatMessageMessages) error
	CreateFirstMessageForFavChat(isoCode string, asker, tasker *modelUsers.Users) (string, error)
	SendMessageToFavChat(params FavMessageParams) (*modelChatMessage.ChatMessageMessages, error)
}

type FavMessageParams struct {
	Task     *modelTask.Task
	Title    *modelService.ServiceText
	Text     *modelService.ServiceText
	SendTo   string
	IsoCode  string
	TimeZone *time.Location
	Actions  []*modelChatMessage.ChatMessageMessagesMessageBySystemActions
	ChatId   string
	Status   string
}

var REPO_BY_ISO_CODE = map[string]Repo{
	globalConstant.ISO_CODE_VN:   rVN,
	globalConstant.ISO_CODE_TH:   rTH,
	globalConstant.ISO_CODE_INDO: rINDO,
	globalConstant.ISO_CODE_MY:   rMY,
}

// Deprecated: Use GetChatConversation and GetChatMessages instead
// NOTE: Support get chat task conversation with message. Old version
func GetChatConversationWithMessages(isoCode string, query, fields bson.M) (*modelChatMessage.ChatMessage, error) {
	return REPO_BY_ISO_CODE[isoCode].GetChatConversationWithMessages(isoCode, query, fields)
}

func GetChatConversation(isoCode string, query, fields bson.M) (*modelChatMessage.ChatMessage, error) {
	return REPO_BY_ISO_CODE[isoCode].GetChatConversation(isoCode, query, fields)
}

func GetChatConversations(isoCode string, query, fields bson.M) ([]*modelChatMessage.ChatMessage, error) {
	return REPO_BY_ISO_CODE[isoCode].GetChatConversations(isoCode, query, fields)
}

func GetChatMessages(isoCode string, query, fields bson.M) (messages []*modelChatMessage.ChatMessageMessages, err error) {
	return REPO_BY_ISO_CODE[isoCode].GetChatMessages(isoCode, query, fields)
}

func GetChatMessage(isoCode string, query, fields bson.M) (message *modelChatMessage.ChatMessageMessages, err error) {
	return REPO_BY_ISO_CODE[isoCode].GetChatMessage(isoCode, query, fields)
}

func GetChatMessageMap(isoCode string, query, fields bson.M) (message map[string]interface{}, err error) {
	return REPO_BY_ISO_CODE[isoCode].GetChatMessageMap(isoCode, query, fields)
}

func GetChatMessagesSort(isoCode string, query, fields, sort bson.M) (messages []*modelChatMessage.ChatMessageMessages, err error) {
	return REPO_BY_ISO_CODE[isoCode].GetChatMessagesSort(isoCode, query, fields, sort)
}

func GetChatMessagesSkipLimitSort(isoCode string, query, fields bson.M, skip, limit int64, sort bson.M) (messages []*modelChatMessage.ChatMessageMessages, err error) {
	return REPO_BY_ISO_CODE[isoCode].GetChatMessagesSkipLimitSort(isoCode, query, fields, skip, limit, sort)
}

func UpdateChatMessages(isoCode string, query, fields bson.M) (int64, error) {
	return REPO_BY_ISO_CODE[isoCode].UpdateChatMessages(isoCode, query, fields)
}

func UpdateChatMessage(isoCode string, query, fields bson.M) (int64, error) {
	return REPO_BY_ISO_CODE[isoCode].UpdateChatMessage(isoCode, query, fields)
}

func UpdateChatConversation(isoCode string, query, fields bson.M) (int64, error) {
	return REPO_BY_ISO_CODE[isoCode].UpdateChatConversation(isoCode, query, fields)
}

func SendMessageToConversation(isoCode string, chatId string, messages []*modelChatMessage.ChatMessageMessages) error {
	return REPO_BY_ISO_CODE[isoCode].SendMessageToConversation(isoCode, chatId, messages)
}

func SendMessageMapToConversation(isoCode string, chatId string, messages []map[string]interface{}) error {
	return REPO_BY_ISO_CODE[isoCode].SendMessageMapToConversation(isoCode, chatId, messages)
}

func CreateChatConversation(isoCode string, chatMessage *modelChatMessage.ChatMessage) error {
	return REPO_BY_ISO_CODE[isoCode].CreateChatConversation(isoCode, chatMessage)
}

func parseChatMessageFields(chatConversationFields bson.M) bson.M {
	// If chatConversationFields is nil or empty -> Its mean get all fields (include messages' fields)
	if len(chatConversationFields) == 0 {
		return bson.M{}
	}

	// Get fields of message in original fields
	result := bson.M{}
	for field := range chatConversationFields {
		// This field mean get all fields of messages
		if field == "messages" {
			return bson.M{}
		}

		// Cut messages. prefix because we move message to another collection
		if value, ok := strings.CutPrefix(field, "messages."); ok {
			result[value] = 1
		}
	}
	return result
}

func DeleteChatMessage(isoCode string, query bson.M) error {
	_, err := REPO_BY_ISO_CODE[isoCode].UpdateChatMessages(isoCode, query, bson.M{"$set": bson.M{"deleted": true}})
	return err
}

func GetChatMessagesPagingSort(isoCode string, query, fields bson.M, page, limit int64, sort bson.M) ([]*modelChatMessage.ChatMessage, error) {
	return REPO_BY_ISO_CODE[isoCode].GetChatMessagesPagingSort(isoCode, query, fields, page, limit, sort)
}

func InsertChatMessage(isoCode string, chatMessage interface{}) error {
	return REPO_BY_ISO_CODE[isoCode].InsertChatMessage(isoCode, chatMessage)
}

func CountMessagesByQuery(isoCode string, query bson.M) (int, error) {
	return REPO_BY_ISO_CODE[isoCode].CountMessagesByQuery(isoCode, query)
}

func SendMessageToConversations(isoCode string, chatIds []string, messages []*modelChatMessage.ChatMessageMessages) error {
	return REPO_BY_ISO_CODE[isoCode].SendMessageToConversations(isoCode, chatIds, messages)
}

func CreateFirstMessageForFavChat(isoCode string, asker, tasker *modelUsers.Users) (string, error) {
	return REPO_BY_ISO_CODE[isoCode].CreateFirstMessageForFavChat(isoCode, asker, tasker)
}
func SendMessageToFavChat(params FavMessageParams) (*modelChatMessage.ChatMessageMessages, error) {
	return REPO_BY_ISO_CODE[params.IsoCode].SendMessageToFavChat(params)
}
