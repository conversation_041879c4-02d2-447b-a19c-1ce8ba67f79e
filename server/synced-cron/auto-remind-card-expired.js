import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import {
  trackingScheduleStatus,
  revertTimeZone,
  sendEmailFunction,
  sendNotificationByIds,
} from '/server/helper-function';


const sendWarningNotification = (card) => {

  const title = {
    vi: `Thẻ thanh toán của bạn sắp hết hạn.`,
    en: `Your payment card is about to expire.`,
    ko: `결제 카드의 유효기간이 곧 만료됩니다.`,
    th: `Your payment card is about to expire.`,
  };

  const text = {
    vi: `Thẻ thanh toán ....${card.number} của bạn sẽ hết hạn vào ${card.expiryMonth}/${card.expiryYear}. Để việc trải nghiệm dịch vụ trên ứng dụng không bị gián đo<PERSON>n, bạn vui lòng cập nhật thông tin thẻ mới trước thời gian trên. bTaskee xin cảm ơn.`,
    en: `Your payment card ....${card.number} will be expired by ${card.expiryMonth}/${card.expiryYear}. To avoid interruption while using bTaskee service, please update your card information or add another card before the expiry date. Thank you.`,
    ko: `고객님의 신용카드 ....${card.number} 는 ${card.expiryYear}년 ${card.expiryMonth}월에 사용 기간이 만료됩니다.  편한 서비스 이용을 위해 만료일 전까지 새 카드 정보를 업데이트 하시길 바랍니다. 감사합니다.`,
    th: `Your payment card ....${card.number} will be expired by ${card.expiryMonth}/${card.expiryYear}. To avoid interruption while using bTaskee service, please update your card information or add another card before the expiry date. Thank you.`,
  };

  sendNotificationByIds([card.userId], {
    title,
    text,
    payload: {},
  }, { isForceView: true, url: 'Payment'});
};

const runCardExpiredReminder = () => {
  return;
	//TODO: need to send email too
	console.log("Start runCardExpiredReminder ...");
  var message = null;
  try {
    let text = '';
    const thisMonth = moment().format('M');
    const thisYear = moment().format('YYYY');
    const nextMonth = moment().add(1, 'month').format('M');
    const nextMonthYear = moment().add(1, 'month').format('YYYY');
    let cardIds = [];
    Meteor.appCollection.PaymentCard.find({
      $or: [
        {
          expiryMonth: thisMonth,
          expiryYear: thisYear
        }, {
          expiryMonth: nextMonth,
          expiryYear: nextMonthYear
        }
      ],
      sentExpiredNotification: {$ne: true},
      disabled: {$ne: true},
    }).forEach((card) => {
      text += `${card._id} ${card.expiryMonth}/${card.expiryYear}\n\n`;
      cardIds.push(card._id);
      sendWarningNotification(card);
    });

    // Update sentExpiredNotification status to these cards
    Meteor.appCollection.PaymentCard.update({
      _id: {$in: cardIds}
    }, {
      $set: {
        sentExpiredNotification: true,
        sentExpiredNotificationAt: new Date(),
      }
    }, { multi: true });
    
  } catch (e) {
    message = e;
  }
	console.log("Finished runCardExpiredReminder ");
  // Tracking
  trackingScheduleStatus({name: 'autoRemindCardExpired', nextRunAt: SyncedCron.nextScheduledAtDate('autoRemindCardExpired'), message});
};

  Meteor.methods({
    autoRemindCardExpired() {
      SyncedCron.add({
        name: 'autoRemindCardExpired',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016,1,1,9)).getHours();
          // Run at 9:00 AM every day with timezone GMT+7.
          return parser.cron(`0 0 ${hourInTimeZoneServer} 1/1 * ? *`, true);
        },
        job() {
          runCardExpiredReminder();
        },
      });
	    console.log("Added autoRemindCardExpired cron");
    },
    'runNow.autoRemindCardExpired'() {
      runCardExpiredReminder();
    },
  });
