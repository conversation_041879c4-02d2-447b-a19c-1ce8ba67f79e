package grpcSMSMY

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcSMSMY"
	"google.golang.org/grpc"
)

func ConnectGRPCSMSMY(endpoint string) (grpcSMSMY.SMSClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcSMSMY.NewSMSClient(connect)
	return client, connect, err
}
