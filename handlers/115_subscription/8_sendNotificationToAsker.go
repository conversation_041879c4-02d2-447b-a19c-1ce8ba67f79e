package subscription

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// sendNotificationToAsker sends notification to asker when task is posted
func sendNotificationToAsker(taskID, userID, language, serviceText string) {
	lang := globalConstant.LANG_VI
	if language != "" {
		lang = language
	}

	// Create notification data
	title := localization.GetLocalizeObject("NOTIFICATION_MESSAGE_ASKER_POSTED_TITLE", serviceText)
	text := localization.GetLocalizeObject("NOTIFICATION_MESSAGE_ASKER_POSTED", serviceText)

	// Create notification for database
	notiInfos := []interface{}{
		&notification.Notification{
			XId:         globalLib.GenerateObjectId(),
			UserId:      userID,
			TaskId:      taskID,
			Type:        2,
			Title:       globalLib.LocalizeServiceName(lang, title),
			Description: globalLib.LocalizeServiceName(lang, text),
			CreatedAt:   timestamppb.New(globalLib.GetCurrentTime(local.TimeZone)),
			NavigateTo:  "TaskDetail",
		},
	}

	// Create user IDs for push notification
	userIds := []*pushNotificationRequest.PushNotificationRequestUserIds{
		{
			UserId:   userID,
			Language: lang,
		},
	}

	// Create title and body with localization
	titleText := &service.ServiceText{
		Vi: globalLib.LocalizeServiceName(lang, title),
	}
	bodyText := &service.ServiceText{
		Vi: globalLib.LocalizeServiceName(lang, text),
	}

	// Create payload
	payload := &pushNotificationRequest.PushNotificationRequestPayload{
		Type:       2, // Task notification type
		TaskId:     taskID,
		NavigateTo: "TaskDetail",
	}

	// Use lib.SendNotification instead of custom gRPC implementation
	lib.SendNotification(notiInfos, userIds, titleText, bodyText, payload, true)
}
