//Package config file config
/*
 * @File: base.go
 * @Description: Handle, Add Config
 * @CreatedAt: 22/02/2021
 * @Author: vinhnt
 */
package config

import (
	"fmt"
	"os"

	Viper "github.com/spf13/viper"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/redisCache"
)

// Config create
type Config struct {
	APIPort                       string `mapstructure:"running_rest_sync_cron_vn_port"`
	GRPC_BPOINT_VN_V3_URL         string `mapstructure:"grpc_bpoint_vn_v3_url"`
	GRPC_Push_Notification_URL    string `mapstructure:"grpc_push_notification_service_url"`
	GRPC_Websocket_Service_V2_URL string `mapstructure:"grpc_websocket_service_v2_url"`
	GRPC_Chat_Server_VN_V3_URL    string `mapstructure:"grpc_chat_server_vn_v3_url"`
	GrpcPaymentPort               string `mapstructure:"grpc_payment_vn_v3_url"`
	GRPC_Cancel_Task_VN_URL       string `mapstructure:"grpc_cancel_task_vn_url"`
	GRPC_Send_Task_URL            string `mapstructure:"grpc_send_task_vn_v3_url"`
	ScheduleSyncCron              map[string]map[string]interface{}
	SlackToken                    string
	UrBoxConfig                   *UrBoxConfig
	RedisAddress                  string `mapstructure:"redis_address"`
	RedisPassword                 string
	RedisCache                    *redisCache.RedisCache
}

type UrBoxConfig struct {
	AppID          string
	AppSecret      string
	UrlGetListGift string
}

var cfg *Config

func init() {
	var folder string
	mode := os.Getenv("APPLICATION_MODE")

	switch mode {
	case "dev", "prod", "test":
		folder = mode
	default:
		folder = "local"
		os.Setenv("APPLICATION_MODE", folder)
	}
	cfgPath := os.Getenv("SYNC_CRON_VN_CONFIG")
	if cfgPath == "" {
		cfgPath = "config"
	}
	path := fmt.Sprintf("%v/%v", cfgPath, folder)

	cfg = new(Config)

	if mode == "prod" {
		cfg.UrBoxConfig = &UrBoxConfig{
			AppID:          os.Getenv("URBOX_APP_ID"),
			AppSecret:      os.Getenv("URBOX_APP_SECRET"),
			UrlGetListGift: os.Getenv("URBOX_URL_GET_LIST_GIFT"),
		}
		cfg.RedisPassword = os.Getenv("REDIS_PASSWORD")
		cfg.SlackToken = os.Getenv("SLACK_TOKEN_VN")
	} else {
		cfg.RedisPassword = ""
	}
	initConfig(path, "base", &cfg)
	initConfig(path, "config", &cfg.ScheduleSyncCron)
	initRedisCache()
}

func initConfig(configPath, configName string, result interface{}) {
	viper := Viper.New()
	viper.AddConfigPath(configPath)
	viper.SetConfigName(configName)

	err := viper.ReadInConfig() // Find and read the config file
	if err == nil {             // Handle errors reading the config file
		err = viper.Unmarshal(result)
		if err != nil { // Handle errors reading the config file
			panic(fmt.Errorf("fatal error config file: %s", err))
		}
	} else {
		panic(fmt.Errorf("fatal error config file: %s", err))
	}
}

func initRedisCache() {
	redisConfig := map[string]interface{}{
		"address":  cfg.RedisAddress,
		"password": cfg.RedisPassword,
	}
	cfg.RedisCache = redisCache.NewRedisCache(redisConfig)
}

// GetConfig is get config data
func GetConfig() *Config {
	return cfg
}
