FROM alpine:3.21.3

RUN apk update && apk upgrade --no-cache

RUN addgroup -S appgroup && adduser -S appuser -G appgroup

WORKDIR /app

RUN chown -R appuser:appgroup /app

ARG MODE
COPY config/$MODE config/$MODE
COPY email-vn-v3-linux-amd64 .
COPY lib/emailTemplate emailTemplate
COPY lib/fileTemplate fileTemplate

RUN chmod +x email-vn-v3-linux-amd64

USER appuser

EXPOSE 18100

LABEL maintainer="<EMAIL>"

ENTRYPOINT ["./email-vn-v3-linux-amd64"]