package grpcWebsocket

import (
	"context"
	"encoding/json"
	"fmt"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/kafkaEvent"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage"
	"gitlab.com/btaskee/go-websocket-service-v2/grpcWebsocket"
	"go.uber.org/zap"
	"google.golang.org/grpc"
)

func ConnectGRPCWebsocket(endpoint string) (grpcWebsocket.WebsocketClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcWebsocket.NewWebsocketClient(connect)
	return client, connect, err
}

func SendSocketMessage(endpoint string, key string, source string, value string, messageTo []string, data map[string]interface{}) (*websocketMessage.WebsocketMessage, error) {
	var dataByte []byte
	if data != nil {
		dataByte, _ = json.Marshal(data)
	}
	request := &websocketMessage.WebsocketMessage{
		Key:       key,
		Source:    source,
		Value:     value,
		Data:      dataByte,
		MessageTo: messageTo,
	}

	client, connect, err := ConnectGRPCWebsocket(endpoint)
	if err != nil {
		globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
			zap.Error(err),
			zap.String("endpoint", endpoint),
			zap.String("source", source),
			zap.Any("messageTo", messageTo),
			zap.Any("data", data),
		)
		return request, err
	}
	defer connect.Close()

	_, err = client.SendSocketMessage(context.Background(), request)
	if err != nil {
		globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
			zap.Error(err),
			zap.Any("body", request),
		)
		return request, err
	}
	return request, nil
}

func SendSocketNotification(arrayNotification []interface{}, endpoint string, userIds []*pushNotificationRequest.PushNotificationRequestUserIds, payloadType int32, title, body *service.ServiceText) (*websocketMessage.WebsocketMessage, error) {
	if len(arrayNotification) > 0 && len(userIds) > 0 {
		data := make(map[string]interface{})
		notifyData, _ := json.Marshal(arrayNotification[0])
		json.Unmarshal(notifyData, &data)
		data["title"] = title
		data["content"] = body
		messageTo := []string{}
		for _, v := range userIds {
			messageTo = append(messageTo, v.UserId)
		}
		wsMessage, err := SendSocketMessage(endpoint, globalConstant.WEBSOCKET_KEY_NOTIFICATION, "", fmt.Sprintf("%d", payloadType), messageTo, data)
		return wsMessage, err
	}
	return nil, nil
}

// Deprecated: Use SendSocketChatMessageV2 instead. Use for receivers instead of messageTo.
func SendSocketChatMessage(endpoint, chatId string, messageTo []string, message *modelChatMessage.ChatMessageMessages, task *modelTask.Task, isoCode string) (*websocketMessage.WebsocketMessage, error) {
	messageMap := chatMessage.MapChatMessageMessageForTasker(message, task)
	t := globalResponse.Translate(messageMap)
	data, _ := json.Marshal(t)
	request := &websocketMessage.WebsocketMessage{
		Key:       globalConstant.WEBSOCKET_KEY_CHAT,
		Source:    "",
		Value:     chatId,
		Data:      data,
		MessageTo: messageTo,
		Version:   globalConstant.API_VERSION_V2,
		IsoCode:   isoCode,
	}

	client, connect, err := ConnectGRPCWebsocket(endpoint)
	if err != nil {
		globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
			zap.Error(err),
			zap.String("endpoint", endpoint),
			zap.Any("messageTo", messageTo),
			zap.Any("data", data),
		)
		return request, err
	}
	defer connect.Close()

	_, err = client.SendSocketMessage(context.Background(), request)
	if err != nil {
		globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
			zap.Error(err),
			zap.Any("body", request),
		)
		return request, err
	}
	return request, nil
}

func SendSocketChatMessageV2(endpoint, chatId string, receivers []*kafkaEvent.ConversationMessageReceiver, message *modelChatMessage.ChatMessageMessages, task *modelTask.Task, isoCode string) (*websocketMessage.WebsocketMessage, error) {
	messageMap := chatMessage.MapChatMessageMessageForTasker(message, task)
	t := globalResponse.Translate(messageMap)
	data, _ := json.Marshal(t)
	request := &websocketMessage.WebsocketMessage{
		Key:       globalConstant.WEBSOCKET_KEY_CHAT,
		Source:    "",
		Value:     chatId,
		Data:      data,
		Version:   globalConstant.API_VERSION_V2,
		Receivers: receivers,
		IsoCode:   isoCode,
	}

	client, connect, err := ConnectGRPCWebsocket(endpoint)
	if err != nil {
		globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
			zap.Error(err),
			zap.String("endpoint", endpoint),
			zap.Any("receivers", receivers),
			zap.Any("data", data),
		)
		return request, err
	}
	defer connect.Close()

	_, err = client.SendSocketMessage(context.Background(), request)
	if err != nil {
		globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
			zap.Error(err),
			zap.Any("body", request),
		)
		return request, err
	}
	return request, nil
}
