package grpcPushNotificationMY

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPushNotificationMY"
	"google.golang.org/grpc"
)

func ConnectGRPCPushNotification(endpoint string) (grpcPushNotificationMY.PushNotificationClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPushNotificationMY.NewPushNotificationClient(connect)
	return client, connect, err
}
