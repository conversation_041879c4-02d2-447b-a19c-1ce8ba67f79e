//Package service file index
/*
 * @File: index.go
 * @Description: Handler Func Start
 * @CreatedAt: 22/02/2021
 * @Author: vinhnt
 */
package service

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"strings"
	"time"

	"go.elastic.co/apm/module/apmhttp/v2"
)

func NewHTTPServer(port string) *http.Server {
	r := NewRouter()
	http.Handle("/", r)
	return &http.Server{
		Addr:    fmt.Sprintf(":%s", port),
		Handler: apmhttp.Wrap(r),
	}
}

func StartHTTPServer(srv *http.Server) error {
	var mode = os.Getenv("APPLICATION_MODE")
	log.Printf("[%s] Started Sync Cron VN V3 REST at port: %s", mode, strings.Trim(srv.Addr, ":"))
	return srv.ListenAndServe()
}

func StopHTTPServer(srv *http.Server) {
	timeWait := 60 * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), timeWait)
	defer func() {
		log.Println("Close another connection")
		cancel()
	}()
	if err := srv.Shutdown(ctx); err == context.DeadlineExceeded {
		log.Print("Halted active connections")
	}
}
