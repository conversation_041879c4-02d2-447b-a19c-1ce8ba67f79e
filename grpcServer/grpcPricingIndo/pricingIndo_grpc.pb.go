// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcPricingIndo

import (
	context "context"
	pricingSubscriptionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionRequest"
	pricingSubscriptionResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionResponse"
	pricingrequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingrequest"
	pricingresponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PricingIndoClient is the client API for PricingIndo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PricingIndoClient interface {
	GetPricingAirConditioner(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingHomeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingDeepCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingHomeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingAirConditioner(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingDeepCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	ReCalculatePricing(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	APIGetPricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingSubscription(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingWashingMachine(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingWashingMachine(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingSofa(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingSofa(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingDisinfection(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingDisinfection(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingMassage(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingMassage(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingHomeMoving(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingHomeMoving(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingAirConditionerV2(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingLaundry(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingLaundry(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	ReCalculatePricingForIncreaseDuration(ctx context.Context, in *pricingrequest.PricingIncreaseDurationRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	APIGetPricingTaskDateOptions(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingHousekeepingV2(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingWaterHeater(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingIroning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
}

type pricingIndoClient struct {
	cc grpc.ClientConnInterface
}

func NewPricingIndoClient(cc grpc.ClientConnInterface) PricingIndoClient {
	return &pricingIndoClient{cc}
}

func (c *pricingIndoClient) GetPricingAirConditioner(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/GetPricingAirConditioner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) GetPricingHomeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/GetPricingHomeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) GetPricingDeepCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/GetPricingDeepCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) RePricingHomeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/RePricingHomeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) RePricingAirConditioner(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/RePricingAirConditioner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) RePricingDeepCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/RePricingDeepCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) ReCalculatePricing(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/ReCalculatePricing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) GetPricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/GetPricingOfficeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) APIGetPricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/APIGetPricingOfficeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) RePricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/RePricingOfficeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) GetPricingSubscription(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	out := new(pricingSubscriptionResponse.PricingSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/GetPricingSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) GetPricingWashingMachine(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/GetPricingWashingMachine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) RePricingWashingMachine(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/RePricingWashingMachine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) GetPricingSofa(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/GetPricingSofa", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) RePricingSofa(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/RePricingSofa", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) GetPricingDisinfection(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/GetPricingDisinfection", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) RePricingDisinfection(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/RePricingDisinfection", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) GetPricingMassage(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/GetPricingMassage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) RePricingMassage(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/RePricingMassage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) GetPricingHomeMoving(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/GetPricingHomeMoving", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) RePricingHomeMoving(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/RePricingHomeMoving", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) GetPricingAirConditionerV2(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/GetPricingAirConditionerV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) GetPricingLaundry(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/GetPricingLaundry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) RePricingLaundry(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/RePricingLaundry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) ReCalculatePricingForIncreaseDuration(ctx context.Context, in *pricingrequest.PricingIncreaseDurationRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/ReCalculatePricingForIncreaseDuration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) APIGetPricingTaskDateOptions(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/APIGetPricingTaskDateOptions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) GetPricingHousekeepingV2(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/GetPricingHousekeepingV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) GetPricingWaterHeater(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/GetPricingWaterHeater", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingIndoClient) GetPricingIroning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingIndo.PricingIndo/GetPricingIroning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PricingIndoServer is the server API for PricingIndo service.
// All implementations must embed UnimplementedPricingIndoServer
// for forward compatibility
type PricingIndoServer interface {
	GetPricingAirConditioner(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingHomeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingDeepCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingHomeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingAirConditioner(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingDeepCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	ReCalculatePricing(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	APIGetPricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingSubscription(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingWashingMachine(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingWashingMachine(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingSofa(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingSofa(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingDisinfection(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingDisinfection(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingMassage(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingMassage(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingHomeMoving(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingHomeMoving(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingAirConditionerV2(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingLaundry(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingLaundry(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	ReCalculatePricingForIncreaseDuration(context.Context, *pricingrequest.PricingIncreaseDurationRequest) (*pricingresponse.CostResult, error)
	APIGetPricingTaskDateOptions(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingHousekeepingV2(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingWaterHeater(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingIroning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	mustEmbedUnimplementedPricingIndoServer()
}

// UnimplementedPricingIndoServer must be embedded to have forward compatible implementations.
type UnimplementedPricingIndoServer struct {
}

func (UnimplementedPricingIndoServer) GetPricingAirConditioner(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingAirConditioner not implemented")
}
func (UnimplementedPricingIndoServer) GetPricingHomeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingHomeCleaning not implemented")
}
func (UnimplementedPricingIndoServer) GetPricingDeepCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingDeepCleaning not implemented")
}
func (UnimplementedPricingIndoServer) RePricingHomeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingHomeCleaning not implemented")
}
func (UnimplementedPricingIndoServer) RePricingAirConditioner(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingAirConditioner not implemented")
}
func (UnimplementedPricingIndoServer) RePricingDeepCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingDeepCleaning not implemented")
}
func (UnimplementedPricingIndoServer) ReCalculatePricing(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReCalculatePricing not implemented")
}
func (UnimplementedPricingIndoServer) GetPricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingOfficeCleaning not implemented")
}
func (UnimplementedPricingIndoServer) APIGetPricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method APIGetPricingOfficeCleaning not implemented")
}
func (UnimplementedPricingIndoServer) RePricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingOfficeCleaning not implemented")
}
func (UnimplementedPricingIndoServer) GetPricingSubscription(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingSubscription not implemented")
}
func (UnimplementedPricingIndoServer) GetPricingWashingMachine(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingWashingMachine not implemented")
}
func (UnimplementedPricingIndoServer) RePricingWashingMachine(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingWashingMachine not implemented")
}
func (UnimplementedPricingIndoServer) GetPricingSofa(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingSofa not implemented")
}
func (UnimplementedPricingIndoServer) RePricingSofa(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingSofa not implemented")
}
func (UnimplementedPricingIndoServer) GetPricingDisinfection(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingDisinfection not implemented")
}
func (UnimplementedPricingIndoServer) RePricingDisinfection(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingDisinfection not implemented")
}
func (UnimplementedPricingIndoServer) GetPricingMassage(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingMassage not implemented")
}
func (UnimplementedPricingIndoServer) RePricingMassage(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingMassage not implemented")
}
func (UnimplementedPricingIndoServer) GetPricingHomeMoving(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingHomeMoving not implemented")
}
func (UnimplementedPricingIndoServer) RePricingHomeMoving(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingHomeMoving not implemented")
}
func (UnimplementedPricingIndoServer) GetPricingAirConditionerV2(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingAirConditionerV2 not implemented")
}
func (UnimplementedPricingIndoServer) GetPricingLaundry(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingLaundry not implemented")
}
func (UnimplementedPricingIndoServer) RePricingLaundry(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingLaundry not implemented")
}
func (UnimplementedPricingIndoServer) ReCalculatePricingForIncreaseDuration(context.Context, *pricingrequest.PricingIncreaseDurationRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReCalculatePricingForIncreaseDuration not implemented")
}
func (UnimplementedPricingIndoServer) APIGetPricingTaskDateOptions(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method APIGetPricingTaskDateOptions not implemented")
}
func (UnimplementedPricingIndoServer) GetPricingHousekeepingV2(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingHousekeepingV2 not implemented")
}
func (UnimplementedPricingIndoServer) GetPricingWaterHeater(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingWaterHeater not implemented")
}
func (UnimplementedPricingIndoServer) GetPricingIroning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingIroning not implemented")
}
func (UnimplementedPricingIndoServer) mustEmbedUnimplementedPricingIndoServer() {}

// UnsafePricingIndoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PricingIndoServer will
// result in compilation errors.
type UnsafePricingIndoServer interface {
	mustEmbedUnimplementedPricingIndoServer()
}

func RegisterPricingIndoServer(s *grpc.Server, srv PricingIndoServer) {
	s.RegisterService(&_PricingIndo_serviceDesc, srv)
}

func _PricingIndo_GetPricingAirConditioner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).GetPricingAirConditioner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/GetPricingAirConditioner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).GetPricingAirConditioner(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_GetPricingHomeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).GetPricingHomeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/GetPricingHomeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).GetPricingHomeCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_GetPricingDeepCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).GetPricingDeepCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/GetPricingDeepCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).GetPricingDeepCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_RePricingHomeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).RePricingHomeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/RePricingHomeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).RePricingHomeCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_RePricingAirConditioner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).RePricingAirConditioner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/RePricingAirConditioner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).RePricingAirConditioner(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_RePricingDeepCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).RePricingDeepCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/RePricingDeepCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).RePricingDeepCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_ReCalculatePricing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).ReCalculatePricing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/ReCalculatePricing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).ReCalculatePricing(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_GetPricingOfficeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).GetPricingOfficeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/GetPricingOfficeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).GetPricingOfficeCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_APIGetPricingOfficeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).APIGetPricingOfficeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/APIGetPricingOfficeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).APIGetPricingOfficeCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_RePricingOfficeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).RePricingOfficeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/RePricingOfficeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).RePricingOfficeCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_GetPricingSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingSubscriptionRequest.PricingSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).GetPricingSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/GetPricingSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).GetPricingSubscription(ctx, req.(*pricingSubscriptionRequest.PricingSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_GetPricingWashingMachine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).GetPricingWashingMachine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/GetPricingWashingMachine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).GetPricingWashingMachine(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_RePricingWashingMachine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).RePricingWashingMachine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/RePricingWashingMachine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).RePricingWashingMachine(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_GetPricingSofa_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).GetPricingSofa(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/GetPricingSofa",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).GetPricingSofa(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_RePricingSofa_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).RePricingSofa(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/RePricingSofa",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).RePricingSofa(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_GetPricingDisinfection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).GetPricingDisinfection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/GetPricingDisinfection",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).GetPricingDisinfection(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_RePricingDisinfection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).RePricingDisinfection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/RePricingDisinfection",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).RePricingDisinfection(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_GetPricingMassage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).GetPricingMassage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/GetPricingMassage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).GetPricingMassage(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_RePricingMassage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).RePricingMassage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/RePricingMassage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).RePricingMassage(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_GetPricingHomeMoving_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).GetPricingHomeMoving(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/GetPricingHomeMoving",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).GetPricingHomeMoving(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_RePricingHomeMoving_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).RePricingHomeMoving(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/RePricingHomeMoving",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).RePricingHomeMoving(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_GetPricingAirConditionerV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).GetPricingAirConditionerV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/GetPricingAirConditionerV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).GetPricingAirConditionerV2(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_GetPricingLaundry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).GetPricingLaundry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/GetPricingLaundry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).GetPricingLaundry(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_RePricingLaundry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).RePricingLaundry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/RePricingLaundry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).RePricingLaundry(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_ReCalculatePricingForIncreaseDuration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingIncreaseDurationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).ReCalculatePricingForIncreaseDuration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/ReCalculatePricingForIncreaseDuration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).ReCalculatePricingForIncreaseDuration(ctx, req.(*pricingrequest.PricingIncreaseDurationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_APIGetPricingTaskDateOptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).APIGetPricingTaskDateOptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/APIGetPricingTaskDateOptions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).APIGetPricingTaskDateOptions(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_GetPricingHousekeepingV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).GetPricingHousekeepingV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/GetPricingHousekeepingV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).GetPricingHousekeepingV2(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_GetPricingWaterHeater_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).GetPricingWaterHeater(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/GetPricingWaterHeater",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).GetPricingWaterHeater(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingIndo_GetPricingIroning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingIndoServer).GetPricingIroning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingIndo.PricingIndo/GetPricingIroning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingIndoServer).GetPricingIroning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PricingIndo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcPricingIndo.PricingIndo",
	HandlerType: (*PricingIndoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPricingAirConditioner",
			Handler:    _PricingIndo_GetPricingAirConditioner_Handler,
		},
		{
			MethodName: "GetPricingHomeCleaning",
			Handler:    _PricingIndo_GetPricingHomeCleaning_Handler,
		},
		{
			MethodName: "GetPricingDeepCleaning",
			Handler:    _PricingIndo_GetPricingDeepCleaning_Handler,
		},
		{
			MethodName: "RePricingHomeCleaning",
			Handler:    _PricingIndo_RePricingHomeCleaning_Handler,
		},
		{
			MethodName: "RePricingAirConditioner",
			Handler:    _PricingIndo_RePricingAirConditioner_Handler,
		},
		{
			MethodName: "RePricingDeepCleaning",
			Handler:    _PricingIndo_RePricingDeepCleaning_Handler,
		},
		{
			MethodName: "ReCalculatePricing",
			Handler:    _PricingIndo_ReCalculatePricing_Handler,
		},
		{
			MethodName: "GetPricingOfficeCleaning",
			Handler:    _PricingIndo_GetPricingOfficeCleaning_Handler,
		},
		{
			MethodName: "APIGetPricingOfficeCleaning",
			Handler:    _PricingIndo_APIGetPricingOfficeCleaning_Handler,
		},
		{
			MethodName: "RePricingOfficeCleaning",
			Handler:    _PricingIndo_RePricingOfficeCleaning_Handler,
		},
		{
			MethodName: "GetPricingSubscription",
			Handler:    _PricingIndo_GetPricingSubscription_Handler,
		},
		{
			MethodName: "GetPricingWashingMachine",
			Handler:    _PricingIndo_GetPricingWashingMachine_Handler,
		},
		{
			MethodName: "RePricingWashingMachine",
			Handler:    _PricingIndo_RePricingWashingMachine_Handler,
		},
		{
			MethodName: "GetPricingSofa",
			Handler:    _PricingIndo_GetPricingSofa_Handler,
		},
		{
			MethodName: "RePricingSofa",
			Handler:    _PricingIndo_RePricingSofa_Handler,
		},
		{
			MethodName: "GetPricingDisinfection",
			Handler:    _PricingIndo_GetPricingDisinfection_Handler,
		},
		{
			MethodName: "RePricingDisinfection",
			Handler:    _PricingIndo_RePricingDisinfection_Handler,
		},
		{
			MethodName: "GetPricingMassage",
			Handler:    _PricingIndo_GetPricingMassage_Handler,
		},
		{
			MethodName: "RePricingMassage",
			Handler:    _PricingIndo_RePricingMassage_Handler,
		},
		{
			MethodName: "GetPricingHomeMoving",
			Handler:    _PricingIndo_GetPricingHomeMoving_Handler,
		},
		{
			MethodName: "RePricingHomeMoving",
			Handler:    _PricingIndo_RePricingHomeMoving_Handler,
		},
		{
			MethodName: "GetPricingAirConditionerV2",
			Handler:    _PricingIndo_GetPricingAirConditionerV2_Handler,
		},
		{
			MethodName: "GetPricingLaundry",
			Handler:    _PricingIndo_GetPricingLaundry_Handler,
		},
		{
			MethodName: "RePricingLaundry",
			Handler:    _PricingIndo_RePricingLaundry_Handler,
		},
		{
			MethodName: "ReCalculatePricingForIncreaseDuration",
			Handler:    _PricingIndo_ReCalculatePricingForIncreaseDuration_Handler,
		},
		{
			MethodName: "APIGetPricingTaskDateOptions",
			Handler:    _PricingIndo_APIGetPricingTaskDateOptions_Handler,
		},
		{
			MethodName: "GetPricingHousekeepingV2",
			Handler:    _PricingIndo_GetPricingHousekeepingV2_Handler,
		},
		{
			MethodName: "GetPricingWaterHeater",
			Handler:    _PricingIndo_GetPricingWaterHeater_Handler,
		},
		{
			MethodName: "GetPricingIroning",
			Handler:    _PricingIndo_GetPricingIroning_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pricingIndo.proto",
}
