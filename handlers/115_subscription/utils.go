package subscription

import (
	"fmt"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelSubscription "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscription"
	"go.mongodb.org/mongo-driver/bson"
)

// Common utility functions for subscription processing

// validateSubscriptionData validates subscription data before processing
func validateSubscriptionData(subs *modelSubscription.Subscription) error {
	if subs.XId == "" {
		return fmt.Errorf("subscription ID is required")
	}
	if subs.UserId == "" {
		return fmt.Errorf("user ID is required")
	}
	if subs.ServiceId == "" {
		return fmt.Errorf("service ID is required")
	}
	if len(subs.Schedule) == 0 {
		return fmt.Errorf("schedule is required")
	}
	if subs.Duration <= 0 {
		return fmt.Errorf("duration must be positive")
	}
	if subs.Address == "" {
		return fmt.Errorf("address is required")
	}
	return nil
}

// isTaskAlreadyPosted checks if a task was already posted for a specific date
func isTaskAlreadyPosted(subsID string, targetDate time.Time) bool {
	var existingTasks []bson.M
	query := bson.M{
		"subscriptionId": subsID,
		"date": bson.M{
			"$gte": targetDate.Truncate(24 * time.Hour),
			"$lt":  targetDate.Truncate(24 * time.Hour).Add(24 * time.Hour),
		},
	}

	err := globalDataAccess.GetAllByQuery(
		globalCollection.COLLECTION_TASK[local.ISO_CODE],
		query,
		bson.M{"_id": 1},
		&existingTasks,
	)

	return err == nil && len(existingTasks) > 0
}

// isSameDate checks if two dates are the same day
func isSameDate(date1, date2 time.Time) bool {
	return date1.Year() == date2.Year() &&
		date1.Month() == date2.Month() &&
		date1.Day() == date2.Day()
}

// calculateSubscriptionProgress calculates the progress of a subscription
func calculateSubscriptionProgress(subs *modelSubscription.Subscription) (int, int) {
	totalTasks := len(subs.Schedule)
	completedTasks := len(subs.History)
	return completedTasks, totalTasks
}

// getNextScheduledDate gets the next date that needs to be processed
func getNextScheduledDate(subs *modelSubscription.Subscription, currentTime time.Time, postTaskBefore int) *time.Time {
	for _, scheduleDate := range subs.Schedule {
		// Check if this date was already processed
		isProcessed := false
		for _, historyDate := range subs.History {
			if isSameDate(historyDate.AsTime(), scheduleDate.AsTime()) {
				isProcessed = true
				break
			}
		}

		if !isProcessed {
			scheduleDateTime := scheduleDate.AsTime()
			postDate := scheduleDateTime.AddDate(0, 0, -postTaskBefore)
			if postDate.Before(currentTime) && scheduleDateTime.After(currentTime) {
				return &scheduleDateTime
			}
		}
	}
	return nil
}

// isSubscriptionExpired checks if a subscription should be expired
func isSubscriptionExpired(subs *modelSubscription.Subscription, expiredDays int) bool {
	if len(subs.Schedule) == 0 {
		return false
	}

	firstScheduleDate := subs.Schedule[0].AsTime()
	expiredDate := firstScheduleDate.AddDate(0, 0, -expiredDays)
	currentTime := time.Now()

	return expiredDate.Before(currentTime) || firstScheduleDate.Before(currentTime)
}

// shouldSendRenewalNotification checks if renewal notification should be sent
func shouldSendRenewalNotification(subs *modelSubscription.Subscription, renewBefore int) bool {
	if subs.Status == "DONE" || len(subs.Schedule) == 0 {
		return false
	}

	// Check if renewal email was already sent
	var subsData bson.M
	query := bson.M{"_id": subs.XId}
	fields := bson.M{"isSentRenewEmail": 1}
	err := globalDataAccess.GetOneByQuery(
		globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE],
		query,
		fields,
		&subsData,
	)
	if err == nil {
		if isSent, ok := subsData["isSentRenewEmail"].(bool); ok && isSent {
			return false
		}
	}

	lastScheduleDate := subs.Schedule[len(subs.Schedule)-1].AsTime()
	dateRenew := lastScheduleDate.AddDate(0, 0, -renewBefore)
	currentTime := time.Now()

	return dateRenew.Before(currentTime)
}

// getSubscriptionServiceText gets the service text for a subscription
func getSubscriptionServiceText(subs *modelSubscription.Subscription) string {
	if subs.ServiceText != nil && subs.ServiceText.Vi != "" {
		return subs.ServiceText.Vi
	}

	var service bson.M
	query := bson.M{"_id": subs.ServiceId}
	fields := bson.M{"text": 1}

	err := globalDataAccess.GetOneByQuery(
		globalCollection.COLLECTION_SERVICE[local.ISO_CODE],
		query,
		fields,
		&service,
	)
	if err != nil {
		return ""
	}

	if text, ok := service["text"].(string); ok {
		return text
	}
	return ""
}

// formatSubscriptionLog formats log message for subscription processing
func formatSubscriptionLog(subsID, action string, details ...interface{}) string {
	message := fmt.Sprintf("Subscription %s: %s", subsID, action)
	if len(details) > 0 {
		message += fmt.Sprintf(" - %v", details...)
	}
	return message
}
