package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	modelBusinessMemberTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMemberTransaction"
	modelBusinessTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessTransaction"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	"go.mongodb.org/mongo-driver/bson"
)

func TestBusinessAllocation(t *testing.T) {
	apiURL := "/api/v3/sync-cron-vn/business-allocation"
	t.Run("1", func(t *testing.T) {
		ResetData()
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		// Rovke
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567895",
				"name":  "Asker 05",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "0834567890",
				"status": "ACTIVE",
				"bPay":   5000000,
				"revokeSetting": map[string]interface{}{
					"status":     "ACTIVE",
					"period":     1,
					"nextTime":   currentTime,
					"dayInMonth": "LAST_DAY_IN_MONTH",
				},
			},
		})
		CreateBusinessLevel([]map[string]interface{}{
			{
				"_id":        "level1",
				"name":       "level1",
				"status":     "ACTIVE",
				"businessId": "0834567890",
				"amount":     675000,
			}, {
				"_id":        "level2",
				"name":       "level2",
				"status":     "ACTIVE",
				"businessId": "0834567890",
				"amount":     1000000,
			},
		})
		CreateBusinessMember([]map[string]interface{}{
			{
				"_id":        "0834567895",
				"businessId": "0834567890",
				"userId":     "0834567895",
				"status":     "ACTIVE",
				"levelId":    "level1",
				"bPay":       200000,
			}, {
				"_id":        "0834567894",
				"businessId": "0834567890",
				"userId":     "0834567894",
				"status":     "ACTIVE",
				"levelId":    "level2",
				"bPay":       300000,
			}, {
				"_id":        "0834567893",
				"businessId": "0834567890",
				"userId":     "0834567893",
				"status":     "ACTIVE",
				"levelId":    "level1",
				"bPay":       500000,
			}, {
				"_id":        "0834567892",
				"businessId": "0834567890",
				"userId":     "0834567892",
				"status":     "ACTIVE",
				"bPay":       700000,
			},
		})

		// Topup
		CreateUser([]map[string]interface{}{
			{
				"phone": "0934567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0934567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0934567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0934567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0934567895",
				"name":  "Asker 05",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "0934567890",
				"status": "ACTIVE",
				"bPay":   5000000,
				"topUpSetting": map[string]interface{}{
					"status":     "ACTIVE",
					"period":     1,
					"nextTime":   currentTime,
					"dayInMonth": "FIRST_DAY_IN_MONTH",
				},
			},
		})
		CreateBusinessLevel([]map[string]interface{}{
			{
				"_id":        "level11",
				"name":       "level1",
				"status":     "ACTIVE",
				"businessId": "0934567890",
				"amount":     675000,
			}, {
				"_id":        "level22",
				"name":       "level2",
				"status":     "ACTIVE",
				"businessId": "0934567890",
				"amount":     1000000,
			},
		})
		CreateBusinessMember([]map[string]interface{}{
			{
				"_id":        "0934567895",
				"businessId": "0934567890",
				"userId":     "0934567895",
				"status":     "ACTIVE",
				"levelId":    "level11",
				"bPay":       200000,
			}, {
				"_id":        "0934567894",
				"businessId": "0934567890",
				"userId":     "0934567894",
				"status":     "ACTIVE",
				"levelId":    "level22",
				"bPay":       300000,
			}, {
				"_id":        "0934567893",
				"businessId": "0934567890",
				"userId":     "0934567893",
				"status":     "ACTIVE",
				"levelId":    "level11",
				"bPay":       500000,
			}, {
				"_id":        "0934567892",
				"businessId": "0934567890",
				"userId":     "0934567892",
				"status":     "INACTIVE",
				"levelId":    "level11",
				"bPay":       0,
			},
		})

		// Noti
		CreateUser([]map[string]interface{}{
			{
				"phone": "0734567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0734567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0734567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0734567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0734567895",
				"name":  "Asker 05",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "0734567890",
				"status": "ACTIVE",
				"bPay":   5000,
				"topUpSetting": map[string]interface{}{
					"status":     "ACTIVE",
					"period":     1,
					"nextTime":   currentTime.AddDate(0, 0, 5),
					"dayInMonth": "FIRST_DAY_IN_MONTH",
				},
			},
		})
		CreateBusinessLevel([]map[string]interface{}{
			{
				"_id":        "0734567890level1",
				"name":       "level1",
				"status":     "ACTIVE",
				"businessId": "0734567890",
				"amount":     675000,
			}, {
				"_id":        "0734567890level2",
				"name":       "level2",
				"status":     "ACTIVE",
				"businessId": "0734567890",
				"amount":     1000000,
			},
		})
		CreateBusinessMember([]map[string]interface{}{
			{
				"_id":        "0734567895",
				"businessId": "0734567890",
				"userId":     "0734567895",
				"status":     "ACTIVE",
				"levelId":    "0734567890level1",
				"bPay":       200000,
			}, {
				"_id":        "0734567894",
				"businessId": "0734567890",
				"userId":     "0734567894",
				"status":     "ACTIVE",
				"levelId":    "0734567890level2",
				"bPay":       300000,
			}, {
				"_id":        "0734567893",
				"businessId": "0734567890",
				"userId":     "0734567893",
				"status":     "ACTIVE",
				"levelId":    "0734567890level1",
				"bPay":       500000,
			}, {
				"_id":        "0734567892",
				"businessId": "0734567890",
				"userId":     "0734567892",
				"status":     "INACTIVE",
				"levelId":    "0734567890level1",
				"bPay":       0,
			},
		})
	})
	Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
		body := map[string]interface{}{
			"action": 1,
		}
		reqBody, _ := json.Marshal(body)
		req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
		resp := httptest.NewRecorder()

		Convey("When the request is handled by the Router", func() {
			service.NewRouter().ServeHTTP(resp, req)

			Convey("Then check the response to test", func() {
				respResult := map[string]interface{}{}
				bytes, _ := io.ReadAll(resp.Body)
				json.Unmarshal(bytes, &respResult)
				So(resp.Code, ShouldEqual, 200)
				func() {
					// Check business bpay revoke
					business, _ := modelBusiness.GetOne(local.ISO_CODE, bson.M{"_id": "0834567890"}, bson.M{"_id": 1, "bPay": 1})
					So(business.BPay, ShouldEqual, 5000000+200000+300000+500000+700000)

					members, _ := modelBusinessMember.GetAll(local.ISO_CODE, bson.M{"businessId": "0834567890"}, bson.M{"userId": 1, "levelId": 1, "bPay": 1, "status": 1})
					So(len(members), ShouldEqual, 4)
					for _, v := range members {
						switch v.UserId {
						case "0834567895":
							So(v.BPay, ShouldEqual, 0)
						case "0834567894":
							So(v.BPay, ShouldEqual, 0)
						case "0834567893":
							So(v.BPay, ShouldEqual, 0)
						case "0834567892":
							So(v.BPay, ShouldEqual, 0)
						}
					}

					bTrans, _ := modelBusinessTransaction.GetAll(local.ISO_CODE, bson.M{"businessId": "0834567890"}, bson.M{"_id": 1, "memberId": 1, "amount": 1, "type": 1, "name": 1})
					So(len(bTrans), ShouldEqual, 4)
					for _, v := range bTrans {
						So(v.Type, ShouldEqual, "D")
						So(v.Name, ShouldEqual, "REVOKE_BPAY_MEMBER")
						switch v.MemberId {
						case "0834567895":
							So(v.Amount, ShouldEqual, 200000)
						case "0834567894":
							So(v.Amount, ShouldEqual, 300000)
						case "0834567893":
							So(v.Amount, ShouldEqual, 500000)
						case "0834567892":
							So(v.Amount, ShouldEqual, 700000)
						}
					}

					bmTrans, _ := modelBusinessMemberTransaction.GetAll(local.ISO_CODE, bson.M{"businessId": "0834567890"}, bson.M{"_id": 1, "memberId": 1, "amount": 1, "type": 1, "name": 1})
					So(len(bmTrans), ShouldEqual, 4)
					for _, v := range bmTrans {
						So(v.Type, ShouldEqual, "C")
						So(v.Name, ShouldEqual, "REVOKE_BPAY_BY_BUSINESS")
						switch v.MemberId {
						case "0834567895":
							So(v.Amount, ShouldEqual, 200000)
						case "0834567894":
							So(v.Amount, ShouldEqual, 300000)
						case "0834567893":
							So(v.Amount, ShouldEqual, 500000)
						case "0834567892":
							So(v.Amount, ShouldEqual, 700000)
						}
					}
				}()

				func() {
					business, _ := modelBusiness.GetOne(local.ISO_CODE, bson.M{"_id": "0934567890"}, bson.M{"_id": 1, "bPay": 1})
					So(business.BPay, ShouldEqual, 5000000-(675000*2)-1000000)

					members, _ := modelBusinessMember.GetAll(local.ISO_CODE, bson.M{"businessId": "0934567890"}, bson.M{"userId": 1, "levelId": 1, "bPay": 1, "status": 1})
					So(len(members), ShouldEqual, 4)
					for _, v := range members {
						switch v.UserId {
						case "0934567895":
							So(v.BPay, ShouldEqual, 200000+675000)
						case "0934567894":
							So(v.BPay, ShouldEqual, 300000+1000000)
						case "0934567893":
							So(v.BPay, ShouldEqual, 500000+675000)
						case "0934567892":
							So(v.Status, ShouldEqual, "INACTIVE")
							So(v.BPay, ShouldEqual, 0)
						}
					}

					bTrans, _ := modelBusinessTransaction.GetAll(local.ISO_CODE, bson.M{"businessId": "0934567890"}, bson.M{"_id": 1, "memberId": 1, "amount": 1, "type": 1, "name": 1})
					So(len(bTrans), ShouldEqual, 3)
					for _, v := range bTrans {
						So(v.Type, ShouldEqual, "C")
						So(v.Name, ShouldEqual, "TOP_UP_BPAY_MEMBER")
						switch v.MemberId {
						case "0934567895":
							So(v.Amount, ShouldEqual, 675000)
						case "0934567894":
							So(v.Amount, ShouldEqual, 1000000)
						case "0934567893":
							So(v.Amount, ShouldEqual, 675000)
						}
					}

					bmTrans, _ := modelBusinessMemberTransaction.GetAll(local.ISO_CODE, bson.M{"businessId": "0934567890"}, bson.M{"_id": 1, "memberId": 1, "amount": 1, "type": 1, "name": 1})
					So(len(bmTrans), ShouldEqual, 3)
					for _, v := range bmTrans {
						So(v.Type, ShouldEqual, "D")
						So(v.Name, ShouldEqual, "TOP_UP_BPAY_BY_BUSINESS")
						switch v.MemberId {
						case "0934567895":
							So(v.Amount, ShouldEqual, 675000)
						case "0934567894":
							So(v.Amount, ShouldEqual, 1000000)
						case "0934567893":
							So(v.Amount, ShouldEqual, 675000)
						}
					}
				}()

				func() {
					// check notify
					var notifies []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{}, bson.M{}, &notifies)
					So(len(notifies), ShouldEqual, 10)
					for _, noti := range notifies {
						switch noti.UserId {
						case "0834567895":
							So(noti.Description, ShouldEqual, "Hệ thống vừa thu hồi 200,000 ₫ từ tài khoản bPay doanh nghiệp của bạn về tài khoản doanh nghiệp")
						case "0834567894":
							So(noti.Description, ShouldEqual, "Hệ thống vừa thu hồi 300,000 ₫ từ tài khoản bPay doanh nghiệp của bạn về tài khoản doanh nghiệp")
						case "0834567893":
							So(noti.Description, ShouldEqual, "Hệ thống vừa thu hồi 500,000 ₫ từ tài khoản bPay doanh nghiệp của bạn về tài khoản doanh nghiệp")
						case "0834567892":
							So(noti.Description, ShouldEqual, "Hệ thống vừa thu hồi 700,000 ₫ từ tài khoản bPay doanh nghiệp của bạn về tài khoản doanh nghiệp")
						case "0834567890":
							So(noti.Description, ShouldEqual, "Hệ thống đã thực hiện thu hồi thành công 1,700,000 ₫ từ các nhóm thành viên về tài khoản bPay doanh nghiệp của bạn.")
						case "0934567895":
							So(noti.Description, ShouldEqual, "Bạn vừa được cộng 675,000 ₫ vào tài khoản bPay doanh nghiệp.")
						case "0934567893":
							So(noti.Description, ShouldEqual, "Bạn vừa được cộng 675,000 ₫ vào tài khoản bPay doanh nghiệp.")
						case "0934567894":
							So(noti.Description, ShouldEqual, "Bạn vừa được cộng 1,000,000 ₫ vào tài khoản bPay doanh nghiệp.")
						case "0934567890":
							So(noti.Description, ShouldEqual, "Hệ thống đã thực hiện nạp tiền thành công vào tài khoản bPay doanh nghiệp cho tất cả các nhóm thành viên.")
						case "0734567890":
							So(noti.Description, ShouldEqual, "Số dư tài khoản bPay doanh nghiệp của bạn không đủ để hệ thống thực hiện giao dịch phân bổ cho các thành viên ở chu kì tiếp theo. Vui lòng nạp thêm để không làm gián đoạn giao dịch.")
						}
					}
				}()
			})
		})
	})
}
