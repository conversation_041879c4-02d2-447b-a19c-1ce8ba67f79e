package autoFindTaskersHaveStarCanDoTestTraining

import (
	"fmt"
	"log"

	"github.com/robfig/cron/v3"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/model"
)

var (
	c            *cron.Cron
	cfg          = config.GetConfig()
	isRunning    = false
	paramsEnough = true
	runAt        string
	LIMIT        int64 = 2000
)

func init() {
	var cronConfig map[string]interface{}
	if cfg != nil {
		cronConfig = cfg.ScheduleSyncCron["autofindtaskershavestarcandotesttraining"]
	}
	// Check if params enough
	if cronConfig["run_at"] == nil {
		paramsEnough = false
		lib.PostToSlackNotStart("AutoFindTaskersHaveStarCanDoTestTraining")
		return
	}

	// Get runAt string for sync cron
	runAt = fmt.Sprintf(cronConfig["run_at"].(string))

	// Check if config has is_run = true
	if cronConfig["is_run"] == nil || !cronConfig["is_run"].(bool) {
		lib.PostToSlackNotStart("AutoFindTaskersHaveStarCanDoTestTraining")
		return
	}

	// Start cron if config["is_run"] = true
	StartCron()
}

func StartCron() {
	c := cron.New(cron.WithLocation(local.TimeZone))
	c.AddFunc(runAt, AutoFindTaskersHaveStarCanDoTestTraining)
	c.Start()
	isRunning = true
}

func Run(reqBody *model.RequestAction) map[string]interface{} {
	// action == 1 -> Run sync cron once now
	if reqBody.Action == lib.RUN_NOW {
		AutoFindTaskersHaveStarCanDoTestTraining()
	}

	// action == 2 -> Start sync cron with schedule config
	if reqBody.Action == lib.START && paramsEnough && !isRunning {
		StartCron()
	}
	// action == 3 -> Stop running sync cron
	if reqBody.Action == lib.STOP && isRunning {
		c.Stop()
		c = nil
		isRunning = false
	}
	return nil
}

func AutoFindTaskersHaveStarCanDoTestTraining() {
	log.Println("Start AutoFindTaskersHaveStarCanDoTestTraining Process")
	defer log.Println("Finish AutoFindTaskersHaveStarCanDoTestTraining Process")
	process()
}

func process() {
	courses := getCourses()
	courseStartDate := getCourseStartDate_new(courses)
	createCourseStartDate(courseStartDate)
}
