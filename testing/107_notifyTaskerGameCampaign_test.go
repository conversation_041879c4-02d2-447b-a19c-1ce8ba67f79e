package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	"go.mongodb.org/mongo-driver/bson"
)

func TestNotifyTaskerGameCampaign(t *testing.T) {
	apiUrl := "/api/v3/sync-cron-vn/notify-tasker-game-campaign"
	// game campaign 3 day remaining
	t.Run("1", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567891",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)

		startDate := globalLib.StartADay(currentTime.AddDate(0, 0, -1))
		endDate := globalLib.EndADay(currentTime.AddDate(0, 0, 3))

		gameCampaignIds := CreateTaskerGameCampaign(map[string]interface{}{
			"startDate": startDate,
			"endDate":   endDate,
		})

		CreateTaskerUserGameCampaign([]map[string]interface{}{
			{
				"userId":         "0834567891",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   0,
				"isUnlockedSpin": true,
			},
			{
				"userId":         "0834567892",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   0,
				"isUnlockedSpin": true,
			},
			{
				"userId":         "0834567893",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   0,
			},
		})

		body := map[string]interface{}{
			"action": lib.RUN_NOW,
		}

		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("Given HTTP request for api %s", apiUrl), t, func() {
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			Convey("Check the response and database", func() {
				// Check data
				So(res.Code, ShouldEqual, 200)

				var notifications []*modelNotification.Notification
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{}, bson.M{}, &notifications)

				So(len(notifications), ShouldEqual, 2)
				tested := map[string]bool{}
				for _, noti := range notifications {
					switch noti.UserId {
					case "0834567891":
						So(noti.Title, ShouldEqual, "Đếm ngược 3 ngày, quay ngay kẻo lỡ!")
						So(noti.Description, ShouldEqual, "Đếm ngược 3 ngày, quay ngay kẻo lỡ!")
						So(noti.NavigateTo, ShouldEqual, globalConstant.PAYLOAD_NAVIGATE_TO_GAME_LUCKY_DRAW)
						tested[noti.UserId] = true
					case "0834567892":
						So(noti.Title, ShouldEqual, "Đếm ngược 3 ngày, quay ngay kẻo lỡ!")
						So(noti.Description, ShouldEqual, "Đếm ngược 3 ngày, quay ngay kẻo lỡ!")
						So(noti.NavigateTo, ShouldEqual, globalConstant.PAYLOAD_NAVIGATE_TO_GAME_LUCKY_DRAW)
						tested[noti.UserId] = true
					}
				}
				So(len(tested), ShouldEqual, 2)
			})
		})
	})
	// game campaign 1 day remaining
	t.Run("2", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567891",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)

		startDate := globalLib.StartADay(currentTime.AddDate(0, 0, -1))
		endDate := globalLib.EndADay(currentTime.AddDate(0, 0, 1))

		gameCampaignIds := CreateTaskerGameCampaign(map[string]interface{}{
			"startDate": startDate,
			"endDate":   endDate,
		})

		CreateTaskerUserGameCampaign([]map[string]interface{}{
			{
				"userId":         "0834567891",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   0,
				"isUnlockedSpin": true,
			},
			{
				"userId":         "0834567892",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   0,
				"isUnlockedSpin": true,
			},
			{
				"userId":         "0834567893",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   0,
			},
		})

		body := map[string]interface{}{
			"action": lib.RUN_NOW,
		}

		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("Given HTTP request for api %s", apiUrl), t, func() {
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			Convey("Check the response and database", func() {
				// Check data
				So(res.Code, ShouldEqual, 200)

				var notifications []*modelNotification.Notification
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{}, bson.M{}, &notifications)

				So(len(notifications), ShouldEqual, 2)
				tested := map[string]bool{}
				for _, noti := range notifications {
					switch noti.UserId {
					case "0834567891":
						So(noti.Title, ShouldEqual, "Đếm ngược 1 ngày, quay ngay kẻo lỡ!")
						So(noti.Description, ShouldEqual, "Đếm ngược 1 ngày, quay ngay kẻo lỡ!")
						So(noti.NavigateTo, ShouldEqual, globalConstant.PAYLOAD_NAVIGATE_TO_GAME_LUCKY_DRAW)
						tested[noti.UserId] = true
					case "0834567892":
						So(noti.Title, ShouldEqual, "Đếm ngược 1 ngày, quay ngay kẻo lỡ!")
						So(noti.Description, ShouldEqual, "Đếm ngược 1 ngày, quay ngay kẻo lỡ!")
						So(noti.NavigateTo, ShouldEqual, globalConstant.PAYLOAD_NAVIGATE_TO_GAME_LUCKY_DRAW)
						tested[noti.UserId] = true
					}
				}
				So(len(tested), ShouldEqual, 2)
			})
		})
	})
	// game campaign 2 day remaining
	t.Run("3", func(t *testing.T) {
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567891",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)

		startDate := globalLib.StartADay(currentTime.AddDate(0, 0, -1))
		endDate := globalLib.EndADay(currentTime.AddDate(0, 0, 2))

		gameCampaignIds := CreateTaskerGameCampaign(map[string]interface{}{
			"startDate": startDate,
			"endDate":   endDate,
		})

		CreateTaskerUserGameCampaign([]map[string]interface{}{
			{
				"userId":         "0834567891",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   0,
				"isUnlockedSpin": true,
			},
			{
				"userId":         "0834567892",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   0,
				"isUnlockedSpin": true,
			},
			{
				"userId":         "0834567893",
				"gameCampaignId": gameCampaignIds[0],
				"numberOfSpin":   0,
			},
		})

		body := map[string]interface{}{
			"action": lib.RUN_NOW,
		}

		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("Given HTTP request for api %s", apiUrl), t, func() {
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			Convey("Check the response and database", func() {
				// Check data
				So(res.Code, ShouldEqual, 200)

				var notifications []*modelNotification.Notification
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{}, bson.M{}, &notifications)

				So(len(notifications), ShouldEqual, 0)
			})
		})
	})
}
