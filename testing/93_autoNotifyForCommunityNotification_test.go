package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalCommunityConstant "gitlab.com/btaskee/go-services-model-v2/globalConstant/community"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	"go.mongodb.org/mongo-driver/bson"
)

func TestAutoNotifyForCommunityNotification(t *testing.T) {
	apiUrl := "/api/v3/sync-cron-vn/auto-notify-for-community-notification"
	t.Run("1", func(t *testing.T) {
		// case: asker1 have notification but in 10 hours ago, asker2 have 3 noti, asker 3 have 1 noti,
		// asker4 have 1 noti but isRead, asker5 have 2 noti 1 read 1 noti unread
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567891",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567892",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567893",
				"name":  "Asker 03",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567894",
				"name":  "Asker 04",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{
				"phone": "0834567895",
				"name":  "Asker 05",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		CreateCommunityNotifications([]map[string]interface{}{
			{
				"userId":    "0834567891",
				"createdAt": now.Add(-10 * time.Hour),
			},
			{
				"userId":    "0834567892",
				"createdAt": now.Add(-2 * time.Hour),
			},
			{
				"userId":    "0834567892",
				"createdAt": now.Add(-2 * time.Hour),
			},
			{
				"userId":    "0834567892",
				"createdAt": now.Add(-1 * time.Hour),
			},
			{
				"userId":    "0834567893",
				"createdAt": now.Add(-1 * time.Hour),
			},
			{
				"userId":    "0834567894",
				"isRead":    true,
				"createdAt": now.Add(-1 * time.Hour),
			},
			{
				"userId":    "0834567895",
				"createdAt": now.Add(-1 * time.Hour),
			},
			{
				"userId":    "0834567895",
				"isRead":    true,
				"createdAt": now.Add(-2 * time.Hour),
			},
		}, globalConstant.ISO_CODE_VN)

		body := map[string]interface{}{
			"action": lib.RUN_NOW,
		}

		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("Given HTTP request for api %s", apiUrl), t, func() {
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			Convey("Check the response and database", func() {
				// Check data
				So(res.Code, ShouldEqual, 200)

				var notifications []*modelNotification.Notification
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[globalConstant.ISO_CODE_VN], bson.M{}, bson.M{}, &notifications)

				So(len(notifications), ShouldEqual, 3)

				So(notifications[0].UserId, ShouldEqual, "0834567892")
				So(notifications[0].Description, ShouldEqual, "Bạn có thông báo mới từ Cộng đồng")
				So(notifications[0].NavigateTo, ShouldEqual, globalCommunityConstant.PAYLOAD_NAVIGATE_TO_COMMUNITY_NOTIFICATION)
				So(notifications[1].UserId, ShouldEqual, "0834567893")
				So(notifications[1].Description, ShouldEqual, "Bạn có thông báo mới từ Cộng đồng")
				So(notifications[1].NavigateTo, ShouldEqual, globalCommunityConstant.PAYLOAD_NAVIGATE_TO_COMMUNITY_NOTIFICATION)
				So(notifications[2].UserId, ShouldEqual, "0834567895")
				So(notifications[2].Description, ShouldEqual, "Bạn có thông báo mới từ Cộng đồng")
				So(notifications[2].NavigateTo, ShouldEqual, globalCommunityConstant.PAYLOAD_NAVIGATE_TO_COMMUNITY_NOTIFICATION)
			})
		})
	})
}
