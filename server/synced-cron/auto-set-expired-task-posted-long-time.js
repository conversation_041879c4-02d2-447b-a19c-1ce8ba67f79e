import { Meteor } from 'meteor/meteor';
import { HTTP } from 'meteor/http';
import _ from 'lodash';
import {
  trackingScheduleStatus,
  postToSlack,
  sendNotificationByIds,
  changeTimeZone,
} from '/server/helper-function';

const cancelOrderTiki = (taskId) => {
  if (taskId) {
    const url = Meteor.settings.GO_SERVICE.API_URL + '/v2/payment/cancel-tiki-order';
    const headers = {
      'content-type': 'application/json',
      'accessKey': Meteor.settings.GO_SERVICE.API_KEY,
    };
    const requestData = {
      _id: taskId,
      cancellationReason: "TASK_EXPIRED"
    };
    HTTP.post(url, {headers: headers, data: requestData});
  }
};

const autoRefund_VN = (refundRequestId) => {
  if (refundRequestId) {
    const url = Meteor.settings.GO_SERVICE.API_URL + '/v2/payment/refund-vn';
    const headers = {
      'content-type': 'application/json',
      'accessKey': Meteor.settings.GO_SERVICE.API_KEY,
    };
    const refundData = {
      refundRequestId: refundRequestId,
      option: {
        from: "SYSTEM",
        userId: "ExpiredTask",
        userName: "ExpiredTask"
      }
    };
    HTTP.post(url, {headers: headers, data: refundData});
  }
};

const createdRefundRequest_TH = (prepaidTaskIds) => {
  Meteor.appCollection.Task.find({_id: {$in: prepaidTaskIds}, isoCode: "TH"}, {fields: {_id: 1, isPrepayTask: 1, 'payment.status': 1, 'payment.method': 1, 'payment.transactionId': 1, askerId: 1, 'costDetail.finalCost': 1}}).forEach((task) => {
    if (task.isPrepayTask && task.payment && task.payment.status === "PAID") {
      var transaction = null;
      var cost, invoice = null;
      if (task.payment.method === "PROMPT_PAY") {
        // Tao FAT
        const fat = {
          userId: task.askerId,
          accountType: "M",
          type: "D",
          source: {
            name: "SYSTEM_REFUND",
            value: task._id,
          },
          amount: task.costDetail.finalCost,
          date: new Date(),
          isoCode: "TH",
          createdAt: new Date(),
        };
        Meteor.appCollection.TH_FATransaction.insert(fat);
        // Cong tien vao tai khoan
        var asker = Meteor.appCollection.users.findOne({_id: task.askerId}, {fields: {fAccountId: 1}});
        if (asker && asker.fAccountId) {
          Meteor.appCollection.FinancialAccount.update({_id: asker.fAccountId}, {$inc: {TH_FMainAccount: task.costDetail.finalCost}});
        } else {
          const msg = `FA Transaction was created, but the customer's account isn't increased. Task id: ${task._id}`;
          postToSlack({
            channel: 'payment-th',
            text: msg,
          });
        }
      } else {
        if (task.payment.method === "SHOPEE_PAY") {
          transaction = Meteor.appCollection.ShopeePayTransaction.findOne({_id: task.payment.transactionId, charged: true}, {fields: {amount: 1, _id: 1, paymentReferenceId: 1}});
          cost = transaction && transaction.amount ? transaction.amount : null;
          if (transaction) {
            invoice = transaction.paymentReferenceId;
            if (!invoice) {
              invoice = transaction._id;
            }
          }
        } else if (task.payment.method === "TRUE_MONEY") {
          transaction = Meteor.appCollection.Payment2C2PTransaction.findOne({_id: task.payment.transactionId, charged: true}, {fields: {cost: 1, invoiceNo: 1}});
          cost = transaction && transaction.cost ? transaction.cost : null;
          invoice = transaction && transaction.invoiceNo ? transaction.invoiceNo : null;
        }
        if (transaction && cost && invoice) {
          const refundRequest = {
            type: "SYSTEM_REFUND",
            amount: cost,
            paymentMethod: task.payment.method,
            currency: "THB",
            status: "NEW",
            userId: task.askerId,
            taskId: task._id,
            invoiceNo: invoice,
            reason: "TASK_EXPIRED",
            createdAt: new Date()
          };
          Meteor.appCollection.TH_RefundRequest.insert(refundRequest);
        } else {
          const msg = `Create refund request failed. Task id: ${task._id}, payment: ${task.payment.method}`;
          postToSlack({
            channel: 'payment-th',
            text: msg,
          });
        }
      }
    }
  });
};

const createdRefundRequest_VN = (prepaidTaskIds) => {
  Meteor.appCollection.Task.find({_id: {$in: prepaidTaskIds}, isoCode: "VN"}, {fields: {_id: 1, isPrepayTask: 1, 'payment.status': 1, 'payment.method': 1, 'payment.transactionId': 1, askerId: 1, fromPartner: 1}}).forEach((task) => {
    if (task.isPrepayTask && task.payment && task.payment.status === "PAID") {
      if (task.fromPartner === "TIKI_MINI_APP") {
        cancelOrderTiki(task._id);
      } else {
        var transaction = null;
        var cost, invoice = null;
        if (task.payment.method === "SHOPEE_PAY") {
          transaction = Meteor.appCollection.VN_ShopeePayTransaction.findOne({_id: task.payment.transactionId, charged: true}, {fields: {amount: 1, _id: 1, paymentReferenceId: 1}});
          cost = transaction && transaction.amount ? transaction.amount : null;
          if (transaction) {
            invoice = transaction.paymentReferenceId;
            if (!invoice) {
              invoice = transaction._id;
            }
          }
        } else if (task.payment.method === "MOMO") {
          transaction = Meteor.appCollection.MomoTransaction.findOne({_id: task.payment.transactionId, charged: true}, {fields: {amount: 1, transId: 1}});
          cost = transaction && transaction.amount ? transaction.amount : null;
          invoice = transaction && transaction.transId ? transaction.transId : null;
        } else if (task.payment.method === "ZALO_PAY") {
          transaction = Meteor.appCollection.ZaloPayTransaction.findOne({_id: task.payment.transactionId, charged: true}, {fields: {amount: 1, zptransid: 1}});
          cost = transaction && transaction.amount ? transaction.amount : null;
          if (transaction && transaction.zptransid) {
            if (typeof transaction.zptransid === "string") {
              invoice = transaction.zptransid;
            } else {
              invoice = transaction.zptransid.toFixed(0);
            }
          }
        } else if (task.payment.method === "VN_PAY") {
          transaction = Meteor.appCollection.VNPayTransaction.findOne({_id: task.payment.transactionId, charged: true}, {fields: {amount: 1, _id: 1}});
          cost = transaction && transaction.amount ? transaction.amount : null;
          invoice = transaction && transaction._id ? transaction._id : null;
        }
        if (transaction && cost && invoice) {
          const refundRequest = {
            type: "SYSTEM_REFUND",
            amount: cost,
            paymentMethod: task.payment.method,
            currency: "VND",
            status: "NEW",
            userId: task.askerId,
            taskId: task._id,
            invoiceNo: invoice,
            reason: "TASK_EXPIRED",
            createdAt: new Date()
          };
          const refundRequestId = Meteor.appCollection.RefundRequest.insert(refundRequest);
          autoRefund_VN(refundRequestId);
        } else {
          const msg = `Lỗi tạo yêu cầu hoàn tiền. Task id: ${task._id}, hình thức thanh toán: ${task.payment.method}`;
          postToSlack({
            channel: 'payment-vn',
            text: msg,
          });
        }
      }
    }
  });
};

const refundTaskExpiredInSub_TH = (taskIds) => {
  Meteor.appCollection.Task.find({_id: {$in: taskIds}, isoCode: "TH"}, {fields: {_id: 1, subscriptionId: 1, askerId: 1, 'costDetail.finalCost': 1}}).forEach((task) => {
    if (task && task.subscriptionId) {
      // Tao FAT
      const fat = {
        userId: task.askerId,
        accountType: "M",
        type: "D",
        source: {
          name: "SYSTEM_REFUND",
          value: task.subscriptionId,
          taskId: task._id,
        },
        amount: task.costDetail.finalCost,
        date: new Date(),
        isoCode: "TH",
        createdAt: new Date(),
      };
      Meteor.appCollection.TH_FATransaction.insert(fat);
      // Cong tien vao tai khoan
      var asker = Meteor.appCollection.users.findOne({_id: task.askerId}, {fields: {fAccountId: 1}});
      if (asker && asker.fAccountId) {
        Meteor.appCollection.FinancialAccount.update({_id: asker.fAccountId}, {$inc: {TH_FMainAccount: task.costDetail.finalCost}});
      }
    }
  });
};

const refundTaskExpiredInSub_VN = (taskIds) => {
  Meteor.appCollection.Task.find({_id: {$in: taskIds}, isoCode: "VN"}, {fields: {_id: 1, subscriptionId: 1, askerId: 1, 'costDetail.finalCost': 1}}).forEach((task) => {
    if (task && task.subscriptionId) {
      // Tao FAT
      const fat = {
        userId: task.askerId,
        accountType: "M",
        type: "D",
        source: {
          name: "SYSTEM_REFUND",
          value: task.subscriptionId,
          taskId: task._id,
        },
        amount: task.costDetail.finalCost,
        date: new Date(),
        isoCode: "VN",
        createdAt: new Date(),
      };
      Meteor.appCollection.FATransaction.insert(fat);
      // Cong tien vao tai khoan
      var asker = Meteor.appCollection.users.findOne({_id: task.askerId}, {fields: {fAccountId: 1}});
      if (asker && asker.fAccountId) {
        Meteor.appCollection.FinancialAccount.update({_id: asker.fAccountId}, {$inc: {FMainAccount: task.costDetail.finalCost}});
      }
    }
  });
};

const runAutoSetExpiredlTask = () => {
  return;
  console.log("Start runAutoSetExpiredTask ...");

  const hourInVNTime = changeTimeZone(new Date()).getHours();
  if ( hourInVNTime > 22 || hourInVNTime < 6 ) {
    // Don't run this job from 10PM to next 8AM
    return;
  }
  var message = null;
  const taskCondition = {
    date: {$lte: new Date()},
    status: 'POSTED',
  };

  let askerIds = [];
  let expireTaskIds = [];
  // let warningTask = [];
  let promotionHistoryIds = [];
  let giftIds = [];
  let prepaidTaskIds_TH = [];
  let prepaidTaskIds_VN = [];
  let subTaskIds_TH = [];
  let subTaskIds_VN = [];
  // Get expireTaskIds and remove new task notification first
  Meteor.appCollection.Task.find(taskCondition, {fields: {
    _id: 1,
    phone: 1,
    askerId: 1,
    serviceText: 1,
    promotion: 1,
    isPrepayTask: 1,
    isoCode: 1,
    subscriptionId: 1
  }}).forEach((task) => {
    // if ( _.get(task, 'serviceText.en', null) === 'Deep Cleaning' ) {
    //   // Warning with Deep cleaning service
    //   warningTask.push(task.phone);
    // } else {
      // Set expired with other services
      askerIds.push(task.askerId);
      expireTaskIds.push(task._id);
      if (task.isPrepayTask) {
        if (task.isoCode === "TH") {
          prepaidTaskIds_TH.push(task._id);
        } else {
          prepaidTaskIds_VN.push(task._id);
        }
      }
      if (task.subscriptionId) {
        if (task.isoCode === "TH") {
          subTaskIds_TH.push(task._id);
        } else {
          subTaskIds_VN.push(task._id);
        }
      }

      // Remove the used promotion history too
      if ( task.promotion && task.promotion.code ) {
        const promoHistory = Meteor.appCollection.PromotionHistory.findOne({
          userId: task.askerId,
          promotionCode: task.promotion.code
        }, { fields: { _id: 1 } });
        if ( promoHistory && promoHistory._id ) {
          promotionHistoryIds.push(promoHistory._id);
        }

        const gift = Meteor.appCollection.Gift.findOne({userId: task.askerId, promotionCode: task.promotion.code}, {fields: {_id: 1}});
        if (gift && gift._id) {
          giftIds.push(gift._id);
        }
      }
    // }
  });
  if ( expireTaskIds && expireTaskIds.length > 0 ) {
    Meteor.appCollection.Notification.remove({ taskId: { $in: expireTaskIds }, type: 0 });

    // Update task status to expired
    Meteor.appCollection.Task.update( { _id: { $in: expireTaskIds } }, {
      $set: {
        status: 'EXPIRED',
        updatedAt: new Date(),
        isSetExpiredBySyncedCron: true,
      }
    }, {
      multi: true
    });

    // Remove old used promotion history
    if ( promotionHistoryIds && promotionHistoryIds.length > 0 ) {
      Meteor.appCollection.PromotionHistory.remove({ _id: {$in: promotionHistoryIds} })
    }

    // Remove used gift
    if (giftIds.length > 0) {
      Meteor.appCollection.Gift.update({_id: {$in: giftIds}}, {$unset: {used: 1, usedAt: 1}}, {multi: true});
    }

    const notificationText = {
      vi: 'bTaskee chân thành xin lỗi Quý khách. Tất cả Cộng tác viên của bTaskee đều đang bận trong thời gian này, xin Quý khách vui lòng đăng việc lại cho ngày khác.',
      en: 'bTaskee sincerely apologizes for this inconvenience. All bTaskers are occupied during this time, kindly reschedule your task to another day.',
      ko: 'bTaskee sincerely apologizes for this inconvenience. All bTaskers are occupied during this time, kindly reschedule your task to another day.',
      th: 'bTaskee sincerely apologizes for this inconvenience. All bTaskers are occupied during this time, kindly reschedule your task to another day.'
    }
    if ( notificationText && notificationText.en && notificationText.ko && notificationText.vi ) {
      sendNotificationByIds(askerIds, {
        title: {
          vi: '',
          en: '',
          ko: '',
          th: '',
        },
        text: {
          vi: notificationText.vi,
          en: notificationText.en,
          ko: notificationText.ko,
          th: notificationText.th,
        },
        payload: {},
      }, { isForceView: true });
    }
  }
  if (prepaidTaskIds_TH && prepaidTaskIds_TH.length > 0) {
    createdRefundRequest_TH(prepaidTaskIds_TH);
  }
  if (prepaidTaskIds_VN && prepaidTaskIds_VN.length > 0) {
    createdRefundRequest_VN(prepaidTaskIds_VN);
  }
  if (subTaskIds_TH && subTaskIds_TH.length > 0) {
    refundTaskExpiredInSub_TH(subTaskIds_TH);
  }
  if (subTaskIds_VN && subTaskIds_VN.length > 0) {
    refundTaskExpiredInSub_VN(subTaskIds_VN);
  }
  // if ( warningTask && warningTask.length > 0 ) {
  //   const warningText = `Cần CS xử lý: Công việc Tổng vệ sinh của các khách hàng chưa có người nhận: ${_.uniq(warningTask).join('\n')}`;
  //   postToSlack({
  //     channel: 'btaskee-system',
  //     text: warningText,
  //   });
  // }
  console.log("Finished runAutoSetExpiredTask ");
  // Tracking
  trackingScheduleStatus({name: 'autoExpirePostedTaskLongTime', nextRunAt: SyncedCron.nextScheduledAtDate('autoExpirePostedTaskLongTime'), message});
};

Meteor.methods({
  autoExpirePostedTaskLongTime() {
    SyncedCron.add({
      name: 'autoExpirePostedTaskLongTime',
      schedule(parser) {
        // Run at every 5 minutes
        return parser.cron('0 0/5 * 1/1 * ? *', true);
      },
      job() {
        runAutoSetExpiredlTask();
      },
    });
    console.log("Added autoExpirePostedTaskLongTime cron");
  },
  'runNow.autoExpirePostedTaskLongTime'() {
    runAutoSetExpiredlTask();
  },
});
