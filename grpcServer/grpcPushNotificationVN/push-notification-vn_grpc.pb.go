// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcPushNotificationVN

import (
	context "context"
	pushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PushNotificationVNClient is the client API for PushNotificationVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PushNotificationVNClient interface {
	Send(ctx context.Context, in *pushNotificationRequest.PushNotificationRequest, opts ...grpc.CallOption) (*response.Response, error)
}

type pushNotificationVNClient struct {
	cc grpc.ClientConnInterface
}

func NewPushNotificationVNClient(cc grpc.ClientConnInterface) PushNotificationVNClient {
	return &pushNotificationVNClient{cc}
}

func (c *pushNotificationVNClient) Send(ctx context.Context, in *pushNotificationRequest.PushNotificationRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPushNotificationVN.PushNotificationVN/Send", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PushNotificationVNServer is the server API for PushNotificationVN service.
// All implementations must embed UnimplementedPushNotificationVNServer
// for forward compatibility
type PushNotificationVNServer interface {
	Send(context.Context, *pushNotificationRequest.PushNotificationRequest) (*response.Response, error)
	mustEmbedUnimplementedPushNotificationVNServer()
}

// UnimplementedPushNotificationVNServer must be embedded to have forward compatible implementations.
type UnimplementedPushNotificationVNServer struct {
}

func (UnimplementedPushNotificationVNServer) Send(context.Context, *pushNotificationRequest.PushNotificationRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Send not implemented")
}
func (UnimplementedPushNotificationVNServer) mustEmbedUnimplementedPushNotificationVNServer() {}

// UnsafePushNotificationVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PushNotificationVNServer will
// result in compilation errors.
type UnsafePushNotificationVNServer interface {
	mustEmbedUnimplementedPushNotificationVNServer()
}

func RegisterPushNotificationVNServer(s *grpc.Server, srv PushNotificationVNServer) {
	s.RegisterService(&_PushNotificationVN_serviceDesc, srv)
}

func _PushNotificationVN_Send_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationRequest.PushNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationVNServer).Send(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPushNotificationVN.PushNotificationVN/Send",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationVNServer).Send(ctx, req.(*pushNotificationRequest.PushNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PushNotificationVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcPushNotificationVN.PushNotificationVN",
	HandlerType: (*PushNotificationVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Send",
			Handler:    _PushNotificationVN_Send_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "push-notification-vn.proto",
}
