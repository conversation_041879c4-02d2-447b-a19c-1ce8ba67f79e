package cancelTaskForceTasker

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func updateTaskResend(tasks []*modelTask.Task) {
	for _, task := range tasks {
		// Get current timestamp
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		// Create data update
		dataUpdate := bson.M{
			"updatedAt": currentTime,
		}
		dataHistory := initDataChangesHistory_TaskResend(task)
		// Update task
		_, err := globalDataAccess.UpdateOneById(
			globalCollection.COLLECTION_TASK[local.ISO_CODE],
			task.XId,
			bson.M{
				"$set":   dataUpdate,
				"$unset": bson.M{"forceTasker": 1, "dateOptions": 1},
				"$push":  bson.M{"changesHistory": dataHistory},
			},
		)
		if err != nil {
			msg := fmt.Sprintf("[go-sync-cron-vn] Update task failed: taskId %s. Error: %s", task.XId, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
			return
		}
	}
}

func initDataChangesHistory_TaskResend(task *modelTask.Task) map[string]interface{} {
	//prepare field changesHistory
	history := make(map[string]interface{})
	history["key"] = globalConstant.CHANGES_HISTORY_KEY_RESEND_FORCE_TASKER_TASKER_REJECTED
	//prepare field content in changesHistory
	content := make(map[string]interface{})
	content["forceTasker"] = task.ForceTasker
	// prepare field in changesHistory
	history["createdBy"] = task.ForceTasker.TaskerId
	history["content"] = content
	history["createdAt"] = globalLib.GetCurrentTime(local.TimeZone)
	// return changesHistory
	return history
}
