---
apiVersion: v1
kind: Service
metadata:
  name: go-email-vn-v3
  namespace: kong
  labels:
    app: go-email-vn-v3
spec:
  ports:
    - port: 80
      targetPort: 18100
      name: http
    - port: 81
      targetPort: 18101
      name: grpc
  selector:
    app: go-email-vn-v3

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: go-email-vn-v3
  namespace: kong
  labels:
    app: go-email-vn-v3
spec:
  replicas: 1
  selector:
    matchLabels:
      app: go-email-vn-v3
  template:
    metadata:
      labels:
        app: go-email-vn-v3
        log-label: "go-services"
    spec:
      containers:
        - name: go-email-vn-v3
          image: linhnhdocker/go-email-service:vn-3.0.0
          imagePullPolicy: "IfNotPresent"
          ports:
            - containerPort: 80
              name: http
            - containerPort: 81
              name: grpc