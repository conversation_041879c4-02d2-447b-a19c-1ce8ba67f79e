package config

import (
	"fmt"
	"os"

	Viper "github.com/spf13/viper"
	"gitlab.com/btaskee/go-services-model-v2/dbConfig"
)

type Config struct {
	REST_Port                  string `mapstructure:"running_rest_send_task_vn_v3_port"`
	GRPC_Port                  string `mapstructure:"running_grpc_send_task_vn_v3_port"`
	GRPC_Push_Notification_URL string `mapstructure:"grpc_push_notification_vn_v3_url"`
	GRPC_Websocket_Service_URL string `mapstructure:"grpc_websocket_vn_v3_url"`
	SlackToken                 string
	MongoDriver                *dbConfig.MongosDriver
}

var cfg *Config

func init() {
	var folder string
	mode := os.Getenv("APPLICATION_MODE")

	switch mode {
	case "dev", "prod", "test":
		folder = mode
	default:
		folder = "local"
		os.Setenv("APPLICATION_MODE", folder)
	}

	cfgPath := os.Getenv("PUSH_NOTIFICATION_NEW_TASK_SERVICE_CONFIG")
	if cfgPath == "" {
		cfgPath = "config"
	}
	path := fmt.Sprintf("%v/%v", cfgPath, folder)

	cfg = new(Config)
	if mode != "test" {
		cfg.SlackToken = os.Getenv("SLACK_TOKEN_VN")
	}
	initConfig(path, "base", &cfg)
	initConfig(path, "mongo", &cfg.MongoDriver)
}

func initConfig(configPath, configName string, result interface{}) {
	viper := Viper.New()
	viper.AddConfigPath(configPath)
	viper.SetConfigName(configName)

	err := viper.ReadInConfig() // Find and read the config file
	if err == nil {             // Handle errors reading the config file
		err = viper.Unmarshal(result)
		if err != nil { // Handle errors reading the config file
			panic(fmt.Errorf("fatal error config file: %s", err))
		}
	}
}

func GetConfig() *Config {
	return cfg
}
