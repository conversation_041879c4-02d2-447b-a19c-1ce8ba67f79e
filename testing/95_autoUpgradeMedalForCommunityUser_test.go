package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalCommunityConstant "gitlab.com/btaskee/go-services-model-v2/globalConstant/community"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelCommunityNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/communityNotification"
	modelCommunityUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/communityUser"
	"go.mongodb.org/mongo-driver/bson"
)

func TestAutoUpgradeForCommunityUser(t *testing.T) {
	apiURL := "/api/v3/sync-cron-vn/update-medal"
	createData := func() {
		CreateUser([]map[string]interface{}{
			{
				"phone":    "0834567890",
				"type":     globalConstant.USER_TYPE_ASKER,
				"name":     "Asker 01",
				"language": globalConstant.LANG_VI,
			},
			{
				"phone":    "0834567891",
				"type":     globalConstant.USER_TYPE_ASKER,
				"name":     "Asker 02",
				"language": globalConstant.LANG_VI,
			},
			{
				"phone":    "0834567892",
				"type":     globalConstant.USER_TYPE_ASKER,
				"name":     "Asker 03",
				"language": globalConstant.LANG_VI,
			},
			{
				"phone":     "0823456789",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker 01",
			},
		})
		CreateCommunityUser([]map[string]interface{}{
			{
				"_id":            "0834567890",
				"name":           "Asker 01",
				"avatar":         "https://s3.amazonaws.com/uifaces/faces/twitter/calebogden/128.jpg",
				"bio":            "I am Asker 01",
				"status":         globalConstant.COMMUNITY_USER_STATUS_ACTIVE,
				"numberOfLikes":  50,
				"numberOfPosts":  100,
				"numberOfShares": 150,
				"medals": []map[string]interface{}{
					{
						"_id":    "medal4",
						"isUsed": false,
					},
					{
						"_id":    "medal5",
						"isUsed": true,
					},
				},
			},
			{
				"_id":            "0834567891",
				"name":           "Asker 02",
				"avatar":         "https://s3.amazonaws.com/uifaces/faces/twitter/calebogden/128.jpg",
				"bio":            "I am Asker 02",
				"status":         globalConstant.COMMUNITY_USER_STATUS_ACTIVE,
				"numberOfLikes":  50,
				"numberOfPosts":  100,
				"numberOfShares": 150,
			},
			{
				"_id":            "0834567892",
				"name":           "Asker 03",
				"avatar":         "https://s3.amazonaws.com/uifaces/faces/twitter/calebogden/128.jpg",
				"bio":            "I am Asker 03",
				"status":         globalConstant.COMMUNITY_USER_STATUS_ACTIVE,
				"numberOfLikes":  49,
				"numberOfPosts":  99,
				"numberOfShares": 149,
			},
			{
				"_id":            "0834567893",
				"name":           "Asker 04",
				"avatar":         "https://s3.amazonaws.com/uifaces/faces/twitter/calebogden/128.jpg",
				"bio":            "I am Asker 04",
				"status":         globalConstant.COMMUNITY_USER_STATUS_INACTIVE,
				"numberOfLikes":  1000,
				"numberOfPosts":  1000,
				"numberOfShares": 1000,
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		taskDate := now.AddDate(0, 0, -1)
		sevenMonthAgo := now.AddDate(0, -7, 0)
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,14,3", now.Year(), now.Month(), now.Day()-1),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"createdAt": taskDate,
			},
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,14,3", now.Year(), now.Month(), now.Day()-1),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"createdAt": taskDate,
			},
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,14,3", sevenMonthAgo.Year(), sevenMonthAgo.Month(), sevenMonthAgo.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"createdAt": sevenMonthAgo,
			},
		})

		CreateMedal([]map[string]interface{}{
			{
				"_id": "medal1",
				"text": map[string]interface{}{
					"en": "Medal 01",
					"vi": "Medal 01",
					"th": "Medal 01",
					"id": "Medal 01",
					"ko": "Medal 01",
				},
				"status": globalConstant.COMMUNITY_MEDAL_STATUS_ACTIVE,
				"type":   globalConstant.COMMUNITY_MEDAL_TYPE_LEVEL,
				"conditions": []map[string]interface{}{
					{
						"type":     globalConstant.COMMUNITY_MEDAL_CONDITION_TYPE_NUMBER_OF_LIKES,
						"operator": "gte",
						"amount":   50,
					},
				},
				"tagIds": []string{"tag1", "tag2"},
			},
			{
				"_id": "medal2",
				"text": map[string]interface{}{
					"en": "Medal 02",
					"vi": "Medal 02",
					"th": "Medal 02",
					"id": "Medal 02",
					"ko": "Medal 02",
				},
				"status": globalConstant.COMMUNITY_MEDAL_STATUS_ACTIVE,
				"type":   globalConstant.COMMUNITY_MEDAL_TYPE_LEVEL,
				"conditions": []map[string]interface{}{
					{
						"type":     globalConstant.COMMUNITY_MEDAL_CONDITION_TYPE_NUMBER_OF_POSTS,
						"operator": "gte",
						"amount":   100,
					},
				},
			},
			{
				"_id": "medal3",
				"text": map[string]interface{}{
					"en": "Medal 03",
					"vi": "Medal 03",
					"th": "Medal 03",
					"id": "Medal 03",
					"ko": "Medal 03",
				},
				"status": globalConstant.COMMUNITY_MEDAL_STATUS_ACTIVE,
				"type":   globalConstant.COMMUNITY_MEDAL_TYPE_LEVEL,
				"conditions": []map[string]interface{}{
					{
						"type":     globalConstant.COMMUNITY_MEDAL_CONDITION_TYPE_NUMBER_OF_SHARES,
						"operator": "gte",
						"amount":   150,
					},
				},
			},
			{
				"_id": "medal7",
				"text": map[string]interface{}{
					"en": "Medal 07",
					"vi": "Medal 07",
					"th": "Medal 07",
					"id": "Medal 07",
					"ko": "Medal 07",
				},
				"status": globalConstant.COMMUNITY_MEDAL_STATUS_ACTIVE,
				"type":   globalConstant.COMMUNITY_MEDAL_TYPE_LEVEL,
				"conditions": []map[string]interface{}{
					{
						"type":     globalConstant.COMMUNITY_MEDAL_CONDITION_TYPE_NUMBER_OF_SERVICES_BOOKINGS,
						"operator": "gte",
						"amount":   2,
					},
				},
			},
			{
				"_id": "medal10",
				"text": map[string]interface{}{
					"en": "Medal 10",
					"vi": "Medal 10",
					"th": "Medal 10",
					"id": "Medal 10",
					"ko": "Medal 10",
				},
				"status": globalConstant.COMMUNITY_MEDAL_STATUS_INACTIVE,
				"type":   globalConstant.COMMUNITY_MEDAL_TYPE_LEVEL,
				"conditions": []map[string]interface{}{
					{
						"type":     globalConstant.COMMUNITY_MEDAL_CONDITION_TYPE_NUMBER_OF_SHARES,
						"operator": "gte",
						"amount":   150,
					},
				},
			},
		})
	}

	//case 2 user 0834567890 & 0834567891 win 4 medal
	t.Run("1", func(t *testing.T) {
		ResetData()
		createData()
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When the service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the database", func() {
					var users []*modelCommunityUsers.CommunityUser
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_COMMUNITY_USER[local.ISO_CODE], bson.M{"status": globalConstant.COMMUNITY_USER_STATUS_ACTIVE}, bson.M{"favouriteTagIds": 1, "medals": 1}, &users)
					So(len(users), ShouldEqual, 3)
					for i, user := range users {
						switch i {
						case 0:
							So(user.XId, ShouldEqual, "0834567890")
							So(len(user.Medals), ShouldEqual, 6)
							So(user.Medals[0].XId, ShouldEqual, "medal4")
							So(user.Medals[1].XId, ShouldEqual, "medal5")
							So(user.Medals[2].XId, ShouldEqual, "medal1")
							So(user.Medals[3].XId, ShouldEqual, "medal2")
							So(user.Medals[4].XId, ShouldEqual, "medal3")
							So(user.Medals[5].XId, ShouldEqual, "medal7")
							So(user.FavouriteTagIds, ShouldResemble, []string{"tag1", "tag2"})
						case 1:
							So(user.XId, ShouldEqual, "0834567891")
							So(len(user.Medals), ShouldEqual, 3)
							So(user.Medals[0].XId, ShouldEqual, "medal1")
							So(user.Medals[1].XId, ShouldEqual, "medal2")
							So(user.Medals[2].XId, ShouldEqual, "medal3")
							So(user.FavouriteTagIds, ShouldResemble, []string{"tag1", "tag2"})
						case 2:
							So(user.XId, ShouldEqual, "0834567892")
							So(len(user.Medals), ShouldEqual, 0)
						}
					}

					var notifications []*modelCommunityNotification.CommunityNotification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_COMMUNITY_NOTIFICATION[globalConstant.ISO_CODE_VN], bson.M{}, bson.M{}, &notifications)
					So(len(notifications), ShouldEqual, 7)
					for _, v := range notifications {
						So(v.UserId, ShouldBeIn, []string{"0834567890", "0834567891"})
						So(v.NavigateTo, ShouldEqual, globalCommunityConstant.PAYLOAD_NAVIGATE_TO_COMMUNITY_PROFILE_UPDATE)
						So(v.MedalId, ShouldNotBeEmpty)
					}
				})
			})
		})
	})

}
