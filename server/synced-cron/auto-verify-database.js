import { Meteor } from 'meteor/meteor';
import _ from 'lodash';
import {
  trackingScheduleStatus,
  postToSlack,
} from '/server/helper-function';
import { History } from '/server/lib/collections';

const removeDuplicatedOutstanding = (taskId, status) => {
  if ( status === 'NEW' ) {
    const keepingOutstanding = Meteor.appCollection.OutstandingPayment.findOne({
      'data.taskId': taskId,
      status: 'NEW'
    });

    if ( keepingOutstanding ) {
      Meteor.appCollection.OutstandingPayment.remove({
        'data.taskId': taskId,
        status: 'NEW',
        _id: {$ne: keepingOutstanding._id}
      });
    }
  }
};

const runCheckOutstandingPayment = () => {
  return;
	console.log('Start runCheckOutstandingPayment ...')
  var message = null;

  // Find all outstanding
  const outstandings = Meteor.appCollection.OutstandingPayment.find({}, {fields: {data: 1, status: 1}}).fetch();
  // Group the outstandings by taskId and status
  const group = _.groupBy(outstandings, (item) => (item.data.taskId + '_' + item.status));
  // Filter taskId with status CHARGED / NEW with more than 1 existence
  const result = _.keysIn(group).filter((key) => {
    const [taskId, status] = key.split('_');
    if ( ['CHARGED', 'NEW'].indexOf(status) > -1 && group[key].length > 1 ) {
      // Error was detected here
      removeDuplicatedOutstanding(taskId, status);
      return true;
    }
    return false;
  });
  if ( result.length > 0 ) {
    postToSlack({
      channel: 'cron-jobs-alert',
      text: `Workflow: Auto remove the duplicated NEW Outstanding payment:\n${result.join('\n')}`,
    });
  }

	console.log('Finished runCheckOutstandingPayment - AutoCheckDatabase') 
  // Tracking
  trackingScheduleStatus({name: 'AutoCheckDatabase', nextRunAt: SyncedCron.nextScheduledAtDate('autoCheckDatabase'), message});
};

  Meteor.methods({
    // Auto Done Task
    autoCheckDatabase() {
      SyncedCron.add({
        name: 'autoCheckDatabase',
        schedule(parser) {
          // Run at 20' every 1 hour
          return parser.cron('0 20 0/1 1/1 * ? *', true);
        },
        job() {
          runCheckOutstandingPayment();
        },
      });
	    console.log("Added autoCheckDatabase cron");
    },
    'runNow.autoCheckDatabase'() {
      runCheckOutstandingPayment();
    },
  });
