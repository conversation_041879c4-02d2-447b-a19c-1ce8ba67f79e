syntax = "proto3";
package grpcCSOutboundVN;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcCSOutboundVN";

import "google/protobuf/empty.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/csOutboundRequest/csOutboundRequest.proto";

service CSOutboundVN {
  rpc SendZNSFirstDoneTask(csOutboundRequest.CSOutboundRequest) returns (google.protobuf.Empty) {} 
  rpc SendZNSOTP(csOutboundRequest.CSOutboundRequest) returns (google.protobuf.Empty) {} 
  rpc SendZNSOTPAppTasker(csOutboundRequest.CSOutboundRequest) returns (google.protobuf.Empty) {} 
  rpc SendZNSReceived9THYearTicket(csOutboundRequest.CSOutboundRequest) returns (google.protobuf.Empty) {} 
}