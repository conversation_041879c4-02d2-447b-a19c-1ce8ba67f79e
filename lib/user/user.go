package user

import (
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccessV2"
	"gitlab.com/btaskee/go-services-model-v2/globalRepo"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func GetOneById(id string, fields bson.M) (*modelUser.Users, error) {
	var user *modelUser.Users
	err := globalRepo.GetOneById(modelUser.COLLECTION_USER, id, fields, &user)
	return user, err
}

func GetOneByQuery(query, fields bson.M) (*modelUser.Users, error) {
	var user *modelUser.Users
	err := globalRepo.GetOneByQuery(modelUser.COLLECTION_USER, query, fields, &user)
	return user, err
}

func GetAll(query, fields bson.M, opts ...*globalDataAccessV2.QueryOptions) ([]*modelUser.Users, error) {
	var users []*modelUser.Users
	var err error
	op := globalDataAccessV2.MergeUserOptions(opts...)
	if op.Page > 0 && op.Limit > 0 && len(op.Sort) > 0 {
		err = globalRepo.GetAllByQueryPagingSort(modelUser.COLLECTION_USER, query, fields, op.Page, op.Limit, op.Sort, &users)
	} else if op.Page > 0 && op.Limit > 0 {
		err = globalRepo.GetAllByQueryPaging(modelUser.COLLECTION_USER, query, fields, op.Page, op.Limit, &users)
	} else if op.Limit > 0 && len(op.Sort) > 0 {
		err = globalRepo.GetAllByQueryLimitSort(modelUser.COLLECTION_USER, query, fields, op.Limit, op.Sort, &users)
	} else if len(op.Sort) > 0 {
		err = globalRepo.GetAllByQuerySort(modelUser.COLLECTION_USER, query, fields, op.Sort, &users)
	} else {
		err = globalRepo.GetAllByQuery(modelUser.COLLECTION_USER, query, fields, &users)
	}
	return users, err
}

func InsertOne(data interface{}) error {
	return globalRepo.InsertOne(modelUser.COLLECTION_USER, data)
}

func DeleteAllByQuery(query bson.M) error {
	return globalRepo.DeleteAllByQuery(modelUser.COLLECTION_USER, query)
}
