package cancelTaskForceTasker

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

// Cancel task after task is created 6 hours
func getTasksPostedLongTime() ([]*modelTask.Task, error) {
	// 2. Get list all task need to be cancel
	var tasks []*modelTask.Task
	fields := bson.M{
		"_id":                   1,
		"isPrepayTask":          1,
		"payment.status":        1,
		"payment.method":        1,
		"payment.transactionId": 1,
		"payment.isPayOff":      1,
		"askerId":               1,
		"date":                  1,
		"fromPartner":           1,
		"contactName":           1,
		"phone":                 1,
		"isoCode":               1,
		"costDetail.finalCost":  1,
		"subscriptionId":        1,
		"promotion.code":        1,
		"status":                1,
		"serviceText":           1,
		"originCurrency":        1,
		"currency":              1,
		"address":               1,
		"duration":              1,
		"promotion":             1,
		"serviceId":             1,
		"costDetail.cost":       1,
		"newCostDetail.cost":    1,
		"forceTasker":           1,
		"dateOptions":           1,
		"createdAt":             1,
	}
	fields = globalLib.MergeQuerySocketTask(fields)
	//Expired task after task is created 6 hours exclude range 23.00 to 6.00
	query := bson.M{
		"status":      globalConstant.TASK_STATUS_POSTED,
		"forceTasker": bson.M{"$exists": true},
		"changesHistory.key": bson.M{"$nin": []string{globalConstant.CHAT_HISTORY_KEY_TASKER_REJECT, globalConstant.CHANGES_HISTORY_KEY_TASKER_SUGGESTED_NEW_DATE_OPTION}},
	}
	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		query,
		fields,
		&tasks,
	)
	return tasks, err
}

func getTasks_TaskerRejectedOrSuggestedNewDate() ([]*modelTask.Task, error) {
	// 2. Get list all task need to be expired
	var tasks []*modelTask.Task
	fields := bson.M{
		"_id":                   1,
		"isPrepayTask":          1,
		"payment.status":        1,
		"payment.method":        1,
		"payment.transactionId": 1,
		"payment.isPayOff":      1,
		"askerId":               1,
		"date":                  1,
		"fromPartner":           1,
		"contactName":           1,
		"phone":                 1,
		"isoCode":               1,
		"costDetail.finalCost":  1,
		"subscriptionId":        1,
		"promotion.code":        1,
		"status":                1,
		"serviceText":           1,
		"originCurrency":        1,
		"currency":              1,
		"address":               1,
		"duration":              1,
		"promotion":             1,
		"serviceId":             1,
		"costDetail.cost":       1,
		"newCostDetail.cost":    1,
		"forceTasker":           1,
		"dateOptions":           1,
		"changesHistory.$":      1,
	}
	fields = globalLib.MergeQuerySocketTask(fields)

	query := bson.M{
		"status":             globalConstant.TASK_STATUS_POSTED,
		"forceTasker":        bson.M{"$exists": true},
		"changesHistory.key": bson.M{"$in": []string{globalConstant.CHAT_HISTORY_KEY_TASKER_REJECT, globalConstant.CHANGES_HISTORY_KEY_TASKER_SUGGESTED_NEW_DATE_OPTION}},
	}
	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		query,
		fields,
		&tasks,
	)
	return tasks, err
}
