package sendMessageFavTaskerNotResponding

import (
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func getTasks() ([]*modelTask.Task, error) {
	// Define the fields to be returned in the query result
	fields := bson.M{
		"_id":         1,
		"forceTasker": 1,
		"createdAt":   1,
		"askerId":     1,
		"date":        1,
		"dateOptions": 1,
		"taskPlace":   1,
		"duration":    1,
		"serviceText": 1,
	}

	timeAllowToSendMessage := globalLib.GetCurrentTime(local.TimeZone).Add(-45 * time.Minute)
	// Define the query to filter the tasks
	query := bson.M{
		"status":      globalConstant.TASK_STATUS_POSTED,
		"createdAt":   bson.M{"$lte": timeAllowToSendMessage},
		"forceTasker": bson.M{"$exists": true},
	}

	// Retrieve the tasks from the database based on the query and paging parameters
	var tasks []*modelTask.Task
	err := globalDataAccess.GetAllByQuery(
		globalCollection.COLLECTION_TASK[globalConstant.ISO_CODE_VN],
		query,
		fields,
		&tasks,
	)
	return tasks, err
}
