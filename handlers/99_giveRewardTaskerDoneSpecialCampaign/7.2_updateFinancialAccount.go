package giveRewardTaskerDoneSpecialCampaign

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelFATransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fatransaction"
	modelSpecialCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/specialCampaign"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func updateFinancialAccount(tasker *modelUser.Users, specialCampaign *modelSpecialCampaign.SpecialCampaign, amount float64, accountType string) error {
	if amount > 0 && (accountType == globalConstant.FA_TRANSACTION_ACCOUNT_TYPE_PROMOTION || accountType == globalConstant.FA_TRANSACTION_ACCOUNT_TYPE_MAIN) {
		updateData := bson.M{}
		if accountType == globalConstant.FA_TRANSACTION_ACCOUNT_TYPE_PROMOTION {
			updateData = bson.M{"$inc": bson.M{"Promotion": amount}}
		} else if accountType == globalConstant.FA_TRANSACTION_ACCOUNT_TYPE_MAIN {
			updateData = bson.M{"$inc": bson.M{"FMainAccount": amount}}
		}

		if len(updateData) == 0 {
			return nil
		}

		language := globalConstant.LANG_EN
		if tasker != nil && tasker.Language != "" {
			language = tasker.Language
		}

		text := localization.T(language, "NOTIFICATION_MESSAGE_WIN_SPECIAL_CAMPAIGN_TITLE")
		reason := localization.GetLocalizeObject("NOTIFICATION_MESSAGE_WIN_SPECIAL_CAMPAIGN_TITLE")

		// Tạo FATransaction
		dataFA := &modelFATransaction.FinancialAccountTransaction{
			XId:         globalLib.GenerateObjectId(),
			UserId:      tasker.XId,
			Type:        globalConstant.FA_TRANSACTION_TYPE_DEPOSIT,
			IsoCode:     local.ISO_CODE,
			AccountType: accountType,
			Amount:      amount,
			Source: &modelFATransaction.FATransactionSource{
				Name:              globalConstant.FA_TRANSACTION_SOURCE_NAME_SPECIAL_CAMPAIGN,
				Value:             specialCampaign.Name,
				SpecialCampaignId: specialCampaign.XId,
				Reason:            text,
				ReasonText:        reason,
			},
			Date:      globalLib.GetCurrentTimestamp(local.TimeZone),
			CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
		}

		// insert FAtransaction
		err := globalDataAccess.InsertOne(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], dataFA)
		if err != nil {
			msg := fmt.Sprintf("Lỗi tạo FATransaction tặng tiền cho tasker win special campaign: campaignId: %s - %v - err: %s", specialCampaign.XId, dataFA, err.Error())
			globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], "bTaskee System", msg)
			return err
		}

		// update financial account cho tasker
		_, err = globalDataAccess.UpdateOneById(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], tasker.FAccountId, updateData)
		if err != nil {
			// Remove FATransaction
			globalDataAccess.DeleteOneById(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], dataFA.XId)

			msg := fmt.Sprintf("Lỗi tặng tiền khuyến mãi cho tasker khi win campaign. taskerId: %s - campaignId: %s. Update financialAccount không thành công - FAccountId: %v - data: %v - err: %s", tasker.XId, specialCampaign.XId, tasker.FAccountId, updateData, err.Error())
			globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], "bTaskee System", msg)
			return err
		}

		// send notification
		sendNotification(tasker, "NOTIFICATION_MESSAGE_WIN_SPECIAL_CAMPAIGN_TITLE", "NOTIFICATION_MESSAGE_WIN_SPECIAL_CAMPAIGN_BODY", "Finance", 6)
	}
	return nil
}
