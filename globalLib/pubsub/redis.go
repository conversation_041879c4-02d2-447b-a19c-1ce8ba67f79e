package pubsub

import (
	"context"
	"log"

	"github.com/redis/go-redis/v9"
	"github.com/spf13/cast"
)

type RedisPubSub struct {
	*redis.Client
}

func NewRedisPubSub(configMap map[string]interface{}) (*RedisPubSub, error) {
	address := cast.ToString(configMap["address"])
	if address == "" {
		address = "localhost:6379"
	}
	password := cast.ToString(configMap["password"])
	username := cast.ToString(configMap["username"])
	return &RedisPubSub{
		Client: redis.NewClient(&redis.Options{
			Addr:     address,
			Username: username,
			Password: password,
			PoolSize: 50,
		}),
	}, nil
}

func (r *RedisPubSub) Subscribe(ctx context.Context, topic string, handler func([]byte) error) error {
	subscriber := r.Client.Subscribe(ctx, topic)
	go func() {
		defer subscriber.Close()
		for {
			msg, err := subscriber.ReceiveMessage(ctx)
			if err != nil {
				log.Println("ERROR: subscribe message:", err)
				continue
			}

			if err := handler([]byte(msg.Payload)); err != nil {
				log.Println("ERROR: handle message:", err)
				continue
			}
		}
	}()
	return nil
}

func (r *RedisPubSub) Publish(ctx context.Context, topic string, message []byte) error {
	return r.Client.Publish(ctx, topic, message).Err()
}
