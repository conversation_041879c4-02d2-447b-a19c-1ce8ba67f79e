package rollBackMissingTaskActionPenalty

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getUsers(userIds []string) map[string]*modelUsers.Users {
	users, err := modelUsers.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": userIds}}, bson.M{"_id": 1, "fAccountId": 1})
	if err != nil || len(users) == 0 {
		msg := fmt.Sprintf("[go-sync-cron-vn-v3] RollbackMissingTaskActionPenalty - getUsers: user.length: %d, error: %v", len(users), err)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
	}

	mapUsers := make(map[string]*modelUsers.Users)
	for _, user := range users {
		mapUsers[user.XId] = user
	}
	return mapUsers
}
