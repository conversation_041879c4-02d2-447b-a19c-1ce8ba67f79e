package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

func TestAutoReportTasksInfo(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		ResetData()
		apiURL := apiSyncCron + "/auto-report-tasks-info"

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker 0",
			},
			{
				"phone": "0834567880",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker 0",
			},
			{
				"phone": "0834567881",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Company 1",
			},
			{
				"phone": "0834567882",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker 2",
			},
			{
				"phone": "08345678803",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker 3",
			},
			{
				"phone": "0834567884",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Company 4",
			},
			{
				"phone": "0834567885",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker 5",
			},
		})

		date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1)
		taskIds_BinhDuong := CreateTask([]map[string]interface{}{
			{ // 0. Not start - Tasker
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        date,
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567880",
					},
				},
			},
			{ // 1. Not start - Company
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        date,
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567880",
						"companyId": "0834567881",
					},
				},
			},
			{ // 2. Not start - Leader
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        date,
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567880",
					},
					{
						"taskerId": "0834567882",
						"isLeader": true,
					},
				},
			},
			{ // 3. Start manual But done by sync cron - Tasker
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        date,
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
				},
				"startWorking": map[string]interface{}{
					"isStart": true,
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":  globalConstant.CHANGES_HISTORY_KEY_DONE_TASK,
						"from": globalConstant.CHANGES_HISTORY_FROM_SYNC_CRON,
					},
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567880",
					},
				},
			},
			{ // 4. Start manual But done by sync cron - Company
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        date,
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
				},
				"startWorking": map[string]interface{}{
					"isStart": true,
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":  globalConstant.CHANGES_HISTORY_KEY_DONE_TASK,
						"from": globalConstant.CHANGES_HISTORY_FROM_SYNC_CRON,
					},
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567880",
						"companyId": "0834567881",
					},
				},
			},
			{ // 5. Start manual But done by sync cron - Leader
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        date,
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
				},
				"startWorking": map[string]interface{}{
					"isStart": true,
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":  globalConstant.CHANGES_HISTORY_KEY_DONE_TASK,
						"from": globalConstant.CHANGES_HISTORY_FROM_SYNC_CRON,
					},
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567880",
						"isLeader": true,
					},
					{
						"taskerId": "0834567882",
					},
				},
			},
			{ // 6. Done manual
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        date,
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
				},
				"startWorking": map[string]interface{}{
					"isStart": true,
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":  globalConstant.CHANGES_HISTORY_KEY_DONE_TASK,
						"from": globalConstant.CHANGES_HISTORY_FROM_TASKER_APP,
					},
				},
			},
		})

		taskIds_HoChiMinh := CreateTask([]map[string]interface{}{
			{ // 0. Not start - Tasker
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        date,
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Thủ Dầu Một",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567883",
					},
				},
			},
			{ // 1. Not start - Company
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        date,
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Thủ Dầu Một",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567883",
						"companyId": "0834567884",
					},
				},
			},
			{ // 2. Not start - Leader
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        date,
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Thủ Dầu Một",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567883",
					},
					{
						"taskerId": "0834567885",
						"isLeader": true,
					},
				},
			},
			{ // 3. Start manual But done by sync cron - Tasker
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        date,
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Thủ Dầu Một",
				},
				"startWorking": map[string]interface{}{
					"isStart": true,
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":  globalConstant.CHANGES_HISTORY_KEY_DONE_TASK,
						"from": globalConstant.CHANGES_HISTORY_FROM_SYNC_CRON,
					},
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567883",
					},
				},
			},
			{ // 4. Start manual But done by sync cron - Company
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        date,
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Thủ Dầu Một",
				},
				"startWorking": map[string]interface{}{
					"isStart": true,
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":  globalConstant.CHANGES_HISTORY_KEY_DONE_TASK,
						"from": globalConstant.CHANGES_HISTORY_FROM_SYNC_CRON,
					},
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0834567883",
						"companyId": "0834567884",
					},
				},
			},
			{ // 5. Start manual But done by sync cron - Leader
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        date,
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Thủ Dầu Một",
				},
				"startWorking": map[string]interface{}{
					"isStart": true,
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":  globalConstant.CHANGES_HISTORY_KEY_DONE_TASK,
						"from": globalConstant.CHANGES_HISTORY_FROM_SYNC_CRON,
					},
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567883",
						"isLeader": true,
					},
					{
						"taskerId": "0834567885",
					},
				},
			},
			{ // 6. Done manual
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        date,
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Thủ Dầu Một",
				},
				"startWorking": map[string]interface{}{
					"isStart": true,
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":  globalConstant.CHANGES_HISTORY_KEY_DONE_TASK,
						"from": globalConstant.CHANGES_HISTORY_FROM_TASKER_APP,
					},
				},
			},
		})

		_ = taskIds_BinhDuong
		_ = taskIds_HoChiMinh

		body := map[string]interface{}{
			"action": lib.RUN_NOW,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)

					reportData := map[string]interface{}{}
					json.Unmarshal(res.Body.Bytes(), &reportData)

					expectedResult := map[string]interface{}{
						"Bình Dương": map[string]interface{}{
							"TASKER": map[string]interface{}{
								"TaskersNotManualStart": []interface{}{"0834567880", "0834567882"},
								"TasksNotManualStart":   []interface{}{taskIds_BinhDuong[0], taskIds_BinhDuong[2]},
								"TaskersNotManualDone":  []interface{}{"0834567880"},
								"TasksNotManualDone":    []interface{}{taskIds_BinhDuong[3], taskIds_BinhDuong[5]},
							},
							"COMPANY": map[string]interface{}{
								"TaskersNotManualStart": []interface{}{"0834567881"},
								"TasksNotManualStart":   []interface{}{taskIds_BinhDuong[1]},
								"TaskersNotManualDone":  []interface{}{"0834567881"},
								"TasksNotManualDone":    []interface{}{taskIds_BinhDuong[4]},
							},
						},
						"Hồ Chí Minh": map[string]interface{}{
							"TASKER": map[string]interface{}{
								"TaskersNotManualStart": []interface{}{"0834567883", "0834567885"},
								"TasksNotManualStart":   []interface{}{taskIds_HoChiMinh[0], taskIds_HoChiMinh[2]},
								"TaskersNotManualDone":  []interface{}{"0834567883"},
								"TasksNotManualDone":    []interface{}{taskIds_HoChiMinh[3], taskIds_HoChiMinh[5]},
							},
							"COMPANY": map[string]interface{}{
								"TaskersNotManualStart": []interface{}{"0834567884"},
								"TasksNotManualStart":   []interface{}{taskIds_HoChiMinh[1]},
								"TaskersNotManualDone":  []interface{}{"0834567884"},
								"TasksNotManualDone":    []interface{}{taskIds_HoChiMinh[4]},
							},
						},
					}

					So(reportData, ShouldResemble, expectedResult)
					b, _ := io.ReadAll(res.Body)
					fmt.Println(string(b))
				})
			})
		})
	})
}
