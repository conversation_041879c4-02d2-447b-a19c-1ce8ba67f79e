package grpcDoneTaskTH

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcDoneTaskTH"
	"google.golang.org/grpc"
)

func ConnectGRPCDoneTaskTH(endpoint string) (grpcDoneTaskTH.DoneBookingTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcDoneTaskTH.NewDoneBookingTHClient(connect)
	return client, connect, err
}
