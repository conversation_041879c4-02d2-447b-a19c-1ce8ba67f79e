package autoReportTasksInfo

import (
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func getTasks(startTime, endTime time.Time) ([]*modelTask.Task, error) {
	chunkTimeInDate := 6 * time.Hour
	tasks := []*modelTask.Task{}
	for stepTime := startTime; stepTime.Before(endTime); stepTime = stepTime.Add(chunkTimeInDate) {
		fromTime := stepTime
		toTime := stepTime.Add(chunkTimeInDate)
		if toTime.After(endTime) {
			toTime = endTime
		}
		chunkTasks, err := modelTask.GetAll(
			local.ISO_CODE,
			bson.M{
				"date": bson.M{
					"$gte": fromTime,
					"$lt":  toTime,
				},
				"status": globalConstant.TASK_STATUS_DONE,
			},
			bson.M{
				"_id":                      1,
				"startWorking.isStart":     1,
				"changesHistory.key":       1,
				"changesHistory.from":      1,
				"taskPlace.city":           1,
				"acceptedTasker.taskerId":  1,
				"acceptedTasker.companyId": 1,
				"acceptedTasker.isLeader":  1,
			},
		)
		if err != nil {
			return nil, err
		}
		if len(chunkTasks) == 0 {
			continue
		}
		tasks = append(tasks, chunkTasks...)
	}
	return tasks, nil
}
