import { check, Match } from 'meteor/check';
import { Meteor } from 'meteor/meteor';
import { DDP } from 'meteor/ddp';
import { HTTP } from 'meteor/http';
import { fetch, Headers } from 'meteor/fetch';
import moment from 'moment-timezone';
import _ from 'lodash';
import { callToPushNotificationGoService } from './push-methods.js';

const COMMISSION = 0.15;

export const CLEARNING_SERVICE = 'CLEANING';
export const HOSTEL_SERVICE = 'HOUSE_KEEPING';
export const COOKING_SERVICE = 'HOME_COOKING';

export const isCleaningService = (serviceName) => (serviceName === CLEARNING_SERVICE);
export const isHostelService = (serviceName) => (serviceName === HOSTEL_SERVICE);
export const isCookingService = (serviceName) => (serviceName === COOKING_SERVICE);

export const formatDate = function (d) {
  if (!d) return '';
  return {
    date: moment(d).format('DD/MM/YYYY HH:mm'),
    day: moment(d).format('DD/MM/YYYY'),
    time: moment(d).format('HH:mm'),
  };
};

export const changeTimeZone = function (date) {
  const TIMEZONE_OFFSET_VN = -420;
  const localTimeZoneOffset = new Date().getTimezoneOffset();
  return new Date(date.getTime() - (TIMEZONE_OFFSET_VN - localTimeZoneOffset) * 60000);
};

export const revertTimeZone = function (date) {
  const localTimeZoneOffset = new Date().getTimezoneOffset();
  return new Date(date.getTime() + (-420 - localTimeZoneOffset) * 60000);
};

export const remoteApp = function(func) {
  var remote = DDP.connect(Meteor.settings.API_SERVER);
  func(remote, Meteor.settings.Master_Key);
  remote.disconnect();
};

// export const sendNotification = function (from, to, message) {
//   check(from, String);
//   check(to, String);
//   check(message, Object);
//   Meteor.defer(function () {
//     remoteApp((remote, key) => {
//       remote.call('sendNotificationFromBackend', {from, to, message}, key);
//     });
//   });
// };

//TODO: convert http.post to fetch
/*mg.messages.create('sandbox-123.mailgun.org', {
    from: "Excited User <<EMAIL>>",
    to: ["<EMAIL>"],
    subject: "Hello",
    text: "Testing some Mailgun awesomness!",
    html: "<h1>Testing some Mailgun awesomness!</h1>"
  })
  .then(msg => console.log(msg)) // logs response data
  .catch(err => console.log(err)); // logs any error
  */

async function sendEmail (data) {
  const response = await fetch( Meteor.settings.EMAIL.MailGunAPIUrl, {
    method: 'POST', // *GET, POST, PUT, DELETE, etc.
    mode: 'cors', // no-cors, *cors, same-origin
    cache: 'no-cache', // *default, no-cache, reload, force-cache, only-if-cached
    credentials: 'same-origin', // include, *same-origin, omit
    headers: new Headers({
      Authorization: `Bearer ${Meteor.settings.EMAIL.MailGunAPIKey}`,
      'Content-Type': 'application/json; charset=utf-8',
      'Content-Length': data.length,
      Accept: 'application/json'
    }),
    redirect: 'follow', // manual, *follow, error
    referrerPolicy: 'no-referrer', // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
    body: JSON.stringify(data) // body data type must match "Content-Type" header
  }).then(response => {
    console.warn(response);
    return response;
  }).catch(err => {
    console.log("ERROR POSTING TO SLACK");
    return false;
  });
};
const sendEmailFunctionCB = Meteor.wrapAsync(sendEmail);
export const sendEmailFunction = function (option) {
  try {
    Meteor.defer(() => {
const formData = require('form-data');
const Mailgun = require('mailgun.js');
const mailgun = new Mailgun(formData);
const mg = mailgun.client({username: 'api', key: Meteor.settings.EMAIL.MailGunAPIKey});
      mg.messages.create('send.btaskee.com', option)
  .then(msg => console.log(msg)) // logs response data
  .catch(err => console.log(err)); // logs any error
      /*
      let apiURL = Meteor.settings.SLACK_POST.API_URL;
      apiURL += `?token=${Meteor.settings.SLACK_POST.TOKEN}`;
      apiURL += `&channel=${option.channel}`;
      apiURL += '&as_user=bTaskee System';
      apiURL += `&text=${encodeURIComponent(option.text)}`;
      Meteor.http.post(apiURL, {});
      */
    });
  } catch (ex) {
    console.log(ex);
  }

};


/*
  const apiURL = Meteor.settings.EMAIL.MailGunAPIUrl;
  console.log(option);
    const headers = {
      'content-type': 'text/plain; charset=uft-8',
    };
  const mailOptions = {
    auth: `api:${Meteor.settings.EMAIL.MailGunAPIKey}`,
    headers: headers,
    data: option
  };
  console.log(mailOptions);
  Meteor.defer(() => {
    HTTP.post(apiURL, { mailOptions }, function(error, result) {
    if (error) {
    console.log(error, 'while sending email to ' + option.to);
    }
  });
  });
};
*/
//
export const sendEmailSubscription = function(userId, subsId) {
  check(userId, String);
  check(subsId, String);
  const headers = {
    'content-type': 'application/json',
    accessKey: Meteor.settings.GO_SERVICE.API_KEY,
  };
  const url = `${Meteor.settings.GO_SERVICE.API_URL}/v2/email/renew-subscription`;

  Meteor.defer(() => {
    HTTP.post(url, { headers, data: { userId, subscriptionId: subsId } });
  });
};


export const sendNotificationByIds = function (userIds, data, option) {
  check(userIds, [String]);
  check(data, {
    title: { vi: String, en: String, ko: String, th: Match.Optional(String) },
    text: { vi: String, en: String, ko: String, th: Match.Optional(String) },
    payload: Object,
  });
  callToPushNotificationGoService(userIds, data.title, data.text, data.payload, option);

  if (option && option.isForceView) {
    var collectionNotification = Meteor.appCollection.VN_Notification;
    if (option.isoCode === 'TH') {
      collectionNotification = Meteor.appCollection.TH_Notification;
    }
    // Insert notification VI
    const viUserIds = Meteor.appCollection.users.find({
      _id: { $in: userIds },
      $or: [{ language: 'vi' }, { language: null }]
    }).map(user => (user._id));
    viUserIds.forEach((userId) => {
      // collectionNotification.remove({
      //   userId,
      //   type: option.type || 25,
      //   description: data.text.vi,
      // });
      collectionNotification.insert({
        userId,
        taskId: _.get(data, 'payload.taskId', null),
        type: option.type || 25,
        url: option.url,
        title: data.title.vi,
        description: data.text.vi,
        createdAt: new Date(),
        navigateTo: _.get(data, 'payload.navigateTo', null),
      });
    });
    // Insert notification TH
    const thUserIds = Meteor.appCollection.users.find({
      _id: { $in: userIds },
      $or: [{ language: 'th' }, { language: null }]
    }).map(user => (user._id));
    thUserIds.forEach((userId) => {
      // collectionNotification.remove({
      //   userId,
      //   type: option.type || 25,
      //   description: data.text.vi,
      // });
      collectionNotification.insert({
        userId,
        taskId: _.get(data, 'payload.taskId', null),
        type: option.type || 25,
        url: option.url,
        title: data.title.th,
        description: data.text.th,
        createdAt: new Date(),
        navigateTo: _.get(data, 'payload.navigateTo', null),
      });
    });

    // Insert notification EN
    const enUserIds = Meteor.appCollection.users.find({
      _id: { $in: userIds },
      language: 'en',
    }).map(user => (user._id));
    enUserIds.forEach((userId) => {
      // collectionNotification.remove({
      //   userId,
      //   type: option.type || 25,
      //   description: data.text.en,
      // });
      collectionNotification.insert({
        userId,
        taskId: _.get(data, 'payload.taskId', null),
        type: option.type || 25,
        url: option.url,
        title: data.title.en,
        description: data.text.en,
        createdAt: new Date(),
        navigateTo: _.get(data, 'payload.navigateTo', null),
      });
    });

    // Insert notification KO
    const koUserIds = Meteor.appCollection.users.find({
      _id: { $in: userIds },
      language: 'ko'
    }).map(user => (user._id));
    koUserIds.forEach((userId) => {
      // collectionNotification.remove({
      //   userId,
      //   type: option.type || 25,
      //   description: data.text.ko,
      // });
      collectionNotification.insert({
        userId,
        taskId: _.get(data, 'payload.taskId', null),
        type: option.type || 25,
        url: option.url,
        title: data.title.ko,
        description: data.text.ko,
        createdAt: new Date(),
        navigateTo: _.get(data, 'payload.navigateTo', null),
      });
    });
  }
};

// export const sendNotificationByToken = function (data) {
//   check(data, {
//     title: String,
//     text: String,
//     tokens: Array,
//     payload: Object,
//   });
//   if (data.tokens.length > 0) {
//     remoteApp((remote, key) => {
//       remote.call('sendNotificationByTokens', data, key);
//     });
//   }
// };

// export const sendNotificationOfNewTask = function (taskId) {
//   check(taskId, String);
//   Meteor.defer(function () {
//     remoteApp((remote, key) => {
//       remote.call('sendNotificationOfNewTaskFromBackend', taskId, key);
//     });
//   });
// };

// Function send SMS via ESMS provider
export const sendSMSViaESMS = function (phone, message) {
  // NOTE: Could put this as constant variables but I want to keep this in the same place so
  // when we change to another carrier we only need to change in this function only
  const url = Meteor.settings.SMS.ESMSUrl;
  const query = 'Phone=' + phone +
    '&Content=' +
    message +
    '&ApiKey=' + Meteor.settings.SMS.ESMSAPIKey +
    '&SecretKey=' + Meteor.settings.SMS.ESMSSecretKey +
    '&SmSType=' + Meteor.settings.SMS.ESMSSmSType;
  // Call SMS server and send SMS.
  var error = null;
  try {
    var response = HTTP.get(url, {timeout: 10000, query: query});
    var result = JSON.parse(response.content);
    switch (result.CodeResult) {
      case '100':
        return result.SMSID; // Success, return the id of the sms
      case '99':
        throw new Meteor.Error('SMS-InvalidNumber', 'Phone number not valid!');
      default:
        throw new Meteor.Error('SMS-ErrorSending', 'Error sending sms');
    }
  } catch (e) {
    if (e.error === 'SMS-InvalidNumber') {
      error = 'SMS-InvalidNumber';
    } else if (e.error === 'SMS-ErrorSending') {
      error = 'SMS-ErrorSending';
    } else {
      error = 'SMS-ServerUnreachable';
    }
  }
  // If successfully: return null
  // If run with error: return the 'SMS-ServerUnreachable'
  return error;
};
export const sendSMS = function (data) {
  check(data, {
    phones: [String],
    message: String,
  });
  return data.phones.map((phone) => {
    const result = sendSMSViaESMS(phone, data.message);
    return {
      phone,
      result,
    };
  });
};

// export const updateOtherTaskCost = function (taskId) {
//   check(taskId, String);
//   remoteApp((remote, key) => {
//     remote.call('backendUpdateOtherTaskCost', taskId, key);
//   });
// };

export const checkConflictTask = function (task, otherTask, timeInBetweenTask) {
  const taskStart_1 = new Date(task.date.getTime() - (timeInBetweenTask * 60 * 1000));
  const taskEnd_1 = new Date(task.date.getTime() + (task.duration * 60 + timeInBetweenTask) * 60 * 1000);
  const taskStart_2 = new Date(otherTask.date);
  const taskEnd_2 = new Date(taskStart_2.getTime() + otherTask.duration * 60 * 60 * 1000);

  if ((taskStart_1 >= taskStart_2 && taskStart_1 < taskEnd_2)
    || (taskStart_2 >= taskStart_1 && taskStart_2 < taskEnd_1)) {
    return true;
  }
  return false;
};

export const insertFATransaction = function (data) {
  check(data, Object);
  check(data.userId, String);
  check(data.main, Number);
  check(data.promotion, Number);
  check(data.type, String);
  if (data.main !== 0) {
    FATransaction.insert({
      userId: data.userId,
      accountType: 'M',
      type: data.type,
      source: data.source,
      amount: data.main > 0 ? data.main : -data.main,
      date: new Date(),
    });
  }
  if (data.promotion !== 0) {
    FATransaction.insert({
      userId: data.userId,
      accountType: 'P',
      type: data.type,
      source: data.source,
      amount: data.promotion > 0 ? data.promotion : -data.promotion,
      date: new Date(),
    });
  }
};

export const calculateTaskEndTime = function (taskDate, duration) {
  const endTime = new Date(taskDate);
  endTime.setHours(endTime.getHours() + duration);
  return endTime;
};

export const checkTaskerFinancialAccount = function(cost, tasker) {
  // Calculate cost of all task which tasker accept, including this task
  var allAcceptedTaskCost =
    Task.find({
      'acceptedTasker.taskerId': tasker._id,
      status: {$in: [ 'CONFIRMED', 'WAITING_ASKER_CONFIRMATION' ]}})
    .map(function (res) { return res.cost; })
    .reduce((function (a, b) {return (a + b);}), cost);
  // Calculate cost of all task which COMPANY accept, NOT including this task
  if (tasker.employeeIds && tasker.employeeIds.length > 0) {
    allAcceptedTaskCost += Task.find({
      'acceptedTasker.companyId': tasker._id,
      'acceptedTasker.taskerId': {$ne: tasker._id},
      status: {$in: [ 'CONFIRMED', 'WAITING_ASKER_CONFIRMATION' ]}})
      .map(function (res) { return res.cost; })
      .reduce((function (a, b) {return (a + b);}), 0);
  }
  // Calculate Tasker's money
  const FA = FinancialAccount.findOne({ _id: tasker.fAccountId });
  const taskerMoney = FA.FMainAccount + FA.Promotion;
  if (taskerMoney >= allAcceptedTaskCost * COMMISSION) {
    // Tasker's money is available for new task.
    return true;
  }
  // Tasker not enough money
  // Calculate money not enough
  const notEnough = allAcceptedTaskCost * COMMISSION - taskerMoney;
  return {
    enough: false,
    money: notEnough,
  };

  // Asker canceled task when Tasker is viewing task detail
  return false;
};

export const checkTaskerCanAcceptTask = function (task, taskerId) {
  if (task.subServiceId && task.subServiceId.length > 0) {
    // When task have subservice
    // Find service channel base on task.subServiceId and taskerId
    const existSubServiceChannel = ServiceChannel.find({ serviceId: { $in: task.subServiceId }, taskerList: taskerId }).fetch();
    if (existSubServiceChannel && existSubServiceChannel.length === task.subServiceId.length) {
      // Compare the length of result and task.subServiceId
      return true;
    }
  } else {
    // When task without subservice
    const existServiceChannel = ServiceChannel.findOne({ serviceId: task.serviceId, taskerList: taskerId });
    if (existServiceChannel) {
      // Only need result exist
      return true;
    }
  }
  // Tasker can not accept this task.
  return false;
};

export const roundMoney = function (money) {
  return Math.round(money / 1000) * 1000;
};

// Post data using fetch
async function postDataToSlack (data) {
  const response = await fetch( Meteor.settings.SLACK_POST.API_URL, {
    method: 'POST', // *GET, POST, PUT, DELETE, etc.
    mode: 'cors', // no-cors, *cors, same-origin
    cache: 'no-cache', // *default, no-cache, reload, force-cache, only-if-cached
    credentials: 'same-origin', // include, *same-origin, omit
    headers: new Headers({
      Authorization: `Bearer ${Meteor.settings.SLACK_POST.TOKEN}`,
      'Content-Type': 'application/json; charset=utf-8',
      'Content-Length': data.length,
      Accept: 'application/json'
    }),
    redirect: 'follow', // manual, *follow, error
    referrerPolicy: 'no-referrer', // no-referrer, *no-referrer-when-downgrade, origin, origin-when-cross-origin, same-origin, strict-origin, strict-origin-when-cross-origin, unsafe-url
    body: JSON.stringify(data) // body data type must match "Content-Type" header
  }).then(response => {
    return response;
  }).catch(err => {
    console.log("ERROR POSTING TO SLACK");
    return false;
  });
}
const postDataToSlackCall = Meteor.wrapAsync(postDataToSlack);

export const postToSlack = function (option) {
  try {
    Meteor.defer(() => {

      let data = {...option, username:'bTaskee System'};
      const results = postDataToSlackCall(data);
      /*
      let apiURL = Meteor.settings.SLACK_POST.API_URL;
      apiURL += `?token=${Meteor.settings.SLACK_POST.TOKEN}`;
      apiURL += `&channel=${option.channel}`;
      apiURL += '&as_user=bTaskee System';
      apiURL += `&text=${encodeURIComponent(option.text)}`;
      Meteor.http.post(apiURL, {});
      */
    });
  } catch (ex) {
    console.log(ex);
  }

};

export const trackingScheduleStatus = function (data) {
  try {
    const exist = Meteor.appCollection.ServiceStatus.findOne({ name: data.name });
    if (exist) {
      Meteor.appCollection.ServiceStatus.update({ name: data.name }, { $set: { lastOnline: new Date(), nextRunAt: data.nextRunAt, message: data.message && String(data.message), updatedAt: new Date() } });
    } else {
      data.lastOnline = new Date();
      data.createdAt = new Date();
      data.message = data.message && String(data.message);
      Meteor.appCollection.ServiceStatus.insert(data);
    }
    // Send to slack if synced cron has error
    if ( data.message ) {
      postToSlack({
        channel: 'cron-jobs-alert',
        text: `There was a problem with the SCHEDULE ${data.name}. Run at: ${data.createdAt}. Error: ${data.message}.`,
      });
    }
  } catch (e) {}
};

export const sendNotificationInApp = (to, message, type = 31) => {
  Meteor.appCollection.Notification.insert({
    userId: to,
    taskId: null,
    type,
    description: message,
    createdAt: new Date(),
  });
};

export const fillTaskDescription = (phone, isoCode) => {
  if ( phone && isoCode ) {
    var collectionTask = Meteor.appCollection.VN_Task;
    if (isoCode === 'TH') {
      collectionTask = Meteor.appCollection.TH_Task;
    }
    const task = collectionTask.findOne({ phone: phone, description: {$nin: [null, '']} }, { fields: { description: 1 }, sort: { createdAt: -1 } });
    if ( task ) {
      return task.description;
    }
  }
  return null;
};

export const getTimestamp = (date) => {
  let defaultDate = new Date();
  if (date) {
    defaultDate = new Date(date);
  }
  const seconds = Math.floor(defaultDate.getTime() / 1000);
  return { seconds, nanos: (defaultDate.getTime() - seconds * 1000) * 1000000 };
};

export const sendTaskToTasker = (taskId, serviceId, favouriteTasker, isoCode) => {
  Meteor.defer(() => {
    var collectionTask = Meteor.appCollection.VN_Task;
    if (isoCode === 'TH') {
      collectionTask = Meteor.appCollection.TH_Task;
    }
    const headers = {
      'content-type': 'application/json',
      'accessKey': Meteor.settings.GO_SERVICE.API_KEY,
    };
    var url = Meteor.settings.GO_SERVICE.API_URL + '/v2/push-notification-new-task/new-task';
    HTTP.post(url, { headers,
      data: {
        service: {
          _id: serviceId,
        },
        booking: {
          _id: taskId,
          isoCode: isoCode,
        },
        favouriteTasker: favouriteTasker || [],
      } });
  });
};

export const sendNotificationToAsker = function (taskId, askerId, askerLanguage, serviceText, isoCode) {
  check(taskId, String);
  check(askerId, String);
  check(serviceText, Object);
  check(isoCode, String);

  var defaultLanguage = "vi";
  if (isoCode === "TH") {
    defaultLanguage = "th";
  }
  const language = askerLanguage || defaultLanguage;
  const message = {
    title: {
      vi: i18n.getTranslation('NOTIFICATION_ASKER_SCHEDULE_TASK', { _locale: 'vi' }),
      th: i18n.getTranslation('NOTIFICATION_ASKER_SCHEDULE_TASK', { _locale: 'th' }),
      ko: i18n.getTranslation('NOTIFICATION_ASKER_SCHEDULE_TASK', { _locale: 'ko' }),
      en: i18n.getTranslation('NOTIFICATION_ASKER_SCHEDULE_TASK', { _locale: 'en' }),
    },
    text: {
      vi: `${serviceText[language]} - ${i18n.getTranslation('NOTIFICATION_ASKER_SCHEDULE_TASK', { _locale: 'vi' })}`,
      th: `${serviceText[language]} - ${i18n.getTranslation('NOTIFICATION_ASKER_SCHEDULE_TASK', { _locale: 'th' })}`,
      ko: `${serviceText[language]} - ${i18n.getTranslation('NOTIFICATION_ASKER_SCHEDULE_TASK', { _locale: 'ko' })}`,
      en: `${serviceText[language]} - ${i18n.getTranslation('NOTIFICATION_ASKER_SCHEDULE_TASK', { _locale: 'en' })}`,
    },
    payload: {
      type: 16,
      taskId,
    },
  };
  sendNotificationByIds([askerId], message, {});
};