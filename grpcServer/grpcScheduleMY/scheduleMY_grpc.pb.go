// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcScheduleMY

import (
	context "context"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	taskSchedule "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskSchedule"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// ScheduleClient is the client API for Schedule service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ScheduleClient interface {
	AddHomeCleaningSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error)
	AddHomeCookingSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error)
	AddHousekeepingSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error)
	AddOfficeCleaningSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error)
}

type scheduleClient struct {
	cc grpc.ClientConnInterface
}

func NewScheduleClient(cc grpc.ClientConnInterface) ScheduleClient {
	return &scheduleClient{cc}
}

func (c *scheduleClient) AddHomeCleaningSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcScheduleVN.Schedule/AddHomeCleaningSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleClient) AddHomeCookingSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcScheduleVN.Schedule/AddHomeCookingSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleClient) AddHousekeepingSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcScheduleVN.Schedule/AddHousekeepingSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleClient) AddOfficeCleaningSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcScheduleVN.Schedule/AddOfficeCleaningSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ScheduleServer is the server API for Schedule service.
// All implementations must embed UnimplementedScheduleServer
// for forward compatibility
type ScheduleServer interface {
	AddHomeCleaningSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error)
	AddHomeCookingSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error)
	AddHousekeepingSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error)
	AddOfficeCleaningSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error)
	mustEmbedUnimplementedScheduleServer()
}

// UnimplementedScheduleServer must be embedded to have forward compatible implementations.
type UnimplementedScheduleServer struct {
}

func (UnimplementedScheduleServer) AddHomeCleaningSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddHomeCleaningSchedule not implemented")
}
func (UnimplementedScheduleServer) AddHomeCookingSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddHomeCookingSchedule not implemented")
}
func (UnimplementedScheduleServer) AddHousekeepingSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddHousekeepingSchedule not implemented")
}
func (UnimplementedScheduleServer) AddOfficeCleaningSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddOfficeCleaningSchedule not implemented")
}
func (UnimplementedScheduleServer) mustEmbedUnimplementedScheduleServer() {}

// UnsafeScheduleServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ScheduleServer will
// result in compilation errors.
type UnsafeScheduleServer interface {
	mustEmbedUnimplementedScheduleServer()
}

func RegisterScheduleServer(s *grpc.Server, srv ScheduleServer) {
	s.RegisterService(&_Schedule_serviceDesc, srv)
}

func _Schedule_AddHomeCleaningSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskSchedule.TaskSchedule)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleServer).AddHomeCleaningSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcScheduleVN.Schedule/AddHomeCleaningSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleServer).AddHomeCleaningSchedule(ctx, req.(*taskSchedule.TaskSchedule))
	}
	return interceptor(ctx, in, info, handler)
}

func _Schedule_AddHomeCookingSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskSchedule.TaskSchedule)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleServer).AddHomeCookingSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcScheduleVN.Schedule/AddHomeCookingSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleServer).AddHomeCookingSchedule(ctx, req.(*taskSchedule.TaskSchedule))
	}
	return interceptor(ctx, in, info, handler)
}

func _Schedule_AddHousekeepingSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskSchedule.TaskSchedule)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleServer).AddHousekeepingSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcScheduleVN.Schedule/AddHousekeepingSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleServer).AddHousekeepingSchedule(ctx, req.(*taskSchedule.TaskSchedule))
	}
	return interceptor(ctx, in, info, handler)
}

func _Schedule_AddOfficeCleaningSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskSchedule.TaskSchedule)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleServer).AddOfficeCleaningSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcScheduleVN.Schedule/AddOfficeCleaningSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleServer).AddOfficeCleaningSchedule(ctx, req.(*taskSchedule.TaskSchedule))
	}
	return interceptor(ctx, in, info, handler)
}

var _Schedule_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcScheduleVN.Schedule",
	HandlerType: (*ScheduleServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddHomeCleaningSchedule",
			Handler:    _Schedule_AddHomeCleaningSchedule_Handler,
		},
		{
			MethodName: "AddHomeCookingSchedule",
			Handler:    _Schedule_AddHomeCookingSchedule_Handler,
		},
		{
			MethodName: "AddHousekeepingSchedule",
			Handler:    _Schedule_AddHousekeepingSchedule_Handler,
		},
		{
			MethodName: "AddOfficeCleaningSchedule",
			Handler:    _Schedule_AddOfficeCleaningSchedule_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "scheduleMY.proto",
}
