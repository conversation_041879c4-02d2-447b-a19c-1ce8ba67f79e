package testing

import (
	"context"
	"fmt"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/local"
	svc "gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalRepo"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

/*
* Mô tả:
	- Nếu task.SerivceText.En nằm trong settings.ServicesApplyDistance -> SendNornal -> End
	- Nếu task.TaskPlace.City nằm trong settings.CitiesApplyDistance -> SendNornal -> End
	- Gửi notification tới tasker:
		+ Active tasker
	  + In service channel
	  + Have workingPlaces in district.
	  + Sort by score and get top x (from settings)
	- Nếu list TopTasker < settings.TopTaskersPriorityScore -> Send tới tất cả tasker trong quận -> End
	- Send tới cả asker.FavouriteAsker
* Kết quả:
	- Cập nhật task.Visibility = 2, viewedTaskers
*/

func TestFavAndTopTasker(t *testing.T) {

	t.Run("4", func(t *testing.T) {
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		date := now.AddDate(0, 0, 1)
		last7Days := now.AddDate(0, 0, -7)

		CreateUser([]map[string]interface{}{
			{
				"phone": "0981234567",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker01",
			},
			{
				"phone": "0981234500",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234501",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234502",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker02",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234503",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker03",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234504",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234505",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234506",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker06",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234507",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker07",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        2,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234508",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234509",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234510",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234511",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{ // Tasker MALE
				"phone": "0981234512",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker07",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":         2,
				"lastDoneTask":  last7Days,
				"firstVerifyAt": now.AddDate(0, 0, -20),
				"gender":        "MALE",
			},
			{ // Tasker MALE
				"phone": "0981234513",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker07",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":         2,
				"firstVerifyAt": last7Days,
				"gender":        "MALE",
			},
			{ // Tasker MALE. last done task is before 2 weeks ago
				"phone": "0981234514",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker07",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":         2,
				"firstVerifyAt": now.AddDate(0, 0, -20),
				"lastDoneTask":  now.AddDate(0, 0, -20),
				"gender":        "MALE",
			},
			{
				"phone": "0981234516",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker16",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  "VN",
					},
				},
				"score":         2,
				"firstVerifyAt": last7Days,
				"gender":        "MALE",
			},
		})

		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0981234567",
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quan 1",
				},
				"date": fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
			},
			{ // Tasker busy
				"askerPhone":  "0981234567",
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quan 1",
				},
				"date": fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0981234502",
					},
				},
			},
		})
		CreateTaskMetadata([]map[string]interface{}{
			{
				"_id":             taskIds[0],
				"excludedTaskers": []string{"0981234516"},
			},
		})
		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"$set": bson.M{"timeInBetweenTask": 30}})

		var sv *modelService.Service
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING}, bson.M{"_id": 1}, &sv)
		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE],
			bson.M{"serviceId": sv.XId},
			bson.M{"$set": bson.M{
				"taskerList": []string{
					"0981234500",
					"0981234501",
					"0981234502",
					"0981234503",
					"0981234504",
					"0981234505",
					"0981234506",
					"0981234507",
					"0981234508",
					"0981234509",
					"0981234510",
					"0981234511",
					"0981234512",
					"0981234513",
					"0981234514",
					"0981234516",
				},
			}},
		)

		req := &pushNotificationNewTask.NewTaskRequest{
			Service: &pushNotificationNewTask.NewTaskRequestService{
				XId: sv.XId,
			},
			Booking: &pushNotificationNewTask.NewTaskRequestBooking{
				XId:     taskIds[0],
				IsoCode: local.ISO_CODE,
			},
		}

		Convey("When the service handle the request", t, func() {
			s := svc.GRPCServer{}
			_, err := s.FavAndTopTasker(context.Background(), req)

			Convey("Check the database", func() {
				So(err, ShouldBeNil)
				var task *modelTask.Task
				globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskIds[0], bson.M{}, &task)
				So(len(task.ViewedTaskers), ShouldEqual, 12)
				for _, v := range task.ViewedTaskers {
					So(v, ShouldNotBeIn, []string{"0981234502", "0981234507"})
				}
				So(task.ViewedTaskers, ShouldContain, "0981234512")
				So(task.ViewedTaskers, ShouldContain, "0981234513")
				So(task.Visibility, ShouldEqual, 2)
				So(task.ChangesHistory[0].From, ShouldEqual, "SYSTEM")
				So(task.ChangesHistory[0].Key, ShouldEqual, "RESEND_FAV_TOPTASKER")
				So(task.ChangesHistory[0].CreatedAt, ShouldNotBeNil)
			})
		})
	})
}
