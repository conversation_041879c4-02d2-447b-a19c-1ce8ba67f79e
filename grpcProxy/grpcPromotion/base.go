package grpcPromotion

import (
	"context"
	"errors"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPromotionIndo"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPromotionMY"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPromotionTH"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPromotionVN"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotionResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/signUpPromotionRequest"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"google.golang.org/grpc"
)

type PromotionClient interface {
	CheckPostTaskPromotion(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error)
	CheckSignUpPromotion(ctx context.Context, in *signUpPromotionRequest.SignUpPromotionRequest, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error)
}

func Connect(isoCode, endpoint string) (PromotionClient, *grpc.ClientConn, error) {
	switch isoCode {
	case globalConstant.ISO_CODE_VN:
		return ConnectGRPCPromotionVN(endpoint)
	case globalConstant.ISO_CODE_TH:
		return ConnectGRPCPromotionTH(endpoint)
	case globalConstant.ISO_CODE_INDO:
		return ConnectGRPCPromotionINDO(endpoint)
	case globalConstant.ISO_CODE_MY:
		return ConnectGRPCPromotionMY(endpoint)
	}
	return nil, nil, errors.New("iso code incorrect")
}

func ConnectGRPCPromotionVN(endpoint string) (grpcPromotionVN.PromotionVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPromotionVN.NewPromotionVNClient(connect)
	return client, connect, err
}

func ConnectGRPCPromotionTH(endpoint string) (grpcPromotionTH.PromotionTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPromotionTH.NewPromotionTHClient(connect)
	return client, connect, err
}

func ConnectGRPCPromotionINDO(endpoint string) (grpcPromotionIndo.PromotionIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPromotionIndo.NewPromotionIndoClient(connect)
	return client, connect, err
}

func ConnectGRPCPromotionMY(endpoint string) (grpcPromotionIndo.PromotionIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPromotionMY.NewPromotionClient(connect)
	return client, connect, err
}
