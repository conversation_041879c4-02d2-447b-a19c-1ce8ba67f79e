package indo

import (
	"errors"
	"fmt"
	"time"

	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/indo"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalModel/modelTransactionInfo"
	modelRefundRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/refundRequest"
)

func RefundPrepayTaskIndo(transactionInfo *modelTransactionInfo.TransactionInfo, cancelFee float64, reason string, timeZone *time.Location) (refundRequestId string, refundAmount float64, isBPayRefund bool, err error) {
	if !isTransactionSuccess(transactionInfo) {
		err = errors.New("transaction not paid")
		return
	}
	// Check total refund amount -> not to refund if total refund amount is greater than total amount
	totalRefundedAmount := 0.0
	if transactionInfo.RefundInfo != nil {
		for _, refund := range transactionInfo.RefundInfo {
			if refund.Status != globalConstant.REFUND_REQUEST_STATUS_CANCEL {
				totalRefundedAmount += refund.RefundAmount
			}
		}
		if totalRefundedAmount >= transactionInfo.Amount {
			err = errors.New("transaction already refunded")
			return
		}
	}

	// 2. calculate refund amount
	refundAmount = transactionInfo.Amount - totalRefundedAmount - cancelFee
	if refundAmount <= 0 {
		return
	}

	// 4. refund wallet
	err = refundWallet_Indo(transactionInfo, refundAmount, cancelFee, reason, timeZone)
	return
}

func refundWallet_Indo(transactionInfo *modelTransactionInfo.TransactionInfo, refundAmount, cancelFee float64, reason string, timeZone *time.Location) (err error) {
	if refundAmount <= 0 {
		return
	}
	refundRequest := &modelRefundRequest.RefundRequest{
		XId:            globalLib.GenerateObjectId(),
		Type:           globalConstant.REFUND_REQUEST_TYPE_PREPAY_TASK,
		Amount:         refundAmount,
		PaymentMethod:  transactionInfo.PaymentMethod,
		Currency:       globalConstant.CURRENCY_SIGN_ID.Code,
		Status:         globalConstant.REFUND_REQUEST_STATUS_NEW,
		UserId:         transactionInfo.UserId,
		TaskId:         transactionInfo.TaskId,
		CancelTaskFee:  cancelFee,
		InvoiceNo:      transactionInfo.InvoiceNo,
		Reason:         reason,
		Bank:           transactionInfo.Bank,
		CreatedAt:      globalLib.GetCurrentTimestamp(timeZone),
		PaymentGateway: transactionInfo.PaymentGateway,
	}
	err = globalDataAccess.InsertOne(globalCollection.COLLECTION_REFUND_REQUEST[globalConstant.ISO_CODE_INDO], refundRequest)
	if err != nil {
		return fmt.Errorf("cannot create refund request for task, err: %v", err)
	}
	return
}
