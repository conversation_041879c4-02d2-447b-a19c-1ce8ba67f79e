package businessAllocation

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPaymentVN"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	modelBusinessLevel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	"go.mongodb.org/mongo-driver/bson"
)

func topUpbPayToMembers(business *modelBusiness.Business) {
	// check enough bPay
	levelInfos := getLevelInfo(business.XId)
	if len(levelInfos) == 0 {
		return
	}
	levelIds := []string{}
	for _, levelInfo := range levelInfos {
		levelIds = append(levelIds, levelInfo.XId)
	}
	membersByLevel := getMembersByLevel(levelIds)
	if len(membersByLevel) == 0 {
		return
	}
	if !isEnoughbPay(business, levelInfos, membersByLevel) {
		// Post slack
		msg := fmt.Sprintf("Tài khoản doanh nghiệp không đủ tiền để thao tác nạp tiền cho members - businessId: %s", business.XId)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.BUSINESS_PARTNERSHIP_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
		modelBusiness.UpdateOneById(local.ISO_CODE, business.XId, bson.M{"$set": bson.M{"topUpSetting.status": globalConstant.BUSINESS_STATUS_INACTIVE}})
		return
	}
	// update next time
	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	period := int(business.GetTopUpSetting().GetPeriod())
	_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], business.XId, bson.M{"$set": bson.M{"topUpSetting.nextTime": getNextTimeForUpdate(currentTime, business.GetTopUpSetting().GetDayInMonth(), period)}})
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, "Update topup setting error: "+err.Error())
		return
	}

	keyCache := fmt.Sprintf("topupMembersLevel-%s", business.XId)
	if cfg.RedisCache != nil {
		var ctx = context.Background()
		if isProcess, err := cfg.RedisCache.Get(ctx, keyCache).Bool(); err == nil && isProcess {
			return
		}
		cfg.RedisCache.Set(ctx, keyCache, true, 1*time.Hour)
	}
	processTopUpMembers(business.XId, levelInfos, membersByLevel, keyCache)
}

func topUpMemberGRPC(requestData *modelBusinessMember.MemberRequestPayment) (result *response.Response, err error) {
	client, connect, err := grpcPaymentVN.ConnectGRPCPaymentVN(cfg.GrpcPaymentPort)
	if err != nil {
		return nil, err
	}
	defer connect.Close()
	response, err := client.BusinessTopupMember(context.Background(), requestData)
	if err != nil {
		return nil, err
	}
	return response, nil
}

func isEnoughbPay(business *modelBusiness.Business, levelInfos []*modelBusinessLevel.BusinessLevel, mapMembersByLevelId map[string][]*modelBusinessMember.BusinessMember) bool {
	// Check business bpay
	var totalAmount float64 = 0
	for _, level := range levelInfos {
		totalAmount += (level.Amount * float64(len(mapMembersByLevelId[level.XId])))
	}
	return business.BPay >= totalAmount
}

func processTopUpMembers(businessId string, levelsInfo []*modelBusinessLevel.BusinessLevel, mapMembersByLevelId map[string][]*modelBusinessMember.BusinessMember, keyCache string) {
	defer func() {
		if cfg.RedisCache != nil {
			cfg.RedisCache.Del(context.Background(), keyCache)
		}
	}()
	listMemberFail := []string{}
	for _, levelInfo := range levelsInfo {
		members := mapMembersByLevelId[levelInfo.XId]
		for _, v := range members {
			_, err := topUpMemberGRPC(&modelBusinessMember.MemberRequestPayment{
				BusinessId: businessId,
				MemberId:   v.XId,
				Amount:     levelInfo.Amount,
			})
			if err != nil {
				listMemberFail = append(listMemberFail, fmt.Sprintf("memberId: %s - amount: %f - error: %v", v.XId, levelInfo.Amount, err))
			}
		}
	}
	if len(listMemberFail) > 0 {
		// Post slack
		msg := fmt.Sprintf("Lỗi nạp tiền bPay cho members business: %s - listMemberFail:\n%v", businessId, strings.Join(listMemberFail, "\n"))
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.BUSINESS_PARTNERSHIP_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
	} else {
		sendNotificationSuccess(businessId, "BUSINESS_TOPUP_SCHEDULE_SUCCESS_TITLE", "BUSINESS_TOPUP_SCHEDULE_SUCCESS_BODY", globalConstant.PAYLOAD_NAVIGATE_TO_TRANSACTION_BUSINESS)
	}
}
