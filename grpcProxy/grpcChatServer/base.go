package grpcChatServer

import (
	"context"
	"errors"

	"github.com/golang/protobuf/ptypes/empty"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcChatServerIndo"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcChatServerMY"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcChatServerTH"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcChatServerVN"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage"
	"google.golang.org/grpc"
)

type ChatServerClient interface {
	SendSocketChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error)
}

func Connect(isoCode, endpoint string) (ChatServerClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	switch isoCode {
	case globalConstant.ISO_CODE_VN:
		return grpcChatServerVN.NewChatServerVNClient(connect), connect, nil
	case globalConstant.ISO_CODE_TH:
		return grpcChatServerTH.NewChatServerTHClient(connect), connect, nil
	case globalConstant.ISO_CODE_INDO:
		return grpcChatServerIndo.NewChatServerIndoClient(connect), connect, nil
	case globalConstant.ISO_CODE_MY:
		return grpcChatServerMY.NewChatServerClient(connect), connect, nil
	}
	return nil, nil, errors.New("invalid isoCode")
}
