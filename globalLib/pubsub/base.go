package pubsub

import (
	"context"
	"errors"
)

type PubSub interface {
	Subscribe(ctx context.Context, topic string, handler func([]byte) error) error
	Publish(ctx context.Context, topic string, message []byte) error
}

func NewPubSub(pubSubType string, configMap map[string]interface{}) (PubSub, error) {
	switch pubSubType {
	case "REDIS":
		return NewRedisPubSub(configMap)
	default:
		return nil, errors.New("invalid pubsub type")
	}
}
