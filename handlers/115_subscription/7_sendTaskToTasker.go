package subscription

import (
	"log"
)

// sendTaskToTasker sends a task to available taskers - STEP 7: Task Assignment
func sendTaskToTasker(taskID, serviceID string, favouriteTasker []string) {
	go func() {
		// Simplified version - in real implementation, this would call the gRPC service
		log.Printf("Sending task %s (service: %s) to taskers. Favourite taskers: %v", taskID, serviceID, favouriteTasker)

		// In the real implementation, you would:
		// 1. Connect to the send task gRPC service
		// 2. Send the task to available taskers
		// 3. Handle the response
	}()
}
