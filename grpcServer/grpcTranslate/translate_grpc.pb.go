// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcTranslate

import (
	context "context"
	translateMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/translateMessage"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// TranslateClient is the client API for Translate service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TranslateClient interface {
	Translate(ctx context.Context, in *translateMessage.TranslateRequest, opts ...grpc.CallOption) (*translateMessage.TranslateResponse, error)
	DetectLanguage(ctx context.Context, in *translateMessage.DetectLanguageRequest, opts ...grpc.CallOption) (*translateMessage.DetectLanguageResponse, error)
}

type translateClient struct {
	cc grpc.ClientConnInterface
}

func NewTranslateClient(cc grpc.ClientConnInterface) TranslateClient {
	return &translateClient{cc}
}

func (c *translateClient) Translate(ctx context.Context, in *translateMessage.TranslateRequest, opts ...grpc.CallOption) (*translateMessage.TranslateResponse, error) {
	out := new(translateMessage.TranslateResponse)
	err := c.cc.Invoke(ctx, "/grpcTranslate.Translate/Translate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *translateClient) DetectLanguage(ctx context.Context, in *translateMessage.DetectLanguageRequest, opts ...grpc.CallOption) (*translateMessage.DetectLanguageResponse, error) {
	out := new(translateMessage.DetectLanguageResponse)
	err := c.cc.Invoke(ctx, "/grpcTranslate.Translate/DetectLanguage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TranslateServer is the server API for Translate service.
// All implementations must embed UnimplementedTranslateServer
// for forward compatibility
type TranslateServer interface {
	Translate(context.Context, *translateMessage.TranslateRequest) (*translateMessage.TranslateResponse, error)
	DetectLanguage(context.Context, *translateMessage.DetectLanguageRequest) (*translateMessage.DetectLanguageResponse, error)
	mustEmbedUnimplementedTranslateServer()
}

// UnimplementedTranslateServer must be embedded to have forward compatible implementations.
type UnimplementedTranslateServer struct {
}

func (UnimplementedTranslateServer) Translate(context.Context, *translateMessage.TranslateRequest) (*translateMessage.TranslateResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Translate not implemented")
}
func (UnimplementedTranslateServer) DetectLanguage(context.Context, *translateMessage.DetectLanguageRequest) (*translateMessage.DetectLanguageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DetectLanguage not implemented")
}
func (UnimplementedTranslateServer) mustEmbedUnimplementedTranslateServer() {}

// UnsafeTranslateServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TranslateServer will
// result in compilation errors.
type UnsafeTranslateServer interface {
	mustEmbedUnimplementedTranslateServer()
}

func RegisterTranslateServer(s *grpc.Server, srv TranslateServer) {
	s.RegisterService(&_Translate_serviceDesc, srv)
}

func _Translate_Translate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(translateMessage.TranslateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TranslateServer).Translate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcTranslate.Translate/Translate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TranslateServer).Translate(ctx, req.(*translateMessage.TranslateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Translate_DetectLanguage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(translateMessage.DetectLanguageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TranslateServer).DetectLanguage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcTranslate.Translate/DetectLanguage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TranslateServer).DetectLanguage(ctx, req.(*translateMessage.DetectLanguageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Translate_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcTranslate.Translate",
	HandlerType: (*TranslateServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Translate",
			Handler:    _Translate_Translate_Handler,
		},
		{
			MethodName: "DetectLanguage",
			Handler:    _Translate_DetectLanguage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "translate.proto",
}
