// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcEventIndo

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	eventMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/eventMessage"
	users "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// EventIndoClient is the client API for EventIndo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EventIndoClient interface {
	TrackingCleverTapPayComboSuccess(ctx context.Context, in *eventMessage.TrackingCleverTapPayComboMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapPayComboPaymentError(ctx context.Context, in *eventMessage.TrackingCleverTapPayComboMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerAcceptTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerCancelTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerWithdrawTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerDoneTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerRating(ctx context.Context, in *eventMessage.TrackingCleverTapRatingMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerFinishTraining(ctx context.Context, in *eventMessage.TrackingCleverTapTrainingMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapPrepayTaskPaymentResponse(ctx context.Context, in *eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	AddAskerDoneBookingReferralGift(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*empty.Empty, error)
	AddAskerSignUpReferralGift(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskExpired(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionLogin(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionDoneTask(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionTaskerDoneTask(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionReferral(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionDoneTaskReferral(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionRollCall(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionRollCallAtNoel(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionDoneTaskFirst(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionNewSignUp(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
}

type eventIndoClient struct {
	cc grpc.ClientConnInterface
}

func NewEventIndoClient(cc grpc.ClientConnInterface) EventIndoClient {
	return &eventIndoClient{cc}
}

func (c *eventIndoClient) TrackingCleverTapPayComboSuccess(ctx context.Context, in *eventMessage.TrackingCleverTapPayComboMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/TrackingCleverTapPayComboSuccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) TrackingCleverTapPayComboPaymentError(ctx context.Context, in *eventMessage.TrackingCleverTapPayComboMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/TrackingCleverTapPayComboPaymentError", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) TrackingCleverTapTaskerAcceptTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/TrackingCleverTapTaskerAcceptTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) TrackingCleverTapTaskerCancelTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/TrackingCleverTapTaskerCancelTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) TrackingCleverTapTaskerWithdrawTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/TrackingCleverTapTaskerWithdrawTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) TrackingCleverTapTaskerDoneTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/TrackingCleverTapTaskerDoneTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) TrackingCleverTapTaskerRating(ctx context.Context, in *eventMessage.TrackingCleverTapRatingMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/TrackingCleverTapTaskerRating", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) TrackingCleverTapTaskerFinishTraining(ctx context.Context, in *eventMessage.TrackingCleverTapTrainingMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/TrackingCleverTapTaskerFinishTraining", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) TrackingCleverTapPrepayTaskPaymentResponse(ctx context.Context, in *eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/TrackingCleverTapPrepayTaskPaymentResponse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) AddAskerDoneBookingReferralGift(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/AddAskerDoneBookingReferralGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) AddAskerSignUpReferralGift(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/AddAskerSignUpReferralGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) TrackingCleverTapTaskExpired(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/TrackingCleverTapTaskExpired", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) PublishUserGameCampaignActionLogin(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionLogin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) PublishUserGameCampaignActionDoneTask(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionDoneTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) PublishUserGameCampaignActionTaskerDoneTask(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionTaskerDoneTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) PublishUserGameCampaignActionReferral(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionReferral", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) PublishUserGameCampaignActionDoneTaskReferral(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionDoneTaskReferral", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) PublishUserGameCampaignActionRollCall(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionRollCall", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) PublishUserGameCampaignActionRollCallAtNoel(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionRollCallAtNoel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) PublishUserGameCampaignActionDoneTaskFirst(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionDoneTaskFirst", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventIndoClient) PublishUserGameCampaignActionNewSignUp(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionNewSignUp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EventIndoServer is the server API for EventIndo service.
// All implementations must embed UnimplementedEventIndoServer
// for forward compatibility
type EventIndoServer interface {
	TrackingCleverTapPayComboSuccess(context.Context, *eventMessage.TrackingCleverTapPayComboMessage) (*empty.Empty, error)
	TrackingCleverTapPayComboPaymentError(context.Context, *eventMessage.TrackingCleverTapPayComboMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerAcceptTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerCancelTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerWithdrawTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerDoneTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerRating(context.Context, *eventMessage.TrackingCleverTapRatingMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerFinishTraining(context.Context, *eventMessage.TrackingCleverTapTrainingMessage) (*empty.Empty, error)
	TrackingCleverTapPrepayTaskPaymentResponse(context.Context, *eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage) (*empty.Empty, error)
	AddAskerDoneBookingReferralGift(context.Context, *users.Users) (*empty.Empty, error)
	AddAskerSignUpReferralGift(context.Context, *users.Users) (*empty.Empty, error)
	TrackingCleverTapTaskExpired(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionLogin(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionDoneTask(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionTaskerDoneTask(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionReferral(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionDoneTaskReferral(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionRollCall(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionRollCallAtNoel(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionDoneTaskFirst(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionNewSignUp(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	mustEmbedUnimplementedEventIndoServer()
}

// UnimplementedEventIndoServer must be embedded to have forward compatible implementations.
type UnimplementedEventIndoServer struct {
}

func (UnimplementedEventIndoServer) TrackingCleverTapPayComboSuccess(context.Context, *eventMessage.TrackingCleverTapPayComboMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapPayComboSuccess not implemented")
}
func (UnimplementedEventIndoServer) TrackingCleverTapPayComboPaymentError(context.Context, *eventMessage.TrackingCleverTapPayComboMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapPayComboPaymentError not implemented")
}
func (UnimplementedEventIndoServer) TrackingCleverTapTaskerAcceptTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerAcceptTask not implemented")
}
func (UnimplementedEventIndoServer) TrackingCleverTapTaskerCancelTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerCancelTask not implemented")
}
func (UnimplementedEventIndoServer) TrackingCleverTapTaskerWithdrawTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerWithdrawTask not implemented")
}
func (UnimplementedEventIndoServer) TrackingCleverTapTaskerDoneTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerDoneTask not implemented")
}
func (UnimplementedEventIndoServer) TrackingCleverTapTaskerRating(context.Context, *eventMessage.TrackingCleverTapRatingMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerRating not implemented")
}
func (UnimplementedEventIndoServer) TrackingCleverTapTaskerFinishTraining(context.Context, *eventMessage.TrackingCleverTapTrainingMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerFinishTraining not implemented")
}
func (UnimplementedEventIndoServer) TrackingCleverTapPrepayTaskPaymentResponse(context.Context, *eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapPrepayTaskPaymentResponse not implemented")
}
func (UnimplementedEventIndoServer) AddAskerDoneBookingReferralGift(context.Context, *users.Users) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAskerDoneBookingReferralGift not implemented")
}
func (UnimplementedEventIndoServer) AddAskerSignUpReferralGift(context.Context, *users.Users) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAskerSignUpReferralGift not implemented")
}
func (UnimplementedEventIndoServer) TrackingCleverTapTaskExpired(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskExpired not implemented")
}
func (UnimplementedEventIndoServer) PublishUserGameCampaignActionLogin(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionLogin not implemented")
}
func (UnimplementedEventIndoServer) PublishUserGameCampaignActionDoneTask(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionDoneTask not implemented")
}
func (UnimplementedEventIndoServer) PublishUserGameCampaignActionTaskerDoneTask(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionTaskerDoneTask not implemented")
}
func (UnimplementedEventIndoServer) PublishUserGameCampaignActionReferral(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionReferral not implemented")
}
func (UnimplementedEventIndoServer) PublishUserGameCampaignActionDoneTaskReferral(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionDoneTaskReferral not implemented")
}
func (UnimplementedEventIndoServer) PublishUserGameCampaignActionRollCall(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionRollCall not implemented")
}
func (UnimplementedEventIndoServer) PublishUserGameCampaignActionRollCallAtNoel(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionRollCallAtNoel not implemented")
}
func (UnimplementedEventIndoServer) PublishUserGameCampaignActionDoneTaskFirst(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionDoneTaskFirst not implemented")
}
func (UnimplementedEventIndoServer) PublishUserGameCampaignActionNewSignUp(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionNewSignUp not implemented")
}
func (UnimplementedEventIndoServer) mustEmbedUnimplementedEventIndoServer() {}

// UnsafeEventIndoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EventIndoServer will
// result in compilation errors.
type UnsafeEventIndoServer interface {
	mustEmbedUnimplementedEventIndoServer()
}

func RegisterEventIndoServer(s *grpc.Server, srv EventIndoServer) {
	s.RegisterService(&_EventIndo_serviceDesc, srv)
}

func _EventIndo_TrackingCleverTapPayComboSuccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapPayComboMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).TrackingCleverTapPayComboSuccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/TrackingCleverTapPayComboSuccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).TrackingCleverTapPayComboSuccess(ctx, req.(*eventMessage.TrackingCleverTapPayComboMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_TrackingCleverTapPayComboPaymentError_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapPayComboMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).TrackingCleverTapPayComboPaymentError(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/TrackingCleverTapPayComboPaymentError",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).TrackingCleverTapPayComboPaymentError(ctx, req.(*eventMessage.TrackingCleverTapPayComboMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_TrackingCleverTapTaskerAcceptTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).TrackingCleverTapTaskerAcceptTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/TrackingCleverTapTaskerAcceptTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).TrackingCleverTapTaskerAcceptTask(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_TrackingCleverTapTaskerCancelTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).TrackingCleverTapTaskerCancelTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/TrackingCleverTapTaskerCancelTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).TrackingCleverTapTaskerCancelTask(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_TrackingCleverTapTaskerWithdrawTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).TrackingCleverTapTaskerWithdrawTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/TrackingCleverTapTaskerWithdrawTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).TrackingCleverTapTaskerWithdrawTask(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_TrackingCleverTapTaskerDoneTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).TrackingCleverTapTaskerDoneTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/TrackingCleverTapTaskerDoneTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).TrackingCleverTapTaskerDoneTask(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_TrackingCleverTapTaskerRating_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapRatingMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).TrackingCleverTapTaskerRating(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/TrackingCleverTapTaskerRating",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).TrackingCleverTapTaskerRating(ctx, req.(*eventMessage.TrackingCleverTapRatingMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_TrackingCleverTapTaskerFinishTraining_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTrainingMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).TrackingCleverTapTaskerFinishTraining(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/TrackingCleverTapTaskerFinishTraining",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).TrackingCleverTapTaskerFinishTraining(ctx, req.(*eventMessage.TrackingCleverTapTrainingMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_TrackingCleverTapPrepayTaskPaymentResponse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).TrackingCleverTapPrepayTaskPaymentResponse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/TrackingCleverTapPrepayTaskPaymentResponse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).TrackingCleverTapPrepayTaskPaymentResponse(ctx, req.(*eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_AddAskerDoneBookingReferralGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(users.Users)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).AddAskerDoneBookingReferralGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/AddAskerDoneBookingReferralGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).AddAskerDoneBookingReferralGift(ctx, req.(*users.Users))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_AddAskerSignUpReferralGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(users.Users)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).AddAskerSignUpReferralGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/AddAskerSignUpReferralGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).AddAskerSignUpReferralGift(ctx, req.(*users.Users))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_TrackingCleverTapTaskExpired_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).TrackingCleverTapTaskExpired(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/TrackingCleverTapTaskExpired",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).TrackingCleverTapTaskExpired(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_PublishUserGameCampaignActionLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).PublishUserGameCampaignActionLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionLogin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).PublishUserGameCampaignActionLogin(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_PublishUserGameCampaignActionDoneTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).PublishUserGameCampaignActionDoneTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionDoneTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).PublishUserGameCampaignActionDoneTask(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_PublishUserGameCampaignActionTaskerDoneTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).PublishUserGameCampaignActionTaskerDoneTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionTaskerDoneTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).PublishUserGameCampaignActionTaskerDoneTask(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_PublishUserGameCampaignActionReferral_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).PublishUserGameCampaignActionReferral(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionReferral",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).PublishUserGameCampaignActionReferral(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_PublishUserGameCampaignActionDoneTaskReferral_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).PublishUserGameCampaignActionDoneTaskReferral(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionDoneTaskReferral",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).PublishUserGameCampaignActionDoneTaskReferral(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_PublishUserGameCampaignActionRollCall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).PublishUserGameCampaignActionRollCall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionRollCall",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).PublishUserGameCampaignActionRollCall(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_PublishUserGameCampaignActionRollCallAtNoel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).PublishUserGameCampaignActionRollCallAtNoel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionRollCallAtNoel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).PublishUserGameCampaignActionRollCallAtNoel(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_PublishUserGameCampaignActionDoneTaskFirst_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).PublishUserGameCampaignActionDoneTaskFirst(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionDoneTaskFirst",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).PublishUserGameCampaignActionDoneTaskFirst(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventIndo_PublishUserGameCampaignActionNewSignUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventIndoServer).PublishUserGameCampaignActionNewSignUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventIndo.EventIndo/PublishUserGameCampaignActionNewSignUp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventIndoServer).PublishUserGameCampaignActionNewSignUp(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

var _EventIndo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcEventIndo.EventIndo",
	HandlerType: (*EventIndoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TrackingCleverTapPayComboSuccess",
			Handler:    _EventIndo_TrackingCleverTapPayComboSuccess_Handler,
		},
		{
			MethodName: "TrackingCleverTapPayComboPaymentError",
			Handler:    _EventIndo_TrackingCleverTapPayComboPaymentError_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerAcceptTask",
			Handler:    _EventIndo_TrackingCleverTapTaskerAcceptTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerCancelTask",
			Handler:    _EventIndo_TrackingCleverTapTaskerCancelTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerWithdrawTask",
			Handler:    _EventIndo_TrackingCleverTapTaskerWithdrawTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerDoneTask",
			Handler:    _EventIndo_TrackingCleverTapTaskerDoneTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerRating",
			Handler:    _EventIndo_TrackingCleverTapTaskerRating_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerFinishTraining",
			Handler:    _EventIndo_TrackingCleverTapTaskerFinishTraining_Handler,
		},
		{
			MethodName: "TrackingCleverTapPrepayTaskPaymentResponse",
			Handler:    _EventIndo_TrackingCleverTapPrepayTaskPaymentResponse_Handler,
		},
		{
			MethodName: "AddAskerDoneBookingReferralGift",
			Handler:    _EventIndo_AddAskerDoneBookingReferralGift_Handler,
		},
		{
			MethodName: "AddAskerSignUpReferralGift",
			Handler:    _EventIndo_AddAskerSignUpReferralGift_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskExpired",
			Handler:    _EventIndo_TrackingCleverTapTaskExpired_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionLogin",
			Handler:    _EventIndo_PublishUserGameCampaignActionLogin_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionDoneTask",
			Handler:    _EventIndo_PublishUserGameCampaignActionDoneTask_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionTaskerDoneTask",
			Handler:    _EventIndo_PublishUserGameCampaignActionTaskerDoneTask_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionReferral",
			Handler:    _EventIndo_PublishUserGameCampaignActionReferral_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionDoneTaskReferral",
			Handler:    _EventIndo_PublishUserGameCampaignActionDoneTaskReferral_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionRollCall",
			Handler:    _EventIndo_PublishUserGameCampaignActionRollCall_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionRollCallAtNoel",
			Handler:    _EventIndo_PublishUserGameCampaignActionRollCallAtNoel_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionDoneTaskFirst",
			Handler:    _EventIndo_PublishUserGameCampaignActionDoneTaskFirst_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionNewSignUp",
			Handler:    _EventIndo_PublishUserGameCampaignActionNewSignUp_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "eventIndo.proto",
}
