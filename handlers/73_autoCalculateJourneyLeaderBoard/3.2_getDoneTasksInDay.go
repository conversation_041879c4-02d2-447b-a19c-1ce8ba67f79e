package autoCalculateJourneyLeaderBoard

import (
	"fmt"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func getDoneTasksInDay(taskerList []string, atDate time.Time) (map[string][]*modelTask.Task, error) {
	tasks, err := getDoneTasksByChunk(taskerList, atDate)
	if err != nil {
		return nil, err
	}

	mapDoneTasksByTaskerId := make(map[string][]*modelTask.Task)
	mapTaskIsPrecessed := make(map[string]bool)
	for _, t := range tasks {
		if mapTaskIsPrecessed[t.XId] {
			continue
		}
		for _, acceptedTasker := range t.AcceptedTasker {
			if mapDoneTasksByTaskerId[acceptedTasker.TaskerId] == nil {
				mapDoneTasksByTaskerId[acceptedTasker.TaskerId] = []*modelTask.Task{}
			}
			mapDoneTasksByTaskerId[acceptedTasker.TaskerId] = append(mapDoneTasksByTaskerId[acceptedTasker.TaskerId], t)
		}
		mapTaskIsPrecessed[t.XId] = true
	}
	return mapDoneTasksByTaskerId, nil
}

func getDoneTasksByChunk(taskerList []string, atDate time.Time) ([]*modelTask.Task, error) {
	chunkTaskerList := globalLib.SplitArrayToChunk(taskerList, CHUNK_SIZE_TASKERS_GET_DONE_TASK)
	startTime, endTime := getTimeRangeToCheckPoint(atDate)
	var tasks []*modelTask.Task

	for _, taskerIds := range chunkTaskerList {
		var chunk []*modelTask.Task
		err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
			bson.M{
				"date":                    bson.M{"$gte": startTime, "$lte": endTime},
				"acceptedTasker.taskerId": bson.M{"$in": taskerIds},
				"status":                  globalConstant.TASK_STATUS_DONE,
			},
			bson.M{"acceptedTasker.taskerId": 1, "duration": 1, "increaseDurationData": 1},
			&chunk,
		)
		if err != nil {
			err = fmt.Errorf("getDoneTasksByChunk: %v", err)
			return nil, err
		}

		tasks = append(tasks, chunk...)
	}
	return tasks, nil
}
