package sendBusinessReportEmail

import (
	"fmt"
	"net/http"

	"gitlab.com/btaskee/go-email-vn-v3/handlers"
	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
)

func sendEmail(filePath, lang, mailTo string, user *modelUser.Users, month, year int32) (*modelEmailResponse.EmailResponse, error) {
	// Get email template
	email := &emailSending.EmailSending{
		ReplyTo: "<EMAIL>",
		From:    "<<EMAIL>>",
		To:      mailTo,
		Subject: localization.T(lang, "BUSINESS_REPORT_EMAIL_SUBJECT"),
		Attachments: []string{
			filePath,
		},
	}
	template := fmt.Sprintf("%s/businessReport.html", cfg.EmailTemplateURL)
	askerName := user.Name
	if lang == globalConstant.LANG_KO {
		askerName = fmt.Sprintf("%s님", askerName)
	}
	// Send email
	emailBusinessReport := map[string]interface{}{
		"EmailHello":                     localization.T(lang, "EMAIL_HELLO"),
		"Username":                       askerName,
		"BusinessReportEmailDescription": localization.T(lang, "BUSINESS_REPORT_EMAIL_DESCRIPTION", month, year),
		"BusinessReportEmailNotice1":     localization.T(lang, "EMAIL_SUPPORT_NOTICE_1"),
		"BusinessReportEmailNotice2":     localization.T(lang, "EMAIL_SUPPORT_NOTICE_2"),
		"BusinessReportEmailClosing1":    localization.T(lang, "EMAIL_CLOSING_1"),
		"BusinessReportEmailClosing2":    localization.T(lang, "EMAIL_CLOSING_2"),
		"BusinessReportEmailClosing3":    localization.T(lang, "EMAIL_CLOSING_3"),
		"FollowbTaskeeAt":                localization.T(lang, "FOLLOW_BTASKEE_AT"),
	}
	emailBusinessReport = globalLib.UpdateBtaskeeInfoInEmailByIsoCode(emailBusinessReport, local.ISO_CODE)
	body, err := handlers.ParseTemplate(template, emailBusinessReport)
	if body == "" || err != nil {
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_CONTENT_NIL,
		}, err
	}

	email.Content = body
	return handlers.SendEmail(email)
}
