import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import {
  revertTimeZone,
  trackingScheduleStatus,
} from '/server/helper-function.js';

const autoCalculateTaskerScore = () => {
  return;
	console.log("Start auto calculate Tasker Score ...");
  var message = null;
  try {
    // This week calculate the score of last week
    const lastWeekDate = new Date();
    lastWeekDate.setDate(lastWeekDate.getDate() - 7); // Last week
    const week_2 = new Date(lastWeekDate.getFullYear(), lastWeekDate.getMonth(), lastWeekDate.getDate() - 7); // Last 2 weeks
    const week_3 = new Date(lastWeekDate.getFullYear(), lastWeekDate.getMonth(), lastWeekDate.getDate() - 14); // Last 3 weeks
    const week_4 = new Date(lastWeekDate.getFullYear(), lastWeekDate.getMonth(), lastWeekDate.getDate() - 21); // Last 4 weeks
    const startOfWeek_1 = moment(lastWeekDate).startOf('isoweek').toDate(); // Monday week 1
    const endOfWeek_1 = moment(lastWeekDate).endOf('isoweek').toDate(); // Sunday week 1
    const startOfWeek_2 = moment(week_2).startOf('isoweek').toDate(); // Monday week 2
    const endOfWeek_2 = moment(week_2).endOf('isoweek').toDate(); // Sunday week 2
    const startOfWeek_3 = moment(week_3).startOf('isoweek').toDate(); // Monday week 3
    const endOfWeek_3 = moment(week_3).endOf('isoweek').toDate(); // Sunday week 3
    const startOfWeek_4 = moment(week_4).startOf('isoweek').toDate(); // Monday week 4
    const endOfWeek_4 = moment(week_4).endOf('isoweek').toDate(); // Sunday week 4
    Meteor.appCollection.users.find({ type: 'TASKER', status: { $in: ['ACTIVE', 'LOCKED'] } }).forEach((tasker) => {
      const avgRating = tasker.avgRating || 0;
      let numberOfTaskInWeek_1 = 0;
      let numberOfTaskInWeek_2 = 0;
      let numberOfTaskInWeek_3 = 0;
      let numberOfTaskInWeek_4 = 0;
      let isUpdate = false;
      // Count number of done tasks in 4 weeks
      if (tasker.employeeIds) {
        // Calculate score for company
        isUpdate = true;
        numberOfTaskInWeek_1 = Meteor.appCollection.Task.find({ 'acceptedTasker.companyId': tasker._id, status: 'DONE', date: { $gte: startOfWeek_1, $lte: endOfWeek_1 } }).count();
        numberOfTaskInWeek_2 = Meteor.appCollection.Task.find({ 'acceptedTasker.companyId': tasker._id, status: 'DONE', date: { $gte: startOfWeek_2, $lte: endOfWeek_2 } }).count();
        numberOfTaskInWeek_3 = Meteor.appCollection.Task.find({ 'acceptedTasker.companyId': tasker._id, status: 'DONE', date: { $gte: startOfWeek_3, $lte: endOfWeek_3 } }).count();
        numberOfTaskInWeek_4 = Meteor.appCollection.Task.find({ 'acceptedTasker.companyId': tasker._id, status: 'DONE', date: { $gte: startOfWeek_4, $lte: endOfWeek_4 } }).count();
      } else if (!tasker.company) {
        // Don't calculate score for employees, just calculate for normal Taskers
        isUpdate = true;
        numberOfTaskInWeek_1 = Meteor.appCollection.Task.find({ 'acceptedTasker.taskerId': tasker._id, status: 'DONE', date: { $gte: startOfWeek_1, $lte: endOfWeek_1 } }).count();
        numberOfTaskInWeek_2 = Meteor.appCollection.Task.find({ 'acceptedTasker.taskerId': tasker._id, status: 'DONE', date: { $gte: startOfWeek_2, $lte: endOfWeek_2 } }).count();
        numberOfTaskInWeek_3 = Meteor.appCollection.Task.find({ 'acceptedTasker.taskerId': tasker._id, status: 'DONE', date: { $gte: startOfWeek_3, $lte: endOfWeek_3 } }).count();
        numberOfTaskInWeek_4 = Meteor.appCollection.Task.find({ 'acceptedTasker.taskerId': tasker._id, status: 'DONE', date: { $gte: startOfWeek_4, $lte: endOfWeek_4 } }).count();
      }
      if (isUpdate) {
        // If new tasker and 3 last week didn't have task
        if (numberOfTaskInWeek_1 > 0 &&
          numberOfTaskInWeek_2 === 0 &&
          numberOfTaskInWeek_3 === 0 &&
          numberOfTaskInWeek_4 === 0 &&
          new Date(tasker.createdAt) >= startOfWeek_1) {
            numberOfTaskInWeek_2 = numberOfTaskInWeek_1;
            numberOfTaskInWeek_3 = numberOfTaskInWeek_1;
            numberOfTaskInWeek_4 = numberOfTaskInWeek_1;
        }
        // Total task
        const totalTasks = tasker.taskDone || 0;
        const loyalty = 2 * numberOfTaskInWeek_1 + 0.8 * numberOfTaskInWeek_2 + 0.6 * numberOfTaskInWeek_3 + 0.4 * numberOfTaskInWeek_4 + 0.2 * totalTasks;
        const level = totalTasks / 40;

        const score = Math.round((loyalty / 3) + (avgRating * level));
        var historyScore = tasker.scoreHistory && tasker.scoreHistory.score ? tasker.scoreHistory.score : 0;
        if (tasker.score && score !== tasker.score) {
          historyScore = tasker.score;
        }
        Meteor.appCollection.users.update({ _id: tasker._id }, { $set: { score: score || 0 , 'scoreHistory.score': historyScore} });
      }
    });

    // Calculate Tasker's score rate
    var count = 0;
    var previousScore = 0;
    var previousRate = 0;
    var page = 1;
    var limit = 1000;
    var run = true;
    while (run) {
      try {
        var taskers = Meteor.appCollection.users.find(
          {type: 'TASKER', status: 'ACTIVE'},
          {
            sort: {score: -1},
            fields: {score: 1, scoreRate: 1},
            skip: (page - 1) * limit,
            limit: limit
          }
        ).fetch();
        if (taskers && taskers.length > 0) {
          taskers.forEach(function(tasker) {
            count += 1;
            var scoreRate = count;
            if (tasker.score !== previousScore) {
              previousScore = tasker.score;
              previousRate = count;
            } else {
              scoreRate = previousRate;
            }
            var historyScoreRate = tasker.scoreHistory && tasker.scoreHistory.scoreRate ? tasker.scoreHistory.scoreRate : 0;
            if ((tasker.scoreRate && scoreRate !== tasker.scoreRate) || (tasker.scoreRate > 0 && historyScoreRate == 0)) {
              historyScoreRate = tasker.scoreRate;
            }
            Meteor.appCollection.users.update({_id: tasker._id}, {$set: {scoreRate: scoreRate, 'scoreHistory.scoreRate': historyScoreRate}});
          });
  
          page++;
        } else {
          run = false;
          return;
        }
      } catch (e) {
        run = false;
        return;
      }
    }
    // Update test Tasker's score is 0
    // const testerPhone = Tester.find().map((t) => (t.phone));
    // Meteor.appCollection.users.update('users', {phone: {$in: testerPhone}, type: 'TASKER'}, {$set: {score: 0}});
  } catch (e) {
    message = e;
  }
	console.log("Finish calculate Tasker Score.");
  // Tracking
  trackingScheduleStatus({name: 'CalculateTaskerScore', nextRunAt: SyncedCron.nextScheduledAtDate('calculateTaskerScore'), message});
};

  Meteor.methods({
    calculateTaskerScore() {
      SyncedCron.add({
        name: 'calculateTaskerScore',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 3)).getHours();
          // Run at 3:00 every Monday with timezone GMT+7.
          return parser.cron(`0 0 ${hourInTimeZoneServer} ? * MON *`, true);
        },
        job() {
          autoCalculateTaskerScore();
        },
      });
	console.log("Added calculateTaskerScore cron job");
    },
    'runNow.calculateTaskerScore'() {
      autoCalculateTaskerScore();
    },
  });
