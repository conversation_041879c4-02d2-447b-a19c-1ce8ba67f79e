syntax = "proto3";
package grpcChatServerIndo;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcChatServerIndo";

import "google/protobuf/empty.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage/websocketMessage.proto";

service ChatServerIndo {
  rpc SendSocketChatMessage (websocketMessage.WebsocketMessage) returns (google.protobuf.Empty) {}
}