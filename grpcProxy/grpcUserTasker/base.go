package grpcUserTasker

import (
	"context"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcUserTaskerVN"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/employeeProfile"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/emptypb"
)

type UserTaskerVNClient interface {
	CreateEmployeeAccount(ctx context.Context, in *employeeProfile.EmployeeProfile, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

func Connect(isoCode, endpoint string) (UserTaskerVNClient, *grpc.ClientConn, error) {
	switch isoCode {
	case globalConstant.ISO_CODE_VN:
		return ConnectGRPCUserTaskerVN(endpoint)
	}
	return nil, nil, nil
}

func ConnectGRPCUserTaskerVN(endpoint string) (grpcUserTaskerVN.UserTaskerVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcUserTaskerVN.NewUserTaskerVNClient(connect)
	return client, connect, err
}
