package grpcScheduleIndo

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcScheduleIndo"
	"google.golang.org/grpc"
)

func ConnectGRPCSchedule(endpoint string) (grpcScheduleIndo.ScheduleIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcScheduleIndo.NewScheduleIndoClient(connect)
	return client, connect, err
}
