package notifyTaskerGameCampaign

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"

	"go.mongodb.org/mongo-driver/bson"
)

func sendNotification(userIds []string, title, description *service.ServiceText) {
	if len(userIds) == 0 {
		return
	}

	var users []*modelUser.Users
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_USERS, bson.M{"_id": bson.M{"$in": userIds}}, bson.M{"_id": 1, "language": 1}, &users)
	if len(users) == 0 {
		return
	}

	var notiInfos []interface{}
	notiUserIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{}

	for _, user := range users {
		lang := globalConstant.LANG_VI
		if user.Language != "" {
			lang = user.Language
		}
		notiInfos = append(notiInfos, &modelNotification.Notification{
			XId:         globalLib.GenerateObjectId(),
			UserId:      user.XId,
			Type:        25,
			Title:       globalLib.LocalizeServiceName(lang, title),
			Description: globalLib.LocalizeServiceName(lang, description),
			NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_GAME_LUCKY_DRAW,
			CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
		})

		notiUserIds = append(notiUserIds, &modelPushNotificationRequest.PushNotificationRequestUserIds{
			UserId:   user.XId,
			Language: lang,
		})
	}
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       25,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_GAME_LUCKY_DRAW,
	}
	lib.SendNotification(notiInfos, notiUserIds, title, description, payload, true)
}
