package grpcApiAskerVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcApiAskerVN"
	"google.golang.org/grpc"
)

func ConnectGRPCApiAskerVN(endpoint string) (grpcApiAskerVN.ApiAskerVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcApiAskerVN.NewApiAskerVNClient(connect)
	return client, connect, err
}
