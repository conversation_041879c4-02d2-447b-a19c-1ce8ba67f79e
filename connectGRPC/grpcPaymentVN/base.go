package grpcPaymentVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPaymentVN"
	"google.golang.org/grpc"
)

func ConnectGRPCPaymentVN(endpoint string) (grpcPaymentVN.PaymentVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPaymentVN.NewPaymentVNClient(connect)
	return client, connect, err
}
