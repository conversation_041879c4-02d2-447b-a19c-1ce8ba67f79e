package handlers

import (
	"fmt"
	"net/http"
	"sort"
	"strings"

	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func SendChangeToCashScheduleEmail(req *modelEmailSending.EmailChangeToTaskCashScheduleRequest) (*modelEmailResponse.EmailResponse, error) {
	defer local.Logger.Sync()

	// Check server config
	resErr := CheckEmailConfig()
	if resErr != nil {
		local.Logger.Error(resErr.Message)
		return resErr, nil
	}

	// Validate
	if req.Task == nil {
		local.Logger.Warn(lib.ERROR_BOOKING_NOT_FOUND,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_BOOKING_NOT_FOUND,
		}, nil
	}
	if req.Asker == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_USER_NOT_FOUND,
		}, nil
	}
	if req.Card == nil {
		local.Logger.Warn(lib.ERROR_CARD_NOT_FOUND,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_CARD_NOT_FOUND,
		}, nil
	}

	if len(req.Asker.Emails) == 0 {
		local.Logger.Warn(lib.ERROR_TO_EMAIL_NIL,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusInternalServerError,
			Message:    lib.ERROR_TO_EMAIL_NIL,
		}, nil
	}
	// Get service data
	var service *modelService.Service
	globalDataAccess.GetOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], req.Task.ServiceId, bson.M{"text": 1}, &service)
	if service == nil {
		local.Logger.Warn(lib.ERROR_SERVICE_NOT_FOUND,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusNotFound,
			Message:    lib.ERROR_SERVICE_NOT_FOUND,
		}, nil
	}

	lang := globalConstant.LANG_EN
	if req.Asker.Language != "" {
		lang = req.Asker.Language
	}
	serviceText := globalLib.LocalizeServiceName(lang, service.Text)
	var currencyCode string
	if req.Task.OriginCurrency != nil && req.Task.OriginCurrency.Code != "" {
		currencyCode = req.Task.OriginCurrency.Code
	} else if req.Task.Currency != "" {
		currencyCode = req.Task.Currency
	}

	// Get setting data
	var settings *modelSettings.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"referralSetting": 1}, &settings)
	if settings == nil {
		local.Logger.Warn(lib.ERROR_SETTING_NOT_FOUND,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusNotFound,
			Message:    lib.ERROR_SETTING_NOT_FOUND,
		}, nil
	}

	emailData := map[string]interface{}{
		"ServiceName":                    serviceText,
		"Dear":                           localization.T(lang, "RENEW_SUBS_EMAIL_DEAR"),
		"AskerName":                      req.Asker.Name,
		"Thank":                          localization.T(lang, "RENEW_SUBS_EMAIL_THANK_FOR"),
		"ChangeToTaskCashScheduleNotice": localization.T(lang, "TASK_SCHEDULE_CHANGE_PAYMENT_EMAIL_CONTENT", len(req.Schedules), req.Card.Number),
		"CardNumberTitle":                localization.T(lang, "CARD_NUMBER_TITLE"),
		"CardNumber":                     req.Card.Number,
		"TaskChangeScheduleListTitle":    localization.T(lang, "TASK_CHANGE_SCHEDULE_LIST_TITLE"),
		"FollowbTaskeeAt":                localization.T(lang, "FOLLOW_BTASKEE_AT"),
	}

	daysInWeekList := []map[string]interface{}{}
	for _, schedule := range req.Schedules {
		// Sort
		sort.SliceStable(schedule.WeeklyRepeater, func(i, j int) bool {
			return schedule.WeeklyRepeater[i] < schedule.WeeklyRepeater[j]
		})
		weeklyRepeaterString := strings.Trim(strings.Join(strings.Fields(fmt.Sprint(schedule.WeeklyRepeater)), ","), "[]")

		// Gen html template
		sTime := globalLib.ParseDateFromTimeStamp(schedule.ScheduleTime, local.TimeZone)
		daysInWeekList = append(daysInWeekList, map[string]interface{}{
			"DaysInWeek": fmt.Sprintf("%s %s %s %s", sTime.Format("15:04"), localization.T(lang, "DAYS_IN_WEEK"), weeklyRepeaterString, localization.T(lang, "WEEKLY")),
		})
	}
	emailData["DaysInWeekList"] = daysInWeekList

	// Check ivitation value
	referralValue, currencyCode := getReferralValueAndCurrency(req.Task.IsoCode, currencyCode, settings)
	if referralValue != 0 && currencyCode != "" {
		emailData["HasInvitation"] = true
		emailData["Invitation1"] = localization.T(lang, "INVITATION_1")
		emailData["Invitation2"] = localization.T(lang, "INVITATION_2")
		emailData["ReferralCode"] = req.Asker.ReferralCode
		emailData["InvitationDescription"] = localization.T(lang, "INVITE_DESCRIPTION", globalLib.FormatMoney(referralValue), currencyCode, globalLib.FormatMoney(referralValue), currencyCode)
	}

	emailData = globalLib.UpdateBtaskeeInfoInEmailByIsoCode(emailData, req.Task.IsoCode)

	emailTemplate := fmt.Sprintf("%s/changeToTaskCashScheduleEmail.html", cfg.EmailTemplateURL)
	body, err := ParseTemplate(emailTemplate, emailData)
	if err != nil {
		return nil, err
	}

	// NOTE: Test with cancel-booking "./test.sh 3 19"
	option := &modelEmailSending.EmailSending{
		ReplyTo: "<EMAIL>",
		From:    "bTaskee Receipts <<EMAIL>>",
		To:      req.Asker.Emails[0].Address,
		Subject: localization.T(lang, `TASK_SCHEDULE_CHANGE_PAYMENT_EMAIL_TITLE`),
		Bcc:     []string{"<EMAIL>"},
		Content: body,
	}

	return SendEmail(option)
}
