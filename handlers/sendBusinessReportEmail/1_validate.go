package sendBusinessReportEmail

import (
	"net/http"

	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	"go.uber.org/zap"
)

func validate(req *modelEmailSending.EmailSendBusinessReportRequest) *modelEmailResponse.EmailResponse {
	// validate request
	if req.BusinessId == "" {
		local.Logger.Warn(lib.ERROR_BUSINESS_ID_REQUIRED,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_BUSINESS_ID_REQUIRED,
		}
	}
	return nil
}
