package globalConstant

import modelSettingCountry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"

const ()

var (
	CURRENCY_SIGN = map[string]*modelSettingCountry.SettingCountryCurrency{
		ISO_CODE_VN:   &CURRENCY_SIGN_VN,
		ISO_CODE_TH:   &CURRENCY_SIGN_TH,
		ISO_CODE_INDO: &CURRENCY_SIGN_ID,
		ISO_CODE_MY:   &CURRENCY_SIGN_MY,
	}
	PREPAY_PAYMENT_METHODS = map[string][]string{
		ISO_CODE_VN:   PREPAY_PAYMENT_METHODS_VN,
		ISO_CODE_TH:   PREPAY_PAYMENT_METHODS_TH,
		ISO_CODE_INDO: PREPAY_PAYMENT_METHODS_INDO,
		ISO_CODE_MY:   PREPAY_PAYMENT_METHODS_MY,
	}
	COUNTRY_CODE = map[string]string{
		ISO_CODE_VN:   COUNTRY_CODE_VN,
		ISO_CODE_TH:   COUNTRY_CODE_TH,
		ISO_CODE_INDO: COUNTRY_CODE_INDO,
		ISO_CODE_MY:   COUNTRY_CODE_MY,
	}
	DEFAULT_TASK_RATE = map[string]float64{
		ISO_CODE_VN:   TASK_RATE,
		ISO_CODE_TH:   TASK_RATE_TH,
		ISO_CODE_INDO: TASK_RATE_INDO,
		ISO_CODE_MY:   0.2,
	}
	ISO_CODE = map[string]string{
		COUNTRY_CODE_VN:   ISO_CODE_VN,
		COUNTRY_CODE_TH:   ISO_CODE_TH,
		COUNTRY_CODE_INDO: ISO_CODE_INDO,
		COUNTRY_CODE_MY:   ISO_CODE_MY,
	}
	LANGUAGE = map[string]string{
		ISO_CODE_VN:   LANG_VI,
		ISO_CODE_TH:   LANG_TH,
		ISO_CODE_INDO: LANG_ID,
		ISO_CODE_MY:   LANG_MY,
	}
)

var (
	TRAINING_RESULT_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "training-result",
		ISO_CODE_TH:   "th-tasker-training-result",
		ISO_CODE_INDO: "id-tasker-training-result",
		ISO_CODE_MY:   "my-tasker-training-result",
	}
	TRAINING_OFFICE_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "training-office",
		ISO_CODE_TH:   "th-tasker-training-result", // bên Thái Lan chưa tách channel nên dùng chung 1 channel
		ISO_CODE_INDO: "id-tasker-training-office", // "id-tasker-training-office"
		ISO_CODE_MY:   "my-tasker-training-office",
	}
	TASKER_BLOCKED_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "tasker-blocked",
		ISO_CODE_TH:   "th-tasker-blocked",
		ISO_CODE_INDO: "id-tasker-blocked",
		ISO_CODE_MY:   "my-tasker-blocked",
	}
	ASKER_COMPLAINT_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "asker-complaint",
		ISO_CODE_TH:   "th-asker-complaint",
		ISO_CODE_INDO: "id-asker-complaint", // "id-asker-complaint"
		ISO_CODE_MY:   "my-asker-complaint",
	}
	ASKER_FEEDBACK_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "asker-feedback",    // "id-asker-feedback"
		ISO_CODE_TH:   "th-asker-feedback", // "id-asker-feedback"
		ISO_CODE_INDO: "id-asker-feedback", // "id-asker-feedback"
		ISO_CODE_MY:   "my-asker-feedback",
	}
	TASKER_BREWARDS_SLACK_CHANNEL = map[string]string{
		ISO_CODE_INDO: "id-tasker-brewards", // "id-tasker-brewards"
		ISO_CODE_MY:   "my-tasker-brewards",
	}
	ASKER_BREWARDS_SLACK_CHANNEL = map[string]string{
		ISO_CODE_INDO: "id-asker-brewards", // "id-tasker-brewards"
		ISO_CODE_MY:   "my-asker-brewards",
	}
	GO_SERVICES_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "go-services",
		ISO_CODE_TH:   "go-services",
		ISO_CODE_INDO: "id-go-services", // "id-go-services"
		ISO_CODE_MY:   "my-go-services",
	}
	ASKER_ERROR_TRACKING_SLACK_CHANNEL = map[string]string{
		ISO_CODE_INDO: "id-asker-error-tracking", // "id-asker-error-tracking"
		ISO_CODE_MY:   "my-asker-error-tracking",
	}
	MIGRATE_REFERRAL_CODE_SLACK_CHANNEL = map[string]string{
		ISO_CODE_INDO: "id-migrage-refferal-code", // "id-migrage-refferal-code"
		ISO_CODE_MY:   "my-migrage-refferal-code",
	}
	TASKER_CANCEL_TASK_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "cancel-task-vn",
		ISO_CODE_TH:   "th-tasker-cancel-task",
		ISO_CODE_INDO: "id-tasker-cancel-task",
		ISO_CODE_MY:   "my-tasker-cancel-task",
	}
	BTASKEE_SYSTEM_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "btaskee-system",
		ISO_CODE_TH:   "th-btaskee-system",
		ISO_CODE_INDO: "id-btaskee-system", // "id-btaskee-system"
		ISO_CODE_MY:   "my-btaskee-system",
	}
	MOVING_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "moving-vn",
		ISO_CODE_TH:   "th-home-moving",
		ISO_CODE_INDO: "id-home-moving",
		ISO_CODE_MY:   "my-home-moving",
	}
	ALERT_ADDRESS_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "alert-address",
		ISO_CODE_TH:   "th-alert-address",
		ISO_CODE_INDO: "id-alert-address",
		ISO_CODE_MY:   "my-alert-address",
	}
	WASHING_MACHINE_SLACK_CHANNEL = map[string]string{
		ISO_CODE_INDO: "id-washing-machine-cs", // "id-washing-machine-cs"
		ISO_CODE_MY:   "my-washing-machine-cs",
	}
	ASKER_CANCEL_TASK_SLACK_CHANNEL = map[string]string{
		ISO_CODE_INDO: "id-asker-cancel-task", // "id-asker-cancel-task"
		ISO_CODE_MY:   "my-asker-cancel-task",
	}
	GO_SERVICE_PAYMENT_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "go-service-payment",
		ISO_CODE_TH:   "go-service-payment",
		ISO_CODE_INDO: "id-go-service-payment",
		ISO_CODE_MY:   "my-go-service-payment",
	}
	ALERTS_SLACK_CHANNEL = map[string]string{
		ISO_CODE_INDO: "id-alerts",
		ISO_CODE_MY:   "my-alerts",
	}
	NEW_ASKER_BLACKLIST_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "cs-vn-new-asker-blacklist",
		ISO_CODE_TH:   "th-cs-new-asker-blacklist",
		ISO_CODE_INDO: "id-new-asker-blacklist",
		ISO_CODE_MY:   "my-new-asker-blacklist",
	}
	ASKER_DUPLICATED_TASK_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "cs-vn-duplicated-task",
		ISO_CODE_TH:   "th-asker-duplicated-task",
		ISO_CODE_INDO: "id-asker-duplicated-task",
		ISO_CODE_MY:   "my-asker-duplicated-task",
	}
	DEV_CS_SLACK_CHANNEL = map[string]string{
		ISO_CODE_INDO: "id-dev-cs",
		ISO_CODE_MY:   "my-dev-cs",
	}
	SYNC_CRON_SLACK_CHANNEL = map[string]string{
		ISO_CODE_INDO: "id-go-service-sync-cron",
		ISO_CODE_MY:   "my-go-service-sync-cron",
	}
	SUB_SCHEDULE_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "vn-sub-schedule",
		ISO_CODE_TH:   "th-sub-schedule",
		ISO_CODE_INDO: "id-sub-schedule",
		ISO_CODE_MY:   "my-sub-schedule",
	}
	RESET_BPOINT_SLACK_CHANNEL = map[string]string{
		ISO_CODE_INDO: "id-go-reset-bpoint",
		ISO_CODE_MY:   "my-go-reset-bpoint",
	}
	REFUND_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "refund-vn",
		ISO_CODE_TH:   "th-refund",
		ISO_CODE_INDO: "id-refund",
		ISO_CODE_MY:   "my-refund",
	}
	SMS_ERROR_TRACKING_SLACK_CHANNEL = map[string]string{
		ISO_CODE_INDO: "id-sms-error-tracking",
		ISO_CODE_MY:   "my-sms-error-tracking",
	}
	OPERATIONS_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN: "operations_vn",
		ISO_CODE_MY: "my_operations",
	}
	TASK_REQUEST_DATE_TIME_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "vn-task-request-datetime",
		ISO_CODE_TH:   "th-task-request-datetime",
		ISO_CODE_INDO: "id-task-request-datetime",
		ISO_CODE_MY:   "my-task-request-datetime",
	}
	TASK_REQUEST_TASK_DETAIL_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "vn-task-request-task-detail",
		ISO_CODE_TH:   "th-task-request-task-detail",
		ISO_CODE_INDO: "id-task-request-task-detail",
		ISO_CODE_MY:   "my-task-request-task-detail",
	}
	PAYMENT_CS_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "payment-vn",
		ISO_CODE_TH:   "th-payment-cs",
		ISO_CODE_INDO: "id-payment-cs",
		ISO_CODE_MY:   "my-payment-cs",
	}
	VAT_SLACK_CHANNEL = map[string]string{
		ISO_CODE_TH: "th-vat",
		ISO_CODE_MY: "my-vat",
	}
	SUBSCRIPTION_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "subscription-vn",
		ISO_CODE_TH:   "th-subscription",
		ISO_CODE_INDO: "id-subscription",
		ISO_CODE_MY:   "my-subscription",
	}
	TASKER_REWARD_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "tasker-reward",
		ISO_CODE_TH:   "th-tasker-reward",
		ISO_CODE_INDO: "id-tasker-reward",
		ISO_CODE_MY:   "my-tasker-reward",
	}
	TASKER_BREWARD_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "vn-tasker-brewards",
		ISO_CODE_TH:   "th-tasker-brewards",
		ISO_CODE_INDO: "id-tasker-brewards",
		ISO_CODE_MY:   "my-tasker-brewards",
	}
	TASKER_BNPL_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "vn-tasker-bnpl",
		ISO_CODE_TH:   "th-tasker-bnpl",
		ISO_CODE_INDO: "id-tasker-bnpl",
		ISO_CODE_MY:   "my-tasker-bnpl",
	}
	REWARDS_ERROR_TRACKING_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "rewards-error-tracking",
		ISO_CODE_TH:   "th-rewards-error-tracking",
		ISO_CODE_INDO: "id-rewards-error-tracking",
		ISO_CODE_MY:   "my-rewards-error-tracking",
	}
	SLACK_CHANNEL_INDUSTRIAL_CLEANING = map[string]string{
		ISO_CODE_VN:   "vn-industrial-cleaining",
		ISO_CODE_TH:   "th-industrial-cleaining",
		ISO_CODE_INDO: "id-industrial-cleaining",
		ISO_CODE_MY:   "my-industrial-cleaining",
	}
	REPORT_TRANSACTION_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "vn-asker-report-transaction",
		ISO_CODE_TH:   "th-asker-report-transaction",
		ISO_CODE_INDO: "id-asker-report-transaction",
		ISO_CODE_MY:   "my-asker-report-transaction",
	}
	BUSINESS_PARTNERSHIP_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "vn-business-partnership",
		ISO_CODE_TH:   "th-business-partnership",
		ISO_CODE_INDO: "id-business-partnership",
		ISO_CODE_MY:   "my-business-partnership",
	}
	OFFICE_CLEANING_FAILED_PAYMENT = map[string]string{
		ISO_CODE_TH: "th-office-cleaning-failed-payment",
	}
	COMBO_VOUCHER_SUBSCRIPTION_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN:   "subscription-combo-voucher",
		ISO_CODE_TH:   "subscription-combo-voucher",
		ISO_CODE_INDO: "subscription-combo-voucher",
		ISO_CODE_MY:   "subscription-combo-voucher",
	}
	TASKER_CLICK_START_END_BUTTON_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN: "vn-tasker-click-start-end-button",
	}
	COMMUNITY_REPORTED_SLACK_CHANNEL = map[string]string{
		ISO_CODE_VN: "asker-community-post-alerts",
	}
)
