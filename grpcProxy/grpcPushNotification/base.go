package grpcPushNotification

import (
	"context"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPushNotificationIndo"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPushNotificationMY"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPushNotificationTH"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPushNotificationVN"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	"google.golang.org/grpc"
)

type PushNotificationClient interface {
	// TestFunc(ctx context.Context, in *pushNotificationRequest.PushNotificationRequest, opts ...grpc.CallOption) (*response.Response, error)
	Send(ctx context.Context, in *pushNotificationRequest.PushNotificationRequest, opts ...grpc.CallOption) (*response.Response, error)
}

func Connect(isoCode, endpoint string) (PushNotificationClient, *grpc.ClientConn, error) {
	switch isoCode {
	case globalConstant.ISO_CODE_VN:
		return ConnectGRPCPushNotificationVN(endpoint)
	case globalConstant.ISO_CODE_TH:
		return ConnectGRPCPushNotificationTH(endpoint)
	case globalConstant.ISO_CODE_INDO:
		return ConnectGRPCPushNotificationINDO(endpoint)
	case globalConstant.ISO_CODE_MY:
		return ConnectGRPCPushNotificationMY(endpoint)
	}
	return nil, nil, nil
}

func ConnectGRPCPushNotificationVN(endpoint string) (grpcPushNotificationVN.PushNotificationVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPushNotificationVN.NewPushNotificationVNClient(connect)
	return client, connect, err
}

func ConnectGRPCPushNotificationTH(endpoint string) (grpcPushNotificationTH.PushNotificationTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPushNotificationTH.NewPushNotificationTHClient(connect)
	return client, connect, err
}

func ConnectGRPCPushNotificationINDO(endpoint string) (grpcPushNotificationIndo.PushNotificationIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPushNotificationIndo.NewPushNotificationIndoClient(connect)
	return client, connect, err
}

func ConnectGRPCPushNotificationMY(endpoint string) (grpcPushNotificationIndo.PushNotificationIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPushNotificationMY.NewPushNotificationClient(connect)
	return client, connect, err
}
