/*
 * @File: main.go
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 01/12/2020
 * @UpdatedBy: ngoctb3
 */
package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"gitlab.com/btaskee/go-email-vn-v3/config"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-email-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"google.golang.org/grpc"
)

var cfg = config.GetConfig()

func main() {
	// Create new server
	grpcServer := service.NewGRPCServer()

	// Start GRPC server
	go func() {
		err := service.StartGRPCServer(grpcServer, cfg.GRPC_Port)
		if err != nil {
			log.Println(err.Error())
		}
	}()

	// Start HTTP Server
	go func() {
		err := service.StartRESTServer(cfg.REST_Port, cfg.GRPC_Port)
		if err != nil {
			log.Println(err.Error())
		}
	}()

	// Graceful shutdown
	handleSigterm(grpcServer)
}

func handleSigterm(grpcServer *grpc.Server) {
	globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[SERVICE RUNNING] - %s", local.SERVICE_NAME))
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)
	signal.Notify(c, syscall.SIGTERM)
	<-c

	// Shutdown GRPC server
	grpcServer.GracefulStop()

	globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[SERVICE DOWN] - %s", local.SERVICE_NAME))

	close(c)
}
