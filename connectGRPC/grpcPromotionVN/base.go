package grpcPromotionVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPromotionVN"
	"google.golang.org/grpc"
)

func ConnectGRPCPromotionVN(endpoint string) (grpcPromotionVN.PromotionVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPromotionVN.NewPromotionVNClient(connect)
	return client, connect, err
}
