package createAskerMonthlyReport

import (
	"fmt"
	"sync"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccessV2"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getAskerCh(w *sync.WaitGroup, reqBody *model.RequestAction, startAMonth time.Time) chan *modelUser.Users {
	var page int64 = 1
	var errCount, totalAsker, doneCount int
	askerCh := make(chan *modelUser.Users, LIMIT)
	var runUsers []string
	if reqBody != nil {
		runUsers = reqBody.RunUsers
	}
	go func() {
		defer w.Done()
		// Close channel khi đã get xong data users
		defer close(askerCh)
		for {
			// Get data users theo page tăng gần
			askersData, err := getAskers(page, runUsers)
			if err == nil {
				for _, user := range askersData {
					if globalLib.ParseDateFromTimeStamp(user.LastDoneTask, local.TimeZone).Before(startAMonth) && globalLib.ParseDateFromTimeStamp(user.LastOnline, local.TimeZone).Before(startAMonth) {
						continue
					}
					askerCh <- user
				}
				if len(askersData) > 0 {
					totalAsker += len(askersData)
					// tăng số page lên +1
					page++
					errCount = 0
					doneCount = 0
					if totalAsker%10000 == 0 {
						msg := fmt.Sprintf("CreateAskerMonthlyReport%s Đã chạy xong monthly report cho %d asker", local.ISO_CODE, totalAsker)
						globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
					}
				} else {
					msg := fmt.Sprintf("CreateAskerMonthlyReport%s data trả về danh sách asker 0 - page:%v", local.ISO_CODE, page)
					globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
					doneCount++
					if doneCount >= 2 {
						msg := fmt.Sprintf("CreateAskerMonthlyReport%s đã chạy xong monthly report cho %d asker", local.ISO_CODE, totalAsker)
						globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
						// Break for khi đã lấy đủ data
						break
					}
				}
			} else {
				errCount++
				if errCount >= 5 {
					errCount = 0
					msg := fmt.Sprintf("CreateAskerMonthlyReportVN Get data asker for monthly report - page:%v error: %v", page, err)
					globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
					break
				}
			}
		}
	}()
	// Trả về channel userCh
	return askerCh
}

func getAskers(page int64, userIds []string) ([]*modelUser.Users, error) {
	// Get data askers
	query := bson.M{
		"type":        globalConstant.USER_TYPE_ASKER,
		"countryCode": globalConstant.COUNTRY_CODE_VN,
		"status":      bson.M{"$in": []string{globalConstant.USER_STATUS_ACTIVE, globalConstant.USER_STATUS_DISABLED}},
	}
	if len(userIds) > 0 {
		query = bson.M{
			"_id": bson.M{"$in": userIds},
		}
	}
	askers, err := modelUser.GetAll(local.ISO_CODE,
		query,
		bson.M{"_id": 1, "resetRankHistory": 1, "referralCode": 1, "lastDoneTask": 1, "lastOnline": 1},
		&globalDataAccessV2.QueryOptions{Page: page, Limit: LIMIT, Sort: bson.M{"createdAt": -1}},
	)
	return askers, err
}
