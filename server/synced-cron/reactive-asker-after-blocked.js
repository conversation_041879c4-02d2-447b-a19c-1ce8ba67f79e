import { Meteor } from 'meteor/meteor';
import {
  revertTimeZone,
  trackingScheduleStatus,
  sendNotificationByIds,
} from '/server/helper-function';
import moment from 'moment';

const runReactiveAskerAfterBlocked = () => {
  return;
	console.log("Start runReactiveAskerAfterBlocked ...");
  let message = null;
  try {
    const users = Meteor.appCollection.users.find({ status: 'DISABLED', cancelBlockedAt: { $exists: true } }, { fields: { _id: 1, cancelBlockedAt: 1 } }).fetch();
    if (users && users.length > 0) {
      // Get users are blocked >= 24h
      const filterUsers = users.filter((item) => {
        const date = moment().subtract(24, 'h').toDate();
        return moment(date).isSameOrAfter(item.cancelBlockedAt);
      });
      // Reactive users
      const ids = filterUsers.map((item) => (item._id));
      if (ids && ids.length > 0) {
        Meteor.appCollection.users.update({ _id: { $in: ids }}, { $set: { status: 'ACTIVE' }, $unset: { cancelBlockedAt: 1 }}, { multi: true });
        sendNotificationByIds(ids, {
          title: {
            vi: i18n.getTranslation('NOW_CAN_POST_TASK', { _locale: 'vi' }),
            th: i18n.getTranslation('NOW_CAN_POST_TASK', { _locale: 'th' }),
            ko: i18n.getTranslation('NOW_CAN_POST_TASK', { _locale: 'ko' }),
            en: i18n.getTranslation('NOW_CAN_POST_TASK', { _locale: 'en' }),
          },
          text: {
            vi: i18n.getTranslation('NOW_CAN_POST_TASK', { _locale: 'vi' }),
            th: i18n.getTranslation('NOW_CAN_POST_TASK', { _locale: 'th' }),
            ko: i18n.getTranslation('NOW_CAN_POST_TASK', { _locale: 'ko' }),
            en: i18n.getTranslation('NOW_CAN_POST_TASK', { _locale: 'en' }),
          },
          payload: {
            type: 25,
            taskId: '',
          },
        }, { isForceView: true });
      }
    }
  } catch (e) {
    message = e;
  }
	console.log("Finished runReactiveAskerAfterBlocked ");
  // Tracking
  trackingScheduleStatus({ name: 'ReactiveAskerAfterBlocked', nextRunAt: SyncedCron.nextScheduledAtDate('reactiveAskerAfterBlocked'), message });
};

  Meteor.methods({
    // Auto Done Task
    reactiveAskerAfterBlocked() {
      SyncedCron.add({
        name: 'reactiveAskerAfterBlocked',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 1)).getHours();
          return parser.cron(`0 15 ${hourInTimeZoneServer}/12 1/1 * ? *`, true);   // Run at 01:15 and 13:15.
        },
        job() {
          runReactiveAskerAfterBlocked();
        },
      });
	    console.log("Added reactiveAskerAfterBlocked cron");
    },
    'runNow.reactiveAskerAfterBlocked'() {
      runReactiveAskerAfterBlocked();
    },
  });
