import { Meteor } from 'meteor/meteor';
import _ from 'lodash';
import {
  trackingScheduleStatus,
} from '/server/helper-function';

const initTaskerLevel = function () {
  return;
	console.log("Start initTaskerLevel ...");
  const levelList = _.get(Meteor.appCollection.SettingSystem.findOne(), 'taskerLevel', []);
  Meteor.appCollection.users.find({
    type: 'TASKER',
    status: { $nin: ['LOCKED', 'UNVERIFIED', 'INACTIVE'] },
  }).forEach((tasker) => {
    const nDoneTask = _.get(tasker, 'taskDone', 0);
    const nDoneTaskInMonth = _.get(tasker, 'nDoneTaskInMonth', 0);
    const previousLevel = _.get(tasker, 'level.level', 1);
    let level = 1;
    levelList.forEach((levelItem, index) => {
      if (nDoneTask >= levelItem.task && nDoneTaskInMonth >= levelItem.retainTask) {
        level = index + 1;
      }
    });
    Meteor.appCollection.users.update({ _id: tasker._id }, {
      $set: {
        level: {
          previousLevel,
          level,
          calculateAt: new Date(),
        },
      },
    });
  });
	console.log("Finished initTaskerLevel"); 
};

const calculateTaskerLevel = function () {
  return;
	console.log("Start calculateTaskerLevel");
  var message = null;
  try {
    const levelList = _.get(Meteor.appCollection.SettingSystem.findOne(), 'taskerLevel', []);
    Meteor.appCollection.users.find({
      type: 'TASKER',
      status: { $nin: ['LOCKED', 'UNVERIFIED', 'INACTIVE'] },
    }).forEach((tasker) => {
      const nDoneTask = _.get(tasker, 'taskDone', 0);
      const nDoneTaskInMonth = _.get(tasker, 'nDoneTaskInMonth', 0);
      const previousLevel = _.get(tasker, 'level.level', 1);
      let level = 1;
      levelList.forEach((levelItem, index) => {
        if (nDoneTask >= levelItem.task && nDoneTaskInMonth >= levelItem.retainTask) {
          level = index + 1;
        }
      });
      Meteor.appCollection.users.update({ _id: tasker._id }, {
        $set: {
          level: {
            previousLevel,
            level,
            calculateAt: new Date(),
          },
        },
      });
    });
  } catch (e) {
    message = e;
  }
	console.log("Finished calculateTaskerLevel");
  trackingScheduleStatus({name: 'CalculateTaskerLevel', nextRunAt: SyncedCron.nextScheduledAtDate('calculateTaskerLevel'), message});
};

  Meteor.methods({
    // Auto Done Task
    calculateTaskerLevel() {
      SyncedCron.add({
        name: 'calculateTaskerLevel',
        schedule(parser) {
          // Run at 00:01 in the first day of the month
          return parser.cron('1 0 0 1 1/1 ? *', true);
        },
        job() {
          calculateTaskerLevel();
        },
      });
	console.log("Added calculateTaskerLevel cron.");
    },
    'runNow.calculateTaskerLevel'() {
      calculateTaskerLevel();
    },
    initTaskerLevel() {
      initTaskerLevel();
    },
  });
