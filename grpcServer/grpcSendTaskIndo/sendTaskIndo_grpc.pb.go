// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcSendTaskIndo

import (
	context "context"
	pushNotificationNewTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// SendTaskIndoClient is the client API for SendTaskIndo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SendTaskIndoClient interface {
	NewTask(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	TopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	NewTaskToDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	FavTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	Normal(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	FavAndTaskerDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	FavAndTopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	NewTaskToCity(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	ForceTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
}

type sendTaskIndoClient struct {
	cc grpc.ClientConnInterface
}

func NewSendTaskIndoClient(cc grpc.ClientConnInterface) SendTaskIndoClient {
	return &sendTaskIndoClient{cc}
}

func (c *sendTaskIndoClient) NewTask(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskIndo.SendTaskIndo/NewTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendTaskIndoClient) TopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskIndo.SendTaskIndo/TopTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendTaskIndoClient) NewTaskToDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskIndo.SendTaskIndo/NewTaskToDistrict", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendTaskIndoClient) FavTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskIndo.SendTaskIndo/FavTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendTaskIndoClient) Normal(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskIndo.SendTaskIndo/Normal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendTaskIndoClient) FavAndTaskerDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskIndo.SendTaskIndo/FavAndTaskerDistrict", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendTaskIndoClient) FavAndTopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskIndo.SendTaskIndo/FavAndTopTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendTaskIndoClient) NewTaskToCity(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskIndo.SendTaskIndo/NewTaskToCity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendTaskIndoClient) ForceTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskIndo.SendTaskIndo/ForceTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SendTaskIndoServer is the server API for SendTaskIndo service.
// All implementations must embed UnimplementedSendTaskIndoServer
// for forward compatibility
type SendTaskIndoServer interface {
	NewTask(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	TopTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	NewTaskToDistrict(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	FavTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	Normal(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	FavAndTaskerDistrict(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	FavAndTopTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	NewTaskToCity(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	ForceTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	mustEmbedUnimplementedSendTaskIndoServer()
}

// UnimplementedSendTaskIndoServer must be embedded to have forward compatible implementations.
type UnimplementedSendTaskIndoServer struct {
}

func (UnimplementedSendTaskIndoServer) NewTask(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewTask not implemented")
}
func (UnimplementedSendTaskIndoServer) TopTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TopTasker not implemented")
}
func (UnimplementedSendTaskIndoServer) NewTaskToDistrict(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewTaskToDistrict not implemented")
}
func (UnimplementedSendTaskIndoServer) FavTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FavTasker not implemented")
}
func (UnimplementedSendTaskIndoServer) Normal(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Normal not implemented")
}
func (UnimplementedSendTaskIndoServer) FavAndTaskerDistrict(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FavAndTaskerDistrict not implemented")
}
func (UnimplementedSendTaskIndoServer) FavAndTopTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FavAndTopTasker not implemented")
}
func (UnimplementedSendTaskIndoServer) NewTaskToCity(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewTaskToCity not implemented")
}
func (UnimplementedSendTaskIndoServer) ForceTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ForceTasker not implemented")
}
func (UnimplementedSendTaskIndoServer) mustEmbedUnimplementedSendTaskIndoServer() {}

// UnsafeSendTaskIndoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SendTaskIndoServer will
// result in compilation errors.
type UnsafeSendTaskIndoServer interface {
	mustEmbedUnimplementedSendTaskIndoServer()
}

func RegisterSendTaskIndoServer(s *grpc.Server, srv SendTaskIndoServer) {
	s.RegisterService(&_SendTaskIndo_serviceDesc, srv)
}

func _SendTaskIndo_NewTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskIndoServer).NewTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskIndo.SendTaskIndo/NewTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskIndoServer).NewTask(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendTaskIndo_TopTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskIndoServer).TopTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskIndo.SendTaskIndo/TopTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskIndoServer).TopTasker(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendTaskIndo_NewTaskToDistrict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskIndoServer).NewTaskToDistrict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskIndo.SendTaskIndo/NewTaskToDistrict",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskIndoServer).NewTaskToDistrict(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendTaskIndo_FavTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskIndoServer).FavTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskIndo.SendTaskIndo/FavTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskIndoServer).FavTasker(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendTaskIndo_Normal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskIndoServer).Normal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskIndo.SendTaskIndo/Normal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskIndoServer).Normal(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendTaskIndo_FavAndTaskerDistrict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskIndoServer).FavAndTaskerDistrict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskIndo.SendTaskIndo/FavAndTaskerDistrict",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskIndoServer).FavAndTaskerDistrict(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendTaskIndo_FavAndTopTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskIndoServer).FavAndTopTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskIndo.SendTaskIndo/FavAndTopTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskIndoServer).FavAndTopTasker(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendTaskIndo_NewTaskToCity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskIndoServer).NewTaskToCity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskIndo.SendTaskIndo/NewTaskToCity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskIndoServer).NewTaskToCity(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendTaskIndo_ForceTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskIndoServer).ForceTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskIndo.SendTaskIndo/ForceTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskIndoServer).ForceTasker(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _SendTaskIndo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcSendTaskIndo.SendTaskIndo",
	HandlerType: (*SendTaskIndoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "NewTask",
			Handler:    _SendTaskIndo_NewTask_Handler,
		},
		{
			MethodName: "TopTasker",
			Handler:    _SendTaskIndo_TopTasker_Handler,
		},
		{
			MethodName: "NewTaskToDistrict",
			Handler:    _SendTaskIndo_NewTaskToDistrict_Handler,
		},
		{
			MethodName: "FavTasker",
			Handler:    _SendTaskIndo_FavTasker_Handler,
		},
		{
			MethodName: "Normal",
			Handler:    _SendTaskIndo_Normal_Handler,
		},
		{
			MethodName: "FavAndTaskerDistrict",
			Handler:    _SendTaskIndo_FavAndTaskerDistrict_Handler,
		},
		{
			MethodName: "FavAndTopTasker",
			Handler:    _SendTaskIndo_FavAndTopTasker_Handler,
		},
		{
			MethodName: "NewTaskToCity",
			Handler:    _SendTaskIndo_NewTaskToCity_Handler,
		},
		{
			MethodName: "ForceTasker",
			Handler:    _SendTaskIndo_ForceTasker_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sendTaskIndo.proto",
}
