// import { Meteor } from 'meteor/meteor';
// import moment from 'moment';
// import _ from 'lodash';
// import {
//   revertTimeZone,
//   trackingScheduleStatus,
// } from '/server/helper-function';

// const THRESHOLD = 900000;
// const REMAIN = 400000;

// const findTaskerNotInSubscription = function (subscriptionTaskerIds, testerPhones) {
//   // Begin date of last week
//   const beginLastWeek = moment().subtract(7, 'days').startOf('isoweek').toDate();
//   // Begin date of last 2 weeks
//   const beginLast2Week = moment().subtract(14, 'days').startOf('isoweek').toDate();
//   // Find financial account have main account > 900K
//   const fAccountIds = Meteor.appCollection.FinancialAccount.find({ FMainAccount: {$gt: THRESHOLD} }).map((fa) => (fa._id));
//   // Find user ids deposit in last 2 weeks
//   const userDeposit2Week = Meteor.appCollection.FATransaction.find({ amount: {$gte: 1000000}, type: "D", accountType: "M", date: {$gte: beginLast2Week} }).map((fat) => (fat.userId));
//   // Find users in bellow filter
//   return Meteor.appCollection.users.find({
//     type: 'TASKER',
//     status: 'ACTIVE',
//     fAccountId: {$in: fAccountIds},
//     _id: {$nin: _.uniq(subscriptionTaskerIds.concat(userDeposit2Week))}, // Concat user not in subscription and not deposit > 1M in 2 weeks
//     phone: {$nin: testerPhones},
//   }, {fields: {_id: 1}}).map((tasker) => (tasker._id));
// }

// const runPayoutLastWeek = function() {
//   // Begin date of last week
//   const beginLastWeek = moment().subtract(7, 'days').startOf('isoweek').toDate();
//   // End date of last week
//   const endLastWeek = moment().subtract(7, 'days').endOf('isoweek').toDate();
//   // Begin date of this week
//   const beginThisWeek = moment().startOf('isoweek').toDate();

//   // Find exists payout was paid last week
//   const findPaidOut = Meteor.appCollection.WeeklyPayout.findOne({
//     createdAt: {
//       $gte: beginLastWeek,
//       $lte: endLastWeek,
//     },
//     status: 'PAID',
//   });
//   if (findPaidOut) {
//     throw new Meteor.Error('PAID_IN_LAST_WEEK', 'Paid in last week');
//   }

//   const existsPayout =  Meteor.appCollection.WeeklyPayout.findOne({
//     createdAt: {
//       $gte: beginLastWeek,
//       $lte: endLastWeek,
//     }
//   });

//   // Remove old payout was created in last week
//   Meteor.appCollection.WeeklyPayout.remove({
//     createdAt: {
//       $gte: beginLastWeek,
//       $lte: endLastWeek,
//     },
//     status: 'WAIT',
//   });

//   // Insert the new payout
//   const taskerSupscription = _.uniq( Meteor.appCollection.Subscription.find({ forceAcceptTaskerId: { $ne: null } }).map((subscription) => (subscription.forceAcceptTaskerId)) );
//   const testerPhones = Meteor.appCollection.SettingSystem.findOne({}).tester || [];
//   // Find Taskers valid and not in subscription
//   const taskerIdsNotInSub = findTaskerNotInSubscription(taskerSupscription, testerPhones);

//   Meteor.appCollection.users.find({
//     _id: { $in: taskerSupscription.concat(taskerIdsNotInSub) }, // Concat 2 array subscription and not in subscription
//     phone: { $nin: testerPhones }
//   }).forEach((tasker) => {
//     var mainAccount = _.get(Meteor.appCollection.FinancialAccount.findOne(tasker.fAccountId), 'FMainAccount', 0);
//     Meteor.appCollection.FATransaction.find({
//       userId: tasker._id,
//       date: {$gte: beginThisWeek},
//       accountType: 'M',
//       'source.name': {$ne: 'WEEKLY_PAYOUT'},
//     }).forEach((trans) => {
//       if (trans.type === 'D') {
//         mainAccount -= trans.amount;
//       } else if (trans.type === 'C') {
//         mainAccount += trans.amount;
//       }
//     });

//     // Update main account with reverse changed from this week begining.
//     if (mainAccount > THRESHOLD) { // payout
//       Meteor.appCollection.WeeklyPayout.insert({
//         taskerId: tasker._id,
//         name: tasker.name,
//         phone: tasker.phone,
//         amount: Math.floor((mainAccount - REMAIN) / 10000) * 10000,
//         status: 'WAIT',
//         createdAt: existsPayout && existsPayout.createdAt ? existsPayout.createdAt : new Date(),
//       });
//     }
//   });
// }

// const weeklyPayout = function () {
//   var message = null;
//   try {
//     const taskerSupscription = _.uniq( Meteor.appCollection.Subscription.find({ forceAcceptTaskerId: { $ne: null } }).map((subscription) => (subscription.forceAcceptTaskerId)) );
//     // remove other payout in week
//     Meteor.appCollection.WeeklyPayout.remove({
//       createdAt: {
//       	$gte: moment().startOf('isoweek').toDate(),// >= begin this week
//       	$lte: moment().endOf('isoweek').toDate(),  // <= end this week
//       }
//     });
//     // Find tester phone for filter
//     const testerPhones = Meteor.appCollection.SettingSystem.findOne({}).tester || [];
//     // Find Taskers valid and not in subscription
//     const taskerIdsNotInSub = findTaskerNotInSubscription(taskerSupscription, testerPhones);

//     // Find financial account have main > THRESHOLD for filter
//     const fAccountValidIds = Meteor.appCollection.FinancialAccount.find({ FMainAccount: {$gt: THRESHOLD} }).map((fa) => {
//       return fa._id;
//     });
//     // create this week payout
//     Meteor.appCollection.users.find({
//       fAccountId: {$in: fAccountValidIds},
//       _id: { $in: taskerSupscription.concat(taskerIdsNotInSub) },
//       phone: { $nin: testerPhones }
//     }).forEach((tasker) => {
//       const mainAccount = _.get(Meteor.appCollection.FinancialAccount.findOne(tasker.fAccountId), 'FMainAccount', 0);
//     	Meteor.appCollection.WeeklyPayout.insert({
//     	  taskerId: tasker._id,
//     	  name: tasker.name,
//     	  phone: tasker.phone,
//     	  amount: Math.floor((mainAccount - REMAIN) / 10000) * 10000,
//     	  status: 'WAIT',
//     	  createdAt: new Date(),
//     	});
//     });
//   } catch (e) {
//     message = e;
//   }
//   // Tracking
//   trackingScheduleStatus({name: 'TaskerWeeklyPayout', nextRunAt: SyncedCron.nextScheduledAtDate('weeklyPayout'), message});
// };

// export default function () {
//   Meteor.methods({
//     weeklyPayout() {
//       SyncedCron.add({
// 	name: 'weeklyPayout',
// 	schedule(parser) {
// 	  // at 02:15 Sunday weekly
// 	  return parser.cron('0 15 2 ? * SUN *', true);
// 	},
// 	job() {
// 	  weeklyPayout();
// 	}
//       });
//     },
//     'runNow.weeklyPayout'() {
//       runPayoutLastWeek();
//     },
//   });
// }
