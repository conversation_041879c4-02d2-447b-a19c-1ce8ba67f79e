// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcCancelTaskTH

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	cancelBookingRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/cancelBookingRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// CancelTaskTHClient is the client API for CancelTaskTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CancelTaskTHClient interface {
	BackendCancelTask(ctx context.Context, in *cancelBookingRequest.CancelBookingRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type cancelTaskTHClient struct {
	cc grpc.ClientConnInterface
}

func NewCancelTaskTHClient(cc grpc.ClientConnInterface) CancelTaskTHClient {
	return &cancelTaskTHClient{cc}
}

func (c *cancelTaskTHClient) BackendCancelTask(ctx context.Context, in *cancelBookingRequest.CancelBookingRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcCancelTaskTH.CancelTaskTH/BackendCancelTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CancelTaskTHServer is the server API for CancelTaskTH service.
// All implementations must embed UnimplementedCancelTaskTHServer
// for forward compatibility
type CancelTaskTHServer interface {
	BackendCancelTask(context.Context, *cancelBookingRequest.CancelBookingRequest) (*empty.Empty, error)
	mustEmbedUnimplementedCancelTaskTHServer()
}

// UnimplementedCancelTaskTHServer must be embedded to have forward compatible implementations.
type UnimplementedCancelTaskTHServer struct {
}

func (UnimplementedCancelTaskTHServer) BackendCancelTask(context.Context, *cancelBookingRequest.CancelBookingRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BackendCancelTask not implemented")
}
func (UnimplementedCancelTaskTHServer) mustEmbedUnimplementedCancelTaskTHServer() {}

// UnsafeCancelTaskTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CancelTaskTHServer will
// result in compilation errors.
type UnsafeCancelTaskTHServer interface {
	mustEmbedUnimplementedCancelTaskTHServer()
}

func RegisterCancelTaskTHServer(s *grpc.Server, srv CancelTaskTHServer) {
	s.RegisterService(&_CancelTaskTH_serviceDesc, srv)
}

func _CancelTaskTH_BackendCancelTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cancelBookingRequest.CancelBookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CancelTaskTHServer).BackendCancelTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcCancelTaskTH.CancelTaskTH/BackendCancelTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CancelTaskTHServer).BackendCancelTask(ctx, req.(*cancelBookingRequest.CancelBookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _CancelTaskTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcCancelTaskTH.CancelTaskTH",
	HandlerType: (*CancelTaskTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BackendCancelTask",
			Handler:    _CancelTaskTH_BackendCancelTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cancel-task-th.proto",
}
