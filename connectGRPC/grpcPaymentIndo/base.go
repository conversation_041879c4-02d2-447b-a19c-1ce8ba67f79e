package grpcPaymentIndo

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPaymentIndo"
	"google.golang.org/grpc"
)

func ConnectGRPCPayment(endpoint string) (grpcPaymentIndo.PaymentIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPaymentIndo.NewPaymentIndoClient(connect)
	return client, connect, err
}
