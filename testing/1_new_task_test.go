package testing

// import (
// 	"testing"
// )

/*
	* <PERSON><PERSON> tả:
		- Gửi notification new task tới tasker:
			+ Nằm trong user.FavouriteTaskers
			+ Nằm trong serviceChannel.TaskerLists
			+ Có status = ACTIVE
			+ Có workingPlaces.city = task.TaskPlace.City
			+ Không nằm trong task.BlackList
		- Nếu tasker.Company.CompanyId != "" -> Send tới company + tasker
		- <PERSON>ếu asker không có favouriteTasker:
			+ Nếu settings.UseDistanceToSendNotification == true -> Send to TopTasker
			+ Nếu settings.UseDistanceToSendNotification == false -> Send Normal
	* Kết quả:
		- Cập nhật task.Visibility = 1, viewedTaskers
*/

// func TestNewTask(t *testing.T) {

// }
