syntax = "proto3";
package grpcBPointMY;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcBPointMY";

import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response/response.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/bPointRequest/bPointRequest.proto";

service BPoint {
  rpc AccumulatePoints (bPointRequest.BPointRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/bpoint-my/accumulate"
      body: "*"
    };
  }
  rpc TaskerAccumulatePoints (bPointRequest.BPointRequest) returns (response.Response) {}
}