// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.21.9
// source: baseTH.proto

package grpcSendTaskTH

import (
	pushNotificationNewTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_baseTH_proto protoreflect.FileDescriptor

var file_baseTH_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x62, 0x61, 0x73, 0x65, 0x54, 0x48, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0e,
	0x67, 0x72, 0x70, 0x63, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x48, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x67, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65,
	0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f,
	0x70, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x2f, 0x70, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x49, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67,
	0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0xfb, 0x07, 0x0a, 0x19, 0x50, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x48, 0x12, 0x70,
	0x0a, 0x07, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x27, 0x2e, 0x70, 0x75, 0x73, 0x68,
	0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x54,
	0x61, 0x73, 0x6b, 0x2e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x22, 0x1d,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x2d, 0x74, 0x61, 0x73,
	0x6b, 0x2d, 0x74, 0x68, 0x2f, 0x6e, 0x65, 0x77, 0x2d, 0x74, 0x61, 0x73, 0x6b, 0x3a, 0x01, 0x2a,
	0x12, 0x74, 0x0a, 0x09, 0x54, 0x6f, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x12, 0x27, 0x2e,
	0x70, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x2e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x24, 0x22, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x73, 0x65, 0x6e, 0x64,
	0x2d, 0x74, 0x61, 0x73, 0x6b, 0x2d, 0x74, 0x68, 0x2f, 0x74, 0x6f, 0x70, 0x2d, 0x74, 0x61, 0x73,
	0x6b, 0x65, 0x72, 0x3a, 0x01, 0x2a, 0x12, 0x7a, 0x0a, 0x11, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73,
	0x6b, 0x54, 0x6f, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x12, 0x27, 0x2e, 0x70, 0x75,
	0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65,
	0x77, 0x54, 0x61, 0x73, 0x6b, 0x2e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22,
	0x22, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x2d, 0x74,
	0x61, 0x73, 0x6b, 0x2d, 0x74, 0x68, 0x2f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x3a,
	0x01, 0x2a, 0x12, 0x79, 0x0a, 0x09, 0x46, 0x61, 0x76, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x12,
	0x27, 0x2e, 0x70, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x2e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2f, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x29, 0x22, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x73, 0x65,
	0x6e, 0x64, 0x2d, 0x74, 0x61, 0x73, 0x6b, 0x2d, 0x74, 0x68, 0x2f, 0x66, 0x61, 0x76, 0x6f, 0x72,
	0x69, 0x74, 0x65, 0x2d, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x3a, 0x01, 0x2a, 0x12, 0x6d, 0x0a,
	0x06, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x12, 0x27, 0x2e, 0x70, 0x75, 0x73, 0x68, 0x4e, 0x6f,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73,
	0x6b, 0x2e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x22, 0x1b, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x2d, 0x74, 0x61, 0x73, 0x6b, 0x2d,
	0x74, 0x68, 0x2f, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x3a, 0x01, 0x2a, 0x12, 0x91, 0x01, 0x0a,
	0x14, 0x46, 0x61, 0x76, 0x41, 0x6e, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x44, 0x69, 0x73,
	0x74, 0x72, 0x69, 0x63, 0x74, 0x12, 0x27, 0x2e, 0x70, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x2e,
	0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12,
	0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x3c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x36, 0x22, 0x31, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x33, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x2d, 0x74, 0x61, 0x73, 0x6b, 0x2d, 0x74, 0x68,
	0x2f, 0x66, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x2d, 0x61, 0x6e, 0x64, 0x2d, 0x74, 0x61,
	0x73, 0x6b, 0x65, 0x72, 0x2d, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x3a, 0x01, 0x2a,
	0x12, 0x87, 0x01, 0x0a, 0x0f, 0x46, 0x61, 0x76, 0x41, 0x6e, 0x64, 0x54, 0x6f, 0x70, 0x54, 0x61,
	0x73, 0x6b, 0x65, 0x72, 0x12, 0x27, 0x2e, 0x70, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x2e, 0x4e,
	0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x37, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x31, 0x22, 0x2c, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x33, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x2d, 0x74, 0x61, 0x73, 0x6b, 0x2d, 0x74, 0x68, 0x2f,
	0x66, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x2d, 0x61, 0x6e, 0x64, 0x2d, 0x74, 0x6f, 0x70,
	0x2d, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x3a, 0x01, 0x2a, 0x12, 0x72, 0x0a, 0x0d, 0x4e, 0x65,
	0x77, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x6f, 0x43, 0x69, 0x74, 0x79, 0x12, 0x27, 0x2e, 0x70, 0x75,
	0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65,
	0x77, 0x54, 0x61, 0x73, 0x6b, 0x2e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e,
	0x22, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x2d, 0x74,
	0x61, 0x73, 0x6b, 0x2d, 0x74, 0x68, 0x2f, 0x63, 0x69, 0x74, 0x79, 0x3a, 0x01, 0x2a, 0x42, 0x43,
	0x5a, 0x41, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61,
	0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x61, 0x73,
	0x6b, 0x54, 0x48, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_baseTH_proto_goTypes = []interface{}{
	(*pushNotificationNewTask.NewTaskRequest)(nil), // 0: pushNotificationNewTask.NewTaskRequest
	(*response.Response)(nil),                      // 1: response.Response
}
var file_baseTH_proto_depIdxs = []int32{
	0, // 0: grpcSendTaskTH.PushNotificationNewTaskTH.NewTask:input_type -> pushNotificationNewTask.NewTaskRequest
	0, // 1: grpcSendTaskTH.PushNotificationNewTaskTH.TopTasker:input_type -> pushNotificationNewTask.NewTaskRequest
	0, // 2: grpcSendTaskTH.PushNotificationNewTaskTH.NewTaskToDistrict:input_type -> pushNotificationNewTask.NewTaskRequest
	0, // 3: grpcSendTaskTH.PushNotificationNewTaskTH.FavTasker:input_type -> pushNotificationNewTask.NewTaskRequest
	0, // 4: grpcSendTaskTH.PushNotificationNewTaskTH.Normal:input_type -> pushNotificationNewTask.NewTaskRequest
	0, // 5: grpcSendTaskTH.PushNotificationNewTaskTH.FavAndTaskerDistrict:input_type -> pushNotificationNewTask.NewTaskRequest
	0, // 6: grpcSendTaskTH.PushNotificationNewTaskTH.FavAndTopTasker:input_type -> pushNotificationNewTask.NewTaskRequest
	0, // 7: grpcSendTaskTH.PushNotificationNewTaskTH.NewTaskToCity:input_type -> pushNotificationNewTask.NewTaskRequest
	1, // 8: grpcSendTaskTH.PushNotificationNewTaskTH.NewTask:output_type -> response.Response
	1, // 9: grpcSendTaskTH.PushNotificationNewTaskTH.TopTasker:output_type -> response.Response
	1, // 10: grpcSendTaskTH.PushNotificationNewTaskTH.NewTaskToDistrict:output_type -> response.Response
	1, // 11: grpcSendTaskTH.PushNotificationNewTaskTH.FavTasker:output_type -> response.Response
	1, // 12: grpcSendTaskTH.PushNotificationNewTaskTH.Normal:output_type -> response.Response
	1, // 13: grpcSendTaskTH.PushNotificationNewTaskTH.FavAndTaskerDistrict:output_type -> response.Response
	1, // 14: grpcSendTaskTH.PushNotificationNewTaskTH.FavAndTopTasker:output_type -> response.Response
	1, // 15: grpcSendTaskTH.PushNotificationNewTaskTH.NewTaskToCity:output_type -> response.Response
	8, // [8:16] is the sub-list for method output_type
	0, // [0:8] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_baseTH_proto_init() }
func file_baseTH_proto_init() {
	if File_baseTH_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_baseTH_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_baseTH_proto_goTypes,
		DependencyIndexes: file_baseTH_proto_depIdxs,
	}.Build()
	File_baseTH_proto = out.File
	file_baseTH_proto_rawDesc = nil
	file_baseTH_proto_goTypes = nil
	file_baseTH_proto_depIdxs = nil
}
