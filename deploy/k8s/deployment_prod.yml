---
apiVersion: v1
kind: Service
metadata:
  name: go-sync-cron-vn-v3
  namespace: kong
  labels:
    app: go-sync-cron-vn-v3
spec:
  ports:
    - port: 80
      targetPort: 23100
      name: http
  selector:
    app: go-sync-cron-vn-v3

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: go-sync-cron-vn-v3
  namespace: kong
  labels:
    app: go-sync-cron-vn-v3
spec:
  replicas: 1
  selector:
    matchLabels:
      app: go-sync-cron-vn-v3
  template:
    metadata:
      labels:
        app: go-sync-cron-vn-v3
        log-label: "go-services"
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: go-services
                operator: NotIn
                values:
                - group-high-memory
                - group-websocket
      containers:
        - name: go-sync-cron-vn-v3
          image: btaskeehub/go-sync-cron-service:vn-3.2.9
          resources:
            requests:
              cpu: 100m
              memory: "256Mi"
            limits:
              # https://home.robusta.dev/blog/stop-using-cpu-limits
              # cpu: 500m
              memory: "512Mi"
          env:
            - name: TZ
              value: "7"
            - name: APPLICATION_MODE
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: APPLICATION_MODE
            - name: SLACK_TOKEN_VN
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: SLACK_TOKEN_VN
            - name: URBOX_APP_ID
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: URBOX_APP_ID
            - name: URBOX_APP_SECRET
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: URBOX_APP_SECRET
            - name: URBOX_URL_GET_LIST_GIFT
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: URBOX_URL_GET_LIST_GIFT
            - name: REDIS_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: REDIS_PASSWORD
          imagePullPolicy: "IfNotPresent"
          ports:
            - containerPort: 80
              name: http
      imagePullSecrets:
            - name: btaskeehub-registrykey
