package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/trainingTaskerV2"
	"go.mongodb.org/mongo-driver/bson"
)

func TestAutoFindTaskersHaveStarCanDoTestTraining(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		apiURL := "/api/v3/sync-cron-vn/auto-find-taskers-have-star-can-do-test-training"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567870",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 4.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
			},
			{
				"phone":     "0834567880",
				"name":      "Tasker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "HCM",
					},
					{
						"city": "DN",
					},
				},
			},
			{
				"phone":     "0834567890",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "HCM",
					},
					{
						"city": "DN",
					},
				},
			},
			{
				"phone":     "0834567860",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"status":    globalConstant.USER_STATUS_INACTIVE,
			},
			{
				"phone":     "0834567850",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"status":    globalConstant.USER_STATUS_BLOCKED,
			},
			{
				"phone":     "0834567840",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"status":    globalConstant.USER_STATUS_DISABLED,
			},
		})
		//Get Service
		serviceModel := GetService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1})
		UpdateServiceChannel(bson.M{"serviceId": serviceModel.XId},
			bson.M{
				"$set": bson.M{"taskerList": []string{"0834567870", "0834567880", "0834567890"}},
			},
		)

		CreateTrainingTaskerCourse([]map[string]interface{}{
			{
				"_id":                     "test1",
				"title":                   "bài test số 1",
				"description":             "bài test dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "TEST",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "ACTIVE",
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "pcZRQ6PqmjrAPe5gt",
						"name": "CLEANING",
					},
				},
				"cities":          []string{"HCM"},
				"displayPosition": "TRAINING_COURSE",
				"condition": map[string]interface{}{
					"byTasker": map[string]interface{}{
						"minimumStar": 3.9,
					},
				},
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},
			{
				"_id":                     "test2",
				"title":                   "bài test số 2",
				"description":             "bài test dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "TEST",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "ACTIVE",
				"cities":                  []string{"HCM"},
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "pcZRQ6PqmjrAPe5gt",
						"name": "CLEANING",
					},
				},
				"displayPosition": "TRAINING_COURSE",
				"condition": map[string]interface{}{
					"coursesMustBeCompleted": map[string]interface{}{
						"courseIds": []string{"test1"},
					},
				},
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},
			{
				"_id":                     "review1",
				"title":                   "bài review số 1",
				"description":             "bài review dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "REVIEW",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "ACTIVE",
				"cities":                  []string{"HCM"},
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "pcZRQ6PqmjrAPe5gt",
						"name": "CLEANING",
					},
				},
				"displayPosition": "TRAINING_COURSE",
				"condition": map[string]interface{}{
					"byTasker": map[string]interface{}{
						"minimumStar": 3.9,
					},
				},
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},
			{
				"_id":                     "test3",
				"title":                   "bài test số 3",
				"description":             "bài test dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "TEST",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "ACTIVE",
				"cities":                  []string{"HCM"},
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "pcZRQ6PqmjrAPe5gt",
						"name": "CLEANING",
					},
				},
				"displayPosition": "TRAINING_COURSE",
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},
		})
		CreateCourseStartDate([]map[string]interface{}{
			{
				"courseId":   "test1",
				"taskerId":   "0834567880",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -22),
				"deadlineIn": 30,
			},
		})
		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)
				UpdateServiceChannel(bson.M{"serviceId": serviceModel.XId},
					bson.M{
						"$unset": bson.M{"taskerList": 1},
					},
				)
				So(res.Code, ShouldEqual, 200)
				var coursesStartDate []*trainingTaskerV2.CourseStartDate
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TRAINING_TASKER_COURSE_START_DATE[local.ISO_CODE], bson.M{}, bson.M{}, &coursesStartDate)
				So(len(coursesStartDate), ShouldEqual, 6)
				So(coursesStartDate[0].CourseId, ShouldEqual, "test1")
				So(coursesStartDate[0].TaskerId, ShouldEqual, "0834567880")
				So(coursesStartDate[0].StartDate.AsTime().Day(), ShouldEqual, globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -22).Day())

				So(coursesStartDate[1].CourseId, ShouldEqual, "test1")
				So(coursesStartDate[1].TaskerId, ShouldEqual, "0834567890")
				So(coursesStartDate[1].StartDate.AsTime().Day(), ShouldEqual, globalLib.GetCurrentTime(local.TimeZone).Day())

				So(coursesStartDate[2].CourseId, ShouldEqual, "review1")
				So(coursesStartDate[2].TaskerId, ShouldEqual, "0834567880")
				So(coursesStartDate[2].StartDate.AsTime().Day(), ShouldEqual, globalLib.GetCurrentTime(local.TimeZone).Day())

				So(coursesStartDate[3].CourseId, ShouldEqual, "review1")
				So(coursesStartDate[3].TaskerId, ShouldEqual, "0834567890")
				So(coursesStartDate[3].StartDate.AsTime().Day(), ShouldEqual, globalLib.GetCurrentTime(local.TimeZone).Day())

				So(coursesStartDate[4].CourseId, ShouldEqual, "test3")
				So(coursesStartDate[4].TaskerId, ShouldEqual, "0834567880")
				So(coursesStartDate[4].StartDate.AsTime().Day(), ShouldEqual, globalLib.GetCurrentTime(local.TimeZone).Day())

				So(coursesStartDate[5].CourseId, ShouldEqual, "test3")
				So(coursesStartDate[5].TaskerId, ShouldEqual, "0834567890")
				So(coursesStartDate[5].StartDate.AsTime().Day(), ShouldEqual, globalLib.GetCurrentTime(local.TimeZone).Day())

			})
		})
	})
}
