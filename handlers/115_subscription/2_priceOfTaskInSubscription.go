package subscription

import (
	"time"

	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

// PricingItem represents a pricing item in the subscription
type PricingItem struct {
	Date       time.Time  `bson:"date" json:"date"`
	Duration   int        `bson:"duration" json:"duration"`
	CostDetail CostDetail `bson:"costDetail" json:"costDetail"`
}

// CostDetail represents the cost details
type CostDetail struct {
	BaseCost  float64                `bson:"baseCost" json:"baseCost"`
	Cost      float64                `bson:"cost" json:"cost"`
	FinalCost float64                `bson:"finalCost" json:"finalCost"`
	Duration  int                    `bson:"duration" json:"duration"`
	Reason    map[string]interface{} `bson:"reason,omitempty" json:"reason,omitempty"`
}

// Subscription represents a subscription document
type Subscription struct {
	ID         string        `bson:"_id" json:"_id"`
	Price      float64       `bson:"price" json:"price"`
	Duration   int           `bson:"duration" json:"duration"`
	Discount   float64       `bson:"discount" json:"discount"`
	Schedule   []time.Time   `bson:"schedule" json:"schedule"`
	CostDetail *CostDetail   `bson:"costDetail,omitempty" json:"costDetail,omitempty"`
	Pricing    []PricingItem `bson:"pricing,omitempty" json:"pricing,omitempty"`
}

// priceOfTaskInSubscription calculates the price for a task in subscription - STEP 4: Price Calculation
func priceOfTaskInSubscription(subs *Subscription, date time.Time) *PricingItem {
	remainRate := 1.0 - subs.Discount
	if remainRate <= 0 {
		remainRate = 1.0
	}

	// Check costDetail.pricing first
	if subs.CostDetail != nil && subs.Pricing != nil && len(subs.Pricing) > 0 {
		for _, pricing := range subs.Pricing {
			if isSameDateTime(pricing.Date, date) {
				if remainRate > 0 && remainRate < 1 {
					pricing.CostDetail.Cost = globalLib.RoundMoney(pricing.CostDetail.Cost*remainRate, "VND")
					pricing.CostDetail.FinalCost = globalLib.RoundMoney(pricing.CostDetail.FinalCost*remainRate, "VND")
				}
				return &pricing
			}
		}
	} else if subs.Pricing != nil && len(subs.Pricing) > 0 {
		// Check pricing array
		for _, pricing := range subs.Pricing {
			if isSameDateTime(pricing.Date, date) {
				if remainRate > 0 && remainRate < 1 {
					pricing.CostDetail.Cost = globalLib.RoundMoney(pricing.CostDetail.Cost*remainRate, "VND")
					if pricing.CostDetail.Reason == nil {
						pricing.CostDetail.Reason = make(map[string]interface{})
					}
					pricing.CostDetail.Reason["subscriptionDiscount"] = subs.Discount
				}
				return &pricing
			}
		}
	}

	// Default calculation
	price := globalLib.RoundMoney(subs.Price/float64(len(subs.Schedule)), "VND")
	return &PricingItem{
		Date:     date,
		Duration: subs.Duration,
		CostDetail: CostDetail{
			BaseCost:  globalLib.RoundMoney(price/remainRate, "VND"),
			Cost:      price,
			FinalCost: price,
			Duration:  subs.Duration,
		},
	}
}

// calculateSubscriptionTaskPrice calculates price for gRPC subscription model
func calculateSubscriptionTaskPrice(grpcSubs interface{}, date time.Time) *PricingItem {
	// For now, return a simple default pricing
	// This should be implemented based on business logic
	price := 100000.0 // Default price in VND

	return &PricingItem{
		Date:     date,
		Duration: 2, // Default 2 hours
		CostDetail: CostDetail{
			BaseCost:  price,
			Cost:      price,
			FinalCost: price,
			Duration:  2,
		},
	}
}

// isSameDateTime checks if two times are the same down to the minute
func isSameDateTime(t1, t2 time.Time) bool {
	return t1.Year() == t2.Year() &&
		t1.Month() == t2.Month() &&
		t1.Day() == t2.Day() &&
		t1.Hour() == t2.Hour() &&
		t1.Minute() == t2.Minute()
}
