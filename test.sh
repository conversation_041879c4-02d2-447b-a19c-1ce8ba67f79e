cover="-coverprofile=cover -coverpkg=./..."
export PUSH_NOTIFICATION_NEW_TASK_SERVICE_CONFIG="../config"
case $1 in
  "1")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover} -run NewTask
    else
      go test -v ./testing ${cover} -run NewTask/^$2$
    fi
    ;;
  "2")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover} -run FavouriteTasker
    else
      go test -v ./testing ${cover} -run FavouriteTasker/^$2$
    fi
    ;;
  "3")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover} -run Top_Tasker
    else
      go test -v ./testing ${cover} -run Top_Tasker/^$2$
    fi
    ;;
  "4")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover} -run District
    else
      go test -v ./testing ${cover} -run District/^$2$
    fi
    ;;
  "5")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover} -run Normal
    else
      go test -v ./testing ${cover} -run Normal/^$2$
    fi
    ;;
  "6")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover} -run IsTimeToSendNotification
    else
      go test -v ./testing ${cover} -run IsTimeToSendNotification/^$2$
    fi
    ;;
  "7")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover} -run City
    else
      go test -v ./testing ${cover} -run City/^$2$
    fi
    ;;
  "8")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover} -run FavAndTopTasker
    else
      go test -v ./testing ${cover} -run FavAndTopTasker/^$2$
    fi
    ;;
  *)
    go test -v ./testing -timeout 1h ${cover}
    ;;
esac