// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcPushNotificationMY

import (
	context "context"
	pushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PushNotificationClient is the client API for PushNotification service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PushNotificationClient interface {
	Send(ctx context.Context, in *pushNotificationRequest.PushNotificationRequest, opts ...grpc.CallOption) (*response.Response, error)
}

type pushNotificationClient struct {
	cc grpc.ClientConnInterface
}

func NewPushNotificationClient(cc grpc.ClientConnInterface) PushNotificationClient {
	return &pushNotificationClient{cc}
}

func (c *pushNotificationClient) Send(ctx context.Context, in *pushNotificationRequest.PushNotificationRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPushNotificationMY.PushNotification/Send", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PushNotificationServer is the server API for PushNotification service.
// All implementations must embed UnimplementedPushNotificationServer
// for forward compatibility
type PushNotificationServer interface {
	Send(context.Context, *pushNotificationRequest.PushNotificationRequest) (*response.Response, error)
	mustEmbedUnimplementedPushNotificationServer()
}

// UnimplementedPushNotificationServer must be embedded to have forward compatible implementations.
type UnimplementedPushNotificationServer struct {
}

func (UnimplementedPushNotificationServer) Send(context.Context, *pushNotificationRequest.PushNotificationRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Send not implemented")
}
func (UnimplementedPushNotificationServer) mustEmbedUnimplementedPushNotificationServer() {}

// UnsafePushNotificationServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PushNotificationServer will
// result in compilation errors.
type UnsafePushNotificationServer interface {
	mustEmbedUnimplementedPushNotificationServer()
}

func RegisterPushNotificationServer(s *grpc.Server, srv PushNotificationServer) {
	s.RegisterService(&_PushNotification_serviceDesc, srv)
}

func _PushNotification_Send_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationRequest.PushNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationServer).Send(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPushNotificationMY.PushNotification/Send",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationServer).Send(ctx, req.(*pushNotificationRequest.PushNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PushNotification_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcPushNotificationMY.PushNotification",
	HandlerType: (*PushNotificationServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Send",
			Handler:    _PushNotification_Send_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pushNotificationMY.proto",
}
