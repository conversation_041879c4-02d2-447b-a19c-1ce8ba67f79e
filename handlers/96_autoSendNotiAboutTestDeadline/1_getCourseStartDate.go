package autoSendNotiAboutTestDeadline

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTrainingV2 "gitlab.com/btaskee/go-services-model-v2/grpcmodel/trainingTaskerV2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// get all record ob course start date which is inserted yesterday to send notification about new test
func getCoursesStartDate() []*modelTrainingV2.CourseStartDate {
	var coursesStartDate []*modelTrainingV2.CourseStartDate
	var res []*modelTrainingV2.CourseStartDate
	query := bson.M{
		"deadlineIn": bson.M{"$gt": 0},
		"$or": []bson.M{
			{"isSentNotiDeadline": bson.M{"$exists": false}},
			{"isSentNotiDeadline": false},
		},
	}
	fields := bson.M{
		"courseId":   1,
		"taskerId":   1,
		"startDate":  1,
		"deadlineIn": 1,
	}

	coursesStartDate, err := handlers.GetCoursesStartDate(query, fields)
	if err != nil && err != mongo.ErrNoDocuments {
		text := fmt.Sprintf("AutoSendNotiAboutTestDeadline %s: Lỗi call API SendNotiAboutTestDeadline , err: %s ", local.ISO_CODE, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], text)
		return nil
	}
	for _, courseStartDate := range coursesStartDate {
		if courseStartDate.StartDate != nil {
			currentCourseStartDate := globalLib.ParseDateFromTimeStamp(courseStartDate.StartDate, local.TimeZone)
			if currentCourseStartDate.AddDate(0, 0, int(courseStartDate.DeadlineIn)).Before(globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, int(lib.NUMBER_OF_DATE_TO_DEADLINE))) {
				res = append(res, courseStartDate)
			}
		}
	}

	return res
}
