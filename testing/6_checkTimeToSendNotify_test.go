package testing

import (
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
)

func TestIsTimeToSendNotification(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		var result bool

		// Case setting == nil
		Convey("Check the result when setting == nil", t, func() {
			result = lib.IsTimeToSendNotification(nil)
			So(result, ShouldBeTrue)
		})

		// Case setting.sendNotificationConfig == nil
		Convey("Check the result when setting.sendNotificationConfig == nil", t, func() {
			result = lib.IsTimeToSendNotification(&settings.Settings{})
			So(result, ShouldBeTrue)
		})

		// Case setting.sendNotificationConfig.isEnable = false. Not use this config
		Convey("Check the result when  setting.sendNotificationConfig.isEnable = false", t, func() {
			result = lib.IsTimeToSendNotification(&settings.Settings{
				SendNotificationConfig: &settings.SendNotificationConfig{
					IsEnableStopNotification: false,
				},
			})
			So(result, ShouldBeTrue)
		})

		// Case not time to send notification
		Convey("Check the result when setting.sendNotificationConfig.isEnable = true, use setting in settingSystem", t, func() {
			now := globalLib.GetCurrentTime(local.TimeZone)
			result = lib.IsTimeToSendNotification(&settings.Settings{
				SendNotificationConfig: &settings.SendNotificationConfig{
					IsEnableStopNotification: true,
					StopNotificationFrom:     now.Add(-10 * time.Minute).Format("15:04"),
					StopNotificationTo:       now.Add(10 * time.Minute).Format("15:04"),
				},
			})
			So(result, ShouldBeFalse)
		})

		// Case time to send notification
		Convey("Check the result when setting.sendNotificationConfig.isEnable = true, use setting in settingSystem", t, func() {
			now := globalLib.GetCurrentTime(local.TimeZone)
			result = lib.IsTimeToSendNotification(&settings.Settings{
				SendNotificationConfig: &settings.SendNotificationConfig{
					IsEnableStopNotification: true,
					StopNotificationFrom:     now.Add(1 * time.Hour).Format("15:04"),
					StopNotificationTo:       now.Add(2 * time.Hour).Format("15:04"),
				},
			})
			So(result, ShouldBeTrue)
		})
	})
}
