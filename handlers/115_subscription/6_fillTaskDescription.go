package subscription

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

// fillTaskDescription finds the most recent task description for a phone number - STEP 6: Task Description Generation
func fillTaskDescription(phone string) string {
	if phone == "" {
		return ""
	}

	var task *task.Task
	query := bson.M{
		"phone": phone,
		"description": bson.M{
			"$nin": []interface{}{nil, ""},
		},
	}
	fields := bson.M{"description": 1}
	sort := bson.M{"createdAt": -1}

	globalDataAccess.GetOneByQuerySort(
		globalCollection.COLLECTION_TASK[local.ISO_CODE],
		query,
		fields,
		sort,
		&task,
	)

	if task == nil {
		return ""
	}

	return task.Description
}
