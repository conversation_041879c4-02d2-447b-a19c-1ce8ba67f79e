package subscription

import (
	"fmt"
	"log"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSubscription "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscription"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type Location struct {
	Lat float64 `bson:"lat" json:"lat"`
	Lng float64 `bson:"lng" json:"lng"`
}

// postSubscriptionTask creates and posts a new task from subscription - STEP 2: Task Creation & Posting
func postSubscriptionTask(subs *modelSubscription.Subscription, nextDate time.Time) float64 {
	createdAt := globalLib.GetCurrentTime(local.TimeZone)

	// Create basic task data as map for insertion
	taskData := bson.M{
		"_id":              globalLib.GenerateObjectId(),
		"serviceId":        getServiceID(subs),
		"date":             nextDate,
		"duration":         float64(subs.Duration),
		"address":          subs.Address,
		"lat":              subs.Location.Lat,
		"lng":              subs.Location.Lng,
		"askerId":          subs.UserId,
		"status":           globalConstant.TASK_STATUS_POSTED,
		"createdAt":        createdAt,
		"rated":            false,
		"subscriptionId":   subs.XId,
		"autoChooseTasker": true,
		"payment": bson.M{
			"method": globalConstant.PAYMENT_METHOD_BANK_TRANSFER,
		},
		"description": subs.Description,
		"isoCode":     "VN",
		"countryCode": getCountryCode(subs),
		"originCurrency": bson.M{
			"sign": "₫",
			"code": "VND",
		},
	}

	// Set service name if available
	if subs.ServiceName != "" {
		taskData["serviceName"] = mapServiceName(subs.ServiceName)
	}

	// Set task note if available
	if subs.TaskNote != "" {
		taskData["taskNote"] = subs.TaskNote
	}

	// Set cooking detail if available
	if subs.CookingDetail != nil {
		// Set eating time
		eatingTime := nextDate.Add(time.Duration(subs.Duration) * time.Hour)

		// Create cooking detail map for task
		cookingDetailMap := map[string]interface{}{
			"eatingTime": eatingTime,
		}

		// Copy existing cooking detail fields if needed
		taskData["cookingDetail"] = cookingDetailMap
	}

	// Set child care detail if available
	if subs.DetailChildCare != nil {
		taskData["detailChildCare"] = subs.DetailChildCare
	}

	// Calculate price
	priceOfTask := calculateSubscriptionTaskPrice(subs, nextDate)

	taskData["cost"] = priceOfTask.CostDetail.Cost
	taskPrice := priceOfTask.CostDetail.Cost

	if priceOfTask.CostDetail.FinalCost > 0 {
		taskPrice = priceOfTask.CostDetail.FinalCost
	}

	// Get user information
	user, err := getUserInfo(subs.UserId)
	if err != nil {
		log.Printf("Error getting user info: %v", err)
		return 0
	}

	// Set user-related fields
	setUserRelatedFields(taskData, user, subs)

	// Fill task description if empty
	if taskData["description"] == "" {
		description := fillTaskDescription(user.Phone)
		if description != "" {
			taskData["description"] = description
		}
	}

	// Set additional fields
	setAdditionalFields(taskData, subs)

	// Get service text if not available
	if subs.ServiceText == nil || subs.ServiceText.Vi == "" {
		serviceText := getServiceText(subs.ServiceId)
		if serviceText != "" {
			taskData["serviceText"] = serviceText
		}
	} else {
		taskData["serviceText"] = subs.ServiceText.Vi
	}

	// Insert task
	taskID, err := insertTask(taskData)
	if err != nil {
		log.Printf("Error inserting task: %v", err)
		return 0
	}

	// Update user's last posted task
	updateUserLastPostedTask(subs.UserId, createdAt)

	// Update subscription history
	updateSubscriptionHistory(subs.XId, taskID, nextDate)

	// Send notification to asker
	serviceTextStr := ""
	if serviceText, ok := taskData["serviceText"].(string); ok {
		serviceTextStr = serviceText
	}
	sendNotificationToAsker(taskID, subs.UserId, user.Language, serviceTextStr)

	// Handle force accept tasker or send to available taskers
	handleTaskerAssignment(subs, taskData, taskID, user)

	// Check if subscription is finished
	checkSubscriptionCompletion(subs, taskID)

	return taskPrice
}

// Helper functions

func getServiceID(subs *modelSubscription.Subscription) string {
	if subs.TaskServiceId != "" {
		return subs.TaskServiceId
	}
	return subs.ServiceId
}

func getCountryCode(subs *modelSubscription.Subscription) string {
	if subs.CountryCode != "" {
		return subs.CountryCode
	}
	return "+84"
}

func getUserInfo(userID string) (*users.Users, error) {
	var user users.Users
	query := bson.M{"_id": userID}
	fields := bson.M{
		"phone":           1,
		"name":            1,
		"taskNote":        1,
		"blackList":       1,
		"language":        1,
		"favouriteTasker": 1,
		"isBlacklist":     1,
	}

	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USERS, query, fields, &user)
	return &user, err
}

func setUserRelatedFields(taskData bson.M, user *users.Users, subs *modelSubscription.Subscription) {
	if user.BlackList != nil && len(user.BlackList) > 0 {
		taskData["blackList"] = user.BlackList
	}
	taskData["phone"] = user.Phone
	taskData["contactName"] = user.Name
	if user.TaskNote != "" {
		taskData["taskNote"] = user.TaskNote
	}
}

func setAdditionalFields(taskData bson.M, subs *modelSubscription.Subscription) {
	if subs.HomeType != "" {
		taskData["homeType"] = subs.HomeType
	}
	if subs.IsPremium {
		taskData["isPremium"] = subs.IsPremium
	}
	if subs.IsEco {
		taskData["isEco"] = subs.IsEco
	}
	if subs.Addons != nil && len(subs.Addons) > 0 {
		taskData["addons"] = subs.Addons
	}
	if subs.Pet != nil && len(subs.Pet) > 0 {
		taskData["pet"] = subs.Pet
	}
}

func getServiceText(serviceID string) string {
	var service bson.M
	query := bson.M{"_id": serviceID}
	fields := bson.M{"text": 1}

	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], query, fields, &service)
	if err != nil {
		return ""
	}
	if text, ok := service["text"].(string); ok {
		return text
	}
	return ""
}

func insertTask(taskData bson.M) (string, error) {
	taskID := taskData["_id"].(string)
	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskData)
	return taskID, err
}

func updateUserLastPostedTask(userID string, createdAt time.Time) {
	query := bson.M{"_id": userID}
	update := bson.M{"$set": bson.M{"lastPostedTask": createdAt}}
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_USERS, query, update)
}

func updateSubscriptionHistory(subsID, taskID string, nextDate time.Time) {
	query := bson.M{"_id": subsID}
	update := bson.M{
		"$push": bson.M{
			"taskIds": taskID,
			"history": nextDate,
		},
	}
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], query, update)
}

func handleTaskerAssignment(subs *modelSubscription.Subscription, taskData bson.M, taskID string, user *users.Users) {
	if subs.ForceAcceptTaskerId != "" {
		// Handle force accept tasker logic
		handleForceAcceptTasker(subs, taskData, taskID, user)
	} else {
		// Send task to available taskers
		if !user.IsBlacklist {
			sendTaskToTasker(taskID, subs.ServiceId, user.FavouriteTasker)
		}
	}
}

func handleForceAcceptTasker(subs *modelSubscription.Subscription, taskData bson.M, taskID string, user *users.Users) {
	// Get tasker information
	var tasker users.Users
	query := bson.M{
		"_id":    subs.ForceAcceptTaskerId,
		"status": bson.M{"$in": []string{"ACTIVE", "LOCKED"}},
	}
	fields := bson.M{
		"avatar":            1,
		"name":              1,
		"avgRating":         1,
		"taskDone":          1,
		"phone":             1,
		"status":            1,
		"notComeLockNumber": 1,
	}

	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USERS, query, fields, &tasker)
	if err != nil || !canTaskerAccept(&tasker) {
		// Post to Slack about tasker not available
		message := fmt.Sprintf("Cần CS xử lý: Công việc của Subscription đã đăng và KHÔNG confirm được do Tasker %s đã bị khóa. CV của Asker: %s", tasker.Phone, user.Phone)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
		return
	}

	// Check for conflicts with other tasks
	if hasTaskConflict(subs.ForceAcceptTaskerId, taskData) {
		message := fmt.Sprintf("Cần CS xử lý: Công việc của Subscription đã đăng và KHÔNG confirm được do Tasker %s - %s trùng giờ làm việc. CV của Asker: %s", tasker.Phone, tasker.Name, user.Phone)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
		return
	}

	// Force accept the task
	acceptedTasker := bson.M{
		"taskerId":   subs.ForceAcceptTaskerId,
		"avatar":     tasker.Avatar,
		"name":       tasker.Name,
		"avgRating":  tasker.AvgRating,
		"taskDone":   tasker.TaskDone,
		"acceptedAt": globalLib.GetCurrentTime(local.TimeZone),
	}

	update := bson.M{
		"$set": bson.M{
			"acceptedTasker": []bson.M{acceptedTasker},
			"status":         globalConstant.TASK_STATUS_CONFIRMED,
			"updatedAt":      globalLib.GetCurrentTime(local.TimeZone),
		},
	}

	globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskID, update)

	// Send notification to tasker
	sendConfirmationNotification(subs.ForceAcceptTaskerId, taskID)

	// Alert if tasker status is not ACTIVE
	if tasker.Status != "ACTIVE" {
		message := fmt.Sprintf("CS lưu ý: Tasker %s status %s đã được thêm vào subscription task (Asker: %s)", tasker.Phone, tasker.Status, user.Phone)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
	}
}

func hasTaskConflict(taskerID string, taskData bson.M) bool {
	// Check for conflicting tasks - simplified version
	var confirmedTasks []bson.M
	query := bson.M{
		"status":                  globalConstant.TASK_STATUS_CONFIRMED,
		"acceptedTasker.taskerId": taskerID,
	}

	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], query, bson.M{}, &confirmedTasks)
	if err != nil {
		return false
	}

	// Simple conflict check - in real implementation, you'd check time overlaps
	return len(confirmedTasks) > 0
}

func sendConfirmationNotification(taskerID, taskID string) {
	// Get tasker language (default to Vietnamese)
	var tasker users.Users
	query := bson.M{"_id": taskerID}
	fields := bson.M{"language": 1}
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USERS, query, fields, &tasker)

	lang := globalConstant.LANG_VI
	if err == nil && tasker.Language != "" {
		lang = tasker.Language
	}

	// Create notification data
	title := localization.GetLocalizeObject("NOTIFICATION_MESSAGE_TASKER_CONFIRMED_TITLE", "")
	text := localization.GetLocalizeObject("NOTIFICATION_MESSAGE_TASKER_CONFIRMED", "")

	// Create notification for database
	notiInfos := []interface{}{
		&notification.Notification{
			XId:         globalLib.GenerateObjectId(),
			UserId:      taskerID,
			TaskId:      taskID,
			Type:        1,
			Title:       globalLib.LocalizeServiceName(lang, title),
			Description: globalLib.LocalizeServiceName(lang, text),
			CreatedAt:   timestamppb.New(globalLib.GetCurrentTime(local.TimeZone)),
			NavigateTo:  "TaskDetail",
		},
	}

	// Create user IDs for push notification
	userIds := []*pushNotificationRequest.PushNotificationRequestUserIds{
		{
			UserId:   taskerID,
			Language: lang,
		},
	}

	// Create title and body with localization
	titleText := &service.ServiceText{
		Vi: globalLib.LocalizeServiceName(lang, title),
	}
	bodyText := &service.ServiceText{
		Vi: globalLib.LocalizeServiceName(lang, text),
	}

	// Create payload
	payload := &pushNotificationRequest.PushNotificationRequestPayload{
		Type:       1, // Task confirmation notification type
		TaskId:     taskID,
		NavigateTo: "TaskDetail",
	}

	// Use lib.SendNotification instead of custom gRPC implementation
	lib.SendNotification(notiInfos, userIds, titleText, bodyText, payload, true)
}

func checkSubscriptionCompletion(subs *modelSubscription.Subscription, taskID string) {
	// Use existing subscription data instead of querying DB again
	if len(subs.Schedule) == 0 {
		return
	}

	// Calculate current progress
	// After adding this task to history, check if all tasks are completed
	totalTasks := len(subs.Schedule)
	completedTasks := len(subs.History) + 1 // +1 for the task we just added

	// Check if this is the last task in the subscription
	if completedTasks >= totalTasks {
		// Mark subscription as DONE
		update := bson.M{"$set": bson.M{
			"status":    "DONE",
			"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
		}}
		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], subs.XId, update)

		// Mark task as final subscription task
		taskUpdate := bson.M{"$set": bson.M{
			"finalSubscriptionTask": true,
			"updatedAt":             globalLib.GetCurrentTime(local.TimeZone),
		}}
		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskID, taskUpdate)

		log.Printf("Subscription %s completed. Final task: %s (completed %d/%d tasks)",
			subs.XId, taskID, completedTasks, totalTasks)
	} else {
		log.Printf("Subscription %s progress: %d/%d tasks completed",
			subs.XId, completedTasks, totalTasks)
	}
}

// Additional helper functions for subscription processing
