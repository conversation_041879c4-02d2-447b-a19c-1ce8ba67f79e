package modelTransactionInfo

import (
	"google.golang.org/protobuf/types/known/timestamppb"
)

type TransactionInfo struct {
	XId           string
	InvoiceNo     string
	Status        string
	Amount        float64
	Charged       bool
	TaskId        string
	Date          *timestamppb.Timestamp
	IsRefunded    bool
	Refund        *RefundInfo // Field này dùng cho version cũ
	PaymentMethod string
	UserId        string
	Bank          string

	RefundInfo     []*RefundInfo // Field này dùng cho version mới. Vì refund có nhiều giao dịch refund chứ không phải 1
	PaymentGateway string
}

type RefundInfo struct {
	Status       string
	Date         *timestamppb.Timestamp
	RefundAmount float64
	CancelFee    float64
	Reason       string
}
