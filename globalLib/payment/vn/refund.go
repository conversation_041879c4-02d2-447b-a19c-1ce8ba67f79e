package vn

import (
	"errors"
	"fmt"
	"time"

	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalModel/modelTransactionInfo"
	modelAccountingJournalEntry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/accountingJournalEntry"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	modelBusinessMemberTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMemberTransaction"
	modelRefundRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/refundRequest"
	"go.mongodb.org/mongo-driver/bson"
)

func CreateRefundRequestTaskCard_VN(transactionInfo *modelTransactionInfo.TransactionInfo, cancelFee float64, reason string, timeZone *time.Location) (refundRequestId string, refundAmount float64, isBPayRefund bool, err error) {
	if !isTransactionSuccess(transactionInfo) {
		err = errors.New("transaction not paid")
		return
	}

	// 2. calculate refund amount
	refundAmount = transactionInfo.Amount - cancelFee
	if refundAmount <= 0 {
		return
	}

	// 4. refund wallet
	err = createdRefundRequestCard_VN(transactionInfo, refundAmount, cancelFee, reason, timeZone)
	return
}

func createdRefundRequestCard_VN(transactionInfo *modelTransactionInfo.TransactionInfo, refundAmount, cancelFee float64, reason string, timeZone *time.Location) (err error) {
	if refundAmount <= 0 {
		return
	}
	refundRequest := &modelRefundRequest.RefundRequest{
		XId:           globalLib.GenerateObjectId(),
		Type:          globalConstant.REFUND_REQUEST_TYPE_SYSTEM_REFUND,
		Reason:        reason,
		CancelTaskFee: cancelFee,
		Amount:        refundAmount,
		TaskId:        transactionInfo.TaskId,
		UserId:        transactionInfo.UserId,
		InvoiceNo:     transactionInfo.InvoiceNo,
		PaymentMethod: transactionInfo.PaymentMethod,
		Currency:      globalConstant.CURRENCY_SIGN_VN.Code,
		Status:        globalConstant.REFUND_REQUEST_STATUS_NEW,
		CreatedAt:     globalLib.GetCurrentTimestamp(timeZone),
	}
	err = globalDataAccess.InsertOne(globalCollection.COLLECTION_REFUND_REQUEST[globalConstant.ISO_CODE_VN], refundRequest)
	if err != nil {
		return fmt.Errorf("cannot create refund request for task, err: %v", err)
	}
	return
}

// func RefundbPayBusinessMember(transactionInfo *modelBusinessMemberTransaction.BusinessMemberTransaction, cancelFee float64, name, reason string) (refundAmount float64, err error) {
// 	// Create businessMemberTransaction
// 	transReport := modelBusinessMemberTransaction.BusinessMemberTransaction{
// 		XId:           globalLib.GenerateObjectId(),
// 		Type:          globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT,
// 		Name:          transactionInfo.Name,
// 		Reason:        transactionInfo.Reason,
// 		Amount:        transactionInfo.Amount,
// 		TaskId:        transactionInfo.TaskId,
// 		UserId:        transactionInfo.UserId,
// 		Currency:      transactionInfo.Currency,
// 		MemberId:      transactionInfo.MemberId,
// 		BusinessId:    transactionInfo.BusinessId,
// 		CancelTaskFee: transactionInfo.CancelTaskFee,
// 	}
// 	err = transReport.InsertOne(globalConstant.ISO_CODE_VN)
// 	if err != nil {
// 		return
// 	}
// 	// Inc refund amount
// 	_, err = modelBusinessMember.UpdateOneById(globalConstant.ISO_CODE_VN, transactionInfo.MemberId, bson.M{"$inc": bson.M{"bPay": refundAmount}})
// 	if err != nil {
// 		modelBusinessMemberTransaction.DeleteOneById(globalConstant.ISO_CODE_VN, transReport.XId)
// 		return
// 	}
// 	return
// }

func RefundbPayBusinessMember(transactionInfo *modelBusinessMemberTransaction.BusinessMemberTransaction, slackToken string) (refundAmount float64, err error) {
	// Create businessMemberTransaction
	refundAmount = transactionInfo.Amount
	transReport := modelBusinessMemberTransaction.BusinessMemberTransaction{
		XId:            globalLib.GenerateObjectId(),
		Type:           globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT,
		Name:           transactionInfo.Name,
		Reason:         transactionInfo.Reason,
		Amount:         refundAmount,
		TaskId:         transactionInfo.TaskId,
		UserId:         transactionInfo.UserId,
		Currency:       transactionInfo.Currency,
		MemberId:       transactionInfo.MemberId,
		BusinessId:     transactionInfo.BusinessId,
		CancelTaskFee:  transactionInfo.CancelTaskFee,
		SubscriptionId: transactionInfo.SubscriptionId,
		CreatedAt:      globalLib.GetCurrentTimestamp(globalLib.GetTimeZone()),
	}
	err = transReport.InsertOne(globalConstant.ISO_CODE_VN)
	if err != nil {
		return
	}
	// Inc refund amount
	_, err = modelBusinessMember.UpdateOneById(globalConstant.ISO_CODE_VN, transactionInfo.MemberId, bson.M{"$inc": bson.M{"bPay": transactionInfo.Amount}})
	if err != nil {
		modelBusinessMemberTransaction.DeleteOneById(globalConstant.ISO_CODE_VN, transReport.XId)
		return
	}

	// Create accounting journal entry
	modelAccountingJournalEntry.InsertOne(globalConstant.ISO_CODE_VN, slackToken,
		&modelAccountingJournalEntry.AccountingJournalEntry{
			XId:            globalLib.GenerateObjectId(),
			Key:            transactionInfo.Name,
			Reason:         transactionInfo.Reason,
			Amount:         refundAmount,
			Type:           globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT,
			AccountType:    globalConstant.FA_TRANSACTION_ACCOUNT_TYPE_MAIN,
			UserId:         transactionInfo.UserId,
			UserType:       globalConstant.USER_TYPE_ASKER,
			SubscriptionId: transactionInfo.SubscriptionId,
			TaskId:         transactionInfo.TaskId,
			BusinessInfo: &modelAccountingJournalEntry.AccountingJournalEntryBusinessInfo{
				MemberId:   transactionInfo.MemberId,
				BusinessId: transactionInfo.BusinessId,
			},
			Payment: &modelAccountingJournalEntry.AccountingJournalEntryPayment{
				Method: globalConstant.PAYMENT_METHOD_BPAY_BUSINESS,
			},
			TransactionId: transReport.XId,
		})

	return
}
