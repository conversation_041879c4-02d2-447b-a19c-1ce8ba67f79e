// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcScheduleIndo

import (
	context "context"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	taskSchedule "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskSchedule"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// ScheduleIndoClient is the client API for ScheduleIndo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ScheduleIndoClient interface {
	AddHomeCleaningSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error)
	AddOfficeCleaningSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error)
	AddHousekeepingSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error)
}

type scheduleIndoClient struct {
	cc grpc.ClientConnInterface
}

func NewScheduleIndoClient(cc grpc.ClientConnInterface) ScheduleIndoClient {
	return &scheduleIndoClient{cc}
}

func (c *scheduleIndoClient) AddHomeCleaningSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSchedule.ScheduleIndo/AddHomeCleaningSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleIndoClient) AddOfficeCleaningSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSchedule.ScheduleIndo/AddOfficeCleaningSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleIndoClient) AddHousekeepingSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSchedule.ScheduleIndo/AddHousekeepingSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ScheduleIndoServer is the server API for ScheduleIndo service.
// All implementations must embed UnimplementedScheduleIndoServer
// for forward compatibility
type ScheduleIndoServer interface {
	AddHomeCleaningSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error)
	AddOfficeCleaningSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error)
	AddHousekeepingSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error)
	mustEmbedUnimplementedScheduleIndoServer()
}

// UnimplementedScheduleIndoServer must be embedded to have forward compatible implementations.
type UnimplementedScheduleIndoServer struct {
}

func (UnimplementedScheduleIndoServer) AddHomeCleaningSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddHomeCleaningSchedule not implemented")
}
func (UnimplementedScheduleIndoServer) AddOfficeCleaningSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddOfficeCleaningSchedule not implemented")
}
func (UnimplementedScheduleIndoServer) AddHousekeepingSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddHousekeepingSchedule not implemented")
}
func (UnimplementedScheduleIndoServer) mustEmbedUnimplementedScheduleIndoServer() {}

// UnsafeScheduleIndoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ScheduleIndoServer will
// result in compilation errors.
type UnsafeScheduleIndoServer interface {
	mustEmbedUnimplementedScheduleIndoServer()
}

func RegisterScheduleIndoServer(s *grpc.Server, srv ScheduleIndoServer) {
	s.RegisterService(&_ScheduleIndo_serviceDesc, srv)
}

func _ScheduleIndo_AddHomeCleaningSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskSchedule.TaskSchedule)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleIndoServer).AddHomeCleaningSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSchedule.ScheduleIndo/AddHomeCleaningSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleIndoServer).AddHomeCleaningSchedule(ctx, req.(*taskSchedule.TaskSchedule))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleIndo_AddOfficeCleaningSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskSchedule.TaskSchedule)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleIndoServer).AddOfficeCleaningSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSchedule.ScheduleIndo/AddOfficeCleaningSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleIndoServer).AddOfficeCleaningSchedule(ctx, req.(*taskSchedule.TaskSchedule))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleIndo_AddHousekeepingSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskSchedule.TaskSchedule)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleIndoServer).AddHousekeepingSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSchedule.ScheduleIndo/AddHousekeepingSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleIndoServer).AddHousekeepingSchedule(ctx, req.(*taskSchedule.TaskSchedule))
	}
	return interceptor(ctx, in, info, handler)
}

var _ScheduleIndo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcSchedule.ScheduleIndo",
	HandlerType: (*ScheduleIndoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddHomeCleaningSchedule",
			Handler:    _ScheduleIndo_AddHomeCleaningSchedule_Handler,
		},
		{
			MethodName: "AddOfficeCleaningSchedule",
			Handler:    _ScheduleIndo_AddOfficeCleaningSchedule_Handler,
		},
		{
			MethodName: "AddHousekeepingSchedule",
			Handler:    _ScheduleIndo_AddHousekeepingSchedule_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "scheduleIndo.proto",
}
