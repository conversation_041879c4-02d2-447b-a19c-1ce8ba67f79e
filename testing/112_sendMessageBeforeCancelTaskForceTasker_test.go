package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"go.mongodb.org/mongo-driver/bson"
)

func TestSendMessageBeforeTaskForceTaskerIsCancelled(t *testing.T) {

	// Check case send message before task is cancelled 1 hour
	t.Run("1", func(t *testing.T) {
		ResetData()
		apiURL := apiSyncCron + "/send-message-before-cancel-task-force-tasker"

		CreateUser([]map[string]interface{}{
			{
				"phone":     "0823456720",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 0",
			},
			{
				"phone":     "0823456721",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 1",
			},
			{
				"phone":     "0823456728",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker",
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		date := now.AddDate(0, 0, 3)
		createdAtTime := now.Add(-12 * time.Hour)
		taskIds := CreateTask([]map[string]interface{}{
			{ // 0. normalTask + RemindMessageCreatedAt before 4 hours -> Send
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"forceTasker": map[string]interface{}{
					"taskerId":               "0823456728",
					"name":                   "Tasker",
					"remindMessageCreatedAt": now.Add(-4 * time.Hour),
				},
			},
			{ // 1. normalTask + RemindMessageCreatedAt before 30 minutes -> WON'T Send
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"forceTasker": map[string]interface{}{
					"taskerId":               "0823456728",
					"name":                   "Tasker",
					"remindMessageCreatedAt": now.Add(-30 * time.Minute),
				},
			},
		})

		chatIds := CreateChatConversation([]map[string]interface{}{
			{
				"askerPhone":  "0823456720",
				"taskerPhone": "0823456728",
				"taskId":      taskIds[0],
				"askerName":   "Asker 0",
				"taskerName":  "Tasker",
				"messages": []map[string]interface{}{
					{
						"_id":       "x63db5fc8e4ca3a9d185346f4",
						"from":      "TASKER",
						"message":   "Xin chào, tôi đã nhận làm công việc của bạn. Tôi sẽ đến đúng giờ.",
						"userId":    "xxx",
						"isRead":    false,
						"createdAt": globalLib.GetCurrentTime(local.TimeZone),
					},
				},
				"members": []map[string]interface{}{
					{
						"_id": "0823456720",
					},
					{
						"_id": "0823456728",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the database", func() {
					time.Sleep(1 * time.Second)

					chatMessage1, _ := pkgChatMessage.GetChatConversationWithMessages(globalConstant.ISO_CODE_VN, bson.M{"_id": chatIds[0]}, bson.M{})
					So(chatMessage1.AskerId, ShouldEqual, "0823456720")
					So(chatMessage1.TaskerId, ShouldEqual, "0823456728")
					So(len(chatMessage1.Messages), ShouldEqual, 2)
					So(chatMessage1.Messages[1].From, ShouldEqual, "SYSTEM")
					So(len(chatMessage1.Messages[1].MessageBySystem.Actions), ShouldEqual, 2)
					So(chatMessage1.Messages[1].MessageBySystem.Actions[0].Title.Vi, ShouldEqual, "Hủy công việc")
					So(chatMessage1.Messages[1].MessageBySystem.Actions[1].Title.Vi, ShouldEqual, "Gửi cho Tasker khác")
					So(chatMessage1.Messages[1].MessageBySystem.Title.Vi, ShouldEqual, "Công việc có thể bị hủy sau 1 giờ nữa")
					So(chatMessage1.Messages[1].MessageBySystem.Text.Vi, ShouldEqual, "Công việc của bạn sẽ tự động hủy sau 1 giờ nữa nếu chưa được xác nhận bởi bạn hoặc Tasker. Bạn cũng có thể gửi công việc này cho các Tasker khác trên hệ thống để không ảnh hưởng đến lịch làm việc.")

				})
			})
		})
	})

	// Check case send message after tasker rejected or suggested new date option
	t.Run("2", func(t *testing.T) {
		ResetData()
		apiURL := apiSyncCron + "/send-message-before-cancel-task-force-tasker"

		CreateUser([]map[string]interface{}{
			{
				"phone":     "0823456720",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 0",
			},
			{
				"phone":     "0823456721",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 1",
			},
			{
				"phone":     "0823456728",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker",
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		date := now.AddDate(0, 0, 3)
		createdAtTime := now.Add(-3 * time.Hour)
		taskIds := CreateTask([]map[string]interface{}{
			{ // 0. normalTask + RemindMessageCreatedAt before 4 hours +tasker rejected -> Send
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"forceTasker": map[string]interface{}{
					"taskerId":               "0823456728",
					"name":                   "Tasker",
					"remindMessageCreatedAt": now.Add(-2 * time.Hour),
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":       globalConstant.CHANGES_HISTORY_KEY_RESEND_FORCE_TASKER,
						"createdAt": time.Date(now.Year(), now.Month(), now.Day(), now.Hour()-3, now.Minute(), 0, 0, local.TimeZone),
					},
					{
						"key":       globalConstant.CHAT_HISTORY_KEY_TASKER_REJECT,
						"createdAt": time.Date(now.Year(), now.Month(), now.Day(), now.Hour()-1, now.Minute(), 0, 0, local.TimeZone),
					},
				},
			},
			{ // 1. normalTask + RemindMessageCreatedAt before 4 hours + tasker suggested new date option -> Send
				"askerPhone":  "0823456721",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour()-2, createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"forceTasker": map[string]interface{}{
					"taskerId":               "0823456728",
					"name":                   "Tasker",
					"remindMessageCreatedAt": now.Add(-2 * time.Hour),
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":       globalConstant.CHANGES_HISTORY_KEY_RESEND_FORCE_TASKER,
						"createdAt": time.Date(now.Year(), now.Month(), now.Day(), now.Hour()-3, now.Minute(), 0, 0, local.TimeZone),
					},
					{
						"key":       globalConstant.CHANGES_HISTORY_KEY_TASKER_SUGGESTED_NEW_DATE_OPTION,
						"createdAt": time.Date(now.Year(), now.Month(), now.Day(), now.Hour()-1, now.Minute(), 0, 0, local.TimeZone),
					},
				},
			},
			{ // 2. normalTask + task is created after 5 hours + RemindMessageCreatedAt before 4 hours + tasker've just suggested new date option  -> WON'T Send
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"forceTasker": map[string]interface{}{
					"taskerId":               "0823456728",
					"name":                   "Tasker",
					"remindMessageCreatedAt": now.Add(-2 * time.Hour),
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":       globalConstant.CHANGES_HISTORY_KEY_RESEND_FORCE_TASKER,
						"createdAt": time.Date(now.Year(), now.Month(), now.Day(), now.Hour()-3, now.Minute(), 0, 0, local.TimeZone),
					},
					{
						"key":       globalConstant.CHANGES_HISTORY_KEY_TASKER_SUGGESTED_NEW_DATE_OPTION,
						"createdAt": time.Date(now.Year(), now.Month(), now.Day(), now.Hour(), now.Minute(), 0, 0, local.TimeZone),
					},
				},
			},
			{ // 1. normalTask + RemindMessageCreatedAt before 30 minutes -> WON'T Send
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"forceTasker": map[string]interface{}{
					"taskerId":               "0823456728",
					"name":                   "Tasker",
					"remindMessageCreatedAt": now.Add(-30 * time.Minute),
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":       globalConstant.CHANGES_HISTORY_KEY_RESEND_FORCE_TASKER,
						"createdAt": time.Date(now.Year(), now.Month(), now.Day(), now.Hour()-3, now.Minute(), 0, 0, local.TimeZone),
					},
					{
						"key":       globalConstant.CHANGES_HISTORY_KEY_TASKER_SUGGESTED_NEW_DATE_OPTION,
						"createdAt": time.Date(now.Year(), now.Month(), now.Day(), now.Hour()-1, now.Minute(), 0, 0, local.TimeZone),
					},
				},
			},
		})

		chatIds := CreateChatConversation([]map[string]interface{}{
			{
				"askerPhone":  "0823456720",
				"taskerPhone": "0823456728",
				"taskId":      taskIds[0],
				"askerName":   "Asker 0",
				"taskerName":  "Tasker",
				"messages": []map[string]interface{}{
					{
						"_id":       "x63db5fc8e4ca3a9d185346f4",
						"from":      "TASKER",
						"message":   "Xin chào, tôi đã nhận làm công việc của bạn. Tôi sẽ đến đúng giờ.",
						"userId":    "xxx",
						"isRead":    false,
						"createdAt": globalLib.GetCurrentTime(local.TimeZone),
					},
				},
				"members": []map[string]interface{}{
					{
						"_id": "0823456720",
					},
					{
						"_id": "0823456728",
					},
				},
			},
			{
				"askerPhone":  "0823456721",
				"taskerPhone": "0823456728",
				"taskId":      taskIds[0],
				"askerName":   "Asker 0",
				"taskerName":  "Tasker",
				"messages": []map[string]interface{}{
					{
						"_id":       "x63db5fc8e4ca3a9d185346f5",
						"from":      "TASKER",
						"message":   "Xin chào, tôi đã nhận làm công việc của bạn. Tôi sẽ đến đúng giờ.",
						"userId":    "xxx",
						"isRead":    false,
						"createdAt": globalLib.GetCurrentTime(local.TimeZone),
					},
				},
				"members": []map[string]interface{}{
					{
						"_id": "0823456721",
					},
					{
						"_id": "0823456728",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the database", func() {
					time.Sleep(1 * time.Second)

					chatMessage1, _ := pkgChatMessage.GetChatConversationWithMessages(globalConstant.ISO_CODE_VN, bson.M{"_id": chatIds[0]}, bson.M{})
					So(chatMessage1.AskerId, ShouldEqual, "0823456720")
					So(chatMessage1.TaskerId, ShouldEqual, "0823456728")
					So(len(chatMessage1.Messages), ShouldEqual, 2)
					So(chatMessage1.Messages[1].From, ShouldEqual, "SYSTEM")
					So(len(chatMessage1.Messages[1].MessageBySystem.Actions), ShouldEqual, 2)
					So(chatMessage1.Messages[1].MessageBySystem.Actions[0].Title.Vi, ShouldEqual, "Hủy công việc")
					So(chatMessage1.Messages[1].MessageBySystem.Actions[1].Title.Vi, ShouldEqual, "Gửi cho Tasker khác")
					So(chatMessage1.Messages[1].MessageBySystem.Title.Vi, ShouldEqual, "Công việc có thể bị hủy sau 1 giờ nữa")
					So(chatMessage1.Messages[1].MessageBySystem.Text.Vi, ShouldEqual, "Công việc của bạn sẽ tự động hủy sau 1 giờ nữa nếu chưa được xác nhận bởi bạn hoặc Tasker. Bạn cũng có thể gửi công việc này cho các Tasker khác trên hệ thống để không ảnh hưởng đến lịch làm việc.")

					chatMessage2, _ := pkgChatMessage.GetChatConversationWithMessages(globalConstant.ISO_CODE_VN, bson.M{"_id": chatIds[1]}, bson.M{})
					So(chatMessage2.AskerId, ShouldEqual, "0823456721")
					So(chatMessage2.TaskerId, ShouldEqual, "0823456728")
					So(len(chatMessage2.Messages), ShouldEqual, 2)
					So(chatMessage2.Messages[1].From, ShouldEqual, "SYSTEM")
					So(len(chatMessage2.Messages[1].MessageBySystem.Actions), ShouldEqual, 2)
					So(chatMessage2.Messages[1].MessageBySystem.Actions[0].Title.Vi, ShouldEqual, "Hủy công việc")
					So(chatMessage2.Messages[1].MessageBySystem.Actions[1].Title.Vi, ShouldEqual, "Gửi cho Tasker khác")
					So(chatMessage2.Messages[1].MessageBySystem.Title.Vi, ShouldEqual, "Công việc có thể bị hủy sau 1 giờ nữa")
					So(chatMessage2.Messages[1].MessageBySystem.Text.Vi, ShouldEqual, "Công việc của bạn sẽ tự động hủy sau 1 giờ nữa nếu chưa được xác nhận bởi bạn hoặc Tasker. Bạn cũng có thể gửi công việc này cho các Tasker khác trên hệ thống để không ảnh hưởng đến lịch làm việc.")

				})
			})
		})
	})

}
