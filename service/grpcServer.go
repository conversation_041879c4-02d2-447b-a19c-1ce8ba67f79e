package service

import (
	"context"

	"gitlab.com/btaskee/go-email-vn-v3/handlers"
	"gitlab.com/btaskee/go-email-vn-v3/handlers/sendBusinessReportEmail"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcEmailVN"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
)

type Server struct {
	grpcEmailVN.UnimplementedEmailVNServer
}

func (s *Server) EmailTest(ctx context.Context, req *modelEmailSending.EmailSending) (*modelEmailResponse.EmailResponse, error) {
	return &modelEmailResponse.EmailResponse{
		Message: "Test ne",
	}, nil
}

func (s *Server) SendEmail(ctx context.Context, req *modelEmailSending.EmailSending) (*modelEmailResponse.EmailResponse, error) {
	return handlers.SendEmail(req)
}

func (s *Server) SendReceiptEmail(ctx context.Context, req *modelTask.Task) (*modelEmailResponse.EmailResponse, error) {
	return handlers.SendReceiptEmail(req)
}

func (s *Server) SendVerifyEmail(ctx context.Context, req *modelUser.Users) (*modelEmailResponse.EmailResponse, error) {
	return handlers.SendVerifyEmail(req)
}

func (s *Server) SendRenewSubscriptionEmail(ctx context.Context, req *modelEmailSending.EmailRequest) (*modelEmailResponse.EmailResponse, error) {
	return handlers.SendRenewSubscriptionEmail(req)
}

func (s *Server) ResendReceiptEmail(ctx context.Context, req *modelEmailSending.EmailRequest) (*modelEmailResponse.EmailResponse, error) {
	return handlers.ResendReceiptEmail(req)
}

func (s *Server) ResendSubscriptionOrderEmail(ctx context.Context, req *modelEmailSending.EmailRequest) (*modelEmailResponse.EmailResponse, error) {
	return handlers.ResendSubscriptionOrderEmail(req)
}

func (s *Server) SendReceiptSubscriptionEmail(ctx context.Context, req *modelEmailSending.EmailRequest) (*modelEmailResponse.EmailResponse, error) {
	return handlers.SendReceiptSubscriptionEmail(req)
}

func (s *Server) SendSubscriptionSuggestionEmail(ctx context.Context, req *modelEmailSending.EmailRequest) (*modelEmailResponse.EmailResponse, error) {
	return handlers.SendSubscriptionSuggestionEmail(req)
}

func (s *Server) SendCancelFeeEmail(ctx context.Context, req *modelEmailSending.EmailCancelFeeRequest) (*modelEmailResponse.EmailResponse, error) {
	return handlers.SendCancelFeeEmail(req)
}

func (s *Server) SendSubscriptionRenewedEmail(ctx context.Context, req *modelEmailSending.EmailRequest) (*modelEmailResponse.EmailResponse, error) {
	return handlers.SendSubscriptionRenewedEmail(req)
}

func (s *Server) SendTopUpSuccessEmail(ctx context.Context, req *modelEmailSending.EmailRequest) (*modelEmailResponse.EmailResponse, error) {
	return handlers.SendTopUpSuccessEmail(req)
}

func (s *Server) SendCancelChargeFailEmail(ctx context.Context, req *modelEmailSending.EmailCancelFeeRequest) (*modelEmailResponse.EmailResponse, error) {
	return handlers.SendCancelChargeFailEmail(req)
}

func (s *Server) SendChargeFailEmail(ctx context.Context, req *modelTask.Task) (*modelEmailResponse.EmailResponse, error) {
	return handlers.SendChargeFailEmail(req)
}

func (s *Server) SendChangeToCashScheduleEmail(ctx context.Context, req *modelEmailSending.EmailChangeToTaskCashScheduleRequest) (*modelEmailResponse.EmailResponse, error) {
	return handlers.SendChangeToCashScheduleEmail(req)
}

func (s *Server) SendBusinessReportEmail(ctx context.Context, req *modelEmailSending.EmailSendBusinessReportRequest) (*modelEmailResponse.EmailResponse, error) {
	return sendBusinessReportEmail.SendBusinessReportEmail(req)
}
