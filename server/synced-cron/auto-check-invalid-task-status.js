import { Meteor } from 'meteor/meteor';
import _ from 'lodash';
import {
  trackingScheduleStatus,
  postToSlack,
  changeTimeZone
} from '/server/helper-function';

const runCheckTask = () => {
  return;
	console.log("Start runCheckTask status ...");
  const hourInVN = changeTimeZone(new Date()).getHours();
  
  if ( hourInVN >= 22 || hourInVN < 7 ) {
    return;
  }

  var message = null;

  let taskArray = [];
  let taskIds = [];

  Meteor.appCollection.Task.find({
    date: {$gte: new Date()},
    status: 'WAITING_ASKER_CONFIRMATION',
    autoChooseTasker: true,
    'serviceText.en': {$ne: 'Deep Cleaning'}
  }, {
    fields: {
      taskPlace: 1,
      serviceText: 1,
      phone: 1,
      lastPostedAlertAt: 1,
      contactName: 1
    },
    sort: {
      'taskPlace.city': 1
    }
  }).forEach((task, index) => {
    taskArray.push(`${task.serviceText.vi} - ${task.taskPlace.city} - ${task.taskPlace.district} - ${task.phone} - ${task.contactName}`)
  });
  if ( taskArray.length > 0 ) { 
    postToSlack({
      channel: 'task-price-checking',
      text: `Các công việc có status WAITING không dúng:\n${taskArray.join('\n')}`,
    });
  }

	console.log("Finished runCheckTask status ");
  // Tracking
  trackingScheduleStatus({name: 'autoCkeckInvalidTaskStatus', nextRunAt: SyncedCron.nextScheduledAtDate('autoCkeckInvalidTaskStatus'), message});
};

  Meteor.methods({
    autoCkeckInvalidTaskStatus() {
      SyncedCron.add({
        name: 'autoCkeckInvalidTaskStatus',
        schedule(parser) {
          // Run every 10 minutes
          return parser.cron('0 0/10 * 1/1 * ? *', true);
        },
        job() {
          runCheckTask();
        },
      });
	    console.log("Added autoCheckInvalidTaskStatus cron");
    },
    'runNow.autoCkeckInvalidTaskStatus'() {
      runCheckTask();
    },
  });
