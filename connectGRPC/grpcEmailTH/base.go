package grpcEmailTH

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcEmailTH"
	"google.golang.org/grpc"
)

func ConnectGRPCEmailTH(endpoint string) (grpcEmailTH.EmailTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcEmailTH.NewEmailTHClient(connect)
	return client, connect, err
}
