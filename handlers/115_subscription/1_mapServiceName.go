package subscription

// mapServiceName maps subscription service names to regular service names
func mapServiceName(subServiceName string) string {
	switch subServiceName {
	case "CLEANING_SUBSCRIPTION":
		return "CLEANING"
	case "ELDERLY_CARE_SUBSCRIPTION":
		return "ELDERLY_CARE"
	case "PATIENT_CARE_SUBSCRIPTION":
		return "PATIENT_CARE"
	case "CHILD_CARE_SUBSCRIPTION":
		return "CHILD_CARE"
	default:
		return subServiceName
	}
}
