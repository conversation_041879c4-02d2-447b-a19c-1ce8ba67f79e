// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.7.1
// source: event.proto

package grpcEventMY

import (
	empty "github.com/golang/protobuf/ptypes/empty"
	eventMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/eventMessage"
	users "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_event_proto protoreflect.FileDescriptor

var file_event_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x67,
	0x72, 0x70, 0x63, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x59, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x51, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32,
	0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x43, 0x67, 0x69, 0x74, 0x6c,
	0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67,
	0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x75, 0x73,
	0x65, 0x72, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32,
	0xe7, 0x08, 0x0a, 0x05, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x12, 0x6c, 0x0a, 0x20, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x50, 0x61,
	0x79, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x2e, 0x2e,
	0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x50, 0x61,
	0x79, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x71, 0x0a, 0x25, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x50, 0x61, 0x79, 0x43,
	0x6f, 0x6d, 0x62, 0x6f, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x45, 0x72, 0x72, 0x6f, 0x72,
	0x12, 0x2e, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61,
	0x70, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x21, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54,
	0x61, 0x73, 0x6b, 0x65, 0x72, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x12,
	0x2a, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70,
	0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x21, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e,
	0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2a, 0x2e, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00,
	0x12, 0x6b, 0x0a, 0x23, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76,
	0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68, 0x64,
	0x72, 0x61, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2a, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43,
	0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x67, 0x0a,
	0x1f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54,
	0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x44, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x12, 0x2a, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e,
	0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61,
	0x70, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x1d, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69,
	0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65,
	0x72, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x2c, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43,
	0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12,
	0x71, 0x0a, 0x25, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65,
	0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x46, 0x69, 0x6e, 0x69, 0x73, 0x68,
	0x54, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x2e, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e,
	0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79,
	0x22, 0x00, 0x12, 0x49, 0x0a, 0x1f, 0x41, 0x64, 0x64, 0x41, 0x73, 0x6b, 0x65, 0x72, 0x44, 0x6f,
	0x6e, 0x65, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61,
	0x6c, 0x47, 0x69, 0x66, 0x74, 0x12, 0x0c, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2e, 0x55, 0x73,
	0x65, 0x72, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x44, 0x0a,
	0x1a, 0x41, 0x64, 0x64, 0x41, 0x73, 0x6b, 0x65, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x47, 0x69, 0x66, 0x74, 0x12, 0x0c, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x1c, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43,
	0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x45, 0x78, 0x70, 0x69,
	0x72, 0x65, 0x64, 0x12, 0x2a, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65,
	0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x42, 0x39, 0x5a, 0x37, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f,
	0x67, 0x6f, 0x2d, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2d, 0x76, 0x33, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x45, 0x76, 0x65,
	0x6e, 0x74, 0x4d, 0x59, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_event_proto_goTypes = []interface{}{
	(*eventMessage.TrackingCleverTapPayComboMessage)(nil), // 0: eventMessage.TrackingCleverTapPayComboMessage
	(*eventMessage.TrackingCleverTapTaskMessage)(nil),     // 1: eventMessage.TrackingCleverTapTaskMessage
	(*eventMessage.TrackingCleverTapRatingMessage)(nil),   // 2: eventMessage.TrackingCleverTapRatingMessage
	(*eventMessage.TrackingCleverTapTrainingMessage)(nil), // 3: eventMessage.TrackingCleverTapTrainingMessage
	(*users.Users)(nil), // 4: users.Users
	(*empty.Empty)(nil), // 5: google.protobuf.Empty
}
var file_event_proto_depIdxs = []int32{
	0,  // 0: grpcEventMY.Event.TrackingCleverTapPayComboSuccess:input_type -> eventMessage.TrackingCleverTapPayComboMessage
	0,  // 1: grpcEventMY.Event.TrackingCleverTapPayComboPaymentError:input_type -> eventMessage.TrackingCleverTapPayComboMessage
	1,  // 2: grpcEventMY.Event.TrackingCleverTapTaskerAcceptTask:input_type -> eventMessage.TrackingCleverTapTaskMessage
	1,  // 3: grpcEventMY.Event.TrackingCleverTapTaskerCancelTask:input_type -> eventMessage.TrackingCleverTapTaskMessage
	1,  // 4: grpcEventMY.Event.TrackingCleverTapTaskerWithdrawTask:input_type -> eventMessage.TrackingCleverTapTaskMessage
	1,  // 5: grpcEventMY.Event.TrackingCleverTapTaskerDoneTask:input_type -> eventMessage.TrackingCleverTapTaskMessage
	2,  // 6: grpcEventMY.Event.TrackingCleverTapTaskerRating:input_type -> eventMessage.TrackingCleverTapRatingMessage
	3,  // 7: grpcEventMY.Event.TrackingCleverTapTaskerFinishTraining:input_type -> eventMessage.TrackingCleverTapTrainingMessage
	4,  // 8: grpcEventMY.Event.AddAskerDoneBookingReferralGift:input_type -> users.Users
	4,  // 9: grpcEventMY.Event.AddAskerSignUpReferralGift:input_type -> users.Users
	1,  // 10: grpcEventMY.Event.TrackingCleverTapTaskExpired:input_type -> eventMessage.TrackingCleverTapTaskMessage
	5,  // 11: grpcEventMY.Event.TrackingCleverTapPayComboSuccess:output_type -> google.protobuf.Empty
	5,  // 12: grpcEventMY.Event.TrackingCleverTapPayComboPaymentError:output_type -> google.protobuf.Empty
	5,  // 13: grpcEventMY.Event.TrackingCleverTapTaskerAcceptTask:output_type -> google.protobuf.Empty
	5,  // 14: grpcEventMY.Event.TrackingCleverTapTaskerCancelTask:output_type -> google.protobuf.Empty
	5,  // 15: grpcEventMY.Event.TrackingCleverTapTaskerWithdrawTask:output_type -> google.protobuf.Empty
	5,  // 16: grpcEventMY.Event.TrackingCleverTapTaskerDoneTask:output_type -> google.protobuf.Empty
	5,  // 17: grpcEventMY.Event.TrackingCleverTapTaskerRating:output_type -> google.protobuf.Empty
	5,  // 18: grpcEventMY.Event.TrackingCleverTapTaskerFinishTraining:output_type -> google.protobuf.Empty
	5,  // 19: grpcEventMY.Event.AddAskerDoneBookingReferralGift:output_type -> google.protobuf.Empty
	5,  // 20: grpcEventMY.Event.AddAskerSignUpReferralGift:output_type -> google.protobuf.Empty
	5,  // 21: grpcEventMY.Event.TrackingCleverTapTaskExpired:output_type -> google.protobuf.Empty
	11, // [11:22] is the sub-list for method output_type
	0,  // [0:11] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_event_proto_init() }
func file_event_proto_init() {
	if File_event_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_event_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_event_proto_goTypes,
		DependencyIndexes: file_event_proto_depIdxs,
	}.Build()
	File_event_proto = out.File
	file_event_proto_rawDesc = nil
	file_event_proto_goTypes = nil
	file_event_proto_depIdxs = nil
}
