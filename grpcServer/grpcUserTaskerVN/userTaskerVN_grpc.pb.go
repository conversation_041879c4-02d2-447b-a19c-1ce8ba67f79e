// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcUserTaskerVN

import (
	context "context"
	employeeProfile "gitlab.com/btaskee/go-services-model-v2/grpcmodel/employeeProfile"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// UserTaskerVNClient is the client API for UserTaskerVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type UserTaskerVNClient interface {
	CreateEmployeeAccount(ctx context.Context, in *employeeProfile.EmployeeProfile, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type userTaskerVNClient struct {
	cc grpc.ClientConnInterface
}

func NewUserTaskerVNClient(cc grpc.ClientConnInterface) UserTaskerVNClient {
	return &userTaskerVNClient{cc}
}

func (c *userTaskerVNClient) CreateEmployeeAccount(ctx context.Context, in *employeeProfile.EmployeeProfile, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/grpcUserTaskerVN.UserTaskerVN/CreateEmployeeAccount", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserTaskerVNServer is the server API for UserTaskerVN service.
// All implementations must embed UnimplementedUserTaskerVNServer
// for forward compatibility
type UserTaskerVNServer interface {
	CreateEmployeeAccount(context.Context, *employeeProfile.EmployeeProfile) (*emptypb.Empty, error)
	mustEmbedUnimplementedUserTaskerVNServer()
}

// UnimplementedUserTaskerVNServer must be embedded to have forward compatible implementations.
type UnimplementedUserTaskerVNServer struct {
}

func (UnimplementedUserTaskerVNServer) CreateEmployeeAccount(context.Context, *employeeProfile.EmployeeProfile) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEmployeeAccount not implemented")
}
func (UnimplementedUserTaskerVNServer) mustEmbedUnimplementedUserTaskerVNServer() {}

// UnsafeUserTaskerVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserTaskerVNServer will
// result in compilation errors.
type UnsafeUserTaskerVNServer interface {
	mustEmbedUnimplementedUserTaskerVNServer()
}

func RegisterUserTaskerVNServer(s *grpc.Server, srv UserTaskerVNServer) {
	s.RegisterService(&_UserTaskerVN_serviceDesc, srv)
}

func _UserTaskerVN_CreateEmployeeAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(employeeProfile.EmployeeProfile)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserTaskerVNServer).CreateEmployeeAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcUserTaskerVN.UserTaskerVN/CreateEmployeeAccount",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserTaskerVNServer).CreateEmployeeAccount(ctx, req.(*employeeProfile.EmployeeProfile))
	}
	return interceptor(ctx, in, info, handler)
}

var _UserTaskerVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcUserTaskerVN.UserTaskerVN",
	HandlerType: (*UserTaskerVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateEmployeeAccount",
			Handler:    _UserTaskerVN_CreateEmployeeAccount_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "userTaskerVN.proto",
}
