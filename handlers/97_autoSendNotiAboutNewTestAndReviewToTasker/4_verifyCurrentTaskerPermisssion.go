package autoSendNotiAboutNewTestAndReviewToTasker

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTrainingV2 "gitlab.com/btaskee/go-services-model-v2/grpcmodel/trainingTaskerV2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func verifyCurrentTaskerPermisssion(courseStartDates []*modelTrainingV2.CourseStartDate) []*modelTrainingV2.CourseStartDate {
	var res []*modelTrainingV2.CourseStartDate
	for _, courseStartDate := range courseStartDates {
		//get tasker
		tasker, cities := getTasker(courseStartDate.TaskerId)
		if tasker == nil || len(cities) == 0 {
			continue
		}
		//get course
		query := bson.M{
			"$and": []bson.M{
				{
					"$or": []bson.M{
						{"condition.byTasker.minimumStar": bson.M{"$gt": tasker.AvgRating}},
						{"condition.byTasker.minimumStar": bson.M{"$exists": false}},
					},
				},
				{
					"$or": []bson.M{
						{"cities": bson.M{"$in": cities}},
						{"cities": bson.M{"$exists": false}},
					},
				},
			},
			"status": globalConstant.TRAINING_COURSE_STATUS_ACTIVE,
			"_id":    courseStartDate.CourseId,
		}
		course, err := handlers.GetCourse(query, bson.M{})
		if err != nil && err != mongo.ErrNoDocuments {
			text := fmt.Sprintf("AutoSendNotiAboutNewTestAndReviewToTasker %s: Lỗi call API SendNotiAboutTestDeadline , err: %s ", local.ISO_CODE, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], text)
			return nil
		}
		if course == nil {
			continue
		}

		isAllowed := verifyCourse(course, tasker.XId)
		if !isAllowed {
			continue
		}
		//check tasker passed the response test
		querySubmission := bson.M{
			"taskerId":   tasker.XId,
			"course._id": course.XId,
		}
		submission, err := handlers.GetSubmissionByQuery(querySubmission, bson.M{})
		if err != nil && err != mongo.ErrNoDocuments {
			text := fmt.Sprintf("AutoSendNotiAboutNewTestAndReviewToTasker %s: Lỗi call API SendNotiAboutTestDeadline , err: %s ", local.ISO_CODE, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], text)
			return nil
		}
		if submission != nil {
			continue
		}
		res = append(res, courseStartDate)
	}
	return res
}
