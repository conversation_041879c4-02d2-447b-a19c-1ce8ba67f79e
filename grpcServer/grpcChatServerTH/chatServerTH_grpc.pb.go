// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcChatServerTH

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	websocketMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// ChatServerTHClient is the client API for ChatServerTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ChatServerTHClient interface {
	SendSocketChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error)
}

type chatServerTHClient struct {
	cc grpc.ClientConnInterface
}

func NewChatServerTHClient(cc grpc.ClientConnInterface) ChatServerTHClient {
	return &chatServerTHClient{cc}
}

func (c *chatServerTHClient) SendSocketChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcChatServerTH.ChatServerTH/SendSocketChatMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChatServerTHServer is the server API for ChatServerTH service.
// All implementations must embed UnimplementedChatServerTHServer
// for forward compatibility
type ChatServerTHServer interface {
	SendSocketChatMessage(context.Context, *websocketMessage.WebsocketMessage) (*empty.Empty, error)
	mustEmbedUnimplementedChatServerTHServer()
}

// UnimplementedChatServerTHServer must be embedded to have forward compatible implementations.
type UnimplementedChatServerTHServer struct {
}

func (UnimplementedChatServerTHServer) SendSocketChatMessage(context.Context, *websocketMessage.WebsocketMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSocketChatMessage not implemented")
}
func (UnimplementedChatServerTHServer) mustEmbedUnimplementedChatServerTHServer() {}

// UnsafeChatServerTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChatServerTHServer will
// result in compilation errors.
type UnsafeChatServerTHServer interface {
	mustEmbedUnimplementedChatServerTHServer()
}

func RegisterChatServerTHServer(s *grpc.Server, srv ChatServerTHServer) {
	s.RegisterService(&_ChatServerTH_serviceDesc, srv)
}

func _ChatServerTH_SendSocketChatMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(websocketMessage.WebsocketMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatServerTHServer).SendSocketChatMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcChatServerTH.ChatServerTH/SendSocketChatMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatServerTHServer).SendSocketChatMessage(ctx, req.(*websocketMessage.WebsocketMessage))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChatServerTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcChatServerTH.ChatServerTH",
	HandlerType: (*ChatServerTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendSocketChatMessage",
			Handler:    _ChatServerTH_SendSocketChatMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "chatServerTH.proto",
}
