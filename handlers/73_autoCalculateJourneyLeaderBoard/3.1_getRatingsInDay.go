package autoCalculateJourneyLeaderBoard

import (
	"fmt"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelRating "gitlab.com/btaskee/go-services-model-v2/grpcmodel/rating"
	"go.mongodb.org/mongo-driver/bson"
)

func getRatingsInDay(taskerList []string, atDate time.Time) (map[string][]*modelRating.Rating, error) {
	ratings, err := getRatingsByChunk(taskerList, atDate)
	if err != nil {
		return nil, err
	}

	mapRatingByTaskerId := make(map[string][]*modelRating.Rating)
	for _, v := range ratings {
		if mapRatingByTaskerId[v.TaskerId] == nil {
			mapRatingByTaskerId[v.TaskerId] = []*modelRating.Rating{}
		}
		mapRatingByTaskerId[v.TaskerId] = append(mapRatingByTaskerId[v.TaskerId], v)
	}
	return mapRatingByTaskerId, nil
}

func getRatingsByChunk(taskerList []string, atDate time.Time) ([]*modelRating.Rating, error) {
	startTime, endTime := getTimeRangeToCheckPoint(atDate)
	chunkTaskerList := globalLib.SplitArrayToChunk(taskerList, CHUNK_SIZE_GET_RATINGS)
	var ratings []*modelRating.Rating
	for _, taskerIds := range chunkTaskerList {
		var chunk []*modelRating.Rating
		err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_RATING[local.ISO_CODE],
			bson.M{
				"taskerId":  bson.M{"$in": taskerIds},
				"createdAt": bson.M{"$gte": startTime, "$lte": endTime},
			},
			bson.M{"rate": 1, "taskerId": 1},
			&chunk,
		)
		if err != nil {
			err = fmt.Errorf("getRatingsByChunk: %v", err)
			return nil, err
		}

		ratings = append(ratings, chunk...)
	}
	return ratings, nil
}
