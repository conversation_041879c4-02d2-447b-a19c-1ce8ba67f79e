// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcAcceptTaskTH

import (
	context "context"
	acceptBookingRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/acceptBookingRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// AcceptTaskTHClient is the client API for AcceptTaskTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AcceptTaskTHClient interface {
	ChooseTasker(ctx context.Context, in *acceptBookingRequest.AcceptBookingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type acceptTaskTHClient struct {
	cc grpc.ClientConnInterface
}

func NewAcceptTaskTHClient(cc grpc.ClientConnInterface) AcceptTaskTHClient {
	return &acceptTaskTHClient{cc}
}

func (c *acceptTaskTHClient) ChooseTasker(ctx context.Context, in *acceptBookingRequest.AcceptBookingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/grpcAcceptTaskTH.AcceptTaskTH/ChooseTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AcceptTaskTHServer is the server API for AcceptTaskTH service.
// All implementations must embed UnimplementedAcceptTaskTHServer
// for forward compatibility
type AcceptTaskTHServer interface {
	ChooseTasker(context.Context, *acceptBookingRequest.AcceptBookingRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedAcceptTaskTHServer()
}

// UnimplementedAcceptTaskTHServer must be embedded to have forward compatible implementations.
type UnimplementedAcceptTaskTHServer struct {
}

func (UnimplementedAcceptTaskTHServer) ChooseTasker(context.Context, *acceptBookingRequest.AcceptBookingRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChooseTasker not implemented")
}
func (UnimplementedAcceptTaskTHServer) mustEmbedUnimplementedAcceptTaskTHServer() {}

// UnsafeAcceptTaskTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AcceptTaskTHServer will
// result in compilation errors.
type UnsafeAcceptTaskTHServer interface {
	mustEmbedUnimplementedAcceptTaskTHServer()
}

func RegisterAcceptTaskTHServer(s *grpc.Server, srv AcceptTaskTHServer) {
	s.RegisterService(&_AcceptTaskTH_serviceDesc, srv)
}

func _AcceptTaskTH_ChooseTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(acceptBookingRequest.AcceptBookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AcceptTaskTHServer).ChooseTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcAcceptTaskTH.AcceptTaskTH/ChooseTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AcceptTaskTHServer).ChooseTasker(ctx, req.(*acceptBookingRequest.AcceptBookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _AcceptTaskTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcAcceptTaskTH.AcceptTaskTH",
	HandlerType: (*AcceptTaskTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ChooseTasker",
			Handler:    _AcceptTaskTH_ChooseTasker_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "acceptTaskTH.proto",
}
