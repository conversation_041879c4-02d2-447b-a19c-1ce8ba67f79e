package grpcDataAccess

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcDataAccess"
	"google.golang.org/grpc"
)

func ConnectGRPCDataAccess(endpoint string) (grpcDataAccess.DataAccessClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcDataAccess.NewDataAccessClient(connect)
	return client, connect, err
}
