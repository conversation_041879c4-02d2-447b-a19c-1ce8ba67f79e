package grpcEvent

import (
	"context"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcEventIndo"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcEventMY"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcEventTH"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcEventVN"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/eventMessage"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/emptypb"
)

type EventClient interface {
	TrackingCleverTapTaskExpired(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

func Connect(isoCode, endpoint string) (EventClient, *grpc.ClientConn, error) {
	switch isoCode {
	case globalConstant.ISO_CODE_VN:
		return ConnectGRPCEventVN(endpoint)
	case globalConstant.ISO_CODE_TH:
		return ConnectGRPCEventTH(endpoint)
	case globalConstant.ISO_CODE_INDO:
		return ConnectGRPCEventIndo(endpoint)
	case globalConstant.ISO_CODE_MY:
		return ConnectGRPCEventMY(endpoint)
	}
	return nil, nil, nil
}

func ConnectGRPCEventVN(endpoint string) (grpcEventVN.EventVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcEventVN.NewEventVNClient(connect)
	return client, connect, err
}

func ConnectGRPCEventTH(endpoint string) (grpcEventTH.EventTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcEventTH.NewEventTHClient(connect)
	return client, connect, err
}

func ConnectGRPCEventIndo(endpoint string) (grpcEventIndo.EventIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcEventIndo.NewEventIndoClient(connect)
	return client, connect, err
}

func ConnectGRPCEventMY(endpoint string) (grpcEventMY.EventClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcEventMY.NewEventClient(connect)
	return client, connect, err
}
