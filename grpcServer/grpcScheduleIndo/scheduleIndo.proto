syntax = "proto3";
package grpcSchedule;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcScheduleIndo";

import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response/response.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskSchedule/taskSchedule.proto";

service ScheduleIndo {
  rpc AddHomeCleaningSchedule (taskSchedule.TaskSchedule) returns (response.Response) {}
  rpc AddOfficeCleaningSchedule (taskSchedule.TaskSchedule) returns (response.Response) {}
  rpc AddHousekeepingSchedule (taskSchedule.TaskSchedule) returns (response.Response) {}
}