package handlers

import (
	"fmt"
	"net/http"
	"time"

	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func SendCancelFeeEmail(req *emailSending.EmailCancelFeeRequest) (*modelEmailResponse.EmailResponse, error) {
	defer local.Logger.Sync()

	// Check server config
	resErr := CheckEmailConfig()
	if resErr != nil {
		local.Logger.Warn(resErr.Message)
		return resErr, nil
	}

	// Validate
	if req.TaskId == "" {
		local.Logger.Warn(lib.ERROR_BOOKING_ID_REQUIRED,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_BOOKING_ID_REQUIRED,
		}, nil
	}
	if req.CancelFee == 0 {
		local.Logger.Warn(lib.ERROR_CANCEL_FEE_INVALID,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_CANCEL_FEE_INVALID,
		}, nil
	}
	if req.CancellationReason == "" {
		local.Logger.Warn(lib.ERROR_CANCELLATION_REASON_REQUIRED,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_CANCELLATION_REASON_REQUIRED,
		}, nil
	}

	// Get task data
	var task *modelTask.Task
	globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		req.TaskId,
		bson.M{
			"originCurrency": 1,
			"currency":       1,
			"date":           1,
			"payment":        1,
			"address":        1,
			"duration":       1,
			"createdAt":      1,
			"askerId":        1,
			"serviceId":      1,
			"isoCode":        1,
			"isTetBooking":   1,
		},
		&task)
	if task == nil {
		local.Logger.Warn(lib.ERROR_BOOKING_NOT_FOUND,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusNotFound,
			Message:    lib.ERROR_BOOKING_NOT_FOUND,
		}, nil
	}

	// Get asker data
	asker, _ := modelUser.GetOneById(local.ISO_CODE, task.AskerId, bson.M{"language": 1, "name": 1, "referralCode": 1, "emails": 1})
	if asker == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusNotFound,
			Message:    lib.ERROR_USER_NOT_FOUND,
		}, nil
	}
	if len(asker.Emails) == 0 {
		local.Logger.Warn(lib.ERROR_TO_EMAIL_NIL,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusInternalServerError,
			Message:    lib.ERROR_TO_EMAIL_NIL,
		}, nil
	}
	if !asker.Emails[0].Verified {
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusOK,
		}, nil
	}
	// Get service data
	var service *modelService.Service
	globalDataAccess.GetOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], task.ServiceId, bson.M{"text": 1, "priceSetting.maxCancelTaskFee": 1, "priceSetting.minCancelTaskFee": 1, "cancelTaskFeeInPercentage": 1, "cancelTaskFee": 1, "cancelPrepayTaskFeeInPercentage": 1, "cancelPrepayTaskFeeMinimum": 1}, &service)
	if service == nil {
		local.Logger.Warn(lib.ERROR_SERVICE_NOT_FOUND,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusNotFound,
			Message:    lib.ERROR_SERVICE_NOT_FOUND,
		}, nil
	}

	lang := globalConstant.LANG_EN
	if asker.Language != "" {
		lang = asker.Language
	}
	serviceText := globalLib.LocalizeServiceName(lang, service.Text)
	var currencySign, currencyCode string
	if task.OriginCurrency != nil && task.OriginCurrency.Sign != "" {
		currencySign = task.OriginCurrency.Sign
	}
	if task.OriginCurrency != nil && task.OriginCurrency.Code != "" {
		currencyCode = task.OriginCurrency.Code
	} else if task.Currency != "" {
		currencyCode = task.Currency
	}
	cancelTaskLocal := globalLib.GetCurrentTime(local.TimeZone)
	cancelNoticeFree := "" +
		localization.T(lang, "CANCEL_TASK_NOTICE_FREE_TITLE_1") + " " +
		localization.T(lang, "CANCEL_TASK_NOTICE_FREE_TITLE_2") + " " +
		localization.T(lang, "CANCEL_TASK_NOTICE_FREE_TITLE_3")
	taskDateLocal := globalLib.ParseDateFromTimeStamp(task.Date, local.TimeZone)
	oneHourBeforeTaskDate := FormatTime(taskDateLocal.Add(-time.Hour))

	var cancellationReasonLocalize string
	if req.CancellationReason != globalConstant.CANCELLATION_REASON_ASKER_BUSY &&
		req.CancellationReason != globalConstant.CANCELLATION_REASON_POSTED_WRONG_DATE &&
		req.CancellationReason != globalConstant.CANCELLATION_REASON_TASKER_NOT_COME &&
		req.CancellationReason != globalConstant.CANCELLATION_REASON_ASKER_DONT_NEED_ANYMORE &&
		req.CancellationReason != globalConstant.CANCELLATION_REASON_TASK_WAS_EXPIRED &&
		req.CancellationReason != globalConstant.CANCELLATION_REASON_NO_TASKER_ACCEPT &&
		req.CancellationReason != globalConstant.CANCELLATION_REASON_TASKER_NOT_COME_WITH_ANNOUCEMENT &&
		req.CancellationReason != globalConstant.CANCELLATION_REASON_BACKEND_CANCEL {
		//Asker type the reason
		cancellationReasonLocalize = req.CancellationReason
	} else {
		//Asker choose a reason
		cancellationReasonLocalize = localization.T(lang, req.CancellationReason)
		if cancellationReasonLocalize == "" {
			cancellationReasonLocalize = req.CancellationReason
		}
	}

	// Get setting data
	var settings *modelSettings.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"cancelTaskFeeInPercentage": 1, "cancelTaskFee": 1, "referralSetting": 1}, &settings)
	if settings == nil {
		local.Logger.Warn(lib.ERROR_SETTING_NOT_FOUND,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusNotFound,
			Message:    lib.ERROR_SETTING_NOT_FOUND,
		}, nil
	}

	emailData := map[string]interface{}{
		"ServiceName":                   serviceText,
		"CancelNotice":                  localization.T(lang, "CANCEL_EMAIL_NOTICE"),
		"CancelFeeTitle":                localization.T(lang, "CANCEL_EMAIL_CANCEL_FEE_TITLE"),
		"CancelFee":                     fmt.Sprintf("%s %s", globalLib.FormatMoney(req.CancelFee), currencyCode),
		"AskerNameTitle":                localization.T(lang, "CANCEL_EMAIL_ASKER_NAME_TITLE"),
		"AskerName":                     asker.Name,
		"PaymentMethodTitle":            localization.T(lang, "CANCEL_EMAIL_PAYMENT_METHOD_TITLE"),
		"PaymentMethod":                 globalLib.LocalizePaymentMethod(task.Payment.Method, lang),
		"CancelDetail":                  localization.T(lang, "EMAIL_TASK_CANCEL_DETAIL"),
		"TaskPlaceTitle":                localization.T(lang, "CANCEL_EMAIL_TASK_PLACE_TITLE"),
		"TaskPlace":                     task.Address,
		"ServiceNameTitle":              localization.T(lang, "CANCEL_EMAIL_SERVICE_TITLE"),
		"TaskDateTitle":                 localization.T(lang, "CANCEL_EMAIL_TASK_DATE_TITLE"),
		"TaskDate":                      FormatTime(taskDateLocal),
		"DurationTitle":                 localization.T(lang, "CANCEL_EMAIL_DURATION_TITLE"),
		"Duration":                      localization.T(lang, "CANCEL_EMAIL_DURATION", int(task.Duration)),
		"CreatedAtTitle":                localization.T(lang, "CANCEL_EMAIL_CREATED_AT_TITLE"),
		"CreatedAt":                     FormatTime(globalLib.ParseDateFromTimeStamp(task.CreatedAt, local.TimeZone)),
		"CanceledAtTitle":               localization.T(lang, "CANCEL_EMAIL_CANCELED_AT_TITLE"),
		"CanceledAt":                    FormatTime(cancelTaskLocal),
		"CancelationReasonTitle":        localization.T(lang, "CANCEL_EMAIL_CANCELATION_REASON_TITLE"),
		"CancelationReason":             cancellationReasonLocalize,
		"CancelatingFeeInformation":     localization.T(lang, "CANCEL_EMAIL_CANCELATING_FEE_INFORMATION"),
		"CancelNoticeFree":              cancelNoticeFree,
		"CancelNoticeFree1":             localization.T(lang, "CANCEL_TASK_NOTICE_FREE_CONDITION_1"),
		"CancelNoticeFree2":             localization.T(lang, "CANCEL_TASK_NOTICE_FREE_CONDITION_2"),
		"CancelNoticeFree3":             localization.T(lang, "CANCEL_TASK_NOTICE_FREE_CONDITION_3"),
		"CancelNoticeChargeFee":         localization.T(lang, "CANCEL_TASK_NOTICE_CHARGE_FEE_1"),
		"CancelNoticeChargeFee2":        localization.T(lang, "CANCEL_TASK_NOTICE_CHARGE_FEE_3", settings.CancelTaskFeeInPercentage, oneHourBeforeTaskDate),
		"FollowbTaskeeAt":               localization.T(lang, "FOLLOW_BTASKEE_AT"),
		"CancelTaskTetNoticeChargeFee":  localization.T(lang, "CANCEL_TASK_TET_NOTICE_CHARGE_FEE_1"),
		"CancelTaskTetNoticeChargeFee1": localization.T(lang, "CANCEL_TASK_TET_NOTICE_CHARGE_FEE_2"),
		"CancelTaskTetNoticeChargeFee2": localization.T(lang, "CANCEL_TASK_TET_NOTICE_CHARGE_FEE_3"),
	}

	if task.Payment.Method == globalConstant.PAYMENT_METHOD_CARD {
		emailData["IsPaymentByCard"] = true
		emailData["PaymentDetailTitle"] = localization.T(lang, "RECEIPT_EMAIL_PAYMENT_DETAIL")
		emailData["PaymentCardNumberTitle"] = localization.T(lang, "PAY_BY_CARD")
		cardNumber := task.Payment.CardNumber
		if cardNumber == "" && task.Payment.CardInfo != nil {
			cardNumber = task.Payment.CardInfo.Number
		}
		emailData["PaymentCardNumber"] = cardNumber
		emailData["PaymentStatusTitle"] = localization.T(lang, "CARD_PAYMENT_STATUS")
		emailData["PaymentStatus"] = localization.T(lang, "CARD_PAYMENT_STATUS_SUCCESS")
	} else if task.Payment.Method == globalConstant.PAYMENT_METHOD_BANK_TRANSFER {
		emailData["IsPaymentByTransfer"] = true
		emailData["PaymentDetailTitle"] = localization.T(lang, "RECEIPT_EMAIL_PAYMENT_DETAIL")
		emailData["PaymentBankNameTitle"] = localization.T(lang, "BANK_TRANSFER_METHOD")
		emailData["PaymentBankName"] = task.Payment.Bank
		emailData["PaymentStatusTitle"] = localization.T(lang, "CARD_PAYMENT_STATUS")
		emailData["PaymentStatus"] = localization.T(lang, "CARD_PAYMENT_STATUS_SUCCESS")
	}

	if lang == globalConstant.LANG_KO {
		emailData["CancelNoticeChargeFee1"] = localization.T(lang, "CANCEL_TASK_NOTICE_CHARGE_FEE_2", globalLib.FormatMoney(settings.CancelTaskFee)+currencySign, oneHourBeforeTaskDate)
	} else {
		emailData["CancelNoticeChargeFee1"] = localization.T(lang, "CANCEL_TASK_NOTICE_CHARGE_FEE_2", globalLib.FormatMoney(settings.CancelTaskFee), currencySign, oneHourBeforeTaskDate)
	}
	var isTetBooking bool
	if task.IsTetBooking {
		isTetBooking = true
	}
	emailData["IsTetBooking"] = isTetBooking
	// Check ivitation value
	referralValue, currencyCode := getReferralValueAndCurrency(task.IsoCode, currencyCode, settings)
	emailData["HasInvitation"] = true
	emailData["Invitation1"] = localization.T(lang, "INVITATION_1")
	emailData["Invitation2"] = localization.T(lang, "INVITATION_2")
	emailData["ReferralCode"] = asker.ReferralCode
	emailData["InvitationDescription"] = localization.T(lang, "INVITE_DESCRIPTION", globalLib.FormatMoney(referralValue), currencyCode, globalLib.FormatMoney(referralValue), currencyCode)

	emailData = globalLib.UpdateBtaskeeInfoInEmailByIsoCode(emailData, task.IsoCode)

	emailTemplate := fmt.Sprintf("%s/cancel-fee-email.html", cfg.EmailTemplateURL)
	body, err := ParseTemplate(emailTemplate, emailData)
	if err != nil {
		return nil, err
	}

	// NOTE: Test with cancel-booking "./test.sh 3 19"
	option := &emailSending.EmailSending{
		ReplyTo: "<EMAIL>",
		From:    "bTaskee Receipts <<EMAIL>>",
		To:      asker.Emails[0].Address,
		Subject: localization.T(lang, `EMAIL_TASK_CANCEL_SUBJECT`),
		Bcc:     []string{"<EMAIL>"},
		Content: body,
	}

	return SendEmail(option)
}
