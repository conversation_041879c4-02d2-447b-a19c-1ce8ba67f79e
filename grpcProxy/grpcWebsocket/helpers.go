package grpcWebsocket

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage"
	"go.uber.org/zap"
)

func SendSocketMessage(isoCode string, endpoint string, key string, source string, value string, messageTo []string, data map[string]interface{}) (*websocketMessage.WebsocketMessage, error) {
	var dataByte []byte
	if data != nil {
		dataByte, _ = json.Marshal(data)
	}
	request := &websocketMessage.WebsocketMessage{
		Key:       key,
		Source:    source,
		Value:     value,
		Data:      dataByte,
		MessageTo: messageTo,
	}

	client, connect, err := Connect(isoCode, endpoint)
	if err != nil {
		mode := os.Getenv("APPLICATION_MODE")
		if mode != "test" {
			globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
				zap.Error(err),
				zap.String("endpoint", endpoint),
				zap.String("source", source),
				zap.Any("messageTo", messageTo),
				zap.Any("data", data),
			)
		}
		return request, err
	}
	defer connect.Close()

	_, err = client.SendSocketMessage(context.Background(), request)
	if err != nil {
		mode := os.Getenv("APPLICATION_MODE")
		if mode != "test" {
			globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
				zap.Error(err),
				zap.Any("body", request),
			)
		}
		return request, err
	}
	return request, nil
}

func SendSocketNotification(isoCode string, arrayNotification []interface{}, endpoint string, userIds []*pushNotificationRequest.PushNotificationRequestUserIds, payloadType int32, title, body *service.ServiceText) (*websocketMessage.WebsocketMessage, error) {
	if len(arrayNotification) > 0 && len(userIds) > 0 {
		data := make(map[string]interface{})
		notifyData, _ := json.Marshal(arrayNotification[0])
		json.Unmarshal(notifyData, &data)
		data["title"] = title
		data["content"] = body
		messageTo := []string{}
		for _, v := range userIds {
			messageTo = append(messageTo, v.UserId)
		}
		wsMessage, err := SendSocketMessage(isoCode, endpoint, globalConstant.WEBSOCKET_KEY_NOTIFICATION, "", fmt.Sprintf("%d", payloadType), messageTo, data)
		return wsMessage, err
	}
	return nil, nil
}

// NOTE: Send socket task only: Must refactor task data to map in this function. Cannot use same as another key ("chat", "notification", "payment")
func SendSocketMessageTask(isoCode string, endpoint string, key string, source string, value string, messageTo []string, data *task.Task) (*websocketMessage.WebsocketMessage, error) {
	defer globalLib.Logger.Sync()

	mapTaskItems := refactorTaskItem(source, messageTo, data)

	client, connect, err := Connect(isoCode, endpoint)
	if err != nil {
		mode := os.Getenv("APPLICATION_MODE")
		if mode != "test" {
			globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
				zap.Error(err),
				zap.String("endpoint", endpoint),
				zap.String("source", source),
				zap.Any("messageTo", messageTo),
				zap.Any("data", data),
			)
		}

		return nil, err
	}
	defer connect.Close()

	for _, v := range mapTaskItems {
		var dataByte []byte
		if v != nil {
			dataByte, _ = json.Marshal(v.Data)
		}
		request := &websocketMessage.WebsocketMessage{
			Key:       key,
			Source:    source,
			Value:     value,
			Data:      dataByte,
			MessageTo: v.MessageTo,
		}

		_, err = client.SendSocketMessage(context.Background(), request)
		if err != nil {
			mode := os.Getenv("APPLICATION_MODE")
			if mode != "test" {
				globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
					zap.Error(err),
					zap.Any("body", request),
				)
			}
			return request, err
		}
	}
	return nil, nil
}
