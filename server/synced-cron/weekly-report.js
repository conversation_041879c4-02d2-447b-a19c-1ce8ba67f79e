import { Meteor } from 'meteor/meteor';
import _ from 'lodash';
import moment from 'moment';
import {
  revertTimeZone,
  trackingScheduleStatus,
} from '/server/helper-function';

const createSuggestion = function (feedbacks) {
  const result = [];
  const nOnTime = feedbacks.filter(feedback => (feedback === 'ON_TIME')).length;
  if (nOnTime === 1) result.push('Vui lòng tới đúng giờ.');
  if (nOnTime >= 2) result.push('Vui lòng đi sớm hơn.');

  const nAttitude = feedbacks.filter(feedback => (feedback === 'ATTITUDE')).length;
  if (nAttitude >= 1) result.push('Vui lòng thân thiện hơn.');
  
  const nNotComing = feedbacks.filter(feedback => (feedback === 'NOT_COMING')).length;
  if (nNotComing >= 1) result.push('Vui lòng đi làm khi bạn nhận công việc.');
  const nNotComingWithAnnounce = feedbacks.filter(feedback => (feedback === 'NOT_COMING_WITH_ANNOUCEMENT')).length;
  if (nNotComingWithAnnounce >= 1) result.push('Vui lòng đi làm khi bạn nhận công việc.');

  const nCare = feedbacks.filter(feedback => (feedback === 'CARE')).length;
  if (nCare >= 1) result.push('Vui lòng dọn dẹp cẩn thận hơn.');
  
  const nClean = feedbacks.filter(feedback => (feedback === 'CLEAN')).length;
  if (nClean >= 1) result.push('Vui lòng làm sạch sẽ hơn.');
  
  const nCheerful = feedbacks.filter(feedback => (feedback === 'CHEERFUL')).length;
  if (nCheerful >= 1) result.push('Vui lòng thân thiện hơn.');
  
  const nWorkingOutSide = feedbacks.filter(feedback => (feedback === 'WORKING_OUTSIDE')).length;
  if (nWorkingOutSide >= 1) result.push('Không được lôi kéo khách hàng làm riêng.');
  
  const nBringTools = feedbacks.filter(feedback => (feedback === 'BRING_CLEANING_TOOLS')).length;
  if (nBringTools >= 1) result.push('Vui lòng mang theo dụng cụ vệ sinh khi khách có yêu cầu thêm trên ứng dụng.');

  const nNotUniform = feedbacks.filter(feedback => (feedback === 'NOT_UNIFORMS')).length;
  if (nNotUniform >= 1) result.push('Vui lòng mặc đồng phục bTaskee khi đi làm.');

  const nProlongTime = feedbacks.filter(feedback => (feedback === 'PROLONG_TIME')).length;
  if (nProlongTime >= 1) result.push('Vui lòng khẩn trương, không kéo dài thời gian làm việc.');
  return result;
};

const localizeFeedback = function (feedbacks) {
  return feedbacks.map((feedback) => {
    switch (feedback) {
      case 'ON_TIME':
        return 'Đúng giờ';
      case 'ATTITUDE':
        return 'Thái độ';
      case 'NOT_COMING':
        return 'Không tới làm';
      case 'CARE':
        return 'Cẩn thận';
      case 'CLEAN':
        return 'Sạch sẽ';
      case 'OTHER':
        return null;
      case 'CHEERFUL':
        return 'Vui vẻ';
      case 'NOT_COMING_WITH_ANNOUCEMENT':
        return 'Báo huỷ';
      case 'WORKING_OUTSIDE':
        return 'Lôi kéo khách hàng làm riêng';
      case 'BRING_CLEANING_TOOLS':
        return 'Không mang đầy đủ dụng cụ vệ sinh theo yêu cầu';
      case 'NOT_UNIFORMS':
        return 'Không mặc đồng phục bTaskee';
      case 'PROLONG_TIME':
        return 'Kéo dài thời gian làm việc';
      default:
        return feedback;
    }
  });
};

// Base on service to calculate income of Tasker.
const getTaskCost = (task, taskerId) => {

  // Deep cleaning service: get income from detailDeepCleaning
  if ( task.serviceText && task.serviceText.en === 'Deep Cleaning' ) {
    const foundLeader = task.acceptedTasker.find((taskerItem) => {
      return taskerItem.taskerId === taskerId && taskerItem.isLeader;
    });
    if ( foundLeader ) {
      return _.get(task, 'detailDeepCleaning.costPerLeaderTasker.total', 0);
    } else {
      return _.get(task, 'detailDeepCleaning.costPerTasker.total', 0);
    }
  }
  // Other services: Get imcome from cost
  return task.cost;
};

const weeklyReport = function (weekBefore) {
  Meteor.appCollection.users.find({ type: 'TASKER', status: {$in: ['ACTIVE', 'LOCKED', 'IN_PROBATION']} }).forEach((tasker) => {
    const start = revertTimeZone(moment().subtract(weekBefore, 'weeks').startOf('isoweek').toDate());
    const end = revertTimeZone(moment().subtract(weekBefore, 'weeks').endOf('isoweek').toDate());
    let numberOfDoneTask = 0;
    let totalIncome = 0;
    let totalRating = 0;
    let countRating = 0;
    const goodRating = {
      total: 0,
      feedback: [],
      review: [],
    };
    const badRating = {
      total: 0,
      feedback: [],
      review: [],
    };
    Meteor.appCollection.Task.find({
      status: 'DONE',
      'acceptedTasker.taskerId': tasker._id,
      date: { $gte: start, $lte: end }
    }, {fields: {
      cost: 1,
      detailDeepCleaning: 1,
      rated: 1,
      serviceText: 1,
      acceptedTasker: 1,
    }}).forEach((task) => {
      numberOfDoneTask += 1;
      totalIncome += getTaskCost(task, tasker._id);
      if (task.rated) {
        const rating = Meteor.appCollection.Rating.findOne({ taskId: task._id });
        if (rating) {
          totalRating += rating.rate;
          countRating += 1;
          if (rating.rate === 5) { // good rating.
            goodRating.total += 1;
            goodRating.feedback = goodRating.feedback.concat(rating.feedBack);
            if (rating.review) goodRating.review.push(rating.review);
          } else if (rating.rate <= 4) { // bad rating.
            badRating.total += 1;
            badRating.feedback = badRating.feedback.concat(rating.feedBack);
            if (rating.review) badRating.review.push(rating.review);
          }
        }
      }
    });
    // remove old same week report
    Meteor.appCollection.TaskReportTasker.remove({
      taskerId: tasker._id,
      fromDate: start,
      toDate: end,
    });
    if (numberOfDoneTask > 0) {
      const suggestion = createSuggestion(badRating.feedback);
      goodRating.feedback = _.uniq(localizeFeedback(goodRating.feedback));
      goodRating.feedback = _.remove(goodRating.feedback, null);
      badRating.feedback = _.uniq(localizeFeedback(badRating.feedback));
      badRating.feedback = _.remove(badRating.feedback, null);
      goodRating.feedback.sort();
      badRating.feedback.sort();
      Meteor.appCollection.TaskReportTasker.insert({
        taskerId: tasker._id,
        phone: tasker.phone,
        name: tasker.name,
        createdAt: new Date(),
        fromDate: start,
        toDate: end,
        numberOfDoneTask,
        avgRating: totalRating > 0 && countRating > 0 ? totalRating / countRating : 0,
        totalIncome,
        suggestion,
        goodRating,
        badRating,
      });
    }
  });
};

const runWeeklyReport = function() {
  return;
	console.log("Start runWeeklyReport ...");
  var message = null;
  try {
    // Fix with running at 20:00 Sunday weekly
    weeklyReport(0);
    weeklyReport(1);
  } catch (e) {
    message = e;
  }
	console.log("Finished runWeeklyReport ");
  // Tracking
  trackingScheduleStatus({name: 'TaskerWeeklyReport', nextRunAt: SyncedCron.nextScheduledAtDate('weeklyReport'), message});
};

  Meteor.methods({
    weeklyReport() {
      SyncedCron.add({
        name: 'weeklyReport',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 20)).getHours();
          // Run at 20:00 Sunday weekly
          // Fixed synced cron. Check carefully when change running schedule 
          return parser.cron(`0 0 ${hourInTimeZoneServer} ? * SUN *`, true);
        },
        job() {
          runWeeklyReport();
        }
      });
	    console.log("Added weeklyReport cron");
    },
    'runNow.weeklyReport'() {
      runWeeklyReport();
    },
  });
