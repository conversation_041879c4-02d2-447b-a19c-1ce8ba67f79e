import { Meteor } from 'meteor/meteor';
import {
  revertTimeZone,
  trackingScheduleStatus,
  postToSlack,
  sendNotificationByIds,
} from '/server/helper-function';
import _ from 'lodash';
import moment from 'moment';

const lockTaskerInactive = () => {
  return;
  var message = null;
  try {
    const settings = Meteor.appCollection.SettingSystem.findOne({});
    const testers = _.get(settings, 'tester', []);
    const last14Days = moment().subtract(14, 'days').toDate();
    
    // Exception 1: Taskers in WAITING, CONFIRMED tasks
    let exeptionList = [];
    Meteor.appCollection.Task.find({
      status: {$in: ['WAITING_ASKER_CONFIRMATION', 'CONFIRMED']}
    }, {fields: {acceptedTasker: 1, status: 1}}).forEach((task) => {
      if ( task.status === 'CONFIRMED' ) {
        exeptionList.push(task.acceptedTasker[0].taskerId);
      } else {
        task.acceptedTasker.forEach((tasker) => {
          exeptionList.push(tasker.taskerId);
        });
      }
    });

    // Exception 2: Tasker re-active in 3 days or Tasker was verified in 3days
    const last3Days = revertTimeZone(moment().subtract(3, 'days').startOf('day').toDate());
    Meteor.appCollection.users.find({
      type: 'TASKER',
      status: 'ACTIVE',
      isoCode: "VN",
      $or: [
        {lastActiveAt: {$gte: last3Days}},
        {'verifyHistory.verifiedAt': {$gte: last3Days}}
      ]
      
    }, {
      fields: { _id: 1 } }).forEach((tasker) => {
      exeptionList.push(tasker._id);
    });

    // Exception 3: Home cooking Taskers
    Meteor.appCollection.Service.find({
      'text.en': {$in: ['Home Cooking', 'Disinfection Service', 'Upholstery Service']}
    }, {fields: {_id: 1}}).forEach(service => {
      const channel = Meteor.appCollection.ServiceChannel.findOne({ serviceId: service._id });
      if ( channel && channel.taskerList && channel.taskerList.length > 0 ) {
        channel.taskerList.forEach((taskerInChannel) => exeptionList.push(taskerInChannel));
      }
    });
    
    let taskerIds = [];

    const findCondition = {
      type: "TASKER",
      status: "ACTIVE",
      isoCode: "VN",
      employeeIds: null,
      company: null,
      phone: {$nin: testers},
      $or: [{ lastDoneTask: null }, { lastDoneTask: {$lte: last14Days} }],
      createdAt: {$lte: last14Days},
      _id: {$nin: exeptionList},
    };

    let taskersInfo = '';
    
    const taskers = Meteor.appCollection.users.find(findCondition, {fields: {phone: 1, name: 1, workingPlaces: 1}}).map((tasker) => {
      taskersInfo += `${tasker.phone} - ${tasker.name} - ${tasker.workingPlaces && tasker.workingPlaces.length > 0 ? tasker.workingPlaces[0].city : ''}\n`
      taskerIds.push(tasker._id);
      return tasker;
    });

    if ( taskerIds && taskerIds.length > 0 ) {
      // Update Taskers's status are LOCKED
      Meteor.appCollection.users.update({ _id: {$in: taskerIds} }, {
        $set: {
          status: 'LOCKED',
          updatedAt: new Date(),
        }
      }, {multi: true});
      // Send notification with force view
      sendNotificationByIds(taskerIds, {
        title: {
          vi: 'Tài khoản của bạn bị tạm khóa.',
          en: 'Your account has been blocked.',
          ko: '계정이 일시적으로 잠겨습니다.',
          th: 'บัญชีของคุณถูกล็อกการใช้งาน',
        },
        text: {
          vi: 'Tài khoản của bạn bị tạm khóa theo qui định của bTaskee về chất lượng dịch vụ. Trong 14 ngày qua, bạn không nhận việc và đi làm trên ứng dụng bTaskee. Vui lòng liên hệ bTaskee trong giờ hành chính nếu bạn cần hỗ trợ.',
          en: "Your account is temporarily locked according to bTaskee's regulations on service quality. During the past 14 days, you have not accepted any task or worked on bTaskee app. Please contact bTaskee within office hours if you need support.",
          ko: "bTaskee의 서비스 품질 규정에 따라 계정이 일시적으로 잠깁니다. 지난 14일 동안 bTaskee 앱에 출근하려 스케줄을 받지 않했습니다 도움이 필요한 경우 근무 시간 중에 bTaskee에 문의하십시오.",
          th: "บัญชีของคุณถูกล็อกการใช้งานจากคุณภาพของการให้บริการที่ทางบริษัทกำหนด เนื่องจากช่วง 14 วันที่ผ่านมาคุณไม่ได้รับงานหรือทำงานใดๆ โปรดติดต่อบริษัทในเวลาทำการเพื่อรับการช่วยเหลือ",
        },
        payload: {},
      }, { isForceView: true, type: 30 });
      
      // Insert UserActionHistory
      taskers.forEach((tasker) => {
        Meteor.appCollection.UserActionHistory.insert({
          phone: tasker.phone,
          name: tasker.name,
          action: 'BLOCK_TASKER',
          data: {
            taskerId: tasker._id,
            reason: 'Hơn 14 ngày không làm việc. Liên hệ công ty khi muốn đi làm lại.',
            city: tasker.workingPlaces && tasker.workingPlaces.length > 0 ? tasker.workingPlaces[0].city : '',
            supporter: 'Auto System'
          },
          createdAt: new Date(),
        });
      });

      // Send locked information to slack
      
      postToSlack({
        channel: 'tasker-blocked',
        text: `[ĐÃ KHÓA] Tasker không làm việc trong 14 ngày.\n${taskersInfo}`,
      });
    }
  } catch(ex) {
    message = ex;
    // Tracking
    trackingScheduleStatus({name: 'autoLockTaskerInactiveOneMonth', nextRunAt: SyncedCron.nextScheduledAtDate('autoLockTaskerInactiveOneMonth'), message});
  }
  
};

export default function () {
  Meteor.methods({
    // Auto Done Task
    autoLockTaskerInactiveOneMonth() {
      SyncedCron.add({
        name: 'autoLockTaskerInactiveOneMonth',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 11)).getHours();
          // Run at 11:30 +7GMT everyday
          return parser.cron(`0 30 ${hourInTimeZoneServer} 1/1 * ? *`, true);
        },
        job() {
          lockTaskerInactive();
        },
      });
    },
    'runNow.autoLockTaskerInactiveOneMonth'() {
      lockTaskerInactive();
    },
  });
}
