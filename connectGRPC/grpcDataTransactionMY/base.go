package grpcDataTransactionMY

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcDataTransactionMY"
	"google.golang.org/grpc"
)

func ConnectGRPCDataTransaction(endpoint string) (grpcDataTransactionMY.DataTransactionClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcDataTransactionMY.NewDataTransactionClient(connect)
	return client, connect, err
}
