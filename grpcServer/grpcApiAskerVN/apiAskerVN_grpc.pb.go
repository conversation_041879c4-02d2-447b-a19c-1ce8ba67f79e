// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcApiAskerVN

import (
	context "context"
	redeemGiftRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/redeemGiftRequest"
	redeemGiftResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/redeemGiftResponse"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// ApiAskerVNClient is the client API for ApiAskerVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ApiAskerVNClient interface {
	RedeemGift(ctx context.Context, in *redeemGiftRequest.RedeemGiftRequest, opts ...grpc.CallOption) (*redeemGiftResponse.RedeemGiftResponse, error)
}

type apiAskerVNClient struct {
	cc grpc.ClientConnInterface
}

func NewApiAskerVNClient(cc grpc.ClientConnInterface) ApiAskerVNClient {
	return &apiAskerVNClient{cc}
}

func (c *apiAskerVNClient) RedeemGift(ctx context.Context, in *redeemGiftRequest.RedeemGiftRequest, opts ...grpc.CallOption) (*redeemGiftResponse.RedeemGiftResponse, error) {
	out := new(redeemGiftResponse.RedeemGiftResponse)
	err := c.cc.Invoke(ctx, "/grpcApiAskerVN.ApiAskerVN/RedeemGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ApiAskerVNServer is the server API for ApiAskerVN service.
// All implementations must embed UnimplementedApiAskerVNServer
// for forward compatibility
type ApiAskerVNServer interface {
	RedeemGift(context.Context, *redeemGiftRequest.RedeemGiftRequest) (*redeemGiftResponse.RedeemGiftResponse, error)
	mustEmbedUnimplementedApiAskerVNServer()
}

// UnimplementedApiAskerVNServer must be embedded to have forward compatible implementations.
type UnimplementedApiAskerVNServer struct {
}

func (UnimplementedApiAskerVNServer) RedeemGift(context.Context, *redeemGiftRequest.RedeemGiftRequest) (*redeemGiftResponse.RedeemGiftResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RedeemGift not implemented")
}
func (UnimplementedApiAskerVNServer) mustEmbedUnimplementedApiAskerVNServer() {}

// UnsafeApiAskerVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ApiAskerVNServer will
// result in compilation errors.
type UnsafeApiAskerVNServer interface {
	mustEmbedUnimplementedApiAskerVNServer()
}

func RegisterApiAskerVNServer(s *grpc.Server, srv ApiAskerVNServer) {
	s.RegisterService(&_ApiAskerVN_serviceDesc, srv)
}

func _ApiAskerVN_RedeemGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(redeemGiftRequest.RedeemGiftRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ApiAskerVNServer).RedeemGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcApiAskerVN.ApiAskerVN/RedeemGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ApiAskerVNServer).RedeemGift(ctx, req.(*redeemGiftRequest.RedeemGiftRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _ApiAskerVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcApiAskerVN.ApiAskerVN",
	HandlerType: (*ApiAskerVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "RedeemGift",
			Handler:    _ApiAskerVN_RedeemGift_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "apiAskerVN.proto",
}
