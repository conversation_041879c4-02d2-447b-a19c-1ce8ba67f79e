// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcDoneTaskVN

import (
	context "context"
	doneBookingRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/doneBookingRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// DoneBookingVNClient is the client API for DoneBookingVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DoneBookingVNClient interface {
	PartnerDone(ctx context.Context, in *doneBookingRequest.DoneBookingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type doneBookingVNClient struct {
	cc grpc.ClientConnInterface
}

func NewDoneBookingVNClient(cc grpc.ClientConnInterface) DoneBookingVNClient {
	return &doneBookingVNClient{cc}
}

func (c *doneBookingVNClient) PartnerDone(ctx context.Context, in *doneBookingRequest.DoneBookingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/grpcDoneTaskVN.DoneBookingVN/PartnerDone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DoneBookingVNServer is the server API for DoneBookingVN service.
// All implementations must embed UnimplementedDoneBookingVNServer
// for forward compatibility
type DoneBookingVNServer interface {
	PartnerDone(context.Context, *doneBookingRequest.DoneBookingRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedDoneBookingVNServer()
}

// UnimplementedDoneBookingVNServer must be embedded to have forward compatible implementations.
type UnimplementedDoneBookingVNServer struct {
}

func (UnimplementedDoneBookingVNServer) PartnerDone(context.Context, *doneBookingRequest.DoneBookingRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartnerDone not implemented")
}
func (UnimplementedDoneBookingVNServer) mustEmbedUnimplementedDoneBookingVNServer() {}

// UnsafeDoneBookingVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DoneBookingVNServer will
// result in compilation errors.
type UnsafeDoneBookingVNServer interface {
	mustEmbedUnimplementedDoneBookingVNServer()
}

func RegisterDoneBookingVNServer(s *grpc.Server, srv DoneBookingVNServer) {
	s.RegisterService(&_DoneBookingVN_serviceDesc, srv)
}

func _DoneBookingVN_PartnerDone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(doneBookingRequest.DoneBookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoneBookingVNServer).PartnerDone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDoneTaskVN.DoneBookingVN/PartnerDone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoneBookingVNServer).PartnerDone(ctx, req.(*doneBookingRequest.DoneBookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _DoneBookingVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcDoneTaskVN.DoneBookingVN",
	HandlerType: (*DoneBookingVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PartnerDone",
			Handler:    _DoneBookingVN_PartnerDone_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "doneBookingVN.proto",
}
