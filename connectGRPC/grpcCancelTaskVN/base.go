package grpcCancelTaskVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcCancelTaskVN"
	"google.golang.org/grpc"
)

func ConnectGRPCCancelTaskVN(endpoint string) (grpcCancelTaskVN.CancelTaskVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcCancelTaskVN.NewCancelTaskVNClient(connect)
	return client, connect, err
}
