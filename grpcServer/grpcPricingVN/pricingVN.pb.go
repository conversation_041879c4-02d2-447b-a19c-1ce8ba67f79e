// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.7.1
// source: pricingVN.proto

package grpcPricingVN

import (
	pricingSubscriptionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionRequest"
	pricingSubscriptionResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionResponse"
	pricingrequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingrequest"
	pricingresponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_pricingVN_proto protoreflect.FileDescriptor

var file_pricingVN_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x56, 0x4e, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0d, 0x67, 0x72, 0x70, 0x63, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x56, 0x4e,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x56,
	0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b,
	0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x58, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f,
	0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x2d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x6f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61,
	0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2f, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x71, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74,
	0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0xe7, 0x29, 0x0a, 0x09, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x56, 0x4e, 0x12, 0x86, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x57, 0x61, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x12,
	0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2d, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x27, 0x22, 0x22, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x77, 0x61, 0x73, 0x68, 0x69, 0x6e, 0x67,
	0x2d, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x86, 0x01, 0x0a, 0x18,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x41, 0x69, 0x72, 0x43, 0x6f, 0x6e,
	0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x22, 0x22, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76,
	0x6e, 0x2f, 0x61, 0x69, 0x72, 0x2d, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65,
	0x72, 0x3a, 0x01, 0x2a, 0x12, 0x82, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x48, 0x6f, 0x6d, 0x65, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x12,
	0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2b, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x25, 0x22, 0x20, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x68, 0x6f, 0x6d, 0x65, 0x2d, 0x63, 0x6c,
	0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0xb6, 0x01, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x22, 0x1f,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d,
	0x76, 0x6e, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x3a,
	0x01, 0x2a, 0x12, 0x82, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x44, 0x65, 0x65, 0x70, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x25, 0x22, 0x20, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x2d, 0x63, 0x6c, 0x65, 0x61,
	0x6e, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0x80, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x6d, 0x65, 0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x6e,
	0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2a,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x22, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x68, 0x6f, 0x6d, 0x65, 0x2d,
	0x63, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0x77, 0x0a, 0x11, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x4c, 0x61, 0x75, 0x6e, 0x64, 0x72, 0x79, 0x12,
	0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x25, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x1f, 0x22, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x6c, 0x61, 0x75, 0x6e, 0x64, 0x72, 0x79,
	0x3a, 0x01, 0x2a, 0x12, 0x81, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x4c, 0x61, 0x75, 0x6e, 0x64, 0x72, 0x79, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x22, 0x25, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76,
	0x6e, 0x2f, 0x72, 0x65, 0x2d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x6c, 0x61, 0x75,
	0x6e, 0x64, 0x72, 0x79, 0x3a, 0x01, 0x2a, 0x12, 0x8a, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x47, 0x72, 0x6f, 0x63, 0x65, 0x72, 0x79, 0x41, 0x73, 0x73,
	0x69, 0x73, 0x74, 0x61, 0x6e, 0x74, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x22, 0x24, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f,
	0x67, 0x72, 0x6f, 0x63, 0x65, 0x72, 0x79, 0x2d, 0x61, 0x73, 0x73, 0x69, 0x73, 0x74, 0x61, 0x6e,
	0x74, 0x3a, 0x01, 0x2a, 0x12, 0x71, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x53, 0x6f, 0x66, 0x61, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x22, 0x17, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f,
	0x73, 0x6f, 0x66, 0x61, 0x3a, 0x01, 0x2a, 0x12, 0x81, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x75, 0x73, 0x65, 0x6b, 0x65, 0x65, 0x70, 0x69,
	0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x22, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33,
	0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x68, 0x6f, 0x75, 0x73,
	0x65, 0x6b, 0x65, 0x65, 0x70, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0x80, 0x01, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x45, 0x6c, 0x64, 0x65, 0x72, 0x6c,
	0x79, 0x43, 0x61, 0x72, 0x65, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x22, 0x1f, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x65,
	0x6c, 0x64, 0x65, 0x72, 0x6c, 0x79, 0x2d, 0x63, 0x61, 0x72, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x80,
	0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x50, 0x61, 0x74,
	0x69, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x65, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x22, 0x1f, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76,
	0x6e, 0x2f, 0x70, 0x61, 0x74, 0x69, 0x65, 0x6e, 0x74, 0x2d, 0x63, 0x61, 0x72, 0x65, 0x3a, 0x01,
	0x2a, 0x12, 0x81, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x44, 0x69, 0x73, 0x69, 0x6e, 0x66, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43,
	0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x24, 0x22, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x64, 0x69, 0x73, 0x69, 0x6e, 0x66, 0x65, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x3a, 0x01, 0x2a, 0x12, 0x59, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e,
	0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00,
	0x12, 0x89, 0x01, 0x0a, 0x1b, 0x41, 0x50, 0x49, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67,
	0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2d, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x27, 0x22, 0x22, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x65,
	0x2d, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0x7d, 0x0a, 0x12,
	0x52, 0x65, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x22, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33,
	0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x72, 0x65, 0x2d, 0x63,
	0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x76, 0x0a, 0x25, 0x52,
	0x65, 0x43, 0x61, 0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x46, 0x6f, 0x72, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x44, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x2e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x63,
	0x72, 0x65, 0x61, 0x73, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x00, 0x12, 0xce, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x61,
	0x74, 0x69, 0x65, 0x6e, 0x74, 0x43, 0x61, 0x72, 0x65, 0x12, 0x36, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x38, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x37, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x31, 0x22, 0x2c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2d, 0x70, 0x61, 0x74, 0x69, 0x65, 0x6e, 0x74, 0x2d, 0x63, 0x61, 0x72,
	0x65, 0x3a, 0x01, 0x2a, 0x12, 0xce, 0x01, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x6c, 0x64, 0x65, 0x72, 0x6c, 0x79, 0x43, 0x61, 0x72, 0x65, 0x12, 0x36, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x38, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x37, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x31, 0x22, 0x2c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2d, 0x65, 0x6c, 0x64, 0x65, 0x72, 0x6c, 0x79, 0x2d, 0x63, 0x61,
	0x72, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x7c, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x43, 0x68, 0x69, 0x6c, 0x64, 0x43, 0x61, 0x72, 0x65, 0x12, 0x1e, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43,
	0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x22, 0x22, 0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x2d, 0x63, 0x61, 0x72, 0x65,
	0x3a, 0x01, 0x2a, 0x12, 0xca, 0x01, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x68,
	0x69, 0x6c, 0x64, 0x43, 0x61, 0x72, 0x65, 0x12, 0x36, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x38, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x35, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x2f, 0x22, 0x2a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2d, 0x63, 0x68, 0x69, 0x6c, 0x64, 0x2d, 0x63, 0x61, 0x72, 0x65, 0x3a, 0x01, 0x2a,
	0x12, 0x9a, 0x01, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x66, 0x66, 0x69, 0x63,
	0x65, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x36, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x38, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xd7, 0x01,
	0x0a, 0x27, 0x41, 0x50, 0x49, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x66, 0x66, 0x69, 0x63,
	0x65, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x36, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x38, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3a, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x34, 0x22, 0x2f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x2d, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x2d, 0x63, 0x6c, 0x65, 0x61,
	0x6e, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0x80, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x74, 0x65, 0x72, 0x48, 0x65, 0x61, 0x74, 0x65,
	0x72, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2a,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x22, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x77, 0x61, 0x74, 0x65, 0x72,
	0x2d, 0x68, 0x65, 0x61, 0x74, 0x65, 0x72, 0x3a, 0x01, 0x2a, 0x12, 0x86, 0x01, 0x0a, 0x18, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x43, 0x61, 0x72, 0x70, 0x65, 0x74, 0x43,
	0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x22, 0x22, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e,
	0x2f, 0x63, 0x61, 0x72, 0x70, 0x65, 0x74, 0x2d, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67,
	0x3a, 0x01, 0x2a, 0x12, 0x7e, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x48, 0x6f, 0x6d, 0x65, 0x4d, 0x6f, 0x76, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23,
	0x22, 0x1e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x68, 0x6f, 0x6d, 0x65, 0x2d, 0x6d, 0x6f, 0x76, 0x69, 0x6e, 0x67,
	0x3a, 0x01, 0x2a, 0x12, 0x80, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x48, 0x6f, 0x6d, 0x65, 0x4d, 0x6f, 0x76, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26,
	0x22, 0x21, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x72, 0x65, 0x2d, 0x68, 0x6f, 0x6d, 0x65, 0x2d, 0x6d, 0x6f, 0x76,
	0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0x8c, 0x01, 0x0a, 0x1c, 0x41, 0x50, 0x49, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x65,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x22, 0x24, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e,
	0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2d, 0x64, 0x61, 0x74, 0x65, 0x2d, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x8b, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x41, 0x69, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x65, 0x72, 0x56, 0x32, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x22, 0x25, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x61, 0x69,
	0x72, 0x2d, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x2d, 0x76, 0x32,
	0x3a, 0x01, 0x2a, 0x12, 0x86, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x48, 0x6f, 0x75, 0x73, 0x65, 0x6b, 0x65, 0x65, 0x70, 0x69, 0x6e, 0x67, 0x56, 0x32,
	0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2d, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x27, 0x22, 0x22, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x6b,
	0x65, 0x65, 0x70, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x32, 0x3a, 0x01, 0x2a, 0x12, 0x8e, 0x01, 0x0a,
	0x1c, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x64, 0x75, 0x73,
	0x74, 0x72, 0x69, 0x61, 0x6c, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x31, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x2b, 0x22, 0x26, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x69, 0x6e, 0x64, 0x75, 0x73, 0x74, 0x72, 0x69, 0x61,
	0x6c, 0x2d, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0x7e, 0x0a,
	0x14, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x42, 0x65, 0x61, 0x75, 0x74,
	0x79, 0x43, 0x61, 0x72, 0x65, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x22, 0x1e, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x35, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x62,
	0x65, 0x61, 0x75, 0x74, 0x79, 0x2d, 0x63, 0x61, 0x72, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x88, 0x01,
	0x0a, 0x13, 0x52, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x42, 0x65, 0x61, 0x75, 0x74,
	0x79, 0x43, 0x61, 0x72, 0x65, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x34, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2e, 0x22, 0x29, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x35, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x72,
	0x65, 0x2d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x62, 0x65, 0x61, 0x75, 0x74, 0x79,
	0x2d, 0x63, 0x61, 0x72, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x75, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x6b, 0x65, 0x75, 0x70, 0x12, 0x1e, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43,
	0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1e, 0x22, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x35, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x6d, 0x61, 0x6b, 0x65, 0x75, 0x70, 0x3a, 0x01, 0x2a, 0x12,
	0x71, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x4e, 0x61, 0x69,
	0x6c, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x22,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1c, 0x22, 0x17, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x35, 0x2f,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x6e, 0x61, 0x69, 0x6c, 0x3a,
	0x01, 0x2a, 0x12, 0x80, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x48, 0x61, 0x69, 0x72, 0x53, 0x74, 0x79, 0x6c, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43,
	0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x24, 0x22, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x35, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2d, 0x76, 0x6e, 0x2f, 0x68, 0x61, 0x69, 0x72, 0x2d, 0x73, 0x74, 0x79, 0x6c, 0x69,
	0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0x77, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x49, 0x72, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x22,
	0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x35, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x2d, 0x76, 0x6e, 0x2f, 0x69, 0x72, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x42, 0x42,
	0x5a, 0x40, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61,
	0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x56, 0x4e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_pricingVN_proto_goTypes = []interface{}{
	(*pricingrequest.PricingRequest)(nil),                           // 0: pricingrequest.PricingRequest
	(*pricingSubscriptionRequest.PricingSubscriptionRequest)(nil),   // 1: pricingSubscriptionRequest.PricingSubscriptionRequest
	(*pricingrequest.PricingIncreaseDurationRequest)(nil),           // 2: pricingrequest.PricingIncreaseDurationRequest
	(*pricingresponse.CostResult)(nil),                              // 3: pricingresponse.CostResult
	(*pricingSubscriptionResponse.PricingSubscriptionResponse)(nil), // 4: pricingSubscriptionResponse.PricingSubscriptionResponse
}
var file_pricingVN_proto_depIdxs = []int32{
	0,  // 0: grpcPricingVN.PricingVN.GetPricingWashingMachine:input_type -> pricingrequest.PricingRequest
	0,  // 1: grpcPricingVN.PricingVN.GetPricingAirConditioner:input_type -> pricingrequest.PricingRequest
	0,  // 2: grpcPricingVN.PricingVN.GetPricingHomeCleaning:input_type -> pricingrequest.PricingRequest
	1,  // 3: grpcPricingVN.PricingVN.GetPricingSubscription:input_type -> pricingSubscriptionRequest.PricingSubscriptionRequest
	0,  // 4: grpcPricingVN.PricingVN.GetPricingDeepCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 5: grpcPricingVN.PricingVN.GetPricingHomeCooking:input_type -> pricingrequest.PricingRequest
	0,  // 6: grpcPricingVN.PricingVN.GetPricingLaundry:input_type -> pricingrequest.PricingRequest
	0,  // 7: grpcPricingVN.PricingVN.RePricingLaundry:input_type -> pricingrequest.PricingRequest
	0,  // 8: grpcPricingVN.PricingVN.GetPricingGroceryAssistant:input_type -> pricingrequest.PricingRequest
	0,  // 9: grpcPricingVN.PricingVN.GetPricingSofa:input_type -> pricingrequest.PricingRequest
	0,  // 10: grpcPricingVN.PricingVN.GetPricingHousekeeping:input_type -> pricingrequest.PricingRequest
	0,  // 11: grpcPricingVN.PricingVN.GetPricingElderlyCare:input_type -> pricingrequest.PricingRequest
	0,  // 12: grpcPricingVN.PricingVN.GetPricingPatientCare:input_type -> pricingrequest.PricingRequest
	0,  // 13: grpcPricingVN.PricingVN.GetPricingDisinfection:input_type -> pricingrequest.PricingRequest
	0,  // 14: grpcPricingVN.PricingVN.GetPricingOfficeCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 15: grpcPricingVN.PricingVN.APIGetPricingOfficeCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 16: grpcPricingVN.PricingVN.ReCalculatePricing:input_type -> pricingrequest.PricingRequest
	2,  // 17: grpcPricingVN.PricingVN.ReCalculatePricingForIncreaseDuration:input_type -> pricingrequest.PricingIncreaseDurationRequest
	1,  // 18: grpcPricingVN.PricingVN.GetPricingSubscriptionPatientCare:input_type -> pricingSubscriptionRequest.PricingSubscriptionRequest
	1,  // 19: grpcPricingVN.PricingVN.GetPricingSubscriptionElderlyCare:input_type -> pricingSubscriptionRequest.PricingSubscriptionRequest
	0,  // 20: grpcPricingVN.PricingVN.GetPricingChildCare:input_type -> pricingrequest.PricingRequest
	1,  // 21: grpcPricingVN.PricingVN.GetPricingSubscriptionChildCare:input_type -> pricingSubscriptionRequest.PricingSubscriptionRequest
	1,  // 22: grpcPricingVN.PricingVN.GetPricingSubscriptionOfficeCleaning:input_type -> pricingSubscriptionRequest.PricingSubscriptionRequest
	1,  // 23: grpcPricingVN.PricingVN.APIGetPricingSubscriptionOfficeCleaning:input_type -> pricingSubscriptionRequest.PricingSubscriptionRequest
	0,  // 24: grpcPricingVN.PricingVN.GetPricingWaterHeater:input_type -> pricingrequest.PricingRequest
	0,  // 25: grpcPricingVN.PricingVN.GetPricingCarpetCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 26: grpcPricingVN.PricingVN.GetPricingHomeMoving:input_type -> pricingrequest.PricingRequest
	0,  // 27: grpcPricingVN.PricingVN.RePricingHomeMoving:input_type -> pricingrequest.PricingRequest
	0,  // 28: grpcPricingVN.PricingVN.APIGetPricingTaskDateOptions:input_type -> pricingrequest.PricingRequest
	0,  // 29: grpcPricingVN.PricingVN.GetPricingAirConditionerV2:input_type -> pricingrequest.PricingRequest
	0,  // 30: grpcPricingVN.PricingVN.GetPricingHousekeepingV2:input_type -> pricingrequest.PricingRequest
	0,  // 31: grpcPricingVN.PricingVN.GetPricingIndustrialCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 32: grpcPricingVN.PricingVN.GetPricingBeautyCare:input_type -> pricingrequest.PricingRequest
	0,  // 33: grpcPricingVN.PricingVN.RePricingBeautyCare:input_type -> pricingrequest.PricingRequest
	0,  // 34: grpcPricingVN.PricingVN.GetPricingMakeup:input_type -> pricingrequest.PricingRequest
	0,  // 35: grpcPricingVN.PricingVN.GetPricingNail:input_type -> pricingrequest.PricingRequest
	0,  // 36: grpcPricingVN.PricingVN.GetPricingHairStyling:input_type -> pricingrequest.PricingRequest
	0,  // 37: grpcPricingVN.PricingVN.GetPricingIroning:input_type -> pricingrequest.PricingRequest
	3,  // 38: grpcPricingVN.PricingVN.GetPricingWashingMachine:output_type -> pricingresponse.CostResult
	3,  // 39: grpcPricingVN.PricingVN.GetPricingAirConditioner:output_type -> pricingresponse.CostResult
	3,  // 40: grpcPricingVN.PricingVN.GetPricingHomeCleaning:output_type -> pricingresponse.CostResult
	4,  // 41: grpcPricingVN.PricingVN.GetPricingSubscription:output_type -> pricingSubscriptionResponse.PricingSubscriptionResponse
	3,  // 42: grpcPricingVN.PricingVN.GetPricingDeepCleaning:output_type -> pricingresponse.CostResult
	3,  // 43: grpcPricingVN.PricingVN.GetPricingHomeCooking:output_type -> pricingresponse.CostResult
	3,  // 44: grpcPricingVN.PricingVN.GetPricingLaundry:output_type -> pricingresponse.CostResult
	3,  // 45: grpcPricingVN.PricingVN.RePricingLaundry:output_type -> pricingresponse.CostResult
	3,  // 46: grpcPricingVN.PricingVN.GetPricingGroceryAssistant:output_type -> pricingresponse.CostResult
	3,  // 47: grpcPricingVN.PricingVN.GetPricingSofa:output_type -> pricingresponse.CostResult
	3,  // 48: grpcPricingVN.PricingVN.GetPricingHousekeeping:output_type -> pricingresponse.CostResult
	3,  // 49: grpcPricingVN.PricingVN.GetPricingElderlyCare:output_type -> pricingresponse.CostResult
	3,  // 50: grpcPricingVN.PricingVN.GetPricingPatientCare:output_type -> pricingresponse.CostResult
	3,  // 51: grpcPricingVN.PricingVN.GetPricingDisinfection:output_type -> pricingresponse.CostResult
	3,  // 52: grpcPricingVN.PricingVN.GetPricingOfficeCleaning:output_type -> pricingresponse.CostResult
	3,  // 53: grpcPricingVN.PricingVN.APIGetPricingOfficeCleaning:output_type -> pricingresponse.CostResult
	3,  // 54: grpcPricingVN.PricingVN.ReCalculatePricing:output_type -> pricingresponse.CostResult
	3,  // 55: grpcPricingVN.PricingVN.ReCalculatePricingForIncreaseDuration:output_type -> pricingresponse.CostResult
	4,  // 56: grpcPricingVN.PricingVN.GetPricingSubscriptionPatientCare:output_type -> pricingSubscriptionResponse.PricingSubscriptionResponse
	4,  // 57: grpcPricingVN.PricingVN.GetPricingSubscriptionElderlyCare:output_type -> pricingSubscriptionResponse.PricingSubscriptionResponse
	3,  // 58: grpcPricingVN.PricingVN.GetPricingChildCare:output_type -> pricingresponse.CostResult
	4,  // 59: grpcPricingVN.PricingVN.GetPricingSubscriptionChildCare:output_type -> pricingSubscriptionResponse.PricingSubscriptionResponse
	4,  // 60: grpcPricingVN.PricingVN.GetPricingSubscriptionOfficeCleaning:output_type -> pricingSubscriptionResponse.PricingSubscriptionResponse
	4,  // 61: grpcPricingVN.PricingVN.APIGetPricingSubscriptionOfficeCleaning:output_type -> pricingSubscriptionResponse.PricingSubscriptionResponse
	3,  // 62: grpcPricingVN.PricingVN.GetPricingWaterHeater:output_type -> pricingresponse.CostResult
	3,  // 63: grpcPricingVN.PricingVN.GetPricingCarpetCleaning:output_type -> pricingresponse.CostResult
	3,  // 64: grpcPricingVN.PricingVN.GetPricingHomeMoving:output_type -> pricingresponse.CostResult
	3,  // 65: grpcPricingVN.PricingVN.RePricingHomeMoving:output_type -> pricingresponse.CostResult
	3,  // 66: grpcPricingVN.PricingVN.APIGetPricingTaskDateOptions:output_type -> pricingresponse.CostResult
	3,  // 67: grpcPricingVN.PricingVN.GetPricingAirConditionerV2:output_type -> pricingresponse.CostResult
	3,  // 68: grpcPricingVN.PricingVN.GetPricingHousekeepingV2:output_type -> pricingresponse.CostResult
	3,  // 69: grpcPricingVN.PricingVN.GetPricingIndustrialCleaning:output_type -> pricingresponse.CostResult
	3,  // 70: grpcPricingVN.PricingVN.GetPricingBeautyCare:output_type -> pricingresponse.CostResult
	3,  // 71: grpcPricingVN.PricingVN.RePricingBeautyCare:output_type -> pricingresponse.CostResult
	3,  // 72: grpcPricingVN.PricingVN.GetPricingMakeup:output_type -> pricingresponse.CostResult
	3,  // 73: grpcPricingVN.PricingVN.GetPricingNail:output_type -> pricingresponse.CostResult
	3,  // 74: grpcPricingVN.PricingVN.GetPricingHairStyling:output_type -> pricingresponse.CostResult
	3,  // 75: grpcPricingVN.PricingVN.GetPricingIroning:output_type -> pricingresponse.CostResult
	38, // [38:76] is the sub-list for method output_type
	0,  // [0:38] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_pricingVN_proto_init() }
func file_pricingVN_proto_init() {
	if File_pricingVN_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pricingVN_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pricingVN_proto_goTypes,
		DependencyIndexes: file_pricingVN_proto_depIdxs,
	}.Build()
	File_pricingVN_proto = out.File
	file_pricingVN_proto_rawDesc = nil
	file_pricingVN_proto_goTypes = nil
	file_pricingVN_proto_depIdxs = nil
}
