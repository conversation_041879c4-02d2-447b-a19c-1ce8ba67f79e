syntax = "proto3";
package grpcTranslate;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcTranslate";

import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/translateMessage/translateMessage.proto";

service Translate {
  rpc Translate (translateMessage.TranslateRequest) returns (translateMessage.TranslateResponse) {}
  rpc DetectLanguage (translateMessage.DetectLanguageRequest) returns (translateMessage.DetectLanguageResponse) {}
}