package grpcLocking

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcLocking"
	"google.golang.org/grpc"
)

func ConnectGRPCLocking(endpoint string) (grpcLocking.LockingClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcLocking.NewLockingClient(connect)
	return client, connect, err
}
