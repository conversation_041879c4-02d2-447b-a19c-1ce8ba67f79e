package testing

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/config"
	libUser "gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/lib/user"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalRepo"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"go.mongodb.org/mongo-driver/bson"
)

var cfgTest = config.GetConfig()

func init() {
	cfgTest.MongoDriver.Get("btaskee").InitDriver(false)
}

func ResetData() {
	// Remove User
	libUser.DeleteAllByQuery(bson.M{})

	// Remove Task
	globalRepo.DeleteAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{})

	// Remove Notification
	globalRepo.DeleteAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{})

	// Reset service channel
	globalRepo.UpdateAllByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{}, bson.M{"$unset": bson.M{"taskerList": 1}})
	globalRepo.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_BNPL_PROCESS[local.ISO_CODE], bson.M{})
	globalRepo.DeleteAllByQuery(globalCollection.COLLECTION_TASK_METADATA[local.ISO_CODE], bson.M{})
}

func CreateUser(data []map[string]interface{}) {
	for k, v := range data {
		userId := v["phone"]
		language := "vi"
		status := globalConstant.USER_STATUS_ACTIVE
		user := map[string]interface{}{
			"username":         v["phone"],
			"type":             v["type"],
			"status":           status,
			"isoCode":          local.ISO_CODE,
			"phone":            v["phone"],
			"name":             v["name"],
			"address":          "104 Mai Thi Luu.",
			"countryCode":      "+84",
			"nDoneTaskInMonth": 0,
			"language":         language,
			"referralCode":     fmt.Sprintf("%s%d", v["type"].(string), k),
		}
		if v["workingPlaces"] != nil {
			user["workingPlaces"] = v["workingPlaces"]
		}
		if v["blackList"] != nil {
			user["blackList"] = v["blackList"]
		}
		if v["company"] != nil {
			user["company"] = v["company"]
		}
		if v["badges"] != nil {
			user["badges"] = v["badges"]
		}
		if v["favouriteTasker"] != nil {
			user["favouriteTasker"] = v["favouriteTasker"]
		}
		if v["score"] != nil {
			user["score"] = v["score"]
		}
		if v["lastDoneTask"] != nil {
			user["lastDoneTask"] = v["lastDoneTask"]
		}
		if v["isPremiumTasker"] != nil {
			user["isPremiumTasker"] = v["isPremiumTasker"]
		}
		if v["employeeIds"] != nil {
			user["employeeIds"] = v["employeeIds"]
		}
		if v["gender"] != nil {
			user["gender"] = v["gender"]
		}
		if v["firstVerifyAt"] != nil {
			user["firstVerifyAt"] = v["firstVerifyAt"]
		}
		globalRepo.InsertOne(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], bson.M{"_id": userId, "FMainAccount": 0, "Promotion": 0})
		user["fAccountId"] = userId
		user["_id"] = userId
		libUser.InsertOne(user)
	}
}

// ======================= TASK

func CreateTask(data []map[string]interface{}) []string {
	var taskIds []string
	for _, v := range data {
		var service *modelService.Service
		query := bson.M{"text.en": v["serviceName"]}
		if v["serviceKeyName"] != nil {
			query = bson.M{"name": v["serviceKeyName"]}
		}
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], query, bson.M{"_id": 1, "text": 1, "name": 1}, &service)
		existAsker, _ := libUser.GetOneByQuery(bson.M{"phone": v["askerPhone"]}, bson.M{"_id": 1, "name": 1})
		taskDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		if v["date"] != nil {
			date := strings.Split(v["date"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			taskDate = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		duration := 2
		cost := 200000
		address := "69 D1, Tân Hưng, quận 7, TPHCM"
		status := globalConstant.TASK_STATUS_POSTED
		if v["status"] != nil {
			status = v["status"].(string)
		}
		rated := false
		homeType := globalConstant.HOME_TYPE_HOME
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		autoChooseTasker := true
		paymentMethod := globalConstant.PAYMENT_METHOD_CASH
		if v["paymentMethod"] != nil {
			paymentMethod = v["paymentMethod"].(string)
		}
		taskItem := map[string]interface{}{
			"serviceId":   service.XId,
			"serviceText": service.Text,
			"description": v["description"],
			"date":        taskDate,
			"duration":    duration,
			"cost":        cost,
			"address":     address,
			"lat":         10.741432,
			"lng":         106.702033,
			"phone":       v["askerPhone"],
			"contactName": existAsker.Name,
			"createdAt":   createdAt,
			"askerId":     existAsker.XId,
			"status":      status,
			"rated":       rated,
			"visibility":  1,
			"payment": map[string]interface{}{
				"method": paymentMethod,
			},
			"homeType":         homeType,
			"autoChooseTasker": autoChooseTasker,
			"isoCode":          local.ISO_CODE,
			"serviceName":      service.Name,
		}
		if v["acceptedTasker"] != nil {
			taskItem["acceptedTasker"] = v["acceptedTasker"]
		}
		if v["costDetail"] != nil {
			taskItem["costDetail"] = v["costDetail"]
		}
		if v["pricing"] != nil {
			taskItem["pricing"] = v["pricing"]
		}
		if v["detailDeepCleaning"] != nil {
			taskItem["detailDeepCleaning"] = v["detailDeepCleaning"]
		}
		if v["isoCode"] != nil {
			taskItem["isoCode"] = v["isoCode"]
		}
		if v["taskPlace"] != nil {
			taskItem["taskPlace"] = v["taskPlace"]
		}
		if v["isPremium"] != nil {
			taskItem["isPremium"] = v["isPremium"]
		}
		delete(taskItem, "viewedTaskers")
		taskId := globalLib.GenerateObjectId()
		taskItem["_id"] = taskId
		globalRepo.InsertOne(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskItem)
		taskIds = append(taskIds, taskId)
	}
	return taskIds
}

func CreateTaskSubscription(data []map[string]interface{}) []string {
	var taskIds []string
	for _, v := range data {
		var service *modelService.Service
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": v["serviceName"]}, bson.M{"_id": 1, "text": 1}, &service)
		existAsker, _ := libUser.GetOneByQuery(bson.M{"phone": v["askerPhone"]}, bson.M{"_id": 1, "name": 1})
		taskDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		if v["date"] != nil {
			date := strings.Split(v["date"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			taskDate = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		duration := 2
		cost := 200000
		address := "69 D1, Tân Hưng, quận 7, TPHCM"
		status := globalConstant.TASK_STATUS_POSTED
		if v["status"] != nil {
			status = v["status"].(string)
		}
		rated := false
		homeType := globalConstant.HOME_TYPE_HOME
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		autoChooseTasker := true
		paymentMethod := globalConstant.PAYMENT_METHOD_CASH
		if v["paymentMethod"] != nil {
			paymentMethod = v["paymentMethod"].(string)
		}
		taskItem := map[string]interface{}{
			"serviceId":   service.XId,
			"serviceText": service.Text,
			"description": v["description"],
			"date":        taskDate,
			"duration":    duration,
			"cost":        cost,
			"address":     address,
			"lat":         10.741432,
			"lng":         106.702033,
			"phone":       v["askerPhone"],
			"contactName": existAsker.Name,
			"createdAt":   createdAt,
			"askerId":     existAsker.XId,
			"status":      status,
			"rated":       rated,
			"visibility":  1,
			"payment": map[string]interface{}{
				"method": paymentMethod,
			},
			"homeType":         homeType,
			"autoChooseTasker": autoChooseTasker,
			"isoCode":          local.ISO_CODE,
			"serviceName":      globalConstant.NORMAL_SERVICE_NAME_FROM_SUBSCRIPTION_SERVICE_NAME[v["serviceName"].(string)],
		}
		if v["acceptedTasker"] != nil {
			taskItem["acceptedTasker"] = v["acceptedTasker"]
		}
		if v["costDetail"] != nil {
			taskItem["costDetail"] = v["costDetail"]
		}
		if v["pricing"] != nil {
			taskItem["pricing"] = v["pricing"]
		}
		if v["detailDeepCleaning"] != nil {
			taskItem["detailDeepCleaning"] = v["detailDeepCleaning"]
		}
		if v["isoCode"] != nil {
			taskItem["isoCode"] = v["isoCode"]
		}
		if v["taskPlace"] != nil {
			taskItem["taskPlace"] = v["taskPlace"]
		}
		if v["isPremium"] != nil {
			taskItem["isPremium"] = v["isPremium"]
		}
		delete(taskItem, "viewedTaskers")
		taskId := globalLib.GenerateObjectId()
		taskItem["_id"] = taskId
		globalRepo.InsertOne(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskItem)
		taskIds = append(taskIds, taskId)
	}
	return taskIds
}
func CreateTaskerBNPLProcess(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		ids = append(ids, v["_id"].(string))
		v["createdAt"] = globalLib.GetCurrentTime(local.TimeZone)
		insertData = append(insertData, v)
	}
	globalRepo.InsertAll(globalCollection.COLLECTION_TASKER_BNPL_PROCESS[local.ISO_CODE], insertData)
	return ids
}

func CreateTaskMetadata(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		ids = append(ids, v["_id"].(string))
		v["createdAt"] = globalLib.GetCurrentTime(local.TimeZone)
		insertData = append(insertData, v)
	}
	globalRepo.InsertAll(globalCollection.COLLECTION_TASK_METADATA[local.ISO_CODE], insertData)
	return ids
}
