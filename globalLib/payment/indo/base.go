package indo

import (
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalModel/modelTransactionInfo"
)

var MIDTRANS_PREPAY_PAYMENT_METHODS = []string{
	globalConstant.PAYMENT_METHOD_QRIS,
	globalConstant.PAYMENT_METHOD_GO_PAY,
	globalConstant.PAYMENT_METHOD_BANK_TRANSFER,
	globalConstant.PAYMENT_METHOD_VIRTUAL_ACCOUNT,
}

var XENDIT_PREPAY_PAYMENT_METHOD = []string{
	globalConstant.PAYMENT_METHOD_DANA,
}

func isTransactionSuccess(transactionInfo *modelTransactionInfo.TransactionInfo) bool {
	return transactionInfo != nil && transactionInfo.Charged
}
