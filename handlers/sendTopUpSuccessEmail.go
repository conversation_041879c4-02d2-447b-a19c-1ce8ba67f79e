package handlers

import (
	"encoding/json"
	"fmt"
	"math"
	"net/http"
	"strings"

	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-email-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	modelPaymentCard "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentCard"
	modelSettingCountry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/transaction"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func SendTopUpSuccessEmail(reqBody *modelEmailSending.EmailRequest) (*modelEmailResponse.EmailResponse, error) {
	defer local.Logger.Sync()

	// Check server config
	resErr := CheckEmailConfig()
	if resErr != nil {
		local.Logger.Error(resErr.Message)
		return resErr, nil
	}

	if reqBody.TransactionId == "" {
		local.Logger.Warn(lib.ERROR_TRANSACTION_ID_REQUIRED,
			zap.Any("body", reqBody),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_TRANSACTION_ID_REQUIRED,
		}, nil
	}

	var transaction *modelTransaction.Transaction
	globalDataAccess.GetOneById(globalCollection.COLLECTION_TRANSACTION[local.ISO_CODE], reqBody.TransactionId, bson.M{}, &transaction)
	if transaction == nil {
		local.Logger.Warn(lib.ERROR_BOOKING_NOT_FOUND,
			zap.Any("body", reqBody),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusNotFound,
			Message:    lib.ERROR_BOOKING_NOT_FOUND,
		}, nil
	}

	asker, _ := modelUser.GetOneById(local.ISO_CODE, transaction.UserId, bson.M{"emails": 1, "language": 1, "name": 1, "referralCode": 1})
	if asker == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND,
			zap.Any("body", reqBody),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusNotFound,
			Message:    lib.ERROR_USER_NOT_FOUND,
		}, nil
	}

	if len(asker.Emails) == 0 {
		local.Logger.Warn("EMAIL_NOT_FOUND",
			zap.Any("body", reqBody),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusNotFound,
			Message:    lib.ERROR_USER_EMAIL_NOT_EXISTS,
		}, nil
	}

	if len(asker.Emails) == 0 || !asker.Emails[0].Verified {
		local.Logger.Warn("EMAIL_UNVERIFIED",
			zap.Any("body", reqBody),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusInternalServerError,
			Message:    lib.EMAIL_UNVERIFIED,
		}, nil
	}

	option, err := getTopUpSuccessEmailOption(transaction, asker)
	if err != nil {
		local.Logger.Error(lib.ERROR_CONTENT_NIL,
			zap.Error(err),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_CONTENT_NIL,
		}, err
	}

	// NOTE: Test with webhook service vn 2 1
	return SendEmail(option)
}

func getTopUpSuccessEmailOption(transaction *modelTransaction.Transaction, user *modelUser.Users) (*modelEmailSending.EmailSending, error) {
	// Get data from transaction
	chargeValue := transaction.Value
	var promotion, topUpValue float64
	if transaction.Data != nil {
		promotion = transaction.Data.TopUpCreditPromotion
		topUpValue = transaction.Data.TopUpValue
	}
	payment := transaction.Payment

	// Prepare content
	language := globalConstant.LANG_EN
	if user.Language != "" {
		language = user.Language
	}
	promotionValue := math.Ceil(chargeValue * promotion)
	paymentMethod := localization.T(language, "TRANSFER_INTO_BTASKEE_ACCOUNT")
	if payment != nil && payment.Method != "" {
		switch payment.Method {
		case globalConstant.PAYMENT_METHOD_BANK_TRANSFER:
			paymentMethod = "ATM " + payment.Bank
		case globalConstant.PAYMENT_METHOD_CARD:
			var paymentCard *modelPaymentCard.PaymentCard
			globalDataAccess.GetOneById(globalCollection.COLLECTION_PAYMENT_CARD[local.ISO_CODE], payment.CardId, bson.M{"number": 1, "type": 1}, &paymentCard)
			if paymentCard != nil {
				paymentMethod = paymentCard.Type + " " + paymentCard.Number
			} else {
				paymentMethod = payment.CardType + " " + payment.CardNumber
			}
		default:
			paymentMethod = globalLib.LocalizePaymentMethod(payment.Method, language)
		}
	}
	currency := globalConstant.CURRENCY_VN

	// Parse content
	emailData := &model.EmailTopUpSuccessData{
		Title:              localization.T(language, "TOP_UP_SUCCESS_TITLE_EMAIL"),
		Dear:               localization.T(language, "EMAIL_HELLO"),
		AskerName:          user.Name,
		Thank:              localization.T(language, "RECEIPT_EMAIL_THANKS"),
		Intro1:             localization.T(language, "EMAIL_TOP_UP_CREDIT_INTRO_1"),
		TotalTopUpTitle:    localization.T(language, "RECEIPT_EMAIL_PAYMENT_TOTAL"),
		TotalTopUp:         fmt.Sprintf("%s %s", globalLib.FormatMoney(topUpValue), currency),
		AskerNameTitle:     localization.T(language, "CANCEL_EMAIL_ASKER_NAME_TITLE"),
		PromotionTitle:     localization.T(language, "EMAIL_PROMOTION_TITLE"),
		Promotion:          fmt.Sprintf("%s %s", globalLib.FormatMoney(promotionValue), currency),
		PaymentMethodTitle: localization.T(language, "CANCEL_EMAIL_PAYMENT_METHOD_TITLE"),
		PaymentMethod:      paymentMethod,
		TopUpDetailTitle:   localization.T(language, "EMAIL_TOP_UP_CREDIT_DETAIL"),
		ChargeValueTitle:   localization.T(language, "EMAIL_TOP_UP_CREDIT_CHARGED_MONEY"),
		ChargeValue:        fmt.Sprintf("%s %s", globalLib.FormatMoney(chargeValue), currency),
	}

	// Invitation
	var settingCountry *modelSettingCountry.SettingCountry
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_COUNTRY[local.ISO_CODE], bson.M{"isoCode": transaction.IsoCode}, bson.M{"currency.code": 1}, &settingCountry)
	var settings *modelSettings.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"referralSetting": 1}, &settings)

	if settingCountry != nil && settingCountry.Currency != nil && settingCountry.Currency.Code != "" {
		emailData.HasInvitation = true
		emailData.ReferralCode = strings.ToUpper(user.ReferralCode)
		value, currency := getReferralValueAndCurrency(transaction.IsoCode, currency, settings)
		emailData.ReferralText = localization.T(language, "EMAIL_REFERRAL_TEXT", globalLib.FormatMoney(value), currency, globalLib.FormatMoney(value), currency)
		emailData.ReferralTitle = localization.T(language, "EMAIL_REFERRAL_TITLE")
	}

	// Update contact info of bTaskee
	b, _ := json.Marshal(emailData)
	emailDataMap := make(map[string]interface{})
	json.Unmarshal(b, &emailDataMap)
	emailDataMap = globalLib.UpdateBtaskeeInfoInEmailByIsoCode(emailDataMap, transaction.IsoCode)

	t := fmt.Sprintf("%s/topUpSuccessEmail.html", cfg.EmailTemplateURL)
	body, err := ParseTemplate(t, emailDataMap)
	if body == "" || err != nil {
		return nil, err
	}

	option := &modelEmailSending.EmailSending{
		From: "bTaskee Receipts <<EMAIL>>",
		To:   user.Emails[0].Address,
		// Bcc:     []string{"<EMAIL>"},
		Subject: localization.T(language, "EMAIL_TOP_UP_CREDIT_SUBJECT"),
		Content: body,
		ReplyTo: "<EMAIL>",
	}

	return option, nil
}
