// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcCancelTaskMY

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	cancelBookingRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/cancelBookingRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// CancelTaskClient is the client API for CancelTask service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CancelTaskClient interface {
	BackendCancelTask(ctx context.Context, in *cancelBookingRequest.CancelBookingRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type cancelTaskClient struct {
	cc grpc.ClientConnInterface
}

func NewCancelTaskClient(cc grpc.ClientConnInterface) CancelTaskClient {
	return &cancelTaskClient{cc}
}

func (c *cancelTaskClient) BackendCancelTask(ctx context.Context, in *cancelBookingRequest.CancelBookingRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcCancelTaskMY.CancelTask/BackendCancelTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CancelTaskServer is the server API for CancelTask service.
// All implementations must embed UnimplementedCancelTaskServer
// for forward compatibility
type CancelTaskServer interface {
	BackendCancelTask(context.Context, *cancelBookingRequest.CancelBookingRequest) (*empty.Empty, error)
	mustEmbedUnimplementedCancelTaskServer()
}

// UnimplementedCancelTaskServer must be embedded to have forward compatible implementations.
type UnimplementedCancelTaskServer struct {
}

func (UnimplementedCancelTaskServer) BackendCancelTask(context.Context, *cancelBookingRequest.CancelBookingRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BackendCancelTask not implemented")
}
func (UnimplementedCancelTaskServer) mustEmbedUnimplementedCancelTaskServer() {}

// UnsafeCancelTaskServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CancelTaskServer will
// result in compilation errors.
type UnsafeCancelTaskServer interface {
	mustEmbedUnimplementedCancelTaskServer()
}

func RegisterCancelTaskServer(s *grpc.Server, srv CancelTaskServer) {
	s.RegisterService(&_CancelTask_serviceDesc, srv)
}

func _CancelTask_BackendCancelTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cancelBookingRequest.CancelBookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CancelTaskServer).BackendCancelTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcCancelTaskMY.CancelTask/BackendCancelTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CancelTaskServer).BackendCancelTask(ctx, req.(*cancelBookingRequest.CancelBookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _CancelTask_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcCancelTaskMY.CancelTask",
	HandlerType: (*CancelTaskServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BackendCancelTask",
			Handler:    _CancelTask_BackendCancelTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cancel-task.proto",
}
