package globalConstant

import (
	modelSettingCountry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
)

var APP_LANGUAGES = SUPPORT_LANGUAGE

var LIST_NOTIFY_FROM_BTASKEE = []int{6, 7, 8, 9, 11, 19, 22, 24, 27, 29, 30, 36}
var LIST_NOTIFY_FROM_BE = []int{37}
var NUMBER_LAST_CHARACTER_NAME = 4
var MARKETING_CAMPAIGN_ACTION_MAP = map[string]int{
	"seen":     MARKETING_CAMPAIGN_ACTION_SEEN,
	"skip":     MARKETING_CAMPAIGN_ACTION_SKIP,
	"book":     MARKETING_CAMPAIGN_ACTION_BOOK,
	"posted":   MARKETING_CAMPAIGN_ACTION_POSTED,
	"done":     MARKETING_CAMPAIGN_ACTION_DONE,
	"canceled": MARKETING_CAMPAIGN_ACTION_CANCELED,
}

var COUNTRY_FLAG_BY_ISO_CODE = map[string]string{
	ISO_CODE_VN:   "🇻🇳",
	ISO_CODE_TH:   "🇹🇭",
	ISO_CODE_INDO: "🇮🇩",
	ISO_CODE_MY:   "🇲🇾",
}

var TASKER_MARKETING_CAMPAIGN_ACTION_MAP = map[string]int{
	"seen":   TASKER_MARKETING_CAMPAIGN_ACTION_SEEN,
	"closed": TASKER_MARKETING_CAMPAIGN_ACTION_CLOSED,
}

var CURRENCY_SIGN_VN = modelSettingCountry.SettingCountryCurrency{Sign: "₫", Code: "VND"}
var CURRENCY_SIGN_TH = modelSettingCountry.SettingCountryCurrency{Sign: "฿", Code: "THB"}
var CURRENCY_SIGN_ID = modelSettingCountry.SettingCountryCurrency{Sign: "Rp", Code: "IDR"}
var CURRENCY_SIGN_MY = modelSettingCountry.SettingCountryCurrency{Sign: "RM", Code: "MYR"}

var PREPAY_PAYMENT_METHODS_VN = []string{PAYMENT_METHOD_SHOPEE_PAY, PAYMENT_METHOD_MOMO, PAYMENT_METHOD_ZALO_PAY, PAYMENT_METHOD_TIKI_MINI_APP, PAYMENT_METHOD_VN_PAY, PAYMENT_METHOD_VIET_QR, PAYMENT_METHOD_KREDIVO, PAYMENT_METHOD_BPAY_BUSINESS}
var PREPAY_PAYMENT_METHODS_TH = []string{PAYMENT_METHOD_PROMPT_PAY, PAYMENT_METHOD_TRUE_MONEY, PAYMENT_METHOD_SHOPEE_PAY, PAYMENT_METHOD_BPAY_BUSINESS}
var PREPAY_PAYMENT_METHODS_INDO = []string{PAYMENT_METHOD_QRIS, PAYMENT_METHOD_GO_PAY, PAYMENT_METHOD_DANA, PAYMENT_METHOD_VIRTUAL_ACCOUNT, PAYMENT_METHOD_BPAY_BUSINESS}
var QR_CODE_PAYMENT_METHODS = []string{PAYMENT_METHOD_VIET_QR, PAYMENT_METHOD_PROMPT_PAY, PAYMENT_METHOD_QRIS} // a Ngoc require remove field PAYMENT_METHOD_BPAY_BUSINESS cuz it's not qr code payment methods
var PREPAY_PAYMENT_METHODS_MY = []string{}

// 6: notify User that his deposit money was increased
// 7: notify user that his account was locked
// 8: notify user that his account was actived again
// 9: notify user that the service was closed
// 11: notify user that the service was active
// 19: notify user if user's financial account is too low.
// 22: notify asker about monthly award.
// 24: email was verified.
// 27: notify user to extend subscription.
// 29: payment
// 30: notify user from backend (holiday...).
// 36: Subscription request from Tasker. Remove when Asker cancel request, request invalid,

var SUPPORT_LANGUAGE = []string{LANG_EN, LANG_VI, LANG_KO, LANG_TH, LANG_ID, LANG_MY}
var SUPPORT_ISO_CODE = []string{ISO_CODE_VN, ISO_CODE_TH, ISO_CODE_INDO, ISO_CODE_MY}

var SERVICES_NOT_SUPPORT_JOURNEY = []string{SERVICE_KEY_NAME_UPHOLSTERY, SERVICE_KEY_NAME_DISINFECTION, SERVICE_KEY_NAME_AIR_CONDITIONER, SERVICE_KEY_NAME_LAUNDRY, SERVICE_KEY_NAME_CAR_ADVERTISING, SERVICE_KEY_NAME_WASHING_MACHINE, SERVICE_KEY_NAME_WATER_HEATER, SERVICE_KEY_NAME_CARPET_CLEANING, SERVICE_KEY_NAME_HOME_MOVING, SERVICE_KEY_NAME_INDUSTRIAL_CLEANING}
var LIST_COMPANY_SERVICES = []string{SERVICE_KEY_NAME_UPHOLSTERY, SERVICE_KEY_NAME_DISINFECTION, SERVICE_KEY_NAME_AIR_CONDITIONER, SERVICE_KEY_NAME_LAUNDRY, SERVICE_KEY_NAME_WASHING_MACHINE, SERVICE_KEY_NAME_CAR_ADVERTISING, SERVICE_KEY_NAME_WATER_HEATER, SERVICE_KEY_NAME_CARPET_CLEANING, SERVICE_KEY_NAME_HOME_MOVING, SERVICE_KEY_NAME_INDUSTRIAL_CLEANING}

var MAP_RANK_REQUIRE = map[string]int32{
	ASKER_RANK_NAME_MEMBER:   1,
	ASKER_RANK_NAME_SILVER:   2,
	ASKER_RANK_NAME_GOLD:     3,
	ASKER_RANK_NAME_PLATINUM: 4,
}

var SERVICE_MUST_HAVE_LEADER = []string{
	SERVICE_KEY_NAME_DEEP_CLEANING,
	SERVICE_KEY_NAME_OFFICE_CLEANING,
	SERVICE_KEY_NAME_MASSAGE,
	SERVICE_KEY_NAME_BEAUTY_CARE,
}

const (
	// ========================= APP

	DB_BTASKEE     = "btaskee"
	API_VERSION_V1 = "v1"
	API_VERSION_V2 = "v2"

	RATING_TIP = "RATING_TIP"

	API_HEADER_AUTHORIZATION = "Authorization"

	TASK_RATE      = 0.2
	TASK_RATE_TH   = 0.2
	TASK_RATE_INDO = 0.2

	MAX_SMS_RESEND_ACTIVATION = 3
	DEFAULT_DISTANCE          = 500

	USER_CHANGE_HISTORY_LIMIT_NUMBER = 20

	APP_NAME_ASKER  = "bTaskeeAsker"
	APP_NAME_TASKER = "bTaskeePartner"

	ISO_CODE_INDO = "ID"
	ISO_CODE_VN   = "VN"
	ISO_CODE_TH   = "TH"
	ISO_CODE_MY   = "MY"

	LAYOUT_DATE        = "02/01/2006"
	LAYOUT_DATE_DASH   = "2006-01-02"
	LAYOUT_TIME        = "15:04:00"
	LAYOUT_DATE_TIME   = "15:04:00 02/01/2006"
	LAYOUT_HOUR_MINUTE = "15:04"
	LAYOUT_DATE_US     = "January 2, 2006"
	LAYOUT_CUSTOM_1    = "15:04 02/01/2006"

	DEFAULT_LANGUAGE = "en"
	DEFAULT_CURRENCY = "VND"

	CURRENCY_VN      = "VND"
	CURRENCY_TH      = "THB"
	CURRENCY_LANG_TH = "บาท"
	CURRENCY_ID      = "IDR"

	HOME_TYPE_HOME  = "HOME"
	HOME_TYPE_HOTEL = "HOTEL"

	TIME_ZONE_VN = "Asia/Ho_Chi_Minh"
	TIME_ZONE_TH = "Asia/Bangkok"

	COUNTRY_CODE_VN   = "+84"
	COUNTRY_CODE_TH   = "+66"
	COUNTRY_CODE_SG   = "+65"
	COUNTRY_CODE_INDO = "+62"
	COUNTRY_CODE_MY   = "+60"

	GENDER_MALE   = "MALE"
	GENDER_FEMALE = "FEMALE"
	GENDER_LGBT   = "LGBT"

	TRAINING_RESULT_SLACK_CHANNEL_VN = "training-result"
	TRAINING_RESULT_SLACK_CHANNEL_TH = "thailand-training-results"
	TRAINING_RESULT_SLACK_CHANNEL_ID = "indo-training-results"
	TRAINING_OFFICE_SLACK_CHANNEL_VN = "training-office"
	// TRAINING_OFFICE_SLACK_CHANNEL_TH = "thailand-training-office" // Tạm thời chưa sử dụng vì thái lan tasker đang ít, nên chỉ cần post result vào channel trên

	REFUND_SLACK_CHANNEL_VN   = "refund-vn"
	REFUND_SLACK_CHANNEL_TH   = "refund-th"
	REFUND_SLACK_CHANNEL_INDO = "refund-id"

	PAYMENT_SLACK_CHANNEL_VN               = "vn-payment"
	TASKER_ERROR_FUNCTION_SLACK_CHANNEL    = "tasker-error-function"
	ASKER_SMS_ERROR_TRACKING_SLACK_CHANNEL = "asker-sms-error-tracking"
	SUB_SCHEDULE_SLACK_CHANNEL_VN          = "vn-sub-schedule"

	SLACK_USER_NAME = "bTaskee System"

	MINI_APP_SLACK_CHANNEL_VN               = "mini-app-vn"
	TIKI_ORDER_NEED_REFUND_SLACK_CHANNEL_VN = "tiki-refund-vn"

	SET_DO_TRAINING_TASKER_SLACK_CHANNEL = "setDoTrainingTasker"

	NEW_COOKING_TASKER_SLACK_CHANNEL   = "new-cooking-tasker"
	INDO_ASKER_COMPLAINT_SLACK_CHANNEL = "id_asker-complaint"

	SLACK_CHANNEL_SMS_ERROR_TRACKING_INDO = "sms-error-tracking-indo"
	SLACK_CHANNEL_ALERT_INDO              = "alerts-indo"
	SLACK_CHANNEL_UPDATE_MISSING_DB       = "update-missing-db"

	BPOINT_TASKER_SLACK_CHANNEL_VN = "vn-bpoint-tasker"
	BPOINT_TASKER_SLACK_CHANNEL_TH = "th-bpoint-tasker"

	SLACK_CHANNEL_CHEATING_CARD_VN = "vn_cheating_card"
	SLACK_CHANNEL_CHEATING_CARD_TH = "th_cheating_card"

	// Slack channel for service notification
	SLACK_CHANNEL_MOVING_VN   = "moving-vn"
	SLACK_CHANNEL_MOVING_INDO = "moving-indo"

	SLACK_CHANNEL_BEAUTY_CARE_VN = "vn_beauty_care"

	// ========================= CHANGES HISTORY

	CHANGES_HISTORY_KEY_UPDATE_SUBSCRIPTION              = "UPDATE_SUBSCRIPTION"
	CHANGES_HISTORY_KEY_CANCEL_SUBSCRIPTION              = "CANCEL_SUBSCRIPTION"
	CHANGES_HISTORY_KEY_CONFIRM_TASKER                   = "CONFIRM_TASKER"
	CHANGES_HISTORY_FROM_ASKER_APP                       = "ASKER_APP"
	CHANGES_HISTORY_FROM_TASKER_APP                      = "TASKER_APP"
	CHANGES_HISTORY_FROM_SYNC_CRON                       = "SYNCED_CRON"
	CHANGES_HISTORY_FROM_SYSTEM                          = "SYSTEM"
	CHANGES_HISTORY_FROM_BACKEND                         = "BACKEND"
	CHANGES_HISTORY_KEY_DONE_TASK                        = "DONE_TASK"
	CHANGES_HISTORY_KEY_CANCEL_TASK                      = "cancelTask"
	CHANGES_HISTORY_KEY_ASKER_RATING                     = "askerRating"
	CHANGES_HISTORY_KEY_TASKER_WITHDRAW_TASK             = "TASKER_WITHDRAW_TASK"
	CHANGES_HISTORY_KEY_UPDATE_REFUND_AMOUNT             = "UPDATE_REFUND_AMOUNT"
	CHANGES_HISTORY_KEY_TASKER_CHOOSE_DATE               = "TASKER_CHOOSE_DATE_OPTION"
	CHANGES_HISTORY_KEY_ASKER_CHOOSE_DATE                = "ASKER_CHOOSE_DATE_OPTION"
	CHANGES_HISTORY_KEY_ASKER_REMOVE_FORCE_TASKER        = "ASKER_REMOVE_FORCE_TASKER"
	CHANGES_HISTORY_KEY_BOOK_ECO_SUBSCRIPTION            = "BOOK_ECO_SUBSCRIPTION"
	CHANGES_HISTORY_KEY_UPDATE_PAYMENT_METHOD            = "UPDATE_PAYMENT_METHOD"
	CHANGES_HISTORY_KEY_REMIND_ASKER_CHOOSE_TASKER       = "REMIND_ASKER_CHOOSE_TASKER"
	CHANGES_HISTORY_KEY_TASKER_SKIP_TASK                 = "TASKER_SKIP_TASK"
	CHANGES_HISTORY_KEY_TASKER_SUGGESTED_NEW_DATE_OPTION = "TASKER_SUGGESTED_NEW_DATE_OPTION"

	CHANGES_HISTORY_KEY_RESEND_FORCE_TASKER                 = "RESEND_FORCETASKER"
	CHANGES_HISTORY_KEY_RESEND_FAV_TASKER                   = "RESEND_FAV"
	CHANGES_HISTORY_KEY_RESEND_FAV_TOPTASKER                = "RESEND_FAV_TOPTASKER"
	CHANGES_HISTORY_KEY_RESEND_FAV_DISTRICT                 = "RESEND_FAV_DISTRICT"
	CHANGES_HISTORY_KEY_RESEND_CITY                         = "RESEND_CITY"
	CHANGES_HISTORY_KEY_RESEND_FORCE_TASKER_TASKER_REJECTED = "RESEND_FORCETASKER_TASKER_REJECTED"
	CHANGES_HISTORY_KEY_RESEND_FORCE_TASKER_TASK_CANCELLED  = "RESEND_FORCETASKER_TASK_CANCELLED"

	CHANGES_HISTORY_KEY_REMIND_TASKER_PROCESS_SPECIAL_CAMPAIGN = "REMIND_TASKER_PROCESS_SPECIAL_CAMPAIGN"
	CHANGES_HISTORY_KEY_ADD_ASKER_TO_BLACKLIST                 = "ADD_ASKER_TO_BLACKLIST"
	CHANGES_HISTORY_KEY_ADD_FORCE_ACCEPTED_TASKER              = "ADD_FORCE_ACCEPTED_TASKER"
	CHANGES_HISTORY_KEY_REPLACE_FORCE_ACCEPTED_TASKER          = "REPLACE_FORCE_ACCEPTED_TASKER"
	CHANGES_HISTORY_KEY_REMOVE_FORCE_ACCEPTED_TASKER           = "REMOVE_FORCE_ACCEPTED_TASKER"

	USER_HISTORY_KEY_CHANGE_LANGUAGE       = "CHANGE_LANGUAGE"
	USER_HISTORY_KEY_CHANGE_TIME_REMIND    = "CHANGE_TIME_REMIND"
	USER_HISTORY_KEY_CHANGE_FREE_SCHEDULE  = "CHANGE_FREE_SCHEDULE"
	USER_HISTORY_KEY_CHANGE_WORKING_PLACES = "CHANGE_WORKING_PLACES"

	// ========================= TRAINING TASKER CHANGES HISTORY
	CHANGES_HISTORY_COMPLETE_QUIZ_COLLECTION       = "COMPLETE_QUIZ_COLLECTION"
	CHANGES_HISTORY_RETRY_COMPLETE_QUIZ_COLLECTION = "RETRY_COMPLETE_QUIZ_COLLECTION"
	CHANGES_HISTORY_SUBMIT_COURSE                  = "SUBMIT_COURSE"
	CHANGES_HISTORY_OPEN_TEST                      = "OPEN_TEST"
	CHANGES_HISTORY_UNBLOCK_TEST                   = "UNBLOCK_TEST"

	// ========================= COMMUNITY CHANGES HISTORY
	CHANGES_HISTORY_COMMUNITY_DELETE_POST             = "COMMUNITY_DELETE_POST"
	CHANGES_HISTORY_COMMUNITY_EDIT_POST               = "COMMUNITY_EDIT_POST"
	CHANGES_HISTORY_COMMUNITY_CREATE_POST             = "COMMUNITY_CREATE_POST"
	CHANGES_HISTORY_COMMUNITY_SCHEDULE_POST           = "COMMUNITY_SCHEDULE_POST"
	CHANGES_HISTORY_COMMUNITY_USER_HIDE_POST          = "COMMUNITY_USER_HIDE_POST"
	CHANGES_HISTORY_COMMUNITY_USER_UNHIDE_POST        = "COMMUNITY_USER_UNHIDE_POST"
	CHANGES_HISTORY_COMMUNITY_USER_BLOCK_OTHER_USER   = "COMMUNITY_USER_BLOCK_OTHER_USER"
	CHANGES_HISTORY_COMMUNITY_USER_UNBLOCK_OTHER_USER = "COMMUNITY_USER_UNBLOCK_OTHER_USER"

	CHANGES_HISTORY_COMMUNITY_KEY_FROM_SYSTEM = "SYSTEM"
	// ========================= USER COMBO VOUCHER CHANGES HISTORY
	CHANGES_HISTORY_USER_COMBO_VOUCHER_EXTEND = "USER_COMBO_VOUCHER_EXTEND"
	// ========================= LANGUAGE

	LANG_VI = "vi"
	LANG_EN = "en"
	LANG_KO = "ko"
	LANG_TH = "th"
	LANG_ID = "id"
	LANG_MY = "ms"

	// ========================= WEBSOCKET PATH

	WEBSOCKET_PATH_TASK_V2         = "/v2/task"
	WEBSOCKET_PATH_NOTIFICATION_V2 = "/v2/notification"
	WEBSOCKET_PATH_PAYMENT_V2      = "/v2/payment"
	// ========================= USER

	USER_STATUS_UNVERIFIED   = "UNVERIFIED"
	USER_STATUS_INACTIVE     = "INACTIVE"
	USER_STATUS_ACTIVE       = "ACTIVE"
	USER_STATUS_BLOCKED      = "LOCKED"
	USER_STATUS_IN_PROBATION = "IN_PROBATION"
	USER_STATUS_DISABLED     = "DISABLED"

	USER_TYPE_ASKER  = "ASKER"
	USER_TYPE_TASKER = "TASKER"

	// ========================= TASK

	TASK_STATUS_POSTED    = "POSTED"
	TASK_STATUS_WAITING   = "WAITING_ASKER_CONFIRMATION"
	TASK_STATUS_CONFIRMED = "CONFIRMED"
	TASK_STATUS_DONE      = "DONE"
	TASK_STATUS_CANCELED  = "CANCELED"
	TASK_STATUS_EXPIRED   = "EXPIRED"

	TASK_PAYMENT_STATUS_NEW      = "NEW"
	TASK_PAYMENT_STATUS_PAID     = "PAID"
	TASK_PAYMENT_STATUS_ERROR    = "ERROR"
	TASK_PAYMENT_STATUS_CHARGING = "CHARGING"
	TASK_PAYMENT_STATUS_CANCELED = "CANCELED"

	TASK_TEXT_CLEANING_TOOL_EN = "Bring cleaning supplies"

	TASK_SOURCE_FROM_QUICK_POST_TASK = "QUICK_POST_TASK"

	LIMIT_TASK_DATE = 8

	// ========================= SERVICE

	SERVICE_NAME_HOME_CLEANING       = "Cleaning"
	SERVICE_NAME_SUBSCRIPTION        = "CLEANING_SUBSCRIPTION"
	SERVICE_NAME_AIR_CONDITIONER     = "Air-conditioner Service"
	SERVICE_NAME_LAUNDRY             = "Laundry"
	SERVICE_NAME_DEEP_CLEANING       = "Deep Cleaning"
	SERVICE_NAME_HOME_COOKING        = "Home Cooking"
	SERVICE_NAME_HOUSEKEEPING        = "Housekeeping"
	SERVICE_NAME_GROCERY_ASSISTANT   = "Grocery Assistant"
	SERVICE_NAME_UPHOLSTERY          = "Upholstery Service"
	SERVICE_NAME_ELDERLY_CARE        = "Elderly Care"
	SERVICE_NAME_PATIENT_CARE        = "Patient Care"
	SERVICE_NAME_DISINFECTION        = "Disinfection Service"
	SERVICE_NAME_CHILD_CARE          = "Child Care"
	SERVICE_NAME_OFFICE_CLEANING     = "Office Cleaning"
	SERVICE_NAME_WASHING_MACHINE     = "Washing Machine"
	SERVICE_NAME_WATER_HEATER        = "Water Heater Cleaning"
	SERVICE_NAME_CARPET_CLEANING     = "Office Carpet Cleaning"
	SERVICE_NAME_HOME_MOVING         = "Household moving service"
	SERVICE_NAME_MASSAGE             = "Massage Service"
	SERVICE_NAME_INDUSTRIAL_CLEANING = "Industrial Cleaning"
	SERVICE_NAME_IRONING             = "Ironing"

	SERVICE_STATUS_ACTIVE   = "ACTIVE"
	SERVICE_STATUS_INACTIVE = "INACTIVE"

	SERVICE_KEY_NAME_HOME_CLEANING                = "CLEANING"
	SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION   = "CLEANING_SUBSCRIPTION"
	SERVICE_KEY_NAME_AIR_CONDITIONER              = "AIR_CONDITIONER_SERVICE"
	SERVICE_KEY_NAME_LAUNDRY                      = "LAUNDRY"
	SERVICE_KEY_NAME_DEEP_CLEANING                = "DEEP_CLEANING"
	SERVICE_KEY_NAME_HOME_COOKING                 = "HOME_COOKING"
	SERVICE_KEY_NAME_HOUSEKEEPING                 = "HOUSE_KEEPING"
	SERVICE_KEY_NAME_GROCERY_ASSISTANT            = "GO_MARKET"
	SERVICE_KEY_NAME_UPHOLSTERY                   = "SofaCleaning"
	SERVICE_KEY_NAME_DISINFECTION                 = "DISINFECTION_SERVICE"
	SERVICE_KEY_NAME_ELDERLY_CARE                 = "ELDERLY_CARE"
	SERVICE_KEY_NAME_PATIENT_CARE                 = "PATIENT_CARE"
	SERVICE_KEY_NAME_ELDERLY_CARE_SUBSCRIPTION    = "ELDERLY_CARE_SUBSCRIPTION"
	SERVICE_KEY_NAME_PATIENT_CARE_SUBSCRIPTION    = "PATIENT_CARE_SUBSCRIPTION"
	SERVICE_KEY_NAME_CAR_ADVERTISING              = "CAR_ADVERTISING"
	SERVICE_KEY_NAME_CHILD_CARE                   = "CHILD_CARE"
	SERVICE_KEY_NAME_CHILD_CARE_SUBSCRIPTION      = "CHILD_CARE_SUBSCRIPTION"
	SERVICE_KEY_NAME_OFFICE_CLEANING              = "OFFICE_CLEANING"
	SERVICE_KEY_NAME_OFFICE_CLEANING_SUBSCRIPTION = "OFFICE_CLEANING_SUBSCRIPTION"
	SERVICE_KEY_NAME_WASHING_MACHINE              = "WASHING_MACHINE"
	SERVICE_KEY_NAME_WATER_HEATER                 = "WATER_HEATER"
	SERVICE_KEY_NAME_CARPET_CLEANING              = "OFFICE_CARPET_CLEANING"
	SERVICE_KEY_NAME_HOME_MOVING                  = "HOME_MOVING"
	SERVICE_KEY_NAME_MASSAGE                      = "MASSAGE"
	SERVICE_KEY_NAME_INDUSTRIAL_CLEANING          = "INDUSTRIAL_CLEANING"
	SERVICE_KEY_NAME_BEAUTY_CARE                  = "BEAUTY_CARE"
	SERVICE_KEY_NAME_HAIR_STYLING                 = "HAIR_STYLING"
	SERVICE_KEY_NAME_MAKEUP                       = "MAKEUP"
	SERVICE_KEY_NAME_NAIL                         = "NAIL"
	SERVICE_KEY_NAME_IRONING                      = "IRONING"

	SERVICE_PREMIUM_OPTIONS_STATUS_ACTIVE   = "ACTIVE"
	SERVICE_PREMIUM_OPTIONS_STATUS_INACTIVE = "INACTIVE"

	// ========================= SUBSCRIPTION

	SUBSCRIPTION_STATUS_NEW      = "NEW"
	SUBSCRIPTION_STATUS_ACTIVE   = "ACTIVE"
	SUBSCRIPTION_STATUS_DONE     = "DONE"
	SUBSCRIPTION_STATUS_CANCELED = "CANCELED"
	SUBSCRIPTION_STATUS_EXPIRED  = "EXPIRED"
	SUBSCRIPTION_STATUS_PAUSED   = "PAUSED"

	// ========================= SUBSCRIPTION REQUEST

	SUBSCRIPTION_REQUEST_STATUS_SUBSCRIPTION_CREATED = "SUBSCRIPTION_CREATED"

	// ========================= PURCHASE ORDER

	PURCHASE_ORDER_STATUS_WAITING = "WAITING"
	PURCHASE_ORDER_STATUS_PAID    = "PAID"
	PURCHASE_ORDER_STATUS_CANCEL  = "CANCEL"
	PURCHASE_ORDER_STATUS_ERROR   = "ERROR"
	PURCHASE_ORDER_STATUS_EXPIRED = "EXPIRED"

	PURCHASE_ORDER_TYPE_INIT = "INIT"

	// ========================= PAYMENT METHODS

	PAYMENT_METHOD_CARD             = "CARD"             // ADYAN
	PAYMENT_METHOD_CASH             = "CASH"             // TIỀN MẶT
	PAYMENT_METHOD_BANK_TRANSFER    = "BANK_TRANSFER"    // CHUYỂN KHOẢN (CV GÓI SUBS)
	PAYMENT_METHOD_DIRECT_TRANSFER  = "DIRECT_TRANSFER"  // CHUYỂN KHOẢN (TT SUBS)
	PAYMENT_METHOD_MOMO             = "MOMO"             // MOMO
	PAYMENT_METHOD_GRAB_PAY_BY_MOCA = "GRAB_PAY_BY_MOCA" // MOCA GRAP
	PAYMENT_METHOD_ZALO_PAY         = "ZALO_PAY"         // ZALO PAY
	PAYMENT_METHOD_PROMOTION        = "PROMOTION"        //
	PAYMENT_METHOD_CREDIT           = "CREDIT"           // BPAY
	PAYMENT_METHOD_PROMPT_PAY       = "PROMPT_PAY"       // PROMPT PAY THAILAND
	PAYMENT_METHOD_TRUE_MONEY       = "TRUE_MONEY"       // TRUE MONEY THAILAND
	PAYMENT_METHOD_SHOPEE_PAY       = "SHOPEE_PAY"       // SHOPEE PAY
	PAYMENT_METHOD_TIKI_MINI_APP    = "TIKI"             // TIKI (MINI APP)
	PAYMENT_METHOD_VN_PAY           = "VN_PAY"           // VN PAY
	PAYMENT_METHOD_GO_PAY           = "GO_PAY"           // GO PAY INDO
	PAYMENT_METHOD_QRIS             = "QRIS"             // QRIS INDO
	PAYMENT_METHOD_DANA             = "DANA"             // DANA INDO
	PAYMENT_METHOD_VIET_QR          = "VIET_QR"          // VIET QR VN
	PAYMENT_METHOD_VIRTUAL_ACCOUNT  = "VIRTUAL_ACCOUNT"  // CHUYỂN KHOẢN TRỰC TIẾP
	PAYMENT_METHOD_KREDIVO          = "KREDIVO"          // KREDIVO VN
	PAYMENT_METHOD_BPAY_BUSINESS    = "BPAY_BUSINESS"    // BPAY DOANH NGHIỆP

	// ========================= FROM PARTNER
	TASK_FROM_PARTNER_TIKI_MINI_APP = "TIKI_MINI_APP"
	TASK_FROM_PARTNER_MOMO_MINI_APP = "MOMO_MINI_APP"

	// ========================= PAYMENT CHANNEL

	PAYMENT_CHANNEL_CODE_PROMPT_PAY  = "PPQR"
	PAYMENT_CHANNEL_CODE_CREDIT_CARD = "CC"
	PAYMENT_CHANNEL_DPAY             = "DPAY"
	PAYMENT_CHANNEL_CODE_TRUE_MONEY  = "TRUEMONEY"

	PAYMENT_MERCHANT_ACCOUNT = "BtaskeeCOM"

	// ========================= NEW PAYMENT REFERRENCE
	PAYMENT_REFERRENCE_TOP_UP_CREDIT    = "TC"
	PAYMENT_REFERRENCE_INTERGRATE_CARD  = "IC"
	PAYMENT_REFERRENCE_PAY_SUBSCRIPTION = "PS"
	PAYMENT_REFERRENCE_CANCEL_TASK      = "CT"
	PAYMENT_REFERRENCE_RECHARGE         = "RC"
	PAYMENT_REFERRENCE_PREPAYMENT_TASK  = "PRT"
	PAYMENT_REFERRENCE_PAY_TASK         = "PT"
	PAYMENT_REFERRENCE_COMBO_VOUCHER    = "CV"
	PAYMENT_REFERRENCE_TASKER_TOOLKIT   = "TT"

	// ======================== PAY TOOLKIT TYPE
	PAY_TASKER_TOOLKIT_TYPE_BNPL     = "BNPL"
	PAY_TASKER_TOOLKIT_TYPE_ONCE_PAY = "ONCE_PAY"

	// ========================= PAYMENT TRANSACTION

	PAYMENT_TRANSACTION_STATUS_SENDING      = "SENDING"
	PAYMENT_TRANSACTION_STATUS_CHARGING     = "CHARGING"
	PAYMENT_TRANSACTION_STATUS_RESPONSED    = "RESPONSED"
	PAYMENT_TRANSACTION_STATUS_3DAUTHORISED = "3DAUTHORISED"
	PAYMENT_TRANSACTION_STATUS_CANCELLED    = "CANCELLED"
	PAYMENT_TRANSACTION_STATUS_ERROR        = "ERROR"
	PAYMENT_TRANSACTION_STATUS_EXPIRED      = "EXPIRED"

	// ========================= OUTSTANDING PAYMENT METHODS

	OUTSTANDING_PAYMENT_STATUS_NEW             = "NEW"
	OUTSTANDING_PAYMENT_STATUS_RECHARGING      = "RECHARGING"
	OUTSTANDING_PAYMENT_STATUS_RECHARGE_FAILED = "RECHARGE_FAILED"
	OUTSTANDING_PAYMENT_STATUS_CHARGED         = "CHARGED"

	// ========================= POINT CAMPAIGN

	POINT_CAMPAIGN_STATUS_ACTIVE = "ACTIVE"

	// ========================= RATING

	RATING_FEEDBACK_NOT_COMING = "NOT_COMING"

	// ========================= BOOKING CANCEL REASON

	CANCELLATION_REASON_TASKER_NOT_COME                  = "TASKER_NOT_COME"
	CANCELLATION_REASON_TASKER_NOT_COME_WITH_ANNOUCEMENT = "TASKER_NOT_COME_WITH_ANNOUCEMENT"
	CANCELLATION_REASON_NO_TASKER_ACCEPT                 = "NO_TASKER_ACCEPT"
	CANCELLATION_REASON_ASKER_BUSY                       = "ASKER_BUSY"
	CANCELLATION_REASON_POSTED_WRONG_DATE                = "POSTED_WRONG_DATE"
	CANCELLATION_REASON_ASKER_DONT_NEED_ANYMORE          = "ASKER_DONT_NEED_ANYMORE"
	CANCELLATION_REASON_TASK_WAS_EXPIRED                 = "TASK_WAS_EXPIRED"
	CANCELLATION_REASON_BACKEND_CANCEL                   = "BACKEND_CANCEL"
	CANCELLATION_REASON_SYSTEM_CANCEL                    = "SYSTEM_CANCEL"
	CANCELLATION_REASON_NEARBY_TASK_PLACE                = "NEARBY_TASK_PLACE"
	CANCELLATION_REASON_REBOOK_TASK                      = "REBOOK_TASK"
	CANCELLATION_REASON_PAYMENT_FAILED                   = "PAYMENT_FAILED"

	// ========================= TASK SCHEDULE

	TASK_SCHEDULE_STATUS_ACTIVE   = "ACTIVE"
	TASK_SCHEDULE_STATUS_INACTIVE = "INACTIVE"
	TASK_SCHEDULE_STATUS_CANCELED = "CANCELED"

	// ========================= PROMOTION

	PROMOTION_TYPE_REFERRAL          = "Referral"
	PROMOTION_TYPE_SIGN_UP_PROMOTION = "SignUpPromotion"
	PROMOTION_TYPE_MONEY             = "MONEY"
	PROMOTION_TYPE_PERCENTAGE        = "PERCENTAGE"

	PROMOTION_APPLY_CURRENT = "CURRENT"
	PROMOTION_APPLY_BOTH    = "BOTH"
	PROMOTION_APPLY_NEW     = "NEW"

	PROMOTION_BY_BTASKEE = "BTASKEE"
	PROMOTION_BY_TASKER  = "TASKER"

	// ========================= MOMO TRANSACTION

	MOMO_TRANSACTION_STATUS_NEW      = "NEW"
	MOMO_TRANSACTION_STATUS_ERROR    = "ERROR"
	MOMO_TRANSACTION_STATUS_CHARGING = "CHARGING"
	MOMO_TRANSACTION_STATUS_PAID     = "PAID"
	MOMO_TRANSACTION_STATUS_CANCELED = "CANCELED"

	// ========================= HOUSEKEEPING

	HOUSEKEEPING_STATUS_ACTIVE  = "ACTIVE"
	HOUSEKEEPING_STATUS_DELETED = "DELETED"

	// ========================= SUBSCRIPTION REQUEST

	SUBSCRIPTION_REQUEST_STATUS_NEW = "NEW"

	// ========================= FA TRANSACTION

	FA_TRANSACTION_SOURCE_NAME_TASK                     = "TASK"
	FA_TRANSACTION_SOURCE_NAME_CARD_TASK                = "CARD_TASK"
	FA_TRANSACTION_SOURCE_NAME_NOT_COMMING              = "NOT_COMMING"
	FA_TRANSACTION_SOURCE_NAME_SUPPORT_TASKER           = "SUPPORT_TASKER"
	FA_TRANSACTION_SOURCE_NAME_COD                      = "COD"
	FA_TRANSACTION_SOURCE_NAME_TASKER_MONTHLY_REWARD    = "TASKER MONTHLY REWARD"
	FA_TRANSACTION_SOURCE_NAME_REFERRAL                 = "REFERRAL"
	FA_TRANSACTION_SOURCE_NAME_CANCEL_TASK              = "CANCEL TASK"
	FA_TRANSACTION_SOURCE_NAME_PAY_OUTSTADING_PAYMENT   = "PAY_OUTSTADING_PAYMENT"
	FA_TRANSACTION_TRANFER_TYPE_CASH                    = "cash"
	FA_TRANSACTION_CASHIER_ID_SYSTEM                    = "System"
	FA_TRANSACTION_CASHIER_NAME_SYSTEM                  = "System"
	FA_TRANSACTION_SOURCE_NAME_REFUND_CANCEL_TASK       = "REFUND_CANCEL_TASK"
	FA_TRANSACTION_SOURCE_NAME_WEEKLY_PAYOUT            = "WEEKLY_PAYOUT"
	FA_TRANSACTION_SOURCE_NAME_REVERT_WEEKLY_PAYOUT     = "REVERT_WEEKLY_PAYOUT"
	FA_TRANSACTION_SOURCE_NAME_REJECT_WEEKLY_PAYOUT     = "REJECT_WEEKLY_PAYOUT"
	FA_TRANSACTION_TRANFER_TYPE_TRANFER                 = "tranfer"
	FA_TRANSACTION_CASHIER_NAME_PAYMENT_SERVER          = "PAYMENT_SERVER"
	FA_TRANSACTION_SOURCE_NAME_RATING_TIP               = "RATING_TIP"
	FA_TRANSACTION_SOURCE_NAME_ROLLBACK_TRANSACTION     = "ROLLBACK_TRANSACTION"
	FA_TRANSACTION_SOURCE_NAME_REFUND_RATING_NOT_COMING = "REFUND_RATING_NOT_COMING"
	FA_TRANSACTION_SOURCE_NAME_COMBO_VOUCHER            = "COMBO_VOUCHER"
	FA_TRANSACTION_SOURCE_NAME_PAY_SUBSCRIPTION         = "PAY_SUBSCRIPTION"
	FA_TRANSACTION_SOURCE_NAME_CANCEL_SUBSCRIPTION      = "CANCEL_SUBSCRIPTION"
	FA_TRANSACTION_SOURCE_NAME_UPDATE_SUBSCRIPTION      = "UPDATE_SUBSCRIPTION"
	FA_TRANSACTION_SOURCE_NAME_DEPOSIT                  = "DEPOSIT"
	FA_TRANSACTION_SOURCE_NAME_WITHDRAW                 = "WITHDRAW"
	FA_TRANSACTION_SOURCE_NAME_SYSTEM_REFUND            = "SYSTEM_REFUND"
	FA_TRANSACTION_SOURCE_NAME_SYSTEM_REWARD            = "SYSTEM_REWARD"
	FA_TRANSACTION_SOURCE_NAME_JOURNEY_REWARD           = "JOURNEY_REWARD"
	FA_TRANSACTION_SOURCE_NAME_EXTRA_VAT                = "EXTRA_VAT"
	FA_TRANSACTION_SOURCE_NAME_GAME_CAMPAIGN            = "GAME_CAMPAIGN"
	FA_TRANSACTION_SOURCE_NAME_CHARGE_BNPL              = "CHARGE_BNPL" // Transaction charge BNPL when tasker done task
	FA_TRANSACTION_SOURCE_NAME_ASKER_LIXI_TASKER        = "ASKER_LIXI_TASKER"
	FA_TRANSACTION_TYPE_DEPOSIT                         = "D"
	FA_TRANSACTION_TYPE_CREDIT                          = "C"
	FA_TRANSACTION_ACCOUNT_TYPE_MAIN                    = "M"
	FA_TRANSACTION_ACCOUNT_TYPE_PROMOTION               = "P"

	FA_TRANSACTION_SOURCE_NAME_REFERRAL_INVITED_TASKER_DONE_20_TASKS_IN_30_DAYS = "REFERRAL_INVITED_TASKER_DONE_20_TASKS_IN_30_DAYS"

	FA_TRANSACTION_SOURCE_NAME_SPECIAL_CAMPAIGN = "SPECIAL_CAMPAIGN"
	// ========================= TASKER TRAINING

	TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER     = "TRAINING_TASKER"
	TASKER_TRAINING_HISTORY_TYPE_TRAINING_INPUT      = "TRAINING_INPUT"
	TASKER_TRAINING_HISTORY_TYPE_TRAINING_TASKER_OLD = "TRAINING_TASKER_OLD"

	// ========================= WEEKLY PAYOUT

	WEEKLY_PAYOUT_STATUS_WAIT     = "WAIT"
	WEEKLY_PAYOUT_STATUS_PAID     = "PAID"
	WEEKLY_PAYOUT_STATUS_REJECTED = "REJECTED"

	// MARKETING CAMPAIGN

	MARKETING_CAMPAIGN_STATUS_ACTIVE = "ACTIVE"

	MARKETING_CAMPAIGN_ACTION_SEEN     = 32
	MARKETING_CAMPAIGN_ACTION_SKIP     = 16
	MARKETING_CAMPAIGN_ACTION_BOOK     = 8
	MARKETING_CAMPAIGN_ACTION_POSTED   = 4
	MARKETING_CAMPAIGN_ACTION_DONE     = 2
	MARKETING_CAMPAIGN_ACTION_CANCELED = 1
	MARKETING_CAMPAIGN_ACTION_NEW      = 0

	// TASKER MARKETING CAMPAIGN
	TASKER_MARKETING_CAMPAIGN_ACTION_CLOSED = 2
	TASKER_MARKETING_CAMPAIGN_ACTION_SEEN   = 1
	TASKER_MARKETING_CAMPAIGN_ACTION_NEW    = 0

	// MARKETING CAMPAIGN TYPE
	MARKETING_CAMPAIGN_TYPE_PROMOTION = "PROMOTION"
	MARKETING_CAMPAIGN_TYPE_INFO      = "INFO"

	// ========================= INCENTIVE

	INCENTIVE_STATUS_ACTIVE            = "ACTIVE"
	INCENTIVE_STATUS_INACTIVE          = "INACTIVE"
	INCENTIVE_FROM_SYSTEM              = "SYSTEM"
	INCENTIVE_FROM_SYSTEM_WITH_PARTNER = "SYSTEM_WITH_PARTNER"

	INCENTIVE_TYPE_TASKER            = "TASKER"
	INCENTIVE_TYPE_ASKER             = "ASKER"
	INCENTIVE_TYPE_RECOMMEND_FOR_YOU = "RECOMMEND_FOR_YOU"
	INCENTIVE_TYPE_TOP_DEAL          = "TOP_DEAL"
	INCENTIVE_TYPE_EXCLUSIVE_DEAL    = "EXCLUSIVE_DEAL"
	INCENTIVE_TYPE_FLASH_SALE        = "FLASH_SALE"
	INCENTIVE_TYPE_LIXI_TASKER       = "LIXI_TASKER"

	// ========================= REDEEM GIFT TRANSACTION

	REDEEM_GIFT_TRANSACTION_STATUS_NEW     = "NEW"
	REDEEM_GIFT_TRANSACTION_STATUS_SUCCESS = "SUCCESS"
	REDEEM_GIFT_TRANSACTION_STATUS_ERROR   = "ERROR"

	// ========================= TRANSACTION

	TRANSACTION_TYPE_TOP_UP_CREDIT = "TOP_UP_CREDIT"

	TRANSACTION_STATUS_WAITING = "WAITING"
	TRANSACTION_STATUS_PAID    = "PAID"
	TRANSACTION_STATUS_ERROR   = "ERROR"
	TRANSACTION_STATUS_FAILED  = "FAILED"

	// ========================= MOCAWALLET

	MOCA_WALLET_STATUS_ACTIVE = "ACTIVE"

	// ========================= GRAB BY MOCA TRANSACTION

	GRAB_PAY_BY_MOCA_TRANSACTION_STATUS_NEW = "NEW"

	// ========================= ZALO PAY TRANSACTION

	ZALO_PAY_TRANSACTION_STATUS_NEW      = "NEW"
	ZALO_PAY_TRANSACTION_STATUS_CHARGING = "CHARGING"
	ZALO_PAY_TRANSACTION_STATUS_ERROR    = "ERROR"
	ZALO_PAY_TRANSACTION_STATUS_PAID     = "PAID"

	// ========================= SETTINGS

	SETTINGS_PLATFORM_ANDROID = "android"
	SETTINGS_PLATFORM_IOS     = "ios"

	// ========================= PAYMENT PROCESS

	PAYMENT_PROCESS_STATUS_INPROCESS = "INPROCESS"
	PAYMENT_PROCESS_STATUS_ERROR     = "ERROR"
	PAYMENT_PROCESS_STATUS_SUCCESS   = "SUCCESS"

	// ========================= TASKER REFERRAL

	TASKER_REFERRAL_STATUS_ACTIVE = "ACTIVE"

	// ========================= NOT_COME_LOCK_HISTORY
	NOT_COME_LOCK_HISTORY_STATUS_LOCKED   = "LOCKED"
	NOT_COME_LOCK_HISTORY_STATUS_UNLOCKED = "UNLOCKED"

	NOT_COME_LOCK_HISTORY_TYPE_LOCK = "LOCK"

	// ========================= GIFT
	GIFT_SOURCE_SYSTEM              = "SYSTEM"
	GIFT_SOURCE_SYSTEM_WITH_PARTNER = "SYSTEM_WITH_PARTNER"

	GIFT_FROM_PAY_COMBO_VOUCHER = "PAY_COMBO_VOUCHER"

	// ========================= PROMOTION_CODE
	PROMOTION_CODE_SOURCE_SYSTEM        = "SYSTEM"
	PROMOTION_CODE_SOURCE_SURVEY        = "SURVEY"
	PROMOTION_CODE_SOURCE_BREWARDS      = "BREWARDS"
	PROMOTION_CODE_SOURCE_REFERRAL      = "REFERRAL"
	PROMOTION_CODE_SOURCE_CLEVERTAP     = "CLEVERTAP"
	PROMOTION_CODE_SOURCE_COMBO_VOUCHER = "COMBO_VOUCHER"

	// ========================= PAYMENT METHOD STATUS
	PAYMENT_METHOD_STATUS_ACTIVE   = "ACTIVE"
	PAYMENT_METHOD_STATUS_PENDING  = "PENDING"
	PAYMENT_METHOD_STATUS_INACTIVE = "INACTIVE"

	// ========================= PAYMENT
	PAYMENT_GATEWAY_ADYEN    = "ADYEN"
	PAYMENT_GATEWAY_2C2P     = "2C2P"
	PAYMENT_GATEWAY_MIDTRANS = "MIDTRANS"
	PAYMENT_GATEWAY_MB_BANK  = "MB_BANK"
	PAYMENT_GATEWAY_DANA     = "DANA"

	// ========================= STORE GROCERY ASSISTANT

	STORE_GROCERY_ASSISTANT_STATUS_ACTIVE   = "ACTIVE"
	STORE_GROCERY_ASSISTANT_STATUS_INACTIVE = "INACTIVE"

	// ========================= PRODUCT GROCERY ASSISTANT

	PRODUCT_GROCERY_ASSISTANT_STATUS_ACTIVE   = "ACTIVE"
	PRODUCT_GROCERY_ASSISTANT_STATUS_INACTIVE = "INACTIVE"
	PAYMENT_STATUS_PAID                       = "PAID"
	PAYMENT_STATUS_ERROR                      = "ERROR"
	PAYMENT_STATUS_NEW                        = "NEW"
	PAYMENT_STATUS_REFUND                     = "REFUND"

	// ========================= REFUND REQUEST

	REFUND_REQUEST_TYPE_PREPAY_TASK            = "PREPAY_TASK"
	REFUND_REQUEST_TYPE_SHOPEE_PAY_PREPAY_TASK = "SHOPEE_PAY_PREPAY_TASK"
	REFUND_REQUEST_TYPE_MOMO_PREPAY_TASK       = "MOMO_PREPAY_TASK"
	REFUND_REQUEST_TYPE_ZALO_PAY_PREPAY_TASK   = "ZALO_PAY_PREPAY_TASK"
	REFUND_REQUEST_TYPE_VN_PAY_PREPAY_TASK     = "VN_PAY_PREPAY_TASK"
	REFUND_REQUEST_TYPE_VIET_QR_PREPAY_TASK    = "VIET_QR_PREPAY_TASK"
	REFUND_REQUEST_TYPE_SYSTEM_REFUND          = "SYSTEM_REFUND"

	REFUND_REQUEST_STATUS_NEW         = "NEW"
	REFUND_REQUEST_STATUS_PAID        = "PAID"
	REFUND_REQUEST_STATUS_ERROR       = "ERROR"
	REFUND_REQUEST_STATUS_PROCESSING  = "PROCESSING"
	REFUND_REQUEST_STATUS_REFUND_BPAY = "REFUND_BPAY"
	REFUND_REQUEST_STATUS_CANCEL      = "CANCEL"

	REFUND_REQUEST_REASON_TASK_EXPIRED                = "TASK_EXPIRED"
	REFUND_REQUEST_REASON_INTEGRATE_CARD_FEE          = "INTEGRATE_CARD_FEE"
	REFUND_REQUEST_REASON_CARD_NON_3DS                = "CARD_NON_3DS"
	REFUND_REQUEST_REASON_REFUND_RATING_NOT_COMING    = "REFUND_RATING_NOT_COMING"
	REFUND_REQUEST_REASON_ASKER_CHANGE_PAYMENT_METHOD = "REFUND_RATING_NOT_COMING"

	// ========================= PAYMENT_PROCESS 2C2P

	PAYMENT_PROCESS_2C2P_TYPE_REFUND  = "R"
	PAYMENT_PROCESS_2C2P_TYPE_VOID    = "V"
	PAYMENT_PROCESS_2C2P_TYPE_INQUIRY = "I"

	PAYMENT_PROCESS_RESPONSE_STATUS_APPROVED                = "A"
	PAYMENT_PROCESS_RESPONSE_STATUS_SETTLED                 = "S"
	PAYMENT_PROCESS_RESPONSE_STATUS_AUTHENTICATION_REJECTED = "AR"

	// ========================= PROMOTION PAYMENT METHOD
	TASK_PROMOTION_KEY_PROMOTION_PAYMENT_METHOD = "PROMOTION_PAYMENT_METHOD"

	PROMOTION_PAYMENT_METHOD_TYPE_OF_PROMOTION_NEW     = "NEW"
	PROMOTION_PAYMENT_METHOD_TYPE_OF_PROMOTION_CURRENT = "CURRENT"
	PROMOTION_PAYMENT_METHOD_TYPE_OF_PROMOTION_BOTH    = "BOTH"
	PROMOTION_PAYMENT_METHOD_STATUS_ACTIVE             = "ACTIVE"
	PROMOTION_PAYMENT_METHOD_STATUS_INACTIVE           = "INACTIVE"

	// ========================= GO MARKET WITH STORE
	GO_MARKET_WITH_STORE_STATUS_ACTIVE   = "ACTIVE"
	GO_MARKET_WITH_STORE_STATUS_INACTIVE = "INACTIVE"

	PAYLOAD_NAVIGATE_TO_TASK_DETAIL                  = "TaskDetail"
	PAYLOAD_NAVIGATE_TO_MY_REWARDS                   = "MyRewards"
	PAYLOAD_NAVIGATE_TO_CHAT                         = "Chat"
	PAYLOAD_NAVIGATE_TO_REWARD_DETAIL                = "RewardDetail"
	PAYLOAD_NAVIGATE_TO_USER_BPAY                    = "UserBpay"
	PAYLOAD_NAVIGATE_TO_QUIZZ_FOR_TASKER_PREMIUM     = "QuizForTaskerPremium"
	PAYLOAD_NAVIGATE_TO_TASK_UPDATE                  = "TaskUpdate"
	PAYLOAD_NAVIGATE_TO_MONTHLY_REWARD_DETAIL        = "MonthlyRewardDetail"
	PAYLOAD_NAVIGATE_TO_TRAINING_INPUT               = "TrainingInput"
	PAYLOAD_NAVIGATE_TO_TRAINING_PROGRAM_LIST        = "TrainingProgramList"
	PAYLOAD_NAVIGATE_TO_FINANCE                      = "Finance"
	PAYLOAD_NAVIGATE_TO_BPOINTS_HISTORY              = "bPointsHistory"
	PAYLOAD_NAVIGATE_TO_TASK_HISTORY_DETAIL          = "TaskHistoryDetail"
	PAYLOAD_NAVIGATE_TO_GIFT_DETAIL                  = "GiftDetail"
	PAYLOAD_NAVIGATE_TO_MEMBER_DETAIL                = "MemberDetail"
	PAYLOAD_NAVIGATE_TO_BNPL_TRANSACTION_HISTORY     = "BNPLTransactionHistory"
	PAYLOAD_NAVIGATE_TO_NOTIFICATION_DETAIL          = "NotificationDetail"
	PAYLOAD_NAVIGATE_TO_USER_REWARDS                 = "UserRewards"
	PAYLOAD_NAVIGATE_TO_SURVEY_DETAIL                = "SurveyDetail"
	PAYLOAD_NAVIGATE_TO_LIST_COURSE                  = "ListCourse"
	PAYLOAD_NAVIGATE_TO_TAB_HOME                     = "TabHome"
	PAYLOAD_NAVIGATE_TO_LIST_EMPLOYEE                = "ListEmployee"
	PAYLOAD_NAVIGATE_TO_USER_BPAY_BUSINESS           = "UserBpayBusiness"
	PAYLOAD_NAVIGATE_TO_TRANSACTION_BUSINESS         = "TransactionBusiness"
	PAYLOAD_NAVIGATE_TO_TRANSACTION_BUSINESS_ACCOUNT = "TransactionsBusinessAccountScreen"
	PAYLOAD_NAVIGATE_TO_TRANSACTION_MEMBER           = "HistoryTransaction"
	PAYLOAD_NAVIGATE_TO_GAME_LUCKY_DRAW              = "GameLuckyDraw"
	PAYLOAD_NAVIGATE_TO_MY_GIFT                      = "MyGift"
	PAYLOAD_NAVIGATE_TO_COMMUNITY_NOTIFICATION       = "NotificationCommunity"
	PAYLOAD_NAVIGATE_TO_COMMUNITY_POST_DETAIL        = "NewsFeedDetail"
	PAYLOAD_NAVIGATE_TO_REVIEW_COLLECTION_LIST       = "ReviewCollectionList"
	PAYLOAD_NAVIGATE_TO_MY_REWARDS_DETAIL            = "MyRewardsDetail"

	// ========================= PARTNER CODE
	PARTNER_CODE_TIKI_MINI_APP = "TIKI_MINI_APP"

	// ========================= TIKI MINI APP
	TIKI_ORDER_STATUS_ONLINE_PAID     = "online_paid"
	TIKI_ORDER_STATUS_ONLINE_CANCELED = "canceled"

	TIKI_MINI_APP_TRANSACTION_STATUS_PAID     = "PAID"
	TIKI_MINI_APP_TRANSACTION_STATUS_CANCELED = "CANCELED"
	TIKI_MINI_APP_TRANSACTION_STATUS_SENDING  = "SENDING"
	TIKI_MINI_APP_TRANSACTION_STATUS_ERROR    = "ERROR"

	// ============== training tasker
	TRAINING_TASKER_STATUS_INACTIVE               = "INACTIVE"
	TRAINING_TASKER_STATUS_ACTIVE                 = "ACTIVE"
	TRAINING_TASKER_TYPE_TRAINING_INPUT           = "TRAINING_INPUT"
	TRAINING_TASKER_TYPE_TRAINING_OVERVIEW        = "TRAINING_OVERVIEW"
	TRAINING_TASKER_TYPE_TRAINING_QUALITY         = "TRAINING_QUALITY"
	TRAINING_TASKER_TYPE_TRAINING_REGULATIONS     = "TRAINING_REGULATIONS"
	TRAINING_TASKER_TYPE_TRAINING_BASIC           = "TRAINING_BASIC"
	TRAINING_TASKER_TYPE_TRAINING_ADVANCED        = "TRAINING_ADVANCED"
	TASKER_CHECK_INPUT_TASKER_INPUT               = "TASKER_INPUT"
	TASKER_CHECK_INPUT_TASKER_PASS                = "TASKER_PASS"
	TASKER_CHECK_INPUT_TASKER_OVERVIEW            = "TASKER_OVERVIEW"
	TASKER_CHECK_INPUT_TASKER_QUALITY             = "TASKER_QUALITY"
	TASKER_CHECK_INPUT_TASKER_REGULATIONS         = "TASKER_REGULATIONS"
	TRAINING_TASKER_HISTORY_STATUS_FAILED         = "FAILED"
	TRAINING_TASKER_HISTORY_STATUS_PASSED         = "PASS"
	TRAINING_TASKER_HISTORY_STATUS_PROCESSING     = "PROCESSING"
	TASKER_CHECK_INPUT_TASKER_BASIC               = "TASKER_BASIC"
	TRAINING_TASKER_HISTORY_STATUS_WATCHED_VIDEOS = "WATCHED_VIDEOS"

	REWARD_TYPE_TASKER_MONTHLY_REWARD = "TASKER_MONTHLY_REWARD"
	REWARD_FROM_JOURNEY_REWARD        = "JOURNEY"

	WEBSOCKET_TASK_SOURCE_NEW_TASK       = "newTask"
	WEBSOCKET_TASK_SOURCE_CONFIRMED_TASK = "confirmedTask"
	WEBSOCKET_KEY_TASK                   = "task"
	WEBSOCKET_KEY_CHAT                   = "chat"
	WEBSOCKET_KEY_NOTIFICATION           = "notification"
	WEBSOCKET_KEY_PAYMENT                = "payment"

	COMBO_VOUCHER_STATUS_ACTIVE   = "ACTIVE"
	COMBO_VOUCHER_STATUS_INACTIVE = "INACTIVE"

	COMBO_VOUCHER_TYPE_COMBO_VOUCHER = "COMBO_VOUCHER"
	COMBO_VOUCHER_TYPE_SUBSCRIPTION  = "SUBSCRIPTION"

	USER_COMBO_VOUCHER_STATUS_ACTIVE  = "ACTIVE"
	USER_COMBO_VOUCHER_STATUS_EXPIRED = "EXPIRED"

	REFERRAL_CAMPAIGN_STATUS_ACTIVE   = "ACTIVE"
	REFERRAL_CAMPAIGN_STATUS_INACTIVE = "INACTIVE"

	MARKETING_CAMPAIN_TARGET_APPLY_CURRENT = "CURRENT"
	MARKETING_CAMPAIN_TARGET_APPLY_BOTH    = "BOTH"
	MARKETING_CAMPAIN_TARGET_APPLY_NEW     = "NEW"

	// =============== TASK ITEM
	TASK_ITEM_STATUS_NEW       = "NEW"
	TASK_ITEM_STATUS_CONFIRMED = "CONFIRMED"

	TASK_ITEM_REQUIREMENT_PET                      = "pet"
	TASK_ITEM_REQUIREMENT_IRON                     = "iron"
	TASK_ITEM_REQUIREMENT_COOK                     = "cook"
	TASK_ITEM_REQUIREMENT_BRINGTOOLS               = "bringTools"
	TASK_ITEM_REQUIREMENT_GO_MARKET                = "goMarket"
	TASK_ITEM_REQUIREMENT_SUBSCRIPTION             = "subscription"
	TASK_ITEM_REQUIREMENT_PREMIUM                  = "premium"
	TASK_ITEM_REQUIREMENT_TET                      = "tet"
	TASK_ITEM_REQUIREMENT_HAVE_FRUIT               = "haveFruit"
	TASK_ITEM_REQUIREMENT_WORKED_FOR_ASKER         = "workedForAsker"
	TASK_ITEM_REQUIREMENT_CLEANING_GLASSES         = "cleaningGlasses"
	TASK_ITEM_REQUIREMENT_VACUUMING_OFFICE_CARPETS = "vacuumingOfficeCarpets"
	TASK_ITEM_REQUIREMENT_AIR_CONDITIONER          = "airConditioner"
	TASK_ITEM_REQUIREMENT_WATER_HEATER             = "waterHeater"
	TASK_ITEM_REQUIREMENT_APARTMENT                = "apartment"
	TASK_ITEM_REQUIREMENT_FRIDGE_CLEANING          = "fridgeCleaning"

	TASK_ADDONS_APARTMENT = "APARTMENT"

	// =============== TASKER PROFILE
	TASKER_PROFILE_STATUS_PASSED       = "PASS"
	TASKER_PROFILE_STATUS_PROCESSING   = "PROCESSING"
	TASKER_PROFILE_STATUS_APPROVED     = "APPROVED"
	TASKER_PROFILE_STATUS_REJECTED     = "REJECTED"
	TASKER_PROFILE_STATUS_ELIMINATED   = "ELIMINATED"
	TASKER_PROFILE_STATUS_VERIFYING    = "VERIFYING"
	TASKER_PROFILE_STATUS_UPDATED      = "UPDATED"
	TASKER_PROFILE_STATUS_FAIL_UPDATED = "FAIL_UPDATED"
	TASKER_PROFILE_STATUS_NEEDS_UPDATE = "NEEDS_UPDATE"
	TASKER_PROFILE_STATUS_SCHEDULED    = "SCHEDULED"

	TASKER_PROFILE_IMAGE_STATUS_REJECTED = "REJECTED"
	TASKER_PROFILE_IMAGE_STATUS_UPDATED  = "UPDATED"
	TASKER_PROFILE_IMAGE_STATUS_APPROVED = "APPROVED"

	TASKER_PROFILE_PROOF_OF_RESIDENCE_HOUSEHOLD    = "PROOF_OF_RESIDENCE"
	TASKER_PROFILE_RESIDENCE_INFORMATION_HOUSEHOLD = "RESIDENCE_INFORMATION"

	// =============== PAYMENT CARD
	PAYMENT_CARD_STATUS_EXPIRED  = "EXPIRED"
	PAYMENT_CARD_STATUS_ACTIVE   = "ACTIVE"
	PAYMENT_CARD_STATUS_DISABLED = "DISABLED"

	// ========================= JOURNEY SETTING

	JOURNEY_SETTING_STATUS_ACTIVE   = "ACTIVE"
	JOURNEY_SETTING_STATUS_INACTIVE = "INACTIVE"

	// ========================= JOURNEY LEVEL
	JOURNEY_LEVEL_CONDITION_NAME_TRAINING                   = "TRAINING"
	JOURNEY_LEVEL_CONDITION_NAME_TASK                       = "TASK"
	JOURNEY_LEVEL_CONDITION_NAME_TASK_IN_MONTH              = "TASK_IN_MONTH"
	JOURNEY_LEVEL_CONDITION_NAME_DURATION_IN_MONTH          = "DURATION_IN_MONTH"
	JOURNEY_LEVEL_CONDITION_NAME_MONTH_IN_ROW               = "MONTH_IN_ROW"
	JOURNEY_LEVEL_CONDITION_NAME_DURATION_LAST_MONTH        = "DURATION_LAST_MONTH"
	JOURNEY_LEVEL_CONDITION_NAME_DURATION_IN_MONTH_UP_LEVEL = "DURATION_IN_MONTH_UP_LEVEL"

	JOURNEY_LEVEL_REWARD_NAME_BPOINT = "BPOINT"

	JOURNEY_LEVEL_BONUS_NAME_BREWARD     = "BREWARD"
	JOURNEY_LEVEL_BONUS_NAME_MONEY       = "MONEY"
	JOURNEY_LEVEL_BONUS_NAME_BPOINT      = "BPOINT"
	JOURNEY_LEVEL_BONUS_NAME_BONUS_PRIZE = "BONUS_PRIZE"

	JOURNEY_LEVEL_STATUS_PASSED     = "PASSED"
	JOURNEY_LEVEL_STATUS_PROCESSING = "PROCESSING"
	JOURNEY_LEVEL_STATUS_LOCKED     = "LOCKED"

	JOURNEY_LEVEL_CONDITION_STATUS_PASSED     = "PASSED"
	JOURNEY_LEVEL_CONDITION_STATUS_PROCESSING = "PROCESSING"

	JOURNEY_LEVEL_CONDITION_TRAINING_STATUS_PASSED     = "PASSED"
	JOURNEY_LEVEL_CONDITION_TRAINING_STATUS_INCOMPLETE = "INCOMPLETE"

	JOURNEY_LEVEL_PERMISSION_LOCK = "LOCK"
	JOURNEY_LEVEL_PERMISSION_OPEN = "OPEN"

	BENEFIT_KEY_NAME_TRAINING_PROGRAM = "trainingProgram"
	BENEFIT_KEY_NAME_BCARE            = "bCare"
	BENEFIT_KEY_NAME_TRAINING_PREMIUM = "trainingPremium"
	BENEFIT_KEY_NAME_BREWARD          = "bReward"
	BENEFIT_KEY_NAME_COMMUNITY        = "community"
	BENEFIT_KEY_NAME_MONTHLY_REWARD   = "monthlyReward"
	BENEFIT_KEY_NAME_THINGS_TO_KNOW   = "thingsToKnow"

	BREWARD_CATEGORY_STATUS_ACTIVE   = "ACTIVE"
	BREWARD_CATEGORY_STATUS_INACTIVE = "INACTIVE"

	FLASH_SALE_STATUS_ACTIVE   = "ACTIVE"
	FLASH_SALE_STATUS_INACTIVE = "INACTIVE"

	VAT_REQUEST_STATUS_NEW = "NEW"

	KEY_FROM_ASKER_APP  = "ASKER_APP"
	KEY_FROM_TASKER_APP = "TASKER_APP"
	KEY_FROM_BACKEND    = "BACKEND"

	ASKER_RANK_NAME_MEMBER   = "MEMBER"
	ASKER_RANK_NAME_SILVER   = "SILVER"
	ASKER_RANK_NAME_GOLD     = "GOLD"
	ASKER_RANK_NAME_PLATINUM = "PLATINUM"

	CHAT_MESSAGE_BY_SYSTEM_KEY_ALERT_TASK_START            = "ALERT_TASK_START"
	CHAT_MESSAGE_BY_SYSTEM_KEY_ALERT_TASK_DONE             = "ALERT_TASK_DONE"
	CHAT_MESSAGE_BY_SYSTEM_KEY_ALERT_GUARANTEED_TASK       = "GUARANTEED_TASK"
	CHAT_MESSAGE_BY_SYSTEM_KEY_TASKER_NON_RESPONSE_REQUEST = "TASKER_NON_RESPONSE_REQUEST"

	CHAT_MESSAGE_BY_SYSTEM_SEND_TO_TASKER = "TASKER"
	CHAT_MESSAGE_BY_SYSTEM_SEND_TO_ASKER  = "ASKER"
	CHAT_MESSAGE_BY_SYSTEM_SEND_TO_BOTH   = "BOTH"

	CHAT_MESSAGE_FROM_SYSTEM = "SYSTEM"
	CHAT_MESSAGE_FROM_ASKER  = "ASKER"
	CHAT_MESSAGE_FROM_TASKER = "TASKER"

	CHAT_MESSAGE_REQUEST_STATUS_WAITING  = "WAITING"
	CHAT_MESSAGE_REQUEST_STATUS_APPROVED = "APPROVED"
	CHAT_MESSAGE_REQUEST_STATUS_REJECTED = "REJECTED"
	CHAT_MESSAGE_REQUEST_STATUS_EXPIRED  = "EXPIRED"

	CHAT_MESSAGE_REQUEST_TYPE_UPDATE_DATE_TIME  = "UPDATE_DATE_TIME"
	CHAT_MESSAGE_REQUEST_TYPE_INCREASE_DURATION = "INCREASE_DURATION"
	CHAT_MESSAGE_REQUEST_TYPE_UPDATE_DETAIL     = "UPDATE_DETAIL"

	CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_PRIMARY   = "PRIMARY"
	CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_SECONDARY = "SECONDARY"

	CHAT_MESSAGE_BY_SYSTEM_ACTION_TYPE_PRIMARY   = "PRIMARY"
	CHAT_MESSAGE_BY_SYSTEM_ACTION_TYPE_SECONDARY = "SECONDARY"

	CHAT_MESSAGE_TYPE_TASK_IS_CANCELLED = "TASK_IS_CANCELLED"

	CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_UPDATE_TASK_AND_REMOVE_TASKER  = "UPDATE_TASK_AND_REMOVE_TASKER"
	CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_NAVIGATE_TO_UPDATE_TASK_SCREEN = "NAVIGATE_TO_UPDATE_TASK_SCREEN"
	CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_DISABLE_ACTION                 = "DISABLE_ACTION"

	GAME_CAMPAIGN_STATUS_ACTIVE = "ACTIVE"

	GAME_CAMPAIGN_TYPE_CHALLENGE_GAME       = "CHALLENGE_GAME"
	GAME_CAMPAIGN_QUIZ_TYPE_ONE_CHOICE      = "ONE_CHOICE"
	GAME_CAMPAIGN_QUIZ_TYPE_MULTIPLE_CHOICE = "MULTIPLE_CHOICE"

	GAME_CAMPAIGN_REWARD_WINNING_RULE_RANDOM_REWARD  = "RANDOM_REWARD"
	GAME_CAMPAIGN_REWARD_WINNING_RULE_ALL_OF_REWARDS = "ALL_OF_REWARDS"

	GAME_CAMPAIGN_CHALLENGE_STATUS_COMPLETED    = "COMPLETED"
	GAME_CAMPAIGN_CHALLENGE_STATUS_OPEN         = "OPEN"
	GAME_CAMPAIGN_CHALLENGE_STATUS_LOCKED       = "LOCKED"
	GAME_CAMPAIGN_CHALLENGE_QUIZ_STATUS_PASSED  = "PASSED"
	GAME_CAMPAIGN_CHALLENGE_QUIZ_STATUS_FAILED  = "FAILED"
	GAME_CAMPAIGN_CHALLENGE_TYPE_QUIZ           = "QUIZ"
	GAME_CAMPAIGN_CHALLENGE_TYPE_COMMUNITY_POST = "COMMUNITY_POST"
	GAME_CAMPAIGN_CHALLENGE_TYPE_SURVEY         = "SURVEY"

	TASK_BNPL_PROCESS_STATUS_PAYING   = "PAYING"
	TASK_BNPL_PROCESS_STATUS_DONE     = "DONE"
	TASK_BNPL_PROCESS_STATUS_OVER_DUE = "OVER_DUE"
	TASK_BNPL_PROCESS_STATUS_STOPPED  = "STOPPED"

	PAYMENT_TOOL_KIT_TRANSACTION_STATUS_WAITING_TASKER_RECEIVE = "WAITING_TASKER_RECEIVE"
	PAYMENT_TOOL_KIT_TRANSACTION_STATUS_TASKER_RECEIVED        = "TASKER_RECEIVED"
	PAYMENT_TOOL_KIT_TRANSACTION_STATUS_INIT                   = "INIT"
	PAYMENT_TOOL_KIT_TRANSACTION_STATUS_EXPIRED                = "EXPIRED"

	SURVEY_SETTING_STATUS_ACTIVE   = "ACTIVE"
	SURVEY_SETTING_STATUS_INACTIVE = "INACTIVE"

	SURVEY_SETTING_FIRST_DONE_TASK = "FIRST_DONE_TASK"

	HOME_MOVING_STEP_STATUS_COMPLETED   = "COMPLETED"
	HOME_MOVING_STEP_STATUS_IN_PROGRESS = "IN_PROGRESS"

	TASK_MASSAGE_TYPE_SEQUENCE       = "SEQUENCE"
	TASK_MASSAGE_TYPE_SIMULTANEOUSLY = "SIMULTANEOUSLY"

	TASK_BEAUTY_CARE_TYPE_SEQUENCE       = "SEQUENCE"
	TASK_BEAUTY_CARE_TYPE_SIMULTANEOUSLY = "SIMULTANEOUSLY"

	// KAFKA
	MESSAGE_QUEUE_PREFIX_RETRY       = "retry"
	MESSAGE_QUEUE_PREFIX_DEAD_LETTER = "dlq"

	MESSAGE_QUEUE_TOPIC_CHAT_MESSAGE                        = "events.chat.vn.message-sent.v1"
	MESSAGE_QUEUE_TOPIC_CHAT_MESSAGE_TH                     = "events.chat.th.message-sent.v1"
	MESSAGE_QUEUE_TOPIC_CHAT_MESSAGE_INDO                   = "events.chat.indo.message-sent.v1"
	MESSAGE_QUEUE_TOPIC_CHAT_MESSAGE_MY                     = "events.chat.my.message-sent.v1"
	MESSAGE_QUEUE_TOPIC_EVENTS_DONE_BOOKING_VN_TASK_DONE_V1 = "events.done-booking.vn.task-done.v1"
	MESSAGE_QUEUE_TYPE_KAFKA                                = "KAFKA"

	MESSAGE_QUEUE_EVENT_NAME_NEW_MESSAGE    = "NEW_MESSAGE"
	MESSAGE_QUEUE_EVENT_NAME_UPDATE_MESSAGE = "UPDATE_MESSAGE"

	PUB_SUB_TYPE_REDIS = "REDIS"

	CONSUMER_GROUP_ID_CHAT_DAL_VN     = "go-chat-dal-vn"
	CONSUMER_GROUP_ID_CHAT_SERVER_VN  = "go-chat-server-vn"
	CONSUMER_GROUP_ID_NOTIFICATION_VN = "go-push-notification-vn"
	CONSUMER_GROUP_ID_EMAIL_VN        = "go-email-service-vn"

	MESSAGE_QUEUE_OFFSET_NEWEST = "newest"

	MESSAGE_SUPPLIER_ZALO     = "ZALO"
	MESSAGE_SUPPLIER_SMS      = "SMS"
	MESSAGE_SUPPLIER_WHATSAPP = "WHATSAPP"

	// AIR CONDITIONER
	AIR_CONDITIONER_TYPE_SERVICE_CLEANING = "Cleaning"

	BPOINT_TRANSACTION_SOURCE_NAME_VOTE_FAV_TASKER = "VOTE_FAV_TASKER"

	// CHAT MESSAGE
	CHAT_MESSAGE_REQUEST_DATA_TEMPLATE_NAME_UPDATE_TASK_DETAIL = "UPDATE_TASK_DETAIL"
	CHAT_MESSAGE_ACTION_UPDATE_CHAT_MESSAGE                    = "UPDATE_CHAT_MESSAGE"

	// Payment method campaign
	PAYMENT_METHOD_CAMPAIGN_STATUS_ACTIVE   = "ACTIVE"
	PAYMENT_METHOD_CAMPAIGN_STATUS_INACTIVE = "INACTIVE"

	// COMMUNITY
	COMMUNITY_USER_STATUS_ACTIVE   = "ACTIVE"
	COMMUNITY_USER_STATUS_INACTIVE = "INACTIVE"
	COMMUNITY_USER_STATUS_BLOCKED  = "BLOCKED"
	COMMUNITY_USER_STATUS_REPORTED = "REPORTED"

	COMMUNITY_COMMENT_STATUS_ACTIVE   = "ACTIVE"
	COMMUNITY_COMMENT_STATUS_INACTIVE = "INACTIVE"
	COMMUNITY_COMMENT_STATUS_HIDDEN   = "HIDDEN"

	COMMUNITY_USER_REPORT_STATUS_ACTIVE   = "ACTIVE"
	COMMUNITY_USER_REPORT_STATUS_INACTIVE = "INACTIVE"

	COMMUNITY_MEDAL_STATUS_ACTIVE                              = "ACTIVE"
	COMMUNITY_MEDAL_STATUS_INACTIVE                            = "INACTIVE"
	COMMUNITY_MEDAL_TYPE_LEVEL                                 = "LEVEL"
	COMMUNITY_MEDAL_TYPE_INTERACT                              = "INTERACT"
	COMMUNITY_MEDAL_CONDITION_TYPE_NUMBER_OF_LIKES             = "NUMBER_OF_LIKES"
	COMMUNITY_MEDAL_CONDITION_TYPE_NUMBER_OF_SHARES            = "NUMBER_OF_SHARES"
	COMMUNITY_MEDAL_CONDITION_TYPE_NUMBER_OF_POSTS             = "NUMBER_OF_POSTS"
	COMMUNITY_MEDAL_CONDITION_TYPE_NUMBER_OF_SERVICES_BOOKINGS = "NUMBER_OF_SERVICES_BOOKINGS"

	// Post status
	COMMUNITY_POST_STATUS_ACTIVE     = "ACTIVE"
	COMMUNITY_POST_STATUS_INACTIVE   = "INACTIVE"
	COMMUNITY_POST_STATUS_DELETED    = "DELETED"
	COMMUNITY_POST_STATUS_NEED_HELP  = "NEED_HELP"
	COMMUNITY_POST_STATUS_NOT_POSTED = "NOT_POSTED"
	// Task list in task
	TODO_LIST_STATUS_NEW  = "NEW"
	TODO_LIST_STATUS_DONE = "DONE"

	// Business
	BUSINESS_STATUS_REGISTERED = "REGISTERED"
	BUSINESS_STATUS_ACTIVE     = "ACTIVE"
	BUSINESS_STATUS_REJECTED   = "REJECTED"
	BUSINESS_STATUS_INACTIVE   = "INACTIVE"
	BUSINESS_STATUS_VERIFYING  = "VERIFYING"

	BUSINESS_LEVEL_STATUS_ACTIVE  = "ACTIVE"
	BUSINESS_LEVEL_STATUS_DELETED = "DELETED"

	BUSINESS_MEMBER_STATUS_ACTIVE   = "ACTIVE"
	BUSINESS_MEMBER_STATUS_INACTIVE = "INACTIVE"

	BUSINESS_TRANSACTION_NAME_PREFIX                 = "BUSINESS_TRANSACTION_NAME_" // use with name below for localize
	BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_BY_BTASKEE = "TOP_UP_BPAY_BY_BTASKEE"
	BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_MEMBER     = "TOP_UP_BPAY_MEMBER"
	BUSINESS_TRANSACTION_NAME_REVOKE_BPAY_MEMBER     = "REVOKE_BPAY_MEMBER"

	BUSINESS_TRANSACTION_TYPE_DEPOSIT = "D"
	BUSINESS_TRANSACTION_TYPE_CREDIT  = "C"

	BUSINESS_MEMBER_TRANSACTION_NAME_PREFIX                   = "BUSINESS_MEMBER_TRANSACTION_NAME_" // use for separate localize business transaction and main transaction
	BUSINESS_MEMBER_TRANSACTION_NAME_TOP_UP_BPAY_BY_BUSINESS  = "TOP_UP_BPAY_BY_BUSINESS"
	BUSINESS_MEMBER_TRANSACTION_NAME_REVOKE_BPAY_BY_BUSINESS  = "REVOKE_BPAY_BY_BUSINESS"
	BUSINESS_MEMBER_TRANSACTION_NAME_RATING_TIP               = "RATING_TIP"
	BUSINESS_MEMBER_TRANSACTION_NAME_CANCEL_TASK              = "CANCEL_TASK"
	BUSINESS_MEMBER_TRANSACTION_NAME_PAY_SUBSCRIPTION         = "PAY_SUBSCRIPTION"
	BUSINESS_MEMBER_TRANSACTION_NAME_TASK                     = "TASK"
	BUSINESS_MEMBER_TRANSACTION_NAME_COMBO_VOUCHER            = "COMBO_VOUCHER"
	BUSINESS_MEMBER_TRANSACTION_NAME_REFUND_CANCEL_TASK       = "REFUND_CANCEL_TASK"
	BUSINESS_MEMBER_TRANSACTION_NAME_REFUND_RATING_NOT_COMING = "REFUND_RATING_NOT_COMING"
	BUSINESS_MEMBER_TRANSACTION_NAME_REFUND_EXPIRED_TASK      = "REFUND_EXPIRED_TASK"
	BUSINESS_MEMBER_TRANSACTION_NAME_UPDATE_TASK              = "UPDATE_TASK"

	BUSINESS_SCHEDULE_KEY_LAST_DAY_IN_MONTH  = "LAST_DAY_IN_MONTH"
	BUSINESS_SCHEDULE_KEY_FIRST_DAY_IN_MONTH = "FIRST_DAY_IN_MONTH"

	//message call status
	CALL_STATUS_SUCCESS = "SUCCESS"
	CALL_STATUS_MISSED  = "MISSED"

	//keyword status
	KEYWORD_STATUS_ACTIVE   = "ACTIVE"
	KEYWORD_STATUS_INACTIVE = "INACTIVE"
	KEYWORD_ACTION_BOOKING  = "BOOKING"

	TYPE_MESSAGE_REMIND_AFTER_15_MINUTES_CREATE_TASK                    = "MESSAGE_REMIND_AFTER_15_MINUTES_CREATE_TASK"
	TYPE_MESSAGE_REMIND_AFTER_45_MINUTES_CREATE_TASK                    = "MESSAGE_REMIND_AFTER_45_MINUTES_CREATE_TASK"
	CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_SEND_TO_OTHER_TASKER              = "SEND_TO_OTHER_TASKER"
	CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_REMIND_TASKER                     = "REMIND_TASKER"
	CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_CANCEL_TASK                       = "CANCEL_TASK"
	CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_CHOOSE_OTHER_TIME                 = "CHOOSE_OTHER_TIME"
	CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_ACCEPT_TASK                       = "ACCEPT_TASK"
	CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_REJECT_REQUEST_CHANGE_DATE_OPTION = "REJECT_REQUEST_CHANGE_DATE_OPTION"
	CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_ACCEPT_REQUEST_CHANGE_DATE_OPTION = "ACCEPT_REQUEST_CHANGE_DATE_OPTION"
	CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_REJECT_TASK                       = "REJECT_TASK"
	CHAT_HISTORY_KEY_TASKER_REJECT                                      = "TASKER_REJECT"
	CHANGES_HISTORY_KEY_ASKER_CHOOSE_NEW_DATE_OPTION                    = "ASKER_CHOOSE_NEW_DATE_OPTION"

	BOOK_FORCE_TASKER_STATUS_WAITING   = "WAITING"
	BOOK_FORCE_TASKER_STATUS_SENT_NOTI = "SENT_NOTI"

	MESSAGE_KEY_WELCOME = "WELCOME"
	KEY_BOOK_WITH_FAV   = "BOOK_WITH_FAV"
	KEY_VIEW_TASK       = "VIEW_TASK"
	TYPE_TEXT_ACTION    = "TEXT_ACTION"

	// Add bpoint source name
	ADD_BPOINT_SOURCE_PREFIX                = "ADD_BPOINT_SOURCE_"
	ADD_BPOINT_SOURCE_SHARE_YEAR_END_REPORT = "SHARE_YEAR_END_REPORT"
	//tag
	COMMUNITY_TAG_DISPLAY_POSITION_INTRODUCTION_PAGE       = "INTRODUCTION_PAGE"
	COMMUNITY_TAG_DISPLAY_POSITION_FILTER_MENU_PAGE        = "FILTER_MENU_PAGE"
	COMMUNITY_TAG_DISPLAY_POSITION_CREATE_UPDATE_POST_PAGE = "CREATE_UPDATE_POST_PAGE"

	COMMUNITY_TAG_STATUS_ACTIVE   = "ACTIVE"
	COMMUNITY_TAG_STATUS_INACTIVE = "INACTIVE"

	KEYWORDS_CHAT = "KEYWORDS_CHAT"

	// event config
	EVENT_CONFIG_STATUS_ACTIVE   = "ACTIVE"
	EVENT_CONFIG_STATUS_INACTIVE = "INACTIVE"

	// action from
	ACTION_FROM_PORTAL  = "PORTAL"
	ACTION_FROM_APP     = "APP"
	ACTION_FROM_BACKEND = "BACKEND"

	// accounting journal entry key
	ACCOUNTING_JOURNAL_ENTRY_KEY_PAY_TASK                            = "PAY_TASK"
	ACCOUNTING_JOURNAL_ENTRY_KEY_PAY_SUBSCRIPTION                    = "PAY_SUBSCRIPTION"
	ACCOUNTING_JOURNAL_ENTRY_KEY_TOP_UP_CREDIT                       = "TOP_UP_CREDIT"
	ACCOUNTING_JOURNAL_ENTRY_KEY_CANCEL_TASK_FEE                     = "CANCEL_TASK_FEE"
	ACCOUNTING_JOURNAL_ENTRY_KEY_INTEGRATE_CARD                      = "INTEGRATE_CARD"
	ACCOUNTING_JOURNAL_ENTRY_KEY_REFUND_INTEGRATE_CARD_FEE           = "REFUND_INTEGRATE_CARD_FEE"
	ACCOUNTING_JOURNAL_ENTRY_KEY_PAY_COMBO_VOUCHER                   = "PAY_COMBO_VOUCHER"
	ACCOUNTING_JOURNAL_ENTRY_KEY_RECHARGE                            = "RECHARGE"
	ACCOUNTING_JOURNAL_ENTRY_KEY_PAY_TOOLKIT                         = "PAY_TOOLKIT"
	ACCOUNTING_JOURNAL_ENTRY_KEY_REFUND_CANCEL_SUBSCRIPTION          = "REFUND_CANCEL_SUBSCRIPTION"
	ACCOUNTING_JOURNAL_ENTRY_KEY_REFUND_EXPIRED_TASK                 = "REFUND_EXPIRED_TASK"
	ACCOUNTING_JOURNAL_ENTRY_KEY_REFUND_UPDATE_SUBSCRIPTION          = "REFUND_UPDATE_SUBSCRIPTION"
	ACCOUNTING_JOURNAL_ENTRY_KEY_CHARGE_UPDATE_TASK                  = "CHARGE_UPDATE_TASK"
	ACCOUNTING_JOURNAL_ENTRY_KEY_PROMOTION_SIGN_UP_TASKER            = "PROMOTION_SIGN_UP_TASKER"
	ACCOUNTING_JOURNAL_ENTRY_KEY_REFUND_CANCEL_TASK                  = "REFUND_CANCEL_TASK"
	ACCOUNTING_JOURNAL_ENTRY_KEY_PAY_OUTSTANDING_PAYMENT             = "PAY_OUTSTANDING_PAYMENT"
	ACCOUNTING_JOURNAL_ENTRY_KEY_REFUND                              = "REFUND"
	ACCOUNTING_JOURNAL_ENTRY_KEY_RATING_TIP                          = "RATING_TIP"
	ACCOUNTING_JOURNAL_ENTRY_KEY_NOT_COMMING                         = "NOT_COMMING"
	ACCOUNTING_JOURNAL_ENTRY_KEY_ROLLBACK_TRANSACTION                = "ROLLBACK_TRANSACTION"
	ACCOUNTING_JOURNAL_ENTRY_KEY_REFUND_RATING_NOT_COMING            = "REFUND_RATING_NOT_COMING"
	ACCOUNTING_JOURNAL_ENTRY_KEY_CHARGE_BNPL                         = "CHARGE_BNPL"
	ACCOUNTING_JOURNAL_ENTRY_KEY_GAME_CAMPAIGN                       = "GAME_CAMPAIGN"
	ACCOUNTING_JOURNAL_ENTRY_KEY_REFERRAL                            = "REFERRAL"
	ACCOUNTING_JOURNAL_ENTRY_KEY_TASKER_DONE_TASK                    = "TASKER_DONE_TASK"
	ACCOUNTING_JOURNAL_ENTRY_KEY_DONE_TASK_SHARE_PROMOTION           = "DONE_TASK_SHARE_PROMOTION"
	ACCOUNTING_JOURNAL_ENTRY_KEY_REFERRAL_TASKER_FOR_CAR_ADVERTISING = "REFERRAL_TASKER_FOR_CAR_ADVERTISING"
	ACCOUNTING_JOURNAL_ENTRY_KEY_TASKER_WEEKLY_PAYOUT                = "TASKER_WEEKLY_PAYOUT"
	ACCOUNTING_JOURNAL_ENTRY_KEY_WITHDRAW_EMPLOYEE                   = "WITHDRAW_EMPLOYEE"
	ACCOUNTING_JOURNAL_ENTRY_KEY_BTASKEE_EMPLOYEE_TOPUP              = "BTASKEE_EMPLOYEE_TOPUP"
	ACCOUNTING_JOURNAL_ENTRY_TYPE_REFUND                             = "R"

	// TASKER ACTION HISTORY
	TASKER_ACTION_HISTORY_ACTION_START_WORKING = "START_WORKING"
	TASKER_ACTION_HISTORY_ACTION_DONE_TASK     = "DONE_TASK"
)

const (
	// ========================= MALAY
	SLACK_CHANNEL_ALERT_MY = "alerts-my"

	//bundle voucher
	BUNDLE_VOUCHER_STATUS_ACTIVE   = "ACTIVE"
	BUNDLE_VOUCHER_STATUS_INACTIVE = "INACTIVE"
	BUNDLE_VOUCHER_TYPE_SECRET_BOX = "SECRET_BOX"
	BUNDLE_VOUCHER_TYPE_UNLIMIT    = "UNLIMIT"
	BUNDLE_VOUCHER_TYPE_LIMIT      = "LIMIT"
)

var PAYMENT_METHOD_POST_TASK_BLACKLIST = []string{PAYMENT_METHOD_BANK_TRANSFER, PAYMENT_METHOD_DIRECT_TRANSFER, PAYMENT_METHOD_MOMO, PAYMENT_METHOD_GRAB_PAY_BY_MOCA, PAYMENT_METHOD_ZALO_PAY, PAYMENT_METHOD_PROMOTION}

var ISO_CODE_MAP_COUNTRY_CODE = map[string]string{
	ISO_CODE_TH:   COUNTRY_CODE_TH,
	ISO_CODE_VN:   COUNTRY_CODE_VN,
	ISO_CODE_INDO: COUNTRY_CODE_INDO,
}

var COUNTRY_CODE_MAP_ISO_CODE = map[string]string{
	COUNTRY_CODE_TH:   ISO_CODE_TH,
	COUNTRY_CODE_VN:   ISO_CODE_VN,
	COUNTRY_CODE_INDO: ISO_CODE_INDO,
}

var NORMAL_SERVICE_NAME_FROM_SUBSCRIPTION_SERVICE_NAME = map[string]string{
	SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION:   SERVICE_KEY_NAME_HOME_CLEANING,
	SERVICE_KEY_NAME_CHILD_CARE_SUBSCRIPTION:      SERVICE_KEY_NAME_CHILD_CARE,
	SERVICE_KEY_NAME_PATIENT_CARE_SUBSCRIPTION:    SERVICE_KEY_NAME_PATIENT_CARE,
	SERVICE_KEY_NAME_ELDERLY_CARE_SUBSCRIPTION:    SERVICE_KEY_NAME_ELDERLY_CARE,
	SERVICE_KEY_NAME_OFFICE_CLEANING_SUBSCRIPTION: SERVICE_KEY_NAME_OFFICE_CLEANING,
}

var GET_NORMAL_SERVICE_NAME = map[string]string{
	SERVICE_KEY_NAME_OFFICE_CLEANING_SUBSCRIPTION: SERVICE_KEY_NAME_OFFICE_CLEANING,
	SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION:   SERVICE_KEY_NAME_HOME_CLEANING,
	SERVICE_KEY_NAME_CHILD_CARE_SUBSCRIPTION:      SERVICE_KEY_NAME_CHILD_CARE,
	SERVICE_KEY_NAME_PATIENT_CARE_SUBSCRIPTION:    SERVICE_KEY_NAME_PATIENT_CARE,
	SERVICE_KEY_NAME_ELDERLY_CARE_SUBSCRIPTION:    SERVICE_KEY_NAME_ELDERLY_CARE,
	SERVICE_KEY_NAME_HOME_CLEANING:                SERVICE_KEY_NAME_HOME_CLEANING,
	SERVICE_KEY_NAME_AIR_CONDITIONER:              SERVICE_KEY_NAME_AIR_CONDITIONER,
	SERVICE_KEY_NAME_LAUNDRY:                      SERVICE_KEY_NAME_LAUNDRY,
	SERVICE_KEY_NAME_DEEP_CLEANING:                SERVICE_KEY_NAME_DEEP_CLEANING,
	SERVICE_KEY_NAME_HOME_COOKING:                 SERVICE_KEY_NAME_HOME_COOKING,
	SERVICE_KEY_NAME_HOUSEKEEPING:                 SERVICE_KEY_NAME_HOUSEKEEPING,
	SERVICE_KEY_NAME_GROCERY_ASSISTANT:            SERVICE_KEY_NAME_GROCERY_ASSISTANT,
	SERVICE_KEY_NAME_UPHOLSTERY:                   SERVICE_KEY_NAME_UPHOLSTERY,
	SERVICE_KEY_NAME_DISINFECTION:                 SERVICE_KEY_NAME_DISINFECTION,
	SERVICE_KEY_NAME_ELDERLY_CARE:                 SERVICE_KEY_NAME_ELDERLY_CARE,
	SERVICE_KEY_NAME_PATIENT_CARE:                 SERVICE_KEY_NAME_PATIENT_CARE,
	SERVICE_KEY_NAME_CAR_ADVERTISING:              SERVICE_KEY_NAME_CAR_ADVERTISING,
	SERVICE_KEY_NAME_CHILD_CARE:                   SERVICE_KEY_NAME_CHILD_CARE,
	SERVICE_KEY_NAME_WASHING_MACHINE:              SERVICE_KEY_NAME_WASHING_MACHINE,
	SERVICE_KEY_NAME_WATER_HEATER:                 SERVICE_KEY_NAME_WATER_HEATER,
	SERVICE_KEY_NAME_CARPET_CLEANING:              SERVICE_KEY_NAME_CARPET_CLEANING,
	SERVICE_KEY_NAME_HOME_MOVING:                  SERVICE_KEY_NAME_HOME_MOVING,
	SERVICE_KEY_NAME_INDUSTRIAL_CLEANING:          SERVICE_KEY_NAME_INDUSTRIAL_CLEANING,
}

// New Training Tasker
const (
	TRAINING_COURSE_STATUS_ACTIVE                    = "ACTIVE"
	TRAINING_COURSE_STATUS_INACTIVE                  = "INACTIVE"
	TRAINING_COURSE_TYPE_TEST                        = "TEST"
	TRAINING_COURSE_TYPE_REVIEW                      = "REVIEW"
	TRAINING_TEST_SUBMIT_PASSED                      = "PASSED"
	TRAINING_TEST_SUBMIT_FAILED                      = "FAILED"
	TRAINING_TEST_SUBMIT_COMPLETE                    = "COMPLETE"
	TRAINING_TEST_SUBMIT_PROCESSING                  = "PROCESSING"
	TRAINING_COURSE_DISPLAY_POSITION_HOME            = "HOME"
	TRAINING_COURSE_DISPLAY_POSITION_TRAINING_COURSE = "TRAINING_COURSE"
	TRAINING_REVIEW_SUBMIT_PASSED                    = "PASSED"
	TRAINING_REVIEW_SUBMIT_PROCESSING                = "PROCESSING"
)

// Home moving service
const (
	HOME_MOVING_SERVICE_FURNITURE_TYPE_NAME_ELECTRONIC = "electronic"
	HOME_MOVING_SERVICE_OPTION_NAME_STAIRS_TRANSPORT   = "stairsTransport"
)

// Tasker profile process status
const (
	TASKER_PROFILE_PROCESS_STATUS_REJECTED       = "REJECTED"
	TASKER_PROFILE_PROCESS_STATUS_ELIMINATED     = "ELIMINATED"
	TASKER_PROFILE_PROCESS_STATUS_FAIL_UPDATED   = "FAIL_UPDATED"
	TASKER_PROFILE_PROCESS_STATUS_FAIL_CALLING   = "FAIL_CALLING"
	TASKER_PROFILE_PROCESS_STATUS_FAIL_INTERVIEW = "FAIL_INTERVIEW"
)

// Special Campaign
const (
	// status Special Campaign
	SPECIAL_CAMPAIGN_STATUS_ACTIVE   = "ACTIVE"
	SPECIAL_CAMPAIGN_STATUS_INACTIVE = "INACTIVE"

	// type
	SPECIAL_CAMPAIGN_TYPE_REFERRAL_CAMPAIGN = "REFERRAL_CAMPAIGN"
	SPECIAL_CAMPAIGN_TYPE_TASK_CAMPAIGN     = "TASK_CAMPAIGN"

	// kind reward
	SPECIAL_CAMPAIGN_REWARD_BPOINT    = "BPOINT"
	SPECIAL_CAMPAIGN_REWARD_MONEY     = "MONEY"
	SPECIAL_CAMPAIGN_REWARD_PROMOTION = "PROMOTION"

	// status Special Campaign Transaction
	SPECIAL_CAMPAIGN_TRANSACTION_STATUS_INPROCESS = "IN-PROCESS"
	SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED = "COMPLETED"
	SPECIAL_CAMPAIGN_TRANSACTION_STATUS_REWARDED  = "REWARDED"
	SPECIAL_CAMPAIGN_TRANSACTION_STATUS_FAILED    = "FAILED"
	SPECIAL_CAMPAIGN_TRANSACTION_STATUS_EXPIRED   = "EXPIRED"
)

var MAP_SERVICE_EXPERT_IMAGE_BY_SERVICE_NAME = map[string]string{
	SERVICE_KEY_NAME_HOME_CLEANING:                "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/don_dep_nha.png",
	SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION:   "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/don_dep_nha.png",
	SERVICE_KEY_NAME_AIR_CONDITIONER:              "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/ve_sinh_may_lanh.png",
	SERVICE_KEY_NAME_LAUNDRY:                      "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/giat_ui.png",
	SERVICE_KEY_NAME_DEEP_CLEANING:                "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/tong_ve_sinh.png",
	SERVICE_KEY_NAME_HOME_COOKING:                 "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/nau_an.png",
	SERVICE_KEY_NAME_HOUSEKEEPING:                 "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/don_dep_buong_phong.png",
	SERVICE_KEY_NAME_GROCERY_ASSISTANT:            "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/di_cho.png",
	SERVICE_KEY_NAME_UPHOLSTERY:                   "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/ve_sinh_sofa.png",
	SERVICE_KEY_NAME_DISINFECTION:                 "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/khu_khuan.png",
	SERVICE_KEY_NAME_ELDERLY_CARE:                 "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/cham_soc_nguoi_gia.png",
	SERVICE_KEY_NAME_ELDERLY_CARE_SUBSCRIPTION:    "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/cham_soc_nguoi_gia.png",
	SERVICE_KEY_NAME_PATIENT_CARE:                 "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/cham_benh.png",
	SERVICE_KEY_NAME_PATIENT_CARE_SUBSCRIPTION:    "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/cham_benh.png",
	SERVICE_KEY_NAME_CHILD_CARE:                   "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/cham_tre.png",
	SERVICE_KEY_NAME_CHILD_CARE_SUBSCRIPTION:      "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/cham_tre.png",
	SERVICE_KEY_NAME_OFFICE_CLEANING:              "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/don_dep_van_phong.png",
	SERVICE_KEY_NAME_OFFICE_CLEANING_SUBSCRIPTION: "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/don_dep_van_phong.png",
	SERVICE_KEY_NAME_WASHING_MACHINE:              "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/ve_sinh_may_giat.png",
	SERVICE_KEY_NAME_WATER_HEATER:                 "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/ve_sinh_binh_nuoc_nong.png",
	SERVICE_KEY_NAME_CARPET_CLEANING:              "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/ve_sinh_tham_van_phong.png",
	SERVICE_KEY_NAME_HOME_MOVING:                  "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/dich_vu_chuyen_nha.png",
	SERVICE_KEY_NAME_MASSAGE:                      "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/massage.png",
	SERVICE_KEY_NAME_INDUSTRIAL_CLEANING:          "https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/campaign/lookback_2024/icon_services/ve_sinh_cong_nghiep.png",
}

var SERVICE_NOT_HAVE_TASK_DURATION = map[string]bool{
	SERVICE_KEY_NAME_LAUNDRY:             true,
	SERVICE_KEY_NAME_HOME_MOVING:         true,
	SERVICE_KEY_NAME_INDUSTRIAL_CLEANING: true,
	SERVICE_KEY_NAME_UPHOLSTERY:          true,
}

// firebase collection
var COLLECTION_FIREBASE_COMMUNITY_SETTING_KEYWORD = map[string]string{
	ISO_CODE_VN:   "vn_communitySettingKeyword",
	ISO_CODE_TH:   "th_communitySettingKeyword",
	ISO_CODE_INDO: "id_communitySettingKeyword",
	ISO_CODE_MY:   "my_communitySettingKeyword",
}

// firebase collection
var COLLECTION_FIREBASE_INCENTIVE = map[string]string{
	ISO_CODE_VN: "vn_incentive",
}

// firebase collection
var COLLECTION_FIREBASE_GIFT = map[string]string{
	ISO_CODE_VN: "vn_gift",
}
