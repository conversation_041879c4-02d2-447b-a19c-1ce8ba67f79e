// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcDoneTaskIndo

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	doneBookingRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/doneBookingRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// DoneTaskIndoClient is the client API for DoneTaskIndo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DoneTaskIndoClient interface {
	PartnerDone(ctx context.Context, in *doneBookingRequest.DoneBookingRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type doneTaskIndoClient struct {
	cc grpc.ClientConnInterface
}

func NewDoneTaskIndoClient(cc grpc.ClientConnInterface) DoneTaskIndoClient {
	return &doneTaskIndoClient{cc}
}

func (c *doneTaskIndoClient) PartnerDone(ctx context.Context, in *doneBookingRequest.DoneBookingRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcDoneTaskIndo.DoneTaskIndo/PartnerDone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DoneTaskIndoServer is the server API for DoneTaskIndo service.
// All implementations must embed UnimplementedDoneTaskIndoServer
// for forward compatibility
type DoneTaskIndoServer interface {
	PartnerDone(context.Context, *doneBookingRequest.DoneBookingRequest) (*empty.Empty, error)
	mustEmbedUnimplementedDoneTaskIndoServer()
}

// UnimplementedDoneTaskIndoServer must be embedded to have forward compatible implementations.
type UnimplementedDoneTaskIndoServer struct {
}

func (UnimplementedDoneTaskIndoServer) PartnerDone(context.Context, *doneBookingRequest.DoneBookingRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartnerDone not implemented")
}
func (UnimplementedDoneTaskIndoServer) mustEmbedUnimplementedDoneTaskIndoServer() {}

// UnsafeDoneTaskIndoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DoneTaskIndoServer will
// result in compilation errors.
type UnsafeDoneTaskIndoServer interface {
	mustEmbedUnimplementedDoneTaskIndoServer()
}

func RegisterDoneTaskIndoServer(s *grpc.Server, srv DoneTaskIndoServer) {
	s.RegisterService(&_DoneTaskIndo_serviceDesc, srv)
}

func _DoneTaskIndo_PartnerDone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(doneBookingRequest.DoneBookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoneTaskIndoServer).PartnerDone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDoneTaskIndo.DoneTaskIndo/PartnerDone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoneTaskIndoServer).PartnerDone(ctx, req.(*doneBookingRequest.DoneBookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _DoneTaskIndo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcDoneTaskIndo.DoneTaskIndo",
	HandlerType: (*DoneTaskIndoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PartnerDone",
			Handler:    _DoneTaskIndo_PartnerDone_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "doneTaskIndo.proto",
}
