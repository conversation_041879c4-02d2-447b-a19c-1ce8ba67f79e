package updateTrustPoint

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelAskerRating "gitlab.com/btaskee/go-services-model-v2/grpcmodel/askerRating"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

// Group list task by userId and recalculate numberPoint after each task
func groupByUserId(tasks []*modelTask.Task) map[string]*ModelUserTrustPointByTask {
	objUser := map[string]*ModelUserTrustPointByTask{}
	if len(tasks) > 0 {
		for _, task := range tasks {
			// check task.askerId exists in objUser
			if objUser[task.AskerId] == nil {
				objUser[task.AskerId] = &ModelUserTrustPointByTask{
					Task:        []*modelTask.Task{task},
					NumberPoint: calculateTrustPoint(task),
				}
			} else {
				// push array
				oldTrustPoint := objUser[task.AskerId].NumberPoint
				// update numberPoint
				objUser[task.AskerId].NumberPoint = oldTrustPoint + calculateTrustPoint(task)
				// push task to task in objUser
				objUser[task.AskerId].Task = append(objUser[task.AskerId].Task, task)
			}
		}
	}
	return objUser
}

func calculateTrustPoint(task *modelTask.Task) int32 {
	if task.Status == globalConstant.TASK_STATUS_DONE {
		var rating *modelAskerRating.AskerRating
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_ASKER_RATING[local.ISO_CODE], bson.M{"taskId": task.XId}, bson.M{"rate": 1}, &rating)
		if rating == nil || (rating != nil && rating.Rate == 5) {
			return 1
		}
		if rating != nil && rating.Rate == 4 {
			return -1
		}
		if rating != nil && rating.Rate < 4 {
			return -2
		}
		return 1
	}
	if task.Status == globalConstant.TASK_STATUS_CANCELED {
		return -2
	}
	return 0
}
