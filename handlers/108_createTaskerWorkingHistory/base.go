package createTaskerWorkingHistory

import (
	"fmt"
	"log"

	"github.com/robfig/cron/v3"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.mongodb.org/mongo-driver/bson"
)

var c *cron.Cron
var cfg = config.GetConfig()
var isRunning = false
var paramsEnough = true
var runAt string

func init() {
	var cronConfig map[string]interface{}
	if cfg != nil {
		cronConfig = cfg.ScheduleSyncCron["createtaskerworkinghistory"]
	}
	// Check if params enough
	if cronConfig["run_at"] == nil {
		paramsEnough = false
		lib.PostToSlackNotStart("createtaskerworkinghistory")
		return
	}

	// Get runAt string for sync cron
	runAt = cronConfig["run_at"].(string)

	// Check if config has is_run = true
	if cronConfig["is_run"] == nil || !cronConfig["is_run"].(bool) {
		lib.PostToSlackNotStart("CreateTaskerWorkingHistory")
		return
	}

	StartCron()
}

func StartCron() {
	c := cron.New(cron.WithLocation(local.TimeZone))
	c.AddFunc(runAt, createTaskerWorkingHistory)
	c.Start()
	isRunning = true
}

func Run(reqBody *model.RequestAction) map[string]interface{} {
	// action == 1 -> Run sync cron once now
	if reqBody.Action == lib.RUN_NOW {
		createTaskerWorkingHistory()
	}
	// action == 2 -> Start sync cron with schedule config
	if reqBody.Action == lib.START && paramsEnough && !isRunning {
		StartCron()
	}
	// action == 3 -> Stop running sync cron
	if reqBody.Action == lib.STOP && isRunning {
		c.Stop()
		c = nil
		isRunning = false
	}

	return nil
}

func createTaskerWorkingHistory() {
	log.Println("Start CreateTaskerWorkingHistory Process")
	defer log.Println("Finish CreateTaskerWorkingHistory Process")
	createTheNextTaskerWorkingHistory()
}

func createTheNextTaskerWorkingHistory() {
	now := globalLib.GetCurrentTime(local.TimeZone)
	name := fmt.Sprintf("%d%d", int(now.Month()), now.Year())
	newData := bson.M{
		"_id":       globalLib.GenerateObjectId(),
		"name":      name,
		"createdAt": now,
	}
	isExisting, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_TASKER_WORKING_HISTORY[local.ISO_CODE], bson.M{"name": name})
	if !isExisting {
		err := globalDataAccess.InsertOne(globalCollection.COLLECTION_TASKER_WORKING_HISTORY[local.ISO_CODE], newData)
		if err != nil {
			msg := fmt.Sprintf("[Gấp] Lỗi Sync cron Monthly Reward VN: Chưa tạo TaskerWorkingHistory cho tháng %s.Lỗi: %s", name, err.Error())
			log.Println(msg)
			globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
		}
	}
}
