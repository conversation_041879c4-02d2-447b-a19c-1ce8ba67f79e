package taskerJourney

import (
	"time"

	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelJourneySetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/journeySetting"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
)

var LAZY_BEE_LEVEL = &modelJourneySetting.JourneySettingLevel{
	Name: "LV0",
	Text: &modelService.ServiceText{
		Vi: "Ong Lười Biếng",
		En: "Lazy Bee",
		Ko: "Lazy Bee",
		Th: "ผึ้งขี้เกียจ",
		Id: "Lazy Bee",
	},
	Title: &modelService.ServiceText{
		Vi: "Cấp 0",
		En: "Level 0",
		Ko: "Level 0",
		Th: "ระดับ 0",
		Id: "Level 0",
	},
}

// Kiểm tra tasker đã pass điều kiện task chưa (<PERSON>ố lượng công việc đã làm)
func CheckConditionTask(tasker *modelUser.Users, condition *modelJourneySetting.JourneySettingLevelCondition) bool {
	if tasker == nil {
		return false
	}
	// Kiểm tra số lượng công việc đã làm vs target
	return ((tasker.GetTaskDone() >= int32(condition.Target)) && (tasker.GetAvgRating() >= condition.AvgRating))
}

// Kiểm tra tasker đã pass điều kiện duration in last month chưa (Số lượng giờ đã làm việc của tháng vừa rồi)
func CheckConditionDurationInMonth(tasker *modelUser.Users, condition *modelJourneySetting.JourneySettingLevelCondition, durationInMonth float64) bool {
	if tasker == nil {
		return false
	}
	// Kiểm tra số giờ làm trong tháng của tasker vs target
	return ((durationInMonth >= condition.Target) && (tasker.GetAvgRating() >= condition.AvgRating))
}

// Lấy thời gian tasker LevelUp
func GetLevelUpAt(tasker *modelUser.Users) time.Time {
	if tasker == nil {
		return time.Time{}
	}
	// Nếu không có levelUpAt thì lấy FirstVerifyAt nếu kg có FirstVerifyAt nữa thì lấy FirstDoneTaskAt
	levelUpAt_ts := tasker.CreatedAt
	if tasker.GetJourneyInfo() != nil && tasker.GetJourneyInfo().GetLevelUpAt() != nil {
		levelUpAt_ts = tasker.GetJourneyInfo().GetLevelUpAt()
	}
	return globalLib.ParseDateFromTimeStamp(levelUpAt_ts, globalLib.GetTimeZone())
}

func GetFirstVerifyAt(tasker *modelUser.Users) time.Time {
	if tasker == nil {
		return time.Time{}
	}
	firstVerifyAt := tasker.CreatedAt
	if tasker.GetFirstVerifyAt() != nil {
		firstVerifyAt = tasker.FirstVerifyAt
	}
	return globalLib.ParseDateFromTimeStamp(firstVerifyAt, globalLib.GetTimeZone())
}

func GetCurrentTaskerLevel(tasker *modelUser.Users, journey *modelJourneySetting.JourneySetting) (taskerLevelInfo *modelJourneySetting.JourneySettingLevel) {
	if tasker == nil {
		return
	}
	if journey == nil {
		return
	}
	// Lấy level journey hiện tại của tasker
	var taskerLevel string
	if tasker.GetJourneyInfo() != nil && tasker.GetJourneyInfo().GetLevel() != "" {
		taskerLevel = tasker.GetJourneyInfo().GetLevel()
	}
	if taskerLevel == "LV0" {
		taskerLevelInfo = LAZY_BEE_LEVEL
		return
	}
	journeyLevel := journey.Levels
	if len(journeyLevel) > 0 {
		taskerLevelInfo = journeyLevel[0]
	}
	for _, v := range journeyLevel {
		if taskerLevel == v.Name {
			taskerLevelInfo = v
			break
		}
	}
	return
}
