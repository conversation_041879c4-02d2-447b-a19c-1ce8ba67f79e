// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcBNPLMY

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	taskerBNPLRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerBNPLRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// BNPLClient is the client API for BNPL service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BNPLClient interface {
	ChargeTaskerBNPLFee(ctx context.Context, in *taskerBNPLRequest.TaskerBNPLRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type bNPLClient struct {
	cc grpc.ClientConnInterface
}

func NewBNPLClient(cc grpc.ClientConnInterface) BNPLClient {
	return &bNPLClient{cc}
}

func (c *bNPLClient) ChargeTaskerBNPLFee(ctx context.Context, in *taskerBNPLRequest.TaskerBNPLRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcBNPLMY.BNPL/ChargeTaskerBNPLFee", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BNPLServer is the server API for BNPL service.
// All implementations must embed UnimplementedBNPLServer
// for forward compatibility
type BNPLServer interface {
	ChargeTaskerBNPLFee(context.Context, *taskerBNPLRequest.TaskerBNPLRequest) (*empty.Empty, error)
	mustEmbedUnimplementedBNPLServer()
}

// UnimplementedBNPLServer must be embedded to have forward compatible implementations.
type UnimplementedBNPLServer struct {
}

func (UnimplementedBNPLServer) ChargeTaskerBNPLFee(context.Context, *taskerBNPLRequest.TaskerBNPLRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChargeTaskerBNPLFee not implemented")
}
func (UnimplementedBNPLServer) mustEmbedUnimplementedBNPLServer() {}

// UnsafeBNPLServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BNPLServer will
// result in compilation errors.
type UnsafeBNPLServer interface {
	mustEmbedUnimplementedBNPLServer()
}

func RegisterBNPLServer(s *grpc.Server, srv BNPLServer) {
	s.RegisterService(&_BNPL_serviceDesc, srv)
}

func _BNPL_ChargeTaskerBNPLFee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskerBNPLRequest.TaskerBNPLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BNPLServer).ChargeTaskerBNPLFee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBNPLMY.BNPL/ChargeTaskerBNPLFee",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BNPLServer).ChargeTaskerBNPLFee(ctx, req.(*taskerBNPLRequest.TaskerBNPLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _BNPL_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcBNPLMY.BNPL",
	HandlerType: (*BNPLServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ChargeTaskerBNPLFee",
			Handler:    _BNPL_ChargeTaskerBNPLFee_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "BNPLMY.proto",
}
