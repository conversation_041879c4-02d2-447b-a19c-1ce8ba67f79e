// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcCancelTaskVN

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	cancelBookingRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/cancelBookingRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// CancelTaskVNClient is the client API for CancelTaskVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CancelTaskVNClient interface {
	BackendCancelTask(ctx context.Context, in *cancelBookingRequest.CancelBookingRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type cancelTaskVNClient struct {
	cc grpc.ClientConnInterface
}

func NewCancelTaskVNClient(cc grpc.ClientConnInterface) CancelTaskVNClient {
	return &cancelTaskVNClient{cc}
}

func (c *cancelTaskVNClient) BackendCancelTask(ctx context.Context, in *cancelBookingRequest.CancelBookingRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcCancelTaskVN.CancelTaskVN/BackendCancelTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CancelTaskVNServer is the server API for CancelTaskVN service.
// All implementations must embed UnimplementedCancelTaskVNServer
// for forward compatibility
type CancelTaskVNServer interface {
	BackendCancelTask(context.Context, *cancelBookingRequest.CancelBookingRequest) (*empty.Empty, error)
	mustEmbedUnimplementedCancelTaskVNServer()
}

// UnimplementedCancelTaskVNServer must be embedded to have forward compatible implementations.
type UnimplementedCancelTaskVNServer struct {
}

func (UnimplementedCancelTaskVNServer) BackendCancelTask(context.Context, *cancelBookingRequest.CancelBookingRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BackendCancelTask not implemented")
}
func (UnimplementedCancelTaskVNServer) mustEmbedUnimplementedCancelTaskVNServer() {}

// UnsafeCancelTaskVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CancelTaskVNServer will
// result in compilation errors.
type UnsafeCancelTaskVNServer interface {
	mustEmbedUnimplementedCancelTaskVNServer()
}

func RegisterCancelTaskVNServer(s *grpc.Server, srv CancelTaskVNServer) {
	s.RegisterService(&_CancelTaskVN_serviceDesc, srv)
}

func _CancelTaskVN_BackendCancelTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cancelBookingRequest.CancelBookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CancelTaskVNServer).BackendCancelTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcCancelTaskVN.CancelTaskVN/BackendCancelTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CancelTaskVNServer).BackendCancelTask(ctx, req.(*cancelBookingRequest.CancelBookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _CancelTaskVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcCancelTaskVN.CancelTaskVN",
	HandlerType: (*CancelTaskVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BackendCancelTask",
			Handler:    _CancelTaskVN_BackendCancelTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cancel-task-vn.proto",
}
