// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcCSOutboundVN

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	csOutboundRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/csOutboundRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// CSOutboundVNClient is the client API for CSOutboundVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CSOutboundVNClient interface {
	SendZNSFirstDoneTask(ctx context.Context, in *csOutboundRequest.CSOutboundRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	SendZNSOTP(ctx context.Context, in *csOutboundRequest.CSOutboundRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	SendZNSOTPAppTasker(ctx context.Context, in *csOutboundRequest.CSOutboundRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	SendZNSReceived9THYearTicket(ctx context.Context, in *csOutboundRequest.CSOutboundRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type cSOutboundVNClient struct {
	cc grpc.ClientConnInterface
}

func NewCSOutboundVNClient(cc grpc.ClientConnInterface) CSOutboundVNClient {
	return &cSOutboundVNClient{cc}
}

func (c *cSOutboundVNClient) SendZNSFirstDoneTask(ctx context.Context, in *csOutboundRequest.CSOutboundRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcCSOutboundVN.CSOutboundVN/SendZNSFirstDoneTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cSOutboundVNClient) SendZNSOTP(ctx context.Context, in *csOutboundRequest.CSOutboundRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcCSOutboundVN.CSOutboundVN/SendZNSOTP", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cSOutboundVNClient) SendZNSOTPAppTasker(ctx context.Context, in *csOutboundRequest.CSOutboundRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcCSOutboundVN.CSOutboundVN/SendZNSOTPAppTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *cSOutboundVNClient) SendZNSReceived9THYearTicket(ctx context.Context, in *csOutboundRequest.CSOutboundRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcCSOutboundVN.CSOutboundVN/SendZNSReceived9THYearTicket", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CSOutboundVNServer is the server API for CSOutboundVN service.
// All implementations must embed UnimplementedCSOutboundVNServer
// for forward compatibility
type CSOutboundVNServer interface {
	SendZNSFirstDoneTask(context.Context, *csOutboundRequest.CSOutboundRequest) (*empty.Empty, error)
	SendZNSOTP(context.Context, *csOutboundRequest.CSOutboundRequest) (*empty.Empty, error)
	SendZNSOTPAppTasker(context.Context, *csOutboundRequest.CSOutboundRequest) (*empty.Empty, error)
	SendZNSReceived9THYearTicket(context.Context, *csOutboundRequest.CSOutboundRequest) (*empty.Empty, error)
	mustEmbedUnimplementedCSOutboundVNServer()
}

// UnimplementedCSOutboundVNServer must be embedded to have forward compatible implementations.
type UnimplementedCSOutboundVNServer struct {
}

func (UnimplementedCSOutboundVNServer) SendZNSFirstDoneTask(context.Context, *csOutboundRequest.CSOutboundRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendZNSFirstDoneTask not implemented")
}
func (UnimplementedCSOutboundVNServer) SendZNSOTP(context.Context, *csOutboundRequest.CSOutboundRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendZNSOTP not implemented")
}
func (UnimplementedCSOutboundVNServer) SendZNSOTPAppTasker(context.Context, *csOutboundRequest.CSOutboundRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendZNSOTPAppTasker not implemented")
}
func (UnimplementedCSOutboundVNServer) SendZNSReceived9THYearTicket(context.Context, *csOutboundRequest.CSOutboundRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendZNSReceived9THYearTicket not implemented")
}
func (UnimplementedCSOutboundVNServer) mustEmbedUnimplementedCSOutboundVNServer() {}

// UnsafeCSOutboundVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CSOutboundVNServer will
// result in compilation errors.
type UnsafeCSOutboundVNServer interface {
	mustEmbedUnimplementedCSOutboundVNServer()
}

func RegisterCSOutboundVNServer(s *grpc.Server, srv CSOutboundVNServer) {
	s.RegisterService(&_CSOutboundVN_serviceDesc, srv)
}

func _CSOutboundVN_SendZNSFirstDoneTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(csOutboundRequest.CSOutboundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CSOutboundVNServer).SendZNSFirstDoneTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcCSOutboundVN.CSOutboundVN/SendZNSFirstDoneTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CSOutboundVNServer).SendZNSFirstDoneTask(ctx, req.(*csOutboundRequest.CSOutboundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CSOutboundVN_SendZNSOTP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(csOutboundRequest.CSOutboundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CSOutboundVNServer).SendZNSOTP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcCSOutboundVN.CSOutboundVN/SendZNSOTP",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CSOutboundVNServer).SendZNSOTP(ctx, req.(*csOutboundRequest.CSOutboundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CSOutboundVN_SendZNSOTPAppTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(csOutboundRequest.CSOutboundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CSOutboundVNServer).SendZNSOTPAppTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcCSOutboundVN.CSOutboundVN/SendZNSOTPAppTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CSOutboundVNServer).SendZNSOTPAppTasker(ctx, req.(*csOutboundRequest.CSOutboundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _CSOutboundVN_SendZNSReceived9THYearTicket_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(csOutboundRequest.CSOutboundRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CSOutboundVNServer).SendZNSReceived9THYearTicket(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcCSOutboundVN.CSOutboundVN/SendZNSReceived9THYearTicket",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CSOutboundVNServer).SendZNSReceived9THYearTicket(ctx, req.(*csOutboundRequest.CSOutboundRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _CSOutboundVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcCSOutboundVN.CSOutboundVN",
	HandlerType: (*CSOutboundVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendZNSFirstDoneTask",
			Handler:    _CSOutboundVN_SendZNSFirstDoneTask_Handler,
		},
		{
			MethodName: "SendZNSOTP",
			Handler:    _CSOutboundVN_SendZNSOTP_Handler,
		},
		{
			MethodName: "SendZNSOTPAppTasker",
			Handler:    _CSOutboundVN_SendZNSOTPAppTasker_Handler,
		},
		{
			MethodName: "SendZNSReceived9THYearTicket",
			Handler:    _CSOutboundVN_SendZNSReceived9THYearTicket_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "csOutboundVN.proto",
}
