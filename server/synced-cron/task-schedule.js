import { Meteor } from 'meteor/meteor';
import { HTTP } from 'meteor/http';
import moment from 'moment';
import _ from 'lodash';

import accounting from 'accounting';

import { Setting, History } from '/server/lib/collections.js';
import {
  changeTimeZone,
  sendNotificationT<PERSON><PERSON><PERSON>,
  sendTaskToTasker,
  postToSlack,
  fillTaskDescription,
  isHostelService,
  isCookingService,
  sendNotificationByIds,
  trackingScheduleStatus,
} from '/server/helper-function.js';

const hourByMilSec = 60 * 60 * 1000;
var markedSendNotificationLowMoney = [];

const getPriceHostel = (task, service) => {
  const headers = {
    'content-type': 'application/json',
    'accessKey': Meteor.settings.GO_SERVICE.API_KEY,
  };
  const taskPriceCalculate = {
    date: moment(task.date).utc().format(),
    autoChooseTasker: Boolean(task.autoChooseTasker),
    taskPlace: task.taskPlace,
    homeType: task.homeType,
    detailHostel: task.detailHostel,
  };
  if (task.requirements && task.requirements.length > 0) {
    taskPriceCalculate.requirements = task.requirements.map((e) => { return { type: e.type } });
  }
  const priceCalculate = {
    service: {_id: service._id},
    task: taskPriceCalculate,
    isoCode: task.isoCode,
  };
  const url = Meteor.settings.GO_SERVICE.API_URL + '/v2/pricing/housekeeping';
  const result = HTTP.post(url, {headers: headers, data: priceCalculate});
  return {
    cost: _.get(result, 'data.cost', null),
    costDetail: _.get(result, 'data', null),
  }
};

const getPriceHostelV3 = (task, service) => {
  const headers = {
    'content-type': 'application/json',
    'accessKey': Meteor.settings.GO_SERVICE.API_KEY,
  };
  const taskPriceCalculate = {
    date: moment(task.date).utc().format(),
    autoChooseTasker: Boolean(task.autoChooseTasker),
    taskPlace: task.taskPlace,
    homeType: task.homeType,
    detailHousekeeping: task.detailHousekeeping,
  };
  if (task.requirements && task.requirements.length > 0) {
    taskPriceCalculate.requirements = task.requirements.map((e) => { return { type: e.type } });
  }
  const priceCalculate = {
    service: {_id: service._id},
    task: taskPriceCalculate,
    isoCode: task.isoCode,
  };
  const url = Meteor.settings.GO_SERVICE.API_URL + '/v3/pricing-vn/housekeeping-v2';
  const result = HTTP.post(url, {headers: headers, data: priceCalculate});
  return {
    cost: _.get(result, 'data.cost', null),
    costDetail: _.get(result, 'data', null),
  }
};

const getPriceHomeCooking = (task, service) => {
  const headers = {
    'content-type': 'application/json',
    'accessKey': Meteor.settings.GO_SERVICE.API_KEY,
  };
  const taskPriceCalculate = {
    autoChooseTasker: Boolean(task.autoChooseTasker),
    taskPlace: task.taskPlace,
    homeType: task.homeType,
    cookingDetail: task.cookingDetail,
  };
  if (task.requirements && task.requirements.length > 0) {
    taskPriceCalculate.requirements = task.requirements.map((e) => { return { type: e.type } });
  }
  const priceCalculate = {
    service: {_id: service._id},
    task: taskPriceCalculate,
    isoCode: task.isoCode,
  };
  const url = Meteor.settings.GO_SERVICE.API_URL + '/v2/pricing/home-cooking';
  const result = HTTP.post(url, {headers: headers, data: priceCalculate});
  return {
    cost: _.get(result, 'data.cost', null),
    costDetail: _.get(result, 'data', null),
  }
};

const getPriceHomeCleaning = (task, service) => {
  const headers = {
    'content-type': 'application/json',
    'accessKey': Meteor.settings.GO_SERVICE.API_KEY,
  };
  const taskPriceCalculate = {
    date: moment(task.date).utc().format(),
    duration: task.duration,
    autoChooseTasker: Boolean(task.autoChooseTasker),
    taskPlace: task.taskPlace,
    homeType: task.homeType,
  };
  if (task.requirements && task.requirements.length > 0) {
    taskPriceCalculate.requirements = task.requirements.map((e) => { return { type: e.type } });
  }
  if (task.isPremium) {
    taskPriceCalculate.isPremium = task.isPremium;
  }
  if (task.addons) {
    taskPriceCalculate.addons = task.addons;
  }
  const priceCalculate = {
    service: {_id: service._id},
    task: taskPriceCalculate,
    isoCode: task.isoCode,
  };
  const url = Meteor.settings.GO_SERVICE.API_URL + '/v2/pricing/home-cleaning';
  const result = HTTP.post(url, {headers: headers, data: priceCalculate});
  return {
    cost: _.get(result, 'data.cost', null),
    costDetail: _.get(result, 'data', null),
  }
};

const getPriceElderlyCare = (task, service) => {
  const headers = {
    'content-type': 'application/json',
    'accessKey': Meteor.settings.GO_SERVICE.API_KEY,
  };
  const taskPriceCalculate = {
    date: moment(task.date).utc().format(),
    duration: task.duration,
    autoChooseTasker: Boolean(task.autoChooseTasker),
    taskPlace: task.taskPlace,
    homeType: task.homeType,
    askerId: task.askerId,
  };
  if (task.requirements && task.requirements.length > 0) {
    taskPriceCalculate.requirements = task.requirements.map((e) => { return { type: e.type } });
  }
  const priceCalculate = {
    service: {_id: service._id},
    task: taskPriceCalculate,
    isoCode: task.isoCode,
  };
  const url = Meteor.settings.GO_SERVICE.API_URL + '/v2/pricing/elderly-care';
  const result = HTTP.post(url, {headers: headers, data: priceCalculate});
  return {
    cost: _.get(result, 'data.cost', null),
    costDetail: _.get(result, 'data', null),
  }
};

const getPricePatientCare = (task, service) => {
  const headers = {
    'content-type': 'application/json',
    'accessKey': Meteor.settings.GO_SERVICE.API_KEY,
  };
  const taskPriceCalculate = {
    date: moment(task.date).utc().format(),
    duration: task.duration,
    autoChooseTasker: Boolean(task.autoChooseTasker),
    taskPlace: task.taskPlace,
    homeType: task.homeType,
    askerId: task.askerId,
  };
  if (task.requirements && task.requirements.length > 0) {
    taskPriceCalculate.requirements = task.requirements.map((e) => { return { type: e.type } });
  }
  const priceCalculate = {
    service: {_id: service._id},
    task: taskPriceCalculate,
    isoCode: task.isoCode,
  };
  const url = Meteor.settings.GO_SERVICE.API_URL + '/v2/pricing/patient-care';
  const result = HTTP.post(url, {headers: headers, data: priceCalculate});
  return {
    cost: _.get(result, 'data.cost', null),
    costDetail: _.get(result, 'data', null),
  }
};

const getPriceChildCare = (task, service) => {
  const headers = {
    'content-type': 'application/json',
    'accessKey': Meteor.settings.GO_SERVICE.API_KEY,
  };
  const taskPriceCalculate = {
    date: moment(task.date).utc().format(),
    duration: task.duration,
    autoChooseTasker: Boolean(task.autoChooseTasker),
    taskPlace: task.taskPlace,
    homeType: task.homeType,
    askerId: task.askerId,
    detailChildCare: task.detailChildCare,
  };
  if (task.requirements && task.requirements.length > 0) {
    taskPriceCalculate.requirements = task.requirements.map((e) => { return { type: e.type } });
  }
  const priceCalculate = {
    service: {_id: service._id},
    task: taskPriceCalculate,
    isoCode: task.isoCode,
  };
  const url = Meteor.settings.GO_SERVICE.API_URL + '/v2/pricing/child-care';
  const result = HTTP.post(url, {headers: headers, data: priceCalculate});
  return {
    cost: _.get(result, 'data.cost', null),
    costDetail: _.get(result, 'data', null),
  }
};

const findNextSchedule = (schedule, task) => {
  if (!task) { return []; }
  // history
  let history = schedule.history;
  if (!history || history.length === 0) {
    
    // not have history yet, create one.
    history = [{
      taskId: task._id,
      date: schedule.beginAt,
    }];
  }

  // set proper recurring date
  let nextTaskDate = history[history.length - 1].date;
  if (schedule.scheduleTime) { // set hour-minute
    nextTaskDate.setHours(schedule.scheduleTime.getHours());
    nextTaskDate.setMinutes(schedule.scheduleTime.getMinutes());
    nextTaskDate.setSeconds(0);
    nextTaskDate.setMilliseconds(0);
  }
  nextTaskDate = moment(nextTaskDate).add(1, 'days').toDate();
  if (nextTaskDate < new Date()) {
    nextTaskDate.setFullYear(new Date().getFullYear(), new Date().getMonth(), new Date().getDate());
  }
  if (nextTaskDate < new Date()) { // set tommorrow if still in the past
    nextTaskDate = moment(nextTaskDate).add(1, 'days').toDate();
  }
  const setting = Setting.findOne({});
  if (!setting || !setting.scheduleRange) {
    throw new Meteor.Error('AutoGenerateTaskSchedule', 'scheduleRange is not defined');
  }
  const scheduleRange = setting.scheduleRange;

  const result = [];
  const rangeStart = new Date();
  const rangeEnd = new Date(rangeStart.getTime() + (scheduleRange * hourByMilSec));
  
  // Temporary code: Check new task date greater than reopen service date
  const reopenDate = new Date(1586970000000); // 16/4/2020 00:00

  while (rangeStart < nextTaskDate && nextTaskDate <= rangeEnd) {
    const nextTaskDateGMT7 = changeTimeZone(nextTaskDate);
    if (schedule.weeklyRepeater.indexOf(nextTaskDateGMT7.getDay()) >= 0 && nextTaskDate >= reopenDate) {
      if (_.findIndex(history, h => (moment(nextTaskDateGMT7).format('DD/MM/YYYY') === moment(changeTimeZone(h.date)).format('DD/MM/YYYY'))) === -1) {
        result.push(new Date(nextTaskDate));
      }
    }
    nextTaskDate = moment(nextTaskDate).add(1, 'days').toDate();
  }
  return result;
};

// Function create a task by schedule
const createTaskBySchedule = (input) => {
  const {
    user,
    task,
    service,
    schedule,
    newTaskDate
  } = input;
  var collectionTask = Meteor.appCollection.VN_Task;
  var collectionTaskSchedule = Meteor.appCollection.VN_TaskSchedule;
  if (task.isoCode === 'TH') {
    collectionTask = Meteor.appCollection.TH_Task;
    collectionTaskSchedule = Meteor.appCollection.TH_TaskSchedule;
  }
  if (task.payment && ['PROMOTION', 'BANK_TRANSFER'].indexOf(task.payment.method) >= 0 ) {
    // Fix old schedule task with PROMOTION, BANK_TRANSFER payment method.
    task.payment.method = 'CASH';
  } else if (task.payment && task.payment.method === 'CREDIT') {
    // check credit account
    if (user && user.fAccountId) {
      var main = 0;
      if (task.isoCode === 'VN') {
        const fAccount = Meteor.appCollection.VN_FinancialAccount.findOne({_id: user.fAccountId});
        main = fAccount.FMainAccount;
      } else {
        const fAccount = Meteor.appCollection.TH_FinancialAccount.findOne({_id: user.fAccountId});
        main = fAccount.TH_FMainAccount;
      }
      const creditMoneyUsed = collectionTask.find({
        askerId: task.askerId,
        'payment.method': 'CREDIT',
        status: {
          $in: ['POSTED', 'WAITING_ASKER_CONFIRMATION', 'CONFIRMED']
        }
      }, {
        fields: {
          viewedTaskers: false
        }
      }).map(res => (res.cost)).reduce((a, b) => (a + b), 0);
      const remainCredit = main - creditMoneyUsed;
      if (remainCredit < task.cost) {
        task.payment = { method: 'CASH' };
      }
    }
  } else if (task.payment && task.payment.method === 'CARD') {
    // Check the old card is exists.
    var existsCard = null;
    if (task.isoCode === 'VN') {
      existsCard = Meteor.appCollection.VN_PaymentCard.findOne({ _id: task.payment.cardId, disabled: {$ne: true} });
    } else {
      existsCard = Meteor.appCollection.TH_PaymentCard.findOne({ _id: task.payment.cardId, disabled: {$ne: true} });
    }
    // If card was deleted or disabled. Change payment method to cash
    // Don't need notify to Asker
    if (!existsCard) {
      task.payment = { method: 'CASH' };
    }
    // If previous task is paid by CARD, but the charging was failed. Change to cash in schedule payment
    if ( schedule.payment && schedule.payment.method === 'CASH' ) {
      task.payment = { method: 'CASH' };
    }
  } else {
    // Payment not exists before: set default payment method is CASH
    task.payment = { method: 'CASH' };
    delete task.isPrepayTask;
  }
  if ( task.payment && task.payment.isPayOff ) {
    delete task.payment.isPayOff;
  }
  if ( !schedule.payment && task.payment ) {
    // Update schedule payment method (Exclude CARD method)
    collectionTaskSchedule.update({ _id: schedule._id }, { $set: { payment: task.payment } });
  }
  if ( task.isoCode === 'TH' ) {
    task.originCurrency = { sign: '฿', code: 'THB' };
  } else {
    task.originCurrency = { sign: '₫', code: 'VND' };
  }
  // get serviceid by schedule
  task.serviceId = schedule.serviceId;
  
  task.date = newTaskDate;
  task.createdAt = new Date();
  task.duration = schedule.scheduleDuration ? schedule.scheduleDuration : task.duration;
  if (!task.subServiceId) {
    let cost;
    let costDetail;
    if (isHostelService(service.name)) {
      // Nếu có field 'detailHousekeeping' thì gọi qua Api v3, nếu không vẫn gọi Api tính giá cũ
      if (schedule.detailHousekeeping) {
        task.detailHousekeeping = schedule.detailHousekeeping;
        const costData = getPriceHostelV3(task, service);
        cost = costData.cost;
        costDetail = costData.costDetail;
        task.duration = _.get(costData, 'costDetail.duration', task.duration);
      } else {
        task.detailHostel = _.get(schedule, 'taskDetail.detail', task.detailHostel);
        const costData = getPriceHostel(task, service);
        cost = costData.cost;
        costDetail = costData.costDetail;
        task.duration = _.get(costData, 'costDetail.duration', task.duration);
      }
    } else if (isCookingService(service.name)) {
      task.cookingDetail.eatingTime = moment(task.date).add(task.duration, 'hours').toDate();
      const costData = getPriceHomeCooking(task, service);
      cost = costData.cost;
      costDetail = costData.costDetail;
    } else if (service.name === "ELDERLY_CARE") {
      task.autoChooseTasker = true;
      const costData = getPriceElderlyCare(task, service);
      cost = costData.cost;
      costDetail = costData.costDetail;
    } else if (service.name === "PATIENT_CARE") {
      task.autoChooseTasker = true;
      const costData = getPricePatientCare(task, service);
      cost = costData.cost;
      costDetail = costData.costDetail;
    } else if (service.name === "CHILD_CARE") {
      const costData = getPriceChildCare(task, service);
      cost = costData.cost;
      costDetail = costData.costDetail;
    } else {
      const costData = getPriceHomeCleaning(task, service);
      cost = costData.cost;
      costDetail = costData.costDetail;
    }
    if (cost === 0) {
      throw new Meteor.Error('task.generateSchedule', 'Find Cost Error');
    }
    if (costDetail.increaseReasons && costDetail.increaseReasons.length === 0) {
      delete costDetail.increaseReasons;
    }
    if (costDetail.decreasedReasons && costDetail.decreasedReasons.length === 0) {
      delete costDetail.decreasedReasons;
    }
    if (costDetail.date === null) {
      delete costDetail.date;
    }
    if (costDetail.reason === null) {
      delete costDetail.reason;
    }
    task.cost = cost;
    if (costDetail.date) {
      costDetail.date = new Date(costDetail.date);
    }
    task.pricing = {
      costDetail,
    };
    task.costDetail = costDetail;
  }
  task.status = 'POSTED';
  task.rated = false;
  task.scheduleId = schedule._id;
  if (!task.taskNote) {
    if (user && user.taskNote && user.taskNote !== '') {
      task.taskNote = user.taskNote;
    }
  }

  // Update task black list
  if ( user && user.blackList && user.blackList.length ) {
    task.blackList = user.blackList;
  }
  if (!task.serviceName) {
    task.serviceName = schedule.serviceName;
  }
  const newTaskId = collectionTask.insert(task);
  Meteor.appCollection.users.update({ _id: task.askerId }, { $set: { lastPostedTask: task.createdAt } });
  collectionTaskSchedule.update({ _id: schedule._id }, {
    $set: { taskId: newTaskId },
    $push: { history: { taskId: newTaskId, date: newTaskDate } }
  });

  // Gửi thông báo cho Asker
  sendNotificationToAsker(newTaskId, task.askerId, user.language, service.text, task.isoCode);

  const isHaveTasker = forceTaskerAccept(newTaskId, task, schedule);
  // Check for sending notifications to Taskers if not have force Taskers
  if ( !isHaveTasker ) {
    // Gửi task cho Tasker (đã có gửi thông báo cho Tasker)
    if (!user.isBlacklist) {
      sendTaskToTasker(newTaskId, task.serviceId, user.favouriteTasker, task.isoCode)
    }

    // Send notification type 34 to asker if service is home cooking and no fixed Tasker
    if (isCookingService(service.name)) {
      sendNotificationByIds([task.askerId], {
        title: {
          vi: 'Bạn muốn ăn gì nào?',
          en: 'What do you want to eat?',
          ko: '어떤 걸 드시고 싶으신가요?',
          th: 'What do you want to eat?',
        },
        text: {
          vi: 'Nhập món ăn ngay để Cộng Tác Viên có thể đáp ứng cho bạn',
          en: 'Please fill in your dishes',
          ko: '요리 도우미님이 답을 하실 수 있도록 원하시는 음식을 지금 입력하세요',
          th: 'Please fill in your dishes',
        },
        payload: {taskId: newTaskId},
      }, { type: 34, isoCode: task.isoCode, isForceView: true });
    }
  }
  History.insert({
    action: 'Generate task By Schedule',
    createdAt: new Date(),
    data: {
      task,
      schedule,
    }
  });
};

const runTHTaskSchedule = () => {
  History.insert({
    action: 'start synced cron task schedule TH',
    createdAt: new Date(),
    data: {}
  });
  var message = null;
  try {
    const thServices = Meteor.appCollection.TH_Service.find({ 'name': { $in: ["CLEANING", "HOUSE_KEEPING", "HOME_COOKING"] }}, { fields: { name: 1, text: 1 }}).fetch();
    Meteor.appCollection.TH_TaskSchedule.find({ status: 'ACTIVE', serviceName: {$ne: "OFFICE_CLEANING"} }).forEach((schedule) => {
      if (schedule.weeklyRepeater && schedule.weeklyRepeater.length > 0) {
        const taskFields = {
          _id: 1,
          serviceId: 1,
          subServiceId: 1,
          duration: 1,
          date: 1,
          address: 1,
          lat: 1,
          lng: 1,
          taskPlace: 1,
          askerId: 1,
          phone: 1,
          contactName: 1,
          autoChooseTasker: 1,
          isSendToFavTaskers: 1,
          cost: 1,
          currency: 1,
          payment: 1,
          taskNote: 1,
          description: 1,
          status: 1,
          requirements: 1,
          pet: 1,
          serviceText: 1,
          homeType: 1,
          detailHostel: 1,
          cookingDetail: 1,
          isApplyCardTransactionFee: 1,
          isoCode: 1,
          detailChildCare: 1,
          serviceName: 1,
          isPremium: 1,
          addons: 1,
          detailHousekeeping: 1,
        };
        let task = Meteor.appCollection.TH_Task.findOne(schedule.taskId, {fields: taskFields});
        if ( !task ) {
          task = Meteor.appCollection.TH_HistoryTasks.findOne(schedule.taskId, {fields: taskFields});
        }
        if (!task) {
          postToSlack({
            channel: 'cron-jobs-alert',
            text: `Task Schedule TH: task not found. Schedule Id: ${schedule._id}`,
          });
          return;
        }
        if (!task.isoCode) {
          postToSlack({
            channel: 'cron-jobs-alert',
            text: `Task Schedule TH: task isoCode not found. Schedule Id: ${schedule._id}`,
          });
          return;
        }
        
        // Fill task description
        if ( task && (!task.description || task.description === '') ) {
          // Find description from old tasks
          const description = fillTaskDescription(task.phone, task.isoCode);
          if ( description ) {
            task.description = description;
          }
        }

        let service = thServices.find((s) => (s._id === schedule.serviceId));
        if ( !service ) {
          postToSlack({
            channel: 'cron-jobs-alert',
            text: `Task Schedule TH: Service not found. Schedule Id: ${schedule._id}`,
          });
          return;
        }

        if ( !task.serviceText ) {
          if ( service ) {
            task.serviceText = service.text;
          }
        }
        try {
          const newTaskDates = findNextSchedule(schedule, task);
          const user = Meteor.appCollection.users.findOne({_id: task.askerId});
          newTaskDates.forEach((newTaskDate, index) => {
            // Delay 2s post 1 task
            if (index !== 0) { Meteor.wrapAsync(cb => { Meteor.setTimeout(() => { cb(); }, 2000); })(); }
            delete task._id;
            try {
              createTaskBySchedule({
                user,
                task,
                service,
                schedule,
                newTaskDate,
              });
              // Reset lại việc lưu mảng Tasker đã gửi thông báo tài khoản không đủ tiền sau mỗi lần chạy
              markedSendNotificationLowMoney = [];
            } catch (exCreateTask) {
              postToSlack({
                channel: 'cron-jobs-alert',
                text: `Task Schedule TH: Can not finish post task for schedule ${schedule._id} at ${newTaskDate.toString()} ${exCreateTask}`,
              });
            }
          });
        } catch(ex) {
          postToSlack({
            channel: 'cron-jobs-alert',
            text: `Task Schedule TH: Can not finish post task for schedule ${schedule._id} - ${ex}`,
          });
        }
      }
    });
  } catch (e) {
    // Temporary track the synced cron errors
    message = e;
  }
  // Tracking
  trackingScheduleStatus({name: 'TaskSchedule', nextRunAt: SyncedCron.nextScheduledAtDate('GenerateTaskSchedule'), message});
};

const runVNTaskSchedule = () => {
  History.insert({
    action: 'start synced cron task schedule VN',
    createdAt: new Date(),
    data: {}
  });
  var message = null;
  try {
    const services = Meteor.appCollection.Service.find({ 'name': { $in: ["CLEANING", "HOUSE_KEEPING", "HOME_COOKING", "ELDERLY_CARE", "PATIENT_CARE", "CHILD_CARE"] }}, { fields: { name: 1, text: 1 }}).fetch();
    Meteor.appCollection.VN_TaskSchedule.find({ status: 'ACTIVE', serviceName: {$ne: "OFFICE_CLEANING"} }).forEach((schedule) => {
      if (schedule.weeklyRepeater && schedule.weeklyRepeater.length > 0) {
        const taskFields = {
          _id: 1,
          serviceId: 1,
          subServiceId: 1,
          duration: 1,
          date: 1,
          address: 1,
          lat: 1,
          lng: 1,
          taskPlace: 1,
          askerId: 1,
          phone: 1,
          contactName: 1,
          autoChooseTasker: 1,
          isSendToFavTaskers: 1,
          cost: 1,
          currency: 1,
          payment: 1,
          taskNote: 1,
          description: 1,
          status: 1,
          requirements: 1,
          pet: 1,
          serviceText: 1,
          homeType: 1,
          detailHostel: 1,
          cookingDetail: 1,
          isApplyCardTransactionFee: 1,
          isoCode: 1,
          detailChildCare: 1,
          serviceName: 1,
          isPremium: 1,
          addons: 1,
        };
        let task = Meteor.appCollection.VN_Task.findOne(schedule.taskId, {fields: taskFields});
        if ( !task ) {
          task = Meteor.appCollection.VN_HistoryTasks.findOne(schedule.taskId, {fields: taskFields});
        }
        if (!task) {
          postToSlack({
            channel: 'cron-jobs-alert',
            text: `Task Schedule VN: task not found. Schedule Id: ${schedule._id}`,
          });
          return;
        }
        if (!task.isoCode) {
          postToSlack({
            channel: 'cron-jobs-alert',
            text: `Task Schedule VN: task isoCode not found. Schedule Id: ${schedule._id}`,
          });
          return;
        }
        
        // Fill task description
        if ( task && (!task.description || task.description === '') ) {
          // Find description from old tasks
          const description = fillTaskDescription(task.phone, task.isoCode);
          if ( description ) {
            task.description = description;
          }
        }

        let service = services.find((s) => (s._id === schedule.serviceId));
        if ( !service ) {
          postToSlack({
            channel: 'cron-jobs-alert',
            text: `Task Schedule VN: Service not found. Schedule Id: ${schedule._id}`,
          });
          return;
        }

        if ( !task.serviceText ) {
          if ( service ) {
            task.serviceText = service.text;
          }
        }
        try {
          const newTaskDates = findNextSchedule(schedule, task);
          const user = Meteor.appCollection.users.findOne({_id: task.askerId});
          newTaskDates.forEach((newTaskDate, index) => {
            // Delay 2s post 1 task
            if (index !== 0) { Meteor.wrapAsync(cb => { Meteor.setTimeout(() => { cb(); }, 2000); })(); }
            delete task._id;
            try {
              createTaskBySchedule({
                user,
                task,
                service,
                schedule,
                newTaskDate,
              });
              // Reset lại việc lưu mảng Tasker đã gửi thông báo tài khoản không đủ tiền sau mỗi lần chạy
              markedSendNotificationLowMoney = [];
            } catch (exCreateTask) {
              postToSlack({
                channel: 'cron-jobs-alert',
                text: `Task Schedule VN: Can not finish post task for schedule ${schedule._id} at ${newTaskDate.toString()} ${exCreateTask}`,
              });
            }
          });
        } catch(ex) {
          postToSlack({
            channel: 'cron-jobs-alert',
            text: `Task Schedule VN: Can not finish post task for schedule ${schedule._id} - ${ex}`,
          });
        }
      }
    });
  } catch (e) {
    // Temporary track the synced cron errors
    message = e;
  }
  // Tracking
  trackingScheduleStatus({name: 'TaskSchedule', nextRunAt: SyncedCron.nextScheduledAtDate('GenerateTaskSchedule'), message});
};

const isConflictTask = (task, otherTask, timeInBetweenTask) => {
  const taskStart_1 = new Date(task.date.getTime() - (timeInBetweenTask * 60 * 1000));
  const taskEnd_1 = new Date(task.date.getTime() + (task.duration * 60 + timeInBetweenTask) * 60 * 1000);
  const taskStart_2 = new Date(otherTask.date);
  const taskEnd_2 = new Date(taskStart_2.getTime() + otherTask.duration * 60 * 60 * 1000);

  if ((taskStart_1 >= taskStart_2 && taskStart_1 < taskEnd_2)
    || (taskStart_2 >= taskStart_1 && taskStart_2 < taskEnd_1)) {
    return true;
  }
  return false;
};

const isTaskerCanAcceptTask = (newTaskId, tasker, task) => {
  // Tasker not active and not locked: inactive, unverified, ...
  if (tasker.status !== 'ACTIVE' && tasker.status !== 'LOCKED' ) {
    return false;
  }
  // Tasker locked with not come reason >= 3 times
  if ( tasker.status === 'LOCKED' && tasker.notComeLockNumber > 2 ) {
    return false;
  }
  // Tasker is locked with other reason
  if ( tasker.status === 'LOCKED' && !tasker.notComeLockNumber ) {
    return false;
  }
  if (task.isPremium && !tasker.isPremiumTasker) {
    return false;
  }
  if ( tasker.status !== 'ACTIVE' ) {
    var channel = "vn-sub-schedule";
    var textAlert = `CS lưu ý: Tasker ${tasker.phone} status ${tasker.status} đã được thêm vào schedule task (Asker: ${task.phone})`;
    if (task.isoCode === "TH") {
      channel = "th-sub-schedule";
      textAlert = `SCHEDULE: Tasker ${tasker.phone} with status ${tasker.status} has been added to schedule. Asker: ${task.phone}`;
    }
    postToSlack({
      channel: channel,
      text: textAlert,
    });
  }
  

	//TODO : fix this, use Task rate instead of hardcode
  const calculateFinancialTaskFee = (costOfTask) => {
    return Math.ceil((costOfTask * 0.20) / 100.0) * 100;
  };
  
  var collectionTask = Meteor.appCollection.VN_Task;
  if (task.isoCode === 'TH') {
    collectionTask = Meteor.appCollection.TH_Task;
  }

  // CHECK TASKER MONEY
  const allAcceptedTaskCost = collectionTask.find({ 'acceptedTasker.taskerId': tasker._id, status: { $in: ['CONFIRMED', 'WAITING_ASKER_CONFIRMATION'] } }).map(res => (calculateFinancialTaskFee(res.cost))).reduce(((a, b) => (a + b)), calculateFinancialTaskFee(task.cost));
  
  var main = 0;
  var promotion = 0;
  if (task.isoCode === 'VN') {
    const FA = Meteor.appCollection.VN_FinancialAccount.findOne({ _id: tasker.fAccountId });
    main = FA.FMainAccount;
    promotion = FA.Promotion;
  } else {
    const FA = Meteor.appCollection.TH_FinancialAccount.findOne({ _id: tasker.fAccountId });
    main = FA.TH_FMainAccount;
    promotion = FA.TH_Promotion;
  }
  const taskerMoney = main + promotion;
  if (taskerMoney < allAcceptedTaskCost) {
    // Gửi thông báo tài khoản không đủ tiền cho Tasker
    // Đối với schedule có nhiều task lên cùng lúc cho 1 Tasker, chỉ gửi thông báo 1 lần
    if (markedSendNotificationLowMoney.indexOf(tasker._id) < 0) {
      var formatMoney = accounting.formatNumber(taskerMoney);
      var currency = task.isoCode === 'TH' ? "THB" : "VND";
      const dataNotify = {
        title: {
          vi: i18n.getTranslation('NOTIFICATION_MESSAGE_TASKER_TOPUP', { _locale: 'vi', money: formatMoney, currency }),
          th: i18n.getTranslation('NOTIFICATION_MESSAGE_TASKER_TOPUP', { _locale: 'th', money: formatMoney, currency }),
          ko: i18n.getTranslation('NOTIFICATION_MESSAGE_TASKER_TOPUP', { _locale: 'ko', money: formatMoney, currency }),
          en: i18n.getTranslation('NOTIFICATION_MESSAGE_TASKER_TOPUP', { _locale: 'en', money: formatMoney, currency }),
        },
        text: {
          vi: i18n.getTranslation('NOTIFICATION_MESSAGE_TASKER_TOPUP', { _locale: 'vi', money: formatMoney, currency }),
          th: i18n.getTranslation('NOTIFICATION_MESSAGE_TASKER_TOPUP', { _locale: 'th', money: formatMoney, currency }),
          ko: i18n.getTranslation('NOTIFICATION_MESSAGE_TASKER_TOPUP', { _locale: 'ko', money: formatMoney, currency }),
          en: i18n.getTranslation('NOTIFICATION_MESSAGE_TASKER_TOPUP', { _locale: 'en', money: formatMoney, currency }),
        },
        payload: {
          type: 2,
          taskId: newTaskId,
          navigateTo: "Topup",
        }
      };
      sendNotificationByIds([tasker._id], dataNotify, {isoCode: task.isoCode, isForceView: true});
      markedSendNotificationLowMoney.push(tasker._id);
    }
    return false; // Tasker not enough money

    // REOPEN for task overload
    // postToSlack({
    //   channel: 'btaskee-system',
    //   text: `CS lưu ý: Tasker ${tasker.phone} - ${tasker.name} được thêm vào công việc Schedule. Số tiền còn lại của Tasker: ${accounting.formatNumber(taskerMoney - allAcceptedTaskCost)}. `,
    // });

  }
  // CHECK TASK WORKING TIME CONFLICT
  var settings;
  if (task.isoCode === "TH") {
    settings = Meteor.appCollection.TH_SettingSystem.findOne({}, { fields: { timeInBetweenTask: 1 } });
  } else {
    settings = Meteor.appCollection.SettingSystem.findOne({}, { fields: { timeInBetweenTask: 1 } });
  }
  const timeInBetweenTask = settings && settings.timeInBetweenTask ? settings.timeInBetweenTask : 30;
  let isConflict = false;
  collectionTask.find({ 'acceptedTasker.taskerId': tasker._id, status: { $in: ['CONFIRMED'] } }).forEach((otherTask) => {
    if (isConflictTask(task, otherTask, timeInBetweenTask)) {
      isConflict = true;
      return false;
    }
  });
  if (isConflict) {
    return false;
  }
  return true;
};

// Force Tasker accept task if schedule has forceAcceptTaskerId
const forceTaskerAccept = function( newTaskId, task, schedule ) {
  if (Setting.findOne({}).forceAcceptSchedule && schedule.forceAcceptTaskerId ) {
    var collectionTask = Meteor.appCollection.VN_Task;
    if (task.isoCode === 'TH') {
      collectionTask = Meteor.appCollection.TH_Task;
    }
    const tasker = Meteor.appCollection.users.findOne({ _id: schedule.forceAcceptTaskerId });
    if ( isTaskerCanAcceptTask(newTaskId, tasker, task) ) {
      // Force tasker accept this task
      collectionTask.update({ _id: newTaskId }, { $set: {
        acceptedTasker: [{
          taskerId: tasker._id,
          avatar: tasker.avatar,
          name: tasker.name,
          avgRating: tasker.avgRating || 0,
          taskDone: tasker.taskDone || 0,
          acceptedAt: new Date()
        }],
        status: 'CONFIRMED',
        updatedAt: new Date(),
      } });

      // remove this tasker from other task waiting
      collectionTask.find({
        _id: { $ne: newTaskId },
        'acceptedTasker.taskerId': tasker._id,
        status: 'WAITING_ASKER_CONFIRMATION',
      }).forEach((taskItem) => {
        const settings = Meteor.appCollection.SettingSystem.findOne({}, { fields: { timeInBetweenTask: 1 } });
        const timeInBetweenTask = settings && settings.timeInBetweenTask ? settings.timeInBetweenTask : 30;
        if (isConflictTask(task, taskItem, timeInBetweenTask)) {
          collectionTask.update({ _id: taskItem._id }, {
            $pull: { acceptedTasker: { taskerId: tasker._id } },
            $set: { status: taskItem.acceptedTasker.length === 1 ? 'POSTED' : 'WAITING_ASKER_CONFIRMATION' },
          });
        }
      });
      const data = {
        title: {
          vi: i18n.getTranslation('NOTIFICATION_MESSAGE_ASKER_CONFIRM', { _locale: 'vi' }),
          th: i18n.getTranslation('NOTIFICATION_MESSAGE_ASKER_CONFIRM', { _locale: 'th' }),
          ko: i18n.getTranslation('NOTIFICATION_MESSAGE_ASKER_CONFIRM', { _locale: 'ko' }),
          en: i18n.getTranslation('NOTIFICATION_MESSAGE_ASKER_CONFIRM', { _locale: 'en' }),
        },
        text: {
          vi: i18n.getTranslation('NOTIFICATION_MESSAGE_ASKER_CONFIRM', { _locale: 'vi' }),
          th: i18n.getTranslation('NOTIFICATION_MESSAGE_ASKER_CONFIRM', { _locale: 'th' }),
          ko: i18n.getTranslation('NOTIFICATION_MESSAGE_ASKER_CONFIRM', { _locale: 'ko' }),
          en: i18n.getTranslation('NOTIFICATION_MESSAGE_ASKER_CONFIRM', { _locale: 'en' }),
        },
        payload: {
          type: 2,
          newTaskId,
          navigateTo: "TaskDetail",
        },
      };
      sendNotificationByIds([tasker._id], data, {isoCode: task.isoCode});
      return true;
    } else {
      // Post error to slack of task can not confirm
      var channel = "vn-sub-schedule";
      var text = `Cần CS xử lý: Công việc schedule có Tasker bắt buộc không CONFIRM được. Kiểm tra status, công việc trùng giờ và tài chính của Tasker ${tasker.phone} (Asker: ${task.phone})`;
      if (task.isPremium && !tasker.isPremiumTasker) {
        text = `Cần CS xử lý: Công việc schedule Premium có Tasker bắt buộc không phải là Tasker Premium. Tasker: ${tasker.phone}, Asker: ${task.phone}`;
      }
      if (task.isoCode === "TH") {
        channel = "th-sub-schedule";
        text = `SCHEDULE: The task in schedule cannot Confirmed. Check status, conflict task and the financial account of Tasker. Tasker: ${tasker.phone}, Asker: ${task.phone}`;
      }
      postToSlack({
        channel: channel,
        text: text,
      });
      return true;
    }
  }
  return false;
};

  Meteor.methods({
    generateTaskBySchedule(every) {
	    console.log("Call generate Task schedule....");
      if (!every) {
        throw new Meteor.Error('AutoGenerateTaskSchedule', 'every is not defined');
      }
      SyncedCron.add({
        name: 'GenerateTaskSchedule',
        schedule(parser) {
          // Run at 0' every x hour
          return parser.recur().every(every).hour();
        },
        job() {
          runVNTaskSchedule();
          runTHTaskSchedule();
        },
      });
	    console.log("Finish calling to generate Task schedule.");
    },
    generateTaskSchedule() {
	    console.log("Run generate Task schedule....");
      runVNTaskSchedule();
      runTHTaskSchedule();
	    console.log("Done generate Task schedule.");
    },
    scheduleTaskSchedule() {
      const setting = Setting.findOne({});
      Meteor.call('generateTaskBySchedule', setting.runEvery);
    }
});
