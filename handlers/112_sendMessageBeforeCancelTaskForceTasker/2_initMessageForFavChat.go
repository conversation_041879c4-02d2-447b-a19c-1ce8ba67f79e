package sendMessageBeforeCancelTaskForceTasker

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func initMessageForChatBeforeCancelTask(tasks []*modelTask.Task) {
	for _, task := range tasks {
		query := bson.M{
			"members":     bson.M{"$size": 2}, // Check the array size is 2
			"members._id": bson.M{"$all": []string{task.AskerId, task.ForceTasker.TaskerId}},
		}
		fields := bson.M{"_id": 1}
		chatMessage, err := pkgChatMessage.GetChatConversation(task.IsoCode, query, fields)
		if err != nil && err != mongo.ErrNoDocuments {
			msg := fmt.Sprintf("[go-sync-cron-vn-v3] Get Chat conversation failed: taskId %s. Error: %s", task.XId, err.Error())
			globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
			return
		}
		if chatMessage == nil {
			return
		}
		chatId := chatMessage.XId
		var asker *modelUser.Users
		err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USERS, bson.M{"_id": task.AskerId}, bson.M{"_id": 1, "name": 1, "type": 1, "avatar": 1, "language": 1}, &asker)
		if err != nil {
			msg := fmt.Sprintf("[go-sync-cron-vn-v3] Can not get user: userId %s. Error: %s", task.AskerId, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
			return
		}
		message := &modelChatMessage.ChatMessageMessages{
			XId:       globalLib.GenerateObjectId(),
			From:      globalConstant.CHAT_MESSAGE_FROM_SYSTEM,
			ChatId:    chatId,
			CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
			TaskRequestData: &modelChatMessage.ChatMessageMessagesTaskRequestData{
				TaskInfo: &modelChatMessage.ChatMessageMessagesTaskRequestDataInfo{
					TaskId:      task.XId,
					District:    task.TaskPlace.District,
					Duration:    task.Duration,
					ServiceText: task.ServiceText,
				},
			},
			MessageBySystem: &modelChatMessage.ChatMessageMessagesMessageBySystem{
				Title:  localization.GetLocalizeObject("MESSAGE_BEFORE_CANCEL_TASK_FORCE_TASKER_TITLE"),
				Text:   localization.GetLocalizeObject("MESSAGE_BEFORE_CANCEL_TASK_FORCE_TASKER_BODY"),
				SendTo: globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_ASKER,
				Key:    globalConstant.KEY_BOOK_WITH_FAV,
				Actions: []*modelChatMessage.ChatMessageMessagesMessageBySystemActions{
					{
						Type:  globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_TYPE_SECONDARY,
						Title: localization.GetLocalizeObject("BUTTON_CANCEL_TASK"),
						Key:   globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_CANCEL_TASK,
					}, {
						Type:  globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_TYPE_PRIMARY,
						Title: localization.GetLocalizeObject("BUTTON_SEND_TO_OTHER_TASKER"),
						Key:   globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_SEND_TO_OTHER_TASKER,
					},
				},
			},
		}
		if len(task.DateOptions) > 0 {
			for _, option := range task.DateOptions {
				dateOption := &modelChatMessage.TaskDateOptions{
					XId:  option.XId,
					Date: option.Date,
				}
				message.TaskRequestData.TaskInfo.DateOptions = append(message.TaskRequestData.TaskInfo.DateOptions, dateOption)
			}
		} else {
			dateOption := &modelChatMessage.TaskDateOptions{
				Date: task.Date,
			}
			message.TaskRequestData.TaskInfo.DateOptions = append(message.TaskRequestData.TaskInfo.DateOptions, dateOption)
		}
		// Insert new message
		err = pkgChatMessage.SendMessageToConversation(local.ISO_CODE, chatId, []*modelChatMessage.ChatMessageMessages{message})
		if err != nil {
			msg := fmt.Sprintf("[go-sync-cron-vn-v3] Send Chat Message to conversation failed: taskId %s. Error: %s", task.XId, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
			return
		}

		go lib.SendChatMessageViaSocket(nil, chatId, []string{asker.XId}, message)

		//// Send notification to received user
		askerLanguage := globalConstant.LANG_EN
		if asker.Language != "" {
			askerLanguage = asker.Language
		}
		var arrayNotification []interface{}
		notify := &modelNotification.Notification{
			XId:         globalLib.GenerateObjectId(),
			ChatId:      chatId,
			UserId:      asker.XId,
			Type:        28,
			Description: localization.T(askerLanguage, "MESSAGE_BEFORE_CANCEL_TASK_FORCE_TASKER_TITLE"),
			NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_CHAT,
			CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
		}
		arrayNotification = append(arrayNotification, notify)
		title := localization.GetLocalizeObject("MESSAGE_BEFORE_CANCEL_TASK_FORCE_TASKER_TITLE")
		body := localization.GetLocalizeObject("MESSAGE_BEFORE_CANCEL_TASK_FORCE_TASKER_BODY")
		payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
			Type:       28,
			ChatId:     chatId,
			NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_CHAT,
		}
		userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{{UserId: asker.XId, Language: askerLanguage}}
		lib.SendNotification(arrayNotification, userIds, title, body, payload, true)

		_, err = globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_TASK[globalConstant.ISO_CODE_VN],
			bson.M{"_id": task.XId},
			bson.M{"$set": bson.M{"forceTasker.remindMessageCreatedAt": globalLib.GetCurrentTimestamp(local.TimeZone)}},
		)
		if err != nil {
			msg := fmt.Sprintf("[go-sync-cron-vn-v3] Can not update task: taskId %s. Error: %s", task.XId, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
			return
		}
	}
}
