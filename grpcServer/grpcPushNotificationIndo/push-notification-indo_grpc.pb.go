// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcPushNotificationIndo

import (
	context "context"
	pushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PushNotificationIndoClient is the client API for PushNotificationIndo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PushNotificationIndoClient interface {
	Send(ctx context.Context, in *pushNotificationRequest.PushNotificationRequest, opts ...grpc.CallOption) (*response.Response, error)
}

type pushNotificationIndoClient struct {
	cc grpc.ClientConnInterface
}

func NewPushNotificationIndoClient(cc grpc.ClientConnInterface) PushNotificationIndoClient {
	return &pushNotificationIndoClient{cc}
}

func (c *pushNotificationIndoClient) Send(ctx context.Context, in *pushNotificationRequest.PushNotificationRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPushNotificationIndo.PushNotificationIndo/Send", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PushNotificationIndoServer is the server API for PushNotificationIndo service.
// All implementations must embed UnimplementedPushNotificationIndoServer
// for forward compatibility
type PushNotificationIndoServer interface {
	Send(context.Context, *pushNotificationRequest.PushNotificationRequest) (*response.Response, error)
	mustEmbedUnimplementedPushNotificationIndoServer()
}

// UnimplementedPushNotificationIndoServer must be embedded to have forward compatible implementations.
type UnimplementedPushNotificationIndoServer struct {
}

func (UnimplementedPushNotificationIndoServer) Send(context.Context, *pushNotificationRequest.PushNotificationRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Send not implemented")
}
func (UnimplementedPushNotificationIndoServer) mustEmbedUnimplementedPushNotificationIndoServer() {}

// UnsafePushNotificationIndoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PushNotificationIndoServer will
// result in compilation errors.
type UnsafePushNotificationIndoServer interface {
	mustEmbedUnimplementedPushNotificationIndoServer()
}

func RegisterPushNotificationIndoServer(s *grpc.Server, srv PushNotificationIndoServer) {
	s.RegisterService(&_PushNotificationIndo_serviceDesc, srv)
}

func _PushNotificationIndo_Send_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationRequest.PushNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationIndoServer).Send(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPushNotificationIndo.PushNotificationIndo/Send",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationIndoServer).Send(ctx, req.(*pushNotificationRequest.PushNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PushNotificationIndo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcPushNotificationIndo.PushNotificationIndo",
	HandlerType: (*PushNotificationIndoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Send",
			Handler:    _PushNotificationIndo_Send_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "push-notification-indo.proto",
}
