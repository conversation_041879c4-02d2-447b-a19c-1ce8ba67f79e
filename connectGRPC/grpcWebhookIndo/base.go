package grpcWebhookIndo

import (
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcWebhookIndo"
	"go.elastic.co/apm/module/apmgrpc"
	"google.golang.org/grpc"
)

func ConnectGRPCWebhook(endpoint string) (grpcWebhookIndo.WebhookClient, *grpc.ClientConn, error) {
	var connect *grpc.ClientConn
	connect, err := grpc.Dial(endpoint, grpc.WithInsecure(), grpc.WithUnaryInterceptor(apmgrpc.NewUnaryClientInterceptor()))
	if err != nil {
		return nil, nil, err
	}
	client := grpcWebhookIndo.NewWebhookClient(connect)
	return client, connect, err
}
