package subscription

import (
	"fmt"
	"log"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

// SubscriptionSettings represents subscription settings
type SubscriptionSettings struct {
	PostTaskBefore int `bson:"postTaskBefore" json:"postTaskBefore"`
	RenewBefore    int `bson:"renewBefore" json:"renewBefore"`
	ExpiredDays    int `bson:"expiredDays" json:"expiredDays"`
}

// runVNSubscription processes VN subscriptions
func runVNSubscription() {
	log.Println("Start runVNSubscription...")
	defer log.Println("Finished runVNSubscription")

	var messageStatus error

	// Get subscription settings
	var subsSettings SubscriptionSettings
	err := globalDataAccess.GetOneByQuery(
		globalCollection.COLLECTION_SUBSCRIPTION_SETTINGS[local.ISO_CODE],
		bson.M{},
		bson.M{"postTaskBefore": 1, "renewBefore": 1, "expiredDays": 1},
		&subsSettings,
	)
	if err != nil {
		log.Printf("Error getting subscription settings: %v", err)
		subsSettings = SubscriptionSettings{
			PostTaskBefore: 2,
			RenewBefore:    8,
			ExpiredDays:    2,
		}
	}

	postTaskBefore := subsSettings.PostTaskBefore
	if postTaskBefore == 0 {
		postTaskBefore = 2
	}

	// Get all active VN subscriptions
	var subscriptions []*SubscriptionData
	query := bson.M{
		"status":      bson.M{"$in": []string{"NEW", "ACTIVE"}},
		"serviceName": bson.M{"$ne": "OFFICE_CLEANING"},
	}

	err = globalDataAccess.GetAllByQuery(
		globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE],
		query,
		bson.M{},
		&subscriptions,
	)
	if err != nil {
		log.Printf("Error getting subscriptions: %v", err)
		return
	}

	// Process each subscription
	for _, subs := range subscriptions {
		err := processSubscription(subs, subsSettings)
		if err != nil {
			messageStatus = err
			// Post error to Slack
			globalLib.PostToSlack(
				cfg.SlackToken,
				globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE],
				globalConstant.SLACK_USER_NAME,
				fmt.Sprintf("Workflow: There is an error on subscription: subscriptionId: %s error: %v", subs.ID, err),
			)
		}
	}

	// Track status
	trackingScheduleStatus("Subscription", messageStatus)
}

// processSubscription processes a single subscription
func processSubscription(subs *SubscriptionData, settings SubscriptionSettings) error {
	if subs.Status == "NEW" {
		// Handle unpaid subscriptions
		return handleNewSubscription(subs, settings)
	} else {
		// Handle active subscriptions
		return handleActiveSubscription(subs, settings)
	}
}

// handleNewSubscription handles subscriptions that haven't been paid yet
func handleNewSubscription(subs *SubscriptionData, settings SubscriptionSettings) error {
	expiredDays := settings.ExpiredDays
	if expiredDays == 0 {
		expiredDays = 2
	}

	// Check if subscription should be expired using utility function
	if isSubscriptionExpired(subs, expiredDays) {
		log.Printf("Expiring subscription %s - past due date", subs.ID)

		// Mark subscription as expired
		update := bson.M{"$set": bson.M{
			"status":    "EXPIRED",
			"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
		}}
		_, err := globalDataAccess.UpdateOneById(
			globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE],
			subs.ID,
			update,
		)
		if err != nil {
			return fmt.Errorf("failed to expire subscription: %v", err)
		}

		// Mark purchase order as expired
		purchaseOrderUpdate := bson.M{"$set": bson.M{
			"status":    "EXPIRED",
			"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
		}}
		globalDataAccess.UpdateOneByQuery(
			globalCollection.COLLECTION_PURCHASE_ORDER[local.ISO_CODE],
			bson.M{"subscriptionId": subs.ID},
			purchaseOrderUpdate,
		)

		log.Printf("Subscription %s and related purchase order marked as EXPIRED", subs.ID)
	}

	return nil
}

// handleActiveSubscription handles active subscriptions
func handleActiveSubscription(subs *SubscriptionData, settings SubscriptionSettings) error {
	// Validate subscription data first
	if err := validateSubscriptionData(subs); err != nil {
		return fmt.Errorf("invalid subscription data: %v", err)
	}

	currentDate := time.Now()
	var taskPricePosted float64
	tasksPosted := 0

	// Process each scheduled date
	for _, nextDate := range subs.Schedule {
		// Check if we should post this task
		postDate := nextDate.AddDate(0, 0, -settings.PostTaskBefore)

		// If the post date hasn't arrived yet, skip
		if postDate.After(currentDate) {
			continue
		}

		// Check if this date was already processed
		isAlreadyProcessed := false
		for _, historyDate := range subs.History {
			if isSameDate(historyDate, nextDate) {
				isAlreadyProcessed = true
				break
			}
		}

		// Skip if task date is in the past
		if nextDate.Before(currentDate) {
			continue
		}

		// Check if task was already posted for this date
		if isTaskAlreadyPosted(subs.ID, nextDate) {
			continue
		}

		// If not processed and the task date is in the future, post the task
		if !isAlreadyProcessed {
			// Add small delay to prevent overwhelming the system
			if tasksPosted > 0 {
				time.Sleep(1 * time.Second)
			}

			// Post subscription task
			taskPrice := postSubscriptionTask(subs, nextDate)
			if taskPrice > 0 {
				taskPricePosted += taskPrice
				tasksPosted++
				log.Printf("Posted subscription task for date %s, price: %.2f", nextDate.Format("2006-01-02 15:04"), taskPrice)
			}
		}
	}

	// Update cost remaining if needed
	if subs.Price > 0 && taskPricePosted > 0 {
		update := bson.M{
			"$inc": bson.M{"costRemaining": -taskPricePosted},
			"$set": bson.M{"updatedAt": globalLib.GetCurrentTime(local.TimeZone)},
		}
		globalDataAccess.UpdateOneById(
			globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE],
			subs.ID,
			update,
		)
		log.Printf("Updated subscription %s cost remaining by %.2f", subs.ID, -taskPricePosted)
	}

	// Handle renewal notification
	return handleRenewalNotification(subs, settings)
}

// handleRenewalNotification sends renewal notification if needed
func handleRenewalNotification(subs *SubscriptionData, settings SubscriptionSettings) error {
	renewBefore := settings.RenewBefore
	if renewBefore == 0 {
		renewBefore = 8
	}

	// Use utility function to check if renewal notification should be sent
	if shouldSendRenewalNotification(subs, renewBefore) {
		lastScheduleDate := subs.Schedule[len(subs.Schedule)-1]

		// Send renewal notification
		err := sendRenewalNotification(subs.UserID, subs.ID, lastScheduleDate)
		if err != nil {
			return fmt.Errorf("failed to send renewal notification: %v", err)
		}

		// Mark as sent
		update := bson.M{"$set": bson.M{
			"isSentRenewEmail": true,
			"updatedAt":        globalLib.GetCurrentTime(local.TimeZone),
		}}
		globalDataAccess.UpdateOneById(
			globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE],
			subs.ID,
			update,
		)

		log.Printf("Renewal notification sent for subscription %s", subs.ID)
	}

	return nil
}

// sendRenewalNotification sends renewal notification to user
func sendRenewalNotification(userID, subsID string, endDate time.Time) error {
	// Create notification data
	title := localization.GetLocalizeObject("SUBSCRIPTION_RENEW_TITLE", "")
	text := localization.GetLocalizeObject("SUBSCRIPTION_RENEW_CONTENT", endDate.Format("02/01/2006"))

	// Send notification (simplified version)
	log.Printf("Sending renewal notification to user %s for subscription %s. Title: %s, Text: %s",
		userID, subsID, title, text)

	// In real implementation, you would use the notification service
	// sendNotificationByIds([userID], data, {})
	// sendEmailSubscription(userID, subsID)

	return nil
}

// trackingScheduleStatus tracks the schedule status
func trackingScheduleStatus(name string, messageStatus error) {
	// In real implementation, this would track the cron job status
	if messageStatus != nil {
		log.Printf("Schedule %s completed with error: %v", name, messageStatus)
		// Post error to Slack
		globalLib.PostToSlack(
			cfg.SlackToken,
			globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE],
			globalConstant.SLACK_USER_NAME,
			fmt.Sprintf("Subscription cron job failed: %v", messageStatus),
		)
	} else {
		log.Printf("Schedule %s completed successfully", name)
	}
}
