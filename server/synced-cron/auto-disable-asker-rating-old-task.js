import { Meteor } from 'meteor/meteor';
import {
  revertTimeZone,
  trackingScheduleStatus,
} from '/server/helper-function';
import moment from 'moment';


const runDiscardRatingOldTask = () => {
  var message = null;
  try {
    Meteor.appCollection.Task.update({
      date: {
        $gte: moment().subtract(14, 'days').toDate(), // Tasks from last 14 days
        $lt: moment().subtract(7, 'days').toDate(), // Tasks were older than 7 days
      },
      status: 'DONE',
      rated: false,
    }, {
      $set: {
        rated: 'NOT_RATE'
      }
    }, { multi: true });
  } catch(ex) {
    message = ex;
  }
  // Tracking
  trackingScheduleStatus({name: 'AutoDisableAskerRating', nextRunAt: SyncedCron.nextScheduledAtDate('autoDisableAskerRating'), message});
};

export default function () {
  Meteor.methods({
    // Auto Done Task
    autoDisableAskerRating() {
      SyncedCron.add({
        name: 'autoDisableAskerRating',
        schedule(parser) {
          // Run at 04:30 GMT+7 every day
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 4)).getHours();
          return parser.cron(`0 30 ${hourInTimeZoneServer} 1/1 * ? *`, true);
        },
        job() {
          runDiscardRatingOldTask();
        },
      });
    },
    'runNow.autoDisableAskerRating'() {
      runDiscardRatingOldTask();
    },
  });
}
