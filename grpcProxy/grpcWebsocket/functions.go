package grpcWebsocket

import (
	"encoding/json"

	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/mapData"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
)

type MessageModel struct {
	Data      map[string]interface{} `json:"data,omitempty"`
	MessageTo []string               `json:"messageTo,omitempty"`
}

func refactorTaskItem(source string, messageTo []string, task *task.Task) []*MessageModel {
	// Case source == "newTask"
	if source == globalConstant.WEBSOCKET_TASK_SOURCE_NEW_TASK {
		return []*MessageModel{
			{
				Data:      mapData.MapDataTaskItem(task, "", []string{}),
				MessageTo: messageTo,
			},
		}
	}

	if source == globalConstant.WEBSOCKET_TASK_SOURCE_CONFIRMED_TASK && task.Status == globalConstant.TASK_STATUS_POSTED {
		return []*MessageModel{
			{
				Data:      mapData.MapDataTaskItem(task, "", []string{}),
				MessageTo: messageTo,
			},
		}
	}

	// Case source == "confirmedTask"
	if source == globalConstant.WEBSOCKET_TASK_SOURCE_CONFIRMED_TASK {
		// Case not deepCleaning service
		if !globalLib.IsDeepCleaningServiceByKeyName(task.ServiceName) {
			return []*MessageModel{
				{
					Data:      mapData.MapDataTaskItem(task, "", []string{}),
					MessageTo: messageTo,
				},
			}
		}

		// Case deep cleaning service
		result := []*MessageModel{}
		var normalData map[string]interface{}
		leaderId := ""
		for _, v := range task.AcceptedTasker {
			// Case leader
			if v.IsLeader {
				leaderId = v.TaskerId
				result = append(result, &MessageModel{
					Data:      mapData.MapDataTaskItem(task, v.TaskerId, []string{}),
					MessageTo: []string{v.TaskerId},
				})
				continue
			}

			// Case not leader and not have normal tasker data. Can not append at this step because maybe have leader after this
			if normalData == nil {
				normalData = mapData.MapDataTaskItem(task, "", []string{})
			}
		}

		// return to client
		if normalData != nil {
			result = append(result, &MessageModel{
				Data:      normalData,
				MessageTo: globalLib.RemoveTaskersFromList(messageTo, []string{leaderId}),
			})
		}
		return result
	}

	if source == "" {
		taskMap := make(map[string]interface{})
		taskData, _ := json.Marshal(task)
		json.Unmarshal(taskData, &taskMap)
		return []*MessageModel{
			{
				Data:      taskMap,
				MessageTo: messageTo,
			},
		}
	}
	return nil
}
