package testing

import (
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func ResetData() {
	// Reset task
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{})

	// Reset users
	modelUser.DeleteAllByQuery(local.ISO_CODE, bson.M{})

	// Business
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER_TRANSACTION[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BUSINESS_TRANSACTION[local.ISO_CODE], bson.M{})
}

// ===================== USER

func CreateUser(data []map[string]interface{}) {
	for k, v := range data {
		userId := v["phone"]
		language := "vi"
		status := globalConstant.USER_STATUS_ACTIVE
		user := map[string]interface{}{
			"username":         v["phone"],
			"type":             v["type"],
			"status":           status,
			"isoCode":          local.ISO_CODE,
			"phone":            v["phone"],
			"name":             v["name"],
			"address":          "104 Mai Thi Luu.",
			"nDoneTaskInMonth": 0,
			"language":         language,
			"referralCode":     fmt.Sprintf("%s%d", v["type"].(string), k),
		}
		if v["workingPlaces"] != nil {
			user["workingPlaces"] = v["workingPlaces"]
		}
		if v["blackList"] != nil {
			user["blackList"] = v["blackList"]
		}
		if v["company"] != nil {
			user["company"] = v["company"]
		}
		if v["emails"] != nil {
			user["emails"] = v["emails"]
		}
		if v["avatar"] != nil {
			user["avatar"] = v["avatar"]
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], bson.M{"_id": userId, "FMainAccount": 0, "Promotion": 0})
		user["fAccountId"] = userId
		user["_id"] = userId
		modelUser.InsertOne(local.ISO_CODE, user)
	}
}

// ======================= TASK

func CreateTask(data []map[string]interface{}) []string {
	var taskIds []string
	for _, v := range data {
		var service *modelService.Service
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"text.en": v["serviceName"]}, bson.M{"_id": 1, "text": 1}, &service)
		existAsker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["askerPhone"]}, bson.M{"_id": 1, "name": 1})
		taskDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		if v["date"] != nil {
			date := strings.Split(v["date"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			taskDate = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		duration := 2
		cost := 200000
		address := "69 D1, Tân Hưng, quận 7, TPHCM"
		status := globalConstant.TASK_STATUS_POSTED
		if v["status"] != nil {
			status = v["status"].(string)
		}
		rated := false
		homeType := globalConstant.HOME_TYPE_HOME
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		autoChooseTasker := true
		paymentMethod := globalConstant.PAYMENT_METHOD_CASH
		if v["paymentMethod"] != nil {
			paymentMethod = v["paymentMethod"].(string)
		}
		taskItem := map[string]interface{}{
			"serviceId":   service.XId,
			"serviceText": service.Text,
			"description": v["description"],
			"date":        taskDate,
			"duration":    duration,
			"cost":        cost,
			"address":     address,
			"lat":         10.741432,
			"lng":         106.702033,
			"phone":       v["askerPhone"],
			"contactName": existAsker.Name,
			"createdAt":   createdAt,
			"askerId":     existAsker.XId,
			"status":      status,
			"rated":       rated,
			"visibility":  1,
			"payment": map[string]interface{}{
				"method": paymentMethod,
			},
			"homeType":         homeType,
			"autoChooseTasker": autoChooseTasker,
			"isoCode":          local.ISO_CODE,
		}
		if v["acceptedTasker"] != nil {
			taskItem["acceptedTasker"] = v["acceptedTasker"]
		}
		if v["costDetail"] != nil {
			taskItem["costDetail"] = v["costDetail"]
		}
		if v["pricing"] != nil {
			taskItem["pricing"] = v["pricing"]
		}
		if v["detailDeepCleaning"] != nil {
			taskItem["detailDeepCleaning"] = v["detailDeepCleaning"]
		}
		if v["isoCode"] != nil {
			taskItem["isoCode"] = v["isoCode"]
		}
		if v["originCurrency"] != nil {
			taskItem["originCurrency"] = v["originCurrency"]
		}
		if v["newCostDetail"] != nil {
			taskItem["newCostDetail"] = v["newCostDetail"]
		}
		taskId := globalLib.GenerateObjectId()
		taskItem["_id"] = taskId
		globalDataAccess.InsertOne(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskItem)
		taskIds = append(taskIds, taskId)
	}
	return taskIds
}

func UpdateSettingSystem(data interface{}) {
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, data)
}

func UpdateService(query, data interface{}) {
	globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], query, data)
}

func CreateBusiness(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], insertData)
}

func CreateBusinessLevel(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], insertData)
}

func CreateBusinessMember(data []map[string]interface{}) {
	userMember := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		userMember = append(userMember, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], userMember)
}

func CreateBusinessMemberTransactions(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_BUSINESS_MEMBER_TRANSACTION[local.ISO_CODE], insertData)
}

func CreateBusinessTransactions(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_BUSINESS_TRANSACTION[local.ISO_CODE], insertData)
}
