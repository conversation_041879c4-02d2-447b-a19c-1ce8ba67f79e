package globalLib

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"math/rand"
	"net"
	"net/http"
	"os"
	"regexp"
	"runtime/debug"
	"sort"
	"strconv"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/gorilla/websocket"
	libNow "github.com/jinzhu/now"
	"github.com/leekchan/accounting"
	"github.com/slack-go/slack"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcCSOutboundVN"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcSMSVN"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccessV2 "gitlab.com/btaskee/go-services-model-v2/globalDataAccessV2"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/common"
	modelCSOutboundRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/csOutboundRequest"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/financialAccountIndo"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/financialAccountMY"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/financialAccountTH"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/financialAccountVN"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPaymentCard "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentCard"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSettingCountry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
	modelSMSSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/smsSending"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var Logger, _ = zap.NewProduction()

type FavChatNotiRequest struct {
	UserLanguage string
	UserId       string
	ChatId       string
	IsoCode      string
	TimeZone     *time.Location
}
type FavChatNotiResponse struct {
	ArrayNotification []interface{}
	UserIds           []*modelPushNotificationRequest.PushNotificationRequestUserIds
	Title             *modelService.ServiceText
	Body              *modelService.ServiceText
	Payload           *modelPushNotificationRequest.PushNotificationRequestPayload
}

func GetTimeZone() *time.Location {
	tz := os.Getenv("TZ")
	if tz == "" {
		tz = "7"
	}
	numTz, err := strconv.Atoi(tz)
	if err != nil {
		numTz = 7
	}
	if numTz >= 0 {
		return time.FixedZone(fmt.Sprintf("UTC+%s", tz), numTz*60*60)
	} else {
		return time.FixedZone(fmt.Sprintf("UTC%s", tz), numTz*60*60)
	}
}

func GetCurrentTime(loc *time.Location) time.Time {
	return time.Now().In(loc)
}

func GetCurrentTimestamp(loc *time.Location) *timestamppb.Timestamp {
	d := GetCurrentTime(loc)
	t := timestamppb.New(d)
	return t
}

/*
Example:
input: "demo_test"
symbol: "_"

result is "demo"
*/
func SubStringBeforeCharacter(input string, symbol string) string {
	position := strings.Index(input, symbol)
	if position == -1 {
		return ""
	}
	return input[0:position]
}

/*
Example:
input: "demo_test"
symbol: "_"

result is "test"
*/
func SubStringAfterCharacter(input string, symbol string) string {
	position := strings.LastIndex(input, symbol)
	if position == -1 {
		return ""
	}
	adjustedPos := position + len(symbol)
	if adjustedPos >= len(input) {
		return ""
	}
	return input[adjustedPos:]
}

/*
Example:
input: "demo_test_haha"
symbol: "_"

result is "test"
*/
func SubStringBetweenCharacters(input string, symbol string, b string) string {
	posFirst := strings.Index(input, symbol)
	if posFirst == -1 {
		return ""
	}
	posLast := strings.Index(input, b)
	if posLast == -1 {
		return ""
	}
	posFirstAdjusted := posFirst + len(symbol)
	if posFirstAdjusted >= posLast {
		return ""
	}
	return input[posFirstAdjusted:posLast]
}

/*
Generate a random string with length is n
*/
// Deprecated: Use globalLib/common.RandomString instead
func RandomString(n int) string {
	var letter = []rune("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789")
	result := make([]rune, n)
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := range result {
		result[i] = letter[r.Intn(len(letter))]
	}
	return string(result)
}

/*
Generate a random int in range min, max
*/
func RandomIntInRange(min int, max int) int {
	if min == max {
		return min
	}
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	return r.Intn(max-min) + min
}

func ConvertInterfaceToMapInterface(key interface{}) (map[string]interface{}, error) {
	var buf bytes.Buffer
	enc := json.NewEncoder(&buf)
	err := enc.Encode(key)
	if err != nil {
		return nil, err
	}
	value := make(map[string]interface{})
	json.Unmarshal(buf.Bytes(), &value)
	return value, nil
}

func ConvertStructToMap(data interface{}) (map[string]interface{}, error) {
	b, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}
	result := make(map[string]interface{})
	err = json.Unmarshal(b, &result)
	return result, err
}

/*
Example:
input: {"cc", "bb", "aa", "cc", "bb", "eee", "dd", "aa"}

result is [cc bb aa eee dd]
*/
func UniqString(input []string) []string {
	u := make([]string, 0, len(input))
	m := make(map[string]bool)
	for _, val := range input {
		if _, ok := m[val]; !ok {
			m[val] = true
			u = append(u, val)
		}
	}
	return u
}

/*
Example:
input: [0, 2, 4, 2, 0, 3]
result: [0, 2, 4, 3]
*/
func UniqInt32(input []int32) []int32 {
	u := make([]int32, 0, len(input))
	m := make(map[int32]bool)
	for _, val := range input {
		if _, ok := m[val]; !ok {
			m[val] = true
			u = append(u, val)
		}
	}
	return u
}

/*
This function compares ver1 vs ver2

	If ver1 > ver2 return 1
	If ver1 < ver2 return -1
	If ver1 == ver2 return 0
*/
func CompareVersion(ver1 string, ver2 string) int {
	slice1 := strings.Split(ver1, ".")
	slice2 := strings.Split(ver2, ".")
	if len(slice1) != 3 || len(slice2) != 3 {
		return -1
	}

	//Version 1
	ver1LetterOne, err := strconv.Atoi(slice1[0])
	if err != nil {
		return -1
	}
	ver1LetterTwo, err := strconv.Atoi(slice1[1])
	if err != nil {
		return -1
	}

	ver1LetterThree, err := strconv.Atoi(slice1[2])
	if err != nil {
		return -1
	}

	// Version 2
	ver2LetterOne, err := strconv.Atoi(slice2[0])
	if err != nil {
		return -1
	}
	ver2LetterTwo, err := strconv.Atoi(slice2[1])
	if err != nil {
		return -1
	}

	ver2LetterThree, err := strconv.Atoi(slice2[2])
	if err != nil {
		return -1
	}

	//Compare
	//Check letter 1
	if ver1LetterOne > ver2LetterOne {
		return 1
	}

	if ver1LetterOne < ver2LetterOne {
		return -1
	}

	//Check letter 2
	if ver1LetterTwo > ver2LetterTwo {
		return 1
	}
	if ver1LetterTwo < ver2LetterTwo {
		return -1
	}

	//Check letter 3
	if ver1LetterThree > ver2LetterThree {
		return 1
	}
	if ver1LetterThree < ver2LetterThree {
		return -1
	}
	return 0
}

func ParseDateFromTimeStamp(date *timestamppb.Timestamp, loc *time.Location) time.Time {
	d := date.AsTime()
	return d.In(loc)
}

func ParseSliceDateFromSliceTimeStamp(date []*timestamppb.Timestamp, loc *time.Location) []time.Time {
	var result []time.Time
	for _, v := range date {
		d := v.AsTime()
		result = append(result, d.In(loc))
	}
	return result
}

func ParseDateFromString(date string, loc *time.Location) time.Time {
	d, _ := time.Parse(time.RFC3339, date)
	return d.In(loc)
}

func ParseDateFromTimeStampNumber(t int64, loc *time.Location) time.Time {
	return time.Unix(t, 0).In(loc)
}

func ParseTimestampFromDate(date time.Time) *timestamppb.Timestamp {
	d := timestamppb.New(date)
	return d
}

func IsSameDate(t1, t2 time.Time) bool {
	return t1.Year() == t2.Year() &&
		t1.Month() == t2.Month() &&
		t1.Day() == t2.Day()
}

func RoundMoney(money float64, isoCode string) float64 {
	var value float64
	if isoCode == globalConstant.ISO_CODE_TH {
		value = math.Round(money/1) * 1
	} else if isoCode == globalConstant.ISO_CODE_VN {
		value = math.Round(money/1000) * 1000
	} else if isoCode == globalConstant.ISO_CODE_INDO {
		value = math.Round(money/1000) * 1000
	} else if isoCode == globalConstant.ISO_CODE_MY {
		value = math.Round(money/1) * 1
	}
	return value
}

func RoundDownMoney(money float64, isoCode string) float64 {
	var value float64
	if isoCode == globalConstant.ISO_CODE_TH {
		value = math.Floor(money/1) * 1
	} else if isoCode == globalConstant.ISO_CODE_VN {
		value = math.Floor(money/1000) * 1000
	} else if isoCode == globalConstant.ISO_CODE_INDO {
		value = math.Floor(money/1000) * 1000
	} else if isoCode == globalConstant.ISO_CODE_MY {
		value = math.Floor(money/1) * 1
	}
	return value
}

/*
*
first Param: the date which want to compare
second param: the start date
third param: the end date
*/
func DateBetweenTwoDate(date time.Time, startDate time.Time, endDate time.Time) bool {
	//Convert to same timezone
	return date.After(startDate) && date.Before(endDate)
}

func IsHomeCleaningService(enText string) (result bool) {
	return enText == globalConstant.SERVICE_NAME_HOME_CLEANING
}

func IsHouseKeepingService(enText string) (result bool) {
	return enText == globalConstant.SERVICE_NAME_HOUSEKEEPING
}

func IsHomeCookingService(enText string) (result bool) {
	return enText == globalConstant.SERVICE_NAME_HOME_COOKING
}

func IsACService(enText string) (result bool) {
	return enText == globalConstant.SERVICE_NAME_AIR_CONDITIONER
}

func IsDeepCleaningService(enText string) (result bool) {
	return enText == globalConstant.SERVICE_NAME_DEEP_CLEANING
}

func IsLaundryService(enText string) (result bool) {
	return enText == globalConstant.SERVICE_NAME_LAUNDRY
}

func IsGroceryAssistantService(enText string) (result bool) {
	return enText == globalConstant.SERVICE_NAME_GROCERY_ASSISTANT
}

func IsSofaService(enText string) (result bool) {
	return enText == globalConstant.SERVICE_NAME_UPHOLSTERY
}

func IsElderlyCareService(enText string) (result bool) {
	return enText == globalConstant.SERVICE_NAME_ELDERLY_CARE
}

func IsPatientCareService(enText string) (result bool) {
	return enText == globalConstant.SERVICE_NAME_PATIENT_CARE
}

func IsDisinfectionService(enText string) (result bool) {
	return enText == globalConstant.SERVICE_NAME_DISINFECTION
}

func IsChildCareService(enText string) (result bool) {
	return enText == globalConstant.SERVICE_NAME_CHILD_CARE
}

// ======== Check service by key name
func IsCarAdvertising(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_CAR_ADVERTISING
}

func IsAirConditionerServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER
}

func IsDeepCleaningServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING
}

func IsLaundryServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_LAUNDRY
}

func IsOfficeCleaningServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_OFFICE_CLEANING
}
func IsOfficeCleaningSubscriptionServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_OFFICE_CLEANING_SUBSCRIPTION
}

func IsWashingMachineServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_WASHING_MACHINE
}

func IsHomeCleaningServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_HOME_CLEANING
}

func IsHomeCleaningSubscriptionServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION
}

func IsHouseKeepingServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_HOUSEKEEPING
}

func IsHomeCookingServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_HOME_COOKING
}

func IsGroceryAssistantServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_GROCERY_ASSISTANT
}

func IsElderlyCareServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_ELDERLY_CARE
}

func IsElderlyCareSubscriptionServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_ELDERLY_CARE_SUBSCRIPTION
}

func IsPatientCareServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_PATIENT_CARE
}

func IsPatientCareSubscriptionServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_PATIENT_CARE_SUBSCRIPTION
}

func IsSofaServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_UPHOLSTERY
}

func IsWaterHeaterServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_WATER_HEATER
}

func IsCarpetCleaningServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_CARPET_CLEANING
}

func IsHomeMovingServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_HOME_MOVING
}

func IsMassageServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_MASSAGE
}

func IsDisinfactionServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_DISINFECTION
}
func IsChildCareServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_CHILD_CARE
}

func IsChildCareSubscriptionServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_CHILD_CARE_SUBSCRIPTION
}

func IsIndustrialCleaningServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING
}

func IsBeautyCareServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_BEAUTY_CARE
}

func IsMakeupServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_MAKEUP
}

func IsHairStylingServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_HAIR_STYLING
}

func IsNailServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_NAIL
}

func IsIroningServiceByKeyName(serviceName string) (result bool) {
	return serviceName == globalConstant.SERVICE_KEY_NAME_IRONING
}

// ===================================

func RandomInteger(min int, max int) int {
	return rand.Intn(max-min) + min
}

func StartADay(t time.Time) time.Time {
	year, month, day := t.Date()
	return time.Date(year, month, day, 0, 0, 0, 0, t.Location())
}

func EndADay(t time.Time) time.Time {
	year, month, day := t.Date()
	return time.Date(year, month, day, 23, 59, 59, 0, t.Location())
}

func LocalizeServiceName(language string, serviceText *modelService.ServiceText) string {
	result := ""
	switch language {
	case "vi", "VI":
		result = serviceText.GetVi()
	case "ko", "KO":
		result = serviceText.GetKo()
	case "th", "TH":
		result = serviceText.GetTh()
	case "id", "ID":
		return serviceText.GetId()
	case "ms", "MS":
		return serviceText.GetMs()
	default:
		result = serviceText.GetEn()
	}
	if result == "" {
		result = serviceText.GetEn()
	}
	return result
}

// this func return localize object contains all language with the same value
func ConvertStringToText(str string) *modelService.ServiceText {
	return &modelService.ServiceText{
		Vi: str,
		En: str,
		Ko: str,
		Th: str,
		Id: str,
	}
}

func GetFAccountByIsoCode(fAccountId, isoCode string) (float64, float64) {
	var main, promotion float64
	switch isoCode {
	case globalConstant.ISO_CODE_VN:
		faVN, _ := financialAccountVN.GetOne(bson.M{"_id": fAccountId}, bson.M{})
		main = faVN.GetFMainAccount()
		promotion = faVN.GetPromotion()
	case globalConstant.ISO_CODE_TH:
		faTH, _ := financialAccountTH.GetOne(bson.M{"_id": fAccountId}, bson.M{})
		main = faTH.GetTH_FMainAccount()
		promotion = faTH.GetTH_Promotion()
	case globalConstant.ISO_CODE_INDO:
		faID, _ := financialAccountIndo.GetOne(bson.M{"_id": fAccountId}, bson.M{})
		main = faID.GetID_FMainAccount()
		promotion = faID.GetID_Promotion()
	case globalConstant.ISO_CODE_MY:
		faMY, _ := financialAccountMY.GetOne(bson.M{"_id": fAccountId}, bson.M{})
		main = faMY.GetFMainAccount()
		promotion = faMY.GetPromotion()
	}
	return main, promotion
}

func GenerateObjectId() string {
	id := primitive.NewObjectID().Hex()
	return fmt.Sprintf("x%x", md5.Sum([]byte(id)))
}

func GetBookingCurrency(booking *modelTask.Task) string {
	var currency string
	if booking != nil {
		if booking.OriginCurrency != nil && booking.OriginCurrency.Code != "" {
			currency = booking.OriginCurrency.Code
		} else {
			currency = booking.Currency
		}
	}
	return currency
}

// func GetAskerPoint(asker *users.Users) (point float64) {
// 	if asker == nil {
// 		return
// 	}
// 	switch asker.IsoCode {
// 	case globalConstant.ISO_CODE_VN:
// 		point = asker.Point
// 	case globalConstant.ISO_CODE_TH:
// 		if asker.BPoint != nil && asker.BPoint.TH > 0 {
// 			point = asker.BPoint.TH
// 		}
// 	case globalConstant.ISO_CODE_INDO:
// 		if asker.BPoint != nil && asker.BPoint.ID > 0 {
// 			point = asker.BPoint.ID
// 		}
// 	}
// 	return
// }

// func GetRankAsker(asker *users.Users) (askerRank string) {
// 	askerRank = globalConstant.ASKER_RANK_NAME_MEMBER
// 	if asker == nil {
// 		return
// 	}
// 	switch asker.IsoCode {
// 	case globalConstant.ISO_CODE_VN:
// 		if asker.RankInfo != nil && asker.RankInfo.RankName != "" {
// 			askerRank = asker.RankInfo.RankName
// 		}
// 	case globalConstant.ISO_CODE_TH:
// 		if asker.RankInfoByCountry != nil && asker.RankInfoByCountry.TH != nil && asker.RankInfoByCountry.TH.RankName != "" {
// 			askerRank = asker.RankInfoByCountry.TH.RankName
// 		}
// 	case globalConstant.ISO_CODE_INDO:
// 		if asker.RankInfoByCountry != nil && asker.RankInfoByCountry.ID != nil && asker.RankInfoByCountry.ID.RankName != "" {
// 			askerRank = asker.RankInfoByCountry.ID.RankName
// 		}
// 	}
// 	return
// }

func GetBookingCurrencyCodeAndSign(booking *modelTask.Task) (string, string) {
	var code, sign string
	if booking != nil {
		if booking.OriginCurrency == nil {
			code = booking.Currency
		} else {
			code = booking.OriginCurrency.Code
			sign = booking.OriginCurrency.Sign
		}
	}
	return code, sign
}

func FormatMoney(money float64) string {
	ac := accounting.Accounting{Precision: 0}
	return ac.FormatMoney(money)
}

func Union(a, b []string) []string {
	m := make(map[string]bool)

	for _, item := range a {
		m[item] = true
	}

	for _, item := range b {
		if _, ok := m[item]; !ok {
			a = append(a, item)
		}
	}
	return a
}

func UnionTimeArray(a, b []time.Time) []time.Time {
	m := make(map[time.Time]bool)

	for _, item := range a {
		m[item] = true
	}

	for _, item := range b {
		if _, ok := m[item]; !ok {
			a = append(a, item)
		}
	}
	return a
}

func RemoveTaskersFromList(taskerList []string, blackList []string) []string {
	if len(taskerList) > 0 && len(blackList) > 0 {
		for _, vBlackList := range blackList {
			index := findItemIndex(taskerList, vBlackList)
			if index >= 0 {
				taskerList = removeIndex(taskerList, index)
			}
		}
	}
	return taskerList
}

/*
intersection([2, 1], [2, 3])
// => [2]
*/
func IntersectionArray(array1 []string, array2 []string) []string {
	var newArray []string
	for _, v := range array2 {
		index := findItemIndex(array1, v)
		if index >= 0 {
			newArray = append(newArray, v)
		}
	}
	return newArray
}

/*
difference([2, 1], [2, 3])
// => [1]
*/
func DifferenceArray(array1 []string, array2 []string) []string {
	if len(array2) == 0 {
		return array1
	}
	var newArray []string
	for _, v := range array1 {
		index := findItemIndex(array2, v)
		if index < 0 {
			newArray = append(newArray, v)
		}
	}
	return newArray
}

func LoadTimeLocation(t time.Time, zone string) (time.Time, error) {
	loc, err := time.LoadLocation(zone)
	if err == nil {
		t = t.In(loc)
	}
	return t, err
}

func GetCountryCodeByIsoCode(isoCode string) (countryCode string) {
	if countryCode, ok := globalConstant.COUNTRY_CODE[isoCode]; ok {
		return countryCode
	}
	return globalConstant.COUNTRY_CODE_VN
}

func GetIsoCodeByCountry(countryCode string) (isoCode string) {
	if isoCode, ok := globalConstant.ISO_CODE[countryCode]; ok {
		return isoCode
	}
	return globalConstant.ISO_CODE_VN
}

func IsPhoneValid(phone string, isoCode string) bool {
	if phone == "" {
		return false
	}
	phoneRegex := `^[0][9]\d{8}$|[0][8][1-9]\d{7}$|[0][1][2|6|8|9]\d{8}$|[0][5][2|6|8|9]\d{7}$|[0][3][2-9]\d{7}$|[0][7][0|6|7|8|9]\d{7}$`
	switch isoCode {
	case globalConstant.ISO_CODE_TH:
		hasPrefixZero := strings.HasPrefix(phone, "0")
		if hasPrefixZero {
			phoneRegex = `^[0][6]\d{8}$|[0][8]\d{8}$|[0][9]\d{8}$`
		} else {
			phoneRegex = `^[6]\d{8}$|[8]\d{8}$|[9]\d{8}$`
		}
	case globalConstant.ISO_CODE_INDO:
		phoneRegex = `^(\+628|628|08|8)[1-9][0-9]{6,10}$`
	case globalConstant.ISO_CODE_MY:
		phoneRegex = `^(\+60|60|0)?1[0-9]{1}[0-9]{7,8}$|^(\+60|60)?[1-9][0-9]{1,2}-?[0-9]{7}$`
	default:
		break
	}
	valid, _ := regexp.MatchString(phoneRegex, phone)
	return valid
}

func IsEmailValid(email string) bool {
	if len(email) < 3 && len(email) > 254 {
		return false
	}
	emailRegex := regexp.MustCompile(`^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$`)
	return emailRegex.MatchString(email)
}

func CheckCardExpiry(cardInfo *modelPaymentCard.PaymentCard) bool {
	expiryYear, err := strconv.Atoi(cardInfo.ExpiryYear)
	if err != nil {
		return true
	}
	expiryMonth, err := strconv.Atoi(cardInfo.ExpiryMonth)
	if err != nil {
		return true
	}
	expiryDate := time.Date(expiryYear, time.Month(expiryMonth+1), 1, 0, 0, 0, 0, GetTimeZone())
	now := time.Now()
	return now.After(expiryDate)
}

func FindStringInSlice(array []string, s string) int {
	return findItemIndex(array, s)
}

func FindInt32InSlice(array []int32, value int32) int {
	index := -1
	if len(array) > 0 {
		for i, v := range array {
			if v == value {
				index = i
				break
			}
		}
	}
	return index
}

func FindIntInSlice(array []int, value int) int {
	index := -1
	if len(array) > 0 {
		for i, v := range array {
			if v == value {
				index = i
				break
			}
		}
	}
	return index
}

func FindTimeInSlice(array []time.Time, value time.Time) int {
	index := -1
	if len(array) > 0 {
		for i, v := range array {
			if v.Equal(value) {
				index = i
				break
			}
		}
	}
	return index
}

func FindFloat64InSlice(array []float64, value float64) int {
	index := -1
	if len(array) > 0 {
		for i, v := range array {
			if v == value {
				index = i
				break
			}
		}
	}
	return index
}

// For Tasker all version && Asker old version
func GenerateActivationCode() string {
	return strconv.Itoa(randomNumberByNumberOfDigits(4))
}

// For Asker new version only
func GenerateActivationCodeV2(userType string, appVersion string) string {
	// TODO: Update app version trước khi lên production
	if userType == globalConstant.USER_TYPE_ASKER && CompareVersion(appVersion, "3.35.0") >= 0 {
		return strconv.Itoa(randomNumberByNumberOfDigits(6))
	}
	return GenerateActivationCode()
}

func randomNumberByNumberOfDigits(n int) int {
	var min int = int(math.Pow(10, float64(n-1)))
	var max int = int(math.Pow(10, float64(n))) - 1
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	num := r.Intn(max-min) + min
	return num
}

func PostToSlack(token string, channel string, username string, message string) {
	defer Logger.Sync()
	if token != "" {
		slackMessageOption := []slack.MsgOption{
			slack.MsgOptionText(message, false),
		}
		if username != "" {
			slackMessageOption = append(slackMessageOption, slack.MsgOptionUsername(username))
		}
		api := slack.New(token)
		_, _, err := api.PostMessage(channel, slackMessageOption...)
		if err != nil {
			Logger.Info("SLACK_ERROR",
				zap.Error(err),
				zap.String("channel", channel),
				zap.String("message", message),
			)
		}
	} else {
		Logger.Info("SLACK_ERROR_TOKEN_EMPTY",
			zap.String("channel", channel),
			zap.String("message", message),
		)
	}
}

func PostToSlackViaBot(token string, channel string, message string) {
	PostToSlack(token, channel, "", message)
}

func GetIPFromHttpRequest(r *http.Request) string {
	//Get IP from the X-REAL-IP header
	ip := r.Header.Get("X-REAL-IP")
	netIP := net.ParseIP(ip)
	if netIP != nil {
		return ip
	}

	//Get IP from X-FORWARDED-FOR header
	ips := r.Header.Get("X-FORWARDED-FOR")
	splitIps := strings.Split(ips, ",")
	for _, ip := range splitIps {
		netIP := net.ParseIP(ip)
		if netIP != nil {
			return ip
		}
	}

	//Get IP from RemoteAddr
	ip, _, err := net.SplitHostPort(r.RemoteAddr)
	if err != nil {
		return ""
	}
	netIP = net.ParseIP(ip)
	if netIP != nil {
		return ip
	}
	return ""
}

func GetTimeMiliseconds(d *timestamppb.Timestamp) int64 {
	return d.Seconds * 1000
}

func GetServiceKeyName(serviceTextEn string) string {
	switch serviceTextEn {
	case globalConstant.SERVICE_NAME_HOME_CLEANING:
		return globalConstant.SERVICE_KEY_NAME_HOME_CLEANING
	case globalConstant.SERVICE_NAME_AIR_CONDITIONER:
		return globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER
	case globalConstant.SERVICE_NAME_LAUNDRY:
		return globalConstant.SERVICE_KEY_NAME_LAUNDRY
	case globalConstant.SERVICE_NAME_DEEP_CLEANING:
		return globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING
	case globalConstant.SERVICE_NAME_HOME_COOKING:
		return globalConstant.SERVICE_KEY_NAME_HOME_COOKING
	case globalConstant.SERVICE_NAME_HOUSEKEEPING:
		return globalConstant.SERVICE_KEY_NAME_HOUSEKEEPING
	case globalConstant.SERVICE_NAME_GROCERY_ASSISTANT:
		return globalConstant.SERVICE_KEY_NAME_GROCERY_ASSISTANT
	case globalConstant.SERVICE_NAME_UPHOLSTERY:
		return globalConstant.SERVICE_KEY_NAME_UPHOLSTERY
	case globalConstant.SERVICE_NAME_ELDERLY_CARE:
		return globalConstant.SERVICE_KEY_NAME_ELDERLY_CARE
	case globalConstant.SERVICE_NAME_PATIENT_CARE:
		return globalConstant.SERVICE_KEY_NAME_PATIENT_CARE
	case globalConstant.SERVICE_NAME_DISINFECTION:
		return globalConstant.SERVICE_KEY_NAME_DISINFECTION
	case globalConstant.SERVICE_NAME_CHILD_CARE:
		return globalConstant.SERVICE_KEY_NAME_CHILD_CARE
	case globalConstant.SERVICE_NAME_OFFICE_CLEANING:
		return globalConstant.SERVICE_KEY_NAME_OFFICE_CLEANING
	case globalConstant.SERVICE_NAME_WASHING_MACHINE:
		return globalConstant.SERVICE_KEY_NAME_WASHING_MACHINE
	case globalConstant.SERVICE_NAME_WATER_HEATER:
		return globalConstant.SERVICE_KEY_NAME_WATER_HEATER
	case globalConstant.SERVICE_NAME_CARPET_CLEANING:
		return globalConstant.SERVICE_KEY_NAME_CARPET_CLEANING
	case globalConstant.SERVICE_NAME_HOME_MOVING:
		return globalConstant.SERVICE_KEY_NAME_HOME_MOVING
	case globalConstant.SERVICE_NAME_INDUSTRIAL_CLEANING:
		return globalConstant.SERVICE_KEY_NAME_INDUSTRIAL_CLEANING
	default:
		return ""
	}
}

// func DetectLanguage(text string, key string) (map[string]interface{}, error) {
// 	body := map[string]interface{}{
// 		"q": text,
// 	}
// 	reqBody, _ := json.Marshal(body)
// 	url := fmt.Sprintf("https://translation.googleapis.com/language/translate/v2/detect?key=%s", key)
// 	req, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
// 	if err != nil {
// 		return nil, err
// 	}
// 	client := &http.Client{}
// 	resp, err := client.Do(req)
// 	if err != nil || resp.Body == nil {
// 		return nil, err
// 	}
// 	b, _ := io.ReadAll(resp.Body)
// 	temp := make(map[string]interface{})
// 	json.Unmarshal(b, &temp)
// 	result := make(map[string]interface{})
// 	if temp["data"] != nil {
// 		b, _ = json.Marshal(temp["data"])
// 		temp = make(map[string]interface{})
// 		json.Unmarshal(b, &temp)
// 		if temp["detections"] != nil {
// 			b, _ = json.Marshal(temp["detections"])
// 			var array [][]map[string]interface{}
// 			json.Unmarshal(b, &array)
// 			if len(array) > 0 {
// 				result = array[0][0]
// 			}
// 		} else {
// 			return nil, fmt.Errorf("error response data.detections is nil. response: %s", string(b))
// 		}
// 	} else {
// 		return nil, fmt.Errorf("error response data is nil. response: %s", string(b))
// 	}
// 	return result, nil
// }

// func TranslateFromGoogle(text string, language string, key string) (map[string]interface{}, error) {
// 	if language == "" {
// 		language = globalConstant.LANG_VI
// 	}
// 	body := map[string]interface{}{
// 		"q":      text,
// 		"target": language,
// 		"format": "text",
// 	}
// 	reqBody, _ := json.Marshal(body)
// 	url := fmt.Sprintf("https://translation.googleapis.com/language/translate/v2?key=%s", key)
// 	req, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
// 	if err != nil {
// 		return nil, err
// 	}
// 	client := &http.Client{}
// 	resp, err := client.Do(req)
// 	if err != nil || resp.Body == nil {
// 		return nil, err
// 	}
// 	b, _ := io.ReadAll(resp.Body)
// 	temp := make(map[string]interface{})
// 	json.Unmarshal(b, &temp)
// 	result := make(map[string]interface{})
// 	if temp["data"] != nil {
// 		b, _ = json.Marshal(temp["data"])
// 		temp = make(map[string]interface{})
// 		json.Unmarshal(b, &temp)
// 		if temp["translations"] != nil {
// 			b, _ = json.Marshal(temp["translations"])
// 			var array []map[string]interface{}
// 			json.Unmarshal(b, &array)
// 			if len(array) > 0 {
// 				result = array[0]
// 			}
// 		} else {
// 			return nil, fmt.Errorf("error response data.translations is nil. response: %s", string(b))
// 		}
// 	} else {
// 		return nil, fmt.Errorf("error response data is nil. response: %s", string(b))
// 	}
// 	return result, nil
// }

func GenerateVoiceCallToken(userId string, sid string, secret string) string {
	var tokenStr string
	now := time.Now()
	exp := now.AddDate(100, 0, 0).Unix()
	header := map[string]interface{}{
		"typ": "JWT",
		"alg": "HS256",
		"cty": "stringee-api;v=1",
	}
	claims := jwt.MapClaims{
		"jti":    fmt.Sprintf("%v-%v", sid, now.Unix()),
		"iss":    sid,
		"exp":    exp,
		"userId": userId,
	}
	token := jwt.Token{
		Method: jwt.SigningMethodHS256,
		Header: header,
		Claims: claims,
	}
	tokenStr, _ = token.SignedString([]byte(secret))
	return tokenStr
}

func GenerateUsername(phone string, countryCode string) string {
	phone = strings.TrimPrefix(phone, "0")
	return fmt.Sprintf("%s%s", countryCode[1:], phone)
}

func DetectOld11DigitPhoneNumber(phone string) string {
	first4Digits := phone[0:4]
	var newFirstDigits string
	switch first4Digits {
	case "0169":
		newFirstDigits = "039"
	case "0168":
		newFirstDigits = "038"
	case "0167":
		newFirstDigits = "037"
	case "0166":
		newFirstDigits = "036"
	case "0165":
		newFirstDigits = "035"
	case "0164":
		newFirstDigits = "034"
	case "0163":
		newFirstDigits = "033"
	case "0162":
		newFirstDigits = "032"
	case "0120":
		newFirstDigits = "070"
	case "0121":
		newFirstDigits = "079"
	case "0122":
		newFirstDigits = "077"
	case "0126":
		newFirstDigits = "076"
	case "0128":
		newFirstDigits = "078"
	case "0123":
		newFirstDigits = "083"
	case "0124":
		newFirstDigits = "084"
	case "0125":
		newFirstDigits = "085"
	case "0127":
		newFirstDigits = "081"
	case "0129":
		newFirstDigits = "082"
	case "0186":
		newFirstDigits = "056"
	case "0188":
		newFirstDigits = "058"
	case "0199":
		newFirstDigits = "059"
	default:
		newFirstDigits = ""
	}
	return newFirstDigits
}

func SendMessageWebsocket(url string, data map[string]interface{}) error {
	c, _, err := websocket.DefaultDialer.Dial(url, nil)
	if err != nil {
		return err
	}
	defer c.Close()

	err = c.WriteJSON(data)
	if err != nil {
		return err
	}
	return nil
}

func MigratePhoneNumber(phone string, isoCode string) string {
	if !strings.HasPrefix(phone, "0") && (isoCode == globalConstant.ISO_CODE_TH || isoCode == globalConstant.ISO_CODE_VN || isoCode == globalConstant.ISO_CODE_INDO || isoCode == globalConstant.ISO_CODE_MY) {
		return fmt.Sprintf("0%s", phone)
	}

	return phone
}

func GetFloatValueByKey(obj interface{}, key string) float64 {
	textMap := make(map[string]float64)
	textData, _ := json.Marshal(obj)
	json.Unmarshal(textData, &textMap)
	return textMap[key]
}

func GetStringValueByKey(obj interface{}, key string) string {
	textMap := make(map[string]string)
	textData, _ := json.Marshal(obj)
	json.Unmarshal(textData, &textMap)
	return textMap[key]
}

func ParseChangesHistoryContent(data map[string]interface{}) string {
	b, err := json.Marshal(data)
	if err != nil {
		return ""
	}
	return string(b)
}

func CalcWeekDayTimestamp(date []*timestamppb.Timestamp, timeZone *time.Location) []int32 {
	result := []int32{}
	for _, d := range date {
		weekday := ParseDateFromTimeStamp(d, timeZone).Weekday()
		result = append(result, int32(weekday))
	}
	result = UniqInt32(result)
	sort.Slice(result, func(i, j int) bool { return result[i] < result[j] })

	return result
}

func CalcWeekDayTime(date []time.Time, timeZone *time.Location) []int32 {
	result := []int32{}
	for _, d := range date {
		dInLocation := d.In(timeZone)
		weekday := dInLocation.Weekday()
		result = append(result, int32(weekday))
	}
	result = UniqInt32(result)
	sort.Slice(result, func(i, j int) bool { return result[i] < result[j] })

	return result
}

// ===============================

func findItemIndex(array []string, value string) int {
	index := -1
	if len(array) > 0 {
		for i, v := range array {
			if v == value {
				index = i
				break
			}
		}
	}
	return index
}

func removeIndex(s []string, index int) []string {
	return append(s[:index], s[index+1:]...)
}

func CheckCardExpiryAtTaskDate(taskEndTime time.Time, cardInfo *modelPaymentCard.PaymentCard) bool {
	expiryYear, err := strconv.Atoi(cardInfo.ExpiryYear)
	if err != nil {
		return true
	}
	expiryMonth, err := strconv.Atoi(cardInfo.ExpiryMonth)
	if err != nil {
		return true
	}
	expiryDate := time.Date(expiryYear, time.Month(expiryMonth+1), 1, 0, 0, 0, 0, GetTimeZone())
	return taskEndTime.After(expiryDate)
}

func SendLoggly(body interface{}, token string) error {
	reqBody, _ := json.Marshal(body)
	url := fmt.Sprintf("https://logs-01.loggly.com/inputs/%s/tag/http/", token)
	req, err := http.NewRequest("POST", url, bytes.NewBuffer(reqBody))
	req.Header.Set("Content-Type", "application/json")
	if err != nil {
		return err
	}
	client := &http.Client{
		Timeout: 10 * time.Second,
	}
	_, err = client.Do(req)
	if err != nil {
		return err
	}
	return nil
}

/*
 * @Description: SplitArrayToChunk
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 */
func SplitArrayToChunk(array []string, numOfItemChunk int) [][]string {
	if numOfItemChunk == 0 {
		numOfItemChunk = 999
	}
	var chunks [][]string
	for i := 0; i < len(array); i += numOfItemChunk {
		end := i + numOfItemChunk
		if end > len(array)-1 {
			end = len(array)
		}
		chunk := array[i:end]
		chunks = append(chunks, chunk)
	}
	return chunks
}

/*
 * @Description: Update contact information of btaskee in email by isoCode
 * @CreatedAt: 29/04/2022
 * @Author: ngoctb3
 */
func UpdateBtaskeeInfoInEmailByIsoCode(emailData map[string]interface{}, isoCode string) map[string]interface{} {
	var facebookUrl, instagramUrl, youtubeUrl, twitterUrl string
	var fullAddress, address_1, address_2, address_3 string
	var phone, tel_phone string
	var mail, mail_to string
	if isoCode == globalConstant.ISO_CODE_TH {
		facebookUrl = "https://www.facebook.com/btaskeethailand"
		instagramUrl = "https://www.instagram.com/btaskeeth/"
		youtubeUrl = "https://www.youtube.com/channel/UCfmPmydxjOik8A7a2eQwGgg"
		twitterUrl = "https://twitter.com/btaskee"
		fullAddress = "654/26 โครงการ สามย่านบิสซิเนสทาวน์ ถนนพระรามที่ 4 แขวงมหาพฤฒาราม เขตบางรัก กรุงเทพมหานคร 10500"
		address_1 = "654/26 โครงการ สามย่านบิสซิเนสทาวน์"
		address_2 = "ถนนพระรามที่ 4 แขวงมหาพฤฒาราม เขตบางรัก"
		address_3 = "กรุงเทพมหานคร 10500"
		phone = "02 113 1345"
		tel_phone = "tel:021131345"
		mail = "<EMAIL>"
		mail_to = "mailTo:<EMAIL>"
	} else if isoCode == globalConstant.ISO_CODE_INDO {
		facebookUrl = "https://www.facebook.com/profile.php?id=61551986097235"
		instagramUrl = "https://www.instagram.com/btaskee/"
		youtubeUrl = "https://www.youtube.com/channel/UC55F4JRrI9ewVpoR6DOMfkQ"
		twitterUrl = "https://twitter.com/btaskee"
		fullAddress = "Millennium Centennial Center, Lt. 38, Jl. Jend. Sudirman No. Kav 25, Kec. Setiabudi, Kota Jakarta Selatan"
		address_1 = "Millennium Centennial Center, Lt. 38,"
		address_2 = "Jl. Jend. Sudirman No. Kav 25,"
		address_3 = "Kec. Setiabudi, Kota Jakarta Selatan"
		phone = "0811 1000 7590"
		tel_phone = "tel:081110007590"
		mail = "<EMAIL>"
		mail_to = "mailTo:<EMAIL>"
	} else {
		facebookUrl = "https://www.facebook.com/btaskee"
		instagramUrl = "https://www.instagram.com/btaskee/"
		youtubeUrl = "https://www.youtube.com/channel/UC55F4JRrI9ewVpoR6DOMfkQ"
		twitterUrl = "https://twitter.com/btaskee"
		fullAddress = "284/25/20 Lý Thường Kiệt, Phường 14, Quận 10, Tp.Hồ Chí Minh."
		address_1 = "284/25/20 Lý Thường Kiệt,"
		address_2 = "Phường 14, Quận 10,"
		address_3 = "Tp.Hồ Chí Minh."
		phone = "1900 636 736"
		tel_phone = "tel:1900636736"
		mail = "<EMAIL>"
		mail_to = "mailTo:<EMAIL>"
	}

	emailData["BtaskeeFacebookUrl"] = facebookUrl
	emailData["BtaskeeInstagramUrl"] = instagramUrl
	emailData["BtaskeeYoutubeUrl"] = youtubeUrl
	emailData["BtaskeeTwitterUrl"] = twitterUrl
	emailData["BtaskeeFullAddress"] = fullAddress
	emailData["BtaskeeAddress1"] = address_1
	emailData["BtaskeeAddress2"] = address_2
	emailData["BtaskeeAddress3"] = address_3
	emailData["BtaskeePhone"] = phone
	emailData["BtaskeeTelPhone"] = tel_phone
	emailData["BtaskeeMail"] = mail
	emailData["BtaskeeMailTo"] = mail_to
	emailData["CurrentYear"] = GetCurrentTime(GetTimeZone()).Year()
	return emailData
}

/*
 * @Description: Check if two task confict at working date
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 21/01/2021
 * @UpdatedBy: linhnh
 */
func IsConflictTask(task *modelTask.Task, otherTask *modelTask.Task, timeInBetweenTask int, timeZone *time.Location) bool {
	// Calculate distance between 2 places
	lat1 := task.Lat
	lng1 := task.Lng
	lat2 := otherTask.Lat
	lng2 := otherTask.Lng
	distance := CalcDistance(lat1, lng1, lat2, lng2)

	// If 2 tasks in 500m => let Tasker accept with timeInBetweenTask 15 mins
	if distance <= globalConstant.DEFAULT_DISTANCE {
		timeInBetweenTask = 15
	}
	taskDate := ParseDateFromTimeStamp(task.Date, timeZone)
	taskStart_1 := taskDate.Add(time.Duration(-timeInBetweenTask) * time.Minute)
	taskEnd_1 := taskDate.Add(time.Duration(task.Duration*60+float64(timeInBetweenTask)) * time.Minute)

	otherTaskDate := ParseDateFromTimeStamp(otherTask.Date, timeZone)
	taskStart_2 := otherTaskDate
	taskEnd_2 := otherTaskDate.Add(time.Duration(otherTask.Duration*60) * time.Minute)

	if (taskStart_1.After(taskStart_2) && taskStart_1.Before(taskEnd_2)) ||
		(taskStart_2.After(taskStart_1) && taskStart_2.Before(taskEnd_1)) ||
		taskStart_1.Equal(taskStart_2) {
		return true
	}
	return false
}

func CalcDistance(lat1 float64, lng1 float64, lat2 float64, lng2 float64) float64 {
	pi := float64(math.Pi)
	phi1 := lat1 * pi / 180
	phi2 := lat2 * pi / 180
	deltaLambda := (lng2 - lng1) * pi / 180
	R := 6371e3
	// gives d in metres
	d := math.Acos(math.Sin(phi1)*math.Sin(phi2)+math.Cos(phi1)*math.Cos(phi2)*math.Cos(deltaLambda)) * R
	return math.Round(d)
}

func GetStartEndOfMonth(timeZone *time.Location, t time.Time) (time.Time, time.Time) {
	timeConfig := &libNow.Config{
		TimeLocation: timeZone,
	}
	return timeConfig.With(t).BeginningOfMonth(), timeConfig.With(t).EndOfMonth()
}

/*
 * @Description: Generate user's referral code
 * @CreatedAt: 30/10/2020
 * @Author: linhnh
 * @UpdatedAt: 07/12/2020
 * @UpdatedBy: linhnh
 */
func GenerateReferralCode(name string) string {
	refferal := strings.ToLower(RemoveUnicode(name))
	if len(refferal) < 4 {
		refferal = fmt.Sprintf("%s%s", refferal, common.RandomString(4-len(refferal)))
	} else {
		regex := regexp.MustCompile(`.{4}$`) // Get last 4 characters
		refferal = regex.FindString(refferal)
	}
	return refferal
}

// =====================================
/*
 * @Description: Refactor Phone Number
 * @CreatedAt: 22/09/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb
 */
func RefactorPhoneNumber(phone string, countryCode string) string {
	if (countryCode == globalConstant.COUNTRY_CODE_TH || countryCode == globalConstant.COUNTRY_CODE_VN || countryCode == globalConstant.COUNTRY_CODE_INDO || countryCode == globalConstant.COUNTRY_CODE_MY) && strings.HasPrefix(phone, "0") {
		phone = phone[1:]
	}
	return fmt.Sprintf("%s%s", countryCode, phone)
}

/*
  - @Description: Get cost of task
  - Input:
    task: task need get cost. task{cost, detailDeepCleaning, acceptedTasker, costDetail, newCostDetail}
    taskerId: id to check tasker is leader or not (only for DEEP_CLEANING task)
  - Output:
    1. taskCost: cost before promotion. To calculate taskFee if task not have costDetail.newFinalCost
    2. taskCostWithPromotion: cost after promotion. To process FATransaction of task
    3. taskNewCost: cost before promotion. To calculate taskFee if task have costDetail.newFinalCost (case prepayTask PAID but updated after. api/v2/update-booking/update-prepay-task)
  - NOTE: If task not have costDetail.newTaskCost, taskNewCost will be return 0. In this case, use taskCost instead
*/
func GetTaskCost(task *modelTask.Task, taskerId string) (float64, float64, float64, float64) {
	// Case costDetail
	taskCost := task.Cost
	taskCostWithPromotion := task.Cost
	if task.CostDetail != nil && task.CostDetail.Cost > 0 {
		taskCost = task.CostDetail.Cost
		taskCostWithPromotion = task.CostDetail.Cost
	}
	isDeepCleaningService := task.DetailDeepCleaning != nil
	isOfficeCleaningService := task.DetailOfficeCleaning != nil
	isMassageService := task.DetailMassage != nil
	isBeautyCareService := task.DetailBeautyCare != nil
	isLeader := false
	if isDeepCleaningService {
		for _, t := range task.AcceptedTasker {
			if t.TaskerId == taskerId && t.IsLeader {
				isLeader = true
				break
			}
		}
		if isLeader && task.DetailDeepCleaning.CostPerLeaderTasker != nil {
			taskCost = task.DetailDeepCleaning.CostPerLeaderTasker.Total
			taskCostWithPromotion = task.DetailDeepCleaning.CostPerLeaderTasker.Main
		} else if task.DetailDeepCleaning.CostPerTasker != nil {
			taskCost = task.DetailDeepCleaning.CostPerTasker.Total
			taskCostWithPromotion = task.DetailDeepCleaning.CostPerTasker.Main
		}
	} else if isOfficeCleaningService {
		for _, t := range task.AcceptedTasker {
			if t.TaskerId == taskerId && t.IsLeader {
				isLeader = true
				break
			}
		}
		if isLeader && task.DetailOfficeCleaning.CostPerLeaderTasker != nil {
			taskCost = task.DetailOfficeCleaning.CostPerLeaderTasker.Total
			taskCostWithPromotion = task.DetailOfficeCleaning.CostPerLeaderTasker.Main
		} else if task.DetailOfficeCleaning.CostPerTasker != nil {
			taskCost = task.DetailOfficeCleaning.CostPerTasker.Total
			taskCostWithPromotion = task.DetailOfficeCleaning.CostPerTasker.Main
		}
	} else if isMassageService {
		taskCost = 0
		taskCostWithPromotion = 0
		for _, v := range task.DetailMassage.Packages {
			if v.TaskerId == taskerId && v.CostDetail != nil {
				taskCost += v.CostDetail.Cost
				taskCostWithPromotion += v.CostDetail.FinalCost
			}
		}
	} else if isBeautyCareService {
		taskCost = 0
		taskCostWithPromotion = 0
		for _, v := range task.DetailBeautyCare.Packages {
			if v.TaskerId == taskerId && v.CostDetail != nil {
				taskCost += v.CostDetail.Cost
				taskCostWithPromotion += v.CostDetail.FinalCost
			}
		}
	} else if task.DetailMakeup != nil {
		taskCost, taskCostWithPromotion = getTaskCostBeautyGroup(task.DetailMakeup.Packages, taskerId)
	} else if task.DetailNail != nil {
		taskCost, taskCostWithPromotion = getTaskCostBeautyGroup(task.DetailNail.Packages, taskerId)
	} else if task.DetailHairStyling != nil {
		taskCost, taskCostWithPromotion = getTaskCostBeautyGroup(task.DetailHairStyling.Packages, taskerId)
	} else if task.CostDetail != nil && task.CostDetail.FinalCost < taskCost {
		if task.CostDetail.FinalCost > 0 {
			taskCostWithPromotion = task.CostDetail.FinalCost
		} else if task.CostDetail.DecreasedReasons != nil && len(task.CostDetail.DecreasedReasons) > 0 { // Nếu final code = 0, kiểm tra đây có phải công việc khuyến mãi 100% hay không.
			for _, v := range task.CostDetail.DecreasedReasons {
				if v.Key == "PROMOTION_CODE" || v.Key == "PROMOTION_PAYMENT_METHOD" {
					taskCostWithPromotion = taskCostWithPromotion - v.Value
					if taskCostWithPromotion < 0 {
						taskCostWithPromotion = 0
					}
					break
				}
			}
		}
	}
	if task.SubscriptionId != "" && task.CostDetail != nil && len(task.CostDetail.DecreasedReasons) > 0 {
		for _, v := range task.CostDetail.DecreasedReasons {
			if v.Key == "SESSION_DISCOUNT" {
				if v.PromotionBy == globalConstant.PROMOTION_BY_BTASKEE {
					taskCost = taskCost + v.Value
					break
				}
			}
		}
	}
	// Case promotionBy:TASKER
	if task.CostDetail != nil && task.CostDetail.PromotionBy == globalConstant.PROMOTION_BY_TASKER {
		taskCost = taskCostWithPromotion
	}

	// Case newTaskCost. Task prepaytask updated
	newTaskCost, newTaskCostWithPromotion := getNewTaskCost(task, isLeader, taskerId)
	return taskCost, taskCostWithPromotion, newTaskCost, newTaskCostWithPromotion
}

func getNewTaskCost(task *modelTask.Task, isLeader bool, taskerId string) (float64, float64) {
	// Case task prepay not update
	if task.CostDetail == nil || (task.CostDetail != nil && task.CostDetail.NewFinalCost == 0) {
		return 0, 0
	}

	taskCost := 0.0
	taskCostWithPromotion := 0.0
	isDeepCleaningService := task.DetailDeepCleaning != nil
	isOfficeCleaningService := task.DetailOfficeCleaning != nil
	isMassageService := task.DetailMassage != nil
	isBeautyCareService := task.DetailBeautyCare != nil
	if isDeepCleaningService {
		if isLeader && task.DetailDeepCleaning.NewCostPerLeaderTasker != nil {
			taskCost = task.DetailDeepCleaning.NewCostPerLeaderTasker.Total
			taskCostWithPromotion = task.DetailDeepCleaning.NewCostPerLeaderTasker.Main
		} else if task.DetailDeepCleaning.NewCostPerTasker != nil {
			taskCost = task.DetailDeepCleaning.NewCostPerTasker.Total
			taskCostWithPromotion = task.DetailDeepCleaning.NewCostPerTasker.Main
		}
	} else if isOfficeCleaningService {
		if isLeader && task.DetailOfficeCleaning.NewCostPerLeaderTasker != nil {
			taskCost = task.DetailOfficeCleaning.NewCostPerLeaderTasker.Total
			taskCostWithPromotion = task.DetailOfficeCleaning.NewCostPerLeaderTasker.Main
		} else if task.DetailOfficeCleaning.NewCostPerTasker != nil {
			taskCost = task.DetailOfficeCleaning.NewCostPerTasker.Total
			taskCostWithPromotion = task.DetailOfficeCleaning.NewCostPerTasker.Main
		}
	} else if isMassageService {
		taskCost = 0
		taskCostWithPromotion = 0
		for _, v := range task.DetailMassage.Packages {
			if v.TaskerId == taskerId {
				if v.NewCostDetail != nil {
					taskCost += v.NewCostDetail.Cost
					taskCostWithPromotion += v.NewCostDetail.FinalCost
				} else if v.CostDetail != nil {
					// khi update 1 package thi package khong duoc update se khong co NewFinalCost
					if v.CostDetail.NewFinalCost > 0 {
						taskCost += v.CostDetail.NewFinalCost
						taskCostWithPromotion += v.CostDetail.NewFinalCost
					} else {
						taskCost += v.CostDetail.Cost
						taskCostWithPromotion += v.CostDetail.FinalCost
					}
				}
			}
		}
	} else if isBeautyCareService {
		taskCost = 0
		taskCostWithPromotion = 0
		for _, v := range task.DetailBeautyCare.Packages {
			if v.TaskerId == taskerId {
				if v.NewCostDetail != nil {
					taskCost += v.NewCostDetail.Cost
					taskCostWithPromotion += v.NewCostDetail.FinalCost
				} else if v.CostDetail != nil {
					// khi update 1 package thi package khong duoc update se khong co NewFinalCost
					if v.CostDetail.NewFinalCost > 0 {
						taskCost += v.CostDetail.NewFinalCost
						taskCostWithPromotion += v.CostDetail.NewFinalCost
					} else {
						taskCost += v.CostDetail.Cost
						taskCostWithPromotion += v.CostDetail.FinalCost
					}
				}
			}
		}
	} else if task.DetailMakeup != nil {
		taskCost, taskCostWithPromotion = getNewTaskCostBeautyGroup(task.DetailMakeup.Packages, taskerId)
	} else if task.DetailNail != nil {
		taskCost, taskCostWithPromotion = getNewTaskCostBeautyGroup(task.DetailNail.Packages, taskerId)
	} else if task.DetailHairStyling != nil {
		taskCost, taskCostWithPromotion = getNewTaskCostBeautyGroup(task.DetailHairStyling.Packages, taskerId)
	} else {
		// Case not deep cleaning
		if task.NewCostDetail != nil {
			taskCost = task.NewCostDetail.Cost
			taskCostWithPromotion = task.NewCostDetail.FinalCost
		} else {
			taskCost = task.CostDetail.NewFinalCost
			taskCostWithPromotion = taskCost
		}
	}

	if task.CostDetail != nil && task.CostDetail.PromotionBy == globalConstant.PROMOTION_BY_TASKER {
		taskCost = taskCostWithPromotion
	}

	return taskCost, taskCostWithPromotion
}

func getTaskCostBeautyGroup(packages []*modelTask.TaskDetailBeautyGroupPackage, taskerId string) (taskCost, taskCostWithPromotion float64) {
	for _, v := range packages {
		if v.TaskerId == taskerId && v.CostDetail != nil {
			taskCost += v.CostDetail.Cost
			taskCostWithPromotion += v.CostDetail.FinalCost
		}
	}
	return
}

func getNewTaskCostBeautyGroup(packages []*modelTask.TaskDetailBeautyGroupPackage, taskerId string) (taskCost, taskCostWithPromotion float64) {
	for _, v := range packages {
		if v.TaskerId == taskerId {
			if v.NewCostDetail != nil {
				taskCost += v.NewCostDetail.Cost
				taskCostWithPromotion += v.NewCostDetail.FinalCost
			} else if v.CostDetail != nil {
				// khi update 1 package thi package khong duoc update se khong co NewFinalCost
				if v.CostDetail.NewFinalCost > 0 {
					taskCost += v.CostDetail.NewFinalCost
					taskCostWithPromotion += v.CostDetail.NewFinalCost
				} else {
					taskCost += v.CostDetail.Cost
					taskCostWithPromotion += v.CostDetail.FinalCost
				}
			}
		}
	}
	return
}

/*
  - @Description: Get cost of task
  - Input:
    task: task need get cost. task{cost, detailDeepCleaning, acceptedTasker, costDetail, newCostDetail}
    taskerId: id to check tasker is leader or not (only for DEEP_CLEANING task)
  - Output:
    1. taskCost: cost before promotion. To calculate taskFee if task not have costDetail.newFinalCost
    2. taskCostWithPromotion: cost after promotion. To process FATransaction of task
    3. taskNewCost: cost before promotion. To calculate taskFee if task have costDetail.newFinalCost (case prepayTask PAID but updated after. api/v2/update-booking/update-prepay-task)
  - NOTE: If task not have costDetail.newTaskCost, taskNewCost will be return 0. In this case, use taskCost instead
*/
func GetTaskCostV2(task *modelTask.Task, taskerId string) (float64, float64, float64, float64) {
	taskCost, taskCostWithPromotion, newTaskCost, newTaskCostWithPromotion := GetTaskCost(task, taskerId)

	// Case tasker has extra money.
	if len(task.IncreaseDurationData) > 0 {
		// Gán lại để xử lý phần tiền extraMoney bên dưới
		// Để xử lý trường hợp
		// - Prepay task đã thanh toán
		// - Chỉ có 1/2 tasker đồng ý làm việc tiếp
		// -> DetailDeepCleaning sẽ không có các field newCostPerLeaderTasker, newCostPerTasker như bình thường nữa
		// Trường hợp trả trước đã được cập nhật (Có newCostDetail)
		// Nhưng newTaskCost, newTaskCostWithPromotion không lấy được ở trên (Vì mình không cập nhật lại detailDeepCleaning nếu chỉ có 1 số T nhận việc tiếp)
		if task.NewCostDetail != nil && newTaskCost == 0 && newTaskCostWithPromotion == 0 {
			newTaskCost, newTaskCostWithPromotion = taskCost, taskCostWithPromotion
		}

		for _, data := range task.IncreaseDurationData {
			for _, tasker := range data.TaskerMoney {
				if tasker.TaskerId == taskerId {
					// Trường hợp payment method là CASH thì sẽ cộng thêm extraMoney vào taskCost để tính phí. Vì sẽ không xuất hiện newCostDetail nếu là CASH
					if task.Payment != nil && task.Payment.Method == globalConstant.PAYMENT_METHOD_CASH {
						taskCost += tasker.ExtraMoney
						taskCostWithPromotion += tasker.ExtraMoney
					} else {
						taskCost += tasker.ExtraMain
						taskCostWithPromotion += tasker.ExtraMain
						newTaskCost += tasker.ExtraMoney
						newTaskCostWithPromotion += tasker.ExtraMoney
					}
					break
				}
			}
		}
	}

	return taskCost, taskCostWithPromotion, newTaskCost, newTaskCostWithPromotion
}

func GetVMGIp(ipstackUrl, ipstackAccessKey string) (string, error) {
	resp, err := http.DefaultClient.Get(fmt.Sprintf("%s/brandsms.vn?access_key=%s&output=json", ipstackUrl, ipstackAccessKey))
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	result := map[string]map[string]string{}
	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	err = json.Unmarshal(responseData, &result)
	if err != nil {
		return "", err
	}

	if result != nil && result["data"] != nil && result["data"]["ip"] != "" {
		return result["data"]["ip"], nil
	}
	return "", fmt.Errorf("cannot get data.ip from response: %s", string(responseData))
}

func LocalizePaymentMethod(paymentMethod string, lang string) string {
	if lang == "" {
		lang = globalConstant.LANG_EN
	}
	localize := paymentMethod
	switch paymentMethod {
	case globalConstant.PAYMENT_METHOD_PROMOTION:
		localize = localization.T(lang, "PROMOTION_ACCOUNT")

	case globalConstant.PAYMENT_METHOD_CASH:
		localize = localization.T(lang, "CASH")

	case globalConstant.PAYMENT_METHOD_CREDIT:
		localize = "bPay"

	case globalConstant.PAYMENT_METHOD_BANK_TRANSFER:
		localize = "SUBSCRIPTION"

	case globalConstant.PAYMENT_METHOD_DIRECT_TRANSFER:
		localize = "SUBSCRIPTION"

	case globalConstant.PAYMENT_METHOD_MOMO:
		localize = "Momo"

	case globalConstant.PAYMENT_METHOD_ZALO_PAY:
		localize = "Zalopay"

	case globalConstant.PAYMENT_METHOD_GRAB_PAY_BY_MOCA:
		localize = "GRAB PAY"

	case globalConstant.PAYMENT_METHOD_PROMPT_PAY:
		localize = "Prompt Pay"

	case globalConstant.PAYMENT_METHOD_TRUE_MONEY:
		localize = "True Money"

	case globalConstant.PAYMENT_METHOD_SHOPEE_PAY:
		localize = "ShopeePay"

	case globalConstant.PAYMENT_METHOD_VN_PAY:
		localize = "VNPAY"

	case globalConstant.PAYMENT_METHOD_CARD:
		localize = localization.T(lang, "BOOKING_PAYMENT_METHOD_CARD_2")

	case globalConstant.PAYMENT_METHOD_GO_PAY:
		localize = "GoPay"

	case globalConstant.PAYMENT_METHOD_QRIS:
		localize = "QRIS"

	case globalConstant.PAYMENT_METHOD_DANA:
		localize = "DANA"

	case globalConstant.PAYMENT_METHOD_VIET_QR:
		localize = "VietQR"

	case globalConstant.PAYMENT_METHOD_KREDIVO:
		localize = "Kredivo"

	case globalConstant.PAYMENT_METHOD_VIRTUAL_ACCOUNT:
		localize = localization.T(lang, "VIRTUAL_ACCOUNT")
	}

	return localize
}

func GetVTCPaymentStatus(status, language string) string {
	switch status {
	case "7":
		return localization.T(language, "VTC_PAYMENT_RESULT_REVIEW")
	case "-9":
		return localization.T(language, "VTC_PAYMENT_RESULT_ERROR_9")
	case "-3":
		return localization.T(language, "VTC_PAYMENT_RESULT_ERROR_3")
	case "-4":
		return localization.T(language, "VTC_PAYMENT_RESULT_ERROR_4")
	case "-5":
		return localization.T(language, "VTC_PAYMENT_RESULT_ERROR_5")
	case "-7":
		return localization.T(language, "VTC_PAYMENT_RESULT_ERROR_7")
	case "-8":
		return localization.T(language, "VTC_PAYMENT_RESULT_ERROR_8")
	case "-22":
		return localization.T(language, "VTC_PAYMENT_RESULT_ERROR_22")
	case "-24":
		return localization.T(language, "VTC_PAYMENT_RESULT_ERROR_24")
	case "-1":
		// Other cases assume as pay fail.
	}
	return localization.T(language, "VTC_PAYMENT_RESULT_ERROR_1")
}

func GetTaskFee(taskCost float64, task *modelTask.Task, service *modelService.Service, isLeader bool) float64 {
	taskRate := getTaskRate(task, service, isLeader)
	excludeCost := getExcludeCost(task, service.Addons)
	if IsGroceryAssistantServiceByKeyName(task.ServiceName) && task.CostDetail != nil && task.CostDetail.DepositMoney > 0 {
		taskCost -= task.CostDetail.DepositMoney
	}
	var round float64 = 100
	if task.IsoCode == globalConstant.ISO_CODE_TH {
		round = 1
	}
	return math.Ceil(((taskCost-excludeCost)*taskRate)/round) * round
}

func getTaskRate(task *modelTask.Task, service *modelService.Service, isLeader bool) float64 {
	taskRate := globalConstant.TASK_RATE
	if task.IsoCode == globalConstant.ISO_CODE_TH {
		taskRate = globalConstant.TASK_RATE_TH
	}
	if task.TaskPlace != nil && task.TaskPlace.City != "" && service.City != nil && len(service.City) > 0 {
		for _, v := range service.City {
			if v.Name == task.TaskPlace.City && v.TaskRate > 0 {
				taskRate = v.TaskRate
				break
			}
		}
	}

	if isLeader {
		if service.ServiceFeeLeaderTasker > 0 {
			taskRate = service.ServiceFeeLeaderTasker
		}
		if taskRate > 1 { // In case value of service.ServiceFeeLeaderTasker > 1 (exp: 20%, 30%)
			taskRate = taskRate / 100 // Make sure that fee is always < 1
		}
	}
	return taskRate
}

func getExcludeCost(task *modelTask.Task, addons []*modelService.ServiceAddons) float64 {
	var excludeCost float64
	if task.Tip > 0 {
		excludeCost = task.Tip
	}
	if task.Requirements != nil && len(task.Requirements) > 0 {
		for _, v := range task.Requirements {
			if v.Type == 3 { // Type 3 là CCDC không còn req này nữa nên loại ra do những case task cũ tạo ra bị lỗi
				continue
			}
			excludeCost += v.Cost
		}
	}
	// excule addons fee
	if len(task.Addons) > 0 && len(addons) > 0 {
		mapAddonsByName := make(map[string]*modelService.ServiceAddons)
		for _, v := range addons {
			mapAddonsByName[v.Name] = v
		}
		for _, v := range task.Addons {
			if addon, ok := mapAddonsByName[v.Name]; ok && addon.IsNotChargeTaskerFee {
				cost := addon.Cost
				if v.Cost > 0 {
					cost = v.Cost
				}
				excludeCost += cost
			}
		}
	}
	// exclude tasker fee
	excludeTaskerFee := task.GetCostDetail().GetExcludeTaskerFee()
	if task.GetNewCostDetail() != nil {
		excludeTaskerFee = task.GetNewCostDetail().GetExcludeTaskerFee()
	}
	for _, v := range excludeTaskerFee {
		excludeCost += v.Price
	}
	return excludeCost
}

func FindInt64InSlice(array []int64, value int64) int {
	index := -1
	if len(array) > 0 {
		for i, v := range array {
			if v == value {
				index = i
				break
			}
		}
	}
	return index
}

func SplitSliceByChunk(input []interface{}, chunkSize int) [][]interface{} {
	var chunks [][]interface{}
	for i := 0; i < len(input); i += chunkSize {
		end := i + chunkSize
		if end > len(input) {
			end = len(input)
		}
		chunk := input[i:end]
		chunks = append(chunks, chunk)
	}
	return chunks
}

func CompareSlice(arr1, arr2 []string) bool {
	arr1 = UniqString(arr1)
	arr2 = UniqString(arr2)
	if len(arr1) != len(arr2) {
		return false
	}
	mapSlice := make(map[string]struct{})
	for _, v := range arr1 {
		mapSlice[v] = struct{}{}
	}
	for _, v := range arr2 {
		if _, ok := mapSlice[v]; !ok {
			return false
		}
	}
	return true
}

/*
 * @Description: Calculate cost per tasker, leader in DeepCleaning task
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 23/04/2020
 * @UpdatedBy: linhnh
 */
func CalculateCostPerTaskerOfficeCleaning(task *modelTask.Task) (*modelTask.TaskDetailOfficeCleaningCost, *modelTask.TaskDetailOfficeCleaningCost) {
	var costPerTasker, costperLeaderTasker *modelTask.TaskDetailOfficeCleaningCost
	numberTasker := float64(task.DetailOfficeCleaning.NumberOfTaskers)
	if task.Promotion != nil && (task.Promotion.Code != "" || task.Promotion.PromotionPaymentMethodCode != "") {
		// Calculate cost for normal Tasker
		mainPerTasker := RoundDownMoney(task.CostDetail.FinalCost/numberTasker, task.IsoCode)
		diffCost := task.CostDetail.Cost - task.CostDetail.FinalCost
		promotionPerTasker := RoundDownMoney(diffCost/numberTasker, task.IsoCode)

		// Calculate cost for leader Tasker
		mainPerLeaderTasker := task.CostDetail.FinalCost - (mainPerTasker * (numberTasker - 1))
		promotionPerLeaderTasker := diffCost - (promotionPerTasker * (numberTasker - 1))

		if mainPerTasker < 0 {
			mainPerTasker = 0
		}
		if promotionPerTasker < 0 {
			promotionPerTasker = 0
		}

		if mainPerLeaderTasker < 0 {
			mainPerLeaderTasker = 0
		}
		if promotionPerLeaderTasker < 0 {
			promotionPerLeaderTasker = 0
		}

		costPerTasker = &modelTask.TaskDetailOfficeCleaningCost{
			Main:      mainPerTasker,
			Promotion: promotionPerTasker,
			Total:     mainPerTasker + promotionPerTasker,
		}
		costperLeaderTasker = &modelTask.TaskDetailOfficeCleaningCost{
			Main:      mainPerLeaderTasker,
			Promotion: promotionPerLeaderTasker,
			Total:     mainPerLeaderTasker + promotionPerLeaderTasker,
		}
	} else {
		// Calculate cost for normal Tasker
		mainPerTasker := RoundDownMoney(task.CostDetail.Cost/numberTasker, task.IsoCode)
		// Calculate cost for leader Tasker
		mainPerLeaderTasker := task.CostDetail.Cost - (mainPerTasker * (numberTasker - 1))
		if mainPerTasker < 0 {
			mainPerTasker = 0
		}
		if mainPerLeaderTasker < 0 {
			mainPerLeaderTasker = 0
		}

		costPerTasker = &modelTask.TaskDetailOfficeCleaningCost{
			Main:  mainPerTasker,
			Total: mainPerTasker,
		}
		costperLeaderTasker = &modelTask.TaskDetailOfficeCleaningCost{
			Main:  mainPerLeaderTasker,
			Total: mainPerLeaderTasker,
		}
	}

	return costPerTasker, costperLeaderTasker
}

func MapCurrencyTH(currency, lang string) (sign string) {
	sign = currency
	if currency == globalConstant.CURRENCY_SIGN_TH.Code && lang == globalConstant.LANG_TH {
		sign = globalConstant.CURRENCY_SIGN_TH.Sign
	}
	return
}

func IsServiceMustHaveLeader(serviceName string) bool {
	return FindStringInSlice(globalConstant.SERVICE_MUST_HAVE_LEADER, serviceName) > -1
}

func IsIsoCodeValid(isoCode string) bool {
	return FindStringInSlice(globalConstant.SUPPORT_ISO_CODE, isoCode) >= 0
}

func GetStartEndOfMonthAtTime(timeZone *time.Location, t time.Time) (time.Time, time.Time) {
	timeConfig := &libNow.Config{
		TimeLocation: timeZone,
	}
	return timeConfig.With(t).BeginningOfMonth(), timeConfig.With(t).EndOfMonth()
}

func GetOriginCurrencyByIsoCode(isoCode string) *modelSettingCountry.SettingCountryCurrency {
	if currency, ok := globalConstant.CURRENCY_SIGN[isoCode]; ok {
		return currency
	}
	return &modelSettingCountry.SettingCountryCurrency{Sign: "", Code: ""}
}

func RoundAvgRating(avgRating float64) float64 {
	// round avgRating ex: 4.85 | 4.44444 | 4.4988
	avgRating = math.Round(avgRating * 1000) // 4850 | 4444 | 4499
	avgRating = math.Floor((avgRating / 10)) // 485 | 444 | 449
	return avgRating / 100
}

// Deprecated: System not use midesk anymore
func GetMiDeskAccessToken(loginUrl, email, password, slackToken string) string {
	return ""
}

func CallCreateMiDeskTicket(createTicketUrl, miDeskToken, slackToken string, data map[string]interface{}) {
	// create a new buffer to store the request body
	reqBody, _ := json.Marshal(data)
	// Prepare request
	req, err := http.NewRequest("POST", createTicketUrl, bytes.NewBuffer(reqBody))
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", miDeskToken))
	if err != nil {
		msg := fmt.Sprintf("Lỗi Call Midesk Create Ticket body: %v - Error: %s", data, err.Error())
		PostToSlack(slackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[globalConstant.ISO_CODE_VN], "bTaskee System", msg)
		return
	}

	// Call api
	timeout := 30 * time.Second
	client := &http.Client{Timeout: timeout}
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	req = req.WithContext(ctx)
	resp, err := client.Do(req)
	if err != nil && ctx.Err() == context.DeadlineExceeded {
		msg := fmt.Sprintf("Lỗi Call Midesk Create Ticket DeadlineExceeded body: %v - Error: %s", data, err.Error())
		PostToSlack(slackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[globalConstant.ISO_CODE_VN], "bTaskee System", msg)
		return
	}
	if err != nil || resp == nil || resp.Body == nil {
		msg := fmt.Sprintf("Lỗi Call Midesk Create Ticket body: %v - Error: %s", data, err.Error())
		PostToSlack(slackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[globalConstant.ISO_CODE_VN], "bTaskee System", msg)
		return
	}

	// Read response body
	defer resp.Body.Close()
	responseData, err := io.ReadAll(resp.Body)
	if err != nil {
		msg := fmt.Sprintf("Lỗi Call Midesk Create Ticket body: %v - Error: %s", data, err.Error())
		PostToSlack(slackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[globalConstant.ISO_CODE_VN], "bTaskee System", msg)
		return
	}

	// Parse response body
	responseBody := make(map[string]interface{})
	err = json.Unmarshal(responseData, &responseBody)
	if err != nil {
		msg := fmt.Sprintf("Lỗi Call Midesk Create Ticket body: %v - Error: %s", data, err.Error())
		PostToSlack(slackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[globalConstant.ISO_CODE_VN], "bTaskee System", msg)
		return
	}
	// Check case error
	if resp.StatusCode != http.StatusOK || (responseBody != nil && responseBody["errors"] != nil) {
		err := responseBody["errors"]
		if responseBody["errors"] == nil {
			err = responseBody["message"]
		}
		msg := fmt.Sprintf("Lỗi Call Midesk Create Ticket body: %v - Error: %v", data, err)
		PostToSlack(slackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[globalConstant.ISO_CODE_VN], "bTaskee System", msg)
		return
	}
}

func Recover(isoCode, slackToken string, data ...[2]string) {
	if rc := recover(); rc != nil {
		msg := "PANIC_ERROR"
		for _, v := range data {
			msg += fmt.Sprintf("\n%s: %s", v[0], v[1])
		}
		msg += fmt.Sprintf("\n%v\n%v", rc, string(debug.Stack()))

		// Post slack
		PostToSlack(slackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[isoCode], "", msg)

		// Logger
		Logger.Error(msg)
	}
}

// If want to use ZNS to send otp, set ZALO in endpointConfig
// If want to use SMS to send otp, set SMS in endpointConfig
func SendOTP_VN(grpcEndpoint map[string]string, countryCode string, phone string, language string, otp string) (supplier string, err error) {
	defer Logger.Sync()
	if grpcEndpoint == nil {
		Logger.Error("SendOTP_VN",
			zap.Any("grpcEndpoint", grpcEndpoint),
			zap.String("error", "endpointConfig is nil"),
		)
		return "", fmt.Errorf("enpointConfig is nil")
	}
	if znsEndpoint := grpcEndpoint[globalConstant.MESSAGE_SUPPLIER_ZALO]; znsEndpoint != "" {
		err := sendOTPViaZNS_VN(znsEndpoint, countryCode, phone, language, otp)
		if err == nil {
			return globalConstant.MESSAGE_SUPPLIER_ZALO, nil
		}
		Logger.Error("SendOTP_VN",
			zap.String("function", "sendOTPViaZNS_VN"),
			zap.Any("grpcEndpoint", grpcEndpoint),
			zap.String("error", err.Error()),
		)
	}
	if smsEndpoint := grpcEndpoint[globalConstant.MESSAGE_SUPPLIER_SMS]; smsEndpoint != "" {
		err := sendOTPViaSMS_VN(smsEndpoint, countryCode, phone, language, otp)
		if err == nil {
			return globalConstant.MESSAGE_SUPPLIER_SMS, nil
		}
		Logger.Error("SendOTP_VN",
			zap.String("function", "sendOTPViaSMS_VN"),
			zap.Any("grpcEndpoint", grpcEndpoint),
			zap.String("error", err.Error()),
		)
	}

	Logger.Error("SendOTP_VN",
		zap.Any("grpcEndpoint", grpcEndpoint),
		zap.String("error", "cannot send OTP"),
	)
	return "", fmt.Errorf("cannot send OTP")
}

func sendOTPViaZNS_VN(endpoint string, countryCode string, phone string, language string, otp string) error {
	request := &modelCSOutboundRequest.CSOutboundRequest{
		CountryCode: countryCode,
		Phone:       phone,
		Language:    language,
		Otp:         otp,
	}

	client, connect, err := grpcCSOutboundVN.ConnectGRPCCSOutboundVN(endpoint)
	if err != nil {
		return fmt.Errorf("error connect grpc cs outbound vn: %v", err.Error())
	}
	defer connect.Close()
	_, err = client.SendZNSOTP(context.Background(), request)
	if err != nil {
		return fmt.Errorf("error from grpc cs outbound vn: %v", err.Error())
	}
	return nil
}

func sendOTPViaSMS_VN(endpoint string, countryCode string, phone string, language string, otp string) error {
	var err error
	client, connect, err := grpcSMSVN.ConnectGRPCSMSVN(endpoint)
	if err != nil {
		return err
	}
	defer connect.Close()

	message := localization.T(language, "ACTIVATION_CODE_LABEL", otp)
	option := &modelSMSSending.SmsRequest{
		Phone:       phone,
		CountryCode: countryCode,
		Message:     message,
	}
	_, err = client.Send(context.Background(), option)
	if err != nil {
		return err
	}
	return nil
}

// Require: task.isPrepayTask, task.payment.status, task.payment.method, task.payment.isPaymentAttempted
// Output:
/*
- task.payment.status = PAID -> return true
- task.payment.isPaymentAttempt = true -> return true
- task.isPrepayTask = false -> return true
- other case -> return false
*/
func IsPrepayTaskCanBeSendToTaskerAndAccepted(task *modelTask.Task, isoCode string) bool {
	// Cannot process this case
	if task == nil {
		return false
	}
	// Case prepay task and charged success
	if task.IsPrepayTask && task.Payment != nil && task.Payment.Status == globalConstant.TASK_PAYMENT_STATUS_PAID {
		return true
	}
	// Case prepay task with qrCode NOT success but isProcessing (task.payment.status not initial value) by tracking from app
	// NOTE: Comment những trường hợp này lại vì khi task đang là prepay task mà thanh toán chưa thành công thì không cho phép send tasker cũng như nhận việc
	// if task.IsPrepayTask && !isPrepayTaskCanBeSendToTaskerAndAcceptedByServiceName(task.IsoCode, task.ServiceName) {
	// 	return false
	// }
	// if task.IsPrepayTask && task.Payment != nil && FindStringInSlice(globalConstant.QR_CODE_PAYMENT_METHODS, task.Payment.Method) > -1 &&
	// 	task.Payment.IsPaymentAttempted && !((isoCode == globalConstant.ISO_CODE_MY || isoCode == globalConstant.ISO_CODE_INDO) && task.IsNewAsker) {
	// 	return true
	// }
	// Case not prepay task
	if !task.IsPrepayTask {
		return true
	}
	// Other cases
	return false
}

// func isPrepayTaskCanBeSendToTaskerAndAcceptedByServiceName(isoCode string, serviceName string) bool {
// 	// All country
// 	if IsMassageServiceByKeyName(serviceName) {
// 		return false
// 	}

// 	// Case check by isoCode
// 	switch isoCode {
// 	case globalConstant.ISO_CODE_TH:
// 		if IsOfficeCleaningServiceByKeyName(serviceName) {
// 			return false
// 		}
// 	}

// 	// Other cases
// 	return true
// }

func GetPromotionCodeOnlyApplyPaymentMethodErrorText(promotionPaymentMethods []string) *modelService.ServiceText {
	return &modelService.ServiceText{
		Vi: localization.T(globalConstant.LANG_VI, "PROMOTION_ONLY_APPLY_FOR_PAYMENT_METHOD", localizeListPaymentMethods(globalConstant.LANG_VI, promotionPaymentMethods)),
		En: localization.T(globalConstant.LANG_EN, "PROMOTION_ONLY_APPLY_FOR_PAYMENT_METHOD", localizeListPaymentMethods(globalConstant.LANG_EN, promotionPaymentMethods)),
		Ko: localization.T(globalConstant.LANG_KO, "PROMOTION_ONLY_APPLY_FOR_PAYMENT_METHOD", localizeListPaymentMethods(globalConstant.LANG_KO, promotionPaymentMethods)),
		Th: localization.T(globalConstant.LANG_TH, "PROMOTION_ONLY_APPLY_FOR_PAYMENT_METHOD", localizeListPaymentMethods(globalConstant.LANG_TH, promotionPaymentMethods)),
		Id: localization.T(globalConstant.LANG_ID, "PROMOTION_ONLY_APPLY_FOR_PAYMENT_METHOD", localizeListPaymentMethods(globalConstant.LANG_ID, promotionPaymentMethods)),
	}
}

func localizeListPaymentMethods(language string, methods []string) string {
	localizeMethods := []string{}
	for _, method := range methods {
		localizeMethods = append(localizeMethods, LocalizePaymentMethod(method, language))
	}
	return strings.Join(localizeMethods, ", ")
}

func GetTimezoneByCity(city string, settingCountry *modelSettingCountry.SettingCountry) (string, *time.Location, error) {
	var timezone string
	if settingCountry != nil && len(settingCountry.City) > 0 {
		for _, v := range settingCountry.City {
			if v.Name == city && v.Timezone != "" {
				timezone = v.Timezone
			}
		}
	}
	location, err := time.LoadLocation(timezone)
	return timezone, location, err
}

func SetNewDateWithLocation(date time.Time, locationTime *time.Location) time.Time {
	return time.Date(date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute(), date.Second(), date.Nanosecond(), locationTime)
}

func GetLocationByTimezone(timezone string) *time.Location {
	if timezone == "" {
		return GetTimeZone()
	}
	locationTime, err := time.LoadLocation(timezone)
	if err != nil || locationTime == nil {
		return GetTimeZone()
	}
	return locationTime
}

func GenerateReferralCodeForTHandID(name string) string {
	refferal := strings.ReplaceAll(strings.ToLower(RemoveUnicode(name)), " ", "")
	if len(refferal) < 4 {
		refferal = fmt.Sprintf("%s%s", refferal, common.RandomString(4-len(refferal)))
	} else {
		regex := regexp.MustCompile(`^.{4}`) // Get last 4 characters
		refferal = regex.FindString(refferal)
	}
	return refferal
}

/*
//Random without letters I, O, L and digit 0
*/
func RandomStringWithout0OIL(n int) string {
	var letter = []rune("abcdefghjkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ123456789")
	result := make([]rune, n)
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := range result {
		result[i] = letter[r.Intn(len(letter))]
	}
	return string(result)
}

func GetTaskDurationByTasker(task *modelTask.Task, taskerId string) float64 {
	duration := task.Duration
	if taskerId != "" && (task.GetDetailMassage() != nil && task.GetDetailMassage().GetType() == globalConstant.TASK_MASSAGE_TYPE_SIMULTANEOUSLY) {
		for _, v := range task.GetDetailMassage().GetPackages() {
			if v.TaskerId == taskerId {
				if v.GetNewCostDetail() != nil {
					duration = v.GetNewCostDetail().GetDuration()
				} else {
					duration = v.GetCostDetail().GetDuration()
				}
			}
		}
	}
	// Beauty care task with multiple tasker
	if task.GetDetailBeautyCare() != nil && task.GetDetailBeautyCare().GetType() == globalConstant.TASK_MASSAGE_TYPE_SIMULTANEOUSLY {
		for _, v := range task.GetDetailBeautyCare().GetPackages() {
			if v.TaskerId == taskerId {
				if v.GetNewCostDetail() != nil {
					duration = v.GetNewCostDetail().GetDuration()
				} else {
					duration = v.GetCostDetail().GetDuration()
				}
			}
		}
	}
	// Makeup, nail, hair styling task with multiple tasker
	if task.GetDetailMakeup() != nil && task.GetDetailMakeup().Type == globalConstant.TASK_MASSAGE_TYPE_SIMULTANEOUSLY {
		for _, v := range task.GetDetailMakeup().GetPackages() {
			if v.TaskerId == taskerId {
				if v.GetNewCostDetail() != nil {
					duration = v.GetNewCostDetail().GetDuration()
				} else {
					duration = v.GetCostDetail().GetDuration()
				}
			}
		}
	} else if task.GetDetailNail() != nil && task.GetDetailNail().Type == globalConstant.TASK_MASSAGE_TYPE_SIMULTANEOUSLY {
		for _, v := range task.GetDetailNail().GetPackages() {
			if v.TaskerId == taskerId {
				if v.GetNewCostDetail() != nil {
					duration = v.GetNewCostDetail().GetDuration()
				} else {
					duration = v.GetCostDetail().GetDuration()
				}
			}
		}
	} else if task.GetDetailHairStyling() != nil && task.GetDetailHairStyling().Type == globalConstant.TASK_MASSAGE_TYPE_SIMULTANEOUSLY {
		for _, v := range task.GetDetailHairStyling().GetPackages() {
			if v.TaskerId == taskerId {
				if v.GetNewCostDetail() != nil {
					duration = v.GetNewCostDetail().GetDuration()
				} else {
					duration = v.GetCostDetail().GetDuration()
				}
			}
		}
	}
	duration -= getIncreaseDurationNotForTasker(task.GetIncreaseDurationData(), taskerId)
	return duration
}

func getIncreaseDurationNotForTasker(taskIncreaseDurationData []*modelTask.TaskIncreaseDurationData, taskerId string) float64 {
	var totalDuration float64
	for _, data := range taskIncreaseDurationData {
		isExists := false
		for _, v := range data.GetTaskerMoney() {
			if v.GetTaskerId() == taskerId {
				isExists = true
				break
			}
		}
		if !isExists {
			totalDuration += data.IncreaseDuration
		}
	}
	return totalDuration
}

// taskerId: tasker._id or company._id
func GetTaskerHoldingTaskFee(isoCode, taskerId string) (float64, int, int, error) {
	var holdingAmount float64

	// 1. Get all task
	fields := bson.M{
		"_id":                                    1,
		"serviceId":                              1,
		"serviceName":                            1,
		"subscriptionId":                         1,
		"cost":                                   1,
		"acceptedTasker.isLeader":                1,
		"acceptedTasker.taskerId":                1,
		"detailDeepCleaning.costPerLeaderTasker": 1,
		"detailDeepCleaning.costPerTasker":       1,
		"detailDeepCleaning.newCostPerLeaderTasker":   1,
		"detailDeepCleaning.newCostPerTasker":         1,
		"costDetail.newFinalCost":                     1,
		"costDetail.depositMoney":                     1,
		"isoCode":                                     1,
		"serviceText":                                 1,
		"tip":                                         1,
		"requirements.cost":                           1,
		"createdAt":                                   1,
		"taskPlace":                                   1,
		"costDetail.cost":                             1,
		"costDetail.promotionBy":                      1,
		"costDetail.finalCost":                        1,
		"newCostDetail.finalCost":                     1,
		"newCostDetail.cost":                          1,
		"detailOfficeCleaning.costPerLeaderTasker":    1,
		"detailOfficeCleaning.costPerTasker":          1,
		"detailOfficeCleaning.newCostPerLeaderTasker": 1,
		"detailOfficeCleaning.newCostPerTasker":       1,
		"increaseDurationData":                        1,
		"payment.method":                              1,
	}
	query := bson.M{
		"status": bson.M{"$in": []string{globalConstant.TASK_STATUS_CONFIRMED, globalConstant.TASK_STATUS_WAITING}},
		"$or": []bson.M{
			{"acceptedTasker.taskerId": taskerId},
			{"acceptedTasker.companyId": taskerId},
		},
	}
	var tasks []*modelTask.Task
	err := globalDataAccessV2.New(isoCode).GetAllByQuery(globalCollection.COLLECTION_TASK[isoCode], query, fields, &tasks)
	if err != nil {
		return 0, 0, 0, err
	}

	// 2. Get all service of these tasks
	serviceIds := []string{}
	for _, task := range tasks {
		serviceIds = append(serviceIds, task.ServiceId)
	}
	var services []*modelService.Service
	err = globalDataAccessV2.New(isoCode).GetAllByQuery(globalCollection.COLLECTION_SERVICE[isoCode], bson.M{"_id": bson.M{"$in": serviceIds}}, bson.M{"city": 1, "serviceFeeLeaderTasker": 1}, &services)
	if err != nil {
		return 0, 0, 0, err
	}

	mapServiceById := make(map[string]*modelService.Service)
	for _, s := range services {
		mapServiceById[s.XId] = s
	}

	// Number of accepted tasks (both tasks has taskFee and not)
	numberOfAcceptedTasks := len(tasks)
	numberTasksHaveHoldingAmount := 0
	// 3. Sum task fee of all task
	for _, task := range tasks {
		// Only check holding amount for cash task
		if task.GetPayment().GetMethod() != globalConstant.PAYMENT_METHOD_CASH {
			continue
		}

		var isLeader bool
		for _, acceptedTasker := range task.AcceptedTasker {
			if acceptedTasker.TaskerId == taskerId {
				isLeader = acceptedTasker.IsLeader
				break
			}
		}

		taskCost, _, newTaskCost, _ := GetTaskCostV2(task, taskerId)
		if newTaskCost != 0 {
			taskCost = newTaskCost
		}
		taskFee := GetTaskFee(taskCost, task, mapServiceById[task.ServiceId], isLeader)
		holdingAmount += taskFee
		numberTasksHaveHoldingAmount++
	}
	return holdingAmount, numberOfAcceptedTasks, numberTasksHaveHoldingAmount, nil
}

func RemoveStringDuplicate(slice []string) []string {
	// Create a map to store unique elements
	seen := make(map[string]bool)
	result := []string{}

	// Loop through the slice, adding elements to the map if they haven't been seen before
	for _, val := range slice {
		if _, ok := seen[val]; !ok {
			seen[val] = true
			result = append(result, val)
		}
	}
	return result
}

func NotiForFavChat(req FavChatNotiRequest) *FavChatNotiResponse {
	// Send notification to received user
	var arrayNotification []interface{}
	notify := &modelNotification.Notification{
		XId:         GenerateObjectId(),
		ChatId:      req.ChatId,
		UserId:      req.UserId,
		Type:        28,
		Description: localization.T(req.UserLanguage, "CHAT_NEW_MESSAGE"),
		NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_CHAT,
		CreatedAt:   GetCurrentTimestamp(req.TimeZone),
	}
	arrayNotification = append(arrayNotification, notify)
	title := localization.GetLocalizeObject("DIALOG_TITLE_INFORMATION")
	body := localization.GetLocalizeObject("CHAT_NEW_MESSAGE")
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       28,
		ChatId:     req.ChatId,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_CHAT,
	}
	userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{{UserId: req.UserId, Language: req.UserLanguage}}
	return &FavChatNotiResponse{
		ArrayNotification: arrayNotification,
		Title:             title,
		Body:              body,
		Payload:           payload,
		UserIds:           userIds,
	}
}

func CalculateHoursExcludingMidnight(startDate time.Time, endDate time.Time) float64 {
	startOfNextDay := StartADay(startDate).AddDate(0, 0, 1)

	startOfMidnight := startOfNextDay.Add(-1 * time.Hour)
	endOfMidnight := startOfNextDay.Add(6 * time.Hour)
	if endDate.Before(startOfMidnight) {
		return endDate.Sub(startDate).Hours()
	}
	if endDate.After(startOfMidnight) && endDate.Before(endOfMidnight) {
		return startOfMidnight.Sub(startDate).Hours()
	}
	return startOfMidnight.Sub(startDate).Hours() + endDate.Sub(endOfMidnight).Hours()
}

func IsLeaderTasker(acceptedTaskers []*modelTask.TaskAcceptedTasker, userId string) bool {
	for _, acceptedTasker := range acceptedTaskers {
		if acceptedTasker.TaskerId == userId {
			return acceptedTasker.IsLeader
		}
	}
	return false
}

type InitChangeHistoryParams struct {
	Key        string                 `bson:"key"`        // required
	ActionFrom string                 `bson:"actionFrom"` // required default APP
	UserId     string                 `bson:"userId"`     // required
	DataChange map[string]interface{} `bson:"dataChange"`
	Username   string                 `bson:"username"` // optional
}

func InitChangeHistory(params InitChangeHistoryParams) (changeHistory bson.M) {
	changeHistory = bson.M{
		"_id":        GenerateObjectId(),
		"key":        params.Key,
		"actionFrom": params.ActionFrom,
		"userId":     params.UserId,
		"dataChange": params.DataChange,
		"createdAt":  GetCurrentTime(GetTimeZone()),
	}

	if changeHistory["actionFrom"] == "" {
		changeHistory["actionFrom"] = "APP"
	}
	if params.Username != "" {
		changeHistory["username"] = params.Username
	}

	return
}

// StartGoroutineFunc is a function that starts a goroutine if the application mode is not test
func StartGoroutineFunc(fn func()) {
	switch os.Getenv("APPLICATION_MODE") {
	case "test":
		fn()
	default:
		go func() {
			fn()
		}()
	}
}

// Require fields: booking: {schedule, startDate, endDate}
// Return: None
// Description: Refactor schedule from request to booking, remove duplicate schedule and sort by time asc. Update directly to booking
func RefactorSubscriptionScheduleFromRequest(subStartDate, subEndDate *timestamppb.Timestamp, subSchedule []*timestamppb.Timestamp) (*timestamppb.Timestamp, *timestamppb.Timestamp, []*timestamppb.Timestamp) {
	newStartDate := subStartDate
	newEndDate := subEndDate
	newSchedule := make([]*timestamppb.Timestamp, 0)

	// Remove duplicate schedule
	scheduleTimeExists := make(map[int64]bool)
	for _, v := range subSchedule {
		if !scheduleTimeExists[v.Seconds] {
			newSchedule = append(newSchedule, v)
		}
		scheduleTimeExists[v.Seconds] = true
	}

	// Sort schedule by time asc
	// This time zone to convert time to sort only
	timeZone := GetTimeZone()
	sort.Slice(newSchedule, func(i, j int) bool {
		timeI := ParseDateFromTimeStamp(newSchedule[i], timeZone)
		timeJ := ParseDateFromTimeStamp(newSchedule[j], timeZone)
		return timeI.Before(timeJ)
	})
	if subStartDate == nil || subStartDate.Seconds > newSchedule[0].Seconds {
		newStartDate = newSchedule[0]
	}
	if subEndDate == nil || subEndDate.Seconds < newSchedule[len(newSchedule)-1].Seconds {
		newEndDate = newSchedule[len(newSchedule)-1]
	}

	return newStartDate, newEndDate, newSchedule
}
