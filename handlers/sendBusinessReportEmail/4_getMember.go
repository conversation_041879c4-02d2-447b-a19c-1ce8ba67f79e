package sendBusinessReportEmail

import (
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type MemberInfo struct {
	Name      string
	Phone     string
	LevelName string
	CreatedAt *timestamppb.Timestamp
}

func getMembers(language, businessId string) (mapMemberDetail map[string]*MemberInfo, activeMembers []*MemberInfo) {
	// get business user info
	fields := bson.M{"userId": 1, "levelId": 1, "createdAt": 1, "status": 1}
	query := bson.M{"businessId": businessId}
	var members []*businessMember.BusinessMember
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], query, fields, bson.M{"createdAt": -1}, &members)

	memberUserIds := []string{}
	levelIds := []string{}
	for _, member := range members {
		memberUserIds = append(memberUserIds, member.UserId)
		if member.LevelId != "" {
			levelIds = append(levelIds, member.LevelId)
		}
	}
	levelIds = globalLib.UniqString(levelIds)

	// get origin user info: name, phone
	memberUserInfos, _ := modelUser.GetAll(local.ISO_CODE, bson.M{"_id": bson.M{"$in": memberUserIds}}, bson.M{"name": 1, "phone": 1})
	mapUserById := map[string]*modelUser.Users{}
	for _, user := range memberUserInfos {
		mapUserById[user.XId] = user
	}

	// get level name
	levelInfos := []*businessLevel.BusinessLevel{}
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], bson.M{"_id": bson.M{"$in": levelIds}}, bson.M{"name": 1}, &levelInfos)
	mapLevelById := map[string]*businessLevel.BusinessLevel{}
	for _, level := range levelInfos {
		mapLevelById[level.XId] = level
	}

	// map result
	mapMemberDetail = map[string]*MemberInfo{}
	activeMembers = []*MemberInfo{}
	for _, member := range members {
		var levelName string
		if level, ok := mapLevelById[member.LevelId]; ok {
			levelName = level.Name
		} else {
			levelName = localization.T(language, "BUSINESS_LEVEL_EMPTY")
		}
		memberUser := mapUserById[member.UserId]
		memberDetail := &MemberInfo{
			Name:      memberUser.Name,
			Phone:     memberUser.Phone,
			LevelName: levelName,
			CreatedAt: member.CreatedAt,
		}
		mapMemberDetail[member.XId] = memberDetail
		if member.Status == globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE {
			activeMembers = append(activeMembers, memberDetail)
		}
	}
	return mapMemberDetail, activeMembers
}
