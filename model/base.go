/*
 * @File: base.go
 * @Description: Define model in service
 * @CreatedAt: 10/12/2020
 * @Author: linhnh
 */
package model

type InputEmail struct {
	Subject string   `json:"subject,omitempty"`
	From    string   `json:"from,omitempty"`
	To      string   `json:"to,omitempty"`
	Cc      []string `json:"cc,omitempty"`
	Bcc     []string `json:"bcc,omitempty"`
	Content string   `json:"content,omitempty"`
}

type EmailReceiptData struct {
	IsAirConditioner bool
	IsHousekeeping   bool

	Hello                        string
	Thanks                       string
	MoneyTotal                   string
	DateAndService               string
	TaskDetailText               string
	TaskerLabel                  string
	TaskerAvatar                 string
	TaskerName                   string
	TaskPlace                    string
	TaskTime                     string
	TaskName                     string
	TaskDuration                 string
	DetailAC                     []*EmailReceiptDetailAC
	HousekeepingDetailText       string
	DetailHousekeeping           []*EmailReceiptDetailHousekeeping
	HousekeepingSummary          string
	PaymentDetailText            string
	PaymentTaskPriceText         string
	TaskBasePrice                string
	IsAutoChooseTasker           bool
	PaymentChooseTaskerPriceText string
	CostForChooseTasker          string
	ToolsRequirementCost         string
	PaymentToolsRequirementText  string
	TaskTip                      string
	TaskTipText                  string
	PromotionDiscount            string
	PromotionDiscountText        string
	PaymentTotalText             string
	PaymentTotal                 string
	PaymentPaidText              string
	PaymentMethod                string
	PromotionAccountText         string
	CashText                     string
	ReferralText                 string
	Referral                     string
	InviteDescription            string
	VerifyEmailContent           string
	Currency                     string
	Language                     string
	AskerName                    string
	TaskDurationCount            int64
	TotalRooms                   int
	TotalArea                    float64
	ReferralValue                string
	Bpoint                       string
	AskerNameTitle               string
	PointText                    string
	TaskTitle                    string
	TaskPlaceTitle               string
	TaskDateTitle                string
	TaskServiceTitle             string
	TaskDurationTitle            string
	PromotionTitle               string
	SupportTitle                 string
	TaskDurationAirConTitle      string
	TaskQuantityTitle            string
	LostAndFoundText             string
	PropertyDamageText           string
	OtherSupportText             string
	ReferralTitle                string
	FollowBtaskeeAt              string
	TaskHostelDetailTitle        string
	TaskHostelSummaryText        string
	PaymentChooseTaskerTitle     string
	PaymentToolsReqTitle         string
	TipsTitle                    string
	TaskDurationText             string
	PaymentMethodCard            string
	PayByCardTitle               string
	PaymentCardNumber            string
	CardPaymentStatusTitle       string
	CardPaymentStatus            string
	BankTransferTitle            string
	BankName                     string
	IsPaymentByCard              bool
	PaymentDetailTitle           string
	PaymentCardNumberTitle       string
	PaymentStatusTitle           string
	PaymentStatus                string
	IsPaymentByTransfer          bool
	PaymentBankNameTitle         string
	PaymentBankName              string
	ChargeFailContent            string
	ChargeFailNote               string
	SubscriptionIntroduce        string
	SubscriptionIntroduce1       string
	SubscriptionIntroduce2       string
	SubscriptionIntroduce3       string
	SubscriptionIntroduce4       string
	SubscriptionIntroduce5       string
	VatTitle                     string
	VatFee                       string
}

type EmailReceiptDetailAC struct {
	TaskQuantityTitle string
	TaskTitle         string
	Type              string
	Quantity          string
	Services          string
}

type EmailReceiptDetailHousekeeping struct {
	NameAndArea string
}

type EmailVerifyModel struct {
	Username   string
	VerifyLink string
}

type EmailSubsRecepitData struct {
	Name                        string
	Hello                       string
	SubsEndDate                 string
	SubsOrderId                 string
	SubsAddress                 string
	SubsName                    string
	SubsDuration                string
	SubsBeginAt                 string
	SubsDays                    string
	SubsDateFrom                string
	Referral                    string
	PromotionDiscount           string
	PaymentTotal                string
	SubsBasePrice               string
	TotalPrice                  string
	OrderNumberTitle            string
	SubsDetailTitle             string
	SubsPlaceTitle              string
	SubsServiceTitle            string
	SubsBeginAtTitle            string
	SubsDurationTitle           string
	SubsDaysTitle               string
	SubsDateFromTitle           string
	SubsPackValueTitle          string
	SubsBasePriceTitle          string
	PaymentTotalText            string
	TransCodeTitle              string
	ReferralText                string
	ReferralTitle               string
	FollowBtaskeeAt             string
	SubsTransCode               string
	AskerNameTitle              string
	AskerName                   string
	PaymentMethodTitle          string
	PaymentMethod               string
	NumberSessionTitle          string
	NumberSession               string
	SubscriptionIntroduce       string
	SubscriptionIntroduce1      string
	SubscriptionIntroduce2      string
	SubscriptionIntroduce3      string
	SubscriptionIntroduce4      string
	SubscriptionIntroduce5      string
	SubscriptionRefundNoteTitle string
	SubscriptionRefundbPayNote  string
	SubscriptionRefundMoneyNote string
	IsPaymentByCard             bool
	PaymentDetailTitle          string
	PaymentCardNumberTitle      string
	PaymentStatusTitle          string
	PaymentStatus               string
	IsPaymentByTransfer         bool
	PaymentBankNameTitle        string
	PaymentBankName             string
	PaymentCardNumber           string
	Dear                        string
	Thank                       string
}

type EmailSubsOrderData struct {
	Name                      string
	SubsInfo                  string
	Dear                      string
	Thank                     string
	AskerNameTitle            string
	TotalCostTitle            string
	PaidByTitle               string
	PaymentMethodBankTransfer string
	PaymentInfo               string
	PaymentInstructions       string
	ReferralValue             string
	AskerName                 string
	SubsEndDate               string
	SubsStartDate             string
	SubsOrderId               string
	SubsAddress               string
	SubsName                  string
	SubsDuration              string
	SubsBeginAt               string
	SubsDays                  string
	SubsDateFrom              string
	PaymentMethod             string
	Referral                  string
	BankName                  string
	BankDepartment            string
	AccountNumber             string
	AccountHolder             string
	PromotionDiscount         string
	PaymentTotal              string
	SubsBasePrice             string
	TotalPrice                string
	BankTitle                 string
	BranchTitle               string
	AccountNumberTitle        string
	AccountHolderTitle        string
	TransferContentTitle      string
	TransferContent           string
	PromotionDiscountText     string
	NoteTitle                 string
	Note1                     string
	Note2                     string
	Note3                     string
	PolicyRefundTitle         string
	PolicyRefund1             string
	PolicyRefund2             string
	PolicyRefund3             string
	PolicyRefund4             string
	PolicyRefund5             string
	SupportRefundTitle        string
	SupportRefund1            string
	SupportRefund2            string
	SupportRefund3            string
	OrderNumberTitle          string
	SubsDetailTitle           string
	SubsPlaceTitle            string
	SubsServiceTitle          string
	SubsBeginAtTitle          string
	SubsDurationTitle         string
	SubsDaysTitle             string
	SubsDateFromTitle         string
	SubsPackValueTitle        string
	SubsBasePriceTitle        string
	PaymentTotalText          string
	PromotionTitle            string
	TransCodeTitle            string
	ReferralText              string
	ReferralTitle             string
	FollowBtaskeeAt           string
	SubsTransCode             string
}

type EmailRenewSubsData struct {
	Name                      string
	Renewing                  string
	Dear                      string
	Thank                     string
	SubsInfoExpire1           string
	SubsInfoExpire1_1         string // For Korea
	SubsInfoExpire2           string
	AskerNameTitle            string
	PaidByTitle               string
	PaymentMethodBankTransfer string
	PaymentMethodCard         string
	TasksCompleted            string
	SubsDetailsTitle          string
	AskerName                 string
	SubsEndDate               string
	SubsOrderId               string
	SubsNumberTaskDoneText    string
	SubsPlace                 string
	SubsName                  string
	SubsDuration              string
	SubsBeginAt               string
	SubsDays                  string
	SubsDateFrom              string
	PaymentMethod             string
	PromotionAccountText      string
	CashText                  string
	Referral                  string
	OrderNumberTitle          string
	SubsDetailTitle           string
	SubsPlaceTitle            string
	SubsServiceTitle          string
	SubsBeginAtTitle          string
	SubsDurationTitle         string
	SubsDaysTitle             string
	SubsDateFromTitle         string
	ReferralText              string
	ReferralTitle             string
	FollowBtaskeeAt           string
	SubsTransCode             string
	SubsStartDate             string
}

type EmailTopUpSuccessData struct {
	Title                 string
	Dear                  string
	AskerName             string
	Thank                 string
	Intro1                string
	TotalTopUpTitle       string
	TotalTopUp            string
	AskerNameTitle        string
	PromotionTitle        string
	Promotion             string
	PaymentMethodTitle    string
	PaymentMethod         string
	TopUpDetailTitle      string
	ChargeValueTitle      string
	ChargeValue           string
	ReferralText          string
	ReferralTitle         string
	HasInvitation         bool
	Invitation1           string
	Invitation2           string
	ReferralCode          string
	InvitationDescription string
}
