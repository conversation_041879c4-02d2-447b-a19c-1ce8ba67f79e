package firebase

import (
	"context"
	"errors"

	firestore "cloud.google.com/go/firestore"
	firebase "firebase.google.com/go/v4"
	"google.golang.org/api/iterator"
	"google.golang.org/api/option"
)

type IFirebaseClient interface {
	AddDocument(collectionName string, data map[string]interface{}) (string, error)
	GetDocument(collectionName string) ([]map[string]interface{}, error)
	GetDocumentByCondition(collectionName string, condition ...[2]string) ([]map[string]interface{}, error)
	UpdateDocumentById(ctx context.Context, collectionName string, documentId string, data map[string]interface{}) error
}

type FirebaseClient struct {
	Client *firestore.Client
}

var (
	ctx = context.Background()
)

func NewFireBaseClient(serviceAccount string) (*FirebaseClient, error) {
	if serviceAccount == "" {
		return nil, errors.New("service account is empty")
	}

	sa := option.WithCredentialsJSON([]byte(serviceAccount))
	app, err := firebase.NewApp(ctx, nil, sa)
	if err != nil {
		return nil, err
	}

	client, err := app.Firestore(ctx)
	if err != nil {
		return nil, err
	}

	return &FirebaseClient{
		Client: client,
	}, nil
}

func (c *FirebaseClient) AddDocument(collectionName string, data map[string]interface{}) (string, error) {
	if c.Client == nil {
		return "", errors.New("init client fail")
	}
	result, _, err := c.Client.Collection(collectionName).Add(ctx, data)
	documentId := ""
	if result != nil {
		documentId = result.ID
	}
	return documentId, err
}

// Deprecated: Use AddDocument instead
// func (c *FirebaseClient) AddCollection(collectionName string, data map[string]interface{}) (string, error) {
// 	if c.Client == nil {
// 		return "", errors.New("init client fail")
// 	}
// 	result, _, err := c.Client.Collection(collectionName).Add(ctx, data)
// 	documentId := ""
// 	if result != nil {
// 		documentId = result.ID
// 	}
// 	return documentId, err
// }

func (c *FirebaseClient) GetDocument(collectionName string) ([]map[string]interface{}, error) {
	if c.Client == nil {
		return nil, errors.New("init client fail")
	}
	var result []map[string]interface{}
	iter := c.Client.Collection(collectionName).Documents(ctx)
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			return nil, err
		}

		// map more id document
		tmp := doc.Data()
		tmp["_id"] = doc.Ref.ID
		result = append(result, tmp)
	}
	return result, nil
}

// This function only support == operator for all parameters
func (c *FirebaseClient) GetDocumentByCondition(collectionName string, condition ...[2]string) ([]map[string]interface{}, error) {
	if len(condition) == 0 {
		return c.GetDocument(collectionName)
	}

	filters := []firestore.EntityFilter{}
	for _, v := range condition {
		filters = append(filters, firestore.PropertyFilter{
			Path:     v[0],
			Operator: "==",
			Value:    v[1],
		})
	}

	collection := c.Client.Collection(collectionName)
	query := collection.WhereEntity(firestore.AndFilter{
		Filters: filters,
	})

	result := make([]map[string]interface{}, 0)
	iter := query.Documents(ctx)
	for {
		doc, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if err != nil {
			return nil, err
		}

		tmp := doc.Data()
		tmp["_id"] = doc.Ref.ID
		result = append(result, tmp)
	}

	return result, nil
}

func (c *FirebaseClient) UpdateDocumentById(ctx context.Context, collectionName string, documentId string, data map[string]interface{}) error {
	if c.Client == nil {
		return errors.New("init client fail")
	}
	updates := []firestore.Update{}
	for k, v := range data {
		updates = append(updates, firestore.Update{
			Path:  k,
			Value: v,
		})
	}
	_, err := c.Client.Collection(collectionName).Doc(documentId).Update(ctx, updates)
	return err
}
