package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	"go.mongodb.org/mongo-driver/bson"
)

func TestAutoSendNotiAboutTestDeadline(t *testing.T) {
	//noti for test
	t.Run("1", func(t *testing.T) {
		apiURL := "/api/v3/sync-cron-vn/auto-send-noti-about-test-deadline"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567870",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "HCM",
					},
					{
						"city": "DN",
					},
				},
			},
			{
				"phone":     "0834567880",
				"name":      "Tasker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "HCM",
					},
					{
						"city": "DN",
					},
				},
			},
			{
				"phone":     "0834567890",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "HCM",
					},
					{
						"city": "DN",
					},
				},
			},
		})

		CreateTrainingTaskerCourse([]map[string]interface{}{
			{
				"_id":                     "test1",
				"title":                   "bài test số 1",
				"description":             "bài test dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "TEST",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "ACTIVE",
				"cities":                  []string{"HCM"},
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "pcZRQ6PqmjrAPe5gt",
						"name": "CLEANING",
					},
				},
				"displayPosition": "TRAINING_COURSE",
				"condition": map[string]interface{}{
					"byTasker": map[string]interface{}{
						"minimumStar": 3.9,
					},
				},
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},
			{
				"_id":                     "test2",
				"title":                   "bài test số 2",
				"description":             "bài test dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "TEST",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "ACTIVE",
				"cities":                  []string{"HCM"},
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "pcZRQ6PqmjrAPe5gt",
						"name": "CLEANING",
					},
				},
				"displayPosition": "TRAINING_COURSE",
				"condition": map[string]interface{}{
					"byTasker": map[string]interface{}{
						"minimumStar": 3.9,
					},
				},
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},
		})
		CreateCourseStartDate([]map[string]interface{}{
			{
				"courseId":   "test1",
				"taskerId":   "0834567880",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -23),
				"deadlineIn": 30,
			},
			{
				"courseId":   "test1",
				"taskerId":   "0834567890",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -22),
				"deadlineIn": 30,
			},
			{
				"courseId":   "test2",
				"taskerId":   "0834567870",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -23),
				"deadlineIn": 30,
			},
			{
				"courseId":  "test2",
				"taskerId":  "0834567880",
				"startDate": globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -23),
			},
		})
		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				var result []*modelNotification.Notification
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[globalConstant.ISO_CODE_VN], bson.M{}, bson.M{}, &result)
				So(len(result), ShouldEqual, 2)
				So(result[0].UserId, ShouldEqual, "0834567880")
				So(result[0].Description, ShouldEqual, "Bài kiểm tra bài test số 1 sẽ đến hạn trong 7 ngày nữa. Hãy làm ngay nào!")
				So(result[1].UserId, ShouldEqual, "0834567870")
				So(result[1].Description, ShouldEqual, "Bài kiểm tra bài test số 2 sẽ đến hạn trong 7 ngày nữa. Hãy làm ngay nào!")
			})
		})
	})

	// Test/review not qualified, won't send noti
	t.Run("2", func(t *testing.T) {
		apiURL := "/api/v3/sync-cron-vn/auto-send-noti-about-test-deadline"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567870",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 4.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "HCM",
					},
					{
						"city": "DN",
					},
				},
			},
			{
				"phone":     "0834567880",
				"name":      "Tasker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 4.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "HCM",
					},
					{
						"city": "DN",
					},
				},
			},
			{
				"phone":     "0834567890",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 4.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "HCM",
					},
					{
						"city": "DN",
					},
				},
			},
		})

		CreateTrainingTaskerCourse([]map[string]interface{}{
			//tasker star > minimum star
			{
				"_id":                     "test1",
				"title":                   "bài test số 1",
				"description":             "bài test dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "TEST",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "ACTIVE",
				"cities":                  []string{"HCM"},
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "pcZRQ6PqmjrAPe5gt",
						"name": "CLEANING",
					},
				},
				"displayPosition": "TRAINING_COURSE",
				"condition": map[string]interface{}{
					"byTasker": map[string]interface{}{
						"minimumStar": 3.9,
					},
				},
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},
			//tasker service not qualified
			{
				"_id":                     "test2",
				"title":                   "bài test số 2",
				"description":             "bài test dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "TEST",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "ACTIVE",
				"cities":                  []string{"HCM"},
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "xyz",
						"name": "CLEANING",
					},
				},
				"displayPosition": "TRAINING_COURSE",
				"condition": map[string]interface{}{
					"byTasker": map[string]interface{}{
						"minimumStar": 4.5,
					},
				},
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},

			//condition course must be completed
			{
				"_id":                     "test3",
				"title":                   "bài test số 3",
				"description":             "bài test dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "TEST",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "ACTIVE",
				"cities":                  []string{"HCM"},
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "pcZRQ6PqmjrAPe5gt",
						"name": "CLEANING",
					},
				},
				"displayPosition": "TRAINING_COURSE",
				"condition": map[string]interface{}{
					"coursesMustBeCompleted": map[string]interface{}{
						"courseIds": []string{"testx"},
					},
				},
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},
			//condition manually unblock
			{
				"_id":                     "test4",
				"title":                   "bài test số 3",
				"description":             "bài test dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "TEST",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "ACTIVE",
				"cities":                  []string{"HCM"},
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "pcZRQ6PqmjrAPe5gt",
						"name": "CLEANING",
					},
				},
				"displayPosition": "TRAINING_COURSE",
				"condition": map[string]interface{}{
					"manuallyUnblock": map[string]interface{}{
						"taskerIdsAllowed": []string{""},
					},
				},
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},
			//inactive test
			{
				"_id":                     "test6",
				"title":                   "bài test số 6",
				"description":             "bài test dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "TEST",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "INACTIVE",
				"cities":                  []string{"HCM"},
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "pcZRQ6PqmjrAPe5gt",
						"name": "CLEANING",
					},
				},
				"displayPosition": "TRAINING_COURSE",
				"condition": map[string]interface{}{
					"byTasker": map[string]interface{}{
						"minimumStar": 4.5,
					},
				},
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},
		})
		CreateCourseStartDate([]map[string]interface{}{
			{
				"courseId":   "test1",
				"taskerId":   "0834567880",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -23),
				"deadlineIn": 30,
			},
			{
				"courseId":   "test1",
				"taskerId":   "0834567890",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -22),
				"deadlineIn": 30,
			},
			{
				"courseId":   "test2",
				"taskerId":   "0834567870",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -23),
				"deadlineIn": 30,
			},
			{
				"courseId":  "test2",
				"taskerId":  "0834567880",
				"startDate": globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -23),
			},
			{
				"courseId":   "test3",
				"taskerId":   "0834567880",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -23),
				"deadlineIn": 30,
			},
			{
				"courseId":   "test4",
				"taskerId":   "0834567880",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -23),
				"deadlineIn": 30,
			},
			{
				"courseId":   "test5",
				"taskerId":   "0834567880",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -23),
				"deadlineIn": 30,
			},
			{
				"courseId":   "test6",
				"taskerId":   "0834567880",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -23),
				"deadlineIn": 30,
			},
		})
		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				var result []*modelNotification.Notification
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[globalConstant.ISO_CODE_VN], bson.M{}, bson.M{}, &result)
				So(len(result), ShouldEqual, 0)
			})
		})
	})

	//noti for review
	t.Run("3", func(t *testing.T) {
		apiURL := "/api/v3/sync-cron-vn/auto-send-noti-about-test-deadline"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567870",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "HCM",
					},
					{
						"city": "DN",
					},
				},
			},
			{
				"phone":     "0834567880",
				"name":      "Tasker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "HCM",
					},
					{
						"city": "DN",
					},
				},
			},
			{
				"phone":     "0834567890",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "HCM",
					},
					{
						"city": "DN",
					},
				},
			},
		})

		CreateTrainingTaskerCourse([]map[string]interface{}{
			{
				"_id":                     "review1",
				"title":                   "bài review số 1",
				"description":             "bài review dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "REVIEW",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "ACTIVE",
				"cities":                  []string{"HCM"},
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "pcZRQ6PqmjrAPe5gt",
						"name": "CLEANING",
					},
				},
				"displayPosition": "TRAINING_COURSE",
				"condition": map[string]interface{}{
					"byTasker": map[string]interface{}{
						"minimumStar": 3.9,
					},
				},
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},
			{
				"_id":                     "review2",
				"title":                   "bài review số 2",
				"description":             "bài review dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "REVIEW",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "ACTIVE",
				"cities":                  []string{"HCM"},
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "pcZRQ6PqmjrAPe5gt",
						"name": "CLEANING",
					},
				},
				"displayPosition": "TRAINING_COURSE",
				"condition": map[string]interface{}{
					"byTasker": map[string]interface{}{
						"minimumStar": 3.9,
					},
				},
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},
		})
		CreateCourseStartDate([]map[string]interface{}{
			{
				"courseId":   "review1",
				"taskerId":   "0834567880",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -23),
				"deadlineIn": 30,
			},
			{
				"courseId":   "review1",
				"taskerId":   "0834567890",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -22),
				"deadlineIn": 30,
			},
			{
				"courseId":   "review2",
				"taskerId":   "0834567870",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -23),
				"deadlineIn": 30,
			},
			{
				"courseId":  "review2",
				"taskerId":  "0834567880",
				"startDate": globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -23),
			},
		})
		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				var result []*modelNotification.Notification
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[globalConstant.ISO_CODE_VN], bson.M{}, bson.M{}, &result)
				So(len(result), ShouldEqual, 2)
				So(result[0].UserId, ShouldEqual, "0834567880")
				So(result[0].Description, ShouldEqual, "Bài học bài review số 1 sẽ đến hạn trong 7 ngày nữa. Hãy làm ngay nào!")
				So(result[1].UserId, ShouldEqual, "0834567870")
				So(result[1].Description, ShouldEqual, "Bài học bài review số 2 sẽ đến hạn trong 7 ngày nữa. Hãy làm ngay nào!")
			})
		})
	})

	//case: If course have submission with status passed, won't send noti
	t.Run("4", func(t *testing.T) {
		apiURL := "/api/v3/sync-cron-vn/auto-send-noti-about-test-deadline"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567870",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "HCM",
					},
					{
						"city": "DN",
					},
				},
			},
			{
				"phone":     "0834567880",
				"name":      "Tasker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "HCM",
					},
					{
						"city": "DN",
					},
				},
			},
		})

		CreateTrainingTaskerCourse([]map[string]interface{}{
			{
				"_id":                     "test1",
				"title":                   "bài test số 1",
				"description":             "bài test dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "TEST",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "ACTIVE",
				"cities":                  []string{"HCM"},
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "pcZRQ6PqmjrAPe5gt",
						"name": "CLEANING",
					},
				},
				"displayPosition": "TRAINING_COURSE",
				"condition": map[string]interface{}{
					"byTasker": map[string]interface{}{
						"minimumStar": 3.9,
					},
				},
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},
			{
				"_id":                     "test2",
				"title":                   "bài test số 2",
				"description":             "bài test dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "TEST",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "ACTIVE",
				"cities":                  []string{"HCM"},
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "pcZRQ6PqmjrAPe5gt",
						"name": "CLEANING",
					},
				},
				"displayPosition": "TRAINING_COURSE",
				"condition": map[string]interface{}{
					"byTasker": map[string]interface{}{
						"minimumStar": 3.9,
					},
				},
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},
		})
		CreateCourseStartDate([]map[string]interface{}{
			{
				"courseId":   "test1",
				"taskerId":   "0834567880",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -23),
				"deadlineIn": 30,
			},
			{
				"courseId":   "test2",
				"taskerId":   "0834567870",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -23),
				"deadlineIn": 30,
			},
		})
		CreateTrainingTaskerSubmission([]map[string]interface{}{
			{
				"_id":                 "submission1",
				"numberOfSubmissions": 1,
				"status":              "PASSED",
				"taskerId":            "0834567880",
				"course": map[string]interface{}{
					"_id": "test1",
				},
			},
			{
				"_id":                 "submission2",
				"numberOfSubmissions": 1,
				"status":              "PASSED",
				"taskerId":            "0834567870",
				"course": map[string]interface{}{
					"_id": "test2",
				},
			},
		})
		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				var result []*modelNotification.Notification
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[globalConstant.ISO_CODE_VN], bson.M{}, bson.M{}, &result)
				So(len(result), ShouldEqual, 0)
			})
		})
	})
}
