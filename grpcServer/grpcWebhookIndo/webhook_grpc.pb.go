// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcWebhookIndo

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	webhookMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/webhookMessage"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// WebhookClient is the client API for Webhook service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WebhookClient interface {
	SendDanaQueryResult(ctx context.Context, in *webhookMessage.DanaRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type webhookClient struct {
	cc grpc.ClientConnInterface
}

func NewWebhookClient(cc grpc.ClientConnInterface) WebhookClient {
	return &webhookClient{cc}
}

func (c *webhookClient) SendDanaQueryResult(ctx context.Context, in *webhookMessage.DanaRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcWebhookIndo.Webhook/SendDanaQueryResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WebhookServer is the server API for Webhook service.
// All implementations must embed UnimplementedWebhookServer
// for forward compatibility
type WebhookServer interface {
	SendDanaQueryResult(context.Context, *webhookMessage.DanaRequest) (*empty.Empty, error)
	mustEmbedUnimplementedWebhookServer()
}

// UnimplementedWebhookServer must be embedded to have forward compatible implementations.
type UnimplementedWebhookServer struct {
}

func (UnimplementedWebhookServer) SendDanaQueryResult(context.Context, *webhookMessage.DanaRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendDanaQueryResult not implemented")
}
func (UnimplementedWebhookServer) mustEmbedUnimplementedWebhookServer() {}

// UnsafeWebhookServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WebhookServer will
// result in compilation errors.
type UnsafeWebhookServer interface {
	mustEmbedUnimplementedWebhookServer()
}

func RegisterWebhookServer(s *grpc.Server, srv WebhookServer) {
	s.RegisterService(&_Webhook_serviceDesc, srv)
}

func _Webhook_SendDanaQueryResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(webhookMessage.DanaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebhookServer).SendDanaQueryResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcWebhookIndo.Webhook/SendDanaQueryResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebhookServer).SendDanaQueryResult(ctx, req.(*webhookMessage.DanaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Webhook_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcWebhookIndo.Webhook",
	HandlerType: (*WebhookServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendDanaQueryResult",
			Handler:    _Webhook_SendDanaQueryResult_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "webhook.proto",
}
