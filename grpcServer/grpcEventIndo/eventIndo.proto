syntax = "proto3";
package grpcEventIndo;

option go_package = "gitlab.com/btaskee/go-event-service-indo-v3/grpcEventIndo";

import "google/protobuf/empty.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/eventMessage/eventMessage.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users/users.proto";

service EventIndo {
  rpc TrackingCleverTapPayComboSuccess(eventMessage.TrackingCleverTapPayComboMessage) returns (google.protobuf.Empty) {} 
  rpc TrackingCleverTapPayComboPaymentError(eventMessage.TrackingCleverTapPayComboMessage) returns (google.protobuf.Empty) {} 
  rpc TrackingCleverTapTaskerAcceptTask(eventMessage.TrackingCleverTapTaskMessage) returns (google.protobuf.Empty) {} 
  rpc TrackingCleverTapTaskerCancelTask(eventMessage.TrackingCleverTapTaskMessage) returns (google.protobuf.Empty) {} 
  rpc TrackingCleverTapTaskerWithdrawTask(eventMessage.TrackingCleverTapTaskMessage) returns (google.protobuf.Empty) {} 
  rpc TrackingCleverTapTaskerDoneTask(eventMessage.TrackingCleverTapTaskMessage) returns (google.protobuf.Empty) {} 
  rpc TrackingCleverTapTaskerRating(eventMessage.TrackingCleverTapRatingMessage) returns (google.protobuf.Empty) {} 
  rpc TrackingCleverTapTaskerFinishTraining(eventMessage.TrackingCleverTapTrainingMessage) returns (google.protobuf.Empty) {}
  rpc TrackingCleverTapPrepayTaskPaymentResponse(eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage) returns (google.protobuf.Empty) {} 
  rpc AddAskerDoneBookingReferralGift(users.Users) returns (google.protobuf.Empty) {} 
  rpc AddAskerSignUpReferralGift(users.Users) returns (google.protobuf.Empty) {} 
  rpc TrackingCleverTapTaskExpired(eventMessage.TrackingCleverTapTaskMessage) returns (google.protobuf.Empty) {}

  rpc PublishUserGameCampaignActionLogin(eventMessage.EventCommonMessage) returns (google.protobuf.Empty) {} 
  rpc PublishUserGameCampaignActionDoneTask(eventMessage.EventCommonMessage) returns (google.protobuf.Empty) {} 
  rpc PublishUserGameCampaignActionTaskerDoneTask(eventMessage.EventCommonMessage) returns (google.protobuf.Empty) {}
  rpc PublishUserGameCampaignActionReferral(eventMessage.EventCommonMessage) returns (google.protobuf.Empty) {}
  rpc PublishUserGameCampaignActionDoneTaskReferral(eventMessage.EventCommonMessage) returns (google.protobuf.Empty) {}
  rpc PublishUserGameCampaignActionRollCall(eventMessage.EventCommonMessage) returns (google.protobuf.Empty) {}
  rpc PublishUserGameCampaignActionRollCallAtNoel(eventMessage.EventCommonMessage) returns (google.protobuf.Empty) {}
  rpc PublishUserGameCampaignActionDoneTaskFirst(eventMessage.EventCommonMessage) returns (google.protobuf.Empty) {}
  rpc PublishUserGameCampaignActionNewSignUp(eventMessage.EventCommonMessage) returns (google.protobuf.Empty) {}
}