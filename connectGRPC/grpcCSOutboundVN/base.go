package grpcCSOutboundVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcCSOutboundVN"
	"google.golang.org/grpc"
)

func ConnectGRPCCSOutboundVN(endpoint string) (grpcCSOutboundVN.CSOutboundVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcCSOutboundVN.NewCSOutboundVNClient(connect)
	return client, connect, err
}
