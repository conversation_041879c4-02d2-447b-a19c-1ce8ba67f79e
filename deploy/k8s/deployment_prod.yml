---
apiVersion: v1
kind: Service
metadata:
  name: go-send-task-vn-v3
  namespace: kong
  labels:
    app: go-send-task-vn-v3
spec:
  ports:
    - port: 80
      targetPort: 21100
      name: http
    - port: 81
      targetPort: 21101
      name: grpc
  selector:
    app: go-send-task-vn-v3

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: go-send-task-vn-v3
  namespace: kong
  labels:
    app: go-send-task-vn-v3
spec:
  replicas: 2
  selector:
    matchLabels:
      app: go-send-task-vn-v3
  template:
    metadata:
      labels:
        app: go-send-task-vn-v3
        log-label: "go-services"
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: go-services
                operator: In
                values:
                - group-high-memory
      containers:
        - name: go-send-task-vn-v3
          image: btaskeehub/go-push-notification-new-task-service:vn-3.4.0
          resources:
            requests:
              cpu: 10m
              memory: "10Mi"
            limits:
              # https://home.robusta.dev/blog/stop-using-cpu-limits
              # cpu: 500m
              memory: "500Mi"
          env:
            - name: TZ
              value: "7"
            - name: ELASTIC_APM_SERVER_URL
              value: "http://apm-server.elastic-system.svc.cluster.local:8200"
            - name: ELASTIC_APM_SERVICE_NAME
              value: "Send Task VN v3"
            - name: APPLICATION_MODE
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: APPLICATION_MODE
            - name: SLACK_TOKEN_VN
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: SLACK_TOKEN_VN
            - name: MONGO_CONNECTION
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: MONGO_CONNECTION
            - name: MONGO_DB_NAME
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: MONGO_DB_NAME
          imagePullPolicy: "IfNotPresent"
          ports:
            - containerPort: 80
              name: http
            - containerPort: 81
              name: grpc
      # hostAliases:
      #   - ip: "*************"
      #     hostnames:
      #       - "db-sgp1-01"
      #   - ip: "*************"
      #     hostnames:
      #       - "db-sgp1-02"
      #   - ip: "***************"
      #     hostnames:
      #       - "db-sgp1-03"
      imagePullSecrets:
            - name: btaskeehub-registrykey