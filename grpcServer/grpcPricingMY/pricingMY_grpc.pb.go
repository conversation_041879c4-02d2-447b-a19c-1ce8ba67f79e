// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcPricingMY

import (
	context "context"
	pricingSubscriptionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionRequest"
	pricingSubscriptionResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionResponse"
	pricingrequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingrequest"
	pricingresponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PricingClient is the client API for Pricing service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PricingClient interface {
	GetPricingHomeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingDeepCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingHomeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingDeepCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	ReCalculatePricing(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	APIGetPricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingSubscription(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingSofa(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingSofa(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingAirConditionerV2(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	APIGetPricingTaskDateOptions(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	ReCalculatePricingForIncreaseDuration(ctx context.Context, in *pricingrequest.PricingIncreaseDurationRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
}

type pricingClient struct {
	cc grpc.ClientConnInterface
}

func NewPricingClient(cc grpc.ClientConnInterface) PricingClient {
	return &pricingClient{cc}
}

func (c *pricingClient) GetPricingHomeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingMY.Pricing/GetPricingHomeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingClient) GetPricingDeepCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingMY.Pricing/GetPricingDeepCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingClient) RePricingHomeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingMY.Pricing/RePricingHomeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingClient) RePricingDeepCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingMY.Pricing/RePricingDeepCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingClient) ReCalculatePricing(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingMY.Pricing/ReCalculatePricing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingClient) GetPricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingMY.Pricing/GetPricingOfficeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingClient) APIGetPricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingMY.Pricing/APIGetPricingOfficeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingClient) RePricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingMY.Pricing/RePricingOfficeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingClient) GetPricingSubscription(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	out := new(pricingSubscriptionResponse.PricingSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/grpcPricingMY.Pricing/GetPricingSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingClient) GetPricingSofa(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingMY.Pricing/GetPricingSofa", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingClient) RePricingSofa(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingMY.Pricing/RePricingSofa", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingClient) GetPricingAirConditionerV2(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingMY.Pricing/GetPricingAirConditionerV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingClient) APIGetPricingTaskDateOptions(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingMY.Pricing/APIGetPricingTaskDateOptions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingClient) ReCalculatePricingForIncreaseDuration(ctx context.Context, in *pricingrequest.PricingIncreaseDurationRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingMY.Pricing/ReCalculatePricingForIncreaseDuration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PricingServer is the server API for Pricing service.
// All implementations must embed UnimplementedPricingServer
// for forward compatibility
type PricingServer interface {
	GetPricingHomeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingDeepCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingHomeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingDeepCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	ReCalculatePricing(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	APIGetPricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingSubscription(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingSofa(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingSofa(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingAirConditionerV2(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	APIGetPricingTaskDateOptions(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	ReCalculatePricingForIncreaseDuration(context.Context, *pricingrequest.PricingIncreaseDurationRequest) (*pricingresponse.CostResult, error)
	mustEmbedUnimplementedPricingServer()
}

// UnimplementedPricingServer must be embedded to have forward compatible implementations.
type UnimplementedPricingServer struct {
}

func (UnimplementedPricingServer) GetPricingHomeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingHomeCleaning not implemented")
}
func (UnimplementedPricingServer) GetPricingDeepCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingDeepCleaning not implemented")
}
func (UnimplementedPricingServer) RePricingHomeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingHomeCleaning not implemented")
}
func (UnimplementedPricingServer) RePricingDeepCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingDeepCleaning not implemented")
}
func (UnimplementedPricingServer) ReCalculatePricing(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReCalculatePricing not implemented")
}
func (UnimplementedPricingServer) GetPricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingOfficeCleaning not implemented")
}
func (UnimplementedPricingServer) APIGetPricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method APIGetPricingOfficeCleaning not implemented")
}
func (UnimplementedPricingServer) RePricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingOfficeCleaning not implemented")
}
func (UnimplementedPricingServer) GetPricingSubscription(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingSubscription not implemented")
}
func (UnimplementedPricingServer) GetPricingSofa(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingSofa not implemented")
}
func (UnimplementedPricingServer) RePricingSofa(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingSofa not implemented")
}
func (UnimplementedPricingServer) GetPricingAirConditionerV2(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingAirConditionerV2 not implemented")
}
func (UnimplementedPricingServer) APIGetPricingTaskDateOptions(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method APIGetPricingTaskDateOptions not implemented")
}
func (UnimplementedPricingServer) ReCalculatePricingForIncreaseDuration(context.Context, *pricingrequest.PricingIncreaseDurationRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReCalculatePricingForIncreaseDuration not implemented")
}
func (UnimplementedPricingServer) mustEmbedUnimplementedPricingServer() {}

// UnsafePricingServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PricingServer will
// result in compilation errors.
type UnsafePricingServer interface {
	mustEmbedUnimplementedPricingServer()
}

func RegisterPricingServer(s *grpc.Server, srv PricingServer) {
	s.RegisterService(&_Pricing_serviceDesc, srv)
}

func _Pricing_GetPricingHomeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServer).GetPricingHomeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingMY.Pricing/GetPricingHomeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServer).GetPricingHomeCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pricing_GetPricingDeepCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServer).GetPricingDeepCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingMY.Pricing/GetPricingDeepCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServer).GetPricingDeepCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pricing_RePricingHomeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServer).RePricingHomeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingMY.Pricing/RePricingHomeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServer).RePricingHomeCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pricing_RePricingDeepCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServer).RePricingDeepCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingMY.Pricing/RePricingDeepCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServer).RePricingDeepCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pricing_ReCalculatePricing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServer).ReCalculatePricing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingMY.Pricing/ReCalculatePricing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServer).ReCalculatePricing(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pricing_GetPricingOfficeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServer).GetPricingOfficeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingMY.Pricing/GetPricingOfficeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServer).GetPricingOfficeCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pricing_APIGetPricingOfficeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServer).APIGetPricingOfficeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingMY.Pricing/APIGetPricingOfficeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServer).APIGetPricingOfficeCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pricing_RePricingOfficeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServer).RePricingOfficeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingMY.Pricing/RePricingOfficeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServer).RePricingOfficeCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pricing_GetPricingSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingSubscriptionRequest.PricingSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServer).GetPricingSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingMY.Pricing/GetPricingSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServer).GetPricingSubscription(ctx, req.(*pricingSubscriptionRequest.PricingSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pricing_GetPricingSofa_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServer).GetPricingSofa(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingMY.Pricing/GetPricingSofa",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServer).GetPricingSofa(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pricing_RePricingSofa_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServer).RePricingSofa(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingMY.Pricing/RePricingSofa",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServer).RePricingSofa(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pricing_GetPricingAirConditionerV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServer).GetPricingAirConditionerV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingMY.Pricing/GetPricingAirConditionerV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServer).GetPricingAirConditionerV2(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pricing_APIGetPricingTaskDateOptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServer).APIGetPricingTaskDateOptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingMY.Pricing/APIGetPricingTaskDateOptions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServer).APIGetPricingTaskDateOptions(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Pricing_ReCalculatePricingForIncreaseDuration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingIncreaseDurationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingServer).ReCalculatePricingForIncreaseDuration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingMY.Pricing/ReCalculatePricingForIncreaseDuration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingServer).ReCalculatePricingForIncreaseDuration(ctx, req.(*pricingrequest.PricingIncreaseDurationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Pricing_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcPricingMY.Pricing",
	HandlerType: (*PricingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPricingHomeCleaning",
			Handler:    _Pricing_GetPricingHomeCleaning_Handler,
		},
		{
			MethodName: "GetPricingDeepCleaning",
			Handler:    _Pricing_GetPricingDeepCleaning_Handler,
		},
		{
			MethodName: "RePricingHomeCleaning",
			Handler:    _Pricing_RePricingHomeCleaning_Handler,
		},
		{
			MethodName: "RePricingDeepCleaning",
			Handler:    _Pricing_RePricingDeepCleaning_Handler,
		},
		{
			MethodName: "ReCalculatePricing",
			Handler:    _Pricing_ReCalculatePricing_Handler,
		},
		{
			MethodName: "GetPricingOfficeCleaning",
			Handler:    _Pricing_GetPricingOfficeCleaning_Handler,
		},
		{
			MethodName: "APIGetPricingOfficeCleaning",
			Handler:    _Pricing_APIGetPricingOfficeCleaning_Handler,
		},
		{
			MethodName: "RePricingOfficeCleaning",
			Handler:    _Pricing_RePricingOfficeCleaning_Handler,
		},
		{
			MethodName: "GetPricingSubscription",
			Handler:    _Pricing_GetPricingSubscription_Handler,
		},
		{
			MethodName: "GetPricingSofa",
			Handler:    _Pricing_GetPricingSofa_Handler,
		},
		{
			MethodName: "RePricingSofa",
			Handler:    _Pricing_RePricingSofa_Handler,
		},
		{
			MethodName: "GetPricingAirConditionerV2",
			Handler:    _Pricing_GetPricingAirConditionerV2_Handler,
		},
		{
			MethodName: "APIGetPricingTaskDateOptions",
			Handler:    _Pricing_APIGetPricingTaskDateOptions_Handler,
		},
		{
			MethodName: "ReCalculatePricingForIncreaseDuration",
			Handler:    _Pricing_ReCalculatePricingForIncreaseDuration_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pricingMY.proto",
}
