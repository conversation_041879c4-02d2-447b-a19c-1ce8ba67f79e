/*
 * @File: model.go
 * @Description: Create struct
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 18/12/2020
 * @UpdatedBy: vinhnt
 */
package modelApi

import (
	"time"

	modelNextExpansion "gitlab.com/btaskee/go-services-model-v2/grpcmodel/nextExpansion"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
)

type ApiRequest struct {
	ID                    string                                 `json:"id,omitempty"`
	Fields                []*string                              `json:"fields,omitempty"`
	Phone                 string                                 `json:"phone,omitempty"`
	UserId                string                                 `json:"userId,omitempty"`
	TaskStatus            string                                 `json:"taskStatus,omitempty"`
	NumberOfTransactions  int64                                  `json:"numberOfTransactions,omitempty"` //limit the number of financial account transaction
	Token                 string                                 `json:"token,omitempty"`
	ISOCode               string                                 `json:"isoCode,omitempty"`
	Metadata              *ApiRequestMetaData                    `json:"metadata,omitempty"`
	LastPostedTask        *time.Time                             `json:"lastPostedTask,omitempty"`
	TaskDate              *time.Time                             `json:"taskDate,omitempty"`
	Duration              float64                                `json:"duration,omitempty"`
	PromotionCode         string                                 `json:"promotionCode,omitempty"`
	ServiceId             string                                 `json:"serviceId,omitempty"`
	AppVersion            string                                 `json:"appVersion,omitempty"`
	TaskId                string                                 `json:"taskId,omitempty"`
	Lat                   float64                                `json:"lat,omitempty"`
	Lng                   float64                                `json:"lng,omitempty"`
	Ip                    string                                 `json:"ip,omitempty"`
	RoomIds               []string                               `json:"roomIds,omitempty"`
	CountryCode           string                                 `json:"countryCode,omitempty"`
	Email                 string                                 `json:"email,omitempty"`
	Name                  string                                 `json:"name,omitempty"`
	Introduction          string                                 `json:"introduction,omitempty"`
	Message               string                                 `json:"message,omitempty"`
	ChatId                string                                 `json:"chatId,omitempty"`
	Key                   string                                 `json:"key,omitempty"`
	Text                  string                                 `json:"text,omitempty"`
	Language              string                                 `json:"language,omitempty"`
	CreatedAt             *time.Time                             `json:"createdAt,omitempty"`
	UserIds               []string                               `json:"userIds,omitempty"`
	ReferralCode          string                                 `json:"referralCode,omitempty"`
	Month                 interface{}                            `json:"month,omitempty"`
	IsCompany             bool                                   `json:"isCompany,omitempty"`
	ServiceName           string                                 `json:"serviceName,omitempty"`
	Cost                  float64                                `json:"cost,omitempty"`
	Address               string                                 `json:"address,omitempty"`
	BeginAt               *time.Time                             `json:"beginAt,omitempty"`
	FreeSchedule          map[int]interface{}                    `json:"freeSchedule,omitempty"`
	Time                  *time.Time                             `json:"time,omitempty"`
	SubscriptionId        string                                 `json:"subscriptionId,omitempty"`
	CurrentCourse         map[string]interface{}                 `json:"currentCourse,omitempty"`
	EmployeeId            string                                 `json:"employeeId,omitempty"`
	IsRead                *bool                                  `json:"isRead"`
	IsDefault             bool                                   `json:"isDefault,omitempty"`
	CardId                string                                 `json:"cardId,omitempty"`
	NoReceiveNotification bool                                   `json:"noReceiveNotification"`
	IsForceUpdate         bool                                   `json:"isForceUpdate,omitempty"`
	Amount                float64                                `json:"amount,omitempty"`
	FromDate              *time.Time                             `json:"fromDate,omitempty"`
	ToDate                *time.Time                             `json:"toDate,omitempty"`
	From                  string                                 `json:"from,omitempty"`
	ScheduleId            string                                 `json:"scheduleId,omitempty"`
	IsActive              bool                                   `json:"isActive,omitempty"`
	Weekday               []int                                  `json:"weekday,omitempty"`
	ScheduleTime          *time.Time                             `json:"scheduleTime,omitempty"`
	ScheduleDuration      float32                                `json:"scheduleDuration,omitempty"`
	PaymentMethod         string                                 `json:"paymentMethod,omitempty"`
	TaskerId              string                                 `json:"taskerId,omitempty"`
	TaskerIds             []string                               `json:"taskerIds,omitempty"`
	FilterBy              map[string]interface{}                 `json:"filterBy,omitempty"`
	SortBy                string                                 `json:"sortBy,omitempty"`
	GiftId                string                                 `json:"giftId,omitempty"`
	SendFrom              string                                 `json:"sendFrom,omitempty"`
	AvatarUrl             string                                 `json:"avatarUrl,omitempty"`
	Data                  map[string]interface{}                 `json:"data,omitempty"`
	CampaignId            string                                 `json:"campaignId,omitempty"`
	CampaignAction        string                                 `json:"campaignAction,omitempty"`
	LocationId            string                                 `json:"locationId,omitempty"`
	VerificationData      map[string]interface{}                 `json:"verificationData,omitempty"`
	Type                  string                                 `json:"type,omitempty"`
	ReportData            map[string]interface{}                 `json:"reportData,omitempty"`
	SubscriptionRequestId string                                 `json:"subscriptionRequestId,omitempty"`
	TransactionId         string                                 `json:"transactionId,omitempty"`
	ReasonReport          []*modelService.ServiceText            `json:"reasonReport,omitempty"`
	OtherReport           string                                 `json:"otherReport,omitempty"`
	Platform              string                                 `json:"platform,omitempty"`
	Version               string                                 `json:"version,omitempty"`
	MessageId             string                                 `json:"messageId,omitempty"`
	Place                 *modelNextExpansion.NextExpansionPlace `json:"place,omitempty"`
	Year                  int                                    `json:"year,omitempty"`
	FromPartner           string                                 `json:"fromPartner,omitempty"`
	Reason                *modelService.ServiceText              `json:"reason,omitempty"`
	ComboVoucherId        string                                 `json:"comboVoucherId,omitempty"`
	MessageTo             []string                               `json:"messageTo,omitempty"`
	PartnerCode           string                                 `json:"partnerCode,omitempty"`
	Point                 float64                                `json:"point,omitempty"`
	// For pagination
	Page  int `json:"page,omitempty"`
	Limit int `json:"limit,omitempty"`
}

type ApiRequestMetaData struct {
	Version string `json:"version,omitempty"`
}

type UrBoxErrorCode struct {
	Message string
	Code    string
}

type ApiBackEndRequest struct {
	From                string                       `json:"from,omitempty"`
	To                  string                       `json:"to,omitempty"`
	Message             map[string]interface{}       `json:"message,omitempty"`
	TaskId              string                       `json:"taskId,omitempty"`
	TaskDate            *time.Time                   `json:"taskDate,omitempty"`
	CancelDate          *time.Time                   `json:"cancelDate,omitempty"`
	TaskCost            float64                      `json:"taskCost,omitempty"`
	CancellationReason  string                       `json:"cancellationReason,omitempty"`
	TaskerNotCommingFee *modelSettings.NotCommingFee `json:"taskerNotCommingFee,omitempty"`
	UserId              string                       `json:"userId,omitempty"`
	SubscriptionId      string                       `json:"subscriptionId,omitempty"`
	RefundReason        string                       `json:"refundReason,omitempty"`
	Option              *InputOption                 `json:"option,omitempty"`
}

type InputOption struct {
	From string                 `json:"from,omitempty"`
	Data map[string]interface{} `json:"data,omitempty"`
}

type StringeeRequest struct {
	From    string `json:"from,omitempty"`
	To      string `json:"to,omitempty"`
	Custom  string `json:"custom,omitempty"`
	IsoCode string `json:"isoCode,omitempty"`
}

type RelatedServiceInfo struct {
	XId          string                    `json:"_id,omitempty"`
	Name         string                    `json:"name,omitempty"`
	Thumbnail    string                    `json:"thumbnail,omitempty"`
	Text         *modelService.ServiceText `json:"text,omitempty"`
	IsNewService bool                      `json:"isNewService,omitempty"`
}

type ApiRequestGroceryAssistant struct {
	StoreId        string     `json:"storeId,omitempty"`
	CategoryId     string     `json:"categoryId,omitempty"`
	SubCategoryId  string     `json:"subCategoryId,omitempty"`
	ProductId      string     `json:"productId,omitempty"`
	Language       string     `json:"language,omitempty"`
	Text           string     `json:"text,omitempty"`
	TaskPlace      *TaskPlace `json:"taskPlace,omitempty"`
	ShoppingCardId string     `json:"shoppingCardId,omitempty"`

	Page  int64 `json:"page,omitempty"`
	Limit int64 `json:"limit,omitempty"`
}

type UpdateShoppingCartRequest struct {
	UserId        string          `json:"userId,omitempty"`
	ProductId     string          `json:"productId,omitempty"`
	Action        string          `json:"action,omitempty"`
	IsUserCreated bool            `json:"isUserCreated,omitempty"`
	Quantity      int32           `json:"quantity,omitempty"`
	NewProduct    *UserNewProduct `json:"newProduct,omitempty"`
}

type UserNewProduct struct {
	Price      float64                   `json:"price,omitempty"`
	Text       *modelService.ServiceText `json:"text,omitempty"`
	CategoryId string                    `json:"categoryId,omitempty"`
}

type TaskPlace struct {
	Country  string  `json:"country,omitempty"`
	City     string  `json:"city,omitempty"`
	District string  `json:"district,omitempty"`
	Lat      float64 `json:"lat,omitempty"`
	Lng      float64 `json:"lng,omitempty"`
}
