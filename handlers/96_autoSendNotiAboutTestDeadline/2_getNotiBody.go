package autoSendNotiAboutTestDeadline

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTrainingV2 "gitlab.com/btaskee/go-services-model-v2/grpcmodel/trainingTaskerV2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// get courseId, courseTitle and taskerId to send notifications
func getNotiBody(coursesStartDate []*modelTrainingV2.CourseStartDate) []*model.NotiNewTestAndReviewAndDeadlineBody {
	var res []*model.NotiNewTestAndReviewAndDeadlineBody
	for _, courseStartDate := range coursesStartDate {
		fields := bson.M{
			"title": 1,
			"type":  1,
		}
		course, err := handlers.GetCourse(bson.M{"_id": courseStartDate.CourseId}, fields)
		if err != nil && err != mongo.ErrNoDocuments {
			text := fmt.Sprintf("AutoSendNotiAboutTestDeadline %s: Lỗi call API SendNotiAboutTestDeadline , err: %s ", local.ISO_CODE, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], text)
			return nil
		}

		flag := false
		for index, notiNewTestAndReviewBody := range res {
			if course.XId == notiNewTestAndReviewBody.CourseId {
				res[index].TaskerIds = append(res[index].TaskerIds, courseStartDate.TaskerId)
				flag = true
				break
			}
		}
		if !flag {
			notiBody := &model.NotiNewTestAndReviewAndDeadlineBody{
				CourseId:    courseStartDate.CourseId,
				CourseTitle: course.Title,
				CourseType:  course.Type,
			}
			notiBody.TaskerIds = append(notiBody.TaskerIds, courseStartDate.TaskerId)
			res = append(res, notiBody)
		}
	}
	return res
}
