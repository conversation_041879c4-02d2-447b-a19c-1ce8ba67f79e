package grpcPayment

import (
	"gitlab.com/btaskee/go-payment-service-v2/grpcPayment"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"google.golang.org/grpc"
)

func ConnectGRPCPayment(endpoint string) (grpcPayment.PaymentClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPayment.NewPaymentClient(connect)
	return client, connect, err
}
