package grpcPricingVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPricingVN"
	"google.golang.org/grpc"
)

func ConnectGRPCPricingVN(endpoint string) (grpcPricingVN.PricingVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPricingVN.NewPricingVNClient(connect)
	return client, connect, err
}
