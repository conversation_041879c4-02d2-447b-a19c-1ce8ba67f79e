// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcPushNotificationTH

import (
	context "context"
	pushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PushNotificationTHClient is the client API for PushNotificationTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PushNotificationTHClient interface {
	Send(ctx context.Context, in *pushNotificationRequest.PushNotificationRequest, opts ...grpc.CallOption) (*response.Response, error)
}

type pushNotificationTHClient struct {
	cc grpc.ClientConnInterface
}

func NewPushNotificationTHClient(cc grpc.ClientConnInterface) PushNotificationTHClient {
	return &pushNotificationTHClient{cc}
}

func (c *pushNotificationTHClient) Send(ctx context.Context, in *pushNotificationRequest.PushNotificationRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPushNotificationTH.PushNotificationTH/Send", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PushNotificationTHServer is the server API for PushNotificationTH service.
// All implementations must embed UnimplementedPushNotificationTHServer
// for forward compatibility
type PushNotificationTHServer interface {
	Send(context.Context, *pushNotificationRequest.PushNotificationRequest) (*response.Response, error)
	mustEmbedUnimplementedPushNotificationTHServer()
}

// UnimplementedPushNotificationTHServer must be embedded to have forward compatible implementations.
type UnimplementedPushNotificationTHServer struct {
}

func (UnimplementedPushNotificationTHServer) Send(context.Context, *pushNotificationRequest.PushNotificationRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Send not implemented")
}
func (UnimplementedPushNotificationTHServer) mustEmbedUnimplementedPushNotificationTHServer() {}

// UnsafePushNotificationTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PushNotificationTHServer will
// result in compilation errors.
type UnsafePushNotificationTHServer interface {
	mustEmbedUnimplementedPushNotificationTHServer()
}

func RegisterPushNotificationTHServer(s *grpc.Server, srv PushNotificationTHServer) {
	s.RegisterService(&_PushNotificationTH_serviceDesc, srv)
}

func _PushNotificationTH_Send_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationRequest.PushNotificationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationTHServer).Send(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPushNotificationTH.PushNotificationTH/Send",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationTHServer).Send(ctx, req.(*pushNotificationRequest.PushNotificationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PushNotificationTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcPushNotificationTH.PushNotificationTH",
	HandlerType: (*PushNotificationTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Send",
			Handler:    _PushNotificationTH_Send_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "push-notification-th.proto",
}
