package service

import (
	"context"
	"fmt"
	"log"
	"net"
	"net/http"
	"os"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcEmailVN"
	"go.elastic.co/apm/module/apmgrpc"
	"go.elastic.co/apm/module/apmhttp"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
)

var mode = os.Getenv("APPLICATION_MODE")

func NewGRPCServer() *grpc.Server {
	// create a server instance
	s := Server{}

	// create a gRPC server object
	grpcServer := grpc.NewServer(grpc.UnaryInterceptor(apmgrpc.NewUnaryServerInterceptor()))

	// attach the Ping service to the server
	grpcEmailVN.RegisterEmailVNServer(grpcServer, &s)

	return grpcServer
}

func StartGRPCServer(grpcServer *grpc.Server, gRPCPort string) error {
	log.Printf("[%s] Started %s gRPC at port: %s", mode, local.SERVICE_NAME, gRPCPort)
	// Create a listener on TCP port
	lis, err := net.Listen("tcp", fmt.Sprintf(":%s", gRPCPort))
	if err != nil {
		return err
	}
	// Start the server
	return grpcServer.Serve(lis)
}

func StartRESTServer(restPort string, gRPCPort string) error {
	ctx := context.Background()
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	mux := runtime.NewServeMux()
	opts := []grpc.DialOption{grpc.WithTransportCredentials(insecure.NewCredentials())}
	err := grpcEmailVN.RegisterEmailVNHandlerFromEndpoint(ctx, mux, fmt.Sprintf(":%s", gRPCPort), opts)
	if err != nil {
		return err
	}

	log.Printf("[%s] Started %s REST at port: %s", mode, local.SERVICE_NAME, restPort)
	http.ListenAndServe(":"+restPort, apmhttp.Wrap(mux))

	return nil
}
