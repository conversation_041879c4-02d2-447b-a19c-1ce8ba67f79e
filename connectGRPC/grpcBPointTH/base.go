package grpcBPointTH

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcBPointTH"
	"google.golang.org/grpc"
)

func ConnectGRPCBPointTH(endpoint string) (grpcBPointTH.BPointTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcBPointTH.NewBPointTHClient(connect)
	return client, connect, err
}
