// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.7.1
// source: apiAskerVN.proto

package grpcApiAskerVN

import (
	redeemGiftRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/redeemGiftRequest"
	redeemGiftResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/redeemGiftResponse"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_apiAskerVN_proto protoreflect.FileDescriptor

var file_apiAskerVN_proto_rawDesc = []byte{
	0x0a, 0x10, 0x61, 0x70, 0x69, 0x41, 0x73, 0x6b, 0x65, 0x72, 0x56, 0x4e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0e, 0x67, 0x72, 0x70, 0x63, 0x41, 0x70, 0x69, 0x41, 0x73, 0x6b, 0x65, 0x72,
	0x56, 0x4e, 0x1a, 0x5b, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62,
	0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x47, 0x69, 0x66, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x47, 0x69,
	0x66, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x5d, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73,
	0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x47, 0x69, 0x66, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x6a,
	0x0a, 0x0a, 0x41, 0x70, 0x69, 0x41, 0x73, 0x6b, 0x65, 0x72, 0x56, 0x4e, 0x12, 0x5c, 0x0a, 0x0a,
	0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x47, 0x69, 0x66, 0x74, 0x12, 0x24, 0x2e, 0x72, 0x65, 0x64,
	0x65, 0x65, 0x6d, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x52,
	0x65, 0x64, 0x65, 0x65, 0x6d, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x26, 0x2e, 0x72, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x47, 0x69, 0x66, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x64, 0x65, 0x65, 0x6d, 0x47, 0x69, 0x66, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x43, 0x5a, 0x41, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65,
	0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x2f, 0x67, 0x72, 0x70, 0x63, 0x41, 0x70, 0x69, 0x41, 0x73, 0x6b, 0x65, 0x72, 0x56, 0x4e, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_apiAskerVN_proto_goTypes = []interface{}{
	(*redeemGiftRequest.RedeemGiftRequest)(nil),   // 0: redeemGiftRequest.RedeemGiftRequest
	(*redeemGiftResponse.RedeemGiftResponse)(nil), // 1: redeemGiftResponse.RedeemGiftResponse
}
var file_apiAskerVN_proto_depIdxs = []int32{
	0, // 0: grpcApiAskerVN.ApiAskerVN.RedeemGift:input_type -> redeemGiftRequest.RedeemGiftRequest
	1, // 1: grpcApiAskerVN.ApiAskerVN.RedeemGift:output_type -> redeemGiftResponse.RedeemGiftResponse
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_apiAskerVN_proto_init() }
func file_apiAskerVN_proto_init() {
	if File_apiAskerVN_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_apiAskerVN_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_apiAskerVN_proto_goTypes,
		DependencyIndexes: file_apiAskerVN_proto_depIdxs,
	}.Build()
	File_apiAskerVN_proto = out.File
	file_apiAskerVN_proto_rawDesc = nil
	file_apiAskerVN_proto_goTypes = nil
	file_apiAskerVN_proto_depIdxs = nil
}
