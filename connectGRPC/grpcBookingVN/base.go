package grpcBookingVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcBookingVN"
	"google.golang.org/grpc"
)

func ConnectGRPCBookingVN(endpoint string) (grpcBookingVN.BookingVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcBookingVN.NewBookingVNClient(connect)
	return client, connect, err
}
