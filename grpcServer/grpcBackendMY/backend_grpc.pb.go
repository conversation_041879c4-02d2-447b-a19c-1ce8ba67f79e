// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcBackendMY

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	processExpiredTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/processExpiredTask"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// BackendClient is the client API for Backend service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BackendClient interface {
	ProcessExpiredTask(ctx context.Context, in *processExpiredTask.ProcessExpiredTask, opts ...grpc.CallOption) (*empty.Empty, error)
}

type backendClient struct {
	cc grpc.ClientConnInterface
}

func NewBackendClient(cc grpc.ClientConnInterface) BackendClient {
	return &backendClient{cc}
}

func (c *backendClient) ProcessExpiredTask(ctx context.Context, in *processExpiredTask.ProcessExpiredTask, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcBackendMY.Backend/ProcessExpiredTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BackendServer is the server API for Backend service.
// All implementations must embed UnimplementedBackendServer
// for forward compatibility
type BackendServer interface {
	ProcessExpiredTask(context.Context, *processExpiredTask.ProcessExpiredTask) (*empty.Empty, error)
	mustEmbedUnimplementedBackendServer()
}

// UnimplementedBackendServer must be embedded to have forward compatible implementations.
type UnimplementedBackendServer struct {
}

func (UnimplementedBackendServer) ProcessExpiredTask(context.Context, *processExpiredTask.ProcessExpiredTask) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ProcessExpiredTask not implemented")
}
func (UnimplementedBackendServer) mustEmbedUnimplementedBackendServer() {}

// UnsafeBackendServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BackendServer will
// result in compilation errors.
type UnsafeBackendServer interface {
	mustEmbedUnimplementedBackendServer()
}

func RegisterBackendServer(s *grpc.Server, srv BackendServer) {
	s.RegisterService(&_Backend_serviceDesc, srv)
}

func _Backend_ProcessExpiredTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(processExpiredTask.ProcessExpiredTask)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BackendServer).ProcessExpiredTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBackendMY.Backend/ProcessExpiredTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BackendServer).ProcessExpiredTask(ctx, req.(*processExpiredTask.ProcessExpiredTask))
	}
	return interceptor(ctx, in, info, handler)
}

var _Backend_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcBackendMY.Backend",
	HandlerType: (*BackendServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ProcessExpiredTask",
			Handler:    _Backend_ProcessExpiredTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "backend.proto",
}
