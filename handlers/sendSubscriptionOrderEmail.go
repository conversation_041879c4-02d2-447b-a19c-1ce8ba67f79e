package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-email-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelSubscription "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscription"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func ResendSubscriptionOrderEmail(req *modelEmailSending.EmailRequest) (*modelEmailResponse.EmailResponse, error) {
	// Check server config
	resErr := CheckEmailConfig()
	if resErr != nil {
		local.Logger.Warn(resErr.Message)
		return resErr, nil
	}

	if req.SubscriptionId == "" {
		local.Logger.Warn(lib.ERROR_SUBSCRIPTION_ID_REQUIRED,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_SUBSCRIPTION_ID_REQUIRED,
		}, nil
	}

	if req.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_USER_ID_REQUIRED,
		}, nil
	}

	user, _ := modelUser.GetOneById(local.ISO_CODE, req.UserId, bson.M{"name": 1, "emails": 1, "language": 1, "referralCode": 1})
	if user == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusNotFound,
			Message:    lib.ERROR_USER_NOT_FOUND,
		}, nil
	}

	if len(user.Emails) == 0 || !user.Emails[0].Verified {
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusOK,
		}, nil
	}

	var subscription *modelSubscription.Subscription
	globalDataAccess.GetOneById(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], req.SubscriptionId,
		bson.M{"serviceText": 1, "taskPlace": 1, "schedule": 1, "price": 1, "weekday": 1, "currency": 1, "isoCode": 1, "discountMoney": 1, "startDate": 1, "endDate": 1, "duration": 1, "orderId": 1, "address": 1, "phone": 1},
		&subscription,
	)

	if subscription == nil {
		local.Logger.Warn(lib.ERROR_SUBSCRIPTION_NOT_FOUND,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusNotFound,
			Message:    lib.ERROR_SUBSCRIPTION_NOT_FOUND,
		}, nil
	}

	// option, err := subscriptionOrderEmail(user, subscription)
	// return SendEmail(option)

	option, err := subscriptionOrderEmail(user, subscription)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CONTENT_NIL,
			zap.Error(err),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_CONTENT_NIL,
		}, err
	}
	return SendEmail(option)
}

func subscriptionOrderEmail(user *modelUser.Users, data *modelSubscription.Subscription) (*modelEmailSending.EmailSending, error) {
	var settings *modelSettings.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"subscriptionPaymentInstruction": 1, "referralSetting": 1}, &settings)
	var instructionInCity *modelSettings.SettingsSubscriptionPaymentInstruction

	for _, city := range settings.SubscriptionPaymentInstruction {
		if data.TaskPlace != nil && city.City == data.TaskPlace.City {
			instructionInCity = city
		}
	}
	if instructionInCity == nil {
		// Alway get Hochiminh instruction when specify city doesn't set
		for _, city := range settings.SubscriptionPaymentInstruction {
			if city.City == "Hồ Chí Minh" {
				instructionInCity = city
			}
		}
	}

	to := user.Emails[0].Address
	language := globalConstant.LANG_EN
	if user.Language != "" {
		language = user.Language
	}
	fomartTime := "03:04 PM"
	fomartDate := "01/02/2006"
	if language == globalConstant.LANG_VI {
		fomartTime = "15:04"
		fomartDate = "02/01/2006"
	}

	emailData := &model.EmailSubsOrderData{}

	// startTime:= getsubs.Schedule[0]
	var startTime, endTime string
	if len(data.Schedule) > 0 {
		startTime = globalLib.ParseDateFromTimeStamp(data.Schedule[0], local.TimeZone).Format(fomartTime)
		endTime = globalLib.ParseDateFromTimeStamp(data.Schedule[0], local.TimeZone).Add(time.Duration(data.Duration) * time.Hour).Format(fomartTime)
	}

	startDate := globalLib.ParseDateFromTimeStamp(data.StartDate, local.TimeZone).Format(fomartDate)
	endDate := globalLib.ParseDateFromTimeStamp(data.EndDate, local.TimeZone).Format(fomartDate)
	totalPrice := data.Price - data.DiscountMoney
	bankName := instructionInCity.BankName.En
	bankDepartment := instructionInCity.BankDepartment.En
	if language == globalConstant.LANG_VI {
		bankName = instructionInCity.BankName.Vi
		bankDepartment = instructionInCity.BankDepartment.Vi
	} else if language == globalConstant.LANG_TH {
		bankName = instructionInCity.BankName.Th
		bankDepartment = instructionInCity.BankDepartment.Th
	} else if language == globalConstant.LANG_KO {
		bankName = instructionInCity.BankName.Ko
		bankDepartment = instructionInCity.BankDepartment.Ko
	}

	var serviceText string
	if data != nil && data.ServiceText != nil {
		serviceText = data.ServiceText.Vi
		switch language {
		case globalConstant.LANG_EN:
			serviceText = data.ServiceText.En
		case globalConstant.LANG_KO:
			serviceText = data.ServiceText.Ko
		case globalConstant.LANG_TH:
			serviceText = data.ServiceText.Th
		}
	}

	currency := globalConstant.CURRENCY_VN
	if data.Currency != nil && data.Currency.Code != "" {
		currency = data.Currency.Code
	}

	Weekday := lib.GetDaysFromDateTimestamp(data.Schedule)
	days := lib.GetIsoWeekDays(Weekday)
	days = lib.DaysByLang(days, language)
	emailData.Name = localization.T(language, "RECEIPT_SUBS_EMAIL_SUBS_NAME")
	emailData.SubsInfo = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_SUBS_INFO")
	emailData.Dear = localization.T(language, "RENEW_SUBS_EMAIL_DEAR")
	emailData.Thank = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_THANK_FOR")
	emailData.OrderNumberTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_OEDER_NUMBER")
	emailData.AskerNameTitle = localization.T(language, "CANCEL_EMAIL_ASKER_NAME_TITLE")
	emailData.TotalCostTitle = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_TOTAL_COST_TITLE")
	emailData.PaidByTitle = localization.T(language, "RENEW_SUBS_EMAIL_PAID_BY")
	emailData.PaymentMethodBankTransfer = localization.T(language, "BOOKING_PAYMENT_METHOD_BANK_TRANSFER_1")
	emailData.PaymentInstructions = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_PAYMENT_INSTRUCTIONS")
	emailData.PaymentInfo = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_PAYMENT_INFO", globalLib.FormatMoney(totalPrice), currency, startDate)
	if language == globalConstant.LANG_KO {
		emailData.PaymentInfo = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_PAYMENT_INFO", startDate, globalLib.FormatMoney(totalPrice), currency)
	}
	emailData.BankTitle = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_PAYMENT_BANK_TITLE")
	emailData.BranchTitle = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_PAYMENT_BRANCH_TITLE")
	emailData.AccountNumberTitle = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_PAYMENT_ACCOUNT_NUMBER_TITLE")
	emailData.AccountHolderTitle = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_PAYMENT_ACCOUNT_HOLDER_TITLE")
	emailData.TransferContentTitle = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_PAYMENT_TRANSFER_CONTENT_TITLE")
	emailData.SubsDetailTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_SUBS_DETAIL_TITLE")
	emailData.SubsPlaceTitle = localization.T(language, "CANCEL_EMAIL_TASK_PLACE_TITLE")
	emailData.SubsServiceTitle = localization.T(language, "CANCEL_EMAIL_SERVICE_TITLE")
	emailData.SubsBeginAtTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_START_TITLE")
	emailData.SubsDurationTitle = localization.T(language, "CANCEL_EMAIL_DURATION_TITLE")
	emailData.SubsDaysTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_DAYS_TITLE")
	emailData.SubsDateFromTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_DURATION_TITLE")
	emailData.SubsPackValueTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_PACK_VALUE")
	emailData.SubsBasePriceTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_COST_OF_TASK_TITLE")
	emailData.PaymentTotalText = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_TOTAL_COST_TITLE")
	emailData.PromotionTitle = localization.T(language, "EMAIL_PROMOTION_TITLE")
	emailData.TransCodeTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_TRANS_CODE_TITLE")
	emailData.NoteTitle = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_NOTE_TITLE")
	emailData.Note1 = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_NOTE_1")
	emailData.Note2 = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_NOTE_2")
	emailData.Note3 = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_NOTE_3")
	emailData.PolicyRefundTitle = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_POLICY_REFUND_TITLE")
	emailData.PolicyRefund1 = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_POLICY_REFUND_1")
	emailData.PolicyRefund2 = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_POLICY_REFUND_2")
	emailData.PolicyRefund3 = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_POLICY_REFUND_3")
	emailData.PolicyRefund4 = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_POLICY_REFUND_4")
	emailData.PolicyRefund5 = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_POLICY_REFUND_5")
	emailData.SupportRefundTitle = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_SUPPORT_REFUND_TITLE")
	emailData.SupportRefund1 = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_SUPPORT_REFUND_1")
	emailData.SupportRefund2 = localization.T(language, "RENEW_SUBS_ORDER_EMAIL_SUPPORT_REFUND_2")
	emailData.ReferralTitle = localization.T(language, "EMAIL_REFERRAL_TITLE")
	emailData.FollowBtaskeeAt = localization.T(language, "FOLLOW_BTASKEE_AT")
	emailData.SubsDuration = localization.T(language, "RECEIPT_SUBS_DURATION", data.Duration)
	emailData.AskerName = user.Name
	emailData.SubsOrderId = data.OrderId
	emailData.SubsStartDate = startDate
	emailData.SubsAddress = data.Address
	emailData.SubsName = serviceText
	emailData.SubsBeginAt = fmt.Sprintf("%s %s %s %s", localization.T(language, "FROM_TIME"), startTime, localization.T(language, "TO_TIME"), endTime)
	emailData.SubsDays = strings.Join(days, ", ")
	emailData.SubsDateFrom = fmt.Sprintf("%s %s %s %s", localization.T(language, "FROM_DATE"), startDate, localization.T(language, "TO_DATE"), endDate)
	if data.DiscountMoney > 0 {
		emailData.PromotionDiscount = fmt.Sprintf("-%s %s", globalLib.FormatMoney(data.DiscountMoney), currency)
	}
	emailData.SubsBasePrice = fmt.Sprintf("%s %s", globalLib.FormatMoney(data.Price), currency)
	emailData.TotalPrice = fmt.Sprintf("%s %s", globalLib.FormatMoney(totalPrice), currency)
	emailData.BankName = bankName
	emailData.BankDepartment = bankDepartment
	emailData.AccountNumber = instructionInCity.AccountNumber
	emailData.AccountHolder = instructionInCity.AccountHolder
	emailData.TransferContent = fmt.Sprintf("%s - %s", data.OrderId, data.Phone)
	emailData.Referral = strings.ToUpper(user.ReferralCode)

	value, currency := getReferralValueAndCurrency(data.IsoCode, currency, settings)
	emailData.ReferralText = localization.T(language, "EMAIL_REFERRAL_TEXT", globalLib.FormatMoney(value), currency, globalLib.FormatMoney(value), currency)

	// Parse email template
	emailTempalte := fmt.Sprintf("%s/subscriptionOrder.html", cfg.EmailTemplateURL)

	// Convert to map to use func update btaskee info
	emailDataJson, _ := json.Marshal(emailData)
	emailDataMap := make(map[string]interface{})
	json.Unmarshal(emailDataJson, &emailDataMap)
	emailDataMap = globalLib.UpdateBtaskeeInfoInEmailByIsoCode(emailDataMap, data.IsoCode)

	body, err := ParseTemplate(emailTempalte, emailDataMap)
	if err != nil {
		return nil, err
	}

	// NOTE: Test with sync cron service "./test.sh 9 3"
	return &modelEmailSending.EmailSending{
		From:    "bTaskee Receipts <<EMAIL>>",
		To:      to,
		Bcc:     []string{"<EMAIL>"},
		ReplyTo: "<EMAIL>",
		Subject: localization.T(language, "SUBSCRIPTION_EMAIL_SUBJECT"),
		Content: body,
	}, nil
}
