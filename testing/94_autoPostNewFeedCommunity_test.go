package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelCommunityPost "gitlab.com/btaskee/go-services-model-v2/grpcmodel/communityPost"
	"go.mongodb.org/mongo-driver/bson"
)

func TestAutoPostNewFeedCommunity(t *testing.T) {
	apiURL := apiSyncCron + "/auto-post-new-feed"

	//case:
	// Update 2 post with timeSchedule -1 minutes && -5seconds
	t.Run("1", func(t *testing.T) {
		ResetData()
		CreateTags([]map[string]any{
			{
				"_id": "tag1",
				"text": map[string]interface{}{
					"en": "Tag 1",
				},
			}, {
				"_id": "tag2",
				"text": map[string]interface{}{
					"en": "Tag 2",
				},
			}, {
				"_id": "tag3",
				"text": map[string]interface{}{
					"en": "Tag 3",
				},
			},
		})

		CreateCommunityUser([]map[string]interface{}{
			{
				"_id":             "0834567890",
				"name":            "Nguyễn Hồng Thanh",
				"avatar":          "https://s3.amazonaws.com/uifaces/faces/twitter/calebogden/128.jpg",
				"bio":             "I am Asker 01",
				"status":          globalConstant.COMMUNITY_USER_STATUS_ACTIVE,
				"numberOfLikes":   1000,
				"numberOfPosts":   1000,
				"numberOfShares":  1000,
				"favouriteTagIds": []string{"tag1", "tag1", "tag3"},
			},
		})

		CreateCommunityPost([]map[string]any{
			{
				"_id":     "x66c31d7d583b1a3281b5c9b9",
				"userId":  "0834567890",
				"status":  globalConstant.COMMUNITY_POST_STATUS_NOT_POSTED,
				"content": "this is post 1",
				"tagIds": []string{
					"tag2",
					"tag3",
				},
				"scheduleTime": globalLib.GetCurrentTime(local.TimeZone).Add(-1 * time.Minute),
				"createdAt":    globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1).Add(1 * time.Hour),
			},
			{
				"_id":     "x66c31d7d583b1a3281b5c9b10",
				"userId":  "0834567890",
				"status":  globalConstant.COMMUNITY_POST_STATUS_NOT_POSTED,
				"content": "this is post 2",
				"tagIds": []string{
					"tag3",
				},
				"scheduleTime": globalLib.GetCurrentTime(local.TimeZone).Add(-5 * time.Second),
				"createdAt":    globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1).Add(3 * time.Hour),
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When the service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the database", func() {
					var posts []*modelCommunityPost.CommunityPost
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_COMMUNITY_POST[local.ISO_CODE], bson.M{}, bson.M{"status": 1, "scheduleTime": 1}, &posts)
					for _, post := range posts {
						So(post.Status, ShouldEqual, globalConstant.COMMUNITY_POST_STATUS_ACTIVE)
						So(post.ScheduleTime, ShouldNotBeNil)
					}
				})
			})
		})
	})

	//case:
	// Update 2 post with timeSchedule +1 minutes && +5seconds
	t.Run("2", func(t *testing.T) {
		ResetData()
		CreateTags([]map[string]any{
			{
				"_id": "tag1",
				"text": map[string]interface{}{
					"en": "Tag 1",
				},
			}, {
				"_id": "tag2",
				"text": map[string]interface{}{
					"en": "Tag 2",
				},
			}, {
				"_id": "tag3",
				"text": map[string]interface{}{
					"en": "Tag 3",
				},
			},
		})

		CreateCommunityUser([]map[string]interface{}{
			{
				"_id":             "0834567890",
				"name":            "Nguyễn Hồng Thanh",
				"avatar":          "https://s3.amazonaws.com/uifaces/faces/twitter/calebogden/128.jpg",
				"bio":             "I am Asker 01",
				"status":          globalConstant.COMMUNITY_USER_STATUS_ACTIVE,
				"numberOfLikes":   1000,
				"numberOfPosts":   1000,
				"numberOfShares":  1000,
				"favouriteTagIds": []string{"tag1", "tag1", "tag3"},
			},
		})

		CreateCommunityPost([]map[string]any{
			{
				"_id":     "x66c31d7d583b1a3281b5c9b9",
				"userId":  "0834567890",
				"status":  globalConstant.COMMUNITY_POST_STATUS_NOT_POSTED,
				"content": "this is post 1",
				"tagIds": []string{
					"tag1",
					"tag2",
				},
				"scheduleTime": globalLib.GetCurrentTime(local.TimeZone).Add(2 * time.Minute),
				"createdAt":    globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1).Add(1 * time.Minute),
			},
			{
				"_id":     "x66c31d7d583b1a3281b5c9b10",
				"userId":  "0834567890",
				"status":  globalConstant.COMMUNITY_POST_STATUS_NOT_POSTED,
				"content": "this is post 2",
				"tagIds": []string{
					"tag1",
				},
				"scheduleTime": globalLib.GetCurrentTime(local.TimeZone).Add(3 * time.Minute),
				"createdAt":    globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1).Add(-5 * time.Second),
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When the service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the database", func() {
					var posts []*modelCommunityPost.CommunityPost
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_COMMUNITY_POST[local.ISO_CODE], bson.M{}, bson.M{"status": 1, "scheduleTime": 1}, &posts)
					for _, post := range posts {
						So(post.Status, ShouldEqual, globalConstant.COMMUNITY_POST_STATUS_NOT_POSTED)
						So(post.ScheduleTime, ShouldNotBeNil)
					}
				})
			})
		})
	})

}
