---
apiVersion: v1
kind: Service
metadata:
  name: go-send-task-vn-v3
  namespace: kong
  labels:
    app: go-send-task-vn-v3
spec:
  ports:
    - port: 80
      targetPort: 21100
      name: http
    - port: 81
      targetPort: 21101
      name: grpc
  selector:
    app: go-send-task-vn-v3

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: go-send-task-vn-v3
  namespace: kong
  labels:
    app: go-send-task-vn-v3
spec:
  replicas: 1
  selector:
    matchLabels:
      app: go-send-task-vn-v3
  template:
    metadata:
      labels:
        app: go-send-task-vn-v3
        log-label: "go-services"
    spec:
      containers:
        - name: go-send-task-vn-v3
          image: linhnhdocker/go-push-notification-new-task-service:vn-3.0.0
          imagePullPolicy: "IfNotPresent"
          ports:
            - containerPort: 80
              name: http
            - containerPort: 81
              name: grpc