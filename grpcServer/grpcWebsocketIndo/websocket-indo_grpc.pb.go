// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcWebsocketIndo

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	websocketMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// WebsocketIndoClient is the client API for WebsocketIndo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WebsocketIndoClient interface {
	SendChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	SendSocketMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error)
}

type websocketIndoClient struct {
	cc grpc.ClientConnInterface
}

func NewWebsocketIndoClient(cc grpc.ClientConnInterface) WebsocketIndoClient {
	return &websocketIndoClient{cc}
}

func (c *websocketIndoClient) SendChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcWebsocketIndo.WebsocketIndo/SendChatMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *websocketIndoClient) SendSocketMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcWebsocketIndo.WebsocketIndo/SendSocketMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WebsocketIndoServer is the server API for WebsocketIndo service.
// All implementations must embed UnimplementedWebsocketIndoServer
// for forward compatibility
type WebsocketIndoServer interface {
	SendChatMessage(context.Context, *websocketMessage.WebsocketMessage) (*empty.Empty, error)
	SendSocketMessage(context.Context, *websocketMessage.WebsocketMessage) (*empty.Empty, error)
	mustEmbedUnimplementedWebsocketIndoServer()
}

// UnimplementedWebsocketIndoServer must be embedded to have forward compatible implementations.
type UnimplementedWebsocketIndoServer struct {
}

func (UnimplementedWebsocketIndoServer) SendChatMessage(context.Context, *websocketMessage.WebsocketMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendChatMessage not implemented")
}
func (UnimplementedWebsocketIndoServer) SendSocketMessage(context.Context, *websocketMessage.WebsocketMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSocketMessage not implemented")
}
func (UnimplementedWebsocketIndoServer) mustEmbedUnimplementedWebsocketIndoServer() {}

// UnsafeWebsocketIndoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WebsocketIndoServer will
// result in compilation errors.
type UnsafeWebsocketIndoServer interface {
	mustEmbedUnimplementedWebsocketIndoServer()
}

func RegisterWebsocketIndoServer(s *grpc.Server, srv WebsocketIndoServer) {
	s.RegisterService(&_WebsocketIndo_serviceDesc, srv)
}

func _WebsocketIndo_SendChatMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(websocketMessage.WebsocketMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebsocketIndoServer).SendChatMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcWebsocketIndo.WebsocketIndo/SendChatMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebsocketIndoServer).SendChatMessage(ctx, req.(*websocketMessage.WebsocketMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebsocketIndo_SendSocketMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(websocketMessage.WebsocketMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebsocketIndoServer).SendSocketMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcWebsocketIndo.WebsocketIndo/SendSocketMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebsocketIndoServer).SendSocketMessage(ctx, req.(*websocketMessage.WebsocketMessage))
	}
	return interceptor(ctx, in, info, handler)
}

var _WebsocketIndo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcWebsocketIndo.WebsocketIndo",
	HandlerType: (*WebsocketIndoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendChatMessage",
			Handler:    _WebsocketIndo_SendChatMessage_Handler,
		},
		{
			MethodName: "SendSocketMessage",
			Handler:    _WebsocketIndo_SendSocketMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "websocket-indo.proto",
}
