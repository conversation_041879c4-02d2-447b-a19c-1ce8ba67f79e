import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import _ from 'lodash';
import {
  trackingScheduleStatus,
  revertTimeZone,
  changeTimeZone
} from '/server/helper-function';


const topupEmployee = () => {
  return;
	console.log("Start topupEmployee ...");
  let message = null;
  try {

    const timeInVN = changeTimeZone(new Date());
    const startOfMonth = moment(timeInVN).startOf('month').toDate();
    const endOfMonth = moment(timeInVN).endOf('month').toDate();

    const startDate = revertTimeZone(startOfMonth);
    const endDate = revertTimeZone(endOfMonth);

    // Find all in employee collection
    Meteor.appCollection.BEmployee.find({}).forEach(employee => {
      // On each employee:
      const asker = Meteor.appCollection.users.findOne({
        phone: employee.phone,
        type: 'ASKER',
        status: 'ACTIVE'
      });
      if ( asker ) {
        const existsDeposit = Meteor.appCollection.FATransaction.findOne({
          userId: asker._id,
          'source.name': 'DEPOSIT',
          'source.value': 'BTASKEE_EMPLOYEE_TOPUP',
          date: {$gte: startDate, $lte: endDate},
        });
        if ( !existsDeposit ) {
          const finance = Meteor.appCollection.FinancialAccount.findOne({_id: asker.fAccountId});

          // Top-up new month
		// TODO : check this, make sure the remainder of the previous topup amount was deducted before topup
          Meteor.appCollection.FATransaction.insert({
            userId: asker._id,
            accountType: 'M',
            type: 'D',
            source: {
              name: 'DEPOSIT',
              value: 'BTASKEE_EMPLOYEE_TOPUP',
            },
            amount: employee.quota,
            date: new Date(),
          });

          // Update Financial Account
          Meteor.appCollection.FinancialAccount.update({
            _id: finance._id
          }, {
            $inc: {
              FMainAccount: employee.quota
            }
          });
        }
      }
    });
    

  } catch (e) {
    message = e;
    console.log('================topupEmployee====================');
    console.log(e);
    console.log('====================================');
    
  }
	console.log("Finished topupEmployee ");
  // Tracking
  trackingScheduleStatus({name: 'autoTopupEmployee', nextRunAt: SyncedCron.nextScheduledAtDate('autoTopupEmployee'), message});
};

  Meteor.methods({
    // Auto top-up bTaskee employees
    autoTopupEmployee() {
      SyncedCron.add({
        name: 'autoTopupEmployee',
        schedule(parser) {

          // TEMP at first run
		// TODO : figure out the schedule
          const hourInTimeZoneServer = revertTimeZone(new Date(2016,1,1,7)).getHours();
          return parser.cron(`0 5 ${hourInTimeZoneServer} 1 1/1 ? *`, true);
        },
        job() {
          topupEmployee();
        },
      });
	    console.log("Added autoTopupEmployee cron");
    },
    'runNow.autoTopupEmployee'() {
      topupEmployee();
    },
  });
