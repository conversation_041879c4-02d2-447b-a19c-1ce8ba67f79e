package autoUpgradeMedalForCommunityUser

import (
	"github.com/spf13/cast"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalCommunityConstant "gitlab.com/btaskee/go-services-model-v2/globalConstant/community"
	modelCommunityMedal "gitlab.com/btaskee/go-services-model-v2/grpcmodel/communityMedal"
	modelCommunityUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/communityUser"
)

func checkUserCanUpgradeMedal(medal *modelCommunityMedal.CommunityMedal, user *modelCommunityUsers.CommunityUser) bool {
	var isCanUpgrade bool
	if user != nil && medal != nil && medal.Conditions != nil && len(medal.Conditions) > 0 {
		for _, condition := range medal.Conditions {
			amount := cast.ToInt32(condition.Amount)
			switch condition.Type {
			case globalConstant.COMMUNITY_MEDAL_CONDITION_TYPE_NUMBER_OF_LIKES:
				return checkOperators(condition.Operator, amount, user.NumberOfLikes)
			case globalConstant.COMMUNITY_MEDAL_CONDITION_TYPE_NUMBER_OF_SHARES:
				return checkOperators(condition.Operator, amount, user.NumberOfShares)
			case globalConstant.COMMUNITY_MEDAL_CONDITION_TYPE_NUMBER_OF_POSTS:
				return checkOperators(condition.Operator, amount, user.NumberOfPosts)
			case globalConstant.COMMUNITY_MEDAL_CONDITION_TYPE_NUMBER_OF_SERVICES_BOOKINGS:
				value := countNumberOrderService(user.XId)
				numberOfTask := cast.ToInt32(value)
				return checkOperators(condition.Operator, amount, numberOfTask)
			}
		}
	}

	return isCanUpgrade
}

func checkOperators(operator string, amount, value int32) bool {
	switch operator {
	case globalCommunityConstant.CONDITION_MEDAL_OPERATOR_EQUAL:
		if value == amount {
			return true
		}
	case globalCommunityConstant.CONDITION_MEDAL_OPERATOR_GREATER_THAN:
		if value > amount {
			return true
		}
	case globalCommunityConstant.CONDITION_MEDAL_OPERATOR_GREATER_THAN_OR_EQUAL:
		if value >= amount {
			return true
		}
	}

	return false
}
