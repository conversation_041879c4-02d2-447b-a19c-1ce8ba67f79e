package grpcEventIndo

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcEventIndo"
	"google.golang.org/grpc"
)

func ConnectGRPCEvent(endpoint string) (grpcEventIndo.EventIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcEventIndo.NewEventIndoClient(connect)
	return client, connect, err
}
