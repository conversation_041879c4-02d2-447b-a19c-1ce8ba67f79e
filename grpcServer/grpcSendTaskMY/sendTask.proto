syntax = "proto3";
package grpcSendTaskMY;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcSendTaskMY";

import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask/pushNotificationNewTask.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response/response.proto";

service SendTask {
  rpc NewTask (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-my/new-task"
      body: "*"
    };
  }
  rpc TopTasker (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-my/top-tasker"
      body: "*"
    };
  }
  rpc NewTaskToDistrict (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-my/district"
      body: "*"
    };
  }
  rpc FavTasker (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-my/favorite-tasker"
      body: "*"
    };
  }
  rpc Normal (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-my/normal"
      body: "*"
    };
  }
  rpc FavAndTaskerDistrict (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-my/favorite-and-tasker-district"
      body: "*"
    };
  }
  rpc FavAndTopTasker (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-my/favorite-and-top-tasker"
      body: "*"
    };
  }
  rpc NewTaskToCity (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-my/city"
      body: "*"
    };
  }
  rpc ForceTasker (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-my/force-tasker"
      body: "*"
    };
  }
}