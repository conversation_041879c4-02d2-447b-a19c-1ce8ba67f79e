package grpcAcceptTaskMY

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcAcceptTaskMY"
	"google.golang.org/grpc"
)

func ConnectGRPCAcceptTaskMY(endpoint string) (grpcAcceptTaskMY.AcceptTaskClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcAcceptTaskMY.NewAcceptTaskClient(connect)
	return client, connect, err
}
