#!/bin/bash

YELLOW='\033[1;33m'
NC='\033[0m' # No Color
DOCKER_TAG=go-email-vn-v3-

CURRENT_BRANCH=$(git branch --show-current)
if [[ "$CURRENT_BRANCH" != "sandbox" ]]; then
  echo ""
  echo -e "${YELLOW}Checkout qua branch sandbox để build${NC}"
  exit 1
fi

# Pull code mới
echo -e "${YELLOW}Đang pull code mới...${NC}"
git pull

# Cập nhật build number
CURRENT_BUILD_NUMBER=$(grep -o "$DOCKER_TAG[^ ]*" deploy/k8s/sandbox/deployment_sandbox.yml | cut -d'-' -f5)
NEW_BUILD_NUMBER=$((CURRENT_BUILD_NUMBER+1))
echo -e "${YELLOW}Build number: $NEW_BUILD_NUMBER ${NC}"
sed -i '' "s|$DOCKER_TAG$CURRENT_BUILD_NUMBER|$DOCKER_TAG$NEW_BUILD_NUMBER|" deploy/k8s/sandbox/deployment_sandbox.yml
sed -i '' "s|$DOCKER_TAG$CURRENT_BUILD_NUMBER|$DOCKER_TAG$NEW_BUILD_NUMBER|" .gitlab-ci.yml

# Push code
./push-code.sh