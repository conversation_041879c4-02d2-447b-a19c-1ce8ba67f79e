package grpcBPointIndo

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcBPointIndo"
	"google.golang.org/grpc"
)

func ConnectGRPCBPoint(endpoint string) (grpcBPointIndo.BPointIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcBPointIndo.NewBPointIndoClient(connect)
	return client, connect, err
}
