package sortTime

import (
	"sort"
	"time"
)

type TimeSlice []time.Time

func Sort(times []time.Time) []time.Time {
	// Create slice of two times
	sort.Sort(TimeSlice(times))
	return times
}

// Forward request for length
func (p TimeSlice) Len() int {
	return len(p)
}

// Define compare
func (p TimeSlice) Less(i, j int) bool {
	return p[i].Before(p[j])
}

// Define swap over an array
func (p TimeSlice) Swap(i, j int) {
	p[i], p[j] = p[j], p[i]
}
