// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcPaymentVN

import (
	context "context"
	businessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	momoTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/momoTransaction"
	paymentMBTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentMBTransaction"
	paymentRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentRequest"
	paymentTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentTransaction"
	refundTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/refundTransaction"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	shopeePayTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/shopeePayTransaction"
	task "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	vatRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/vatRequest"
	zaloPayTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/zaloPayTransaction"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PaymentVNClient is the client API for PaymentVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PaymentVNClient interface {
	SubscriptionPayment(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error)
	ChargePayment(ctx context.Context, in *paymentTransaction.PaymentTransaction, opts ...grpc.CallOption) (*response.Response, error)
	RefundVN(ctx context.Context, in *refundTransaction.RefundTransaction, opts ...grpc.CallOption) (*response.Response, error)
	CancelTikiOrder(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*response.Response, error)
	CompleteTikiOrder(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*response.Response, error)
	CheckTransactionStatusShopeePayVN(ctx context.Context, in *shopeePayTransaction.ShopeePayTransaction, opts ...grpc.CallOption) (*response.Response, error)
	CheckTransactionStatusMomo(ctx context.Context, in *momoTransaction.MomoTransaction, opts ...grpc.CallOption) (*response.Response, error)
	CheckTransactionStatusZaloPay(ctx context.Context, in *zaloPayTransaction.ZaloPayTransaction, opts ...grpc.CallOption) (*response.Response, error)
	CreateGiftByComboVoucherVN(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error)
	CreateVNVatRequest(ctx context.Context, in *vatRequest.VatRequest, opts ...grpc.CallOption) (*response.Response, error)
	CheckTransactionStatusMB(ctx context.Context, in *paymentMBTransaction.PaymentMBTransaction, opts ...grpc.CallOption) (*response.Response, error)
	GetTransactionsMB(ctx context.Context, in *paymentMBTransaction.GetMBTransactionRequest, opts ...grpc.CallOption) (*paymentMBTransaction.PaymentMBTransactionReport, error)
	GetTransactionsMBTaskerPayToolkit(ctx context.Context, in *paymentMBTransaction.GetMBTransactionRequest, opts ...grpc.CallOption) (*paymentMBTransaction.PaymentMBTransactionReport, error)
	GetTransactionsMBAsker(ctx context.Context, in *paymentMBTransaction.GetMBTransactionRequest, opts ...grpc.CallOption) (*paymentMBTransaction.PaymentMBTransactionReport, error)
	BusinessTopupMember(ctx context.Context, in *businessMember.MemberRequestPayment, opts ...grpc.CallOption) (*response.Response, error)
	BusinessRevokeMember(ctx context.Context, in *businessMember.MemberRequestPayment, opts ...grpc.CallOption) (*response.Response, error)
	PayTaskBybPayBusiness(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error)
	PayComboVoucherVN(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error)
}

type paymentVNClient struct {
	cc grpc.ClientConnInterface
}

func NewPaymentVNClient(cc grpc.ClientConnInterface) PaymentVNClient {
	return &paymentVNClient{cc}
}

func (c *paymentVNClient) SubscriptionPayment(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/SubscriptionPayment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) ChargePayment(ctx context.Context, in *paymentTransaction.PaymentTransaction, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/ChargePayment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) RefundVN(ctx context.Context, in *refundTransaction.RefundTransaction, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/RefundVN", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) CancelTikiOrder(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/CancelTikiOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) CompleteTikiOrder(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/CompleteTikiOrder", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) CheckTransactionStatusShopeePayVN(ctx context.Context, in *shopeePayTransaction.ShopeePayTransaction, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/CheckTransactionStatusShopeePayVN", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) CheckTransactionStatusMomo(ctx context.Context, in *momoTransaction.MomoTransaction, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/CheckTransactionStatusMomo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) CheckTransactionStatusZaloPay(ctx context.Context, in *zaloPayTransaction.ZaloPayTransaction, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/CheckTransactionStatusZaloPay", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) CreateGiftByComboVoucherVN(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/CreateGiftByComboVoucherVN", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) CreateVNVatRequest(ctx context.Context, in *vatRequest.VatRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/CreateVNVatRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) CheckTransactionStatusMB(ctx context.Context, in *paymentMBTransaction.PaymentMBTransaction, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/CheckTransactionStatusMB", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) GetTransactionsMB(ctx context.Context, in *paymentMBTransaction.GetMBTransactionRequest, opts ...grpc.CallOption) (*paymentMBTransaction.PaymentMBTransactionReport, error) {
	out := new(paymentMBTransaction.PaymentMBTransactionReport)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/GetTransactionsMB", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) GetTransactionsMBTaskerPayToolkit(ctx context.Context, in *paymentMBTransaction.GetMBTransactionRequest, opts ...grpc.CallOption) (*paymentMBTransaction.PaymentMBTransactionReport, error) {
	out := new(paymentMBTransaction.PaymentMBTransactionReport)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/GetTransactionsMBTaskerPayToolkit", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) GetTransactionsMBAsker(ctx context.Context, in *paymentMBTransaction.GetMBTransactionRequest, opts ...grpc.CallOption) (*paymentMBTransaction.PaymentMBTransactionReport, error) {
	out := new(paymentMBTransaction.PaymentMBTransactionReport)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/GetTransactionsMBAsker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) BusinessTopupMember(ctx context.Context, in *businessMember.MemberRequestPayment, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/BusinessTopupMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) BusinessRevokeMember(ctx context.Context, in *businessMember.MemberRequestPayment, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/BusinessRevokeMember", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) PayTaskBybPayBusiness(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/PayTaskBybPayBusiness", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentVNClient) PayComboVoucherVN(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentVN.PaymentVN/PayComboVoucherVN", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PaymentVNServer is the server API for PaymentVN service.
// All implementations must embed UnimplementedPaymentVNServer
// for forward compatibility
type PaymentVNServer interface {
	SubscriptionPayment(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error)
	ChargePayment(context.Context, *paymentTransaction.PaymentTransaction) (*response.Response, error)
	RefundVN(context.Context, *refundTransaction.RefundTransaction) (*response.Response, error)
	CancelTikiOrder(context.Context, *task.Task) (*response.Response, error)
	CompleteTikiOrder(context.Context, *task.Task) (*response.Response, error)
	CheckTransactionStatusShopeePayVN(context.Context, *shopeePayTransaction.ShopeePayTransaction) (*response.Response, error)
	CheckTransactionStatusMomo(context.Context, *momoTransaction.MomoTransaction) (*response.Response, error)
	CheckTransactionStatusZaloPay(context.Context, *zaloPayTransaction.ZaloPayTransaction) (*response.Response, error)
	CreateGiftByComboVoucherVN(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error)
	CreateVNVatRequest(context.Context, *vatRequest.VatRequest) (*response.Response, error)
	CheckTransactionStatusMB(context.Context, *paymentMBTransaction.PaymentMBTransaction) (*response.Response, error)
	GetTransactionsMB(context.Context, *paymentMBTransaction.GetMBTransactionRequest) (*paymentMBTransaction.PaymentMBTransactionReport, error)
	GetTransactionsMBTaskerPayToolkit(context.Context, *paymentMBTransaction.GetMBTransactionRequest) (*paymentMBTransaction.PaymentMBTransactionReport, error)
	GetTransactionsMBAsker(context.Context, *paymentMBTransaction.GetMBTransactionRequest) (*paymentMBTransaction.PaymentMBTransactionReport, error)
	BusinessTopupMember(context.Context, *businessMember.MemberRequestPayment) (*response.Response, error)
	BusinessRevokeMember(context.Context, *businessMember.MemberRequestPayment) (*response.Response, error)
	PayTaskBybPayBusiness(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error)
	PayComboVoucherVN(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error)
	mustEmbedUnimplementedPaymentVNServer()
}

// UnimplementedPaymentVNServer must be embedded to have forward compatible implementations.
type UnimplementedPaymentVNServer struct {
}

func (UnimplementedPaymentVNServer) SubscriptionPayment(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubscriptionPayment not implemented")
}
func (UnimplementedPaymentVNServer) ChargePayment(context.Context, *paymentTransaction.PaymentTransaction) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChargePayment not implemented")
}
func (UnimplementedPaymentVNServer) RefundVN(context.Context, *refundTransaction.RefundTransaction) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefundVN not implemented")
}
func (UnimplementedPaymentVNServer) CancelTikiOrder(context.Context, *task.Task) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelTikiOrder not implemented")
}
func (UnimplementedPaymentVNServer) CompleteTikiOrder(context.Context, *task.Task) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CompleteTikiOrder not implemented")
}
func (UnimplementedPaymentVNServer) CheckTransactionStatusShopeePayVN(context.Context, *shopeePayTransaction.ShopeePayTransaction) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckTransactionStatusShopeePayVN not implemented")
}
func (UnimplementedPaymentVNServer) CheckTransactionStatusMomo(context.Context, *momoTransaction.MomoTransaction) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckTransactionStatusMomo not implemented")
}
func (UnimplementedPaymentVNServer) CheckTransactionStatusZaloPay(context.Context, *zaloPayTransaction.ZaloPayTransaction) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckTransactionStatusZaloPay not implemented")
}
func (UnimplementedPaymentVNServer) CreateGiftByComboVoucherVN(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGiftByComboVoucherVN not implemented")
}
func (UnimplementedPaymentVNServer) CreateVNVatRequest(context.Context, *vatRequest.VatRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVNVatRequest not implemented")
}
func (UnimplementedPaymentVNServer) CheckTransactionStatusMB(context.Context, *paymentMBTransaction.PaymentMBTransaction) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckTransactionStatusMB not implemented")
}
func (UnimplementedPaymentVNServer) GetTransactionsMB(context.Context, *paymentMBTransaction.GetMBTransactionRequest) (*paymentMBTransaction.PaymentMBTransactionReport, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionsMB not implemented")
}
func (UnimplementedPaymentVNServer) GetTransactionsMBTaskerPayToolkit(context.Context, *paymentMBTransaction.GetMBTransactionRequest) (*paymentMBTransaction.PaymentMBTransactionReport, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionsMBTaskerPayToolkit not implemented")
}
func (UnimplementedPaymentVNServer) GetTransactionsMBAsker(context.Context, *paymentMBTransaction.GetMBTransactionRequest) (*paymentMBTransaction.PaymentMBTransactionReport, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTransactionsMBAsker not implemented")
}
func (UnimplementedPaymentVNServer) BusinessTopupMember(context.Context, *businessMember.MemberRequestPayment) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BusinessTopupMember not implemented")
}
func (UnimplementedPaymentVNServer) BusinessRevokeMember(context.Context, *businessMember.MemberRequestPayment) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BusinessRevokeMember not implemented")
}
func (UnimplementedPaymentVNServer) PayTaskBybPayBusiness(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayTaskBybPayBusiness not implemented")
}
func (UnimplementedPaymentVNServer) PayComboVoucherVN(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PayComboVoucherVN not implemented")
}
func (UnimplementedPaymentVNServer) mustEmbedUnimplementedPaymentVNServer() {}

// UnsafePaymentVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PaymentVNServer will
// result in compilation errors.
type UnsafePaymentVNServer interface {
	mustEmbedUnimplementedPaymentVNServer()
}

func RegisterPaymentVNServer(s *grpc.Server, srv PaymentVNServer) {
	s.RegisterService(&_PaymentVN_serviceDesc, srv)
}

func _PaymentVN_SubscriptionPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentRequest.PaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).SubscriptionPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/SubscriptionPayment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).SubscriptionPayment(ctx, req.(*paymentRequest.PaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_ChargePayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentTransaction.PaymentTransaction)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).ChargePayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/ChargePayment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).ChargePayment(ctx, req.(*paymentTransaction.PaymentTransaction))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_RefundVN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(refundTransaction.RefundTransaction)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).RefundVN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/RefundVN",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).RefundVN(ctx, req.(*refundTransaction.RefundTransaction))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_CancelTikiOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(task.Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).CancelTikiOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/CancelTikiOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).CancelTikiOrder(ctx, req.(*task.Task))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_CompleteTikiOrder_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(task.Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).CompleteTikiOrder(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/CompleteTikiOrder",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).CompleteTikiOrder(ctx, req.(*task.Task))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_CheckTransactionStatusShopeePayVN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(shopeePayTransaction.ShopeePayTransaction)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).CheckTransactionStatusShopeePayVN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/CheckTransactionStatusShopeePayVN",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).CheckTransactionStatusShopeePayVN(ctx, req.(*shopeePayTransaction.ShopeePayTransaction))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_CheckTransactionStatusMomo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(momoTransaction.MomoTransaction)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).CheckTransactionStatusMomo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/CheckTransactionStatusMomo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).CheckTransactionStatusMomo(ctx, req.(*momoTransaction.MomoTransaction))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_CheckTransactionStatusZaloPay_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(zaloPayTransaction.ZaloPayTransaction)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).CheckTransactionStatusZaloPay(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/CheckTransactionStatusZaloPay",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).CheckTransactionStatusZaloPay(ctx, req.(*zaloPayTransaction.ZaloPayTransaction))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_CreateGiftByComboVoucherVN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentRequest.PaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).CreateGiftByComboVoucherVN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/CreateGiftByComboVoucherVN",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).CreateGiftByComboVoucherVN(ctx, req.(*paymentRequest.PaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_CreateVNVatRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(vatRequest.VatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).CreateVNVatRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/CreateVNVatRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).CreateVNVatRequest(ctx, req.(*vatRequest.VatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_CheckTransactionStatusMB_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentMBTransaction.PaymentMBTransaction)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).CheckTransactionStatusMB(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/CheckTransactionStatusMB",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).CheckTransactionStatusMB(ctx, req.(*paymentMBTransaction.PaymentMBTransaction))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_GetTransactionsMB_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentMBTransaction.GetMBTransactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).GetTransactionsMB(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/GetTransactionsMB",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).GetTransactionsMB(ctx, req.(*paymentMBTransaction.GetMBTransactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_GetTransactionsMBTaskerPayToolkit_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentMBTransaction.GetMBTransactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).GetTransactionsMBTaskerPayToolkit(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/GetTransactionsMBTaskerPayToolkit",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).GetTransactionsMBTaskerPayToolkit(ctx, req.(*paymentMBTransaction.GetMBTransactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_GetTransactionsMBAsker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentMBTransaction.GetMBTransactionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).GetTransactionsMBAsker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/GetTransactionsMBAsker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).GetTransactionsMBAsker(ctx, req.(*paymentMBTransaction.GetMBTransactionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_BusinessTopupMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(businessMember.MemberRequestPayment)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).BusinessTopupMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/BusinessTopupMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).BusinessTopupMember(ctx, req.(*businessMember.MemberRequestPayment))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_BusinessRevokeMember_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(businessMember.MemberRequestPayment)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).BusinessRevokeMember(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/BusinessRevokeMember",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).BusinessRevokeMember(ctx, req.(*businessMember.MemberRequestPayment))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_PayTaskBybPayBusiness_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentRequest.PaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).PayTaskBybPayBusiness(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/PayTaskBybPayBusiness",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).PayTaskBybPayBusiness(ctx, req.(*paymentRequest.PaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentVN_PayComboVoucherVN_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentRequest.PaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentVNServer).PayComboVoucherVN(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentVN.PaymentVN/PayComboVoucherVN",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentVNServer).PayComboVoucherVN(ctx, req.(*paymentRequest.PaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PaymentVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcPaymentVN.PaymentVN",
	HandlerType: (*PaymentVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SubscriptionPayment",
			Handler:    _PaymentVN_SubscriptionPayment_Handler,
		},
		{
			MethodName: "ChargePayment",
			Handler:    _PaymentVN_ChargePayment_Handler,
		},
		{
			MethodName: "RefundVN",
			Handler:    _PaymentVN_RefundVN_Handler,
		},
		{
			MethodName: "CancelTikiOrder",
			Handler:    _PaymentVN_CancelTikiOrder_Handler,
		},
		{
			MethodName: "CompleteTikiOrder",
			Handler:    _PaymentVN_CompleteTikiOrder_Handler,
		},
		{
			MethodName: "CheckTransactionStatusShopeePayVN",
			Handler:    _PaymentVN_CheckTransactionStatusShopeePayVN_Handler,
		},
		{
			MethodName: "CheckTransactionStatusMomo",
			Handler:    _PaymentVN_CheckTransactionStatusMomo_Handler,
		},
		{
			MethodName: "CheckTransactionStatusZaloPay",
			Handler:    _PaymentVN_CheckTransactionStatusZaloPay_Handler,
		},
		{
			MethodName: "CreateGiftByComboVoucherVN",
			Handler:    _PaymentVN_CreateGiftByComboVoucherVN_Handler,
		},
		{
			MethodName: "CreateVNVatRequest",
			Handler:    _PaymentVN_CreateVNVatRequest_Handler,
		},
		{
			MethodName: "CheckTransactionStatusMB",
			Handler:    _PaymentVN_CheckTransactionStatusMB_Handler,
		},
		{
			MethodName: "GetTransactionsMB",
			Handler:    _PaymentVN_GetTransactionsMB_Handler,
		},
		{
			MethodName: "GetTransactionsMBTaskerPayToolkit",
			Handler:    _PaymentVN_GetTransactionsMBTaskerPayToolkit_Handler,
		},
		{
			MethodName: "GetTransactionsMBAsker",
			Handler:    _PaymentVN_GetTransactionsMBAsker_Handler,
		},
		{
			MethodName: "BusinessTopupMember",
			Handler:    _PaymentVN_BusinessTopupMember_Handler,
		},
		{
			MethodName: "BusinessRevokeMember",
			Handler:    _PaymentVN_BusinessRevokeMember_Handler,
		},
		{
			MethodName: "PayTaskBybPayBusiness",
			Handler:    _PaymentVN_PayTaskBybPayBusiness_Handler,
		},
		{
			MethodName: "PayComboVoucherVN",
			Handler:    _PaymentVN_PayComboVoucherVN_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "paymentVN.proto",
}
