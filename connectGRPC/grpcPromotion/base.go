package grpcPromotion

import (
	"gitlab.com/btaskee/go-promotion-service-v2/grpcPromotion"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"google.golang.org/grpc"
)

func ConnectGRPCPromotion(endpoint string) (grpcPromotion.PromotionClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPromotion.NewPromotionClient(connect)
	return client, connect, err
}
