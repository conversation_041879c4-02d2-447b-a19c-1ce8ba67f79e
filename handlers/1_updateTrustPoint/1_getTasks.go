package updateTrustPoint

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func getTasks() []*modelTask.Task {

	now := globalLib.GetCurrentTime(local.TimeZone)
	beforeTime := globalLib.StartADay(now.AddDate(0, 0, -1))
	afterTime := globalLib.EndADay(now.AddDate(0, 0, -1))

	//get all task with status DONE, CANCELED, add trust point for user
	taskCondition := bson.M{
		"$or": []bson.M{
			{"status": globalConstant.TASK_STATUS_DONE},
			{
				"status": globalConstant.TASK_STATUS_CANCELED,
				"cancellationReason": bson.M{"$in": []string{
					globalConstant.CANCELLATION_REASON_ASKER_BUSY,
					globalConstant.CANCELLATION_REASON_BACKEND_CANCEL,
					globalConstant.CANCELLATION_REASON_ASKER_DONT_NEED_ANYMORE,
					globalConstant.CANCELLATION_REASON_POSTED_WRONG_DATE,
					globalConstant.CANCELLATION_REASON_NO_TASKER_ACCEPT,
				},
				},
			},
		},
		"isoCode":   local.ISO_CODE,
		"updatedAt": bson.M{"$gte": beforeTime, "$lte": afterTime},
	}

	var tasks []*modelTask.Task
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		taskCondition,
		bson.M{
			"askerId":            1,
			"status":             1,
			"updatedAt":          1,
			"createdAt":          1,
			"cancellationReason": 1,
			"cancellationText":   1,
		},
		&tasks,
	)
	return tasks
}
