package grpcScheduleMY

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcScheduleMY"
	"google.golang.org/grpc"
)

func ConnectGRPCSchedule(endpoint string) (grpcScheduleMY.ScheduleClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcScheduleMY.NewScheduleClient(connect)
	return client, connect, err
}
