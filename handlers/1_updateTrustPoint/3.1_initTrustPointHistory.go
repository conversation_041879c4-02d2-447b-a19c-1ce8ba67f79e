package updateTrustPoint

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

const MAX_POINT int32 = 100
const MIN_POINT int32 = 0
const DEFAULT_TRUST_POINT int32 = 80 //medium default

func initTrustPointHistory(user *modelUser.Users) string {
	objTrustPoint := &modelUser.UsersTrustPoint{
		Point: DEFAULT_TRUST_POINT,
		Rated: calculateAvg(DEFAULT_TRUST_POINT),
	}

	if user.TrustPoint == nil {
		modelUser.UpdateOneById(local.ISO_CODE,
			user.XId,
			bson.M{"$set": bson.M{"trustPoint": objTrustPoint}},
		)
	}
	//check TrustPointHistory
	trustPointHistoryExist, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_TRUST_POINT_HISTORY[local.ISO_CODE], bson.M{"userId": user.XId})
	if !trustPointHistoryExist {
		now := globalLib.GetCurrentTime(local.TimeZone)
		id := globalLib.GenerateObjectId()
		globalDataAccess.InsertOne(globalCollection.COLLECTION_TRUST_POINT_HISTORY[local.ISO_CODE],
			bson.M{
				"_id":       id,
				"userId":    user.XId,
				"createdAt": now,
				"updatedAt": now,
			})
		return id
	}
	return ""
}
