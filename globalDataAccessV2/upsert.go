package globalDataAccessV2

import (
	"context"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcDataAccess"
	modelDataAccess "gitlab.com/btaskee/go-services-model-v2/grpcmodel/dataAccess"
	"go.mongodb.org/mongo-driver/bson"
)

func (d *DataAccess) UpsertOneById(collectionName, id string, data interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(DATA_ACCESS_URL[d.isoCode][MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	b, err := EncodeData(data)
	if err != nil {
		return err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Id: id, Data: b}
	_, err = client.UpsertOneById(context.Background(), request)
	return err
}

func (d *DataAccess) UpsertOneByQuery(collectionName string, query, data interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(DATA_ACCESS_URL[d.isoCode][MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(REGISTRY, query)
	if err != nil {
		return err
	}
	byteData, err := EncodeData(data)
	if err != nil {
		return err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Data: byteData}
	_, err = client.UpsertOneByQuery(context.Background(), request)
	return err
}
