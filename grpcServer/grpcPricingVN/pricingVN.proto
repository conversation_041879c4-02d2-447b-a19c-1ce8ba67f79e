syntax = "proto3";
package grpcPricingVN;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPricingVN";

import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingrequest/pricing-request.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse/pricing-response.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionRequest/pricing-subscription-request.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionResponse/pricing-subscription-response.proto";

service PricingVN {
  rpc GetPricingWashingMachine (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/washing-machine"
      body: "*"
    };
  }
  rpc GetPricingAirConditioner (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/air-conditioner"
      body: "*"
    };
  }
  rpc GetPricingHomeCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/home-cleaning"
      body: "*"
    };
  }
  rpc GetPricingSubscription (pricingSubscriptionRequest.PricingSubscriptionRequest) returns (pricingSubscriptionResponse.PricingSubscriptionResponse) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/subscription"
      body: "*"
    };
  }
  rpc GetPricingDeepCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/deep-cleaning"
      body: "*"
    };
  }
  rpc GetPricingHomeCooking (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/home-cooking"
      body: "*"
    };
  }
  rpc GetPricingLaundry (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/laundry"
      body: "*"
    };
  }
  rpc RePricingLaundry (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/re-pricing-laundry"
      body: "*"
    };
  }
  rpc GetPricingGroceryAssistant (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/grocery-assistant"
      body: "*"
    };
  }
  rpc GetPricingSofa (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/sofa"
      body: "*"
    };
  }
  rpc GetPricingHousekeeping (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/housekeeping"
      body: "*"
    };
  }
  rpc GetPricingElderlyCare (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/elderly-care"
      body: "*"
    };
  }
  rpc GetPricingPatientCare (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/patient-care"
      body: "*"
    };
  }
  rpc GetPricingDisinfection (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/disinfection"
      body: "*"
    };
  }
  rpc GetPricingOfficeCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {}
  rpc APIGetPricingOfficeCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/office-cleaning"
      body: "*"
    };
  }
  rpc ReCalculatePricing (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/re-calculate"
      body: "*"
    };
  }
  rpc ReCalculatePricingForIncreaseDuration (pricingrequest.PricingIncreaseDurationRequest) returns (pricingresponse.CostResult) {}
  rpc GetPricingSubscriptionPatientCare (pricingSubscriptionRequest.PricingSubscriptionRequest) returns (pricingSubscriptionResponse.PricingSubscriptionResponse) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/subscription-patient-care"
      body: "*"
    };
  }
  rpc GetPricingSubscriptionElderlyCare (pricingSubscriptionRequest.PricingSubscriptionRequest) returns (pricingSubscriptionResponse.PricingSubscriptionResponse) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/subscription-elderly-care"
      body: "*"
    };
  }
  rpc GetPricingChildCare (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/child-care"
      body: "*"
    };
  }
  rpc GetPricingSubscriptionChildCare (pricingSubscriptionRequest.PricingSubscriptionRequest) returns (pricingSubscriptionResponse.PricingSubscriptionResponse) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/subscription-child-care"
      body: "*"
    };
  }
  rpc GetPricingSubscriptionOfficeCleaning (pricingSubscriptionRequest.PricingSubscriptionRequest) returns (pricingSubscriptionResponse.PricingSubscriptionResponse) {}
  rpc APIGetPricingSubscriptionOfficeCleaning (pricingSubscriptionRequest.PricingSubscriptionRequest) returns (pricingSubscriptionResponse.PricingSubscriptionResponse) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/subscription-office-cleaning"
      body: "*"
    };
  }
  rpc GetPricingWaterHeater (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/water-heater"
      body: "*"
    };
  }
  rpc GetPricingCarpetCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/carpet-cleaning"
      body: "*"
    };
  }
  rpc GetPricingHomeMoving (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/home-moving"
      body: "*"
    };
  }
  rpc RePricingHomeMoving (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/re-home-moving"
      body: "*"
    };
  }
  rpc APIGetPricingTaskDateOptions (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/task-date-options"
      body: "*"
    };
  }
  rpc GetPricingAirConditionerV2 (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/air-conditioner-v2"
      body: "*"
    };
  }
  rpc GetPricingHousekeepingV2 (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/housekeeping-v2"
      body: "*"
    };
  }
  rpc GetPricingIndustrialCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-vn/industrial-cleaning"
      body: "*"
    };
  }
  rpc GetPricingBeautyCare (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v5/pricing-vn/beauty-care"
      body: "*"
    };
  }
  rpc RePricingBeautyCare (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v5/pricing-vn/re-pricing-beauty-care"
      body: "*"
    };
  }
  // pricing makeup
  rpc GetPricingMakeup (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v5/pricing-vn/makeup"
      body: "*"
    };
  }
  // pricing nail
  rpc GetPricingNail (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v5/pricing-vn/nail"
      body: "*"
    };
  }
  // pricing hair styling
  rpc GetPricingHairStyling (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v5/pricing-vn/hair-styling"
      body: "*"
    };
  }
  
  rpc GetPricingIroning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v5/pricing-vn/ironing"
      body: "*"
    };
  }
}