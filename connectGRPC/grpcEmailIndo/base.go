package grpcEmailIndo

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcEmailIndo"
	"google.golang.org/grpc"
)

func ConnectGRPCEmail(endpoint string) (grpcEmailIndo.EmailIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcEmailIndo.NewEmailIndoClient(connect)
	return client, connect, err
}
