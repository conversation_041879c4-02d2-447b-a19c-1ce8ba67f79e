package testing

import (
	"context"
	"fmt"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/local"
	svc "gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalRepo"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

/*
* <PERSON>ô tả:
	- Nếu task.SerivceText.En nằm trong settings.ServicesApplyDistance -> SendNornal -> End
	- Nếu task.TaskPlace.City nằm trong settings.CitiesApplyDistance -> SendNornal -> End
	- Gửi notification tới tasker:
		+ Active tasker
	  + In service channel
	  + Have workingPlaces in district.
	  + Sort by score and get top x (from settings)
	- Nếu list TopTasker < settings.TopTaskersPriorityScore -> Send tới tất cả tasker trong quận -> End
	- Send tới cả asker.FavouriteAsker
* Kết quả:
	- Cập nhật task.Visibility = 2, viewedTaskers
*/

func TestTop_Tasker(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		ResetData()

		Convey("Validate request params if request body is nil", t, func() {
			var req *pushNotificationNewTask.NewTaskRequest
			s := svc.GRPCServer{}
			resp, _ := s.TopTasker(context.Background(), req)

			Convey("Check the response", func() {
				So(resp.StatusCode, ShouldEqual, 400)
				So(resp.Error.Code, ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})

		Convey("Validate request params if service is empty", t, func() {
			req := &pushNotificationNewTask.NewTaskRequest{
				Service: &pushNotificationNewTask.NewTaskRequestService{
					XId: "",
				}}
			s := svc.GRPCServer{}
			resp, _ := s.TopTasker(context.Background(), req)

			Convey("Check the response", func() {
				So(resp.StatusCode, ShouldEqual, 400)
				So(resp.Error.Code, ShouldEqual, lib.ERROR_SERVICE_ID_REQUIRED.ErrorCode)
			})
		})

		Convey("Validate request params if Booking.XId is empty", t, func() {
			req := &pushNotificationNewTask.NewTaskRequest{
				Service: &pushNotificationNewTask.NewTaskRequestService{
					XId: "1111",
				},
				Booking: &pushNotificationNewTask.NewTaskRequestBooking{
					XId: "",
				},
			}
			s := svc.GRPCServer{}
			resp, _ := s.TopTasker(context.Background(), req)

			Convey("Check the response", func() {
				So(resp.StatusCode, ShouldEqual, 400)
				So(resp.Error.Code, ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			})
		})

		Convey("Validate request params if Booking.IsoCode is empty", t, func() {
			req := &pushNotificationNewTask.NewTaskRequest{
				Service: &pushNotificationNewTask.NewTaskRequestService{
					XId: "1111",
				},
				Booking: &pushNotificationNewTask.NewTaskRequestBooking{
					XId:     "111",
					IsoCode: "",
				},
			}
			s := svc.GRPCServer{}
			resp, _ := s.TopTasker(context.Background(), req)

			Convey("Check the response", func() {
				So(resp.StatusCode, ShouldEqual, 400)
				So(resp.Error.Code, ShouldEqual, lib.ERROR_BOOKING_ISOCODE_REQUIRED.ErrorCode)
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		date := now.AddDate(0, 0, 1)
		last7Days := now.AddDate(0, 0, -7)

		CreateUser([]map[string]interface{}{
			{
				"phone": "0981234567",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker01",
			},
			{
				"phone": "0981234500",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234501",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234502",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker02",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234503",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker03",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234504",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234505",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234506",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker06",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234507",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker07",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        2,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234508",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234509",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234510",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234511",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{ // Not top but MALE tasker
				"phone": "0981234512",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        1,
				"lastDoneTask": last7Days,
				"gender":       globalConstant.GENDER_MALE,
			},
			{ // Not top but MALE tasker
				"phone": "0981234513",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        1,
				"lastDoneTask": last7Days,
				"gender":       globalConstant.GENDER_MALE,
			},
			{
				"phone": "0981234514",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker14",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  "VN",
					},
				},
				"score":        550,
				"lastDoneTask": last7Days,
			},
		})

		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "0981234567",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_POSTED,
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quan 1",
				},
				"date": fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
			},
			{ // Tasker busy
				"askerPhone":     "0981234567",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_CONFIRMED,
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quan 1",
				},
				"date": fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0981234502",
					},
				},
			},
		})
		CreateTaskMetadata([]map[string]interface{}{
			{
				"_id":             taskIds[0],
				"excludedTaskers": []string{"0981234514"},
			},
		})
		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"$set": bson.M{"timeInBetweenTask": 30}})

		var sv *modelService.Service
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1}, &sv)
		globalRepo.UpsertOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE],
			bson.M{"serviceId": sv.XId},
			bson.M{"$set": bson.M{
				"taskerList": []string{
					"0981234500",
					"0981234501",
					"0981234502",
					"0981234503",
					"0981234504",
					"0981234505",
					"0981234506",
					"0981234507",
					"0981234508",
					"0981234509",
					"0981234510",
					"0981234511",
					"0981234512",
					"0981234513",
					"0981234514",
				},
			}},
		)

		req := &pushNotificationNewTask.NewTaskRequest{
			Service: &pushNotificationNewTask.NewTaskRequestService{
				XId: sv.XId,
			},
			Booking: &pushNotificationNewTask.NewTaskRequestBooking{
				XId:     taskIds[0],
				IsoCode: local.ISO_CODE,
			},
		}

		Convey("When the service handle the request", t, func() {
			s := svc.GRPCServer{}
			_, err := s.TopTasker(context.Background(), req)

			Convey("Check the database", func() {
				So(err, ShouldBeNil)
				var task *modelTask.Task
				globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskIds[0], bson.M{}, &task)
				So(len(task.ViewedTaskers), ShouldEqual, 10)
				for _, v := range task.ViewedTaskers {
					So(v, ShouldNotBeIn, []string{"0981234502", "0981234507", "0981234514"})
				}
				So(task.Visibility, ShouldEqual, 2)
			})
		})
	})

	// Same as case 2, but this is DEEP_CLEANING service -> TOP_TASKER + MALE tasker
	t.Run("2.1", func(t *testing.T) {
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		date := now.AddDate(0, 0, 1)
		last7Days := now.AddDate(0, 0, -7)

		CreateUser([]map[string]interface{}{
			{
				"phone": "0981234567",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker01",
			},
			{
				"phone": "0981234500",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234501",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234502",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker02",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234503",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker03",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234504",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234505",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234506",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker06",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234507",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker07",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        2,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234508",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234509",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234510",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234511",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{ // Not top but MALE tasker
				"phone": "0981234512",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        1,
				"lastDoneTask": last7Days,
				"gender":       globalConstant.GENDER_MALE,
			},
			{ // Not top but MALE tasker
				"phone": "0981234513",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        1,
				"lastDoneTask": last7Days,
				"gender":       globalConstant.GENDER_MALE,
			},
		})

		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0981234567",
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quan 1",
				},
				"date": fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
			},
			{ // Tasker busy
				"askerPhone":  "0981234567",
				"serviceName": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quan 1",
				},
				"date": fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0981234502",
					},
				},
			},
		})
		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"$set": bson.M{"timeInBetweenTask": 30}})

		var sv *modelService.Service
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"text.en": globalConstant.SERVICE_NAME_DEEP_CLEANING}, bson.M{"_id": 1}, &sv)
		globalRepo.UpsertOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE],
			bson.M{"serviceId": sv.XId},
			bson.M{"$set": bson.M{
				"taskerList": []string{
					"0981234500",
					"0981234501",
					"0981234502",
					"0981234503",
					"0981234504",
					"0981234505",
					"0981234506",
					"0981234507",
					"0981234508",
					"0981234509",
					"0981234510",
					"0981234511",
					"0981234512",
					"0981234513",
				},
			}},
		)

		req := &pushNotificationNewTask.NewTaskRequest{
			Service: &pushNotificationNewTask.NewTaskRequestService{
				XId: sv.XId,
			},
			Booking: &pushNotificationNewTask.NewTaskRequestBooking{
				XId:     taskIds[0],
				IsoCode: local.ISO_CODE,
			},
		}

		Convey("When the service handle the request", t, func() {
			s := svc.GRPCServer{}
			_, err := s.TopTasker(context.Background(), req)

			Convey("Check the database", func() {
				So(err, ShouldBeNil)
				var task *modelTask.Task
				globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskIds[0], bson.M{}, &task)
				So(len(task.ViewedTaskers), ShouldEqual, 12)
				for _, v := range task.ViewedTaskers {
					So(v, ShouldNotBeIn, []string{"0981234502", "0981234507"})
				}
				So(task.Visibility, ShouldEqual, 2)
			})
		})
	})

	t.Run("3", func(t *testing.T) {
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		date := now.AddDate(0, 0, 1)
		last7Days := now.AddDate(0, 0, -7)

		CreateUser([]map[string]interface{}{
			{
				"phone": "0981234567",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker01",
			},
			{
				"phone": "0981234500",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        10,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234501",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":           10,
				"lastDoneTask":    last7Days,
				"isPremiumTasker": true,
			},
			{
				"phone": "0981234502",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker02",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":           10,
				"lastDoneTask":    last7Days,
				"isPremiumTasker": true,
			},
			{
				"phone": "0981234503",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker03",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":           10,
				"lastDoneTask":    last7Days,
				"isPremiumTasker": true,
			},
			{
				"phone": "0981234504",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":           10,
				"lastDoneTask":    last7Days,
				"isPremiumTasker": true,
			},
			{
				"phone": "0981234505",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":           10,
				"lastDoneTask":    last7Days,
				"isPremiumTasker": true,
			},
			{
				"phone": "0981234506",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker06",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":           10,
				"lastDoneTask":    last7Days,
				"isPremiumTasker": true,
			},
			{
				"phone": "0981234507",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker07",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":        2,
				"lastDoneTask": last7Days,
			},
			{
				"phone": "0981234508",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":           10,
				"lastDoneTask":    last7Days,
				"isPremiumTasker": true,
			},
			{
				"phone": "0981234509",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":           10,
				"lastDoneTask":    last7Days,
				"isPremiumTasker": true,
			},
			{
				"phone": "0981234510",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":           10,
				"lastDoneTask":    last7Days,
				"isPremiumTasker": true,
			},
			{
				"phone": "0981234511",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker04",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":           10,
				"lastDoneTask":    last7Days,
				"isPremiumTasker": true,
			},
			{
				"phone": "0981234512",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker12",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"score":           5,
				"lastDoneTask":    last7Days,
				"isPremiumTasker": true,
			},
		})

		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "0981234567",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_POSTED,
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quan 1",
				},
				"date":      fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
				"isPremium": true,
			},
			{ // Tasker busy
				"askerPhone":     "0981234567",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_CONFIRMED,
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quan 1",
				},
				"date": fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0981234502",
					},
				},
			},
		})
		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"$set": bson.M{"timeInBetweenTask": 30}})
		var sv *modelService.Service
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1}, &sv)
		globalRepo.UpsertOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE],
			bson.M{"serviceId": sv.XId},
			bson.M{"$set": bson.M{
				"taskerList": []string{
					"0981234500",
					"0981234501",
					"0981234502",
					"0981234503",
					"0981234504",
					"0981234505",
					"0981234506",
					"0981234507",
					"0981234508",
					"0981234509",
					"0981234510",
					"0981234511",
					"0981234512",
				},
			}},
		)

		req := &pushNotificationNewTask.NewTaskRequest{
			Service: &pushNotificationNewTask.NewTaskRequestService{
				XId: sv.XId,
			},
			Booking: &pushNotificationNewTask.NewTaskRequestBooking{
				XId:     taskIds[0],
				IsoCode: local.ISO_CODE,
			},
		}

		Convey("When the service handle the request", t, func() {
			s := svc.GRPCServer{}
			_, err := s.TopTasker(context.Background(), req)

			Convey("Check the database", func() {
				So(err, ShouldBeNil)
				var task *modelTask.Task
				globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskIds[0], bson.M{}, &task)
				So(len(task.ViewedTaskers), ShouldEqual, 10)
				for _, v := range task.ViewedTaskers {
					So(v, ShouldNotBeIn, []string{"0981234502", "0981234507", "0981234500"})
				}
				So(task.Visibility, ShouldEqual, 2)
				So(task.ChangesHistory[0].From, ShouldEqual, "SYSTEM")
				So(task.ChangesHistory[0].Key, ShouldEqual, "RESEND_FAV_TOPTASKER")
				So(task.ChangesHistory[0].CreatedAt, ShouldNotBeNil)
			})
		})
		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"$unset": bson.M{"timeInBetweenTask": 1}})
	})

}
