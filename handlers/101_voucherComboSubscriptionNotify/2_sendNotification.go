package voucherComboSubscriptionNotify

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucher"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelUserComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userComboVoucher"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"

	"go.mongodb.org/mongo-driver/bson"
)

func sendNotification(userIds []string, mapUserComboVouchers map[string][]*modelUserComboVoucher.UserComboVoucher, mapComboVouchers map[string]*modelComboVoucher.ComboVoucher) {
	var users []*modelUser.Users
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_USERS, bson.M{"_id": bson.M{"$in": userIds}}, bson.M{"_id": 1, "language": 1, "phone": 1}, &users)
	if len(users) == 0 {
		return
	}

	for _, user := range users {
		lang := globalConstant.LANG_VI
		if user.Language != "" {
			lang = user.Language
		}
		userComboVouchers := mapUserComboVouchers[user.XId]
		for _, userComboVoucher := range userComboVouchers {
			var notiInfos []interface{}
			comboVoucher := mapComboVouchers[userComboVoucher.ComboVoucherId]
			comboVoucherTitle := globalLib.LocalizeServiceName(lang, comboVoucher.GetTitle())
			title := localization.GetLocalizeObject("COMBO_VOUCHER_SUBSCRIPTION_EXPIRED_NOTI_TITLE", comboVoucherTitle)
			text := localization.GetLocalizeObject("COMBO_VOUCHER_SUBSCRIPTION_EXPIRED_NOTI_DESCRIPTION", comboVoucherTitle)
			notiInfos = append(notiInfos, &modelNotification.Notification{
				XId:         globalLib.GenerateObjectId(),
				UserId:      user.XId,
				Type:        25,
				Title:       globalLib.LocalizeServiceName(lang, title),
				Description: globalLib.LocalizeServiceName(lang, text),
				NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_USER_BPAY,
				CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
			})

			notiUserIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{
				{
					UserId:   user.XId,
					Language: lang,
				},
			}
			payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
				Type:       25,
				NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_USER_BPAY,
			}
			lib.SendNotification(notiInfos, notiUserIds, title, text, payload, true)

			message := fmt.Sprintf("KH [%s] không đủ bPay để thanh toán cho kỳ hạn Subscription tiếp theo. Xem link chi tiết: [Link navigate đến trang chi tiết trên BE CS]", user.Phone) // update when product provide link
			globalLib.PostToSlack(cfg.SlackToken, globalConstant.COMBO_VOUCHER_SUBSCRIPTION_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
		}
	}
}
