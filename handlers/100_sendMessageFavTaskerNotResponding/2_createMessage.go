package sendMessageFavTaskerNotResponding

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func createMessage(tasks []*modelTask.Task) {

	for _, task := range tasks {
		if !task.ForceTasker.IsSuggestedNewDateOptionByTasker && !task.ForceTasker.IsRejectedByTasker && task.ForceTasker.RemindMessageCreatedAt == nil {
			chatId := getChatConversation(task)
			createMessageForFavChat(task, chatId)
		}
	}
}
func getChatConversation(task *modelTask.Task) string {
	// Check if chatmessage is exists
	query := bson.M{
		"members":     bson.M{"$size": 2}, // Check the array size is 2
		"members._id": bson.M{"$all": []string{task.AskerId, task.ForceTasker.TaskerId}},
	}
	fields := bson.M{"_id": 1}
	chatMessage, err := pkgChatMessage.GetChatConversation(local.ISO_CODE, query, fields)
	if err != nil && err != mongo.ErrNoDocuments {
		msg := fmt.Sprintf("Sync Cron VN - FavChatTaskerNotResponse - Get Chat conversation failed: taskId %s. Error: %s", task.XId, err.Error())
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
		return ""
	}
	if chatMessage == nil {
		return ""
	}
	return chatMessage.XId
}

func createMessageForFavChat(task *modelTask.Task, chatId string) {
	if chatId == "" {
		return
	}
	message := &modelChatMessage.ChatMessageMessages{
		XId:       globalLib.GenerateObjectId(),
		CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
		From:      globalConstant.CHAT_MESSAGE_FROM_SYSTEM,
		ChatId:    chatId,
		Type:      globalConstant.TYPE_MESSAGE_REMIND_AFTER_45_MINUTES_CREATE_TASK,
		TaskRequestData: &modelChatMessage.ChatMessageMessagesTaskRequestData{
			TaskInfo: &modelChatMessage.ChatMessageMessagesTaskRequestDataInfo{
				TaskId:      task.XId,
				District:    task.TaskPlace.District,
				Duration:    task.Duration,
				ServiceText: task.ServiceText,
			},
		},
		MessageBySystem: &modelChatMessage.ChatMessageMessagesMessageBySystem{
			Title:  localization.GetLocalizeObject("BOOK_WITH_FAV_TASKER_NOT_RESPONDING_AFTER_45_MINUTES_TITLE"),
			Text:   localization.GetLocalizeObject("BOOK_WITH_FAV_TASKER_NOT_RESPONDING_AFTER_45_MINUTES_BODY"),
			SendTo: globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_ASKER,
			Key:    globalConstant.KEY_BOOK_WITH_FAV,
			Actions: []*modelChatMessage.ChatMessageMessagesMessageBySystemActions{
				{
					Type:  globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_TYPE_SECONDARY,
					Title: localization.GetLocalizeObject("BUTTON_CANCEL_TASK"),
					Key:   globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_CANCEL_TASK,
				}, {
					Type:  globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_TYPE_PRIMARY,
					Title: localization.GetLocalizeObject("BUTTON_SEND_TO_OTHER_TASKER"),
					Key:   globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_SEND_TO_OTHER_TASKER,
				},
			},
		},
	}
	if task.DateOptions != nil {
		for _, option := range task.DateOptions {
			dateOption := &modelChatMessage.TaskDateOptions{
				XId:  option.XId,
				Date: option.Date,
			}
			message.TaskRequestData.TaskInfo.DateOptions = append(message.TaskRequestData.TaskInfo.DateOptions, dateOption)
		}
	} else {
		dateOption := &modelChatMessage.TaskDateOptions{
			Date: task.Date,
		}
		message.TaskRequestData.TaskInfo.DateOptions = append(message.TaskRequestData.TaskInfo.DateOptions, dateOption)
	}

	err := pkgChatMessage.SendMessageToConversation(local.ISO_CODE, chatId, []*modelChatMessage.ChatMessageMessages{message})
	if err != nil {
		msg := fmt.Sprintf("FavChatTaskerNotResponse - Send Chat Message to conversation failed: taskId %s. Error: %s", task.XId, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
		return
	}

	_, err = globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{"_id": task.XId},
		bson.M{"$set": bson.M{"forceTasker.remindMessageCreatedAt": globalLib.GetCurrentTimestamp(local.TimeZone)}},
	)
	if err != nil {
		msg := fmt.Sprintf("[go-sync-cron-vn-v3] Can not update task: taskId %s. Error: %s", task.XId, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
		return
	}

	var asker *modelUsers.Users
	err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USERS, bson.M{"_id": task.AskerId}, bson.M{"_id": 1, "name": 1, "language": 1}, &asker)
	if err != nil {
		msg := fmt.Sprintf("[go-sync-cron-vn-v3] Can not get user: userId %s. Error: %s", task.AskerId, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
		return
	}
	go lib.SendChatMessageViaSocket(nil, chatId, []string{asker.XId}, message)

	// Send notification to received user
	askerLanguage := globalConstant.LANG_EN
	if asker.Language != "" {
		askerLanguage = asker.Language
	}
	reqNoti := globalLib.FavChatNotiRequest{
		ChatId:       chatId,
		UserId:       asker.XId,
		UserLanguage: askerLanguage,
		IsoCode:      local.ISO_CODE,
		TimeZone:     local.TimeZone,
	}
	notiResponse := globalLib.NotiForFavChat(reqNoti)
	if notiResponse != nil {
		go lib.SendNotification(notiResponse.ArrayNotification, notiResponse.UserIds, notiResponse.Title, notiResponse.Body, notiResponse.Payload, true)
	}
}
