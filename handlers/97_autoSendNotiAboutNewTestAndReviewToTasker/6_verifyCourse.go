package autoSendNotiAboutNewTestAndReviewToTasker

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTrainingV2 "gitlab.com/btaskee/go-services-model-v2/grpcmodel/trainingTaskerV2"
	"go.mongodb.org/mongo-driver/bson"
)

func verifyCourse(course *modelTrainingV2.Course, taskerId string) bool {
	if len(course.RelatedServices) > 0 {
		var serviceIds []string
		for _, relatedService := range course.RelatedServices {
			serviceIds = append(serviceIds, relatedService.XId)
		}
		queryIsExistInServiceChannel := bson.M{
			"serviceId":  bson.M{"$in": serviceIds},
			"taskerList": taskerId,
		}
		isExistTasker, err := handlers.IsExistTaskerInServiceChannel(queryIsExistInServiceChannel)
		if err != nil {
			text := fmt.Sprintf("AutoSendNotiAboutTestDeadline %s: Lỗi call API SendNotiAboutTestDeadline , err: %s ", local.ISO_CODE, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], text)
			return false
		} else if !isExistTasker {
			return false
		}
	}

	if course.Condition != nil {
		if course.Condition.CoursesMustBeCompleted != nil && len(course.Condition.CoursesMustBeCompleted.CourseIds) >= 0 {
			vQuery := bson.M{
				"taskerId":   taskerId,
				"status":     globalConstant.TRAINING_TEST_SUBMIT_PASSED,
				"course._id": bson.M{"$in": course.Condition.CoursesMustBeCompleted.CourseIds},
			}
			isExistSubmission := handlers.IsExistSubmissionByQuery(vQuery)
			if !isExistSubmission {
				return false
			}
		} else if course.Condition.ManuallyUnblock != nil && len(course.Condition.ManuallyUnblock.TaskerIdsAllowed) >= 0 {
			flag := false
			for _, tId := range course.Condition.ManuallyUnblock.TaskerIdsAllowed {
				if tId == taskerId {
					flag = true
					break
				}
			}
			if !flag {
				return false
			}
		}
	}
	return true
}
