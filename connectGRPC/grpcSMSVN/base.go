package grpcSMSVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcSMSVN"
	"google.golang.org/grpc"
)

func ConnectGRPCSMSVN(endpoint string) (grpcSMSVN.SMSVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcSMSVN.NewSMSVNClient(connect)
	return client, connect, err
}
