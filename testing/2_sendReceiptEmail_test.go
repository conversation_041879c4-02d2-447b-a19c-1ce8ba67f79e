package testing

import (
	"context"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
)

func TestSendReceiptEmail(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		Convey("Given a request to validate send receipt email", t, func() {
			req := &modelTask.Task{
				Duration: 1,
			}
			<PERSON>vey("Check the response if task empty", func() {
				s := service.Server{}
				res, _ := s.SendReceiptEmail(context.Background(), req)

				So(res.Message, ShouldEqual, lib.ERROR_EMAIL_RECEIPT_TASK_EMPTY)
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"emails": []map[string]interface{}{
					{
						"address":  "<EMAIL>",
						"verified": true,
					},
				},
			},
		})
		taskIds := CreateTask([]map[string]interface{}{
			{
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"askerPhone":  "0834567890",
				"description": "Task 01",
				"originCurrency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"costDetail": map[string]interface{}{
					"cost":      200000,
					"finalCost": 190000,
					"vat":       19000,
					"totalCost": 209000,
				},
				"newCostDetail": map[string]interface{}{
					"cost":      400000,
					"finalCost": 390000,
					"vat":       39000,
					"totalCost": 429000,
				},
			},
		})
		Convey("Given a request to check send receipt email", t, func() {
			req := &modelTask.Task{
				XId: taskIds[0],
			}
			Convey("Check the response if email content nil (no such file directory)", func() {
				s := service.Server{}
				res, _ := s.SendReceiptEmail(context.Background(), req)
				So(res, ShouldNotBeNil)
				So(res.Message, ShouldEqual, lib.ERROR_CONTENT_NIL)
			})
		})
	})
}
