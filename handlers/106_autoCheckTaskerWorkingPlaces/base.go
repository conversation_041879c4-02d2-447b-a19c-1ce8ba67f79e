package autoCheckTaskerWorkingPlaces

import (
	"fmt"
	"log"

	"github.com/robfig/cron/v3"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

var c *cron.Cron
var cfg = config.GetConfig()
var isRunning = false
var paramsEnough = true
var runAt string

var (
	DISTRICT_COUNT_TO_POST_SLACK_NOTIFICATION = map[string]int{
		// <2 -> Post slack notification
		"Long An":  2,
		"Bắ<PERSON>nh": 2,

		// Not post slack notification
		"Thừa Thiên Huế": 1,
		"Hưng <PERSON>ê<PERSON>":       1,
		"Vũng Tàu":       1,
		"Bắc Giang":      1,
		"<PERSON>ình Định":      1,
		"<PERSON>uảng Ninh":     1,
		"Khánh Hòa":      1,
		"Quảng Nam":      1,
		"Nghệ An":        1,
		"Thanh Hóa":      1,

		// Còn lại sẽ default là 3
	}
)

func init() {
	var cronConfig map[string]interface{}
	if cfg != nil {
		cronConfig = cfg.ScheduleSyncCron["autochecktaskerworkingplaces"]
	}

	// Check if params enough
	if cronConfig["run_at"] == nil {
		paramsEnough = false
		lib.PostToSlackNotStart("AutoCheckTaskerWorkingPlaces")
		return
	}

	// Get runAt string for sync cron
	runAt = cronConfig["run_at"].(string)

	// Check if config has is_run = true
	if cronConfig["is_run"] == nil || !cronConfig["is_run"].(bool) {
		lib.PostToSlackNotStart("AutoCheckTaskerWorkingPlaces")
		return
	}

	StartCron()
}

func StartCron() {
	c := cron.New(cron.WithLocation(local.TimeZone))
	c.AddFunc(runAt, AutoCheckTaskerWorkingPlaces)
	c.Start()
	isRunning = true
}

func Run(action int) interface{} {
	// action == 1 -> Run sync cron once now
	if action == lib.RUN_NOW {
		return process()
	}
	// action == 2 -> Start sync cron with schedule config
	if action == lib.START && paramsEnough && !isRunning {
		StartCron()
	}
	// action == 3 -> Stop running sync cron
	if action == lib.STOP && isRunning {
		c.Stop()
		c = nil
		isRunning = false
	}

	return nil
}

func AutoCheckTaskerWorkingPlaces() {
	log.Println("Start AutoCheckTaskerWorkingPlaces Process")
	defer log.Println("Finish AutoCheckTaskerWorkingPlaces Process")

	process()
}

func process() interface{} {
	// Get all taskers
	taskers, errList := getTaskers()
	if len(errList) > 0 {
		msg := fmt.Sprintf("[%s] AutoCheckTaskerWorkingPlaces. Error when get taskers list: %v", local.SERVICE_NAME, errList)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
		return nil
	}

	// Convert data to slack message
	data, slackMsg, totalTaskerLessThan3Area := convertDataToSlackMsg(taskers)
	if slackMsg != "" {
		msg := fmt.Sprintf("%v Tasker có ÍT khu vực làm việc %s", totalTaskerLessThan3Area, slackMsg)
		go globalLib.PostToSlack(cfg.SlackToken, "vn-tasker-check-working-place", globalConstant.SLACK_USER_NAME, msg)
	}

	return map[string]interface{}{
		"totalTaskerLessThan3Area": totalTaskerLessThan3Area,
		"data":                     data,
	}
}
