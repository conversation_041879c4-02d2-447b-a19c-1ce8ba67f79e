package grpcBNPL

import (
	"context"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcBNPLIndo"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcBNPLMY"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcBNPLTH"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcBNPLVN"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerBNPLRequest"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/emptypb"
)

type BNPLClient interface {
	ChargeTaskerBNPLFee(ctx context.Context, in *taskerBNPLRequest.TaskerBNPLRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

func Connect(isoCode, endpoint string) (BNPLClient, *grpc.ClientConn, error) {
	switch isoCode {
	case globalConstant.ISO_CODE_VN:
		return ConnectGRPCBNPLVN(endpoint)
	case globalConstant.ISO_CODE_TH:
		return ConnectGRPCBNPLTH(endpoint)
	case globalConstant.ISO_CODE_INDO:
		return ConnectGRPCBNPLIndo(endpoint)
	case globalConstant.ISO_CODE_MY:
		return ConnectGRPCBNPLMY(endpoint)
	}
	return nil, nil, nil
}

func ConnectGRPCBNPLVN(endpoint string) (grpcBNPLVN.BNPLVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcBNPLVN.NewBNPLVNClient(connect)
	return client, connect, err
}

func ConnectGRPCBNPLTH(endpoint string) (grpcBNPLTH.BNPLTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcBNPLTH.NewBNPLTHClient(connect)
	return client, connect, err
}

func ConnectGRPCBNPLIndo(endpoint string) (grpcBNPLIndo.BNPLIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcBNPLIndo.NewBNPLIndoClient(connect)
	return client, connect, err
}

func ConnectGRPCBNPLMY(endpoint string) (grpcBNPLMY.BNPLClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcBNPLMY.NewBNPLClient(connect)
	return client, connect, err
}
