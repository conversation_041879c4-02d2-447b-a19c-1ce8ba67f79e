# Subscription Processing Flow

## 📋 Overview
This document explains the clear step-by-step processing flow for subscription handling, showing how files are organized and executed.

## 🔄 Execution Flow

```
📁 base.go (Entry Point)
│
├── 🚀 runSubscription()
    │
    └── 📋 STEP 1: runVNSubscription() [File 5]
        │
        ├── 🔧 Get subscription settings from DB
        ├── 🔍 Query active subscriptions (NEW, ACTIVE)
        ├── 📊 Process each subscription:
        │   │
        │   ├── 🆕 NEW subscriptions → handleNewSubscription()
        │   │   └── ⏰ Check expiration → Mark as EXPIRED
        │   │
        │   └── ✅ ACTIVE subscriptions → handleActiveSubscription()
        │       │
        │       └── 📝 STEP 2: postSubscriptionTask() [File 4]
        │           │
        │           ├── 🏷️  STEP 3: mapServiceName() [File 1]
        │           ├── 💰 STEP 4: priceOfTaskInSubscription() [File 2]
        │           ├── 👤 Get user information
        │           ├── 📄 STEP 6: fillTaskDescription() [File 6]
        │           ├── 💾 Insert task into database
        │           ├── 🎯 Handle tasker assignment:
        │           │   │
        │           │   ├── 🔒 Force Accept → STEP 5: canTaskerAccept() [File 3]
        │           │   │   └── 📲 sendConfirmationNotification()
        │           │   │
        │           │   └── 🌐 Normal → STEP 7: sendTaskToTasker() [File 7]
        │           │
        │           ├── 📱 STEP 8: sendNotificationToAsker() [File 8]
        │           └── ✔️  checkSubscriptionCompletion()
        │
        └── 🔔 handleRenewalNotification()
            └── 📧 sendRenewalNotification()
```

## 📁 File Organization & Purpose

### Main Entry Point
- **`base.go`** - Cron job management and entry point

### Core Processing (Execution Order)
1. **`5_runVNSubscription.go`** - **MAIN LOGIC**: Subscription processing orchestration
2. **`4_postSubscriptionTask.go`** - **TASK CREATION**: Task posting and management

### Utility Functions (Called by Core)
3. **`1_mapServiceName.go`** - Service name mapping
4. **`2_priceOfTaskInSubscription.go`** - Price calculation
5. **`3_canTaskerAccept.go`** - Tasker validation
6. **`6_fillTaskDescription.go`** - Task description generation
7. **`7_sendTaskToTasker.go`** - Task assignment
8. **`8_sendNotificationToAsker.go`** - User notifications

### Support Files
- **`utils.go`** - Common utilities
- **`*.md`** - Documentation

## 🎯 Detailed Step Breakdown

### STEP 1: Main Processing (`5_runVNSubscription.go`)
**Purpose**: Orchestrate entire subscription processing
**Key Functions**:
- `runVNSubscription()` - Main entry point
- `processSubscription()` - Process individual subscription
- `handleNewSubscription()` - Handle unpaid subscriptions
- `handleActiveSubscription()` - Handle active subscriptions
- `handleRenewalNotification()` - Send renewal notifications

**Flow**:
1. Get subscription settings from database
2. Query all active subscriptions (status: NEW, ACTIVE)
3. Process each subscription based on status
4. Handle errors and post to Slack

### STEP 2: Task Creation (`4_postSubscriptionTask.go`)
**Purpose**: Create and post tasks from subscriptions
**Key Functions**:
- `postSubscriptionTask()` - Main task creation
- `getUserInfo()` - Get user data
- `insertTask()` - Save task to database
- `handleTaskerAssignment()` - Assign to taskers
- `sendConfirmationNotification()` - Notify taskers
- `checkSubscriptionCompletion()` - Check if done

**Flow**:
1. Create task data structure
2. Calculate pricing and get user info
3. Set task fields and metadata
4. Insert task into database
5. Handle tasker assignment (force or normal)
6. Send notifications
7. Check subscription completion

### STEP 3: Service Mapping (`1_mapServiceName.go`)
**Purpose**: Map service names for consistency
**Key Functions**:
- `mapServiceName()` - Convert service names

### STEP 4: Price Calculation (`2_priceOfTaskInSubscription.go`)
**Purpose**: Calculate task pricing with discounts
**Key Functions**:
- `priceOfTaskInSubscription()` - Main pricing logic
- `isSameDateTime()` - Date comparison utility

### STEP 5: Tasker Validation (`3_canTaskerAccept.go`)
**Purpose**: Validate tasker availability
**Key Functions**:
- `canTaskerAccept()` - Check if tasker can accept

### STEP 6: Task Description (`6_fillTaskDescription.go`)
**Purpose**: Generate task descriptions
**Key Functions**:
- `fillTaskDescription()` - Create description text

### STEP 7: Task Assignment (`7_sendTaskToTasker.go`)
**Purpose**: Send tasks to available taskers
**Key Functions**:
- `sendTaskToTasker()` - Assign task to taskers

### STEP 8: User Notifications (`8_sendNotificationToAsker.go`)
**Purpose**: Notify users about task posting
**Key Functions**:
- `sendNotificationToAsker()` - Send push notifications

## 🔧 Configuration Flow

```
Config File → base.go → runVNSubscription() → Settings
│
├── run_at: "0 */5 * * * *"     → Cron schedule
├── is_run: true                → Enable/disable
├── postTaskBefore: 2           → Days before to post
├── renewBefore: 8              → Days before to renew
└── expiredDays: 2              → Days to expire
```

## 📊 Data Flow

```
Database → Subscriptions → Processing → Tasks → Notifications
│
├── 🔍 Query active subscriptions
├── 📋 Process each subscription
├── 💰 Calculate pricing
├── 📝 Create task data
├── 💾 Insert task
├── 👤 Assign to taskers
└── 📱 Send notifications
```

## ⚡ Performance Considerations

1. **Batch Processing**: Process multiple subscriptions efficiently
2. **Database Optimization**: Minimal queries with proper indexing
3. **Async Notifications**: Non-blocking notification sending
4. **Error Isolation**: One failed subscription doesn't stop others
5. **Memory Management**: Process subscriptions one by one

## 🛠️ Error Handling Strategy

1. **Validation**: Check data before processing
2. **Logging**: Detailed logs for debugging
3. **Slack Alerts**: Critical errors posted to Slack
4. **Graceful Degradation**: Continue processing on non-critical errors
5. **Retry Logic**: Built into notification system

## 📈 Monitoring & Tracking

1. **Cron Status**: Track job execution
2. **Processing Metrics**: Count processed subscriptions
3. **Task Creation**: Monitor task posting success
4. **Notification Delivery**: Track notification success rates
5. **Error Rates**: Monitor and alert on failures

## 🔄 Typical Execution Sequence

1. **Cron Trigger** → `base.go:runSubscription()`
2. **Main Processing** → `5_runVNSubscription.go:runVNSubscription()`
3. **For each subscription**:
   - **Task Creation** → `4_postSubscriptionTask.go:postSubscriptionTask()`
   - **Service Mapping** → `1_mapServiceName.go:mapServiceName()`
   - **Price Calculation** → `2_priceOfTaskInSubscription.go:priceOfTaskInSubscription()`
   - **User Info** → Get from database
   - **Task Description** → `6_fillTaskDescription.go:fillTaskDescription()`
   - **Database Insert** → Save task
   - **Tasker Assignment**:
     - Force Accept → `3_canTaskerAccept.go:canTaskerAccept()`
     - Normal → `7_sendTaskToTasker.go:sendTaskToTasker()`
   - **Notifications** → `8_sendNotificationToAsker.go:sendNotificationToAsker()`
   - **Completion Check** → Update subscription status

This flow ensures clear separation of concerns and makes the codebase easy to understand and maintain.
