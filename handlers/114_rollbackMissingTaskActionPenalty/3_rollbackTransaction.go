package rollBackMissingTaskActionPenalty

import (
	"errors"
	"fmt"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelFATransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fatransaction"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func rollbackTransactions(transactions []*modelFATransaction.FinancialAccountTransaction, mapUsersById map[string]*modelUsers.Users) ([]string, []string) {
	successIds := make([]string, 0)
	errorIds := make([]string, 0)
	for _, transaction := range transactions {
		// Sleep 200ms để tránh hit db liên tục
		time.Sleep(200 * time.Millisecond)
		rollbackTransactionId, err := rollbackTransaction(transaction, mapUsersById[transaction.GetUserId()])
		// Nếu rollback fail, log từng giao dịch lên slack và xử lý giao dịch tiếp theo
		if err != nil {
			msg := fmt.Sprintf("[go-sync-cron-vn-v3] RollbackMissingTaskActionPenalty - rollbackTransaction: transaction._id: %s, error: %v", transaction.GetXId(), err)
			globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)

			// Thêm vào error để trả về đầu API
			errorIds = append(errorIds, fmt.Sprintf("%s-%v", transaction.GetXId(), err))
			continue
		}
		// Nếu rollback success, add _id của rollback transaction vào successIds: oldTransactionId-rollbackTransactionId
		successIds = append(successIds, fmt.Sprintf("%s-%s", transaction.GetXId(), rollbackTransactionId))
	}
	return successIds, errorIds
}

func rollbackTransaction(transaction *modelFATransaction.FinancialAccountTransaction, user *modelUsers.Users) (string, error) {
	if transaction == nil {
		return "", errors.New("transaction nil")
	}
	if user == nil {
		return "", errors.New("user nil")
	}
	if user.FAccountId == "" {
		return "", errors.New("user.FAccountId empty")
	}
	sourceName := transaction.GetSource().GetName()
	if sourceName == "" {
		return "", errors.New("sourceName empty")
	}

	// 1. Insert rollback transaction
	now_ts := globalLib.GetCurrentTimestamp(local.TimeZone)
	rollbackTransactionSourceName := fmt.Sprintf("ROLLBACK_%s", sourceName)
	rollbackTransaction := &modelFATransaction.FinancialAccountTransaction{
		XId:         globalLib.GenerateObjectId(),
		UserId:      user.GetXId(),
		Type:        globalConstant.FA_TRANSACTION_TYPE_DEPOSIT,
		AccountType: globalConstant.FA_TRANSACTION_ACCOUNT_TYPE_MAIN,
		Amount:      transaction.GetAmount(),
		Source: &modelFATransaction.FATransactionSource{
			Name:  rollbackTransactionSourceName,
			Value: transaction.GetSource().GetValue(),
			ReasonText: &service.ServiceText{
				Vi: "Hoàn phí Không bấm BĐ/KT",
				En: "Hoàn phí Không bấm BĐ/KT",
				Ko: "Hoàn phí Không bấm BĐ/KT",
				Th: "Hoàn phí Không bấm BĐ/KT",
				Id: "Hoàn phí Không bấm BĐ/KT",
				Ms: "Hoàn phí Không bấm BĐ/KT",
			},
		},
		IsoCode:   local.ISO_CODE,
		Date:      now_ts,
		CreatedAt: now_ts,
	}
	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], rollbackTransaction)
	if err != nil {
		return "", fmt.Errorf("create rollback transaction: %v", err)
	}

	// 2. Update user financial account
	updatedCount, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], user.FAccountId, bson.M{"$inc": bson.M{"FMainAccount": transaction.GetAmount()}})
	if err != nil {
		// Nếu update fail (database error), delete transaction được tạo ở trên
		errRollback := globalDataAccess.DeleteOneById(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], rollbackTransaction.GetXId())
		// Nếu delete transaction fail thì add error vào original error
		if errRollback != nil {
			err = fmt.Errorf("original error: %v, rollback transaction: %v", err, errRollback)
		}
		// Trả về error để log bên ngoài
		return "", fmt.Errorf("update user financial account: %v", err)
	}
	if updatedCount == 0 {
		err := fmt.Errorf("update user financial account: updatedCount == 0")
		// Nếu update fail (Không có record được update), delete transaction được tạo ở trên
		errRollback := globalDataAccess.DeleteOneById(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], rollbackTransaction.GetXId())
		// Nếu delete transaction fail thì add error vào original error
		if errRollback != nil {
			err = fmt.Errorf("original error: %v, rollback transaction: %v", err, errRollback)
		}
		return "", err
	}

	// Nếu update success, trả về _id của rollback transaction
	return rollbackTransaction.GetXId(), nil
}
