package sendBusinessReportEmail

import (
	"fmt"
	"net/http"

	"github.com/xuri/excelize/v2"
	"gitlab.com/btaskee/go-email-vn-v3/lib"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
)

func openFile() (*excelize.File, *modelEmailResponse.EmailResponse, error) {
	filePath := fmt.Sprintf("%s/businessReport.xlsx", cfg.FileTemplateURL)
	file, err := excelize.OpenFile(filePath)
	if err != nil {
		return nil, &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusInternalServerError,
			Message:    lib.ERROR_FILE_TEMPLATE_NOT_FOUND,
		}, err
	}
	file.SetDefaultFont("Source Sans Pro")
	return file, nil, nil
}
