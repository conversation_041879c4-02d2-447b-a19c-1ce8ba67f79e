package giveRewardTaskerDoneSpecialCampaign

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelSpecialCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/specialCampaign"
	modelSpecialCampaignTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/specialCampaignTransaction"
	"go.mongodb.org/mongo-driver/bson"
)

// gửi quà cho tasker khi đã qua campaign
func processSendRewardToTasker() error {
	var campaigns []*modelSpecialCampaign.SpecialCampaign
	now := globalLib.GetCurrentTime(local.TimeZone)
	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN[local.ISO_CODE], bson.M{
		"status":  globalConstant.SPECIAL_CAMPAIGN_STATUS_ACTIVE,
		"endDate": bson.M{"$lt": now},
	}, bson.M{"name": 1, "text": 1, "_id": 1, "rewards": 1}, &campaigns)
	if err != nil {
		return err
	}

	if len(campaigns) > 0 {
		for _, campaign := range campaigns {
			var specialCampaignTransaction []*modelSpecialCampaignTransaction.SpecialCampaignTransaction
			err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{
				"campaignId": campaign.XId,
				"status":     globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED,
			}, bson.M{"status": 1, "taskerId": 1, "_id": 1}, &specialCampaignTransaction)
			if err != nil {
				return err
			}
			if len(specialCampaignTransaction) == 0 {
				continue
			}

			for _, v := range specialCampaignTransaction {
				if v.Status != globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED {
					continue
				}
				tasker := getTaskerInfo(v.TaskerId)
				if tasker == nil {
					continue
				}

				err := giveRewardToTasker(tasker, campaign)
				now := globalLib.GetCurrentTime(local.TimeZone)
				if err == nil {
					query := bson.M{
						"$set": bson.M{
							"status":       globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_REWARDED,
							"rewardedDate": now,
						},
						"$push": bson.M{
							"changeHistory": bson.M{
								"createdAt": now,
								"content": bson.M{
									"oldStatus": v.Status,
									"newStatus": globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_REWARDED,
								},
								"from": globalConstant.CHANGES_HISTORY_FROM_SYSTEM,
								"key":  "SEND_REWARD_SPECIAL_CAMPAIGN",
							},
						},
					}
					globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], v.XId, query)
				} else {
					query := bson.M{
						"$set": bson.M{
							"status": globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_FAILED,
						},
						"$push": bson.M{
							"changeHistory": bson.M{
								"createdAt": now,
								"content": bson.M{
									"oldStatus": v.Status,
									"newStatus": globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_FAILED,
								},
								"from": globalConstant.CHANGES_HISTORY_FROM_SYSTEM,
								"key":  "SEND_REWARD_SPECIAL_CAMPAIGN",
							},
						},
					}
					globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], v.XId, query)
				}
			}
		}
	}

	return err
}
