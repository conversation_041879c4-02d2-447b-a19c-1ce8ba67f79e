package cancelTaskForceTasker

import (
	"fmt"
	"log"
	"time"

	"github.com/robfig/cron/v3"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
)

var (
	c            *cron.Cron
	cfg          = config.GetConfig()
	isRunning    = false
	paramsEnough = true
	runAt        string
)

func init() {
	var cronConfig map[string]interface{}
	if cfg != nil {
		cronConfig = cfg.ScheduleSyncCron["autocanceltaskforcetasker"]
	}

	// Check if params enough
	if cronConfig["run_at"] == nil {
		paramsEnough = false
		lib.PostToSlackNotStart("AutoCancelTaskForceTasker")
		return
	}

	// Get runAt string for sync cron
	runAt = fmt.Sprintf(cronConfig["run_at"].(string))

	// Check if config has is_run = true
	if cronConfig["is_run"] == nil || !cronConfig["is_run"].(bool) {
		lib.PostToSlackNotStart("AutoCancelTaskForceTasker")
		return
	}

	StartCron()
}

func StartCron() {
	c := cron.New(cron.WithLocation(local.TimeZone))
	c.AddFunc(runAt, AutoCancelTaskForceTasker)
	c.Start()
	isRunning = true
}

func Run(action int) {
	// action == 1 -> Run sync cron once now
	if action == lib.RUN_NOW {
		AutoCancelTaskForceTasker()
	}
	// action == 2 -> Start sync cron with schedule config
	if action == lib.START && paramsEnough && !isRunning {
		StartCron()
	}
	// action == 3 -> Stop running sync cron
	if action == lib.STOP && isRunning {
		c.Stop()
		c = nil
		isRunning = false
	}
}

func AutoCancelTaskForceTasker() {
	log.Println("Start AutoCancelTaskForceTasker Process")
	defer log.Println("Finish AutoCancelTaskForceTasker Process")
	processCancelTaskPostedLongTime()
	processCancelTaskTaskerRejectedOrSuggestedNewDate()
}

func processCancelTaskPostedLongTime() {
	// 1. get tasks
	tasks, err := getTasksPostedLongTime()
	if err != nil {
		message := fmt.Sprintf("Sync Cron Set Cancel Task Force Tasker. Error get list task: %s", err.Error())
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[globalConstant.ISO_CODE_VN], lib.GO_SERVICE_SYNC_CRON_USER_NAME, message)
	}

	if len(tasks) == 0 {
		return
	}
	var satisfiedTasksCancel, satisfiedTasksResend []*modelTask.Task
	for _, task := range tasks {
		//Only cancel task if task is created 6 hours excluding range 23.00 to 6.00
		if globalLib.CalculateHoursExcludingMidnight(globalLib.ParseDateFromTimeStamp(task.CreatedAt, local.TimeZone), globalLib.GetCurrentTime(local.TimeZone)) >= 6 {
			if !task.ForceTasker.IsResent {
				satisfiedTasksCancel = append(satisfiedTasksCancel, task)
			} else {
				satisfiedTasksResend = append(satisfiedTasksResend, task)
			}
		}
	}

	if len(satisfiedTasksCancel) != 0 {
		err = callGrpcToCancelTask(satisfiedTasksCancel)
		if err != nil {
			message := fmt.Sprintf("Sync Cron Set Cancel Task Force Tasker. Error call grpc backend service: %s", err.Error())
			globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[globalConstant.ISO_CODE_VN], lib.GO_SERVICE_SYNC_CRON_USER_NAME, message)
			return
		}
	}
	if len(satisfiedTasksResend) != 0 {
		// Update task resend
		updateTaskResend(satisfiedTasksResend)

		// Send task to fav tasker
		sendTaskToFavTasker(satisfiedTasksResend)

		//Send message to fav chat
		go initMessageDataForFavChat_TaskResent(satisfiedTasksResend)
	}
}

func processCancelTaskTaskerRejectedOrSuggestedNewDate() {
	// 1. get tasks
	tasks, err := getTasks_TaskerRejectedOrSuggestedNewDate()
	if err != nil {
		message := fmt.Sprintf("Sync Cron Set Cancel Task Force Tasker. Error get list task: %s", err.Error())
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[globalConstant.ISO_CODE_VN], lib.GO_SERVICE_SYNC_CRON_USER_NAME, message)
	}

	if len(tasks) == 0 {
		return
	}
	timeToExpired := globalLib.GetCurrentTime(local.TimeZone).Add(-2 * time.Hour)
	var satisfiedTasksCancel, satisfiedTasksResend []*modelTask.Task

	for _, task := range tasks {
		if len(task.ChangesHistory) == 0 {
			continue
		}
		createdAt := globalLib.ParseDateFromTimeStamp(task.ChangesHistory[0].CreatedAt, local.TimeZone)
		if createdAt.Before(timeToExpired) {
			if !task.ForceTasker.IsResent {
				satisfiedTasksCancel = append(satisfiedTasksCancel, task)
			} else {
				satisfiedTasksResend = append(satisfiedTasksResend, task)
			}
		}
	}

	if len(satisfiedTasksCancel) != 0 {
		err = callGrpcToCancelTask(satisfiedTasksCancel)
		if err != nil {
			message := fmt.Sprintf("Sync Cron Set Cancel Task Force Tasker. Error call grpc backend service: %s", err.Error())
			globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[globalConstant.ISO_CODE_VN], lib.GO_SERVICE_SYNC_CRON_USER_NAME, message)
			return
		}
	}
	if len(satisfiedTasksResend) != 0 {
		// Update task resend
		updateTaskResend(satisfiedTasksResend)

		// Send task to fav tasker
		sendTaskToFavTasker(satisfiedTasksResend)

		//Send message to fav chat
		go initMessageDataForFavChat_TaskResent(satisfiedTasksResend)
	}
}
