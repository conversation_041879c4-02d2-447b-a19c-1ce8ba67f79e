package grpcPushNotificationTH

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPushNotificationTH"
	"google.golang.org/grpc"
)

func ConnectGRPCPushNotificationTH(endpoint string) (grpcPushNotificationTH.PushNotificationTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPushNotificationTH.NewPushNotificationTHClient(connect)
	return client, connect, err
}
