package grpcPaymentMY

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPaymentMY"
	"google.golang.org/grpc"
)

func ConnectGRPCPayment(endpoint string) (grpcPaymentMY.PaymentClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPaymentMY.NewPaymentClient(connect)
	return client, connect, err
}
