package globalRepo

import (
	"context"
	"encoding/json"

	"gitlab.com/btaskee/go-services-model-v2/dbConfig"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func IsExistById(collectionName string, id string) (bool, error) {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	var limit int64 = 1
	count, err := collection.CountDocuments(ctx, bson.M{"_id": id}, &options.CountOptions{Limit: &limit})
	return count > 0, err
}

func IsExistByQuery(collectionName string, query interface{}) (bool, error) {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	var limit int64 = 1
	count, err := collection.CountDocuments(ctx, query, &options.CountOptions{Limit: &limit})
	return count > 0, err
}

func CountByQuery(collectionName string, query interface{}) (int64, error) {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	count, err := collection.CountDocuments(ctx, query)
	return count, err
}

// ======================================== GET ONE

func GetOneById(collectionName string, id string, fields interface{}, result interface{}) error {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	err := collection.FindOne(ctx, bson.M{"_id": id}, &options.FindOneOptions{Projection: fields}).Decode(result)
	return err
}

func GetOneByQuery(collectionName string, query interface{}, fields interface{}, result interface{}) error {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	err := collection.FindOne(ctx, query, &options.FindOneOptions{Projection: fields}).Decode(result)
	return err
}

func GetOneByQuerySort(collectionName string, query interface{}, fields interface{}, sort interface{}, result interface{}) error {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	err := collection.FindOne(ctx, query, &options.FindOneOptions{Projection: fields, Sort: sort}).Decode(result)
	return err
}

func GetOneByQueryMap(collectionName string, query interface{}, fields interface{}) (map[string]interface{}, error) {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	var result map[string]interface{}
	collection := dbConfig.GetCollection(collectionName)
	err := collection.FindOne(ctx, query, &options.FindOneOptions{Projection: fields}).Decode(&result)
	return result, err
}

func GetOneByIdMap(collectionName string, id string, fields interface{}) (map[string]interface{}, error) {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	result := map[string]interface{}{}
	collection := dbConfig.GetCollection(collectionName)
	err := collection.FindOne(ctx, bson.M{"_id": id}, &options.FindOneOptions{Projection: fields}).Decode(result)
	return result, err
}

// ======================================== GET ALL

func GetAllByQuery(collectionName string, query interface{}, fields interface{}, result interface{}) error {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	cur, err := collection.Find(ctx, query, &options.FindOptions{Projection: fields})
	if err != nil {
		return err
	}
	defer cur.Close(ctx)

	return cur.All(ctx, result)
}

func GetAllByQueryMap(collectionName string, query interface{}, fields interface{}) ([]map[string]interface{}, error) {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	cur, err := collection.Find(ctx, query, &options.FindOptions{Projection: fields})
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var result []map[string]interface{}
	for cur.Next(ctx) {
		var item map[string]interface{}
		cur.Decode(&item)
		if item != nil {
			result = append(result, item)
		}
	}
	if len(result) > 0 {
		b, _ := json.Marshal(result)
		json.Unmarshal(b, &result)
	}
	return result, err
}

func GetAllByQueryPagingSort(collectionName string, query interface{}, fields interface{}, page, limit int64, sort interface{}, result interface{}) error {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	skip := (page - 1) * limit
	collection := dbConfig.GetCollection(collectionName)
	cur, err := collection.Find(ctx, query, &options.FindOptions{Projection: fields, Sort: sort, Skip: &skip, Limit: &limit})
	if err != nil {
		return err
	}
	defer cur.Close(ctx)

	return cur.All(ctx, result)
	// var array []map[string]interface{}
	// for cur.Next(ctx) {
	// 	var item map[string]interface{}
	// 	cur.Decode(&item)
	// 	if item != nil {
	// 		array = append(array, item)
	// 	}
	// }
	// if array != nil && len(array) > 0 {
	// 	b, _ := json.Marshal(array)
	// 	json.Unmarshal(b, &result)
	// }
	// return err
}

func GetAllByQuerySort(collectionName string, query, fields, sort interface{}, result interface{}) error {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	cur, err := collection.Find(ctx, query, &options.FindOptions{Projection: fields, Sort: sort})
	if err != nil {
		return err
	}
	defer cur.Close(ctx)

	return cur.All(ctx, result)
	// var array []map[string]interface{}
	// for cur.Next(ctx) {
	// 	var item map[string]interface{}
	// 	cur.Decode(&item)
	// 	if item != nil {
	// 		array = append(array, item)
	// 	}
	// }
	// if array != nil && len(array) > 0 {
	// 	b, _ := json.Marshal(array)
	// 	json.Unmarshal(b, &result)
	// }
	// return err
}

func GetAllByQueryPaging(collectionName string, query interface{}, fields interface{}, page, limit int64, result interface{}) error {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	skip := (page - 1) * limit
	collection := dbConfig.GetCollection(collectionName)
	cur, err := collection.Find(ctx, query, &options.FindOptions{Projection: fields, Skip: &skip, Limit: &limit})
	if err != nil {
		return err
	}
	defer cur.Close(ctx)

	return cur.All(ctx, result)
	// var array []map[string]interface{}
	// for cur.Next(ctx) {
	// 	var item map[string]interface{}
	// 	cur.Decode(&item)
	// 	if item != nil {
	// 		array = append(array, item)
	// 	}
	// }
	// if array != nil && len(array) > 0 {
	// 	b, _ := json.Marshal(array)
	// 	json.Unmarshal(b, &result)
	// }
	// return err
}

func GetAllByQueryLimitSort(collectionName string, query interface{}, fields interface{}, limit int64, sort interface{}, result interface{}) error {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	cur, err := collection.Find(ctx, query, &options.FindOptions{Projection: fields, Limit: &limit, Sort: sort})
	if err != nil {
		return err
	}
	defer cur.Close(ctx)

	return cur.All(ctx, result)
	// var array []map[string]interface{}
	// for cur.Next(ctx) {
	// 	var item map[string]interface{}
	// 	cur.Decode(&item)
	// 	if item != nil {
	// 		array = append(array, item)
	// 	}
	// }
	// if array != nil && len(array) > 0 {
	// 	b, _ := json.Marshal(array)
	// 	json.Unmarshal(b, &result)
	// }
	// return err
}

func GetAllByQuerySortMap(collectionName string, query interface{}, fields interface{}, sort interface{}) ([]map[string]interface{}, error) {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	cur, err := collection.Find(ctx, query, &options.FindOptions{Projection: fields, Sort: sort})
	if err != nil {
		return nil, err
	}
	defer cur.Close(ctx)
	var result []map[string]interface{}
	for cur.Next(ctx) {
		var item map[string]interface{}
		cur.Decode(&item)
		if item != nil {
			result = append(result, item)
		}
	}
	return result, err
}

func GetDistinctByQuery(collectionName string, distinctFieldName string, query interface{}) ([]interface{}, error) {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	cur, err := collection.Distinct(ctx, distinctFieldName, query)
	if err != nil {
		return nil, err
	}
	return cur, nil
}

// ================ Aggregate
func Aggregate(collectionName string, pipeline interface{}, result interface{}) error {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	cur, err := collection.Aggregate(ctx, pipeline)
	if err != nil {
		return err
	}
	defer cur.Close(ctx)

	return cur.All(ctx, result)
}

func GetAllByQueryWithBatchSize(collectionName string, query interface{}, fields interface{}, batchSize int32, result interface{}) error {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	cur, err := collection.Find(ctx, query, &options.FindOptions{Projection: fields, BatchSize: &batchSize})
	if err != nil {
		return err
	}
	defer cur.Close(ctx)

	return cur.All(ctx, result)
}

func GetOneByIdWithContext(ctx context.Context, collectionName string, id string, fields interface{}, result interface{}) error {
	collection := dbConfig.GetCollection(collectionName)
	err := collection.FindOne(ctx, bson.M{"_id": id}, &options.FindOneOptions{Projection: fields}).Decode(result)
	return err
}
