package giveRewardTaskerDoneSpecialCampaign

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelSpecialCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/specialCampaign"
	"go.mongodb.org/mongo-driver/bson"
)

func getSpecialCampaign() (campaigns []*modelSpecialCampaign.SpecialCampaign) {
	now := globalLib.GetCurrentTime(local.TimeZone)
	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN[local.ISO_CODE], bson.M{
		"status":    globalConstant.SPECIAL_CAMPAIGN_STATUS_ACTIVE,
		"startDate": bson.M{"$lte": now},
		"endDate":   bson.M{"$gte": now},
	}, bson.M{"name": 1, "text": 1, "type": 1, "status": 1, "rewards": 1, "startDate": 1, "endDate": 1, "value": 1, "city": 1, "taskerJourneyLevels": 1, "minRateTask": 1, "applyForServices": 1}, &campaigns)
	if err != nil {
		return
	}

	return
}
