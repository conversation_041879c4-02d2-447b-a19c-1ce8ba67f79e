package grpcWebsocket

import (
	"context"
	"encoding/json"

	"github.com/golang/protobuf/ptypes/empty"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcWebsocketIndo"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcWebsocketMY"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage"
	"gitlab.com/btaskee/go-websocket-service-v2/grpcWebsocket"
	"go.uber.org/zap"
	"google.golang.org/grpc"
)

type WebsocketClient interface {
	SendChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	SendSocketMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error)
}

func Connect(isoCode, endpoint string) (WebsocketClient, *grpc.ClientConn, error) {
	switch isoCode {
	case globalConstant.ISO_CODE_VN, globalConstant.ISO_CODE_TH:
		return ConnectGRPCWebsocketVN(endpoint)
	case globalConstant.ISO_CODE_INDO:
		return ConnectGRPCWebsocketIndo(endpoint)
	case globalConstant.ISO_CODE_MY:
		return ConnectGRPCWebsocketMY(endpoint)
	}
	return nil, nil, nil
}

func ConnectGRPCWebsocketVN(endpoint string) (grpcWebsocket.WebsocketClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcWebsocket.NewWebsocketClient(connect)
	return client, connect, err
}

func ConnectGRPCWebsocketIndo(endpoint string) (grpcWebsocketIndo.WebsocketIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcWebsocketIndo.NewWebsocketIndoClient(connect)
	return client, connect, err
}

func ConnectGRPCWebsocketMY(endpoint string) (grpcWebsocketIndo.WebsocketIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcWebsocketMY.NewWebsocketClient(connect)
	return client, connect, err
}

func SendSocketChatMessage(isoCode, endpoint, chatId string, messageTo []string, message *modelChatMessage.ChatMessageMessages, task *modelTask.Task) (*websocketMessage.WebsocketMessage, error) {
	return SendSocketChatMessageV2("", isoCode, endpoint, chatId, messageTo, message, task)
}

// Send socket message with language
func SendSocketChatMessageV2(language, isoCode, endpoint, chatId string, messageTo []string, message *modelChatMessage.ChatMessageMessages, task *modelTask.Task) (*websocketMessage.WebsocketMessage, error) {
	messageMap := chatMessage.MapChatMessageMessageForTaskerByLanguage(language, message, task)
	t := globalResponse.Translate(messageMap)
	data, _ := json.Marshal(t)
	request := &websocketMessage.WebsocketMessage{
		Key:       globalConstant.WEBSOCKET_KEY_CHAT,
		Source:    "",
		Value:     chatId,
		Data:      data,
		MessageTo: messageTo,
		Version:   globalConstant.API_VERSION_V2,
		IsoCode:   isoCode,
	}

	client, connect, err := Connect(isoCode, endpoint)
	if err != nil {
		globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
			zap.Error(err),
			zap.String("endpoint", endpoint),
			zap.Any("messageTo", messageTo),
			zap.Any("data", data),
		)
		return request, err
	}
	defer connect.Close()

	_, err = client.SendSocketMessage(context.Background(), request)
	if err != nil {
		globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
			zap.Error(err),
			zap.Any("body", request),
		)
		return request, err
	}
	return request, nil
}

func SendSocketUpdateChatMessage(isoCode, endpoint, chatId string, messageTo []string, message map[string]interface{}) (*websocketMessage.WebsocketMessage, error) {
	data, _ := json.Marshal(message)
	request := &websocketMessage.WebsocketMessage{
		Key:       globalConstant.WEBSOCKET_KEY_CHAT,
		Source:    "",
		Value:     chatId,
		Data:      data,
		MessageTo: messageTo,
		Version:   globalConstant.API_VERSION_V2,
		IsoCode:   isoCode,
		Action:    globalConstant.CHAT_MESSAGE_ACTION_UPDATE_CHAT_MESSAGE,
	}

	client, connect, err := Connect(isoCode, endpoint)
	if err != nil {
		globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
			zap.Error(err),
			zap.String("endpoint", endpoint),
			zap.Any("messageTo", messageTo),
			zap.Any("data", data),
		)
		return request, err
	}
	defer connect.Close()

	_, err = client.SendSocketMessage(context.Background(), request)
	if err != nil {
		globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
			zap.Error(err),
			zap.Any("body", request),
		)
		return request, err
	}
	return request, nil
}
