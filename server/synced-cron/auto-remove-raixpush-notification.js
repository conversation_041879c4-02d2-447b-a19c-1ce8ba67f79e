import { Meteor } from 'meteor/meteor';
import {
  trackingScheduleStatus,
} from '/server/helper-function';

import moment from 'moment';

const runRemoveOldRaixPushNotification = () => {
  var message = null;

  const last3Mins = moment().subtract(3, 'minutes').toDate();
  Push.notifications.remove({
    sent: false,
    createdAt: {$lt: last3Mins}
  });

  // Tracking
  trackingScheduleStatus({name: 'autoRemoveOldRaixPushNotification', nextRunAt: SyncedCron.nextScheduledAtDate('autoRemoveOldRaixPushNotification'), message});
};

export default function () {
  Meteor.methods({
    // Auto Done Task
    autoRemoveOldRaixPushNotification() {
      SyncedCron.add({
        name: 'autoRemoveOldRaixPushNotification',
        schedule(parser) {
          // Run every 5 minutes
          return parser.cron('0 0/5 * 1/1 * ? *', true);
        },
        job() {
          runRemoveOldRaixPushNotification();
        },
      });
    },
    'runNow.autoRemoveOldRaixPushNotification'() {
      runRemoveOldRaixPushNotification();
    },
  });
}
