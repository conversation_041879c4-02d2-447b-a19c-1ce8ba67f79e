//Package service file router
/*
 * @File: routes.go
 * @Description: Handler Func New Router
 * @CreatedAt: 22/02/2021
 * @Author: vinhnt
 */
package service

import (
	"github.com/gorilla/mux"
)

// NewRouter func
func NewRouter() *mux.Router {
	router := mux.NewRouter().StrictSlash(true)

	apiGroup := router.PathPrefix("/api/v3/sync-cron-vn").Subrouter()
	for _, route := range routes {
		apiGroup.Methods(route.Method).Path(route.Pattern).Name(route.Name).Handler(route.HandlerFunc)
	}

	return router
}
