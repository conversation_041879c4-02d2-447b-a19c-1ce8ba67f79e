package giveRewardTaskerDoneSpecialCampaign

import (
	"fmt"
	"log"

	"github.com/robfig/cron/v3"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
)

var (
	c            *cron.Cron
	cfg          = config.GetConfig()
	isRunning    = false
	paramsEnough = true
	runAt        string
	LIMIT        int64 = 2000
)

func init() {
	var cronConfig map[string]interface{}
	if cfg != nil {
		cronConfig = cfg.ScheduleSyncCron["giverewardtaskerdonespecialcampaign"]
	}
	// Check if params enough
	if cronConfig["run_at"] == nil {
		paramsEnough = false
		lib.PostToSlackNotStart("GiveRewardTaskerDoneSpecialCampaign")
		return
	}

	// Get runAt string for sync cron
	runAt = fmt.Sprintf(cronConfig["run_at"].(string))

	// Check if config has is_run = true
	if cronConfig["is_run"] == nil || !cronConfig["is_run"].(bool) {
		lib.PostToSlackNotStart("GiveRewardTaskerDoneSpecialCampaign")
		return
	}

	// Start cron if config["is_run"] = true
	StartCron()
}

func StartCron() {
	c := cron.New(cron.WithLocation(local.TimeZone))
	c.AddFunc(runAt, autoRun)
	c.Start()
	isRunning = true
}

func Run(reqBody *model.RequestAction) map[string]interface{} {
	// action == 1 -> Run sync cron once now
	if reqBody.Action == lib.RUN_NOW {
		giveRewardTaskerDoneSpecialCampaign()
	}

	// action == 2 -> Start sync cron with schedule config
	if reqBody.Action == lib.START && paramsEnough && !isRunning {
		StartCron()
	}
	// action == 3 -> Stop running sync cron
	if reqBody.Action == lib.STOP && isRunning {
		c.Stop()
		c = nil
		isRunning = false
	}
	return nil
}

func autoRun() {
	log.Println("Start GiveRewardTaskerDoneSpecialCampaign Process")
	defer log.Println("Finish GiveRewardTaskerDoneSpecialCampaign Process")
	giveRewardTaskerDoneSpecialCampaign()
}

/*
Description:
	1. Lấy danh sách special campaign với đúng điều kiện (status active, thời gian còn trong thời gian hiệu lực)
	2. Lấy sách sách tasker đủ điều hiện để đua special campaign
	3. Chạy vòng lặp xử lý từng tasker trong từng special campaign:
		1. Kiểm tra xem tasker đã win 100% campaign (đánh dấu complete campaign) special của tasker hay chưa -> Nếu rồi thì bỏ qua tasker này
		2. Kiểm tra xem tasker đã win 30% or 50% => gửi notify tới user, 100% đánh dấu complete campaign.
	4. Lấy list campaign đã exprided & danh sách tasker đã compeled campaign => gửi reward cho tasker
*/

func giveRewardTaskerDoneSpecialCampaign() {
	log.Println("Start giveRewardTaskerDoneSpecialCampaign Process")
	defer log.Println("Finish giveRewardTaskerDoneSpecialCampaign Process")

	campaign := getSpecialCampaign()
	if len(campaign) > 0 {
		taskerInfos := getTasker()
		for _, specialCampaign := range campaign {
			if specialCampaign != nil {
				for _, tasker := range taskerInfos {
					// Convert timestamp to time.Time
					startDate := globalLib.ParseDateFromTimeStamp(specialCampaign.StartDate, local.TimeZone)
					endDate := globalLib.ParseDateFromTimeStamp(specialCampaign.EndDate, local.TimeZone)

					// kiểm tra journey level tasker có đủ điều kiện tham gia campaign không
					if len(specialCampaign.TaskerJourneyLevels) > 0 && tasker.JourneyInfo == nil &&
						globalLib.FindStringInSlice(specialCampaign.TaskerJourneyLevels, lib.JOURNEY_YOUNG_BE) == -1 {

						continue
					} else if len(specialCampaign.TaskerJourneyLevels) > 0 && tasker.JourneyInfo != nil &&
						globalLib.FindStringInSlice(specialCampaign.TaskerJourneyLevels, tasker.JourneyInfo.Level) == -1 {
						continue
					}

					if tasker.CompanyInfo != nil {
						// tasker là đối tác thì không được tham gia campaign
						continue
					}

					isWin, isInProcess, err := isTaskerWinOrInProcessCampaign(tasker.XId, specialCampaign.XId)
					if err != nil && err != mongo.ErrNoDocuments {
						local.Logger.Info("[SYN CRON VN V3 - giveRewardTaskerDoneSpecialCampaign] error check task in process or win campaign", zap.Error(err))
						continue
					}
					if isWin {
						continue
					}

					// Get total referral code used and total task done in range date
					var totalReferralCodeUsed, totalTaskDone int32
					switch specialCampaign.Type {
					case globalConstant.SPECIAL_CAMPAIGN_TYPE_REFERRAL_CAMPAIGN:
						totalReferralCodeUsed = processCountReferralCodeUsedInRangeDate(tasker.ReferralCode, startDate, endDate)
						if totalReferralCodeUsed > 0 {
							err := processMarkDoneAndNotiToTasker(tasker, specialCampaign, totalReferralCodeUsed, isInProcess)
							if err != nil {
								local.Logger.Info("[SYN CRON VN V3 - giveRewardTaskerDoneSpecialCampaign] error processMarkDoneAndNotiToTasker type referral campaign", zap.Error(err))
							}
						}
					case globalConstant.SPECIAL_CAMPAIGN_TYPE_TASK_CAMPAIGN:
						totalTaskDone = checkConditionTaskerDoneTask(tasker.XId, startDate, endDate, specialCampaign)
						if totalTaskDone > 0 {
							err := processMarkDoneAndNotiToTasker(tasker, specialCampaign, totalTaskDone, isInProcess)
							if err != nil {
								local.Logger.Info("[SYN CRON VN V3 - giveRewardTaskerDoneSpecialCampaign] error processMarkDoneAndNotiToTasker type task campaign", zap.Error(err))
							}
						}
					}
				}
			}
		}
	}

	// processSendRewardToTasker
	err := processSendRewardToTasker()
	if err != nil {
		local.Logger.Info("[SYN CRON VN V3 - giveRewardTaskerDoneSpecialCampaign] error send reward to tasker", zap.Error(err))
	}
}
