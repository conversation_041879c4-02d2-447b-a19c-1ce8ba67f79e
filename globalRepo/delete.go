package globalRepo

import (
	"context"

	"gitlab.com/btaskee/go-services-model-v2/dbConfig"
	"go.mongodb.org/mongo-driver/bson"
)

func DeleteAllByQuery(collectionName string, query interface{}) error {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	_, err := collection.DeleteMany(ctx, query)
	return err
}

func DeleteOneById(collectionName string, id string) error {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	_, err := collection.DeleteOne(ctx, bson.M{"_id": id})
	return err
}

func DeleteOneByQuery(collectionName string, query interface{}) error {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	_, err := collection.DeleteOne(ctx, query)
	return err
}
