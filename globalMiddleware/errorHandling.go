package globalMiddleware

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"runtime/debug"

	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.uber.org/zap"
)

func ErrorHandlingMiddleware(isoCode string, logger *zap.Logger, slackToken string) func(next http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		fn := func(w http.ResponseWriter, r *http.Request) {
			// Read request body
			reqBody, _ := io.ReadAll(r.Body)
			r.Body = io.NopCloser(bytes.NewBuffer(reqBody))

			// Logic here
			defer func() {
				if rc := recover(); rc != nil {
					// log terminal
					logger.Warn("PANIC_ERROR",
						zap.String("url", r.RequestURI),
						zap.Any("stack", string(debug.Stack())),
						zap.Any("error", rc),
						zap.String("body", string(reqBody)),
					)

					// Log slack
					go globalLib.PostToSlack(slackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[isoCode], globalConstant.SLACK_USER_NAME, fmt.Sprintf("PANIC_ERROR\n%v\n%v\n%v", r.RequestURI, rc, string(debug.Stack())))

					// Return error to client
					internalServerError := globalResponse.ResponseErrorCode{
						StatusCode: http.StatusInternalServerError,
						ErrorCode:  "UNKNOWN_ERROR",
						Message:    "An error occurred. Please try again!",
					}
					globalResponse.ResponseError(w, internalServerError)
				}
			}()

			// Call the next handler
			next.ServeHTTP(w, r)
		}

		return http.HandlerFunc(fn)
	}
}

func AppRecover(isoCode string, logger *zap.Logger, slackToken string) {
	if rc := recover(); rc != nil {
		// log terminal
		logger.Warn("PANIC_ERROR",
			zap.Any("stack", string(debug.Stack())),
			zap.Any("error", rc),
		)

		globalLib.PostToSlack(slackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[isoCode], globalConstant.SLACK_USER_NAME, fmt.Sprintf("PANIC_ERROR\n%v\n%v", rc, string(debug.Stack())))
	}
}
