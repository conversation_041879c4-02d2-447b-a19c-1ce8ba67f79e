// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcPromotionIndo

import (
	context "context"
	promotionResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotionResponse"
	signUpPromotionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/signUpPromotionRequest"
	task "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PromotionIndoClient is the client API for PromotionIndo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PromotionIndoClient interface {
	CheckPostTaskPromotion(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error)
	CheckSignUpPromotion(ctx context.Context, in *signUpPromotionRequest.SignUpPromotionRequest, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error)
}

type promotionIndoClient struct {
	cc grpc.ClientConnInterface
}

func NewPromotionIndoClient(cc grpc.ClientConnInterface) PromotionIndoClient {
	return &promotionIndoClient{cc}
}

func (c *promotionIndoClient) CheckPostTaskPromotion(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error) {
	out := new(promotionResponse.PromotionResponse)
	err := c.cc.Invoke(ctx, "/grpcPromotionIndo.PromotionIndo/CheckPostTaskPromotion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionIndoClient) CheckSignUpPromotion(ctx context.Context, in *signUpPromotionRequest.SignUpPromotionRequest, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error) {
	out := new(promotionResponse.PromotionResponse)
	err := c.cc.Invoke(ctx, "/grpcPromotionIndo.PromotionIndo/CheckSignUpPromotion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PromotionIndoServer is the server API for PromotionIndo service.
// All implementations must embed UnimplementedPromotionIndoServer
// for forward compatibility
type PromotionIndoServer interface {
	CheckPostTaskPromotion(context.Context, *task.Task) (*promotionResponse.PromotionResponse, error)
	CheckSignUpPromotion(context.Context, *signUpPromotionRequest.SignUpPromotionRequest) (*promotionResponse.PromotionResponse, error)
	mustEmbedUnimplementedPromotionIndoServer()
}

// UnimplementedPromotionIndoServer must be embedded to have forward compatible implementations.
type UnimplementedPromotionIndoServer struct {
}

func (UnimplementedPromotionIndoServer) CheckPostTaskPromotion(context.Context, *task.Task) (*promotionResponse.PromotionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPostTaskPromotion not implemented")
}
func (UnimplementedPromotionIndoServer) CheckSignUpPromotion(context.Context, *signUpPromotionRequest.SignUpPromotionRequest) (*promotionResponse.PromotionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckSignUpPromotion not implemented")
}
func (UnimplementedPromotionIndoServer) mustEmbedUnimplementedPromotionIndoServer() {}

// UnsafePromotionIndoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PromotionIndoServer will
// result in compilation errors.
type UnsafePromotionIndoServer interface {
	mustEmbedUnimplementedPromotionIndoServer()
}

func RegisterPromotionIndoServer(s *grpc.Server, srv PromotionIndoServer) {
	s.RegisterService(&_PromotionIndo_serviceDesc, srv)
}

func _PromotionIndo_CheckPostTaskPromotion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(task.Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionIndoServer).CheckPostTaskPromotion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPromotionIndo.PromotionIndo/CheckPostTaskPromotion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionIndoServer).CheckPostTaskPromotion(ctx, req.(*task.Task))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionIndo_CheckSignUpPromotion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(signUpPromotionRequest.SignUpPromotionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionIndoServer).CheckSignUpPromotion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPromotionIndo.PromotionIndo/CheckSignUpPromotion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionIndoServer).CheckSignUpPromotion(ctx, req.(*signUpPromotionRequest.SignUpPromotionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PromotionIndo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcPromotionIndo.PromotionIndo",
	HandlerType: (*PromotionIndoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckPostTaskPromotion",
			Handler:    _PromotionIndo_CheckPostTaskPromotion_Handler,
		},
		{
			MethodName: "CheckSignUpPromotion",
			Handler:    _PromotionIndo_CheckSignUpPromotion_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "promotionIndo.proto",
}
