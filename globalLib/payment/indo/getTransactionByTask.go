package indo

import (
	"errors"
	"fmt"

	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/indo"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalModel/modelTransactionInfo"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/danaTransaction"
	modelPaymentMidtransTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentMidtransTransaction"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentXenditTransaction"
	modelRefundRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/refundRequest"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// ================== TASK_PROCESS
func GetPrepayTransactionInfoByTask_Indo(taskId string, taskPaymentTransactionId string, paymentMethod string, paymentGateway string) (transactionInfo *modelTransactionInfo.TransactionInfo, err error) {
	if paymentGateway == globalConstant.PAYMENT_GATEWAY_DANA {
		return getPrepayTransactionInfoByTaskTransactionId_DANA_INDO(taskId, taskPaymentTransactionId, paymentMethod)
	}

	// get transaction info for suit payment gateway
	if globalLib.FindStringInSlice(MIDTRANS_PREPAY_PAYMENT_METHODS, paymentMethod) >= 0 {
		return getPrepayTransactionInfoByTaskTransactionId_Midtrans_INDO(taskId, taskPaymentTransactionId, paymentMethod)
	}

	if globalLib.FindStringInSlice(XENDIT_PREPAY_PAYMENT_METHOD, paymentMethod) >= 0 {
		return getPrepayTransactionInfoByTaskTransactionId_Xendit_INDO(taskId, taskPaymentTransactionId, paymentMethod)
	}

	// Case payment method not in any payment gateway
	err = errors.New("payment method not implemented")
	return
}

func getPrepayTransactionInfoByTaskTransactionId_Midtrans_INDO(taskId, taskPaymentTransactionId, paymentMethod string) (transactionInfo *modelTransactionInfo.TransactionInfo, err error) {
	pattern := fmt.Sprintf("%s_", globalConstant.PAYMENT_REFERRENCE_PREPAYMENT_TASK)
	var transaction *modelPaymentMidtransTransaction.PaymentMidtransTransaction
	err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_PAYMENT_MIDTRANS_TRANSACTION[globalConstant.ISO_CODE_INDO],
		bson.M{
			"_id":         taskPaymentTransactionId,
			"reference":   bson.M{"$regex": primitive.Regex{Pattern: pattern, Options: "i"}},
			"data.taskId": taskId,
		},
		bson.M{
			"_id":         1,
			"charged":     1,
			"cost":        1,
			"status":      1,
			"data.taskId": 1,
			"isRefunded":  1,
			"createdAt":   1,
			"userId":      1,
			"bank":        1,
		},
		&transaction,
	)
	if err != nil {
		return nil, err
	}
	if transaction == nil {
		return nil, errors.New("transaction not found")
	}

	result := &modelTransactionInfo.TransactionInfo{
		XId:           transaction.XId,
		InvoiceNo:     transaction.XId,
		Status:        transaction.Status,
		Amount:        transaction.Cost,
		Charged:       transaction.Charged,
		TaskId:        taskId,
		Date:          transaction.CreatedAt,
		IsRefunded:    transaction.IsRefunded,
		PaymentMethod: paymentMethod,
		UserId:        transaction.UserId,
		Bank:          transaction.Bank,
		RefundInfo:    getRefundTransactionInfo(taskId, taskPaymentTransactionId, paymentMethod),
	}
	if transaction.Data != nil {
		result.TaskId = transaction.Data.TaskId
	}

	return result, nil
}

func getPrepayTransactionInfoByTaskTransactionId_Xendit_INDO(taskId, taskPaymentTransactionId, paymentMethod string) (transactionInfo *modelTransactionInfo.TransactionInfo, err error) {
	pattern := fmt.Sprintf("%s_", globalConstant.PAYMENT_REFERRENCE_PREPAYMENT_TASK)
	var transaction *paymentXenditTransaction.PaymentXenditTransaction
	err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_PAYMENT_XENDIT_TRANSACTION[globalConstant.ISO_CODE_INDO],
		bson.M{
			"_id":         taskPaymentTransactionId,
			"reference":   bson.M{"$regex": primitive.Regex{Pattern: pattern, Options: "i"}},
			"data.taskId": taskId,
		},
		bson.M{
			"_id":              1,
			"charged":          1,
			"cost":             1,
			"status":           1,
			"data.taskId":      1,
			"paymentRequestId": 1,
			"isRefunded":       1,
			"createdAt":        1,
			"userId":           1,
		},
		&transaction,
	)

	if err != nil {
		return nil, err
	}
	if transaction == nil {
		return nil, errors.New("transaction not found")
	}

	result := &modelTransactionInfo.TransactionInfo{
		XId:           transaction.XId,
		InvoiceNo:     transaction.PaymentRequestId,
		Status:        transaction.Status,
		Amount:        transaction.Cost,
		Charged:       transaction.Charged,
		TaskId:        taskId,
		Date:          transaction.CreatedAt,
		IsRefunded:    transaction.IsRefunded,
		PaymentMethod: paymentMethod,
		UserId:        transaction.UserId,
		RefundInfo:    getRefundTransactionInfo(taskId, taskPaymentTransactionId, paymentMethod),
	}

	return result, nil
}

func getPrepayTransactionInfoByTaskTransactionId_DANA_INDO(taskId, taskPaymentTransactionId, paymentMethod string) (transactionInfo *modelTransactionInfo.TransactionInfo, err error) {
	var transaction *danaTransaction.DanaTransaction
	err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_DANA_TRANSACTION[globalConstant.ISO_CODE_INDO],
		bson.M{
			"_id":         taskPaymentTransactionId,
			"data.taskId": taskId,
		},
		bson.M{
			"_id":              1,
			"charged":          1,
			"cost":             1,
			"status":           1,
			"data.taskId":      1,
			"isRefunded":       1,
			"createdAt":        1,
			"userId":           1,
			"bank":             1,
			"paymentRequestId": 1,
		},
		&transaction,
	)
	if err != nil {
		return nil, err
	}
	if transaction == nil {
		return nil, errors.New("transaction not found")
	}

	result := &modelTransactionInfo.TransactionInfo{
		XId:           transaction.XId,
		InvoiceNo:     transaction.PaymentRequestId,
		Status:        transaction.Status,
		Amount:        transaction.Cost,
		Charged:       transaction.Charged,
		TaskId:        taskId,
		Date:          transaction.CreatedAt,
		IsRefunded:    transaction.IsRefunded,
		PaymentMethod: paymentMethod,
		UserId:        transaction.UserId,
		RefundInfo:    getRefundTransactionInfo(taskId, taskPaymentTransactionId, paymentMethod),

		PaymentGateway: globalConstant.PAYMENT_GATEWAY_DANA,
	}

	return result, nil
}

func getRefundTransactionInfo(taskId, taskPaymentTransactionId, paymentMethod string) []*modelTransactionInfo.RefundInfo {
	var refundRequests []*modelRefundRequest.RefundRequest
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_REFUND_REQUEST[globalConstant.ISO_CODE_INDO],
		bson.M{
			"taskId":        taskId,
			"invoiceNo":     taskPaymentTransactionId,
			"paymentMethod": paymentMethod,
		},
		bson.M{
			"status":        1,
			"amount":        1,
			"cancelTaskFee": 1,
		},
		&refundRequests,
	)
	if refundRequests == nil {
		return nil
	}

	result := make([]*modelTransactionInfo.RefundInfo, 0)
	for _, v := range refundRequests {
		result = append(result, &modelTransactionInfo.RefundInfo{
			Status:       v.Status,
			Date:         v.CreatedAt,
			RefundAmount: v.Amount,
			CancelFee:    v.CancelTaskFee,
			Reason:       v.Reason,
		})
	}
	return result
}
