// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.7.1
// source: sendTaskIndo.proto

package grpcSendTaskIndo

import (
	pushNotificationNewTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_sendTaskIndo_proto protoreflect.FileDescriptor

var file_sendTaskIndo_proto_rawDesc = []byte{
	0x0a, 0x12, 0x73, 0x65, 0x6e, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x64, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x67, 0x72, 0x70, 0x63, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x61,
	0x73, 0x6b, 0x49, 0x6e, 0x64, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x67, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72,
	0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69,
	0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x2f,
	0x70, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x49, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65,
	0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xfa, 0x08, 0x0a, 0x0c, 0x53, 0x65, 0x6e,
	0x64, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x64, 0x6f, 0x12, 0x72, 0x0a, 0x07, 0x4e, 0x65, 0x77,
	0x54, 0x61, 0x73, 0x6b, 0x12, 0x27, 0x2e, 0x70, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x2e, 0x4e,
	0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x22, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x33, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x2d, 0x74, 0x61, 0x73, 0x6b, 0x2d, 0x69, 0x6e, 0x64,
	0x6f, 0x2f, 0x6e, 0x65, 0x77, 0x2d, 0x74, 0x61, 0x73, 0x6b, 0x3a, 0x01, 0x2a, 0x12, 0x76, 0x0a,
	0x09, 0x54, 0x6f, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x12, 0x27, 0x2e, 0x70, 0x75, 0x73,
	0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77,
	0x54, 0x61, 0x73, 0x6b, 0x2e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x22,
	0x21, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x2d, 0x74, 0x61,
	0x73, 0x6b, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x74, 0x6f, 0x70, 0x2d, 0x74, 0x61, 0x73, 0x6b,
	0x65, 0x72, 0x3a, 0x01, 0x2a, 0x12, 0x7c, 0x0a, 0x11, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b,
	0x54, 0x6f, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x12, 0x27, 0x2e, 0x70, 0x75, 0x73,
	0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77,
	0x54, 0x61, 0x73, 0x6b, 0x2e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x22,
	0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x2d, 0x74, 0x61,
	0x73, 0x6b, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x64, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74,
	0x3a, 0x01, 0x2a, 0x12, 0x7b, 0x0a, 0x09, 0x46, 0x61, 0x76, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72,
	0x12, 0x27, 0x2e, 0x70, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x2e, 0x4e, 0x65, 0x77, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x31, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x2b, 0x22, 0x26, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x73,
	0x65, 0x6e, 0x64, 0x2d, 0x74, 0x61, 0x73, 0x6b, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x66, 0x61,
	0x76, 0x6f, 0x72, 0x69, 0x74, 0x65, 0x2d, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x3a, 0x01, 0x2a,
	0x12, 0x6f, 0x0a, 0x06, 0x4e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x12, 0x27, 0x2e, 0x70, 0x75, 0x73,
	0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77,
	0x54, 0x61, 0x73, 0x6b, 0x2e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x28, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x22, 0x22,
	0x1d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x2d, 0x74, 0x61,
	0x73, 0x6b, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x6e, 0x6f, 0x72, 0x6d, 0x61, 0x6c, 0x3a, 0x01,
	0x2a, 0x12, 0x93, 0x01, 0x0a, 0x14, 0x46, 0x61, 0x76, 0x41, 0x6e, 0x64, 0x54, 0x61, 0x73, 0x6b,
	0x65, 0x72, 0x44, 0x69, 0x73, 0x74, 0x72, 0x69, 0x63, 0x74, 0x12, 0x27, 0x2e, 0x70, 0x75, 0x73,
	0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77,
	0x54, 0x61, 0x73, 0x6b, 0x2e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x3e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x38, 0x22,
	0x33, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x2d, 0x74, 0x61,
	0x73, 0x6b, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x66, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74, 0x65,
	0x2d, 0x61, 0x6e, 0x64, 0x2d, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x2d, 0x64, 0x69, 0x73, 0x74,
	0x72, 0x69, 0x63, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x89, 0x01, 0x0a, 0x0f, 0x46, 0x61, 0x76, 0x41,
	0x6e, 0x64, 0x54, 0x6f, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x12, 0x27, 0x2e, 0x70, 0x75,
	0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65,
	0x77, 0x54, 0x61, 0x73, 0x6b, 0x2e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x39, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x33,
	0x22, 0x2e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x2d, 0x74,
	0x61, 0x73, 0x6b, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x66, 0x61, 0x76, 0x6f, 0x72, 0x69, 0x74,
	0x65, 0x2d, 0x61, 0x6e, 0x64, 0x2d, 0x74, 0x6f, 0x70, 0x2d, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x72,
	0x3a, 0x01, 0x2a, 0x12, 0x74, 0x0a, 0x0d, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x54, 0x6f,
	0x43, 0x69, 0x74, 0x79, 0x12, 0x27, 0x2e, 0x70, 0x75, 0x73, 0x68, 0x4e, 0x6f, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x2e, 0x4e,
	0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x26, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x20, 0x22, 0x1b, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x33, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x2d, 0x74, 0x61, 0x73, 0x6b, 0x2d, 0x69, 0x6e, 0x64,
	0x6f, 0x2f, 0x63, 0x69, 0x74, 0x79, 0x3a, 0x01, 0x2a, 0x12, 0x7a, 0x0a, 0x0b, 0x46, 0x6f, 0x72,
	0x63, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x12, 0x27, 0x2e, 0x70, 0x75, 0x73, 0x68, 0x4e,
	0x6f, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x54, 0x61,
	0x73, 0x6b, 0x2e, 0x4e, 0x65, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x28, 0x22, 0x23, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x73, 0x65, 0x6e, 0x64, 0x2d, 0x74, 0x61, 0x73, 0x6b,
	0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x66, 0x6f, 0x72, 0x63, 0x65, 0x2d, 0x74, 0x61, 0x73, 0x6b,
	0x65, 0x72, 0x3a, 0x01, 0x2a, 0x42, 0x45, 0x5a, 0x43, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32,
	0x2f, 0x67, 0x72, 0x70, 0x63, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x67, 0x72, 0x70, 0x63,
	0x53, 0x65, 0x6e, 0x64, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x6e, 0x64, 0x6f, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var file_sendTaskIndo_proto_goTypes = []interface{}{
	(*pushNotificationNewTask.NewTaskRequest)(nil), // 0: pushNotificationNewTask.NewTaskRequest
	(*response.Response)(nil),                      // 1: response.Response
}
var file_sendTaskIndo_proto_depIdxs = []int32{
	0, // 0: grpcSendTaskIndo.SendTaskIndo.NewTask:input_type -> pushNotificationNewTask.NewTaskRequest
	0, // 1: grpcSendTaskIndo.SendTaskIndo.TopTasker:input_type -> pushNotificationNewTask.NewTaskRequest
	0, // 2: grpcSendTaskIndo.SendTaskIndo.NewTaskToDistrict:input_type -> pushNotificationNewTask.NewTaskRequest
	0, // 3: grpcSendTaskIndo.SendTaskIndo.FavTasker:input_type -> pushNotificationNewTask.NewTaskRequest
	0, // 4: grpcSendTaskIndo.SendTaskIndo.Normal:input_type -> pushNotificationNewTask.NewTaskRequest
	0, // 5: grpcSendTaskIndo.SendTaskIndo.FavAndTaskerDistrict:input_type -> pushNotificationNewTask.NewTaskRequest
	0, // 6: grpcSendTaskIndo.SendTaskIndo.FavAndTopTasker:input_type -> pushNotificationNewTask.NewTaskRequest
	0, // 7: grpcSendTaskIndo.SendTaskIndo.NewTaskToCity:input_type -> pushNotificationNewTask.NewTaskRequest
	0, // 8: grpcSendTaskIndo.SendTaskIndo.ForceTasker:input_type -> pushNotificationNewTask.NewTaskRequest
	1, // 9: grpcSendTaskIndo.SendTaskIndo.NewTask:output_type -> response.Response
	1, // 10: grpcSendTaskIndo.SendTaskIndo.TopTasker:output_type -> response.Response
	1, // 11: grpcSendTaskIndo.SendTaskIndo.NewTaskToDistrict:output_type -> response.Response
	1, // 12: grpcSendTaskIndo.SendTaskIndo.FavTasker:output_type -> response.Response
	1, // 13: grpcSendTaskIndo.SendTaskIndo.Normal:output_type -> response.Response
	1, // 14: grpcSendTaskIndo.SendTaskIndo.FavAndTaskerDistrict:output_type -> response.Response
	1, // 15: grpcSendTaskIndo.SendTaskIndo.FavAndTopTasker:output_type -> response.Response
	1, // 16: grpcSendTaskIndo.SendTaskIndo.NewTaskToCity:output_type -> response.Response
	1, // 17: grpcSendTaskIndo.SendTaskIndo.ForceTasker:output_type -> response.Response
	9, // [9:18] is the sub-list for method output_type
	0, // [0:9] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_sendTaskIndo_proto_init() }
func file_sendTaskIndo_proto_init() {
	if File_sendTaskIndo_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_sendTaskIndo_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_sendTaskIndo_proto_goTypes,
		DependencyIndexes: file_sendTaskIndo_proto_depIdxs,
	}.Build()
	File_sendTaskIndo_proto = out.File
	file_sendTaskIndo_proto_rawDesc = nil
	file_sendTaskIndo_proto_goTypes = nil
	file_sendTaskIndo_proto_depIdxs = nil
}
