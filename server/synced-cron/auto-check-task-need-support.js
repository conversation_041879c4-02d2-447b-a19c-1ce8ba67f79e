import { Meteor } from 'meteor/meteor';
import _ from 'lodash';
import moment from 'moment';
import {
  trackingScheduleStatus,
  postToSlack,
  changeTimeZone
} from '/server/helper-function';

const runAlertTaskNeedSupport = () => {
  return;
	console.log("Start runAlertTaskNeedSupport ...");
  // const hourInVN = changeTimeZone(new Date()).getHours();
  
  // if ( hourInVN >= 18 || hourInVN < 8 ) {
  //   return;
  // }

  var message = null;

  let taskArray = [];
  let taskIds = [];

  const beginDate = moment().subtract(1, 'month').toDate();

  const badRatingAskerIds = Meteor.appCollection.Rating.find({
    createdAt: {$gte: beginDate},
    rate: {$lte: 3},
  }, {
    fields: {
      askerId: 1
    }
  }).map(rating => rating.askerId);

  Meteor.appCollection.Task.find({
    date: {$gte: new Date()},
    status: {$in: ['POSTED', 'WAITING_ASKER_CONFIRMATION', 'CONFIRMED']},
    askerId: {$in: badRatingAskerIds},
    // 'taskPlace.city': 'Hà Nội',
    specialCS: null
  }, {
    fields: {
      taskPlace: 1,
      serviceText: 1,
      phone: 1,
      contactName: 1,
      askerId: 1
    },
    sort: {
      'taskPlace.city': 1
    }
  }).forEach((task, index) => {

    // Get latest 2 ratings
    const last2Rating = Meteor.appCollection.Rating.find({
      askerId: task.askerId,
      createdAt: {$gt: beginDate}
    }, {
      sort: {
        createdAt: -1
      },
      limit: 2
    }).fetch();
    if ( last2Rating && last2Rating.length === 2 && last2Rating[0].rate === 5 && last2Rating[1].rate === 5  ) {
      // Don't need support this task
      // Asker has 2 rating 5 stars continueous
    } else {
      taskIds.push(task._id);
      taskArray.push(`${task.serviceText.vi} - ${task.taskPlace.city} - ${task.phone} - ${task.contactName}`);
    }
    
  });
  if ( taskArray.length > 0 ) { 
    postToSlack({
      channel: 'special-cs-tasks',
      text: `Các công việc cần chăm sóc đặc biệt để tăng trải nghiệm khách hàng:\n${taskArray.join('\n')}`,
    });
    Meteor.appCollection.Task.update({
      _id: {$in: taskIds}
    }, {
      $set: {
        specialCS: {
          status: 'NEW'
        }
      }
    }, {
      multi: true
    });
  }

	console.log("Finished runAlertTaskNeedSupport ");
  // Tracking
  trackingScheduleStatus({name: 'autoCheckTaskNeedSupport', nextRunAt: SyncedCron.nextScheduledAtDate('autoCheckTaskNeedSupport'), message});
};

  Meteor.methods({
    autoCheckTaskNeedSupport() {
      SyncedCron.add({
        name: 'autoCheckTaskNeedSupport',
        schedule(parser) {
          // Run every 1 hour
          return parser.cron('0 0 0/1 1/1 * ? *', true);
        },
        job() {
          runAlertTaskNeedSupport();
        },
      });
	    console.log("Added AutoCheckTaskNeedSupport cron");
    },
    'runNow.autoCheckTaskNeedSupport'() {
      runAlertTaskNeedSupport();
    },
  });
