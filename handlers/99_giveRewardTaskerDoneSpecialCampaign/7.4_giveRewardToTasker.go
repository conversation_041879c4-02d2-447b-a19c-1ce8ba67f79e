package giveRewardTaskerDoneSpecialCampaign

import (
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	modelSpecialCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/specialCampaign"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
)

func giveRewardToTasker(tasker *modelUser.Users, specialCampaign *modelSpecialCampaign.SpecialCampaign) error {
	for _, reward := range specialCampaign.Rewards {
		if reward != nil && reward.Amount > 0 {
			switch reward.Type {
			case globalConstant.SPECIAL_CAMPAIGN_REWARD_MONEY:
				err := updateFinancialAccount(tasker, specialCampaign, reward.Amount, reward.ApplyAccountType)
				if err != nil {
					return err
				}
			case globalConstant.SPECIAL_CAMPAIGN_REWARD_BPOINT:
				err := callGrpcUpdateBpointToTasker(tasker, specialCampaign.XId, reward.Amount)
				if err != nil {
					return err
				}
			}
		}
	}

	return nil
}
