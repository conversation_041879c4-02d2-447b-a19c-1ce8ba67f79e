package autoReportTasksInfo

import (
	"fmt"
	"strings"

	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
)

type ReportData struct {
	mapTaskerNotManualStart map[string]bool
	mapTaskerNotManualDone  map[string]bool
	TaskersNotManualStart   []string
	TaskersNotManualDone    []string
	TasksNotManualStart     []string
	TasksNotManualDone      []string
}

type TaskerType string

const (
	REPORT_FOR_TASKER  TaskerType = "TASKER"
	REPORT_FOR_COMPANY TaskerType = "COMPANY"
)

/*
----
[Khu vực của task]
Không bấm Bắt đầu:
- [Số lượng] Tasker - [Số lượng] Task
- [Số lượng] Partner - [Số lượng] Task
Không bấm Kết thúc
- [Số lượng] Tasker - [Số lượng] Task
- [Số lượng] Partner - [Số lượng] Task
*/

func createReportData(tasks []*modelTask.Task) (map[string]map[TaskerType]*ReportData, string) {
	reportDataByCity := make(map[string]map[TaskerType]*ReportData)

	for _, task := range tasks {
		cityName := task.GetTaskPlace().GetCity()
		if reportDataByCity[cityName] == nil {
			reportDataByCity[cityName] = map[TaskerType]*ReportData{
				REPORT_FOR_TASKER:  initReportData(),
				REPORT_FOR_COMPANY: initReportData(),
			}
		}

		taskerType, reportForTaskerId := getReportTaskerInfo(task)
		if taskerType == "" || reportForTaskerId == "" {
			continue
		}

		reportData := reportDataByCity[cityName][taskerType]

		// Task not manual start
		if task.StartWorking == nil || !task.StartWorking.IsStart {
			reportData.TasksNotManualStart = append(reportData.TasksNotManualStart, task.GetXId())

			// If tasker not in report -> Add tasker
			if !reportData.mapTaskerNotManualStart[reportForTaskerId] {
				reportData.TaskersNotManualStart = append(reportData.TaskersNotManualStart, reportForTaskerId)
				reportData.mapTaskerNotManualStart[reportForTaskerId] = true
			}

			continue
		}

		// Task not manual done
		for i := len(task.GetChangesHistory()) - 1; i >= 0; i-- {
			history := task.GetChangesHistory()[i]
			if history.Key == globalConstant.CHANGES_HISTORY_KEY_DONE_TASK {
				isTaskerDoneManual := history.From == globalConstant.CHANGES_HISTORY_FROM_TASKER_APP
				if !isTaskerDoneManual {
					reportData.TasksNotManualDone = append(reportData.TasksNotManualDone, task.GetXId())

					// If tasker not in report -> Add tasker
					if !reportData.mapTaskerNotManualDone[reportForTaskerId] {
						reportData.TaskersNotManualDone = append(reportData.TaskersNotManualDone, reportForTaskerId)
						reportData.mapTaskerNotManualDone[reportForTaskerId] = true
					}

					break
				}
				break
			}
		}
	}

	// Convert to slack message
	cityDatas := []string{}
	for cityName, reportByCity := range reportDataByCity {
		taskerReport := reportByCity[REPORT_FOR_TASKER]
		companyReport := reportByCity[REPORT_FOR_COMPANY]

		cityData := fmt.Sprintf("[%s]\n", cityName)
		cityData += fmt.Sprintf("Không bấm Bắt đầu:\n- %d Tasker. %d Task\n- %d Partner. %d Task\n", len(taskerReport.TaskersNotManualStart), len(taskerReport.TasksNotManualStart), len(companyReport.TaskersNotManualStart), len(companyReport.TasksNotManualStart))
		cityData += fmt.Sprintf("Không bấm Kết thúc:\n- %d Tasker. %d Task\n- %d Partner. %d Task\n", len(taskerReport.TaskersNotManualDone), len(taskerReport.TasksNotManualDone), len(companyReport.TaskersNotManualDone), len(companyReport.TasksNotManualDone))

		cityDatas = append(cityDatas, cityData)
	}

	return reportDataByCity, strings.Join(cityDatas, "\n----\n")
}

func initReportData() *ReportData {
	return &ReportData{
		mapTaskerNotManualStart: make(map[string]bool),
		mapTaskerNotManualDone:  make(map[string]bool),
		TaskersNotManualStart:   make([]string, 0),
		TaskersNotManualDone:    make([]string, 0),
		TasksNotManualStart:     make([]string, 0),
		TasksNotManualDone:      make([]string, 0),
	}
}

func getReportTaskerInfo(task *modelTask.Task) (taskerType TaskerType, taskerId string) {
	// Done but not have tasker -> Error
	if len(task.GetAcceptedTasker()) == 0 {
		return
	}

	// Default value
	taskerType = REPORT_FOR_TASKER
	taskerId = task.GetAcceptedTasker()[0].GetTaskerId()

	// Case company
	if len(task.GetAcceptedTasker()) == 1 && task.GetAcceptedTasker()[0].GetCompanyId() != "" {
		taskerType = REPORT_FOR_COMPANY
		taskerId = task.GetAcceptedTasker()[0].GetCompanyId()
		return
	}

	// Case multi tasker -> Find leader
	if len(task.GetAcceptedTasker()) > 1 {
		for _, acceptedTasker := range task.GetAcceptedTasker() {
			if acceptedTasker.IsLeader {
				taskerType = REPORT_FOR_TASKER
				taskerId = acceptedTasker.GetTaskerId()
				return
			}
		}
	}
	return
}
