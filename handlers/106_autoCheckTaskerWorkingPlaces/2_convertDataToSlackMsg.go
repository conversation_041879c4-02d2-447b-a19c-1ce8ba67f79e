package autoCheckTaskerWorkingPlaces

import (
	"fmt"
	"strings"

	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
)

type Report struct {
	TaskerPhone  string   `json:"taskerPhone"`
	TaskerName   string   `json:"taskerName"`
	WorkingAreas []string `json:"workingAreas"`
}

func convertDataToSlackMsg(taskers []*modelUser.Users) (interface{}, string, int) {
	// map[city]tasker
	dataReport := map[string][]*Report{}
	totalTaskerLessThan3Area := 0

	for _, tasker := range taskers {
		dataTaskerByCity := getDataTaskerByCity(tasker)
		for city, districts := range dataTaskerByCity {
			// Case district count >= 3
			districtCountToCheck := 3
			if count, exists := DISTRICT_COUNT_TO_POST_SLACK_NOTIFICATION[city]; exists {
				districtCountToCheck = count
			}
			if len(districts) >= districtCountToCheck {
				continue
			}

			// Init report if not exists
			if _, exists := dataReport[city]; !exists {
				dataReport[city] = []*Report{}
			}

			// Case district count < 3
			dataReport[city] = append(dataReport[city], &Report{
				TaskerName:   tasker.GetName(),
				TaskerPhone:  tasker.GetPhone(),
				WorkingAreas: districts,
			})

			totalTaskerLessThan3Area++
		}
	}

	// Generate slack message
	/*
		------
		Khu vực Hồ Chí Minh
		• Tasker Ngân Giang - 09999999 - Quận 2, Quận 7, Bình Thạnh, Quận 10
		• Tasker Bẩm Trao - 09999999 - Quận 2, Quận 9, Bình Thạnh, Quận 10
	*/
	slackMsg := ""
	for cityName, report := range dataReport {
		slackMsg += fmt.Sprintf("\n------\nKhu vực %s", cityName)
		for _, tasker := range report {
			slackMsg += fmt.Sprintf("\n • Tasker: %s - %s - %s\n", tasker.TaskerPhone, tasker.TaskerName, strings.Join(tasker.WorkingAreas, ", "))
		}
	}

	return dataReport, slackMsg, totalTaskerLessThan3Area
}

// Response example: {"Hanoi": ["Quan 1", "Quan 2"], "HCM": ["Quan 3", "Quan 4"]}
func getDataTaskerByCity(tasker *modelUser.Users) map[string][]string {
	result := make(map[string][]string)
	for _, workingPlace := range tasker.GetWorkingPlaces() {
		// Check if city exists in result
		if _, exists := result[workingPlace.City]; !exists {
			result[workingPlace.City] = []string{}
		}
		// Increase count
		result[workingPlace.City] = append(result[workingPlace.City], workingPlace.District)
	}
	return result
}
