package grpcSendTaskIndo

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcSendTaskIndo"
	"google.golang.org/grpc"
)

func ConnectGRPCSendTask(endpoint string) (grpcSendTaskIndo.SendTaskIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcSendTaskIndo.NewSendTaskIndoClient(connect)
	return client, connect, err
}
