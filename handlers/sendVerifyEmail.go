package handlers

import (
	"fmt"
	"net/http"

	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
 * @Description: Send verify email to user
 * @CreatedAt: 10/12/2020
 * @Author: ngoctb3
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
func SendVerifyEmail(req *modelUser.Users) (*modelEmailResponse.EmailResponse, error) {
	defer local.Logger.Sync()

	// Validate input
	resErr := validateSendVerifyEmail(req)
	if resErr != nil {
		local.Logger.Warn(resErr.Message,
			zap.Any("body", req),
		)
		return resErr, nil
	}

	// Check server config
	resErr = CheckEmailConfig()
	if resErr != nil {
		local.Logger.Warn(resErr.Message)
		return resErr, nil
	}

	lang := globalConstant.LANG_EN
	if req.Language != "" {
		lang = req.Language
	}

	// Get email template
	email := &emailSending.EmailSending{
		From:    "<<EMAIL>>",
		To:      req.Emails[0].Address,
		Subject: localization.T(lang, "VERIFY_EMAIL_SUBJECT"),
	}
	template := fmt.Sprintf("%s/verify_email.html", cfg.EmailTemplateURL)
	askerName := req.Name
	if lang == globalConstant.LANG_KO {
		askerName = fmt.Sprintf("%s님", askerName)
	}
	//Send verify email
	emailVerify := map[string]interface{}{
		"EmailHello":              localization.T(lang, "EMAIL_HELLO"),
		"Username":                askerName,
		"VerifyEmailDescription1": localization.T(lang, "VERIFY_EMAIL_DESCRIPTION_1"),
		"VerifyEmailDescription2": localization.T(lang, "VERIFY_EMAIL_DESCRIPTION_2"),
		"VerifyLink":              fmt.Sprintf("%s/v2/user/verify-email?accessKey=%s&email=%s", cfg.EmailVerifyURL, cfg.VerifyEmailApiKey, req.Emails[0].Address),
		"VerifyEmailButtonText":   localization.T(lang, "VERIFY_EMAIL_BUTTON_TEXT"),
		"VerifyEmailNotice1":      localization.T(lang, "VERIFY_EMAIL_NOTICE"),
		"VerifyEmailNotice2":      localization.T(lang, "EMAIL_SUPPORT_NOTICE_1"),
		"VerifyEmailNotice3":      localization.T(lang, "EMAIL_SUPPORT_NOTICE_2"),
		"VerifyEmailClosing1":     localization.T(lang, "EMAIL_CLOSING_1"),
		"VerifyEmailClosing2":     localization.T(lang, "EMAIL_CLOSING_2"),
		"VerifyEmailClosing3":     localization.T(lang, "EMAIL_CLOSING_3"),
		"FollowbTaskeeAt":         localization.T(lang, "FOLLOW_BTASKEE_AT"),
	}

	var settingCountry *settingCountry.SettingCountry
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_COUNTRY[local.ISO_CODE], bson.M{"isoCode": local.ISO_CODE}, bson.M{"currency": 1}, &settingCountry)
	if settingCountry != nil && settingCountry.Currency != nil && settingCountry.Currency.Code != "" {
		var settings *settings.Settings
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"referralSetting": 1}, &settings)

		referralValue, referralSignCode := getReferralValueAndCurrency(local.ISO_CODE, settingCountry.Currency.Code, settings)
		emailVerify["HasInvitation"] = true
		emailVerify["Invitation1"] = localization.T(lang, "INVITATION_1")
		emailVerify["Invitation2"] = localization.T(lang, "INVITATION_2")
		emailVerify["ReferralCode"] = req.ReferralCode
		emailVerify["InvitationDescription"] = localization.T(lang, "INVITE_DESCRIPTION", globalLib.FormatMoney(referralValue), referralSignCode, globalLib.FormatMoney(referralValue), referralSignCode)
	}

	emailVerify = globalLib.UpdateBtaskeeInfoInEmailByIsoCode(emailVerify, local.ISO_CODE)

	body, err := ParseTemplate(template, emailVerify)
	if body == "" || err != nil {
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_CONTENT_NIL,
		}, err
	}

	// NOTE: Test with user service "./test.sh 1 50"
	email.Content = body
	return SendEmail(email)
}

func validateSendVerifyEmail(req *modelUser.Users) *modelEmailResponse.EmailResponse {
	if req == nil || req.Emails == nil || len(req.Emails) == 0 {
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_EMAIL_VERIFY_USER_EMPTY,
		}
	}
	if !globalLib.IsEmailValid(req.Emails[0].Address) {
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusInternalServerError,
			Message:    lib.ERROR_EMAIL_INVALID,
		}
	}
	return nil
}
