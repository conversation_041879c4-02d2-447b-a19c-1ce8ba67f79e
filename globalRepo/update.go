package globalRepo

import (
	"context"

	"gitlab.com/btaskee/go-services-model-v2/dbConfig"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func UpdateOneById(collectionName string, id string, data interface{}) (*mongo.UpdateResult, error) {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	return collection.UpdateOne(ctx, bson.M{"_id": id}, data)
}

func UpdateAllByQuery(collectionName string, query interface{}, data interface{}) (*mongo.UpdateResult, error) {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	return collection.UpdateMany(ctx, query, data)
}

func UpdateOneByQuery(collectionName string, query interface{}, data interface{}) (*mongo.UpdateResult, error) {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	return collection.UpdateOne(ctx, query, data)
}

func UpdateOneByIdWithContext(ctx context.Context, collectionName string, id string, data interface{}) (*mongo.UpdateResult, error) {
	collection := dbConfig.GetCollection(collectionName)
	return collection.UpdateOne(ctx, bson.M{"_id": id}, data)
}

func UpdateOneByQueryWithContext(ctx context.Context, collectionName string, query interface{}, data interface{}) (*mongo.UpdateResult, error) {
	collection := dbConfig.GetCollection(collectionName)
	return collection.UpdateOne(ctx, query, data)
}
