package globalDataAccessV2

import (
	"bytes"
	"fmt"
	"io"
	"os"

	"gitlab.com/btaskee/go-services-model-v2/dbConfig"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/bsonrw"
)

type QueryOptions struct {
	Sort  bson.M
	Limit int64
	Page  int64
}

func MergeUserOptions(opts ...*QueryOptions) *QueryOptions {
	op := &QueryOptions{}
	for _, opt := range opts {
		if opt == nil {
			continue
		}
		if opt.Sort != nil {
			op.Sort = opt.Sort
		}
		if opt.Limit > 0 {
			op.Limit = opt.Limit
		}
		if opt.Page > 0 {
			op.Page = opt.Page
		}
	}
	return op
}

type DataAccess struct {
	isoCode string
}

func New(isoCode string) *DataAccess {
	return &DataAccess{isoCode: isoCode}
}

type Mode map[string]string

var (
	MODE               = os.Getenv("APPLICATION_MODE")
	REGISTRY           = dbConfig.RegisterCodec(bson.NewRegistryBuilder()).Build()
	DATA_ACCESS_URL_VN = Mode{
		"":      "127.0.0.1:58585",
		"dev":   "go-data-access-vn-v3.kong.svc.cluster.local:81",
		"local": "127.0.0.1:58585",
		"prod":  "go-data-access-vn-v3.kong.svc.cluster.local:81",
		"test":  "127.0.0.1:58585",
		"ci":    "go-data-access-vn-v3:58585",
	}
	DATA_ACCESS_URL_TH = Mode{
		"":      "127.0.0.1:58586",
		"dev":   "go-data-access-th-v3.kong.svc.cluster.local:81",
		"local": "127.0.0.1:58586",
		"prod":  "go-data-access-th-v3.kong.svc.cluster.local:81",
		"test":  "127.0.0.1:58586",
		"ci":    "go-data-access-th-v3:58586",
	}
	DATA_ACCESS_URL_ID = Mode{
		"":      "127.0.0.1:58587",
		"dev":   "go-data-access-indo-v3.kong.svc.cluster.local:81",
		"local": "127.0.0.1:58587",
		"prod":  "go-data-access-indo-v3.kong.svc.cluster.local:81",
		"test":  "127.0.0.1:58587",
		"ci":    "go-data-access-indo-v3:58587",
	}
	DATA_ACCESS_URL_MY = Mode{
		"":      "127.0.0.1:58588",
		"dev":   "go-data-access-my-v3.kong.svc.cluster.local:81",
		"local": "127.0.0.1:58588",
		"prod":  "go-data-access-my-v3.kong.svc.cluster.local:81",
		"test":  "127.0.0.1:58588",
		"ci":    "go-data-access-my-v3:58588",
	}
	DATA_ACCESS_URL = map[string]Mode{
		globalConstant.ISO_CODE_VN:   DATA_ACCESS_URL_VN,
		globalConstant.ISO_CODE_TH:   DATA_ACCESS_URL_TH,
		globalConstant.ISO_CODE_INDO: DATA_ACCESS_URL_ID,
		globalConstant.ISO_CODE_MY:   DATA_ACCESS_URL_MY,
	}
)

func EncodeData(data interface{}) ([]byte, error) {
	buf := new(bytes.Buffer)
	vw, err := bsonrw.NewExtJSONValueWriter(buf, true, false)
	if err != nil {
		return nil, fmt.Errorf("ERROR NEW EXT JSON VALUE WRITER: %v", err)
	}
	encoder, err := bson.NewEncoder(vw)
	if err != nil {
		return nil, fmt.Errorf("ERROR NEW ENCODER: %v", err)
	}
	err = encoder.SetRegistry(REGISTRY)
	if err != nil {
		return nil, fmt.Errorf("ERROR SET REGISTRY: %v", err)
	}
	err = encoder.Encode(data)
	if err != nil {
		return nil, fmt.Errorf("ERROR ENCODE: %v", err)
	}
	return buf.Bytes(), nil
}

func EncodeListData(data []interface{}) ([]byte, error) {
	buf := new(bytes.Buffer)
	vw, err := bsonrw.NewExtJSONValueWriter(buf, true, false)
	if err != nil {
		return nil, fmt.Errorf("ERROR LIST NEW EXT JSON VALUE WRITER: %v", err)
	}
	encoder, err := bson.NewEncoder(vw)
	if err != nil {
		return nil, fmt.Errorf("ERROR LIST NEW ENCODER: %v", err)
	}
	err = encoder.SetRegistry(REGISTRY)
	if err != nil {
		return nil, fmt.Errorf("ERROR SET REGISTRY: %v", err)
	}
	for i := 0; i < len(data); i++ {
		err = encoder.Encode(data[i])
		if err != nil {
			return nil, fmt.Errorf("ERROR LIST ENCODE: %v", err)
		}
	}
	return buf.Bytes(), nil
}

func EncodeBSONList(data []bson.M) ([]byte, error) {
	buf := new(bytes.Buffer)
	vw, err := bsonrw.NewExtJSONValueWriter(buf, true, false)
	if err != nil {
		return nil, fmt.Errorf("ERROR LIST NEW EXT JSON VALUE WRITER: %v", err)
	}
	encoder, err := bson.NewEncoder(vw)
	if err != nil {
		return nil, fmt.Errorf("ERROR LIST NEW ENCODER: %v", err)
	}
	err = encoder.SetRegistry(REGISTRY)
	if err != nil {
		return nil, fmt.Errorf("ERROR SET REGISTRY: %v", err)
	}
	for i := 0; i < len(data); i++ {
		err = encoder.Encode(data[i])
		if err != nil {
			return nil, fmt.Errorf("ERROR LIST ENCODE: %v", err)
		}
	}
	return buf.Bytes(), nil
}

func DecodeData(b []byte) (interface{}, error) {
	vr, err := bsonrw.NewExtJSONValueReader(bytes.NewReader(b), true)
	if err != nil {
		return nil, fmt.Errorf("ERROR NEW EXT JSON Value Reader: %v", err)
	}
	decoder, err := bson.NewDecoder(vr)
	if err != nil {
		return nil, fmt.Errorf("ERROR NEW DECODER: %v", err)
	}
	var data interface{}
	err = decoder.Decode(&data)
	if err != nil {
		return nil, fmt.Errorf("ERROR DECODER: %v", err)
	}
	return data, nil
}

func DecodeBSONList(b []byte) ([]bson.M, error) {
	vr, err := bsonrw.NewExtJSONValueReader(bytes.NewBuffer(b), true)
	if err != nil {
		return nil, fmt.Errorf("ERROR NEW EXT JSON Value Reader: %v", err)
	}
	decoder, err := bson.NewDecoder(vr)
	if err != nil {
		return nil, fmt.Errorf("ERROR NEW DECODER: %v", err)
	}
	var data []bson.M
	for {
		var item bson.M
		err = decoder.Decode(&item)
		if err == io.EOF {
			err = nil
			break
		}
		if err != nil {
			err = fmt.Errorf("ERROR DECODER: %v", err)
			break
		}
		data = append(data, item)
	}
	return data, err
}

func DecodeListData(b []byte) ([]interface{}, error) {
	vr, err := bsonrw.NewExtJSONValueReader(bytes.NewBuffer(b), true)
	if err != nil {
		return nil, fmt.Errorf("ERROR NEW EXT JSON Value Reader: %v", err)
	}
	decoder, err := bson.NewDecoder(vr)
	if err != nil {
		return nil, fmt.Errorf("ERROR NEW DECODER: %v", err)
	}
	var data []interface{}
	for {
		var item interface{}
		err = decoder.Decode(&item)
		if err == io.EOF {
			err = nil
			break
		}
		if err != nil {
			err = fmt.Errorf("ERROR DECODER: %v", err)
			break
		}
		data = append(data, item)
	}
	return data, err
}

func ConvertData(result map[string]interface{}) map[string]interface{} {
	for key, val := range result {
		switch i := val.(type) {
		case int:
			result[key] = float64(i)
		case int8:
			result[key] = float64(i)
		case int16:
			result[key] = float64(i)
		case int32:
			result[key] = float64(i)
		case int64:
			result[key] = float64(i)
		}
	}
	return result
}
