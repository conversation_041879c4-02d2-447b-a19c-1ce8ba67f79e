package subscription

import (
	"log"

	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/localization"
)

// sendNotificationToAsker sends notification to asker when task is posted
func sendNotificationToAsker(taskID, userID, language, serviceText string) {
	lang := globalConstant.LANG_VI
	if language != "" {
		lang = language
	}

	// Create notification data
	title := localization.GetLocalizeObject("NOTIFICATION_MESSAGE_ASKER_POSTED_TITLE", serviceText)
	text := localization.GetLocalizeObject("NOTIFICATION_MESSAGE_ASKER_POSTED", serviceText)

	// Simplified notification sending - in real implementation, you would use the notification service
	log.Printf("Sending notification to asker %s for task %s. Title: %s, Text: %s",
		userID, taskID,
		globalLib.LocalizeServiceName(lang, title),
		globalLib.LocalizeServiceName(lang, text))
}
