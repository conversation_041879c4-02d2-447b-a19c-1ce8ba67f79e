// import { Meteor } from 'meteor/meteor';
// import {
//   trackingScheduleStatus,
// } from '/server/helper-function';

// const calculateAvgRating = (taskerId) => {
//   const ratings = Meteor.appCollection.Rating.find({ taskerId }, { fields: { rate: 1 } });
//   if (ratings && ratings.count() > 0) {
//     // Calculate avg rating
//     let sum = 0;
//     ratings.forEach((rating) => {
//       sum += rating.rate;
//     });
//     return sum / ratings.count();
//   }
//   return 0;
// };

// const runAutoRate = () => {
//   var message = null;
//   try {
//     const lastWeekDate = new Date();
//     lastWeekDate.setDate(lastWeekDate.getDate() - 7);
//     const arrayOfTask = [];
//     // Find DONE task didn't rated from last 7 days.
//     Meteor.appCollection.Task.find({ status: 'DONE', rated: false, date: { $lte: lastWeekDate } }).forEach((task) => {
//       if (task.acceptedTasker && task.acceptedTasker.length > 0 && task.acceptedTasker[0].taskerId) {
//         // Make sure that task had acceptedTasker.
//         const taskerId = task.acceptedTasker[0].taskerId;
//         const existsRating = Meteor.appCollection.Rating.findOne({ askerId: task.askerId, taskerId, taskId: task._id });
//         if (!existsRating) {
//           // Make sure that task hadn't any rating.
//           arrayOfTask.push(task._id);
//           // Insert into rating
//           Meteor.appCollection.Rating.insert({
//             askerId: task.askerId,
//             taskerId,
//             taskId: task._id,
//             rate: 5,
//             feedBack: [],
//             createdAt: new Date(),
//           });
//           // Calculate avg of ratings and update avgRating of users.
//           const avgRating = calculateAvgRating(taskerId);
//           if (avgRating > 0) {
//             Meteor.appCollection.users.update({ _id: taskerId }, { $set: { avgRating } });
//           }
//         }
//       }
//     });
//     // Update rated status of the tasks.
//     if (arrayOfTask.length > 0) {
//       Meteor.appCollection.Task.update({ _id: { $in: arrayOfTask } }, {
//         $set: { rated: true, updatedAt: new Date() },
//       }, { multi: true });
//     }
//   } catch (e) {
//     message = e;
//   }
//   // Tracking
//   trackingScheduleStatus({name: 'AutoRate', nextRunAt: SyncedCron.nextScheduledAtDate('autoRate'), message});
// };

// export default function () {
//   Meteor.methods({
//     // Auto Rate
//     autoRate() {
//       SyncedCron.add({
//         name: 'autoRate',
//         schedule(parser) {
//           // Run at 00:10 every day.
//           return parser.cron('0 10 0 1/1 * ? *', true);
//         },
//         job() {
//           runAutoRate();
//         },
//       });
//     },
//     'runNow.autoRate'() {
//       runAutoRate();
//     },
//   });
// }
