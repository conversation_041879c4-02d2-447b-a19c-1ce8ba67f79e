import { Meteor } from 'meteor/meteor';
import { Mongo } from 'meteor/mongo';

  let isConnected = false;
  while (!isConnected) {
    try {
      const database = new MongoInternals.RemoteCollectionDriver(
        Meteor.settings.DEFAULT_MONGO_SERVER, {
          oplogUrl: Meteor.settings.DEFAULT_MONGO_SERVER_OPLOG,
        }
      );
      isConnected = true;
      Meteor.appCollection = {
        users: {
          find: (selector = {}, option = {}) => (database.mongo.find('users', selector, option)),
          findOne: (selector = {}, option = {}) => (database.mongo.findOne('users', selector, option)),
          update: (selector = {}, update = {}, option = {}) => (database.mongo.update('users', selector, update, option)),
          aggregate: (query = []) => {
            const aggregate = Meteor.wrapAsync(database.mongo.rawCollection('users').aggregate, database.mongo.rawCollection('users'));
            return aggregate(query);
          },
        },
        VN_Task: new Mongo.Collection('vn_task', { _driver: database }),
        TH_Task: new Mongo.Collection('th_task', { _driver: database }),
        VN_HistoryTasks: new Mongo.Collection('vn_history_tasks', { _driver: database }),
        TH_HistoryTasks: new Mongo.Collection('th_history_tasks', { _driver: database }),
        // AppUser: new Mongo.Collection('users', { _driver: database }),
        VN_TaskSchedule: new Mongo.Collection('vn_taskSchedule', { _driver: database }),
        TH_TaskSchedule: new Mongo.Collection('th_taskSchedule', { _driver: database }),
        Service: new Mongo.Collection('service', { _driver: database }),
        SubService: new Mongo.Collection('subService', { _driver: database }),
        ActivationCode: new Mongo.Collection('userActivation', { _driver: database }),
        VN_Notification: new Mongo.Collection('vn_notification', { _driver: database }),
        TH_Notification: new Mongo.Collection('th_notification', { _driver: database }),
        ServiceChannel: new Mongo.Collection('serviceChannel', { _driver: database }),
        VN_FinancialAccount: new Mongo.Collection('vn_financialAccount', { _driver: database }),
        TH_FinancialAccount: new Mongo.Collection('th_financialAccount', { _driver: database }),
        FATransaction: new Mongo.Collection('FATransaction', { _driver: database }),
        TaskerTaskHistory: new Mongo.Collection('taskerTaskHistory', { _driver: database }),
        Rating: new Mongo.Collection('rating', { _driver: database }),
        SettingSystem: new Mongo.Collection('settingSystem', { _driver: database }),
        PromotionCode: new Mongo.Collection('promotionCode', { _driver: database }),
        PromotionHistory: new Mongo.Collection('promotionHistory', { _driver: database }),
        //NewTask: new Mongo.Collection('newTask', { _driver: database }),
        //PushTokens: new Mongo.Collection('_raix_push_app_tokens', { _driver: database }),
        Guideline: new Mongo.Collection('guidelines', { _driver: database }),
        UserActionHistory: new Mongo.Collection('userActionHistory', { _driver: database }),
        Reward: new Mongo.Collection('reward', { _driver: database }),
        SupportNotification: new Mongo.Collection('supportNotification', { _driver: database }),
        WorkingPlaces: new Mongo.Collection('workingPlaces', { _driver: database }),
        FreeTask: new Mongo.Collection('freeTask', { _driver: database }),
        Campaign: new Mongo.Collection('campaign', { _driver: database }),
        TaskDistribution: new Mongo.Collection('taskDistribution', { _driver: database }),
        VN_Subscription: new Mongo.Collection('vn_subscription', { _driver: database }),
        TH_Subscription: new Mongo.Collection('th_subscription', { _driver: database }),
        SubscriptionSettings: new Mongo.Collection('subscriptionSettings', { _driver: database }),
        VN_PurchaseOrder: new Mongo.Collection('vn_purchaseOrder', { _driver: database }),
        TH_PurchaseOrder: new Mongo.Collection('th_purchaseOrder', { _driver: database }),
        WeeklyPayout: new Mongo.Collection('weeklyPayout', { _driver: database }),
        TaskReportTasker: new Mongo.Collection('taskReportTasker', { _driver: database }),
        ServiceStatus: new Mongo.Collection('serviceStatus', { _driver: database }),
        VN_PaymentCard: new Mongo.Collection('vn_paymentCard', { _driver: database }),
        TH_PaymentCard: new Mongo.Collection('th_paymentCard', { _driver: database }),
        OutstandingPayment: new Mongo.Collection('outstandingPayment', { _driver: database }),
        TrustPointHistory: new Mongo.Collection('trustPointHistory', { _driver: database }),
        AskerRating: new Mongo.Collection('askerRating', { _driver: database }),
        TaskerReferral: new Mongo.Collection('taskerReferral', { _driver: database }),
        NotComeLockHistory: new Mongo.Collection('notComeLockHistory', { _driver: database }),
        MarketingCampaign: new Mongo.Collection('marketingCampaign', { _driver: database }),
        PaymentTransaction: new Mongo.Collection('paymentTransaction', { _driver: database }),
        Incentive: new Mongo.Collection('incentive', { _driver: database }),
        ZaloPayTransaction: new Mongo.Collection('zaloPayTransaction', { _driver: database }),
        BEmployee: new Mongo.Collection('bEmployee', { _driver: database }),
        TaskerViolate: new Mongo.Collection('taskerViolate', { _driver: database }),
        ChatMessage: new Mongo.Collection('chatMessage', { _driver: database }),
        UserLocationHistory: new Mongo.Collection('userLocationHistory', { _driver: database }),
        
        TH_SubscriptionSettings: new Mongo.Collection('th_subscriptionSettings', { _driver: database }),
        TH_Service: new Mongo.Collection('th_service', { _driver: database }),
        TH_FATransaction: new Mongo.Collection('th_FATransaction', { _driver: database }),
        GrabFoodReward: new Mongo.Collection('GrabFoodReward', { _driver: database }),
        Payment2C2PTransaction: new Mongo.Collection('payment2C2PTransaction', { _driver: database }),
        TH_RefundRequest: new Mongo.Collection('th_refundRequest', { _driver: database }),
        ShopeePayTransaction: new Mongo.Collection('shopeePayTransaction', { _driver: database }),
        VN_ShopeePayTransaction: new Mongo.Collection('vn_shopeePayTransaction', { _driver: database }),
        RefundRequest: new Mongo.Collection('refundRequest', { _driver: database }),
        MomoTransaction: new Mongo.Collection('momoTransaction', { _driver: database }),
        VNPayTransaction: new Mongo.Collection('vnPayTransaction', { _driver: database }),
        TH_SettingSystem: new Mongo.Collection('th_settingSystem', { _driver: database }),
        Gift: new Mongo.Collection('gift', { _driver: database }),
      };
	    console.log("Connected to App DB");
    } catch (e) {
      console.log(e);
      // have error, pause 1s then reconnect
      isConnected = false;
      Meteor.wrapAsync(cb => Meteor.setTimeout(() => cb(), 2000))();
    }
  }
