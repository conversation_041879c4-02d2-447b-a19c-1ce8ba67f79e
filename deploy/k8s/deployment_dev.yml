---
apiVersion: v1
kind: Service
metadata:
  name: go-sync-cron-vn-v3
  namespace: kong
  labels:
    app: go-sync-cron-vn-v3
spec:
  ports:
    - port: 80
      targetPort: 23100
      name: http
  selector:
    app: go-sync-cron-vn-v3

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: go-sync-cron-vn-v3
  namespace: kong
  labels:
    app: go-sync-cron-vn-v3
spec:
  replicas: 1
  selector:
    matchLabels:
      app: go-sync-cron-vn-v3
  template:
    metadata:
      labels:
        app: go-sync-cron-vn-v3
        log-label: "go-services"
    spec:
      containers:
        - name: go-sync-cron-vn-v3
          image: linhnhdocker/go-sync-cron-service:vn-3.0.0
          imagePullPolicy: "IfNotPresent"
          ports:
            - containerPort: 80
              name: http