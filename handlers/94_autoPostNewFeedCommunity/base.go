package autoPostNewFeedCommunity

import (
	"fmt"
	"log"

	"github.com/robfig/cron/v3"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
)

var c *cron.Cron
var cfg = config.GetConfig()
var isRunning = false
var paramsEnough = true
var runAt string

func init() {
	var cronConfig map[string]interface{}
	if cfg != nil {
		cronConfig = cfg.ScheduleSyncCron["autopostnewfeedcommunity"]
	}

	// Check if params enough
	if cronConfig["run_at"] == nil {
		lib.PostToSlackNotStart("AutoPostNewFeedCommunity")
		return
	}
	// Get runAt string for sync cron
	runAt = fmt.Sprintf(cronConfig["run_at"].(string))

	// Check if config has is_run = true
	if cronConfig["is_run"] == nil || !cronConfig["is_run"].(bool) {
		lib.PostToSlackNotStart("AutoPostNewFeedCommunity")
		return
	}

	StartCron()
}

func StartCron() {
	c := cron.New(cron.WithLocation(local.TimeZone))
	c.AddFunc(runAt, RunAutoPostNewFeedCommunity)
	c.Start()
	isRunning = true
}

func Run(action int) {
	// action == 1 -> Run sync cron once now
	if action == lib.RUN_NOW {
		RunAutoPostNewFeedCommunity()
	}
	// action == 2 -> Start sync cron with schedule config
	if action == lib.START && paramsEnough && !isRunning {
		StartCron()
	}
	// action == 3 -> Stop running sync cron
	if action == lib.STOP && isRunning {
		c.Stop()
		c = nil
		isRunning = false
	}
}
func RunAutoPostNewFeedCommunity() {
	log.Println("Start RunAutoPostNewFeed Process")
	defer log.Println("Finish RunAutoPostNewFeed Process")

	posts := getPosts()
	updateStatusPosts(posts)
}
