package autoCheckIncentive

import (
	"encoding/json"
	"io"
	"log"
	"net/http"
	"net/url"
	"time"
)

func getListGiftFromURBOX() []UrBoxGift {
	if cfg.UrBoxConfig == nil || cfg.UrBoxConfig.AppID == "" || cfg.UrBoxConfig.AppSecret == "" || cfg.UrBoxConfig.UrlGetListGift == "" {
		log.Println("URBox config not found")
		return nil
	}
	urlUrBox := cfg.UrBoxConfig.UrlGetListGift
	query := url.Values{}
	query.Set("app_id", cfg.UrBoxConfig.AppID)
	query.Set("app_secret", cfg.UrBoxConfig.AppSecret)
	query.Set("per_page", "200")

	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	req, err := http.NewRequest("GET", urlUrBox, nil)
	if err != nil {
		log.Fatalf("Error creating request: %v", err)
	}
	req.URL.RawQuery = query.Encode()

	resp, err := client.Do(req)
	if err != nil {
		log.Fatalf("Error making request: %v", err)
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		log.Fatalf("Error reading response body: %v", err)
	}

	var result Response
	if err := json.Unmarshal(body, &result); err != nil {
		log.Fatalf("Error parsing JSON: %v", err)
	}
	return result.Data.Items
}
