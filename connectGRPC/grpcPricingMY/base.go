package grpcPricingMY

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPricingMY"
	"google.golang.org/grpc"
)

func ConnectGRPCPricing(endpoint string) (grpcPricingMY.PricingClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPricingMY.NewPricingClient(connect)
	return client, connect, err
}
