package grpcPromotionTH

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPromotionTH"
	"google.golang.org/grpc"
)

func ConnectGRPCPromotionTH(endpoint string) (grpcPromotionTH.PromotionTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPromotionTH.NewPromotionTHClient(connect)
	return client, connect, err
}
