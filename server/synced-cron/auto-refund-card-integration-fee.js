import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import _ from 'lodash';
import {
  revertTimeZone,
  trackingScheduleStatus,
  postToSlack,
} from '/server/helper-function';

const refundTransaction = (transaction) => {
  
  const username = Meteor.settings.Adyen.adyenUserName;
  const password = Meteor.settings.Adyen.adyenPassword;
  // Mark transaction is sending refund
  Meteor.appCollection.PaymentTransaction.update({
    _id: transaction._id
  }, {
    $set: {
      refundStatus: 'SENDING',
      'refundData.sendingAt': new Date()
    }
  });
  // Call Adyen API for refundding
  Meteor.defer(() => {
    const result = HTTP.call('POST', Meteor.settings.Adyen.refundEndPoint, {
      auth: username + ':' + password,
      headers: {
        "Content-Type": "application/json"
      },
      data: {
        merchantAccount: "BtaskeeCOM",
        modificationAmount: {
          value: transaction.cost,
          currency: transaction.currency
        },
        originalReference: transaction.pspReference,
        reference: `Refund_${transaction.pspReference}`
      },
    });
  
    if ( result && result.data && result.data.response === '[refund-received]' ) {
      // Result ok, Adyen received the refund request successfully.
      Meteor.appCollection.PaymentTransaction.update({
        _id: transaction._id
      }, {
        $set: {
          refundStatus: 'RECEIVED',
          'refundData.receivedAt': new Date(),
          'refundData.refundPspReference': result.data.pspReference
        }
      });
    }
  });
}

const runRefundIn24h = () => {
  // Do nothing if the enableAutoRefundIntegrationFee not be true
  const setting = Meteor.appCollection.SettingSystem.findOne({}, {fields: {enableAutoRefundIntegrationFee: 1}});
  if ( !_.get(setting, 'enableAutoRefundIntegrationFee', false) ) {
    return
  }

  var message = null;
  let transactions = [];
  
  // Find all integrate transaction in last 24h
  Meteor.appCollection.PaymentTransaction.find({
    createdAt: {$gte: moment().subtract(24, 'hours').toDate()},
    $or: [
      {reference: new RegExp('IntegrateCard')},
      {reference: new RegExp('IC')},
    ],
    status: 'RESPONSED',
    charged: true,
    pspReference: {$exists: true},
    refundStatus: null // Not yet refund
  }, {
    fields: {
      pspReference: 1,
      cost: 1,
      currency: 1,
      reference: 1
    },
    sort: {
      createdAt: 1
    }}).forEach((transaction) => {
    try {
      // transactions for logging
      transactions.push(transaction.pspReference + ' - ' + transaction.reference)

      // Do refund
      refundTransaction(transaction);

    } catch (e) {
      message = e;
      postToSlack({
        channel: 'cron-jobs-alert',
        text: `Workflow: There was an error on autoRefundCardIntegrationFee: transactionId: ${transaction._id} Message: ${message}`,
      });
    }
  });
  
  // Post to Slack the result of refund
  let slackMessage = `Workflow Auto Refund: KHÔNG có giao dịch cần refund`;
  if ( transactions.length > 0 ) {
    slackMessage = `Workflow Auto Refund: Đã gửi refund ${transactions.length} giao dịch:\n${transactions.join('\n')}`;
  }
  postToSlack({
    channel: 'cron-jobs-alert',
    text: slackMessage,
  });

  // Tracking
  trackingScheduleStatus({name: 'autoRefundCardIntegrationFee', nextRunAt: SyncedCron.nextScheduledAtDate('autoRefundCardIntegrationFee'), message});
};

  Meteor.methods({
    autoRefundCardIntegrationFee() {
      SyncedCron.add({
        name: 'autoRefundCardIntegrationFee',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 8)).getHours();
          // Run at 8AM and 8PM every day
          return parser.cron(`0 0 ${hourInTimeZoneServer}/12 1/1 * ? *`, true);
        },
        job() {
       //   runRefundIn24h();
        },
      });
    },
    'runNow.autoRefundCardIntegrationFee'() {
      //runRefundIn24h();
    },
  });


      
