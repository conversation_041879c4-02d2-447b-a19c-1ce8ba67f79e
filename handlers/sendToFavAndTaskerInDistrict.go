package handlers

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/lib"
	libUser "gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/lib/user"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalRepo"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelServiceChannel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/serviceChannel"
	modelSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func SendNotificationToFavAndTaskersDistrict(serviceObject *modelService.Service, taskId string, isoCode string) {
	var serviceChannel *modelServiceChannel.ServiceChannel
	globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": serviceObject.XId}, bson.M{"taskerList": 1}, &serviceChannel)
	var task *modelTask.Task
	fields := bson.M{"taskPlace": 1, "date": 1, "askerId": 1, "blackList": 1, "createdAt": 1, "isPremium": 1, "isoCode": 1}
	fields = globalLib.MergeQuerySocketTask(fields)
	err := globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskId, fields, &task)
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[ERROR Push New Task v2]: func SendNotificationToFavAndTaskersDistrict, task nil: %v", err))
		return
	}
	asker, err := libUser.GetOneById(task.AskerId, bson.M{"favouriteTasker": 1})
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[ERROR Push New Task v2]: func SendNotificationToFavAndTaskersDistrict, asker nil: %v", err))
		return
	}
	var taskers []*modelUser.Users
	// Lưu ý: lấy danh sách Fav trước khi lấy danh sách Tasker District, vì khi lấy Tasker District 'serviceChannel' đã bị func 'globalLib.RemoveTaskersFromList' cập nhật lại
	var settings *modelSetting.Settings
	globalRepo.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"topTaskersPriorityScore": 1, "citiesApplyDistance": 1, "timeInBetweenTask": 1, "limitNumberAcceptTaskInDay": 1, "servicesApplyDistance": 1, "limitNumberAcceptTaskInDayRush": 1, "sendNotificationConfig": 1}, &settings)
	busyTaskers := lib.GetBusyTaskersForTask(serviceObject.XId, task, settings)
	// NOTE: Because when opening services in a new city, tasks with the payment method 'cash' will not be accepted by any tasker,
	// we will allow taskers using BNPL to take on cash tasks.
	// Remove Tasker BNPL
	// taskerHasBNPL := lib.GetListTaskerHasBNPL(task)
	excludedTaskers := lib.GetTaskMetadata(task.XId).GetExcludedTaskers()
	favTaskers := getFavTaskers(serviceChannel, asker, task, busyTaskers, excludedTaskers)
	taskersDistrict := getTaskersDistrict(serviceChannel, asker, task, busyTaskers, excludedTaskers)

	if len(taskersDistrict) > 0 {
		taskers = taskersDistrict
	}
	if len(favTaskers) > 0 {
		taskers = append(taskers, favTaskers...)
	}
	if len(taskers) == 0 {
		lib.SendNotificationNormal(serviceObject, taskId)
		return
	}
	// Get notification data
	taskerIds, listOfTaskerId, listOfMessage := lib.GetPushNotificationData(task, serviceObject, taskers)
	// Update viewedTasker
	// globalRepo.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskId, bson.M{"$set": bson.M{"viewedTaskers": taskerIds, "visibility": 3}})
	lib.UpdateTask(taskId, globalConstant.CHANGES_HISTORY_KEY_RESEND_FAV_DISTRICT, taskerIds, 3)
	// TODO: Synced Cron
	// if (!SyncedCron.nextScheduledAtDate(taskId)) {
	//   startJobResendNotification(taskId, task.createdAt);
	// }
	title := &modelService.ServiceText{
		Vi: localization.T("vi", "NOTIFICATION_NEW_TASK"),
		En: localization.T("en", "NOTIFICATION_NEW_TASK"),
		Ko: localization.T("ko", "NOTIFICATION_NEW_TASK"),
		Th: localization.T("th", "NOTIFICATION_NEW_TASK"),
	}
	mapDistrict := lib.MapDistrictText(task.TaskPlace)
	body := &modelService.ServiceText{
		Vi: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("vi", serviceObject.Text), mapDistrict.Vi),
		En: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("en", serviceObject.Text), mapDistrict.En),
		Ko: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("ko", serviceObject.Text), mapDistrict.Ko),
		Th: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("th", serviceObject.Text), mapDistrict.Th),
		Id: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("id", serviceObject.Text), mapDistrict.Id),
	}
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       0,
		TaskId:     taskId,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
	}
	lib.SendNotification(listOfMessage, listOfTaskerId, title, body, payload, "", settings)

	// Send socket new task
	lib.SendSocket(task, taskerIds)
}
