package bkafka

import (
	"context"
	"errors"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/IBM/sarama"
	"github.com/spf13/cast"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"go.uber.org/zap"
)

type kafkaMQ struct {
	pd *producerProvider
	// oncePD  *sync.Once
	brokers []string
}

var logger *zap.Logger

func init() {
	logConfig := zap.NewProductionConfig()
	logLevel := zap.DebugLevel
	// zapLevel := os.Getenv("LOG_LEVEL") // DEBUG|WARN|ERROR|DPANIC|PANIC|FATAL|debug|warn|error|dpanic|panic|fatal
	// if zapLevel == "" {
	// 	zapLevel = "error"
	// }
	// if zapLevel != "" {
	// 	logLevel.UnmarshalText([]byte(zapLevel))
	// }
	logConfig.Level.SetLevel(logLevel)
	logger, _ = logConfig.Build()
}

func NewKafkaMQ() (*kafkaMQ, error) {
	return nil, errors.New("not implemented")
	// logger.Debug("NewKafkaMQ successfully")
	// return &kafkaMQ{
	// 	oncePD: &sync.Once{},
	// }, nil
}

func (k *kafkaMQ) NewProducer(configMap map[string]interface{}) error {
	err := k.parseBrokers(configMap)
	if err != nil {
		return err
	}

	return k.newProducer()
}

func (k *kafkaMQ) ProduceMessage(topic string, message []byte) error {
	return k.produceMessage(&sarama.ProducerMessage{
		Topic:   topic,
		Key:     nil,
		Value:   sarama.ByteEncoder(message),
		Headers: nil,
	})
}

func (k *kafkaMQ) NewConsumerGroup(topic string, handler, retryHandler func([]byte) error, configMap map[string]interface{}) error {
	keepRunning := true

	k.parseBrokers(configMap)
	consumerGroupId := cast.ToString(configMap["consumerGroupId"])
	offset := cast.ToString(configMap["offset"])

	config := sarama.NewConfig()
	config.Version = sarama.DefaultVersion
	config.Consumer.Offsets.Initial = sarama.OffsetOldest
	if offset == globalConstant.MESSAGE_QUEUE_OFFSET_NEWEST {
		config.Consumer.Offsets.Initial = sarama.OffsetNewest
	}
	config.Consumer.Group.Rebalance.Strategy = sarama.NewBalanceStrategyRoundRobin()
	config.Metadata.Retry.Max = 30
	config.Consumer.Retry.Backoff = 2 * time.Second

	ctx, cancel := context.WithCancel(context.Background())
	client, err := sarama.NewConsumerGroup(k.brokers, consumerGroupId, config)
	if err != nil {
		logger.Error("Error creating consumer group client: %v", zap.Error(err))
		cancel()
		return err
	}

	consumer := Consumer{
		ready:   make(chan bool),
		handler: handler,
		topic:   topic,
		brokers: k.brokers,
	}

	topics := []string{consumer.topic}
	if retryHandler != nil {
		consumer.retryHandler = retryHandler
		consumer.retryTopic = fmt.Sprintf("%s.%s.%s", topic, globalConstant.MESSAGE_QUEUE_PREFIX_RETRY, consumerGroupId)
		topics = append(topics, consumer.retryTopic)

		consumer.deadLetterTopic = fmt.Sprintf("%s.%s.%s", topic, globalConstant.MESSAGE_QUEUE_PREFIX_DEAD_LETTER, consumerGroupId)
		topics = append(topics, consumer.deadLetterTopic)
	}

	logger.Debug(fmt.Sprintf("Create consumer group success for topics: %v", strings.Join(topics, ",")))

	consumptionIsPaused := false
	wg := &sync.WaitGroup{}
	wg.Add(1)
	go func() {
		defer wg.Done()
		for {
			// `Consume` should be called inside an infinite loop, when a
			// server-side rebalance happens, the consumer session will need to be
			// recreated to get the new claims

			if err := client.Consume(ctx, topics, &consumer); err != nil {
				if errors.Is(err, sarama.ErrClosedConsumerGroup) {
					return
				}
				logger.Error("Error from consumer: %v", zap.Error(err))
			}
			// check if context was cancelled, signaling that the consumer should stop
			if ctx.Err() != nil {
				return
			}
			consumer.ready = make(chan bool)
		}
	}()

	<-consumer.ready // Await till the consumer has been set up
	logger.Info("Sarama consumer up and running!...")

	sigusr1 := make(chan os.Signal, 1)
	signal.Notify(sigusr1, syscall.SIGUSR1)

	sigterm := make(chan os.Signal, 1)
	signal.Notify(sigterm, syscall.SIGINT, syscall.SIGTERM)

	for keepRunning {
		select {
		case <-ctx.Done():
			logger.Info("terminating: context cancelled")
			keepRunning = false
		case <-sigterm:
			logger.Info("terminating: via signal")
			keepRunning = false
		case <-sigusr1:
			toggleConsumptionFlow(client, &consumptionIsPaused)
		}
	}
	cancel()
	wg.Wait()
	if err = client.Close(); err != nil {
		logger.Error("Error closing client: %v", zap.Error(err))
	}

	return nil
}

func (k *kafkaMQ) parseBrokers(configMap map[string]interface{}) error {
	if configMap == nil {
		return errors.New("configMap is nil")
	}
	k.brokers = cast.ToStringSlice(configMap["brokers"])
	if len(k.brokers) == 0 {
		return errors.New("brokers is empty")
	}
	return nil
}

func (k *kafkaMQ) produceMessage(message *sarama.ProducerMessage) error {
	m, _ := message.Value.Encode()
	logger.Debug("ProduceMessage", zap.String("topic", message.Topic), zap.ByteString("message", m))

	producer, err := k.pd.borrow()
	if err != nil {
		return err
	}

	logger.Debug("Producer: producer borrowed successfully", zap.String("topic", message.Topic), zap.ByteString("message", m))

	defer k.pd.release(producer)

	logger.Debug("Producer: producer is ready", zap.String("topic", message.Topic), zap.ByteString("message", m))

	// Start kafka transaction
	err = producer.BeginTxn()
	if err != nil {
		logger.Error("unable to start txn %s\n", zap.Error(err))
		return nil
	}
	logger.Debug("Producer: txn started successfully", zap.String("topic", message.Topic), zap.ByteString("message", m))

	// Produce some records in transaction
	producer.Input() <- message

	logger.Debug("Producer: message produced successfully", zap.String("topic", message.Topic), zap.ByteString("message", m))

	// commit transaction
	err = producer.CommitTxn()
	if err != nil {
		logger.Error("Producer: unable to commit txn", zap.Any("error", err))
		for {
			if producer.TxnStatus()&sarama.ProducerTxnFlagFatalError != 0 {
				// fatal error. need to recreate producer.
				logger.Error("Producer: producer is in a fatal state, need to recreate it")
				break
			}
			// If producer is in abortable state, try to abort current transaction.
			if producer.TxnStatus()&sarama.ProducerTxnFlagAbortableError != 0 {
				err = producer.AbortTxn()
				if err != nil {
					// If an error occured just retry it.
					logger.Error("Producer: unable to abort transaction:", zap.Any("error", err))
					continue
				}
				break
			}
			// if not you can retry
			err = producer.CommitTxn()
			if err != nil {
				logger.Error("Producer: unable to commit txn", zap.Any("error", err))
				continue
			}
		}
		return nil
	}
	logger.Debug("Producer: txn committed successfully", zap.String("topic", message.Topic), zap.ByteString("message", m))
	return nil
}

func toggleConsumptionFlow(client sarama.ConsumerGroup, isPaused *bool) {
	if *isPaused {
		client.ResumeAll()
		logger.Info("Resuming consumption")
	} else {
		client.PauseAll()
		logger.Info("Pausing consumption")
	}

	*isPaused = !*isPaused
}

func (k *kafkaMQ) newProducer() error {
	k.pd = newProducerProvider(k.brokers, func() *sarama.Config {
		config := sarama.NewConfig()
		config.Version = sarama.DefaultVersion
		config.Producer.Idempotent = true
		config.Producer.Return.Errors = false
		config.Producer.RequiredAcks = sarama.WaitForAll
		config.Producer.Partitioner = sarama.NewRoundRobinPartitioner
		config.Producer.Transaction.Retry.Backoff = 10
		config.Producer.Transaction.ID = "txn_producer"
		config.Net.MaxOpenRequests = 1
		config.Producer.Timeout = 5 * time.Second
		// config.Producer.Transaction.Timeout = 5 * time.Second
		return config
	})
	logger.Debug("Producer: producer created successfully")

	// handler sigterm to release provider
	sigterm := make(chan os.Signal, 1)
	signal.Notify(sigterm, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sigterm
		k.pd.clear()
	}()

	return nil
}
