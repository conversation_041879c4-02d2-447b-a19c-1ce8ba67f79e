package subscription

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

// fillTaskDescription finds the most recent task description for a phone number - STEP 6: Task Description Generation
func fillTaskDescription(phone string) string {
	if phone == "" {
		return ""
	}

	var tasks []*task.Task
	query := bson.M{
		"phone": phone,
		"description": bson.M{
			"$nin": []interface{}{nil, ""},
		},
	}
	fields := bson.M{"description": 1}
	sort := bson.M{"createdAt": -1}

	err := globalDataAccess.GetAllByQuerySort(
		globalCollection.COLLECTION_TASK[local.ISO_CODE],
		query,
		fields,
		sort,
		&tasks,
	)

	if err != nil || len(tasks) == 0 {
		return ""
	}

	return tasks[0].Description
}
