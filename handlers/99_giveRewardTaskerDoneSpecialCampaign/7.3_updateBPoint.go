package giveRewardTaskerDoneSpecialCampaign

import (
	"context"

	grpcBpointVNConnect "gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcBPointVN"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcBPointVN"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
)

func callGrpcUpdateBpointToTasker(tasker *modelUser.Users, specialCampaignId string, point float64) error {
	client, connect, err := grpcBpointVNConnect.ConnectGRPCBPointVN(cfg.GRPC_BPOINT_VN_V3_URL)
	if err != nil {
		return err
	}
	defer connect.Close()
	req := initBpointRequest(tasker.XId, specialCampaignId, point)
	_, err = client.GivePointTaskerDoneSpecialCampaign(context.Background(), req)
	if err != nil {
		return err
	}

	// send notification
	sendNotification(tasker, "NOTIFICATION_MESSAGE_WIN_SPECIAL_CAMPAIGN_TITLE", "NOTIFICATION_MESSAGE_WIN_SPECIAL_CAMPAIGN_BODY", "bPointsHistory", 6)

	return nil
}

func initBpointRequest(taskerId, specialCampaignId string, point float64) *grpcBPointVN.BPointRequest {
	return &grpcBPointVN.BPointRequest{
		TaskerId:          taskerId,
		SpecialCampaignId: specialCampaignId,
		Point:             point,
	}
}
