import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import {
  trackingScheduleStatus,
  revertTimeZone,
} from '/server/helper-function';

// Auto disable tasker rating with old task ( <= 3 days ).
const runAutoDisableTaskerRating = () => {
  // Move sync cron to Go
  return;
	console.log("Start runAutoDisableTaskerRating ...");
  var message = null;
  try {
    const lastThreeDay = moment().subtract(3, 'days').toDate();
    const last7Days = moment().subtract(7, 'days').toDate();
    Meteor.appCollection.Task.update({
      date: {
        $gte: last7Days,
        $lt: lastThreeDay
      },
      status: 'DONE',
      taskerRated: false,
    }, {
      $unset: {
        taskerRated: 1
      }
    }, {
      multi: true,
    });
  } catch (e) {
    message = e;
  }
	console.log("Finished runAutoDisableTaskerRating ");
  // Tracking
  trackingScheduleStatus({name: 'AutoDisableTaskerRating', nextRunAt: SyncedCron.nextScheduledAtDate('autoDisableTaskerRating'), message});
};

  Meteor.methods({
    // Auto Rate
    autoDisableTaskerRating() {
      SyncedCron.add({
        name: 'autoDisableTaskerRating',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016,1,1,5)).getHours();
          // Run at 5:00 every 3 days with timezone GMT+7.
          return parser.cron(`0 0 ${hourInTimeZoneServer} 1/3 * ? *`, true);
        },
        job() {
          runAutoDisableTaskerRating();
        },
      });
	    console.log("Added autoDisableTaskerRating cron");
    },
    'runNow.autoDisableTaskerRating'() {
      runAutoDisableTaskerRating();
    },
  });
