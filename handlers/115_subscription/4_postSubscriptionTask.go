package subscription

import (
	"fmt"
	"log"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSubscription "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscription"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// postSubscriptionTask creates and posts a new task from subscription - STEP 2: Task Creation & Posting
func postSubscriptionTask(subs *modelSubscription.Subscription, nextDate time.Time) float64 {
	createdAt := globalLib.GetCurrentTime(local.TimeZone)

	// Create basic task data as map for insertion
	newTaskId := globalLib.GenerateObjectId()
	taskData := &modelTask.Task{
		XId:              newTaskId,
		ServiceId:        getServiceID(subs),
		Date:             globalLib.ParseTimestampFromDate(nextDate),
		Duration:         subs.Duration,
		Address:          subs.Address,
		Lat:              subs.Location.Lat,
		Lng:              subs.Location.Lng,
		AskerId:          subs.UserId,
		Status:           globalConstant.TASK_STATUS_POSTED,
		CreatedAt:        globalLib.GetCurrentTimestamp(local.TimeZone),
		Rated:            false,
		SubscriptionId:   subs.XId,
		AutoChooseTasker: true,
		Payment: &modelTask.TaskPayment{
			Method: globalConstant.PAYMENT_METHOD_BANK_TRANSFER,
		},
		Description:    subs.Description,
		IsoCode:        local.ISO_CODE,
		CountryCode:    globalConstant.COUNTRY_CODE_VN,
		OriginCurrency: &globalConstant.CURRENCY_SIGN_VN,
	}

	// Set service name if available
	if subs.ServiceName != "" {
		taskData.ServiceName = mapServiceName(subs.ServiceName)
	}

	// Set task note if available
	if subs.TaskNote != "" {
		taskData.TaskNote = subs.TaskNote
	}

	// Set cooking detail if available
	if subs.CookingDetail != nil {
		// Set eating time
		eatingTime := nextDate.Add(time.Duration(subs.Duration) * time.Hour)

		// Copy existing cooking detail fields if needed
		taskData.CookingDetail = &modelTask.TaskCookingDetail{EatingTime: timestamppb.New(eatingTime)}
	}

	// Set child care detail if available
	if subs.DetailChildCare != nil {
		taskData.DetailChildCare = subs.DetailChildCare
	}

	// Calculate price
	priceOfTask := priceOfTaskInSubscription(subs, nextDate)

	taskData.Cost = priceOfTask.CostDetail.Cost
	taskPrice := priceOfTask.CostDetail.Cost

	if priceOfTask.CostDetail.FinalCost > 0 {
		taskPrice = priceOfTask.CostDetail.FinalCost
	}

	// Get user information
	user, err := getUserInfo(subs.UserId)
	if user == nil {
		globalLib.PostToSlack(
			cfg.SlackToken,
			globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE],
			globalConstant.SLACK_USER_NAME,
			fmt.Sprintf("[Sync cron vn v3] Error getting user info: subscriptionId: %s, error: %v", subs.XId, err),
		)
		return 0
	}

	// Set user-related fields
	setUserRelatedFields(taskData, user, subs)

	// Fill task description if empty
	if taskData.Description == "" {
		description := fillTaskDescription(user.Phone)
		if description != "" {
			taskData.Description = description
		}
	}

	// Set additional fields
	setAdditionalFields(taskData, subs)

	// Get service text if not available
	if subs.ServiceText == nil {
		serviceText := getServiceText(subs.ServiceId)
		if serviceText != nil {
			taskData.ServiceText = serviceText
		}
	} else {
		taskData.ServiceText = subs.ServiceText
	}

	// Insert task
	err = insertTask(taskData)
	if err != nil {
		globalLib.PostToSlack(
			cfg.SlackToken,
			globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE],
			globalConstant.SLACK_USER_NAME,
			fmt.Sprintf("[Sync cron vn v3] Error inserting task: subscriptionId: %s, error: %v", subs.XId, err),
		)
		return 0
	}

	// Update user's last posted task
	updateUserLastPostedTask(subs.UserId, createdAt)

	// Update subscription history
	updateSubscriptionHistory(subs.XId, newTaskId, nextDate)

	// Send notification to asker
	sendNotificationToAsker(newTaskId, subs.UserId, user.Language, taskData.ServiceText)

	// Handle force accept tasker or send to available taskers
	handleTaskerAssignment(subs, taskData, newTaskId, user)

	// Check if subscription is finished
	checkSubscriptionCompletion(subs, newTaskId)

	return taskPrice
}

// Helper functions

func getServiceID(subs *modelSubscription.Subscription) string {
	if subs.TaskServiceId != "" {
		return subs.TaskServiceId
	}
	return subs.ServiceId
}

func getUserInfo(userID string) (*modelUser.Users, error) {
	var user *modelUser.Users
	query := bson.M{"_id": userID}
	fields := bson.M{
		"phone":           1,
		"name":            1,
		"taskNote":        1,
		"blackList":       1,
		"language":        1,
		"favouriteTasker": 1,
		"isBlacklist":     1,
	}

	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USERS, query, fields, &user)
	return user, err
}

func setUserRelatedFields(taskData *modelTask.Task, user *modelUser.Users, subs *modelSubscription.Subscription) {
	if len(user.BlackList) > 0 {
		taskData.BlackList = user.BlackList
	}
	taskData.Phone = user.Phone
	taskData.ContactName = user.Name
	if user.TaskNote != "" {
		taskData.TaskNote = user.TaskNote
	}
}

func setAdditionalFields(taskData *modelTask.Task, subs *modelSubscription.Subscription) {
	if subs.HomeType != "" {
		taskData.HomeType = subs.HomeType
	}
	if subs.IsPremium {
		taskData.IsPremium = subs.IsPremium
	}
	if subs.IsEco {
		taskData.IsEco = subs.IsEco
	}
	if len(subs.Addons) > 0 {
		taskData.Addons = subs.Addons
	}
	if len(subs.Pet) > 0 {
		taskData.Pet = subs.Pet
	}
}

func getServiceText(serviceID string) *modelService.ServiceText {
	var service *modelService.Service
	query := bson.M{"_id": serviceID}
	fields := bson.M{"text": 1}

	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], query, fields, &service)
	if service == nil {
		return nil
	}
	return service.Text
}

func insertTask(taskData *modelTask.Task) error {
	return globalDataAccess.InsertOne(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskData)
}

func updateUserLastPostedTask(userID string, createdAt time.Time) {
	query := bson.M{"_id": userID}
	update := bson.M{"$set": bson.M{"lastPostedTask": createdAt}}
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_USERS, query, update)
}

func updateSubscriptionHistory(subsID, taskID string, nextDate time.Time) {
	query := bson.M{"_id": subsID}
	update := bson.M{
		"$push": bson.M{
			"taskIds": taskID,
			"history": nextDate,
		},
	}
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], query, update)
}

func handleTaskerAssignment(subs *modelSubscription.Subscription, taskData *modelTask.Task, taskID string, user *modelUser.Users) {
	if subs.ForceAcceptTaskerId != "" {
		// Handle force accept tasker logic
		handleForceAcceptTasker(subs, taskData, taskID, user)
	} else {
		// Send task to available taskers
		if !user.IsBlacklist {
			sendTaskToTasker(taskID, subs.ServiceId, user.FavouriteTasker)
		}
	}
}

func handleForceAcceptTasker(subs *modelSubscription.Subscription, taskData *modelTask.Task, taskID string, user *modelUser.Users) {
	// Get tasker information
	var tasker *modelUser.Users
	query := bson.M{
		"_id":    subs.ForceAcceptTaskerId,
		"status": bson.M{"$in": [2]string{globalConstant.USER_STATUS_ACTIVE, globalConstant.USER_STATUS_BLOCKED}},
	}
	fields := bson.M{
		"avatar":            1,
		"name":              1,
		"avgRating":         1,
		"taskDone":          1,
		"phone":             1,
		"status":            1,
		"notComeLockNumber": 1,
	}

	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USERS, query, fields, &tasker)
	if err != nil || !canTaskerAccept(tasker) {
		// Post to Slack about tasker not available
		message := fmt.Sprintf("Cần CS xử lý: Công việc của Subscription đã đăng và KHÔNG confirm được do Tasker %s đã bị khóa. CV của Asker: %s", tasker.Phone, user.Phone)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
		return
	}

	// Check for conflicts with other tasks
	if hasTaskConflict(subs.ForceAcceptTaskerId, taskData) {
		message := fmt.Sprintf("Cần CS xử lý: Công việc của Subscription đã đăng và KHÔNG confirm được do Tasker %s - %s trùng giờ làm việc. CV của Asker: %s", tasker.Phone, tasker.Name, user.Phone)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
		return
	}

	// Force accept the task
	acceptedTasker := bson.M{
		"taskerId":   subs.ForceAcceptTaskerId,
		"avatar":     tasker.Avatar,
		"name":       tasker.Name,
		"avgRating":  tasker.AvgRating,
		"taskDone":   tasker.TaskDone,
		"acceptedAt": globalLib.GetCurrentTime(local.TimeZone),
	}

	update := bson.M{
		"$set": bson.M{
			"acceptedTasker": []bson.M{acceptedTasker},
			"status":         globalConstant.TASK_STATUS_CONFIRMED,
			"updatedAt":      globalLib.GetCurrentTime(local.TimeZone),
		},
	}

	globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskID, update)

	// Send notification to tasker
	sendConfirmationNotification(subs.ForceAcceptTaskerId, taskID)

	// Alert if tasker status is not ACTIVE
	if tasker.Status != "ACTIVE" {
		message := fmt.Sprintf("CS lưu ý: Tasker %s status %s đã được thêm vào subscription task (Asker: %s)", tasker.Phone, tasker.Status, user.Phone)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
	}
}

func hasTaskConflict(taskerID string) bool {
	// Check for conflicting tasks - simplified version
	var confirmedTasks []bson.M
	query := bson.M{
		"status":                  globalConstant.TASK_STATUS_CONFIRMED,
		"acceptedTasker.taskerId": taskerID,
	}

	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], query, bson.M{}, &confirmedTasks)
	if err != nil {
		return false
	}

	// Simple conflict check - in real implementation, you'd check time overlaps
	return len(confirmedTasks) > 0
}

func sendConfirmationNotification(taskerID, taskID string) {
	// Get tasker language (default to Vietnamese)
	var tasker modelUser.Users
	query := bson.M{"_id": taskerID}
	fields := bson.M{"language": 1}
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USERS, query, fields, &tasker)

	lang := globalConstant.LANG_VI
	if err == nil && tasker.Language != "" {
		lang = tasker.Language
	}

	// Create notification data
	title := localization.GetLocalizeObject("NOTIFICATION_MESSAGE_TASKER_CONFIRMED_TITLE", "")
	text := localization.GetLocalizeObject("NOTIFICATION_MESSAGE_TASKER_CONFIRMED", "")

	// Create notification for database
	notiInfos := []interface{}{
		&notification.Notification{
			XId:         globalLib.GenerateObjectId(),
			UserId:      taskerID,
			TaskId:      taskID,
			Type:        1,
			Title:       globalLib.LocalizeServiceName(lang, title),
			Description: globalLib.LocalizeServiceName(lang, text),
			CreatedAt:   timestamppb.New(globalLib.GetCurrentTime(local.TimeZone)),
			NavigateTo:  "TaskDetail",
		},
	}

	// Create user IDs for push notification
	userIds := []*pushNotificationRequest.PushNotificationRequestUserIds{
		{
			UserId:   taskerID,
			Language: lang,
		},
	}

	// Create title and body with localization
	titleText := &modelService.ServiceText{
		Vi: globalLib.LocalizeServiceName(lang, title),
	}
	bodyText := &modelService.ServiceText{
		Vi: globalLib.LocalizeServiceName(lang, text),
	}

	// Create payload
	payload := &pushNotificationRequest.PushNotificationRequestPayload{
		Type:       1, // Task confirmation notification type
		TaskId:     taskID,
		NavigateTo: "TaskDetail",
	}

	// Use lib.SendNotification instead of custom gRPC implementation
	lib.SendNotification(notiInfos, userIds, titleText, bodyText, payload, true)
}

func checkSubscriptionCompletion(subs *modelSubscription.Subscription, taskID string) {
	// Use existing subscription data instead of querying DB again
	if len(subs.Schedule) == 0 {
		return
	}

	// Calculate current progress
	// After adding this task to history, check if all tasks are completed
	totalTasks := len(subs.Schedule)
	completedTasks := len(subs.History) + 1 // +1 for the task we just added

	// Check if this is the last task in the subscription
	if completedTasks >= totalTasks {
		// Mark subscription as DONE
		update := bson.M{"$set": bson.M{
			"status":    "DONE",
			"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
		}}
		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], subs.XId, update)

		// Mark task as final subscription task
		taskUpdate := bson.M{"$set": bson.M{
			"finalSubscriptionTask": true,
			"updatedAt":             globalLib.GetCurrentTime(local.TimeZone),
		}}
		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskID, taskUpdate)

		log.Printf("Subscription %s completed. Final task: %s (completed %d/%d tasks)",
			subs.XId, taskID, completedTasks, totalTasks)
	} else {
		log.Printf("Subscription %s progress: %d/%d tasks completed",
			subs.XId, completedTasks, totalTasks)
	}
}

// Additional helper functions for subscription processing
