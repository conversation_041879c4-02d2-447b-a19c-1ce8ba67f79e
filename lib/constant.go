/*
 * @File: constant.go
 * @Description: Define errorCode, constant for service
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 10/12/2020
 * @UpdatedBy: ngoctb3
 */
package lib

// error
const (
	ERROR_EMAIL_CONFIG_NOT_SET = "ERROR_EMAIL_CONFIG_NOT_SET"
	ERROR_FROM_EMAIL_NIL       = "ERROR_FROM_EMAIL_NIL"
	ERROR_EMAIL_INVALID        = "ERROR_EMAIL_INVALID"
	ERROR_TO_EMAIL_NIL         = "ERROR_TO_EMAIL_NIL"
	ERROR_CONTENT_NIL          = "ERROR_CONTENT_NIL"
	ERROR_SUBJECT_EMAIL_NIL    = "ERROR_SUBJECT_EMAIL_NIL"
	ERROR_TO_EMAIL_INVALID     = "ERROR_TO_EMAIL_INVALID"
	ERROR_CANCEL_FEE_INVALID   = "ERROR_CANCEL_FEE_INVALID"

	ERROR_EMAIL_RECEIPT_TASK_EMPTY = "ERROR_EMAIL_RECEIPT_TASK_EMPTY"
	ERROR_EMAIL_VERIFY_USER_EMPTY  = "ERROR_EMAIL_VERIFY_USER_EMPTY"

	ERROR_SUBSCRIPTION_ID_REQUIRED     = "ERROR_SUBSCRIPTION_ID_REQUIRED"
	ERROR_USER_ID_REQUIRED             = "ERROR_USER_ID_REQUIRED"
	ERROR_SUBSCRIPTION_NOT_FOUND       = "ERROR_SUBSCRIPTION_NOT_FOUND"
	ERROR_USER_EMAIL_NOT_EXISTS        = "ERROR_USER_EMAIL_NOT_EXISTS"
	ERROR_BOOKING_ID_REQUIRED          = "ERROR_BOOKING_ID_REQUIRED"
	ERROR_BOOKING_NOT_FOUND            = "ERROR_BOOKING_NOT_FOUND"
	ERROR_USER_NOT_FOUND               = "ERROR_USER_NOT_FOUND"
	EMAIL_UNVERIFIED                   = "EMAIL_UNVERIFIED"
	ERROR_CANCELLATION_REASON_REQUIRED = "ERROR_CANCELLATION_REASON_REQUIRED"
	ERROR_SERVICE_NOT_FOUND            = "ERROR_SERVICE_NOT_FOUND"
	ERROR_SETTING_NOT_FOUND            = "ERROR_SETTING_NOT_FOUND"

	ERROR_SENT_FAILED             = "ERROR_SENT_FAILED"
	ERROR_TRANSACTION_ID_REQUIRED = "ERROR_TRANSACTION_ID_REQUIRED"
	ERROR_TRANSACTION_NOT_FOUND   = "ERROR_TRANSACTION_NOT_FOUND"
	ERROR_CARD_NOT_FOUND          = "ERROR_CARD_NOT_FOUND"

	ERROR_BUSINESS_ID_REQUIRED    = "ERROR_BUSINESS_ID_REQUIRED"
	ERROR_BUSINESS_NOT_FOUND      = "ERROR_BUSINESS_NOT_FOUND"
	ERROR_FILE_TEMPLATE_NOT_FOUND = "ERROR_FILE_TEMPLATE_NOT_FOUND"
	ERROR_FAILED_TO_CREATE_FILE   = "ERROR_FAILED_TO_CREATE_FILE"
	ERROR_FAILED_TO_DELETE_FILE   = "ERROR_FAILED_TO_DELETE_FILE"
)

// some constant
const (
	SENDING_EMAIL           = "Sending some email"
	DATE_FORMAT_RECEIPT     = "02/01/2006"
	DATETIME_FORMAT_RECEIPT = "15:04, 02/01/2006"
	DEFAULT_AVATAR          = "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/strDe7qpzQyFCSLkg"
)
