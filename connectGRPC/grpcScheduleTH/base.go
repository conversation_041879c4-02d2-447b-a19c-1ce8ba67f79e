package grpcScheduleTH

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcScheduleTH"
	"google.golang.org/grpc"
)

func ConnectGRPCScheduleTH(endpoint string) (grpcScheduleTH.ScheduleTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcScheduleTH.NewScheduleTHClient(connect)
	return client, connect, err
}
