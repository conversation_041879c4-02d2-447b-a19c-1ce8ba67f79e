package globalCollection

import "gitlab.com/btaskee/go-services-model-v2/globalConstant"

type Collection map[string]string

var (
	// <PERSON><PERSON>c dòng lệnh xoá user trong source sẽ không sử dụng nữa, thay vào đó insert vào bảng này, sync cron sẽ xoá
	COLLECTION_NEED_DELETE_USER = Collection{
		globalConstant.ISO_CODE_VN:   "vn_needDeleteUser",
		globalConstant.ISO_CODE_TH:   "th_needDeleteUser",
		globalConstant.ISO_CODE_INDO: "id_needDeleteUser",
		globalConstant.ISO_CODE_MY:   "my_needDeleteUser",
	}
	COLLECTION_INCENTIVE = Collection{
		globalConstant.ISO_CODE_VN:   "vn_incentive",
		globalConstant.ISO_CODE_TH:   "th_incentive",
		globalConstant.ISO_CODE_INDO: "id_incentive",
		globalConstant.ISO_CODE_MY:   "my_incentive",
	}
	COLLECTION_WORKINGPLACES = Collection{
		globalConstant.ISO_CODE_VN:   "vn_workingPlaces",
		globalConstant.ISO_CODE_TH:   "th_workingPlaces",
		globalConstant.ISO_CODE_INDO: "id_workingPlaces",
		globalConstant.ISO_CODE_MY:   "my_workingPlaces",
	}
	COLLECTION_WEEKLYPAYOUT = Collection{
		globalConstant.ISO_CODE_VN:   "weeklyPayout",
		globalConstant.ISO_CODE_TH:   "weeklyPayout",
		globalConstant.ISO_CODE_INDO: "id_weeklyPayout",
		globalConstant.ISO_CODE_MY:   "my_weeklyPayout",
	}
	COLLECTION_USERS               = "users"
	COLLECTION_CARD_PAYMENT_CONFIG = Collection{
		globalConstant.ISO_CODE_VN:   "vn_cardPaymentConfig",
		globalConstant.ISO_CODE_TH:   "th_cardPaymentConfig",
		globalConstant.ISO_CODE_INDO: "id_cardPaymentConfig",
		globalConstant.ISO_CODE_MY:   "my_cardPaymentConfig",
	}
	COLLECTION_FATRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "FATransaction",
		globalConstant.ISO_CODE_TH:   "th_FATransaction",
		globalConstant.ISO_CODE_INDO: "id_FATransaction",
		globalConstant.ISO_CODE_MY:   "my_FATransaction",
	}
	COLLECTION_FINANCIAL_ACCOUNT = Collection{
		globalConstant.ISO_CODE_VN:   "vn_financialAccount",
		globalConstant.ISO_CODE_TH:   "th_financialAccount",
		globalConstant.ISO_CODE_INDO: "id_financialAccount",
		globalConstant.ISO_CODE_MY:   "my_financialAccount",
	}
	////
	COLLECTION_USER_LOCATION_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_userLocationHistory",
		globalConstant.ISO_CODE_TH:   "th_userLocationHistory",
		globalConstant.ISO_CODE_INDO: "id_userLocationHistory",
		globalConstant.ISO_CODE_MY:   "my_userLocationHistory",
	}
	////
	COLLECTION_GIFT = Collection{
		globalConstant.ISO_CODE_VN:   "vn_gift",
		globalConstant.ISO_CODE_TH:   "th_gift",
		globalConstant.ISO_CODE_INDO: "id_gift",
		globalConstant.ISO_CODE_MY:   "my_gift",
	}
	COLLECTION_HOUSEKEEPING = Collection{
		globalConstant.ISO_CODE_VN:   "vn_hostel",
		globalConstant.ISO_CODE_TH:   "th_hostel",
		globalConstant.ISO_CODE_INDO: "id_hostel",
		globalConstant.ISO_CODE_MY:   "my_hostel",
	}
	////
	COLLECTION_USER_ACTIVATION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_userActivation",
		globalConstant.ISO_CODE_TH:   "th_userActivation",
		globalConstant.ISO_CODE_INDO: "id_userActivation",
		globalConstant.ISO_CODE_MY:   "my_userActivation",
	}
	////
	COLLECTION_USER_ACTION_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_userActionHistory",
		globalConstant.ISO_CODE_TH:   "th_userActionHistory",
		globalConstant.ISO_CODE_INDO: "id_userActionHistory",
		globalConstant.ISO_CODE_MY:   "my_userActionHistory",
	}
	////
	COLLECTION_MARKETING_CAMPAIGN = Collection{
		globalConstant.ISO_CODE_VN:   "vn_marketingCampaign",
		globalConstant.ISO_CODE_TH:   "th_marketingCampaign",
		globalConstant.ISO_CODE_INDO: "id_marketingCampaign",
		globalConstant.ISO_CODE_MY:   "my_marketingCampaign",
	}
	////
	COLLECTION_NOTIFICATION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_notification",
		globalConstant.ISO_CODE_TH:   "th_notification",
		globalConstant.ISO_CODE_INDO: "id_notification",
		globalConstant.ISO_CODE_MY:   "my_notification",
	}
	COLLECTION_PAYMENT_CARD = Collection{
		globalConstant.ISO_CODE_VN:   "vn_paymentCard",
		globalConstant.ISO_CODE_TH:   "th_paymentCard",
		globalConstant.ISO_CODE_INDO: "id_paymentCard",
		globalConstant.ISO_CODE_MY:   "my_paymentCard",
	}
	COLLECTION_PAYMENT_TRANSACTION = "paymentTransaction"
	////
	COLLECTION_POINT_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_pointTransaction",
		globalConstant.ISO_CODE_TH:   "th_pointTransaction",
		globalConstant.ISO_CODE_INDO: "id_pointTransaction",
		globalConstant.ISO_CODE_MY:   "my_pointTransaction",
	}
	////
	COLLECTION_PROMOTION_CODE = Collection{
		globalConstant.ISO_CODE_VN:   "vn_promotionCode",
		globalConstant.ISO_CODE_TH:   "th_promotionCode",
		globalConstant.ISO_CODE_INDO: "id_promotionCode",
		globalConstant.ISO_CODE_MY:   "my_promotionCode",
	}
	////
	COLLECTION_PROMOTION_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_promotionHistory",
		globalConstant.ISO_CODE_TH:   "th_promotionHistory",
		globalConstant.ISO_CODE_INDO: "id_promotionHistory",
		globalConstant.ISO_CODE_MY:   "my_promotionHistory",
	}
	COLLECTION_RATING = Collection{
		globalConstant.ISO_CODE_VN:   "vn_rating",
		globalConstant.ISO_CODE_TH:   "th_rating",
		globalConstant.ISO_CODE_INDO: "id_rating",
		globalConstant.ISO_CODE_MY:   "my_rating",
	}
	COLLECTION_SERVICE = Collection{
		globalConstant.ISO_CODE_VN:   "service",
		globalConstant.ISO_CODE_TH:   "th_service",
		globalConstant.ISO_CODE_INDO: "id_service",
		globalConstant.ISO_CODE_MY:   "my_service",
	}
	COLLECTION_TASK = Collection{
		globalConstant.ISO_CODE_VN:   "vn_task",
		globalConstant.ISO_CODE_TH:   "th_task",
		globalConstant.ISO_CODE_INDO: "id_task",
		globalConstant.ISO_CODE_MY:   "my_task",
	}
	////
	COLLECTION_SUBSCRIPTION_REQUEST = Collection{
		globalConstant.ISO_CODE_VN:   "vn_subscriptionRequest",
		globalConstant.ISO_CODE_TH:   "th_subscriptionRequest",
		globalConstant.ISO_CODE_INDO: "id_subscriptionRequest",
		globalConstant.ISO_CODE_MY:   "my_subscriptionRequest",
	}
	COLLECTION_TASKER_TASK_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerTaskHistory",
		globalConstant.ISO_CODE_TH:   "th_taskerTaskHistory",
		globalConstant.ISO_CODE_INDO: "id_taskerTaskHistory",
		globalConstant.ISO_CODE_MY:   "my_taskerTaskHistory",
	}
	COLLECTION_PURCHASE_ORDER = Collection{
		globalConstant.ISO_CODE_VN:   "vn_purchaseOrder",
		globalConstant.ISO_CODE_TH:   "th_purchaseOrder",
		globalConstant.ISO_CODE_INDO: "id_purchaseOrder",
		globalConstant.ISO_CODE_MY:   "my_purchaseOrder",
	}
	COLLECTION_RAIX_PUSH_APP_TOKENS = "_raix_push_app_tokens"
	COLLECTION_TRANSACTION          = Collection{
		globalConstant.ISO_CODE_VN:   "vn_transaction",
		globalConstant.ISO_CODE_TH:   "th_transaction",
		globalConstant.ISO_CODE_INDO: "id_transaction",
		globalConstant.ISO_CODE_MY:   "my_transaction",
	}
	////
	COLLECTION_REDEEM_GIFT_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_redeemGiftTransaction",
		globalConstant.ISO_CODE_TH:   "th_redeemGiftTransaction",
		globalConstant.ISO_CODE_INDO: "id_redeemGiftTransaction",
		globalConstant.ISO_CODE_MY:   "my_redeemGiftTransaction",
	}
	COLLECTION_REGISTER_SERVICE = Collection{
		globalConstant.ISO_CODE_VN:   "vn_registerService",
		globalConstant.ISO_CODE_TH:   "th_registerService",
		globalConstant.ISO_CODE_INDO: "id_registerService",
		globalConstant.ISO_CODE_MY:   "my_registerService",
	}
	COLLECTION_REWARD = Collection{
		globalConstant.ISO_CODE_VN:   "reward",
		globalConstant.ISO_CODE_TH:   "th_reward",
		globalConstant.ISO_CODE_INDO: "id_reward",
		globalConstant.ISO_CODE_MY:   "my_reward",
	}
	COLLECTION_SERVICE_CHANNEL = Collection{
		globalConstant.ISO_CODE_VN:   "vn_serviceChannel",
		globalConstant.ISO_CODE_TH:   "th_serviceChannel",
		globalConstant.ISO_CODE_INDO: "id_serviceChannel",
		globalConstant.ISO_CODE_MY:   "my_serviceChannel",
	}
	COLLECTION_TASK_SCHEDULE = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskSchedule",
		globalConstant.ISO_CODE_TH:   "th_taskSchedule",
		globalConstant.ISO_CODE_INDO: "id_taskSchedule",
		globalConstant.ISO_CODE_MY:   "my_taskSchedule",
	}
	COLLECTION_TASK_REPORT_TASKER = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskReportTasker",
		globalConstant.ISO_CODE_TH:   "th_taskReportTasker",
		globalConstant.ISO_CODE_INDO: "id_taskReportTasker",
		globalConstant.ISO_CODE_MY:   "my_taskReportTasker",
	}
	COLLECTION_SETTING_COUNTRY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_settingCountry",
		globalConstant.ISO_CODE_TH:   "th_settingCountry",
		globalConstant.ISO_CODE_INDO: "id_settingCountry",
		globalConstant.ISO_CODE_MY:   "my_settingCountry",
	}
	COLLECTION_SETTING_RATING = Collection{
		globalConstant.ISO_CODE_VN:   "vn_settingRating",
		globalConstant.ISO_CODE_TH:   "th_settingRating",
		globalConstant.ISO_CODE_INDO: "id_settingRating",
		globalConstant.ISO_CODE_MY:   "my_settingRating",
	}
	COLLECTION_SETTING_SYSTEM = Collection{
		globalConstant.ISO_CODE_VN:   "settingSystem",
		globalConstant.ISO_CODE_TH:   "th_settingSystem",
		globalConstant.ISO_CODE_INDO: "id_settingSystem",
		globalConstant.ISO_CODE_MY:   "my_settingSystem",
	}
	COLLECTION_SMS_VIET_GUYS = "smsVietGuys"
	COLLECTION_SUBSCRIPTION  = Collection{
		globalConstant.ISO_CODE_VN:   "vn_subscription",
		globalConstant.ISO_CODE_TH:   "th_subscription",
		globalConstant.ISO_CODE_INDO: "id_subscription",
		globalConstant.ISO_CODE_MY:   "my_subscription",
	}
	COLLECTION_SUBSCRIPTION_SETTINGS = Collection{
		globalConstant.ISO_CODE_VN:   "subscriptionSettings",
		globalConstant.ISO_CODE_TH:   "th_subscriptionSettings",
		globalConstant.ISO_CODE_INDO: "id_subscriptionSettings",
		globalConstant.ISO_CODE_MY:   "my_subscriptionSettings",
	}
	COLLECTION_TASKER_SETTINGS = Collection{
		globalConstant.ISO_CODE_VN:   "taskerSettings",
		globalConstant.ISO_CODE_TH:   "th_taskerSettings",
		globalConstant.ISO_CODE_INDO: "id_taskerSettings",
		globalConstant.ISO_CODE_MY:   "my_taskerSettings",
	}
	////
	COLLECTION_TASKER_TRAINING = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerTraining",
		globalConstant.ISO_CODE_TH:   "th_taskerTraining",
		globalConstant.ISO_CODE_INDO: "id_taskerTraining",
		globalConstant.ISO_CODE_MY:   "my_taskerTraining",
	}
	////
	COLLECTION_FEEDBACK = Collection{
		globalConstant.ISO_CODE_VN:   "vn_feedback",
		globalConstant.ISO_CODE_TH:   "th_feedback",
		globalConstant.ISO_CODE_INDO: "id_feedback",
		globalConstant.ISO_CODE_MY:   "my_feedback",
	}
	COLLECTION_OUTSTANDING_PAYMENT = Collection{
		globalConstant.ISO_CODE_VN:   "vn_outstandingPayment",
		globalConstant.ISO_CODE_TH:   "th_outstandingPayment",
		globalConstant.ISO_CODE_INDO: "id_outstandingPayment",
		globalConstant.ISO_CODE_MY:   "my_outstandingPayment",
	}
	////
	COLLECTION_BANNED_REFERRAL_CODE = Collection{
		globalConstant.ISO_CODE_VN:   "vn_bannedReferralCode",
		globalConstant.ISO_CODE_TH:   "th_bannedReferralCode",
		globalConstant.ISO_CODE_INDO: "id_bannedReferralCode",
		globalConstant.ISO_CODE_MY:   "my_bannedReferralCode",
	}
	COLLECTION_ASKER_RATING = Collection{
		globalConstant.ISO_CODE_VN:   "vn_askerRating",
		globalConstant.ISO_CODE_TH:   "th_askerRating",
		globalConstant.ISO_CODE_INDO: "id_askerRating",
		globalConstant.ISO_CODE_MY:   "my_askerRating",
	}
	////
	COLLECTION_POINT_CAMPAIGN = Collection{
		globalConstant.ISO_CODE_VN:   "vn_pointCampaign",
		globalConstant.ISO_CODE_TH:   "th_pointCampaign",
		globalConstant.ISO_CODE_INDO: "id_pointCampaign",
		globalConstant.ISO_CODE_MY:   "my_pointCampaign",
	}
	////
	COLLECTION_POINT_CAMPAIGN_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_pointCampaignHistory",
		globalConstant.ISO_CODE_TH:   "th_pointCampaignHistory",
		globalConstant.ISO_CODE_INDO: "id_pointCampaignHistory",
		globalConstant.ISO_CODE_MY:   "my_pointCampaignHistory",
	}
	COLLECTION_ASKER_BLACK_LIST_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_askerBlacklistHistory",
		globalConstant.ISO_CODE_TH:   "th_askerBlacklistHistory",
		globalConstant.ISO_CODE_INDO: "id_askerBlacklistHistory",
		globalConstant.ISO_CODE_MY:   "my_askerBlacklistHistory",
	}
	COLLECTION_HISTORY_TASKS = Collection{
		globalConstant.ISO_CODE_VN:   "vn_history_tasks",
		globalConstant.ISO_CODE_TH:   "th_history_tasks",
		globalConstant.ISO_CODE_INDO: "id_history_tasks",
		globalConstant.ISO_CODE_MY:   "my_history_tasks",
	}
	COLLECTION_MOMO_TRANSACTION     = "momoTransaction"
	COLLECTION_REMOVED_PAYMENT_CARD = Collection{
		globalConstant.ISO_CODE_VN:   "vn_removedPaymentCard",
		globalConstant.ISO_CODE_TH:   "th_removedPaymentCard",
		globalConstant.ISO_CODE_INDO: "id_removedPaymentCard",
		globalConstant.ISO_CODE_MY:   "my_removedPaymentCard",
	}
	COLLECTION_ZALOPAY_TRANSACTION = "zaloPayTransaction"
	COLLECTION_ASKER_SETTINGS      = Collection{
		globalConstant.ISO_CODE_VN:   "askerSetting",
		globalConstant.ISO_CODE_TH:   "th_askerSetting",
		globalConstant.ISO_CODE_INDO: "id_askerSetting",
		globalConstant.ISO_CODE_MY:   "my_askerSetting",
	}
	COLLECTION_REPORT_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_reportTransaction",
		globalConstant.ISO_CODE_TH:   "th_reportTransaction",
		globalConstant.ISO_CODE_INDO: "id_reportTransaction",
		globalConstant.ISO_CODE_MY:   "my_reportTransaction",
	}
	COLLECTION_PAYMENT_PROCESS = Collection{
		globalConstant.ISO_CODE_VN:   "vn_paymentProcess",
		globalConstant.ISO_CODE_TH:   "th_paymentProcess",
		globalConstant.ISO_CODE_INDO: "id_paymentProcess",
		globalConstant.ISO_CODE_MY:   "my_paymentProcess",
	}
	COLLECTION_NOT_COME_LOCK_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_notComeLockHistory",
		globalConstant.ISO_CODE_TH:   "th_notComeLockHistory",
		globalConstant.ISO_CODE_INDO: "id_notComeLockHistory",
		globalConstant.ISO_CODE_MY:   "my_notComeLockHistory",
	}
	COLLECTION_TASKER_REFERRAL = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerReferral",
		globalConstant.ISO_CODE_TH:   "th_taskerReferral",
		globalConstant.ISO_CODE_INDO: "id_taskerReferral",
		globalConstant.ISO_CODE_MY:   "my_taskerReferral",
	}
	COLLECTION_TRUST_POINT_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_trustPointHistory",
		globalConstant.ISO_CODE_TH:   "th_trustPointHistory",
		globalConstant.ISO_CODE_INDO: "id_trustPointHistory",
		globalConstant.ISO_CODE_MY:   "my_trustPointHistory",
	}
	////
	COLLECTION_BEMPLOYEE = Collection{
		globalConstant.ISO_CODE_VN:   "bEmployee",
		globalConstant.ISO_CODE_TH:   "th_bEmployee",
		globalConstant.ISO_CODE_INDO: "id_bEmployee",
		globalConstant.ISO_CODE_MY:   "my_bEmployee",
	}
	COLLECTION_TASKER_VIOLATE = Collection{
		globalConstant.ISO_CODE_VN:   "taskerViolate",
		globalConstant.ISO_CODE_TH:   "th_taskerViolate",
		globalConstant.ISO_CODE_INDO: "id_taskerViolate",
		globalConstant.ISO_CODE_MY:   "my_taskerViolate",
	}
	COLLECTION_PAYMENT_2C2P_TRANSACTION = "payment2C2PTransaction"
	COLLECTION_TASKER_NOT_VIOLATE       = Collection{
		globalConstant.ISO_CODE_VN:   "taskerNotViolate",
		globalConstant.ISO_CODE_TH:   "th_taskerNotViolate",
		globalConstant.ISO_CODE_INDO: "id_taskerNotViolate",
		globalConstant.ISO_CODE_MY:   "my_taskerNotViolate",
	}
	COLLECTION_STORE_GROCERY_ASSISTANT = Collection{
		globalConstant.ISO_CODE_VN:   "storeGroceryAssistant",
		globalConstant.ISO_CODE_TH:   "storeGroceryAssistant",
		globalConstant.ISO_CODE_INDO: "id_storeGroceryAssistant",
	}
	COLLECTION_PRODUCT_GROCERY_ASSISTANT = Collection{
		globalConstant.ISO_CODE_VN:   "productGroceryAssistant",
		globalConstant.ISO_CODE_TH:   "productGroceryAssistant",
		globalConstant.ISO_CODE_INDO: "id_productGroceryAssistant",
	}
	COLLECTION_SHOPPING_CART = Collection{
		globalConstant.ISO_CODE_VN:   "shoppingCartGroceryAssistant",
		globalConstant.ISO_CODE_TH:   "shoppingCartGroceryAssistant",
		globalConstant.ISO_CODE_INDO: "id_shoppingCartGroceryAssistant",
	}
	COLLECTION_WALLET_TRUE_MONEY = "walletTrueMoney"
	COLLECTION_REFUND_REQUEST    = Collection{
		globalConstant.ISO_CODE_VN:   "refundRequest",
		globalConstant.ISO_CODE_TH:   "th_refundRequest",
		globalConstant.ISO_CODE_INDO: "id_refundRequest",
		globalConstant.ISO_CODE_MY:   "my_refundRequest",
	}
	COLLECTION_REFUND_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "refundTransaction",
		globalConstant.ISO_CODE_TH:   "th_refundTransaction",
		globalConstant.ISO_CODE_INDO: "id_refundTransaction",
		globalConstant.ISO_CODE_MY:   "my_refundTransaction",
	}
	////
	COLLECTION_PROMOTION_PAYMENT_METHOD = Collection{
		globalConstant.ISO_CODE_VN:   "vn_promotionPaymentMethod",
		globalConstant.ISO_CODE_TH:   "th_promotionPaymentMethod",
		globalConstant.ISO_CODE_INDO: "id_promotionPaymentMethod",
		globalConstant.ISO_CODE_MY:   "my_promotionPaymentMethod",
	}
	COLLECTION_WORLD_NEXT_EXPANSION = Collection{
		globalConstant.ISO_CODE_VN:   "world_NextExpansion",
		globalConstant.ISO_CODE_TH:   "world_NextExpansion",
		globalConstant.ISO_CODE_INDO: "world_NextExpansion",
		globalConstant.ISO_CODE_MY:   "myrld_NextExpansion",
	}
	////
	COLLECTION_SHOPEEPAY_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_shopeePayTransaction",
		globalConstant.ISO_CODE_TH:   "shopeePayTransaction",
		globalConstant.ISO_CODE_INDO: "",
		globalConstant.ISO_CODE_MY:   "",
	}
	////
	COLLECTION_GAME_CAMPAIGN = Collection{
		globalConstant.ISO_CODE_VN:   "vn_gameCampaign",
		globalConstant.ISO_CODE_TH:   "th_gameCampaign",
		globalConstant.ISO_CODE_INDO: "id_gameCampaign",
		globalConstant.ISO_CODE_MY:   "my_gameCampaign",
	}
	////
	COLLECTION_LUCKY_DRAW = Collection{
		globalConstant.ISO_CODE_VN:   "vn_luckyDraw",
		globalConstant.ISO_CODE_TH:   "th_luckyDraw",
		globalConstant.ISO_CODE_INDO: "id_luckyDraw",
		globalConstant.ISO_CODE_MY:   "my_luckyDraw",
	}
	////
	COLLECTION_LUCKY_DRAW_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_luckyDrawHistory",
		globalConstant.ISO_CODE_TH:   "th_luckyDrawHistory",
		globalConstant.ISO_CODE_INDO: "id_luckyDrawHistory",
		globalConstant.ISO_CODE_MY:   "my_luckyDrawHistory",
	}
	COLLECTION_TASKER_YEAR_END_REPORT = Collection{
		globalConstant.ISO_CODE_VN:   "taskerYearEndReport_",
		globalConstant.ISO_CODE_TH:   "th_taskerYearEndReport_",
		globalConstant.ISO_CODE_INDO: "id_taskerYearEndReport_",
		globalConstant.ISO_CODE_MY:   "my_taskerYearEndReport_",
	}
	COLLECTION_ASKER_YEAR_END_REPORT = Collection{
		globalConstant.ISO_CODE_VN:   "askerYearEndReport_",
		globalConstant.ISO_CODE_TH:   "th_askerYearEndReport_",
		globalConstant.ISO_CODE_INDO: "id_askerYearEndReport_",
		globalConstant.ISO_CODE_MY:   "my_askerYearEndReport_",
	}
	COLLECTION_OPERATION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_operation",
		globalConstant.ISO_CODE_TH:   "th_operation",
		globalConstant.ISO_CODE_INDO: "id_operation",
		globalConstant.ISO_CODE_MY:   "my_operation",
	}
	COLLECTION_TIKI_MINI_APP_TRANSACTION = "tikiMiniAppTransaction"
	////
	COLLECTION_SETTING_SYNC_CRON = Collection{
		globalConstant.ISO_CODE_VN:   "vn_settingSyncCron",
		globalConstant.ISO_CODE_TH:   "th_settingSyncCron",
		globalConstant.ISO_CODE_INDO: "id_settingSyncCron",
		globalConstant.ISO_CODE_MY:   "my_settingSyncCron",
	}
	////
	COLLECTION_QUIZ_FOR_TASKER_PREMIUM = Collection{
		globalConstant.ISO_CODE_VN:   "vn_quizForTaskerPremium",
		globalConstant.ISO_CODE_TH:   "th_quizForTaskerPremium",
		globalConstant.ISO_CODE_INDO: "id_quizForTaskerPremium",
		globalConstant.ISO_CODE_MY:   "my_quizForTaskerPremium",
	}
	////
	COLLECTION_QUIZ_FOR_TASKER_PREMIUM_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_quizForTaskerPremiumHistory",
		globalConstant.ISO_CODE_TH:   "th_quizForTaskerPremiumHistory",
		globalConstant.ISO_CODE_INDO: "id_quizForTaskerPremiumHistory",
		globalConstant.ISO_CODE_MY:   "my_quizForTaskerPremiumHistory",
	}
	COLLECTION_USER_COMPLAINT = Collection{
		globalConstant.ISO_CODE_VN:   "userComplaint",
		globalConstant.ISO_CODE_TH:   "th_userComplaint",
		globalConstant.ISO_CODE_INDO: "id_userComplaint",
		globalConstant.ISO_CODE_MY:   "my_userComplaint",
	}
	////
	COLLECTION_COMPENSATION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_compensation",
		globalConstant.ISO_CODE_TH:   "th_compensation",
		globalConstant.ISO_CODE_INDO: "id_compensation",
		globalConstant.ISO_CODE_MY:   "my_compensation",
	}
	COLLECTION_USERS_DELETED = Collection{
		globalConstant.ISO_CODE_VN:   "vn_users_deleted",
		globalConstant.ISO_CODE_TH:   "th_users_deleted",
		globalConstant.ISO_CODE_INDO: "id_users_deleted",
		globalConstant.ISO_CODE_MY:   "my_users_deleted",
	}
	COLLECTION_APP_USER_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_appUserHistory",
		globalConstant.ISO_CODE_TH:   "th_appUserHistory",
		globalConstant.ISO_CODE_INDO: "id_appUserHistory",
		globalConstant.ISO_CODE_MY:   "my_appUserHistory",
	}
	COLLECTION_TRAINING_TASKER = Collection{
		globalConstant.ISO_CODE_VN:   "trainingTasker",
		globalConstant.ISO_CODE_TH:   "th_trainingTasker",
		globalConstant.ISO_CODE_INDO: "id_trainingTasker",
		globalConstant.ISO_CODE_MY:   "my_trainingTasker",
	}
	COLLECTION_TRAINING_TASKER_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "trainingTaskerHistory",
		globalConstant.ISO_CODE_TH:   "th_trainingTaskerHistory",
		globalConstant.ISO_CODE_INDO: "id_trainingTaskerHistory",
		globalConstant.ISO_CODE_MY:   "my_trainingTaskerHistory",
	}
	COLLECTION_QUIZZES_TRAINING_TASKER = Collection{
		globalConstant.ISO_CODE_VN:   "quizzesTrainingTasker",
		globalConstant.ISO_CODE_TH:   "th_quizzesTrainingTasker",
		globalConstant.ISO_CODE_INDO: "id_quizzesTrainingTasker",
		globalConstant.ISO_CODE_MY:   "my_quizzesTrainingTasker",
	}
	COLLECTION_TASKER_PROFILE = Collection{
		globalConstant.ISO_CODE_VN:   "taskerProfile",
		globalConstant.ISO_CODE_TH:   "th_taskerProfile",
		globalConstant.ISO_CODE_INDO: "id_taskerProfile",
		globalConstant.ISO_CODE_MY:   "my_taskerProfile",
	}
	////
	COLLECTION_TASKER_WORKED_FOR_ASKERS = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerWorkedForAskers",
		globalConstant.ISO_CODE_TH:   "th_taskerWorkedForAskers",
		globalConstant.ISO_CODE_INDO: "id_taskerWorkedForAskers",
		globalConstant.ISO_CODE_MY:   "my_taskerWorkedForAskers",
	}
	COLLECTION_PAYOUT_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_payoutHistory",
		globalConstant.ISO_CODE_TH:   "th_payoutHistory",
		globalConstant.ISO_CODE_INDO: "id_payoutHistory",
		globalConstant.ISO_CODE_MY:   "my_payoutHistory",
	}
	COLLECTION_TASKER_WORKING_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerWorkingHistory",
		globalConstant.ISO_CODE_TH:   "th_taskerWorkingHistory",
		globalConstant.ISO_CODE_INDO: "id_taskerWorkingHistory",
		globalConstant.ISO_CODE_MY:   "my_taskerWorkingHistory",
	}
	COLLECTION_FINANCIAL_ACCOUNT_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_financialAccountHistory",
		globalConstant.ISO_CODE_TH:   "th_financialAccountHistory",
		globalConstant.ISO_CODE_INDO: "id_financialAccountHistory",
		globalConstant.ISO_CODE_MY:   "my_financialAccountHistory",
	}
	COLLECTION_COMBO_VOUCHER = Collection{
		globalConstant.ISO_CODE_VN:   "vn_comboVoucher",
		globalConstant.ISO_CODE_TH:   "th_comboVoucher",
		globalConstant.ISO_CODE_INDO: "id_comboVoucher",
		globalConstant.ISO_CODE_MY:   "my_comboVoucher",
	}
	COLLECTION_COMBO_VOUCHER_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_comboVoucherTransaction",
		globalConstant.ISO_CODE_TH:   "th_comboVoucherTransaction",
		globalConstant.ISO_CODE_INDO: "id_comboVoucherTransaction",
		globalConstant.ISO_CODE_MY:   "my_comboVoucherTransaction",
	}
	COLLECTION_USER_COMBO_VOUCHER = Collection{
		globalConstant.ISO_CODE_VN:   "vn_userComboVoucher",
		globalConstant.ISO_CODE_TH:   "th_userComboVoucher",
		globalConstant.ISO_CODE_INDO: "id_userComboVoucher",
	}
	COLLECTION_JOURNEY_SETTING = Collection{
		globalConstant.ISO_CODE_VN:   "vn_journeySetting",
		globalConstant.ISO_CODE_TH:   "th_journeySetting",
		globalConstant.ISO_CODE_INDO: "id_journeySetting",
		globalConstant.ISO_CODE_MY:   "my_journeySetting",
	}
	COLLECTION_JOURNEY_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_journeyHistory",
		globalConstant.ISO_CODE_TH:   "th_journeyHistory",
		globalConstant.ISO_CODE_INDO: "id_journeyHistory",
		globalConstant.ISO_CODE_MY:   "my_journeyHistory",
	}
	COLLECTION_JOURNEY_LEADER_BOARD = Collection{
		globalConstant.ISO_CODE_VN:   "vn_journeyLeaderBoard",
		globalConstant.ISO_CODE_TH:   "th_journeyLeaderBoard",
		globalConstant.ISO_CODE_INDO: "id_journeyLeaderBoard",
		globalConstant.ISO_CODE_MY:   "my_journeyLeaderBoard",
	}
	COLLECTION_TASKER_INCENTIVE = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerIncentive",
		globalConstant.ISO_CODE_TH:   "th_taskerIncentive",
		globalConstant.ISO_CODE_INDO: "id_taskerIncentive",
		globalConstant.ISO_CODE_MY:   "my_taskerIncentive",
	}
	COLLECTION_TASKER_GIFT = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerGift",
		globalConstant.ISO_CODE_TH:   "th_taskerGift",
		globalConstant.ISO_CODE_INDO: "id_taskerGift",
		globalConstant.ISO_CODE_MY:   "my_taskerGift",
	}
	COLLECTION_TASKER_REDEEM_GIFT_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerRedeemGiftTransaction",
		globalConstant.ISO_CODE_TH:   "th_taskerRedeemGiftTransaction",
		globalConstant.ISO_CODE_INDO: "id_taskerRedeemGiftTransaction",
		globalConstant.ISO_CODE_MY:   "my_taskerRedeemGiftTransaction",
	}
	COLLECTION_TASKER_POINT_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerPointTransaction",
		globalConstant.ISO_CODE_TH:   "th_taskerPointTransaction",
		globalConstant.ISO_CODE_INDO: "id_taskerPointTransaction",
		globalConstant.ISO_CODE_MY:   "my_taskerPointTransaction",
	}
	COLLECTION_VAT_REQUEST = Collection{
		globalConstant.ISO_CODE_VN:   "vn_vatRequest",
		globalConstant.ISO_CODE_TH:   "th_vatRequest",
		globalConstant.ISO_CODE_INDO: "id_vatRequest",
		globalConstant.ISO_CODE_MY:   "my_vatRequest",
	}
	COLLECTION_NEXT_EXPANSION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_NextExpansion",
		globalConstant.ISO_CODE_TH:   "th_NextExpansion",
		globalConstant.ISO_CODE_INDO: "id_NextExpansion",
		globalConstant.ISO_CODE_MY:   "my_NextExpansion",
	}
	COLLECTION_VN_PAY_TRANSACTION         = "vnPayTransaction"
	COLLECTION_ASKER_FLASH_SALE_INCENTIVE = Collection{
		globalConstant.ISO_CODE_VN:   "vn_askerFlashSaleIncentive",
		globalConstant.ISO_CODE_TH:   "th_askerFlashSaleIncentive",
		globalConstant.ISO_CODE_INDO: "id_askerFlashSaleIncentive",
		globalConstant.ISO_CODE_MY:   "my_askerFlashSaleIncentive",
	}
	COLLECTION_ASKER_REFERRAL_SETTING = Collection{
		globalConstant.ISO_CODE_VN:   "vn_askerReferralSetting",
		globalConstant.ISO_CODE_TH:   "th_askerReferralSetting",
		globalConstant.ISO_CODE_INDO: "id_askerReferralSetting",
		globalConstant.ISO_CODE_MY:   "my_askerReferralSetting",
	}
	////
	COLLECTION_SUPPORT_NOTIFICATION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_supportNotification",
		globalConstant.ISO_CODE_TH:   "th_supportNotification",
		globalConstant.ISO_CODE_INDO: "id_supportNotification",
		globalConstant.ISO_CODE_MY:   "my_supportNotification",
	}
	COLLECTION_TRAINING_JOURNEY_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_trainingJourneyHistory",
		globalConstant.ISO_CODE_TH:   "th_trainingJourneyHistory",
		globalConstant.ISO_CODE_INDO: "id_trainingJourneyHistory",
		globalConstant.ISO_CODE_MY:   "my_trainingJourneyHistory",
	}
	COLLECTION_TRAINING_JOURNEY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_trainingJourney",
		globalConstant.ISO_CODE_TH:   "th_trainingJourney",
		globalConstant.ISO_CODE_INDO: "id_trainingJourney",
		globalConstant.ISO_CODE_MY:   "my_trainingJourney",
	}
	COLLECTION_QUIZZES_TRAINING_JOURNEY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_quizzesTrainingJourney",
		globalConstant.ISO_CODE_TH:   "th_quizzesTrainingJourney",
		globalConstant.ISO_CODE_INDO: "id_quizzesTrainingJourney",
		globalConstant.ISO_CODE_MY:   "my_quizzesTrainingJourney",
	}
	COLLECTION_PARTNER_MINI_APP_SETTINGS = "partnerMiniAppSettings"
	COLLECTION_ADA_SMS_REPORT            = Collection{
		globalConstant.ISO_CODE_INDO: "id_adaSmsReport",
	}
	COLLECTION_PAYMENT_MIDTRANS_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "",
		globalConstant.ISO_CODE_TH:   "",
		globalConstant.ISO_CODE_INDO: "id_paymentMidtransTransaction",
		globalConstant.ISO_CODE_MY:   "",
	}
	COLLECTION_ASKER_REFERRAL_CAMPAIGN = Collection{
		globalConstant.ISO_CODE_VN:   "vn_askerReferralCampaign",
		globalConstant.ISO_CODE_TH:   "th_askerReferralCampaign",
		globalConstant.ISO_CODE_INDO: "id_askerReferralCampaign",
		globalConstant.ISO_CODE_MY:   "my_askerReferralCampaign",
	}
	COLLECTION_TASKER_MARKETING_CAMPAIGN = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerMarketingCampaign",
		globalConstant.ISO_CODE_TH:   "th_taskerMarketingCampaign",
		globalConstant.ISO_CODE_INDO: "id_taskerMarketingCampaign",
		globalConstant.ISO_CODE_MY:   "my_taskerMarketingCampaign",
	}
	COLLECTION_PAYMENT_XENDIT_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "",
		globalConstant.ISO_CODE_TH:   "",
		globalConstant.ISO_CODE_INDO: "id_paymentXenditTransaction",
		globalConstant.ISO_CODE_MY:   "",
	}
	COLLECTION_ASKER_FINANCIAL_ACCOUNT_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_askerFinancialAccountHistory",
		globalConstant.ISO_CODE_TH:   "th_askerFinancialAccountHistory",
		globalConstant.ISO_CODE_INDO: "id_askerFinancialAccountHistory",
		globalConstant.ISO_CODE_MY:   "my_askerFinancialAccountHistory",
	}
	COLLECTION_TASKER_WEEKLY_PAYOUT = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerWeeklyPayout",
		globalConstant.ISO_CODE_TH:   "th_taskerWeeklyPayout",
		globalConstant.ISO_CODE_INDO: "id_taskerWeeklyPayout",
		globalConstant.ISO_CODE_MY:   "my_taskerWeeklyPayout",
	}
	COLLECTION_TASKER_BNPL_PROCESS = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerBNPLProcess",
		globalConstant.ISO_CODE_TH:   "th_taskerBNPLProcess",
		globalConstant.ISO_CODE_INDO: "id_taskerBNPLProcess",
		globalConstant.ISO_CODE_MY:   "my_taskerBNPLProcess",
	}
	COLLECTION_TASKER_BNPL_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerBNPLTransaction",
		globalConstant.ISO_CODE_TH:   "th_taskerBNPLTransaction",
		globalConstant.ISO_CODE_INDO: "id_taskerBNPLTransaction",
		globalConstant.ISO_CODE_MY:   "my_taskerBNPLTransaction",
	}
	COLLECTION_PAYMENT_TOOLKIT_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_paymentToolKitTransaction",
		globalConstant.ISO_CODE_TH:   "th_paymentToolKitTransaction",
		globalConstant.ISO_CODE_INDO: "id_paymentToolKitTransaction",
		globalConstant.ISO_CODE_MY:   "my_paymentToolKitTransaction",
	}
	COLLECTION_USER_GAME_CAMPAIGN = Collection{
		globalConstant.ISO_CODE_VN:   "vn_userGameCampaign",
		globalConstant.ISO_CODE_TH:   "th_userGameCampaign",
		globalConstant.ISO_CODE_INDO: "id_userGameCampaign",
		globalConstant.ISO_CODE_MY:   "my_userGameCampaign",
	}
	COLLECTION_GAME_CAMPAIGN_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_gameCampaignHistory",
		globalConstant.ISO_CODE_TH:   "th_gameCampaignHistory",
		globalConstant.ISO_CODE_INDO: "id_gameCampaignHistory",
		globalConstant.ISO_CODE_MY:   "my_gameCampaignHistory",
	}
	COLLECTION_SERVICE_GROUP = Collection{
		globalConstant.ISO_CODE_VN:   "vn_serviceGroup",
		globalConstant.ISO_CODE_TH:   "th_serviceGroup",
		globalConstant.ISO_CODE_INDO: "id_serviceGroup",
		globalConstant.ISO_CODE_MY:   "my_serviceGroup",
	}
	COLLECTION_TASKER_GAME_CAMPAIGN = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerGameCampaign",
		globalConstant.ISO_CODE_TH:   "th_taskerGameCampaign",
		globalConstant.ISO_CODE_INDO: "id_taskerGameCampaign",
		globalConstant.ISO_CODE_MY:   "my_taskerGameCampaign",
	}
	COLLECTION_TASKER_USER_GAME_CAMPAIGN = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerUserGameCampaign",
		globalConstant.ISO_CODE_TH:   "th_taskerUserGameCampaign",
		globalConstant.ISO_CODE_INDO: "id_taskerUserGameCampaign",
		globalConstant.ISO_CODE_MY:   "my_taskerUserGameCampaign",
	}
	COLLECTION_TASKER_GAME_CAMPAIGN_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerGameCampaignHistory",
		globalConstant.ISO_CODE_TH:   "th_taskerGameCampaignHistory",
		globalConstant.ISO_CODE_INDO: "id_taskerGameCampaignHistory",
		globalConstant.ISO_CODE_MY:   "my_taskerGameCampaignHistory",
	}
	COLLECTION_THINGS_TO_KNOW = Collection{
		globalConstant.ISO_CODE_VN:   "vn_thingsToKnow",
		globalConstant.ISO_CODE_TH:   "th_thingsToKnow",
		globalConstant.ISO_CODE_INDO: "id_thingsToKnow",
		globalConstant.ISO_CODE_MY:   "my_thingsToKnow",
	}
	COLLECTION_PAYMENT_MB_TRANSACTION = "vn_paymentMBTransaction"
	COLLECTION_SURVEY_SETTING         = Collection{
		globalConstant.ISO_CODE_VN:   "vn_surveySetting",
		globalConstant.ISO_CODE_TH:   "th_surveySetting",
		globalConstant.ISO_CODE_INDO: "id_surveySetting",
		globalConstant.ISO_CODE_MY:   "my_surveySetting",
	}
	COLLECTION_SURVEY_REPORT = Collection{
		globalConstant.ISO_CODE_VN:   "vn_surveyReport",
		globalConstant.ISO_CODE_TH:   "th_surveyReport",
		globalConstant.ISO_CODE_INDO: "id_surveyReport",
		globalConstant.ISO_CODE_MY:   "my_surveyReport",
	}
	COLLECTION_USER_BALANCE_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_userBalanceHistory",
		globalConstant.ISO_CODE_TH:   "th_userBalanceHistory",
		globalConstant.ISO_CODE_INDO: "id_userBalanceHistory",
		globalConstant.ISO_CODE_MY:   "my_userBalanceHistory",
	}
	COLLECTION_TASKER_FLASH_SALE_INCENTIVE = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerFlashSaleIncentive",
		globalConstant.ISO_CODE_TH:   "th_taskerFlashSaleIncentive",
		globalConstant.ISO_CODE_INDO: "id_taskerFlashSaleIncentive",
		globalConstant.ISO_CODE_MY:   "my_taskerFlashSaleIncentive",
	}
	COLLECTION_ASKER_REPORT = Collection{
		globalConstant.ISO_CODE_VN:   "vn_askerReport",
		globalConstant.ISO_CODE_TH:   "th_askerReport",
		globalConstant.ISO_CODE_INDO: "id_askerReport",
		globalConstant.ISO_CODE_MY:   "my_askerReport",
	}
	COLLECTION_EMPLOYEE_PROFILE = Collection{
		globalConstant.ISO_CODE_VN:   "vn_employeeProfile",
		globalConstant.ISO_CODE_TH:   "th_employeeProfile",
		globalConstant.ISO_CODE_INDO: "id_employeeProfile",
		globalConstant.ISO_CODE_MY:   "my_employeeProfile",
	}
	COLLECTION_TASKER_TOOLKIT_LADING_DETAILS = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerToolkitLadingDetails",
		globalConstant.ISO_CODE_TH:   "th_taskerToolkitLadingDetails",
		globalConstant.ISO_CODE_INDO: "id_taskerToolkitLadingDetails",
		globalConstant.ISO_CODE_MY:   "my_taskerToolkitLadingDetails",
	}
	COLLECTION_VOTE_FAV_TASKER = Collection{
		globalConstant.ISO_CODE_VN:   "vn_voteFavTasker",
		globalConstant.ISO_CODE_TH:   "th_voteFavTasker",
		globalConstant.ISO_CODE_INDO: "id_voteFavTasker",
		globalConstant.ISO_CODE_MY:   "my_voteFavTasker",
	}
	COLLECTION_GROUP_ADDRESS = Collection{
		globalConstant.ISO_CODE_VN:   "vn_groupAddress",
		globalConstant.ISO_CODE_TH:   "th_groupAddress",
		globalConstant.ISO_CODE_INDO: "id_groupAddress",
		globalConstant.ISO_CODE_MY:   "my_groupAddress",
	}
	COLLECTION_CS_OUTBOUND_REPORT = Collection{
		globalConstant.ISO_CODE_VN:   "vn_csOutboundReport",
		globalConstant.ISO_CODE_TH:   "",
		globalConstant.ISO_CODE_INDO: "",
	}
	// New chat
	COLLECTION_CHAT_CONVERSATION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_chatConversation",
		globalConstant.ISO_CODE_TH:   "th_chatConversation",
		globalConstant.ISO_CODE_INDO: "id_chatConversation",
		globalConstant.ISO_CODE_MY:   "my_chatConversation",
	}
	COLLECTION_CHAT_MESSAGE = Collection{
		globalConstant.ISO_CODE_VN:   "vn_chatMessage",
		globalConstant.ISO_CODE_TH:   "th_chatMessage",
		globalConstant.ISO_CODE_INDO: "id_chatMessage",
		globalConstant.ISO_CODE_MY:   "my_chatMessage",
	}
	COLLECTION_HISTORY_CHAT_MESSAGES = Collection{
		globalConstant.ISO_CODE_VN:   "history_vn_chatMessages",
		globalConstant.ISO_CODE_TH:   "history_th_chatMessages",
		globalConstant.ISO_CODE_INDO: "history_id_chatMessages",
		globalConstant.ISO_CODE_MY:   "history_my_chatMessages",
	}
	COLLECTION_HISTORY_CHAT_CONVERSATIONS = Collection{
		globalConstant.ISO_CODE_VN:   "history_vn_chatConversations",
		globalConstant.ISO_CODE_TH:   "history_th_chatConversations",
		globalConstant.ISO_CODE_INDO: "history_id_chatConversations",
		globalConstant.ISO_CODE_MY:   "history_my_chatConversations",
	}
	COLLECTION_TOOL_KIT_SETTING = Collection{
		globalConstant.ISO_CODE_VN:   "vn_toolKitSetting",
		globalConstant.ISO_CODE_TH:   "th_toolKitSetting",
		globalConstant.ISO_CODE_INDO: "id_toolKitSetting",
		globalConstant.ISO_CODE_MY:   "my_toolKitSetting",
	}
	COLLECTION_TOOL_KIT_ITEMS = Collection{
		globalConstant.ISO_CODE_VN:   "vn_toolKitItems",
		globalConstant.ISO_CODE_TH:   "th_toolKitItems",
		globalConstant.ISO_CODE_INDO: "id_toolKitItems",
		globalConstant.ISO_CODE_MY:   "my_toolKitItems",
	}
	// New training tasker
	COLLECTION_TRAINING_TASKER_QUIZ = Collection{
		globalConstant.ISO_CODE_VN:   "vn_trainingTaskerQuiz",
		globalConstant.ISO_CODE_TH:   "th_trainingTaskerQuiz",
		globalConstant.ISO_CODE_INDO: "id_trainingTaskerQuiz",
		globalConstant.ISO_CODE_MY:   "my_trainingTaskerQuiz",
	}
	COLLECTION_TRAINING_TASKER_QUIZ_COLLECTION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_trainingTaskerQuizCollection",
		globalConstant.ISO_CODE_TH:   "th_trainingTaskerQuizCollection",
		globalConstant.ISO_CODE_INDO: "id_trainingTaskerQuizCollection",
		globalConstant.ISO_CODE_MY:   "my_trainingTaskerQuizCollection",
	}
	COLLECTION_TRAINING_TASKER_COURSE = Collection{
		globalConstant.ISO_CODE_VN:   "vn_trainingTaskerCourse",
		globalConstant.ISO_CODE_TH:   "th_trainingTaskerCourse",
		globalConstant.ISO_CODE_INDO: "id_trainingTaskerCourse",
		globalConstant.ISO_CODE_MY:   "my_trainingTaskerCourse",
	}
	COLLECTION_TRAINING_TASKER_SUBMISSION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_trainingTaskerSubmission",
		globalConstant.ISO_CODE_TH:   "th_trainingTaskerSubmission",
		globalConstant.ISO_CODE_INDO: "id_trainingTaskerSubmission",
		globalConstant.ISO_CODE_MY:   "my_trainingTaskerSubmission",
	}
	COLLECTION_FAIRY_TALE = Collection{
		globalConstant.ISO_CODE_VN:   "vn_fairyTale",
		globalConstant.ISO_CODE_TH:   "th_fairyTale",
		globalConstant.ISO_CODE_INDO: "id_fairyTale",
		globalConstant.ISO_CODE_MY:   "my_fairyTale",
	}
	COLLECTION_PAYMENT_METHOD_CAMPAIGN = Collection{
		globalConstant.ISO_CODE_VN:   "vn_paymentMethodCampaign",
		globalConstant.ISO_CODE_TH:   "th_paymentMethodCampaign",
		globalConstant.ISO_CODE_INDO: "id_paymentMethodCampaign",
		globalConstant.ISO_CODE_MY:   "my_paymentMethodCampaign",
	}
	//community
	COLLECTION_COMMUNITY_TAG = Collection{
		globalConstant.ISO_CODE_VN:   "vn_communityTag",
		globalConstant.ISO_CODE_TH:   "th_communityTag",
		globalConstant.ISO_CODE_INDO: "id_communityTag",
		globalConstant.ISO_CODE_MY:   "my_communityTag",
	}
	COLLECTION_COMMUNITY_TAG_ORDER = Collection{
		globalConstant.ISO_CODE_VN:   "vn_communityTagOrder",
		globalConstant.ISO_CODE_TH:   "th_communityTagOrder",
		globalConstant.ISO_CODE_INDO: "id_communityTagOrder",
		globalConstant.ISO_CODE_MY:   "my_communityTagOrder",
	}
	COLLECTION_COMMUNITY_USER = Collection{
		globalConstant.ISO_CODE_VN:   "vn_communityUser",
		globalConstant.ISO_CODE_TH:   "th_communityUser",
		globalConstant.ISO_CODE_INDO: "id_communityUser",
		globalConstant.ISO_CODE_MY:   "my_communityUser",
	}
	COLLECTION_COMMUNITY_POST = Collection{
		globalConstant.ISO_CODE_VN:   "vn_communityPost",
		globalConstant.ISO_CODE_TH:   "th_communityPost",
		globalConstant.ISO_CODE_INDO: "id_communityPost",
		globalConstant.ISO_CODE_MY:   "my_communityPost",
	}
	COLLECTION_COMMUNITY_COMMENT = Collection{
		globalConstant.ISO_CODE_VN:   "vn_communityComment",
		globalConstant.ISO_CODE_TH:   "th_communityComment",
		globalConstant.ISO_CODE_INDO: "id_communityComment",
		globalConstant.ISO_CODE_MY:   "my_communityComment",
	}
	COLLECTION_COMMUNITY_NOTIFICATION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_communityNotification",
		globalConstant.ISO_CODE_TH:   "th_communityNotification",
		globalConstant.ISO_CODE_INDO: "id_communityNotification",
		globalConstant.ISO_CODE_MY:   "my_communityNotification",
	}
	COLLECTION_COMMUNITY_MEDAL = Collection{
		globalConstant.ISO_CODE_VN:   "vn_communityMedal",
		globalConstant.ISO_CODE_TH:   "th_communityMedal",
		globalConstant.ISO_CODE_INDO: "id_communityMedal",
		globalConstant.ISO_CODE_MY:   "my_communityMedal",
	}
	COLLECTION_COMMUNITY_USER_REPORT = Collection{
		globalConstant.ISO_CODE_VN:   "vn_communityUserReport",
		globalConstant.ISO_CODE_TH:   "th_communityUserReport",
		globalConstant.ISO_CODE_INDO: "id_communityUserReport",
		globalConstant.ISO_CODE_MY:   "my_communityUserReport",
	}
	COLLECTION_JOURNEY_LEADER_BOARD_BACKUP = Collection{
		globalConstant.ISO_CODE_VN:   "vn_journeyLeaderBoardBackup",
		globalConstant.ISO_CODE_TH:   "th_journeyLeaderBoardBackup",
		globalConstant.ISO_CODE_INDO: "id_journeyLeaderBoardBackup",
		globalConstant.ISO_CODE_MY:   "my_journeyLeaderBoardBackup",
	}
	COLLECTION_JOURNEY_LEADER_BOARD_WEEKLY_REPORT = Collection{
		globalConstant.ISO_CODE_VN:   "vn_journeyLeaderBoardWeeklyReport",
		globalConstant.ISO_CODE_TH:   "th_journeyLeaderBoardWeeklyReport",
		globalConstant.ISO_CODE_INDO: "id_journeyLeaderBoardWeeklyReport",
		globalConstant.ISO_CODE_MY:   "my_journeyLeaderBoardWeeklyReport",
	}
	COLLECTION_PAYMENT_KREDIVO_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_kredivoTransaction",
		globalConstant.ISO_CODE_TH:   "",
		globalConstant.ISO_CODE_INDO: "",
	}
	COLLECTION_TASKER_SPECIAL_CAMPAIGN = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerSpecialCampaign",
		globalConstant.ISO_CODE_TH:   "th_taskerSpecialCampaign",
		globalConstant.ISO_CODE_INDO: "id_taskerSpecialCampaign",
		globalConstant.ISO_CODE_MY:   "my_taskerSpecialCampaign",
	}
	COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerSpecialCampaignTransaction",
		globalConstant.ISO_CODE_TH:   "th_taskerSpecialCampaignTransaction",
		globalConstant.ISO_CODE_INDO: "id_taskerSpecialCampaignTransaction",
		globalConstant.ISO_CODE_MY:   "my_taskerSpecialCampaignTransaction",
	}
	COLLECTION_BUSINESS = Collection{
		globalConstant.ISO_CODE_VN:   "vn_business",
		globalConstant.ISO_CODE_TH:   "th_business",
		globalConstant.ISO_CODE_INDO: "id_business",
		globalConstant.ISO_CODE_MY:   "my_business",
	}
	COLLECTION_BUSINESS_LEVEL = Collection{
		globalConstant.ISO_CODE_VN:   "vn_businessLevel",
		globalConstant.ISO_CODE_TH:   "th_businessLevel",
		globalConstant.ISO_CODE_INDO: "id_businessLevel",
		globalConstant.ISO_CODE_MY:   "my_businessLevel",
	}
	COLLECTION_BUSINESS_MEMBER = Collection{
		globalConstant.ISO_CODE_VN:   "vn_businessMember",
		globalConstant.ISO_CODE_TH:   "th_businessMember",
		globalConstant.ISO_CODE_INDO: "id_businessMember",
		globalConstant.ISO_CODE_MY:   "my_businessMember",
	}
	COLLECTION_BUSINESS_MEMBER_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_businessMemberTransaction",
		globalConstant.ISO_CODE_TH:   "th_businessMemberTransaction",
		globalConstant.ISO_CODE_INDO: "id_businessMemberTransaction",
		globalConstant.ISO_CODE_MY:   "my_businessMemberTransaction",
	}
	COLLECTION_BUSINESS_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_businessTransaction",
		globalConstant.ISO_CODE_TH:   "th_businessTransaction",
		globalConstant.ISO_CODE_INDO: "id_businessTransaction",
		globalConstant.ISO_CODE_MY:   "my_businessTransaction",
	}
	COLLECTION_VN_CS_OUTBOUND_REPORT = Collection{
		globalConstant.ISO_CODE_VN:   "vn_csOutboundReport",
		globalConstant.ISO_CODE_TH:   "",
		globalConstant.ISO_CODE_INDO: "",
	}
	COLLECTION_KEYWORDS      = "keywords"
	COLLECTION_TASK_METADATA = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskMetadata",
		globalConstant.ISO_CODE_TH:   "th_taskMetadata",
		globalConstant.ISO_CODE_INDO: "id_taskMetadata",
		globalConstant.ISO_CODE_MY:   "my_taskMetadata",
	}
	COLLECTION_ASKER_MONTHLY_REPORT = Collection{
		globalConstant.ISO_CODE_VN:   "vn_askerMonthlyReport",
		globalConstant.ISO_CODE_TH:   "th_askerMonthlyReport",
		globalConstant.ISO_CODE_INDO: "id_askerMonthlyReport",
		globalConstant.ISO_CODE_MY:   "my_askerMonthlyReport",
	}
	COLLECTION_TASKER_MONTHLY_REPORT = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerMonthlyReport",
		globalConstant.ISO_CODE_TH:   "th_taskerMonthlyReport",
		globalConstant.ISO_CODE_INDO: "id_taskerMonthlyReport",
		globalConstant.ISO_CODE_MY:   "my_taskerMonthlyReport",
	}
	COLLECTION_COMMUNITY_SETTING = Collection{
		globalConstant.ISO_CODE_VN:   "vn_communitySetting",
		globalConstant.ISO_CODE_TH:   "th_communitySetting",
		globalConstant.ISO_CODE_INDO: "id_communitySetting",
		globalConstant.ISO_CODE_MY:   "my_communitySetting",
	}
	COLLECTION_TRAINING_TASKER_COURSE_START_DATE = Collection{
		globalConstant.ISO_CODE_VN:   "vn_trainingTaskerCourseStartDate",
		globalConstant.ISO_CODE_TH:   "th_trainingTaskerCourseStartDate",
		globalConstant.ISO_CODE_INDO: "id_trainingTaskerCourseStartDate",
		globalConstant.ISO_CODE_MY:   "my_trainingTaskerCourseStartDate",
	}
	COLLECTION_EVENT_CONFIG = Collection{
		globalConstant.ISO_CODE_VN:   "vn_eventConfig",
		globalConstant.ISO_CODE_TH:   "th_eventConfig",
		globalConstant.ISO_CODE_INDO: "id_eventConfig",
		globalConstant.ISO_CODE_MY:   "my_eventConfig",
	}
	COLLECTION_ACCOUNTING_JOURNAL_ENTRY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_accountingJournalEntry",
		globalConstant.ISO_CODE_TH:   "th_accountingJournalEntry",
		globalConstant.ISO_CODE_INDO: "id_accountingJournalEntry",
		globalConstant.ISO_CODE_MY:   "my_accountingJournalEntry",
	}
	COLLECTION_TASKER_ACTION_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_taskerActionHistory",
		globalConstant.ISO_CODE_TH:   "th_taskerActionHistory",
		globalConstant.ISO_CODE_INDO: "id_taskerActionHistory",
		globalConstant.ISO_CODE_MY:   "my_taskerActionHistory",
	}
	COLLECTION_DANA_TRANSACTION = Collection{
		globalConstant.ISO_CODE_INDO: "id_danaTransaction",
	}
	COLLECTION_SERVICE_PACKAGE = Collection{
		globalConstant.ISO_CODE_VN:   "vn_servicePackage",
		globalConstant.ISO_CODE_TH:   "",
		globalConstant.ISO_CODE_INDO: "",
		globalConstant.ISO_CODE_MY:   "",
	}
	COLLECTION_BUNDLE_VOUCHER = Collection{
		globalConstant.ISO_CODE_VN:   "vn_bundleVoucher",
		globalConstant.ISO_CODE_TH:   "th_bundleVoucher",
		globalConstant.ISO_CODE_INDO: "id_bundleVoucher",
	}
	COLLECTION_REDEEM_BUNDLE_VOUCHER_TRANSACTION = Collection{
		globalConstant.ISO_CODE_VN:   "vn_redeemBundleVoucherTransaction",
		globalConstant.ISO_CODE_TH:   "th_redeemBundleVoucherTransaction",
		globalConstant.ISO_CODE_INDO: "id_redeemBundleVoucherTransaction",
	}
	COLLECTION_REDEEM_BUNDLE_VOUCHER_HISTORY = Collection{
		globalConstant.ISO_CODE_VN:   "vn_redeemBundleVoucherHistory",
		globalConstant.ISO_CODE_TH:   "th_redeemBundleVoucherHistory",
		globalConstant.ISO_CODE_INDO: "id_redeemBundleVoucherHistory",
	}
)
