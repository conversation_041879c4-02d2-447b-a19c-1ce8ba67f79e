package autoMoveChatToHistory

import (
	"fmt"
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.mongodb.org/mongo-driver/bson"
)

// moveChatConversationToHistory retrieves chat data for each task from the task channel and returns a channel of chat.
func processChatConversations(w *sync.WaitGroup, chatCh chan string) chan string {
	// Create a channel to store the output data
	outDataCh := make(chan string, LIMIT)
	var listChatConversation []string
	go func() {
		defer w.Done()
		defer close(outDataCh)
		var chatIds []string
		// get channel chat
		for chat := range chatCh {
			chatIds = append(chatIds, chat)
			listChatConversation = append(listChatConversation, chat)
			if len(chatIds) >= int(LIMIT) {
				err := moveChatConversationsToHistory(chatIds)
				if err != nil {
					chatIds = []string{}
				}
				// Send chats to the output channel
				for _, c := range chatIds {
					outDataCh <- c
				}
				chatIds = []string{}
			}
		}
		if len(chatIds) > 0 {
			err := moveChatConversationsToHistory(chatIds)
			if err != nil {
				chatIds = []string{}
			}
			// Send chats to the output channel
			for _, c := range chatIds {
				outDataCh <- c
			}
		}
	}()
	return outDataCh
}

// moveChatConversationsToHistory retrieves chat data for each task from the task channel and returns a channel of chat.
func moveChatConversationsToHistory(chatIds []string) error {
	// match condition
	matchCondition := bson.M{
		"$match": bson.M{
			"_id": bson.M{"$in": chatIds},
		},
	}
	// move data to history
	mergeCondition := bson.M{
		"$merge": bson.M{
			"into":           globalCollection.COLLECTION_HISTORY_CHAT_CONVERSATIONS[local.ISO_CODE],
			"on":             "_id",
			"whenMatched":    "merge",
			"whenNotMatched": "insert",
		},
	}
	// run aggregation
	var results []interface{}
	err := globalDataAccess.Aggregate(globalCollection.COLLECTION_CHAT_CONVERSATION[local.ISO_CODE], []bson.M{matchCondition, mergeCondition}, &results)
	if err != nil {
		// log error
		globalLib.PostToSlack(cfg.SlackToken, lib.GO_SERVICE_CHANNEL, "bTaskee System", fmt.Sprintf("AutoMoveChatToHistory-%s: Move chatConversation - error: %v - chatIds: %v", local.ISO_CODE, err, chatIds))
	}
	return err
}
