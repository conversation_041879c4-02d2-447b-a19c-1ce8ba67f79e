package testing

import (
	"context"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
)

func TestSendBusinessReport(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		Convey("Given a request to validate", t, func() {
			req := &modelEmailSending.EmailSendBusinessReportRequest{}
			Convey("Check the response if businessId empty", func() {
				s := service.Server{}
				res, _ := s.SendBusinessReportEmail(context.Background(), req)

				So(res.Message, ShouldEqual, lib.ERROR_BUSINESS_ID_REQUIRED)
			})
		})
	})
	t.Run("2", func(t *testing.T) {
		ResetData()
		Convey("Given a request to validate send receipt email", t, func() {
			req := &modelEmailSending.EmailSendBusinessReportRequest{
				BusinessId: "business-01",
			}
			Convey("Check the response if business not found", func() {
				s := service.Server{}
				res, _ := s.SendBusinessReportEmail(context.Background(), req)

				So(res.Message, ShouldEqual, lib.ERROR_BUSINESS_NOT_FOUND)
			})
		})
	})
	t.Run("3", func(t *testing.T) {
		ResetData()
		CreateBusiness([]map[string]interface{}{
			{
				"_id":    "0834567890",
				"name":   "bTaskee",
				"status": globalConstant.BUSINESS_STATUS_ACTIVE,
				"bPay":   13_240_000.0,
			},
		})
		Convey("Given a request to validate send receipt email", t, func() {
			req := &modelEmailSending.EmailSendBusinessReportRequest{
				BusinessId: "0834567890",
			}
			Convey("Check the response if user not found", func() {
				s := service.Server{}
				res, _ := s.SendBusinessReportEmail(context.Background(), req)

				So(res.Message, ShouldEqual, lib.ERROR_USER_NOT_FOUND)
			})
		})
	})
	// t.Run("4", func(t *testing.T) {
	// 	ResetData()
	// 	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	// 	date := time.Date(currentTime.Year(), currentTime.Month(), 1, 0, 0, 0, 0, local.TimeZone)
	// 	// Create User
	// 	CreateUser([]map[string]interface{}{
	// 		{
	// 			"phone": "0834567890",
	// 			"name":  "Van",
	// 			"type":  globalConstant.USER_TYPE_ASKER,
	// 		}, {
	// 			"phone":  "0834567891",
	// 			"name":   "Asker 01",
	// 			"type":   globalConstant.USER_TYPE_ASKER,
	// 			"avatar": "avatar-01",
	// 			"emails": []map[string]interface{}{
	// 				{
	// 					"address": "email-01",
	// 				},
	// 			},
	// 		}, {
	// 			"phone":  "0834567892",
	// 			"name":   "Asker 02",
	// 			"type":   globalConstant.USER_TYPE_ASKER,
	// 			"avatar": "avatar-02",
	// 		}, {
	// 			"phone":  "0834567893",
	// 			"name":   "Asker 03",
	// 			"type":   globalConstant.USER_TYPE_ASKER,
	// 			"avatar": "avatar-03",
	// 		}, {
	// 			"phone":  "0834567894",
	// 			"name":   "Asker 04",
	// 			"type":   globalConstant.USER_TYPE_ASKER,
	// 			"avatar": "avatar-04",
	// 		}, {
	// 			"phone":  "0834567895",
	// 			"name":   "Asker 05",
	// 			"type":   globalConstant.USER_TYPE_ASKER,
	// 			"avatar": "avatar-05",
	// 		}, {
	// 			"phone":  "0834567896",
	// 			"name":   "Asker 06",
	// 			"type":   globalConstant.USER_TYPE_ASKER,
	// 			"avatar": "avatar-06",
	// 		}, {
	// 			"phone":  "0834567897",
	// 			"name":   "Asker 07",
	// 			"type":   globalConstant.USER_TYPE_ASKER,
	// 			"avatar": "avatar-07",
	// 		}, {
	// 			"phone":  "0834567898",
	// 			"name":   "Asker 08",
	// 			"type":   globalConstant.USER_TYPE_ASKER,
	// 			"avatar": "avatar-08",
	// 		},
	// 	})
	// 	CreateBusiness([]map[string]interface{}{
	// 		{
	// 			"_id":    "0834567890",
	// 			"name":   "bTaskee",
	// 			"status": globalConstant.BUSINESS_STATUS_ACTIVE,
	// 			"bPay":   13_240_000.0,
	//          "email":  "<EMAIL>",
	// 		},
	// 	})
	// 	CreateBusinessLevel([]map[string]interface{}{
	// 		{
	// 			"_id":        "levelBronzeId",
	// 			"name":       "Bronze",
	// 			"businessId": "0834567890",
	// 			"amount":     300_000,
	// 			"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
	// 		},
	// 		{
	// 			"_id":        "levelSilverId",
	// 			"name":       "Silver",
	// 			"businessId": "0834567890",
	// 			"amount":     600_000,
	// 			"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
	// 		},
	// 		{
	// 			"_id":        "levelGoldId",
	// 			"name":       "Gold",
	// 			"businessId": "0834567890",
	// 			"amount":     1_200_000,
	// 			"status":     globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE,
	// 		},
	// 	})
	// 	CreateBusinessMember([]map[string]interface{}{
	// 		{
	// 			"_id":        "businessMember_1",
	// 			"businessId": "0834567890",
	// 			"userId":     "0834567891",
	// 			"levelId":    "levelBronzeId",
	// 			"bPay":       300_000,
	// 			"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	// 			"createdAt":  date,
	// 		}, {
	// 			"_id":        "businessMember_3",
	// 			"businessId": "0834567890",
	// 			"userId":     "0834567893",
	// 			"levelId":    "levelBronzeId",
	// 			"bPay":       300_000,
	// 			"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	// 			"createdAt":  date,
	// 		}, {
	// 			"_id":        "businessMember_2",
	// 			"businessId": "0834567890",
	// 			"userId":     "0834567892",
	// 			"levelId":    "levelBronzeId",
	// 			"bPay":       300_000,
	// 			"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	// 			"createdAt":  date,
	// 		}, {
	// 			"_id":        "businessMember_4",
	// 			"businessId": "0834567890",
	// 			"userId":     "0834567894",
	// 			"levelId":    "levelSilverId",
	// 			"bPay":       600_000,
	// 			"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	// 			"createdAt":  date.AddDate(0, 0, 2),
	// 		}, {
	// 			"_id":        "businessMember_6",
	// 			"businessId": "0834567890",
	// 			"userId":     "0834567896",
	// 			"levelId":    "levelGoldId",
	// 			"bPay":       1_200_000,
	// 			"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	// 			"createdAt":  date.AddDate(0, 0, 3),
	// 		}, {
	// 			"_id":        "businessMember_5",
	// 			"businessId": "0834567890",
	// 			"userId":     "0834567895",
	// 			"levelId":    "levelSilverId",
	// 			"bPay":       600_000,
	// 			"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	// 			"createdAt":  date.AddDate(0, 0, 2),
	// 		}, {
	// 			"_id":        "businessMember_7",
	// 			"businessId": "0834567890",
	// 			"userId":     "0834567897",
	// 			"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	// 			"createdAt":  date.AddDate(0, 0, 2),
	// 		}, {
	// 			"_id":        "businessMember_8",
	// 			"businessId": "0834567890",
	// 			"userId":     "0834567898",
	// 			"status":     globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE,
	// 			"createdAt":  date.AddDate(0, 0, 2),
	// 		},
	// 	})

	// 	CreateBusinessTransactions([]map[string]interface{}{
	// 		{
	// 			"_id":        "transaction_1",
	// 			"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT,
	// 			"amount":     300_000,
	// 			"name":       globalConstant.BUSINESS_TRANSACTION_NAME_REVOKE_BPAY_MEMBER,
	// 			"memberId":   "businessMember_1",
	// 			"businessId": "0834567890",
	// 			"createdAt":  date.AddDate(0, 0, 1),
	// 		}, {
	// 			"_id":        "transaction_2",
	// 			"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT,
	// 			"amount":     12_000_000,
	// 			"name":       globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_BY_BTASKEE,
	// 			"businessId": "0834567890",
	// 			"createdAt":  date,
	// 		}, {
	// 			"_id":        "transaction_3",
	// 			"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT,
	// 			"amount":     300_000,
	// 			"name":       globalConstant.BUSINESS_TRANSACTION_NAME_REVOKE_BPAY_MEMBER,
	// 			"memberId":   "businessMember_2",
	// 			"businessId": "0834567890",
	// 			"createdAt":  date.AddDate(0, 0, 1),
	// 		}, {
	// 			"_id":        "transaction_4",
	// 			"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT,
	// 			"amount":     300_000,
	// 			"name":       globalConstant.BUSINESS_TRANSACTION_NAME_REVOKE_BPAY_MEMBER,
	// 			"memberId":   "businessMember_3",
	// 			"businessId": "0834567890",
	// 			"createdAt":  date.AddDate(0, 0, 1),
	// 		}, {
	// 			"_id":        "transaction_5",
	// 			"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT,
	// 			"amount":     300_000,
	// 			"name":       globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_MEMBER,
	// 			"memberId":   "businessMember_3",
	// 			"businessId": "0834567890",
	// 			"createdAt":  date.AddDate(0, 0, 2),
	// 			"levelInfo": map[string]interface{}{
	// 				"levelId":   "levelBronzeId",
	// 				"levelName": "Bronze",
	// 			},
	// 		}, {
	// 			"_id":        "transaction_6",
	// 			"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT,
	// 			"amount":     300_000,
	// 			"name":       globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_MEMBER,
	// 			"memberId":   "businessMember_2",
	// 			"businessId": "0834567890",
	// 			"createdAt":  date.AddDate(0, 0, 1),
	// 			"levelInfo": map[string]interface{}{
	// 				"levelId":   "levelBronzeId",
	// 				"levelName": "Bronze",
	// 			},
	// 		}, {
	// 			"_id":        "transaction_7",
	// 			"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT,
	// 			"amount":     600_000,
	// 			"name":       globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_MEMBER,
	// 			"memberId":   "businessMember_5",
	// 			"businessId": "0834567890",
	// 			"createdAt":  date.AddDate(0, 0, 4),
	// 			"levelInfo": map[string]interface{}{
	// 				"levelId":   "levelSilverId",
	// 				"levelName": "Silver",
	// 			},
	// 		}, {
	// 			"_id":        "transaction_8",
	// 			"type":       globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT,
	// 			"amount":     1_200_000,
	// 			"name":       globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_MEMBER,
	// 			"memberId":   "businessMember_6",
	// 			"businessId": "0834567890",
	// 			"createdAt":  date.AddDate(0, 0, 10),
	// 			"levelInfo": map[string]interface{}{
	// 				"levelId":   "levelGoldId",
	// 				"levelName": "Gold",
	// 			},
	// 		},
	// 	})

	// 	Convey("Given a request to check send receipt email", t, func() {
	// 		req := &modelEmailSending.EmailSendBusinessReportRequest{
	// 			BusinessId: "0834567890",
	// 			Month:      int32(currentTime.Month()),
	// 			Year:       int32(currentTime.Year()),
	// 		}
	// 		Convey("Check the response", func() {
	// 			s := service.Server{}
	// 			_, err := s.SendBusinessReportEmail(context.Background(), req)
	// 			So(err, ShouldBeNil)
	// 		})
	// 	})
	// })
}
