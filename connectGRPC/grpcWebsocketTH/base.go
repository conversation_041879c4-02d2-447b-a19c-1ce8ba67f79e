package grpcWebsocketTH

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcWebsocketTH"
	"google.golang.org/grpc"
)

func ConnectGRPCWebsocketTH(endpoint string) (grpcWebsocketTH.WebsocketTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcWebsocketTH.NewWebsocketTHClient(connect)
	return client, connect, err
}
