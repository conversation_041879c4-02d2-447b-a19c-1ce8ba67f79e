package cancelTaskForceTasker

import (
	"context"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcCancelTaskVN"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/cancelBookingRequest"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"google.golang.org/protobuf/types/known/wrapperspb"
)

func callGrpcToCancelTask(tasks []*modelTask.Task) error {
	for _, task := range tasks {
		request := &cancelBookingRequest.CancelBookingRequest{
			TaskId: task.XId,
			UserId: task.AskerId,
			Reason: globalConstant.CANCELLATION_REASON_NO_TASKER_ACCEPT,
			Backend: &cancelBookingRequest.CancelBookingRequestBackend{
				From:              globalConstant.CHANGES_HISTORY_FROM_SYNC_CRON,
				IsNotChargeTasker: true,
				IsNotChargeAsker:  true,
			},
			ChangeTaskToPosted: &wrapperspb.BoolValue{Value: false},
		}
		client, connect, err := grpcCancelTaskVN.ConnectGRPCCancelTaskVN(cfg.GRPC_Cancel_Task_VN_URL)
		if err != nil {
			return err
		}
		defer connect.Close()
		_, err = client.BackendCancelTask(context.Background(), request)
		if err != nil {
			return err
		}
	}
	return nil
}
