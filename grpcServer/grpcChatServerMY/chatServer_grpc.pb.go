// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcChatServerMY

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	websocketMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// ChatServerClient is the client API for ChatServer service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ChatServerClient interface {
	SendSocketChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error)
}

type chatServerClient struct {
	cc grpc.ClientConnInterface
}

func NewChatServerClient(cc grpc.ClientConnInterface) ChatServerClient {
	return &chatServerClient{cc}
}

func (c *chatServerClient) SendSocketChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcChatServerMY.ChatServer/SendSocketChatMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChatServerServer is the server API for ChatServer service.
// All implementations must embed UnimplementedChatServerServer
// for forward compatibility
type ChatServerServer interface {
	SendSocketChatMessage(context.Context, *websocketMessage.WebsocketMessage) (*empty.Empty, error)
	mustEmbedUnimplementedChatServerServer()
}

// UnimplementedChatServerServer must be embedded to have forward compatible implementations.
type UnimplementedChatServerServer struct {
}

func (UnimplementedChatServerServer) SendSocketChatMessage(context.Context, *websocketMessage.WebsocketMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSocketChatMessage not implemented")
}
func (UnimplementedChatServerServer) mustEmbedUnimplementedChatServerServer() {}

// UnsafeChatServerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChatServerServer will
// result in compilation errors.
type UnsafeChatServerServer interface {
	mustEmbedUnimplementedChatServerServer()
}

func RegisterChatServerServer(s *grpc.Server, srv ChatServerServer) {
	s.RegisterService(&_ChatServer_serviceDesc, srv)
}

func _ChatServer_SendSocketChatMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(websocketMessage.WebsocketMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatServerServer).SendSocketChatMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcChatServerMY.ChatServer/SendSocketChatMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatServerServer).SendSocketChatMessage(ctx, req.(*websocketMessage.WebsocketMessage))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChatServer_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcChatServerMY.ChatServer",
	HandlerType: (*ChatServerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendSocketChatMessage",
			Handler:    _ChatServer_SendSocketChatMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "chatServer.proto",
}
