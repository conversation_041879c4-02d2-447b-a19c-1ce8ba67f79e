// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.7.1
// source: csOutboundVN.proto

package grpcCSOutboundVN

import (
	empty "github.com/golang/protobuf/ptypes/empty"
	csOutboundRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/csOutboundRequest"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_csOutboundVN_proto protoreflect.FileDescriptor

var file_csOutboundVN_proto_rawDesc = []byte{
	0x0a, 0x12, 0x63, 0x73, 0x4f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x56, 0x4e, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x67, 0x72, 0x70, 0x63, 0x43, 0x53, 0x4f, 0x75, 0x74, 0x62,
	0x6f, 0x75, 0x6e, 0x64, 0x56, 0x4e, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x5b, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70,
	0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x63, 0x73, 0x4f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e,
	0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2f, 0x63, 0x73, 0x4f, 0x75, 0x74, 0x62, 0x6f,
	0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0xeb, 0x02, 0x0a, 0x0c, 0x43, 0x53, 0x4f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x56,
	0x4e, 0x12, 0x56, 0x0a, 0x14, 0x53, 0x65, 0x6e, 0x64, 0x5a, 0x4e, 0x53, 0x46, 0x69, 0x72, 0x73,
	0x74, 0x44, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x24, 0x2e, 0x63, 0x73, 0x4f, 0x75,
	0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x53,
	0x4f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x4c, 0x0a, 0x0a, 0x53, 0x65, 0x6e,
	0x64, 0x5a, 0x4e, 0x53, 0x4f, 0x54, 0x50, 0x12, 0x24, 0x2e, 0x63, 0x73, 0x4f, 0x75, 0x74, 0x62,
	0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x43, 0x53, 0x4f, 0x75,
	0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x55, 0x0a, 0x13, 0x53, 0x65, 0x6e, 0x64, 0x5a,
	0x4e, 0x53, 0x4f, 0x54, 0x50, 0x41, 0x70, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x12, 0x24,
	0x2e, 0x63, 0x73, 0x4f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x43, 0x53, 0x4f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x5e,
	0x0a, 0x1c, 0x53, 0x65, 0x6e, 0x64, 0x5a, 0x4e, 0x53, 0x52, 0x65, 0x63, 0x65, 0x69, 0x76, 0x65,
	0x64, 0x39, 0x54, 0x48, 0x59, 0x65, 0x61, 0x72, 0x54, 0x69, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x24,
	0x2e, 0x63, 0x73, 0x4f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x43, 0x53, 0x4f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x42, 0x45,
	0x5a, 0x43, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61,
	0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x43, 0x53, 0x4f, 0x75, 0x74, 0x62, 0x6f,
	0x75, 0x6e, 0x64, 0x56, 0x4e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_csOutboundVN_proto_goTypes = []interface{}{
	(*csOutboundRequest.CSOutboundRequest)(nil), // 0: csOutboundRequest.CSOutboundRequest
	(*empty.Empty)(nil),                         // 1: google.protobuf.Empty
}
var file_csOutboundVN_proto_depIdxs = []int32{
	0, // 0: grpcCSOutboundVN.CSOutboundVN.SendZNSFirstDoneTask:input_type -> csOutboundRequest.CSOutboundRequest
	0, // 1: grpcCSOutboundVN.CSOutboundVN.SendZNSOTP:input_type -> csOutboundRequest.CSOutboundRequest
	0, // 2: grpcCSOutboundVN.CSOutboundVN.SendZNSOTPAppTasker:input_type -> csOutboundRequest.CSOutboundRequest
	0, // 3: grpcCSOutboundVN.CSOutboundVN.SendZNSReceived9THYearTicket:input_type -> csOutboundRequest.CSOutboundRequest
	1, // 4: grpcCSOutboundVN.CSOutboundVN.SendZNSFirstDoneTask:output_type -> google.protobuf.Empty
	1, // 5: grpcCSOutboundVN.CSOutboundVN.SendZNSOTP:output_type -> google.protobuf.Empty
	1, // 6: grpcCSOutboundVN.CSOutboundVN.SendZNSOTPAppTasker:output_type -> google.protobuf.Empty
	1, // 7: grpcCSOutboundVN.CSOutboundVN.SendZNSReceived9THYearTicket:output_type -> google.protobuf.Empty
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_csOutboundVN_proto_init() }
func file_csOutboundVN_proto_init() {
	if File_csOutboundVN_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_csOutboundVN_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_csOutboundVN_proto_goTypes,
		DependencyIndexes: file_csOutboundVN_proto_depIdxs,
	}.Build()
	File_csOutboundVN_proto = out.File
	file_csOutboundVN_proto_rawDesc = nil
	file_csOutboundVN_proto_goTypes = nil
	file_csOutboundVN_proto_depIdxs = nil
}
