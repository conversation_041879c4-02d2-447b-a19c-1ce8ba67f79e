// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.21.9
// source: email-indo.proto

package grpcEmailIndo

import (
	emailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	emailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	task "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	users "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_email_indo_proto protoreflect.FileDescriptor

var file_email_indo_proto_rawDesc = []byte{
	0x0a, 0x10, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0d, 0x67, 0x72, 0x70, 0x63, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x64,
	0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x54, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73,
	0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x52, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67,
	0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2d, 0x73, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x41, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f,
	0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d,
	0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x74, 0x61, 0x73,
	0x6b, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x43, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65,
	0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0xc8, 0x04, 0x0a, 0x09, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x64, 0x6f, 0x12,
	0x66, 0x0a, 0x09, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x65, 0x73, 0x74, 0x12, 0x1a, 0x2e, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x1a, 0x1c, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x1f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x19, 0x12, 0x17,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2d, 0x69, 0x6e,
	0x64, 0x6f, 0x2f, 0x74, 0x65, 0x73, 0x74, 0x12, 0x47, 0x0a, 0x09, 0x53, 0x65, 0x6e, 0x64, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x1a, 0x1c, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x63, 0x0a, 0x10, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x0a, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b,
	0x1a, 0x1c, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x25,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x22, 0x1a, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x72, 0x65, 0x63, 0x65, 0x69,
	0x70, 0x74, 0x3a, 0x01, 0x2a, 0x12, 0x59, 0x0a, 0x12, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x61, 0x6e,
	0x63, 0x65, 0x6c, 0x46, 0x65, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x23, 0x2e, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x46, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1c, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x88, 0x01, 0x0a, 0x1a, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12,
	0x1a, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x2a, 0x22, 0x25, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x2d, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x3a, 0x01, 0x2a, 0x12, 0x3f, 0x0a, 0x0f, 0x53,
	0x65, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x69, 0x66, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x0c,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x1a, 0x1c, 0x2e, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x2d, 0x5a, 0x2b,
	0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b,
	0x65, 0x65, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x67, 0x72,
	0x70, 0x63, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x49, 0x6e, 0x64, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
}

var file_email_indo_proto_goTypes = []interface{}{
	(*emailSending.EmailSending)(nil),          // 0: emailSending.EmailSending
	(*task.Task)(nil),                          // 1: task.Task
	(*emailSending.EmailCancelFeeRequest)(nil), // 2: emailSending.EmailCancelFeeRequest
	(*emailSending.EmailRequest)(nil),          // 3: emailSending.EmailRequest
	(*users.Users)(nil),                        // 4: users.Users
	(*emailResponse.EmailResponse)(nil),        // 5: emailResponse.EmailResponse
}
var file_email_indo_proto_depIdxs = []int32{
	0, // 0: grpcEmailIndo.EmailIndo.EmailTest:input_type -> emailSending.EmailSending
	0, // 1: grpcEmailIndo.EmailIndo.SendEmail:input_type -> emailSending.EmailSending
	1, // 2: grpcEmailIndo.EmailIndo.SendReceiptEmail:input_type -> task.Task
	2, // 3: grpcEmailIndo.EmailIndo.SendCancelFeeEmail:input_type -> emailSending.EmailCancelFeeRequest
	3, // 4: grpcEmailIndo.EmailIndo.SendRenewSubscriptionEmail:input_type -> emailSending.EmailRequest
	4, // 5: grpcEmailIndo.EmailIndo.SendVerifyEmail:input_type -> users.Users
	5, // 6: grpcEmailIndo.EmailIndo.EmailTest:output_type -> emailResponse.EmailResponse
	5, // 7: grpcEmailIndo.EmailIndo.SendEmail:output_type -> emailResponse.EmailResponse
	5, // 8: grpcEmailIndo.EmailIndo.SendReceiptEmail:output_type -> emailResponse.EmailResponse
	5, // 9: grpcEmailIndo.EmailIndo.SendCancelFeeEmail:output_type -> emailResponse.EmailResponse
	5, // 10: grpcEmailIndo.EmailIndo.SendRenewSubscriptionEmail:output_type -> emailResponse.EmailResponse
	5, // 11: grpcEmailIndo.EmailIndo.SendVerifyEmail:output_type -> emailResponse.EmailResponse
	6, // [6:12] is the sub-list for method output_type
	0, // [0:6] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_email_indo_proto_init() }
func file_email_indo_proto_init() {
	if File_email_indo_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_email_indo_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_email_indo_proto_goTypes,
		DependencyIndexes: file_email_indo_proto_depIdxs,
	}.Build()
	File_email_indo_proto = out.File
	file_email_indo_proto_rawDesc = nil
	file_email_indo_proto_goTypes = nil
	file_email_indo_proto_depIdxs = nil
}
