package globalRepo

import (
	"context"

	"gitlab.com/btaskee/go-services-model-v2/dbConfig"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

func UpsertOneByIdWithContext(ctx context.Context, collectionName string, id string, data interface{}) (*mongo.UpdateResult, error) {
	opts := options.Update().SetUpsert(true)
	collection := dbConfig.GetCollection(collectionName)
	return collection.UpdateOne(ctx, bson.M{"_id": id}, data, opts)
}

func UpsertOneById(collectionName string, id string, data interface{}) (*mongo.UpdateResult, error) {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	opts := options.Update().SetUpsert(true)
	collection := dbConfig.GetCollection(collectionName)
	return collection.UpdateOne(ctx, bson.M{"_id": id}, data, opts)
}

func UpsertOneByQuery(collectionName string, query interface{}, data interface{}) (*mongo.UpdateResult, error) {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	opts := options.Update().SetUpsert(true)
	collection := dbConfig.GetCollection(collectionName)
	return collection.UpdateOne(ctx, query, data, opts)
}
