package payment

import (
	"errors"
	"time"

	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/payment/indo"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/payment/th"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/payment/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalModel/modelTransactionInfo"
)

func GetPrepayTransactionInfoByTask_Indo(taskId string, taskPaymentTransactionId string, paymentMethod string, paymentGateway string) (result *modelTransactionInfo.TransactionInfo, err error) {
	// validate
	if taskId == "" {
		err = errors.New("taskId is empty")
		return
	}
	if taskPaymentTransactionId == "" {
		err = errors.New("taskPaymentTransactionId is empty")
		return
	}
	if paymentMethod == "" {
		err = errors.New("paymentMethod is empty")
		return
	}

	return indo.GetPrepayTransactionInfoByTask_Indo(taskId, taskPaymentTransactionId, paymentMethod, paymentGateway)
}

// This function is used to create refund request for prepay task only
func RefundPrepayTask_Indo(taskId, transactionId, paymentMethod, paymentGateway string, cancelFee float64, reason string, timeZone *time.Location) (refundRequestId string, refundAmount float64, isBPayRefund bool, err error) {
	if taskId == "" {
		err = errors.New("taskId is empty")
		return
	}
	if paymentMethod == "" {
		err = errors.New("paymentMethod is required")
		return
	}

	// 1. get transaction info need to use in
	transactionInfo, funcErr := GetPrepayTransactionInfoByTask_Indo(taskId, transactionId, paymentMethod, paymentGateway)
	if funcErr != nil {
		err = funcErr
		return
	}

	// 2. Create refund requests
	return indo.RefundPrepayTaskIndo(transactionInfo, cancelFee, reason, timeZone)
}

func GetCardTransactionInfoByTask_VN(taskId string) (result *modelTransactionInfo.TransactionInfo, err error) {
	// validate
	if taskId == "" {
		err = errors.New("taskId is empty")
		return
	}
	return vn.GetCardTransactionInfoByTask_VN(taskId)
}

// This function is used to create refund request for card task only
func RefundCardTask_VN(taskId string, cancelFee float64, reason string, timeZone *time.Location) (refundRequestId string, refundAmount float64, isBPayRefund bool, err error) {
	if taskId == "" {
		err = errors.New("taskId is empty")
		return
	}

	// 1. get transaction info need to use in
	transactionInfo, funcErr := GetCardTransactionInfoByTask_VN(taskId)
	if funcErr != nil {
		err = funcErr
		return
	}

	// 2. Create refund requests
	return vn.CreateRefundRequestTaskCard_VN(transactionInfo, cancelFee, reason, timeZone)
}

func GetCardTransactionInfoByTask_TH(taskId string) (result *modelTransactionInfo.TransactionInfo, err error) {
	// validate
	if taskId == "" {
		err = errors.New("taskId is empty")
		return
	}
	return th.GetCardTransactionInfoByTask_TH(taskId)
}

// This function is used to create refund request for card task only
func RefundCardTask_TH(taskId string, cancelFee float64, reason string, timeZone *time.Location) (refundRequestId string, refundAmount float64, isBPayRefund bool, err error) {
	if taskId == "" {
		err = errors.New("taskId is empty")
		return
	}

	// 1. get transaction info need to use in
	transactionInfo, funcErr := GetCardTransactionInfoByTask_TH(taskId)
	if funcErr != nil {
		err = funcErr
		return
	}

	// 2. Create refund requests
	return th.CreateRefundRequestTaskCard_TH(transactionInfo, cancelFee, reason, timeZone)
}

func RefundTaskbPayBusiness(isoCode, taskId, name, reason, slackToken string) (refundAmount float64, err error) {
	// validate
	if taskId == "" {
		err = errors.New("taskId is empty")
		return
	}
	switch isoCode {
	case globalConstant.ISO_CODE_VN:
		transaction, err := vn.GetbPayBusinessMemberTransaction(isoCode, taskId)
		if err != nil {
			return 0, err
		}
		transaction.Name = name
		transaction.Reason = reason
		return vn.RefundbPayBusinessMember(transaction, slackToken)
	}
	return
}
