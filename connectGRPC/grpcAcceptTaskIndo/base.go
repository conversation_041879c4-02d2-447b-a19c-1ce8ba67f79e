package grpcAcceptTaskIndo

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcAcceptTaskIndo"
	"google.golang.org/grpc"
)

func ConnectGRPCAcceptTaskIndo(endpoint string) (grpcAcceptTaskIndo.AcceptTaskIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcAcceptTaskIndo.NewAcceptTaskIndoClient(connect)
	return client, connect, err
}
