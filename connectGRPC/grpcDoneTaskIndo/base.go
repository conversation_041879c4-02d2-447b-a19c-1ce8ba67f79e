package grpcDoneTaskIndo

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcDoneTaskIndo"
	"google.golang.org/grpc"
)

func ConnectGRPCDoneTaskIndo(endpoint string) (grpcDoneTaskIndo.DoneTaskIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcDoneTaskIndo.NewDoneTaskIndoClient(connect)
	return client, connect, err
}
