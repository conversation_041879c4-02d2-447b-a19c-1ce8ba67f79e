package grpcPushNotification

import (
	"gitlab.com/btaskee/go-push-notification-service-v2/grpcPushNotification"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"google.golang.org/grpc"
)

func ConnectGRPCPushNotification(endpoint string) (grpcPushNotification.PushNotificationClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPushNotification.NewPushNotificationClient(connect)
	return client, connect, err
}
