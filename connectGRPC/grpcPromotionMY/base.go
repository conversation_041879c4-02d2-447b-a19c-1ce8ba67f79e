package grpcPromotionMY

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPromotionMY"
	"google.golang.org/grpc"
)

func ConnectGRPCPromotion(endpoint string) (grpcPromotionMY.PromotionClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPromotionMY.NewPromotionClient(connect)
	return client, connect, err
}
