syntax = "proto3";
package grpcSMSTH;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcSMSTH";

import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/smsSending/smsSending.proto";

service SMSTH {
  rpc Send (smsSending.SmsRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/api/v3/sms-th/send"
      body: "*"
    };
  }
  rpc SendSMSViaVMG (smsSending.SmsRequest) returns (google.protobuf.Empty) {}
}