package grpcDataTransactionTH

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcDataTransactionTH"
	"google.golang.org/grpc"
)

func ConnectGRPCDataTransaction(endpoint string) (grpcDataTransactionTH.DataTransactionClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcDataTransactionTH.NewDataTransactionClient(connect)
	return client, connect, err
}
