// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcCancelTaskINDO

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	cancelBookingRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/cancelBookingRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// CancelTaskINDOClient is the client API for CancelTaskINDO service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type CancelTaskINDOClient interface {
	BackendCancelTask(ctx context.Context, in *cancelBookingRequest.CancelBookingRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type cancelTaskINDOClient struct {
	cc grpc.ClientConnInterface
}

func NewCancelTaskINDOClient(cc grpc.ClientConnInterface) CancelTaskINDOClient {
	return &cancelTaskINDOClient{cc}
}

func (c *cancelTaskINDOClient) BackendCancelTask(ctx context.Context, in *cancelBookingRequest.CancelBookingRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcCancelTaskINDO.CancelTaskINDO/BackendCancelTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// CancelTaskINDOServer is the server API for CancelTaskINDO service.
// All implementations must embed UnimplementedCancelTaskINDOServer
// for forward compatibility
type CancelTaskINDOServer interface {
	BackendCancelTask(context.Context, *cancelBookingRequest.CancelBookingRequest) (*empty.Empty, error)
	mustEmbedUnimplementedCancelTaskINDOServer()
}

// UnimplementedCancelTaskINDOServer must be embedded to have forward compatible implementations.
type UnimplementedCancelTaskINDOServer struct {
}

func (UnimplementedCancelTaskINDOServer) BackendCancelTask(context.Context, *cancelBookingRequest.CancelBookingRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BackendCancelTask not implemented")
}
func (UnimplementedCancelTaskINDOServer) mustEmbedUnimplementedCancelTaskINDOServer() {}

// UnsafeCancelTaskINDOServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to CancelTaskINDOServer will
// result in compilation errors.
type UnsafeCancelTaskINDOServer interface {
	mustEmbedUnimplementedCancelTaskINDOServer()
}

func RegisterCancelTaskINDOServer(s *grpc.Server, srv CancelTaskINDOServer) {
	s.RegisterService(&_CancelTaskINDO_serviceDesc, srv)
}

func _CancelTaskINDO_BackendCancelTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(cancelBookingRequest.CancelBookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(CancelTaskINDOServer).BackendCancelTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcCancelTaskINDO.CancelTaskINDO/BackendCancelTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(CancelTaskINDOServer).BackendCancelTask(ctx, req.(*cancelBookingRequest.CancelBookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _CancelTaskINDO_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcCancelTaskINDO.CancelTaskINDO",
	HandlerType: (*CancelTaskINDOServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BackendCancelTask",
			Handler:    _CancelTaskINDO_BackendCancelTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "cancel-task-indo.proto",
}
