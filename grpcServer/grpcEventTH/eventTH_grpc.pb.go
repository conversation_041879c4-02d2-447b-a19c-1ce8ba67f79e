// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcEventTH

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	eventMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/eventMessage"
	users "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// EventTHClient is the client API for EventTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EventTHClient interface {
	TrackingCleverTapPayComboSuccess(ctx context.Context, in *eventMessage.TrackingCleverTapPayComboMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapPayComboPaymentError(ctx context.Context, in *eventMessage.TrackingCleverTapPayComboMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerAcceptTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerCancelTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerWithdrawTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerDoneTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerRating(ctx context.Context, in *eventMessage.TrackingCleverTapRatingMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerFinishTraining(ctx context.Context, in *eventMessage.TrackingCleverTapTrainingMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapPrepayTaskPaymentResponse(ctx context.Context, in *eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	AddAskerDoneBookingReferralGift(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*empty.Empty, error)
	AddAskerSignUpReferralGift(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionLogin(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionDoneTask(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskExpired(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionReferral(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionDoneTaskReferral(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionRollCall(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*eventMessage.EventRollCallMessage, error)
	PublishUserGameCampaignActionDoneTaskFirst(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionNewSignUp(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
}

type eventTHClient struct {
	cc grpc.ClientConnInterface
}

func NewEventTHClient(cc grpc.ClientConnInterface) EventTHClient {
	return &eventTHClient{cc}
}

func (c *eventTHClient) TrackingCleverTapPayComboSuccess(ctx context.Context, in *eventMessage.TrackingCleverTapPayComboMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/TrackingCleverTapPayComboSuccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) TrackingCleverTapPayComboPaymentError(ctx context.Context, in *eventMessage.TrackingCleverTapPayComboMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/TrackingCleverTapPayComboPaymentError", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) TrackingCleverTapTaskerAcceptTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/TrackingCleverTapTaskerAcceptTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) TrackingCleverTapTaskerCancelTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/TrackingCleverTapTaskerCancelTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) TrackingCleverTapTaskerWithdrawTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/TrackingCleverTapTaskerWithdrawTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) TrackingCleverTapTaskerDoneTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/TrackingCleverTapTaskerDoneTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) TrackingCleverTapTaskerRating(ctx context.Context, in *eventMessage.TrackingCleverTapRatingMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/TrackingCleverTapTaskerRating", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) TrackingCleverTapTaskerFinishTraining(ctx context.Context, in *eventMessage.TrackingCleverTapTrainingMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/TrackingCleverTapTaskerFinishTraining", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) TrackingCleverTapPrepayTaskPaymentResponse(ctx context.Context, in *eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/TrackingCleverTapPrepayTaskPaymentResponse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) AddAskerDoneBookingReferralGift(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/AddAskerDoneBookingReferralGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) AddAskerSignUpReferralGift(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/AddAskerSignUpReferralGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) PublishUserGameCampaignActionLogin(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/PublishUserGameCampaignActionLogin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) PublishUserGameCampaignActionDoneTask(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/PublishUserGameCampaignActionDoneTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) TrackingCleverTapTaskExpired(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/TrackingCleverTapTaskExpired", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) PublishUserGameCampaignActionReferral(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/PublishUserGameCampaignActionReferral", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) PublishUserGameCampaignActionDoneTaskReferral(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/PublishUserGameCampaignActionDoneTaskReferral", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) PublishUserGameCampaignActionRollCall(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*eventMessage.EventRollCallMessage, error) {
	out := new(eventMessage.EventRollCallMessage)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/PublishUserGameCampaignActionRollCall", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) PublishUserGameCampaignActionDoneTaskFirst(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/PublishUserGameCampaignActionDoneTaskFirst", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventTHClient) PublishUserGameCampaignActionNewSignUp(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventTH.EventTH/PublishUserGameCampaignActionNewSignUp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EventTHServer is the server API for EventTH service.
// All implementations must embed UnimplementedEventTHServer
// for forward compatibility
type EventTHServer interface {
	TrackingCleverTapPayComboSuccess(context.Context, *eventMessage.TrackingCleverTapPayComboMessage) (*empty.Empty, error)
	TrackingCleverTapPayComboPaymentError(context.Context, *eventMessage.TrackingCleverTapPayComboMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerAcceptTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerCancelTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerWithdrawTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerDoneTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerRating(context.Context, *eventMessage.TrackingCleverTapRatingMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerFinishTraining(context.Context, *eventMessage.TrackingCleverTapTrainingMessage) (*empty.Empty, error)
	TrackingCleverTapPrepayTaskPaymentResponse(context.Context, *eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage) (*empty.Empty, error)
	AddAskerDoneBookingReferralGift(context.Context, *users.Users) (*empty.Empty, error)
	AddAskerSignUpReferralGift(context.Context, *users.Users) (*empty.Empty, error)
	PublishUserGameCampaignActionLogin(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionDoneTask(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	TrackingCleverTapTaskExpired(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionReferral(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionDoneTaskReferral(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionRollCall(context.Context, *eventMessage.EventCommonMessage) (*eventMessage.EventRollCallMessage, error)
	PublishUserGameCampaignActionDoneTaskFirst(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionNewSignUp(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	mustEmbedUnimplementedEventTHServer()
}

// UnimplementedEventTHServer must be embedded to have forward compatible implementations.
type UnimplementedEventTHServer struct {
}

func (UnimplementedEventTHServer) TrackingCleverTapPayComboSuccess(context.Context, *eventMessage.TrackingCleverTapPayComboMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapPayComboSuccess not implemented")
}
func (UnimplementedEventTHServer) TrackingCleverTapPayComboPaymentError(context.Context, *eventMessage.TrackingCleverTapPayComboMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapPayComboPaymentError not implemented")
}
func (UnimplementedEventTHServer) TrackingCleverTapTaskerAcceptTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerAcceptTask not implemented")
}
func (UnimplementedEventTHServer) TrackingCleverTapTaskerCancelTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerCancelTask not implemented")
}
func (UnimplementedEventTHServer) TrackingCleverTapTaskerWithdrawTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerWithdrawTask not implemented")
}
func (UnimplementedEventTHServer) TrackingCleverTapTaskerDoneTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerDoneTask not implemented")
}
func (UnimplementedEventTHServer) TrackingCleverTapTaskerRating(context.Context, *eventMessage.TrackingCleverTapRatingMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerRating not implemented")
}
func (UnimplementedEventTHServer) TrackingCleverTapTaskerFinishTraining(context.Context, *eventMessage.TrackingCleverTapTrainingMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerFinishTraining not implemented")
}
func (UnimplementedEventTHServer) TrackingCleverTapPrepayTaskPaymentResponse(context.Context, *eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapPrepayTaskPaymentResponse not implemented")
}
func (UnimplementedEventTHServer) AddAskerDoneBookingReferralGift(context.Context, *users.Users) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAskerDoneBookingReferralGift not implemented")
}
func (UnimplementedEventTHServer) AddAskerSignUpReferralGift(context.Context, *users.Users) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAskerSignUpReferralGift not implemented")
}
func (UnimplementedEventTHServer) PublishUserGameCampaignActionLogin(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionLogin not implemented")
}
func (UnimplementedEventTHServer) PublishUserGameCampaignActionDoneTask(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionDoneTask not implemented")
}
func (UnimplementedEventTHServer) TrackingCleverTapTaskExpired(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskExpired not implemented")
}
func (UnimplementedEventTHServer) PublishUserGameCampaignActionReferral(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionReferral not implemented")
}
func (UnimplementedEventTHServer) PublishUserGameCampaignActionDoneTaskReferral(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionDoneTaskReferral not implemented")
}
func (UnimplementedEventTHServer) PublishUserGameCampaignActionRollCall(context.Context, *eventMessage.EventCommonMessage) (*eventMessage.EventRollCallMessage, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionRollCall not implemented")
}
func (UnimplementedEventTHServer) PublishUserGameCampaignActionDoneTaskFirst(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionDoneTaskFirst not implemented")
}
func (UnimplementedEventTHServer) PublishUserGameCampaignActionNewSignUp(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionNewSignUp not implemented")
}
func (UnimplementedEventTHServer) mustEmbedUnimplementedEventTHServer() {}

// UnsafeEventTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EventTHServer will
// result in compilation errors.
type UnsafeEventTHServer interface {
	mustEmbedUnimplementedEventTHServer()
}

func RegisterEventTHServer(s *grpc.Server, srv EventTHServer) {
	s.RegisterService(&_EventTH_serviceDesc, srv)
}

func _EventTH_TrackingCleverTapPayComboSuccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapPayComboMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).TrackingCleverTapPayComboSuccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/TrackingCleverTapPayComboSuccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).TrackingCleverTapPayComboSuccess(ctx, req.(*eventMessage.TrackingCleverTapPayComboMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_TrackingCleverTapPayComboPaymentError_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapPayComboMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).TrackingCleverTapPayComboPaymentError(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/TrackingCleverTapPayComboPaymentError",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).TrackingCleverTapPayComboPaymentError(ctx, req.(*eventMessage.TrackingCleverTapPayComboMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_TrackingCleverTapTaskerAcceptTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).TrackingCleverTapTaskerAcceptTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/TrackingCleverTapTaskerAcceptTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).TrackingCleverTapTaskerAcceptTask(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_TrackingCleverTapTaskerCancelTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).TrackingCleverTapTaskerCancelTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/TrackingCleverTapTaskerCancelTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).TrackingCleverTapTaskerCancelTask(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_TrackingCleverTapTaskerWithdrawTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).TrackingCleverTapTaskerWithdrawTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/TrackingCleverTapTaskerWithdrawTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).TrackingCleverTapTaskerWithdrawTask(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_TrackingCleverTapTaskerDoneTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).TrackingCleverTapTaskerDoneTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/TrackingCleverTapTaskerDoneTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).TrackingCleverTapTaskerDoneTask(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_TrackingCleverTapTaskerRating_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapRatingMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).TrackingCleverTapTaskerRating(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/TrackingCleverTapTaskerRating",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).TrackingCleverTapTaskerRating(ctx, req.(*eventMessage.TrackingCleverTapRatingMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_TrackingCleverTapTaskerFinishTraining_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTrainingMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).TrackingCleverTapTaskerFinishTraining(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/TrackingCleverTapTaskerFinishTraining",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).TrackingCleverTapTaskerFinishTraining(ctx, req.(*eventMessage.TrackingCleverTapTrainingMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_TrackingCleverTapPrepayTaskPaymentResponse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).TrackingCleverTapPrepayTaskPaymentResponse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/TrackingCleverTapPrepayTaskPaymentResponse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).TrackingCleverTapPrepayTaskPaymentResponse(ctx, req.(*eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_AddAskerDoneBookingReferralGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(users.Users)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).AddAskerDoneBookingReferralGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/AddAskerDoneBookingReferralGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).AddAskerDoneBookingReferralGift(ctx, req.(*users.Users))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_AddAskerSignUpReferralGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(users.Users)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).AddAskerSignUpReferralGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/AddAskerSignUpReferralGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).AddAskerSignUpReferralGift(ctx, req.(*users.Users))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_PublishUserGameCampaignActionLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).PublishUserGameCampaignActionLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/PublishUserGameCampaignActionLogin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).PublishUserGameCampaignActionLogin(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_PublishUserGameCampaignActionDoneTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).PublishUserGameCampaignActionDoneTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/PublishUserGameCampaignActionDoneTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).PublishUserGameCampaignActionDoneTask(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_TrackingCleverTapTaskExpired_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).TrackingCleverTapTaskExpired(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/TrackingCleverTapTaskExpired",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).TrackingCleverTapTaskExpired(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_PublishUserGameCampaignActionReferral_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).PublishUserGameCampaignActionReferral(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/PublishUserGameCampaignActionReferral",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).PublishUserGameCampaignActionReferral(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_PublishUserGameCampaignActionDoneTaskReferral_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).PublishUserGameCampaignActionDoneTaskReferral(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/PublishUserGameCampaignActionDoneTaskReferral",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).PublishUserGameCampaignActionDoneTaskReferral(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_PublishUserGameCampaignActionRollCall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).PublishUserGameCampaignActionRollCall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/PublishUserGameCampaignActionRollCall",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).PublishUserGameCampaignActionRollCall(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_PublishUserGameCampaignActionDoneTaskFirst_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).PublishUserGameCampaignActionDoneTaskFirst(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/PublishUserGameCampaignActionDoneTaskFirst",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).PublishUserGameCampaignActionDoneTaskFirst(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventTH_PublishUserGameCampaignActionNewSignUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventTHServer).PublishUserGameCampaignActionNewSignUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventTH.EventTH/PublishUserGameCampaignActionNewSignUp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventTHServer).PublishUserGameCampaignActionNewSignUp(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

var _EventTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcEventTH.EventTH",
	HandlerType: (*EventTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TrackingCleverTapPayComboSuccess",
			Handler:    _EventTH_TrackingCleverTapPayComboSuccess_Handler,
		},
		{
			MethodName: "TrackingCleverTapPayComboPaymentError",
			Handler:    _EventTH_TrackingCleverTapPayComboPaymentError_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerAcceptTask",
			Handler:    _EventTH_TrackingCleverTapTaskerAcceptTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerCancelTask",
			Handler:    _EventTH_TrackingCleverTapTaskerCancelTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerWithdrawTask",
			Handler:    _EventTH_TrackingCleverTapTaskerWithdrawTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerDoneTask",
			Handler:    _EventTH_TrackingCleverTapTaskerDoneTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerRating",
			Handler:    _EventTH_TrackingCleverTapTaskerRating_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerFinishTraining",
			Handler:    _EventTH_TrackingCleverTapTaskerFinishTraining_Handler,
		},
		{
			MethodName: "TrackingCleverTapPrepayTaskPaymentResponse",
			Handler:    _EventTH_TrackingCleverTapPrepayTaskPaymentResponse_Handler,
		},
		{
			MethodName: "AddAskerDoneBookingReferralGift",
			Handler:    _EventTH_AddAskerDoneBookingReferralGift_Handler,
		},
		{
			MethodName: "AddAskerSignUpReferralGift",
			Handler:    _EventTH_AddAskerSignUpReferralGift_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionLogin",
			Handler:    _EventTH_PublishUserGameCampaignActionLogin_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionDoneTask",
			Handler:    _EventTH_PublishUserGameCampaignActionDoneTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskExpired",
			Handler:    _EventTH_TrackingCleverTapTaskExpired_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionReferral",
			Handler:    _EventTH_PublishUserGameCampaignActionReferral_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionDoneTaskReferral",
			Handler:    _EventTH_PublishUserGameCampaignActionDoneTaskReferral_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionRollCall",
			Handler:    _EventTH_PublishUserGameCampaignActionRollCall_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionDoneTaskFirst",
			Handler:    _EventTH_PublishUserGameCampaignActionDoneTaskFirst_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionNewSignUp",
			Handler:    _EventTH_PublishUserGameCampaignActionNewSignUp_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "eventTH.proto",
}
