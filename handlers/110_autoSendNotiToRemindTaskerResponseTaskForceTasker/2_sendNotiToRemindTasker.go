package autoSendNotiToRemindTaskerResponseTaskForceTasker

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func sendNotiToRemindTasker(tasks []*modelTask.Task) {
	for _, task := range tasks {
		// 1. Get tasker
		tasker, err := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": task.ForceTasker.TaskerId}, bson.M{"name": 1, "language": 1})
		if err != nil {
			msg := fmt.Sprintf("[go-sync-cron-vn-v3] Can not get user: userId %s. Error: %s", task.ForceTasker.TaskerId, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
			return
		}
		// Send notification to received user
		taskerLanguage := globalConstant.LANG_EN
		if tasker.Language != "" {
			taskerLanguage = tasker.Language
		}
		var arrayNotification []interface{}
		notify := &modelNotification.Notification{
			XId:         globalLib.GenerateObjectId(),
			TaskId:      task.XId,
			UserId:      tasker.XId,
			Type:        28,
			Description: localization.T(taskerLanguage, "NOTI_REMIND_TASKER_RESPONSE_TASK_FORCE_TITLE"),
			NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
			CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
		}
		arrayNotification = append(arrayNotification, notify)
		title := localization.GetLocalizeObject("NOTI_REMIND_TASKER_RESPONSE_TASK_FORCE_TITLE")
		body := localization.GetLocalizeObject("NOTI_REMIND_TASKER_RESPONSE_TASK_FORCE_BODY")
		payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
			Type:       28,
			TaskId:     task.XId,
			NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
		}
		userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{{UserId: tasker.XId, Language: taskerLanguage}}
		lib.SendNotification(arrayNotification, userIds, title, body, payload, true)
	}
}
