package giveRewardTaskerDoneSpecialCampaign

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getTaskerInfo(taskerId string) (user *modelUser.Users) {
	user, _ = modelUser.GetOneById(local.ISO_CODE, taskerId, bson.M{"fAccountId": 1, "phone": 1, "_id": 1, "language": 1})
	return
}
