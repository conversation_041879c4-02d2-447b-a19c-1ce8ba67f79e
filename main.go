package main

import (
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

var cfg = config.GetConfig()

func main() {
	// Create new server
	httpServer := service.NewHTTPServer(cfg.APIPort)

	// Start HTTP Server
	go func() {
		err := service.StartHTTPServer(httpServer)
		if err != nil {
			log.Println("Error Sync Cron VN:", err.Error())
		}
	}()

	// Graceful shutdown
	handleSigterm(httpServer)
}

func handleSigterm(srv *http.Server) {
	serviceName := "Sync Cron VN v3"
	globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[SERVICE RUNNING] - %s", serviceName))

	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)
	signal.Notify(c, syscall.SIGTERM)
	<-c

	// Shutdown HTTP server
	service.StopHTTPServer(srv)

	globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[SERVICE DOWN] - %s", serviceName))

	close(c)
}
