// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.7.1
// source: BNPLMY.proto

package grpcBNPLMY

import (
	empty "github.com/golang/protobuf/ptypes/empty"
	taskerBNPLRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerBNPLRequest"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_BNPLMY_proto protoreflect.FileDescriptor

var file_BNPLMY_proto_rawDesc = []byte{
	0x0a, 0x0c, 0x42, 0x4e, 0x50, 0x4c, 0x4d, 0x59, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a,
	0x67, 0x72, 0x70, 0x63, 0x42, 0x4e, 0x50, 0x4c, 0x4d, 0x59, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x5b, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32,
	0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x65,
	0x72, 0x42, 0x4e, 0x50, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2f, 0x74, 0x61, 0x73,
	0x6b, 0x65, 0x72, 0x42, 0x4e, 0x50, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0x5d, 0x0a, 0x04, 0x42, 0x4e, 0x50, 0x4c, 0x12, 0x55, 0x0a, 0x13,
	0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x42, 0x4e, 0x50, 0x4c,
	0x46, 0x65, 0x65, 0x12, 0x24, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x42, 0x4e, 0x50, 0x4c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x42, 0x4e,
	0x50, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x00, 0x42, 0x34, 0x5a, 0x32, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67,
	0x72, 0x70, 0x63, 0x42, 0x4e, 0x50, 0x4c, 0x4d, 0x59, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var file_BNPLMY_proto_goTypes = []interface{}{
	(*taskerBNPLRequest.TaskerBNPLRequest)(nil), // 0: taskerBNPLRequest.TaskerBNPLRequest
	(*empty.Empty)(nil),                         // 1: google.protobuf.Empty
}
var file_BNPLMY_proto_depIdxs = []int32{
	0, // 0: grpcBNPLMY.BNPL.ChargeTaskerBNPLFee:input_type -> taskerBNPLRequest.TaskerBNPLRequest
	1, // 1: grpcBNPLMY.BNPL.ChargeTaskerBNPLFee:output_type -> google.protobuf.Empty
	1, // [1:2] is the sub-list for method output_type
	0, // [0:1] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_BNPLMY_proto_init() }
func file_BNPLMY_proto_init() {
	if File_BNPLMY_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_BNPLMY_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_BNPLMY_proto_goTypes,
		DependencyIndexes: file_BNPLMY_proto_depIdxs,
	}.Build()
	File_BNPLMY_proto = out.File
	file_BNPLMY_proto_rawDesc = nil
	file_BNPLMY_proto_goTypes = nil
	file_BNPLMY_proto_depIdxs = nil
}
