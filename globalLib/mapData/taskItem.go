package mapData

import (
	"encoding/json"
	"time"

	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccessV2"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelPricingResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSettingCountry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

/*
MapData task to taskItem info: get data from fileds
_id, serviceText, date, collectionDate, costDetail, newCostDetail, originCurrency,
duration, description, isPremium, status, acceptedTasker, ratingTip, pet, cookingDetail.IsGoMarket,
requirements, detailDeepCleaning, taskPlace, subscriptionId, isTetBooking, DetailLaundry.IsReceived
*/

var MapAddonsToRequirements = map[string]string{
	globalConstant.TASK_ADDONS_APARTMENT: globalConstant.TASK_ITEM_REQUIREMENT_APARTMENT,
}

func MapDataTaskItem(task *modelTask.Task, taskerId string, listAskersWorked []string) map[string]interface{} {
	if task == nil {
		return nil
	}
	taskItemDistrict := MapDistrictText(task.TaskPlace, task.IsoCode)
	taskItem := map[string]interface{}{
		"_id":          task.XId,
		"serviceText":  task.ServiceText,
		"date":         getTaskItemDate(task),
		"cost":         getTaskItemCost(task, taskerId),
		"district":     taskItemDistrict,
		"description":  task.Description,
		"duration":     task.Duration,
		"currency":     task.OriginCurrency,
		"isPremium":    task.IsPremium,
		"status":       task.Status,
		"ratingTip":    task.RatingTip,
		"contactName":  task.ContactName,
		"address":      task.Address,
		"isEco":        task.IsEco,
		"timezone":     task.Timezone,
		"isTetBooking": task.IsTetBooking,
		"serviceName":  task.ServiceName,
	}
	requirements := getTaskItemRequirement(task, listAskersWorked)
	if len(requirements) > 0 {
		taskItem["requirements"] = requirements
	}
	acceptedTasker := getTaskItemAcceptedTasker(task)
	if len(acceptedTasker) > 0 {
		taskItem["acceptedTasker"] = acceptedTasker
	}
	if task.DetailLaundry != nil && task.DetailLaundry.IsReceived {
		taskItem["isReceived"] = task.DetailLaundry.IsReceived
	}
	if len(task.DateOptions) > 0 && task.Status == globalConstant.TASK_STATUS_POSTED { // Chỉ hiện khi POSTED để tasker chọn giờ mong muốn - Nếu đã CONFIRMED,... thì nếu còn thì cũng kg hiện nữa
		taskItem["dateOptions"] = mapDateOptionsForTaskItem(task)
	}
	if task.ForceTasker != nil && task.ForceTasker.TaskerId != "" && (taskerId == "" || taskerId == task.GetForceTasker().GetTaskerId()) {
		taskItem["isForceTasker"] = true
		forceTasker := map[string]interface{}{}
		forceTaskerData, _ := json.Marshal(task.ForceTasker)
		json.Unmarshal(forceTaskerData, &forceTasker)
		if task.Status != globalConstant.TASK_STATUS_POSTED {
			// Chỗ này để hiện thông báo "Bạn đã từ chối công việc này" thay thế action kéo nhận việc trong task detail, nếu task CONFIRMED thì cũng thay thể luôn action Gọi/Nhắn Tin => Nếu kg phải POSTED thì bỏ cờ này đi
			delete(forceTasker, "isRejectedByTasker")
		}
		taskItem["forceTasker"] = forceTasker
	}
	if task.DetailHomeMoving != nil && task.DetailHomeMoving.IsInBuilding {
		taskItem["isHomeMovingInBuilding"] = true
	}
	return taskItem
}

func mapDateOptionsForTaskItem(task *modelTask.Task) []map[string]interface{} {
	var dateOptions []map[string]interface{}
	location, err := time.LoadLocation(task.Timezone)
	if err != nil || location == nil {
		location = globalLib.GetTimeZone()
	}
	for _, dateOption := range task.DateOptions {
		dateOptions = append(dateOptions, map[string]interface{}{
			"_id":        dateOption.XId,
			"date":       globalLib.ParseDateFromTimeStamp(dateOption.Date, location),
			"costDetail": dateOption.CostDetail,
		})
	}
	return dateOptions
}

func MapDistrictText(taskPlace *modelTask.TaskPlace, isoCode string) *modelService.ServiceText {
	if taskPlace == nil {
		return nil
	}
	if taskPlace.DistrictText != nil {
		return taskPlace.DistrictText
	}
	taskItemDistrict := &modelService.ServiceText{
		Vi: taskPlace.District,
		En: taskPlace.District,
		Th: taskPlace.District,
		Id: taskPlace.District,
		Ko: taskPlace.District,
		Ms: taskPlace.District,
	}
	if isoCode == globalConstant.ISO_CODE_VN {
		return taskItemDistrict
	}
	var districtText string = taskPlace.District
	var settingCountry *modelSettingCountry.SettingCountry
	globalDataAccessV2.New(isoCode).GetOneByQuery(globalCollection.COLLECTION_SETTING_COUNTRY[isoCode], bson.M{"isoCode": isoCode}, bson.M{"city": 1}, &settingCountry)
	if settingCountry != nil && len(settingCountry.City) > 0 {
		for _, v := range settingCountry.City {
			if v.Name == taskPlace.City {
				for _, d := range v.District {
					if d.Name == taskPlace.District {
						if d.Text != "" {
							districtText = d.Text
						}
						break
					}
				}
			}
		}
	}
	switch isoCode {
	case globalConstant.ISO_CODE_TH:
		taskItemDistrict.Th = districtText
	case globalConstant.ISO_CODE_VN:
		taskItemDistrict.Vi = districtText
	case globalConstant.ISO_CODE_INDO:
		taskItemDistrict.Id = districtText
	case globalConstant.ISO_CODE_MY:
		taskItemDistrict.Ms = districtText
	}
	return taskItemDistrict
}

// Get Info acceptedTasker
func getTaskItemAcceptedTasker(task *modelTask.Task) []map[string]interface{} {
	var acceptedTaskers []map[string]interface{}
	for _, tasker := range task.AcceptedTasker {
		acceptedTasker := map[string]interface{}{
			"taskerId":  tasker.TaskerId,
			"companyId": tasker.CompanyId,
			"avatar":    tasker.Avatar,
			"name":      tasker.Name,
			"isLeader":  tasker.IsLeader,
		}
		acceptedTaskers = append(acceptedTaskers, acceptedTasker)
	}
	return acceptedTaskers
}

// Get Info Requirements => icon in app
func getTaskItemRequirement(task *modelTask.Task, listAskersWorked []string) []string {
	var requirements []string
	if task.IsPremium {
		requirements = append(requirements, globalConstant.TASK_ITEM_REQUIREMENT_PREMIUM)
	}
	if task.SubscriptionId != "" {
		requirements = append(requirements, globalConstant.TASK_ITEM_REQUIREMENT_SUBSCRIPTION)
	}
	if len(listAskersWorked) > 0 && globalLib.FindStringInSlice(listAskersWorked, task.AskerId) >= 0 {
		requirements = append(requirements, globalConstant.TASK_ITEM_REQUIREMENT_WORKED_FOR_ASKER)
	}
	for _, req := range task.Requirements {
		switch req.Type {
		case 1:
			requirements = append(requirements, globalConstant.TASK_ITEM_REQUIREMENT_COOK)
		case 2:
			requirements = append(requirements, globalConstant.TASK_ITEM_REQUIREMENT_IRON)
		case 3:
			requirements = append(requirements, globalConstant.TASK_ITEM_REQUIREMENT_BRINGTOOLS)
		case 6:
			requirements = append(requirements, globalConstant.TASK_ITEM_REQUIREMENT_CLEANING_GLASSES)
		case 7:
			requirements = append(requirements, globalConstant.TASK_ITEM_REQUIREMENT_VACUUMING_OFFICE_CARPETS)
		case 8:
			requirements = append(requirements, globalConstant.TASK_ITEM_REQUIREMENT_FRIDGE_CLEANING)
		}
	}
	if len(task.Pet) > 0 {
		requirements = append(requirements, globalConstant.TASK_ITEM_REQUIREMENT_PET)
	}
	if task.CookingDetail != nil && task.CookingDetail.IsGoMarket {
		requirements = append(requirements, globalConstant.TASK_ITEM_REQUIREMENT_GO_MARKET)
	}
	if task.CookingDetail != nil && task.CookingDetail.HaveFruit {
		requirements = append(requirements, globalConstant.TASK_ITEM_REQUIREMENT_HAVE_FRUIT)
	}
	if task.IsTetBooking {
		requirements = append(requirements, globalConstant.TASK_ITEM_REQUIREMENT_TET)
	}
	if task.DetailHomeMoving != nil {
		for _, v := range task.DetailHomeMoving.Furniture {
			if v.Name == "electronic" {
				for _, option := range v.Options {
					requirements = append(requirements, option.Name)
				}
			}
		}
	}
	for _, v := range task.Addons {
		if req, ok := MapAddonsToRequirements[v.Name]; ok && req != "" {
			requirements = append(requirements, req)
		}
	}
	return requirements
}

// Get data taskDate
func getTaskItemDate(task *modelTask.Task) time.Time {
	location, err := time.LoadLocation(task.Timezone)
	if err != nil || location == nil {
		location = globalLib.GetTimeZone()
	}
	taskDate := globalLib.ParseDateFromTimeStamp(task.Date, location)
	if globalLib.IsLaundryServiceByKeyName(task.ServiceName) && (task.DetailLaundry == nil || !task.DetailLaundry.IsReceived) {
		taskDate = globalLib.ParseDateFromTimeStamp(task.CollectionDate, location)
	}
	return taskDate
}

// Get task cost by tasker
func getTaskItemCost(task *modelTask.Task, taskerId string) float64 {
	// Case costDetail
	taskCost := task.Cost
	taskCostWithPromotion := task.Cost
	if task.CostDetail != nil && task.CostDetail.Cost > 0 {
		taskCost = task.CostDetail.Cost
	}
	isLeader := false
	if task.DetailDeepCleaning != nil {
		for _, t := range task.AcceptedTasker {
			if t.TaskerId == taskerId && t.IsLeader {
				isLeader = true
				break
			}
		}
		// Nếu là task DeepCleaning => lấy số tiền thực lãnh của tasker nếu không phải leader
		if !isLeader && (task.DetailDeepCleaning.CostPerTasker != nil) {
			taskCost = task.DetailDeepCleaning.CostPerTasker.Total
			taskCostWithPromotion = task.DetailDeepCleaning.CostPerTasker.Main
		}
	} else if task.DetailOfficeCleaning != nil {
		for _, t := range task.AcceptedTasker {
			if t.TaskerId == taskerId && t.IsLeader {
				isLeader = true
				break
			}
		}
		// Nếu là task DeepCleaning => lấy số tiền thực lãnh của tasker nếu không phải leader
		if !isLeader && (task.DetailOfficeCleaning.CostPerTasker != nil) {
			taskCost = task.DetailOfficeCleaning.CostPerTasker.Total
			taskCostWithPromotion = task.DetailOfficeCleaning.CostPerTasker.Main
		}
	} else if task.DetailMassage != nil {
		var taskCostByPackage, taskCostWithPromotionByPackage float64 = 0, 0
		for _, v := range task.DetailMassage.Packages {
			if v.TaskerId == taskerId && v.CostDetail != nil {
				taskCostByPackage += v.CostDetail.Cost
				taskCostWithPromotionByPackage += v.CostDetail.FinalCost
			}
		}
		if taskCostByPackage > 0 {
			taskCost = taskCostByPackage
			taskCostWithPromotion = taskCostWithPromotionByPackage
		}
	} else if task.DetailBeautyCare != nil {
		var taskCostByPackage, taskCostWithPromotionByPackage float64 = 0, 0
		for _, v := range task.DetailBeautyCare.Packages {
			if v.TaskerId == taskerId && v.CostDetail != nil {
				taskCostByPackage += v.CostDetail.Cost
				taskCostWithPromotionByPackage += v.CostDetail.FinalCost
			}
		}
		if taskCostByPackage > 0 {
			taskCost = taskCostByPackage
			taskCostWithPromotion = taskCostWithPromotionByPackage
		}
	} else if task.DetailMakeup != nil {
		if taskCostByPackage, taskCostWithPromotionByPackage := getTaskCostBeautyGroup(task.DetailMakeup.Packages, taskerId); taskCostByPackage > 0 {
			taskCost = taskCostByPackage
			taskCostWithPromotion = taskCostWithPromotionByPackage
		}
	} else if task.DetailNail != nil {
		if taskCostByPackage, taskCostWithPromotionByPackage := getTaskCostBeautyGroup(task.DetailNail.Packages, taskerId); taskCostByPackage > 0 {
			taskCost = taskCostByPackage
			taskCostWithPromotion = taskCostWithPromotionByPackage
		}
	} else if task.DetailHairStyling != nil {
		if taskCostByPackage, taskCostWithPromotionByPackage := getTaskCostBeautyGroup(task.DetailHairStyling.Packages, taskerId); taskCostByPackage > 0 {
			taskCost = taskCostByPackage
			taskCostWithPromotion = taskCostWithPromotionByPackage
		}
	} else if task.CostDetail != nil && task.CostDetail.FinalCost < taskCost {
		if task.CostDetail.FinalCost > 0 {
			taskCostWithPromotion = task.CostDetail.FinalCost
		} else if task.CostDetail.DecreasedReasons != nil && len(task.CostDetail.DecreasedReasons) > 0 { // Nếu final code = 0, kiểm tra đây có phải công việc khuyến mãi 100% hay không.
			for _, v := range task.CostDetail.DecreasedReasons {
				if v.Key == "PROMOTION_CODE" || v.Key == "PROMOTION_PAYMENT_METHOD" {
					taskCostWithPromotion = taskCostWithPromotion - v.Value
					if taskCostWithPromotion < 0 {
						taskCostWithPromotion = 0
					}
					break
				}
			}
		}
	}
	if task.SubscriptionId != "" && task.CostDetail != nil && len(task.CostDetail.DecreasedReasons) > 0 {
		for _, v := range task.CostDetail.DecreasedReasons {
			if v.Key == "SESSION_DISCOUNT" {
				if v.PromotionBy == globalConstant.PROMOTION_BY_BTASKEE {
					taskCost = taskCost + v.Value
					break
				}
			}
		}
	}
	// Case promotionBy:TASKER
	if task.CostDetail != nil && task.CostDetail.PromotionBy == globalConstant.PROMOTION_BY_TASKER {
		taskCost = taskCostWithPromotion
	}

	// Case newTaskCost. Task prepaytask updated
	newTaskCost := getNewTaskItemCost(task, isLeader, taskerId)
	if newTaskCost > 0 {
		taskCost = newTaskCost
	}

	// Không cần chia theo loại tiền mặt hoặc trả trước, vì phần này chỉ cộng tổng tiền cho user thôi. Trường hợp leader đã lấy tổng tiền nên case này chỉ xử lý cho normal tasker
	// Field này chỉ xuất hiện sau khi task đã CONFIRMED nên không cần check thêm chỗ này
	if !isLeader {
		for _, data := range task.IncreaseDurationData {
			for _, tasker := range data.TaskerMoney {
				if tasker.TaskerId == taskerId {
					taskCost += tasker.ExtraMoney
					taskCost += tasker.ExtraMain
					break
				}
			}
		}
	}

	return taskCost
}

func getTaskCostBeautyGroup(packages []*modelTask.TaskDetailBeautyGroupPackage, taskerId string) (taskCost, taskCostWithPromotion float64) {
	for _, v := range packages {
		if v.TaskerId == taskerId && v.CostDetail != nil {
			taskCost += v.CostDetail.Cost
			taskCostWithPromotion += v.CostDetail.FinalCost
		}
	}
	return
}

func getNewTaskItemCost(task *modelTask.Task, isLeader bool, taskerId string) float64 {
	// Case task prepay not update
	if task.CostDetail == nil || (task.CostDetail != nil && task.CostDetail.NewFinalCost == 0) {
		return 0
	}

	taskCost := task.Cost
	taskCostWithPromotion := task.Cost
	// Case deepCleaning và tasker không phải là tasker leader
	if (task.DetailDeepCleaning != nil) && !isLeader {
		if task.DetailDeepCleaning.NewCostPerTasker != nil {
			taskCost = task.DetailDeepCleaning.NewCostPerTasker.Total
			taskCostWithPromotion = task.DetailDeepCleaning.NewCostPerTasker.Main
		}
	} else if (task.DetailOfficeCleaning != nil) && !isLeader {
		if task.DetailOfficeCleaning.NewCostPerTasker != nil {
			taskCost = task.DetailOfficeCleaning.NewCostPerTasker.Total
			taskCostWithPromotion = task.DetailOfficeCleaning.NewCostPerTasker.Main
		}
	} else if task.DetailMassage != nil {
		var taskCostByPackage, taskCostWithPromotionByPackage float64 = 0, 0
		for _, v := range task.DetailMassage.Packages {
			if v.TaskerId == taskerId {
				if v.NewCostDetail != nil {
					taskCostByPackage += v.NewCostDetail.Cost
					taskCostWithPromotionByPackage += v.NewCostDetail.FinalCost
				} else {
					taskCostByPackage += v.GetCostDetail().Cost
					taskCostWithPromotionByPackage += v.GetCostDetail().FinalCost
				}
			}
		}
		if taskCostByPackage > 0 {
			taskCost = taskCostByPackage
			taskCostWithPromotion = taskCostWithPromotionByPackage
		}
	} else if task.DetailBeautyCare != nil {
		var taskCostByPackage, taskCostWithPromotionByPackage float64 = 0, 0
		for _, v := range task.DetailBeautyCare.Packages {
			if v.TaskerId == taskerId {
				if v.NewCostDetail != nil {
					taskCostByPackage += v.NewCostDetail.Cost
					taskCostWithPromotionByPackage += v.NewCostDetail.FinalCost
				} else {
					taskCostByPackage += v.GetCostDetail().Cost
					taskCostWithPromotionByPackage += v.GetCostDetail().FinalCost
				}
			}
		}
		if taskCostByPackage > 0 {
			taskCost = taskCostByPackage
			taskCostWithPromotion = taskCostWithPromotionByPackage
		}
	} else if task.DetailHairStyling != nil {
		if taskCostByPackage, taskCostWithPromotionByPackage := getNewTaskItemCostByPackage(task.DetailHairStyling.Packages, taskerId); taskCostByPackage > 0 {
			taskCost = taskCostByPackage
			taskCostWithPromotion = taskCostWithPromotionByPackage
		}
	} else if task.DetailMakeup != nil {
		if taskCostByPackage, taskCostWithPromotionByPackage := getNewTaskItemCostByPackage(task.DetailMakeup.Packages, taskerId); taskCostByPackage > 0 {
			taskCost = taskCostByPackage
			taskCostWithPromotion = taskCostWithPromotionByPackage
		}
	} else if task.DetailNail != nil {
		if taskCostByPackage, taskCostWithPromotionByPackage := getNewTaskItemCostByPackage(task.DetailNail.Packages, taskerId); taskCostByPackage > 0 {
			taskCost = taskCostByPackage
			taskCostWithPromotion = taskCostWithPromotionByPackage
		}
	} else {
		// Case not deep cleaning
		if task.NewCostDetail != nil {
			taskCost = task.NewCostDetail.Cost
			taskCostWithPromotion = task.NewCostDetail.FinalCost
		} else {
			taskCost = task.CostDetail.NewFinalCost
			taskCostWithPromotion = task.CostDetail.NewFinalCost
			if task.CostDetail != nil && (task.CostDetail.Cost > task.CostDetail.FinalCost) {
				taskCost = task.CostDetail.NewFinalCost + (task.CostDetail.Cost - task.CostDetail.FinalCost)
			}
		}
	}

	if task.CostDetail != nil && task.CostDetail.PromotionBy == globalConstant.PROMOTION_BY_TASKER {
		taskCost = taskCostWithPromotion
	}

	return taskCost
}

func getNewTaskItemCostByPackage(packages []*modelTask.TaskDetailBeautyGroupPackage, taskerId string) (taskCost, taskCostWithPromotion float64) {
	for _, v := range packages {
		if v.TaskerId == taskerId {
			if v.NewCostDetail != nil {
				taskCost += v.NewCostDetail.Cost
				taskCostWithPromotion += v.NewCostDetail.FinalCost
			} else {
				taskCost += v.GetCostDetail().Cost
				taskCostWithPromotion += v.GetCostDetail().FinalCost
			}
		}
	}
	return
}

func mapTaskCostForTaskVAT(costDetail *modelPricingResponse.CostResult) (cost, finalCost float64) {
	if costDetail == nil {
		return cost, finalCost
	}
	cost = costDetail.Cost
	finalCost = costDetail.FinalCost
	if costDetail.Vat > 0 {
		cost = costDetail.Cost + costDetail.Vat
		finalCost = costDetail.TotalCost
	}
	return cost, finalCost
}

func MapCostDetailForAsker(task map[string]interface{}) {
	if costDetail, ok := task["costDetail"].(map[string]interface{}); costDetail != nil && ok {
		var taskCostDetail *modelPricingResponse.CostResult
		taskCostDetailData, _ := json.Marshal(task["costDetail"])
		json.Unmarshal(taskCostDetailData, &taskCostDetail)
		if taskCostDetail.Vat > 0 {
			costDetail["cost"], costDetail["finalCost"] = mapTaskCostForTaskVAT(taskCostDetail)
		}
		if newCostDetail, ok := task["newCostDetail"].(map[string]interface{}); newCostDetail != nil && ok {
			var taskNewCostDetail *modelPricingResponse.CostResult
			taskNewCostDetailData, _ := json.Marshal(task["newCostDetail"])
			json.Unmarshal(taskNewCostDetailData, &taskNewCostDetail)
			if taskNewCostDetail.Vat > 0 {
				_, costDetail["newFinalCost"] = mapTaskCostForTaskVAT(taskNewCostDetail)
			}
		}
		task["costDetail"] = costDetail
	}
}
