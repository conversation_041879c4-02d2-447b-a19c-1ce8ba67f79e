/*
* @File: 49_createTaskerYearEndReportVN_test.go
* @Description: Handler function, case test
 * @CreatedAt: 29/12/2021
 * @Author: vinhnt
*/
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTaskerMonthlyReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerMonthlyReport"
	"go.mongodb.org/mongo-driver/bson"
)

func TestCreateTaskerMonthlyReport(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Unit Test build TaskerMonthlyReport")
		apiURL := apiSyncCron + "/create-tasker-monthly-report"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "0823456720",
				"name":            "Asker 01",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0823456714", "0823456789", "0823456710", "08234568881"},
			}, {
				"phone":           "0823456721",
				"name":            "Asker 02",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0823456714", "0823456789", "0823456710", "08234568881", "08234568882"},
			}, {
				"phone":           "0823456722",
				"name":            "Asker 03",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0823456714", "0823456789", "08234568882"},
			}, {
				"phone":           "0823456789",
				"name":            "Tasker 01",
				"type":            globalConstant.USER_TYPE_TASKER,
				"score":           62,
				"scoreRate":       2,
				"taskDone":        3,
				"isPremiumTasker": true,
				"badges": []map[string]interface{}{
					{
						"name":        "ON_TIME",
						"numOfBadges": 22,
					},
				},
				"address": "Hoàn Kiếm, Hà Nội, VN",
				"workingPlaces": []map[string]interface{}{
					{
						"country":  "VN",
						"city":     "Hà Nội",
						"district": "Hoàn Kiếm",
					}, {
						"country":  "VN",
						"city":     "Hà Nội",
						"district": "Hàng Than",
					}, {
						"country":  "VN",
						"city":     "Bình Dương",
						"district": "Thủ Dầu Một",
					},
				},
			}, {
				"phone":     "0823456710",
				"name":      "Tasker 02",
				"type":      globalConstant.USER_TYPE_TASKER,
				"score":     70,
				"scoreRate": 3,
				"taskDone":  35,
				"badges": []map[string]interface{}{
					{
						"name":        "ON_TIME",
						"numOfBadges": 22,
					}, {
						"name":        "CLEANING",
						"numOfBadges": 21,
					}, {
						"name":        "TIMING",
						"numOfBadges": 23,
					},
				},
				"address": "Quận 7, Hồ Chí Minh, VN",
				"workingPlaces": []map[string]interface{}{
					{
						"country":  "VN",
						"city":     "Hồ Chí Minh",
						"district": "Quận 7",
					}, {
						"country":  "VN",
						"city":     "Biên Hòa",
						"district": "Long Bình",
					},
				},
			}, {
				"phone":           "0823456713",
				"name":            "Tasker 03",
				"type":            globalConstant.USER_TYPE_TASKER,
				"score":           62,
				"scoreRate":       1,
				"taskDone":        52,
				"isPremiumTasker": true,
				"badges": []map[string]interface{}{
					{
						"name":        "ON_TIME",
						"numOfBadges": 22,
					}, {
						"name":        "CLEANING",
						"numOfBadges": 21,
					}, {
						"name":        "GOOD JOB",
						"numOfBadges": 23,
					},
				},
				"workingPlaces": []map[string]interface{}{
					{
						"country": "VN",
						"city":    "Hồ Chí Minh",
					}, {
						"country": "VN",
						"city":    "Bình Dương",
					},
				},
			}, {
				"phone":     "0823456714",
				"name":      "Tasker 04",
				"type":      globalConstant.USER_TYPE_TASKER,
				"score":     62,
				"scoreRate": 1,
				"taskDone":  52,
				"workingPlaces": []map[string]interface{}{
					{
						"country": "VN",
						"city":    "Hồ Chí Minh",
					}, {
						"country": "VN",
						"city":    "Bình Dương",
					},
				},
			}, {
				"phone": "0823456888",
				"name":  "Company 01",
				"type":  globalConstant.USER_TYPE_TASKER,
				"company": map[string]interface{}{
					"companyId": "0823456888",
				},
				"employeeIds": []string{"08234568881", "08234568882"},
			}, {
				"phone": "08234568881",
				"name":  "Emloyee 01",
				"type":  globalConstant.USER_TYPE_TASKER,
				"company": map[string]interface{}{
					"companyId": "0823456888",
				},
				"badges": []map[string]interface{}{
					{
						"name":        "ON_TIME",
						"numOfBadges": 11,
					}, {
						"name":        "CLEANING",
						"numOfBadges": 10,
					}, {
						"name":        "TIMING",
						"numOfBadges": 11,
					},
				},
			}, {
				"phone": "08234568882",
				"name":  "Emloyee 02",
				"type":  globalConstant.USER_TYPE_TASKER,
				"company": map[string]interface{}{
					"companyId": "0823456888",
				},
				"badges": []map[string]interface{}{
					{
						"name":        "ON_TIME",
						"numOfBadges": 11,
					}, {
						"name":        "CLEANING",
						"numOfBadges": 11,
					}, {
						"name":        "TIMING",
						"numOfBadges": 12,
					},
				},
			}, {
				"phone":        "0777777777",
				"name":         "Tasker inactive in year",
				"type":         globalConstant.USER_TYPE_TASKER,
				"lastDoneTask": globalLib.GetCurrentTime(local.TimeZone).AddDate(0, -3, 0),
			},
		})
		var listTasker []map[string]interface{}
		for i := 0; i < 500; i++ {
			tasker := map[string]interface{}{
				"phone":     fmt.Sprintf("%s%d", "0823456713", i),
				"name":      "Tasker 013",
				"type":      globalConstant.USER_TYPE_TASKER,
				"score":     62,
				"scoreRate": 1,
				"taskDone":  52,
				"badges": []map[string]interface{}{
					{
						"name":        "ON_TIME",
						"numOfBadges": 22,
					},
				},
				"workingPlaces": []map[string]interface{}{
					{
						"country": "VN",
						"city":    "Hồ Chí Minh",
					}, {
						"country": "VN",
						"city":    "Bình Dương",
					},
				},
			}
			listTasker = append(listTasker, tasker)
		}
		CreateUser(listTasker)
		createdAtTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, -1, 0)
		var listTask []map[string]interface{}
		for i := 0; i < 500; i++ {
			task := map[string]interface{}{
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"cost": globalLib.RandomIntInRange(200000, 400000),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": fmt.Sprintf("%s%d", "0823456713", i),
					},
				},
			}
			listTask = append(listTask, task)
		}
		for i := 0; i < 50; i++ {
			dateTime := time.Date(createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute(), createdAtTime.Second(), 0, local.TimeZone)
			task := map[string]interface{}{
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), 1, dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
			}
			listTask = append(listTask, task)
		}
		for i := 0; i < 100; i++ {
			dateTime := time.Date(createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute(), createdAtTime.Second(), 0, local.TimeZone)
			task := map[string]interface{}{
				"askerPhone":  "0823456721",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), 1, dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
			}
			listTask = append(listTask, task)
		}
		CreateTask(listTask)
		dateTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, -1, 0)
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), dateTime.Day(), dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"visibility": 3,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
			}, {
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), dateTime.Day(), dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				},
				"detailDeepCleaning": map[string]interface{}{
					"numberOfTaskersDeepCleaning": 2,
					"newCostPerLeaderTasker": map[string]interface{}{
						"total": 300000,
					},
					"newCcostPerTasker": map[string]interface{}{
						"total": 200000,
					},
				},
				"subscriptionId": "123abc",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					}, {
						"taskerId": "08234567131",
						"isLeader": true,
					},
				},
			}, {
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), dateTime.Day(), dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				},
				"detailDeepCleaning": map[string]interface{}{
					"numberOfTaskersDeepCleaning": 2,
					"costPerLeaderTasker": map[string]interface{}{
						"total": 200000,
					},
					"costPerTasker": map[string]interface{}{
						"total": 100000,
					},
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
						"isLeader": true,
					}, {
						"taskerId": "08234567131",
					},
				},
			}, {
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), dateTime.Day(), dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				},
				"subscriptionId": "123abc",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456710",
					},
				},
			}, {
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), dateTime.Day(), dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456888",
					},
				},
			}, {
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), dateTime.Day(), dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "08234568881",
						"companyId": "0823456888",
					},
				},
			}, {
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), dateTime.Day(), dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "08234568882",
						"companyId": "0823456888",
					},
				},
			},
		})

		//Create Rating
		CreateRating([]map[string]interface{}{
			{
				"taskerPhone": "0823456789",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"askerPhone":  "0823456720",
				"rate":        5,
				"review":      "Chị làm tốt, kỹ, nhanh nhẹn",
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
			}, {
				"taskerPhone": "0823456789",
				"askerPhone":  "0823456720",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"rate":        5,
				"review":      "Làm tốt. Lịch sự",
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
			}, {
				"taskerPhone": "0823456789",
				"askerPhone":  "0823456720",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"rate":        5,
				"review":      "Làm tốt. Lịch sự 1",
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
			}, {
				"taskerPhone": "0823456789",
				"askerPhone":  "0823456720",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"rate":        5,
				"review":      "Làm tốt. Lịch sự 2",
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
			}, {
				"taskerPhone": "0823456789",
				"askerPhone":  "0823456720",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"rate":        5,
				"review":      "Làm tốt. Lịch sự 3",
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
			}, {
				"taskerPhone": "0823456789",
				"askerPhone":  "0823456720",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"rate":        5,
				"review":      "Làm tốt. Lịch sự 4",
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
			}, {
				"taskerPhone": "0823456789",
				"askerPhone":  "0823456720",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"rate":        5,
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
			}, {
				"taskerPhone": "0823456789",
				"askerPhone":  "0823456720",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"rate":        2,
				"review":      "Làm tốt. Lịch sự 6",
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
			}, {
				"taskerPhone": "0823456789",
				"askerPhone":  "0823456720",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"review":      "Làm tốt. Lịch sự 6",
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
			}, {
				"taskerPhone": "0823456789",
				"askerPhone":  "0823456720",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
			}, {
				"taskerPhone": "0823456789",
				"askerPhone":  "0823456720",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"rate":        5,
				"review":      "",
			}, {
				"taskerPhone": "0823456789",
				"askerPhone":  "0823456720",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"review":      "",
			}, {
				"taskerPhone": "08234568881",
				"askerPhone":  "0823456720",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"review":      "Good 1.1",
				"rate":        5,
			}, {
				"taskerPhone": "08234568881",
				"askerPhone":  "0823456720",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"review":      "Good 1.2",
				"rate":        5,
			}, {
				"taskerPhone": "08234568882",
				"askerPhone":  "0823456720",
				"isoCode":     globalConstant.ISO_CODE_VN,
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"review":      "Good 2",
				"rate":        5,
			},
		})

		CreateTaskerReward([]map[string]interface{}{
			{
				"userId":    "0823456789",
				"type":      "TASKER_MONTHLY_REWARD",
				"award":     120000,
				"createdAt": createdAtTime,
			}, {
				"userId":    "0823456789",
				"type":      "TASKER_MONTHLY_REWARD",
				"award":     100000,
				"createdAt": createdAtTime,
			}, {
				"userId":    "0823456710",
				"type":      "TASKER_MONTHLY_REWARD",
				"award":     120000,
				"createdAt": createdAtTime,
			}, {
				"userId":    "08234568881",
				"type":      "TASKER_MONTHLY_REWARD",
				"award":     120000,
				"createdAt": createdAtTime,
			}, {
				"userId":    "08234568882",
				"type":      "TASKER_MONTHLY_REWARD",
				"award":     120000,
				"createdAt": createdAtTime,
			},
		})
		CreateTaskerPointTransaction([]map[string]interface{}{
			{
				"userId":    "0823456789",
				"point":     60.0,
				"type":      "C",
				"createdAt": createdAtTime,
				"isoCode":   globalConstant.ISO_CODE_VN,
			}, {
				"userId":    "0823456789",
				"point":     10.0,
				"type":      "D",
				"createdAt": createdAtTime,
				"isoCode":   globalConstant.ISO_CODE_VN,
			}, {
				"userId":    "0823456789",
				"point":     20.0,
				"type":      "D",
				"createdAt": createdAtTime,
				"isoCode":   globalConstant.ISO_CODE_VN,
			}, {
				"userId":    "0823456789",
				"point":     50.0,
				"type":      "D",
				"createdAt": createdAtTime,
				"isoCode":   globalConstant.ISO_CODE_VN,
			}, {
				"userId":    "0823456710",
				"point":     30.0,
				"type":      "D",
				"createdAt": createdAtTime,
				"isoCode":   globalConstant.ISO_CODE_VN,
			}, {
				"userId":    "08234568881",
				"point":     30.0,
				"type":      "D",
				"createdAt": createdAtTime,
				"isoCode":   globalConstant.ISO_CODE_VN,
			}, {
				"userId":    "08234568882",
				"point":     30.0,
				"type":      "D",
				"createdAt": createdAtTime,
				"isoCode":   globalConstant.ISO_CODE_VN,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
				// "runUsers": []string{"0823456789", "0823456710", "0823456888"},
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
				// Remove taskerYearEndReport
				count, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_TASKER_MONTHLY_REPORT[local.ISO_CODE], bson.M{})
				So(count, ShouldEqual, 506)
				var taskerReportDatas []*modelTaskerMonthlyReport.TaskerMonthlyReport
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASKER_MONTHLY_REPORT[local.ISO_CODE], bson.M{"userId": bson.M{"$in": []string{"0823456789", "0823456710", "0823456888"}}}, bson.M{}, &taskerReportDatas)
				tested := map[string]bool{}
				for _, tRp := range taskerReportDatas {
					if tRp.UserId == "0823456789" {
						So(tRp.MonthlyReport[0].TotalTaskDone, ShouldEqual, 153)
						So(tRp.MonthlyReport[0].TotalTaskCost, ShouldEqual, 30600000.0)
						So(tRp.MonthlyReport[0].TotalMonthlyRewardAmount, ShouldEqual, 220000)
						So(len(tRp.MonthlyReport[0].Reviews), ShouldEqual, 6)
						So(tRp.MonthlyReport[0].TotalBPoint, ShouldEqual, 80)
						tested[tRp.XId] = true
					} else if tRp.UserId == "0823456710" {
						So(tRp.MonthlyReport[0].TotalTaskDone, ShouldEqual, 1)
						So(tRp.MonthlyReport[0].TotalTaskCost, ShouldEqual, 200000.0)
						So(tRp.MonthlyReport[0].TotalMonthlyRewardAmount, ShouldEqual, 120000)
						So(tRp.MonthlyReport[0].TotalBPoint, ShouldEqual, 30)
						tested[tRp.XId] = true
					} else if tRp.UserId == "0823456888" {
						So(tRp.MonthlyReport[0].TotalTaskDone, ShouldEqual, 2)
						So(tRp.MonthlyReport[0].TotalTaskCost, ShouldEqual, 400000.0)
						So(tRp.MonthlyReport[0].TotalMonthlyRewardAmount, ShouldEqual, 240000)
						So(tRp.MonthlyReport[0].TotalBPoint, ShouldEqual, 0) // Company no has bPoint
						So(tRp.MonthlyReport[0].NumberOfGoodRating, ShouldEqual, 3)
						So(len(tRp.MonthlyReport[0].Reviews), ShouldEqual, 3)
						for _, review := range tRp.MonthlyReport[0].Reviews {
							So(review, ShouldBeIn, []string{"Good 1.1", "Good 1.2", "Good 2"})
						}
						tested[tRp.XId] = true
					}
				}
				So(len(tested), ShouldEqual, 3)
				// not run for tasker in company and not owner, tasker inactive
				isExist, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_TASKER_MONTHLY_REPORT[local.ISO_CODE], bson.M{"userId": bson.M{"$in": []string{"08234568881", "08234568882"}}})
				So(isExist, ShouldBeFalse)
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		log.Println("==================================== Unit Test build TaskerMonthlyReport")
		apiURL := apiSyncCron + "/create-tasker-monthly-report"
		ResetData()
		createdAtTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, -1, 0)
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone":           "0823456720",
				"name":            "Asker 01",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0823456714"},
			}, {
				"phone":           "0823456721",
				"name":            "Asker 02",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0823456714"},
			}, {
				"phone":           "0823456722",
				"name":            "Asker 03",
				"type":            globalConstant.USER_TYPE_ASKER,
				"favouriteTasker": []string{"0823456714"},
			}, {
				"phone":           "0823456789",
				"name":            "Tasker 01",
				"type":            globalConstant.USER_TYPE_TASKER,
				"score":           62,
				"scoreRate":       2,
				"taskDone":        3,
				"isPremiumTasker": true,
				"badges": []map[string]interface{}{
					{
						"name":        "ON_TIME",
						"numOfBadges": 22,
					},
				},
				"address": "Hoàn Kiếm, Hà Nội, VN",
				"workingPlaces": []map[string]interface{}{
					{
						"country":  "VN",
						"city":     "Hà Nội",
						"district": "Hoàn Kiếm",
					}, {
						"country":  "VN",
						"city":     "Hà Nội",
						"district": "Hàng Than",
					}, {
						"country":  "VN",
						"city":     "Bình Dương",
						"district": "Thủ Dầu Một",
					},
				},
			}, {
				"phone":     "0823456710",
				"name":      "Tasker 012",
				"type":      globalConstant.USER_TYPE_TASKER,
				"score":     70,
				"scoreRate": 3,
				"taskDone":  35,
				"badges": []map[string]interface{}{
					{
						"name":        "ON_TIME",
						"numOfBadges": 22,
					}, {
						"name":        "CLEANING",
						"numOfBadges": 21,
					}, {
						"name":        "TIMING",
						"numOfBadges": 23,
					},
				},
				"address": "Quận 7, Hồ Chí Minh, VN",
				"workingPlaces": []map[string]interface{}{
					{
						"country":  "VN",
						"city":     "Hồ Chí Minh",
						"district": "Quận 7",
					}, {
						"country":  "VN",
						"city":     "Biên Hòa",
						"district": "Long Bình",
					},
				},
			}, {
				"phone":           "0823456713",
				"name":            "Tasker 013",
				"type":            globalConstant.USER_TYPE_TASKER,
				"score":           62,
				"scoreRate":       1,
				"taskDone":        52,
				"isPremiumTasker": true,
				"badges": []map[string]interface{}{
					{
						"name":        "ON_TIME",
						"numOfBadges": 22,
					}, {
						"name":        "CLEANING",
						"numOfBadges": 21,
					}, {
						"name":        "GOOD JOB",
						"numOfBadges": 23,
					},
				},
				"workingPlaces": []map[string]interface{}{
					{
						"country": "VN",
						"city":    "Hồ Chí Minh",
					}, {
						"country": "VN",
						"city":    "Bình Dương",
					},
				},
			}, {
				"phone":     "0823456714",
				"name":      "Tasker 014",
				"type":      globalConstant.USER_TYPE_TASKER,
				"score":     62,
				"scoreRate": 1,
				"taskDone":  52,
				"workingPlaces": []map[string]interface{}{
					{
						"country": "VN",
						"city":    "Hồ Chí Minh",
					}, {
						"country": "VN",
						"city":    "Bình Dương",
					},
				},
			}, {
				"phone": "0823456888",
				"name":  "Company 01",
				"type":  globalConstant.USER_TYPE_TASKER,
				"company": map[string]interface{}{
					"companyId": "0823456888",
				},
				"employeeIds": []string{"08234568881"},
			}, {
				"phone": "08234568881",
				"name":  "Emloyee 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"company": map[string]interface{}{
					"companyId": "0823456888",
				},
			},
		})

		dateTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, -1, 0)
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), dateTime.Day(), dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				},
				"detailDeepCleaning": map[string]interface{}{
					"numberOfTaskersDeepCleaning": 2,
					"newCostPerLeaderTasker": map[string]interface{}{
						"total": 300000,
					},
					"newCostPerTasker": map[string]interface{}{
						"total": 200000,
					},
				},
				"subscriptionId": "123abc",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					}, {
						"taskerId": "0823456710",
						"isLeader": true,
					},
				},
				"increaseDurationData": []map[string]interface{}{
					{
						"increaseDuration": 1,
						"taskerMoney": []map[string]interface{}{
							{
								"taskerId":   "0823456789",
								"extraMoney": 100000,
							},
						},
					},
				},
			},
			{
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), dateTime.Day(), dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				},
				"detailDeepCleaning": map[string]interface{}{
					"numberOfTaskersDeepCleaning": 2,
					"newCostPerLeaderTasker": map[string]interface{}{
						"total": 300000,
					},
					"newCostPerTasker": map[string]interface{}{
						"total": 200000,
					},
				},
				"subscriptionId": "123abc",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					}, {
						"taskerId": "0823456710",
						"isLeader": true,
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
				// Remove taskerYearEndReport
				count, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_TASKER_MONTHLY_REPORT[globalConstant.ISO_CODE_VN], bson.M{})
				So(count, ShouldEqual, 5)
				var taskerReportDatas []*modelTaskerMonthlyReport.TaskerMonthlyReport
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASKER_MONTHLY_REPORT[globalConstant.ISO_CODE_VN], bson.M{"userId": bson.M{"$in": []string{"0823456789", "0823456710"}}}, bson.M{}, &taskerReportDatas)
				for _, tRp := range taskerReportDatas {
					So(tRp.MonthlyReport[0].TotalTaskDone, ShouldEqual, 2)
					// So(tRp.AvgRating, ShouldEqual, 2.5)
					So(tRp.MonthlyReport[0].TotalMonthlyRewardAmount, ShouldEqual, 0)
					So(len(tRp.MonthlyReport[0].Reviews), ShouldEqual, 0)
					So(tRp.MonthlyReport[0].TotalBPoint, ShouldEqual, 0)

					if tRp.UserId == "0823456789" {
						So(tRp.MonthlyReport[0].TotalTaskCost, ShouldEqual, 500000.0)
					} else {
						So(tRp.MonthlyReport[0].TotalTaskCost, ShouldEqual, 400000.0)
					}
				}
			})
		})
	})
}
