package th

import (
	"errors"
	"fmt"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/th"
	"gitlab.com/btaskee/go-services-model-v2/globalModel/modelTransactionInfo"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/payment2C2PTransaction"
	modelRefundRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/refundRequest"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func GetCardTransactionInfoByTask_TH(taskId string) (transactionInfo *modelTransactionInfo.TransactionInfo, err error) {
	pattern := fmt.Sprintf("%s_%s", globalConstant.PAYMENT_REFERRENCE_PAY_TASK, taskId)
	var transaction *payment2C2PTransaction.Payment2C2PTransaction
	err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_PAYMENT_2C2P_TRANSACTION,
		bson.M{
			"reference":   bson.M{"$regex": primitive.Regex{Pattern: pattern, Options: "i"}},
			"data.taskId": taskId,
		},
		bson.M{
			"_id":         1,
			"charged":     1,
			"cost":        1,
			"status":      1,
			"data.taskId": 1,
			"isRefunded":  1,
			"createdAt":   1,
			"userId":      1,
			"invoiceNo":   1,
		},
		&transaction,
	)
	if err != nil {
		return nil, err
	}
	if transaction == nil {
		return nil, errors.New("transaction not found")
	}

	result := &modelTransactionInfo.TransactionInfo{
		XId:           transaction.XId,
		InvoiceNo:     transaction.InvoiceNo,
		Status:        transaction.Status,
		Amount:        transaction.Cost,
		Charged:       transaction.Charged,
		TaskId:        taskId,
		Date:          transaction.CreatedAt,
		IsRefunded:    transaction.IsRefunded,
		PaymentMethod: globalConstant.PAYMENT_METHOD_CARD,
		UserId:        transaction.UserId,
	}
	if transaction.Data != nil && transaction.Data.TaskId != "" {
		result.TaskId = transaction.Data.TaskId
	}
	if transaction.UserId == "" {
		taskMap, _ := globalDataAccess.GetOneByQueryMap(globalCollection.COLLECTION_TASK[globalConstant.ISO_CODE_TH], bson.M{"_id": taskId}, bson.M{"askerId": 1})
		if taskMap != nil {
			result.UserId = cast.ToString(taskMap["askerId"])
		}
	}
	if transaction.IsRefunded {
		result.Refund = getRefundTransactionInfo(taskId, transaction.InvoiceNo, globalConstant.PAYMENT_METHOD_CARD)
	}

	return result, nil
}

func getRefundTransactionInfo(taskId, invoiceNo, paymentMethod string) *modelTransactionInfo.RefundInfo {
	var refundRequest *modelRefundRequest.RefundRequest
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_REFUND_REQUEST[globalConstant.ISO_CODE_TH],
		bson.M{
			"taskId":        taskId,
			"invoiceNo":     invoiceNo,
			"paymentMethod": paymentMethod,
		},
		bson.M{
			"status":        1,
			"amount":        1,
			"cancelTaskFee": 1,
		},
		&refundRequest,
	)
	if refundRequest == nil {
		return nil
	}
	return &modelTransactionInfo.RefundInfo{
		Status:       refundRequest.Status,
		Date:         refundRequest.CreatedAt,
		RefundAmount: refundRequest.Amount,
		CancelFee:    refundRequest.CancelTaskFee,
		Reason:       refundRequest.Reason,
	}
}
