import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import {HTTP} from 'meteor/http';
const { appId, endpoint, macKey } = Meteor.settings.ZALO_PAY;
import crypto from 'crypto';
import _ from 'lodash'
import {
  postToSlack,
  trackingScheduleStatus,
} from '../helper-function';

const processOrder = (respond, apptransid) => {
  try {
    if (respond.status === 'error') {
      // fail when call API
      postToSlack({
        channel: 'zalopay-repay',
        text: `Workflow: There was an error on runAutoCheckOrderZaloPay: apptransid: ${apptransid} Message: ${JSON.stringify(respond.data)}`,
      });
      return;
    }
    delete respond.status;
    let objUpdate = {};

    // insert zptransid
    const zptransid = _.get(respond, 'data.zptransid', null);
    if (zptransid) {
      objUpdate.zptransid = zptransid;
    }
    
    // PAID
    if (respond.data.returncode === 1) {
      objUpdate = {
        result: respond.data,
        isCheckedStatusOrder: true, // flag checked status,
        updatedAt: new Date(),
        newStatus: 'REPAY',
      }
      postToSlack({
        channel: 'zalopay-repay',
        text: `Workflow: Đơn hàng đã được thanh toán qua ZaloPay thành công. Hãy kiểm tra apptransid: ${apptransid} để Top Up bằng tay cho khách hàng.`,
      });
    } else { // pay fail
      objUpdate = {
        result: respond.data,
        isCheckedStatusOrder: true, // flag checked status,
        updatedAt: new Date(),
        status: 'FAILED',
      }
    }
    // error
    Meteor.appCollection.ZaloPayTransaction.update({apptransid: apptransid}, {$set: {...objUpdate}});
    return;
  } catch (error) {
    throw error;
  }
}

const checkStatusOrder = (apptransid) => {
  const params = {
    appid: appId,
    apptransid, 
  };
  const hmacInput = appId + "|" + apptransid + "|" + macKey; // appid|apptransid|key1
  params.mac = crypto.createHmac('sha256', macKey)
              .update(new Buffer(hmacInput, 'utf-8'))
              .digest('hex');
  const options = {
    params: params,
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
    },
    timeout: 10000,
  };
  // check order api
  const response = HTTP.get(endpoint, options);
  // check statusCode
  if (response.statusCode !== 200) {
    return { status: 'error', statusCode: response.statusCode };
  }
  // success
  return { status: 'success', data: response.data };
}

const runAutoCheckOrderZaloPay = () => {
  return;
	console.log("Start runAutoCheckOrderZaloPay ...");
  var message = null;
  try {
    // only get info 48h
    const dateQr = moment().subtract(2, 'days').toDate();
    // get orders
    const data = Meteor.appCollection.ZaloPayTransaction.find({
      apptransid: { $exists: true },
      status: 'CHARGING',
      charged: false,
      isCheckedStatusOrder: { $ne: true },
      createdAt: { $gte: dateQr }
    }, {
      fields: { apptransid: 1, createdAt: 1, data: 1, reference: 1 }
    }).fetch();

    // not order
    if (data.length === 0) {
      return;
    }
    data.forEach((order) => {
      // only check order generated after 20 minutes
      if (moment().isAfter(moment(order.createdAt).add(20, 'minutes'))) {
        // check order
        const respond = checkStatusOrder(order.apptransid);
        // update transaction from result
        processOrder(respond, order.apptransid);
      }
    });
  } catch (error) {
    message = error;
    // can remove
    postToSlack({
      channel: 'zalopay-repay',
      text: `Workflow: There was an error on runAutoCheckOrderZaloPay. Message: ${JSON.stringify(error)}`,
    });
  }
	console.log("Finished runAutoCheckOrderZaloPay ");
  // Tracking
  trackingScheduleStatus({name: 'runAutoCheckOrderZaloPay', nextRunAt: SyncedCron.nextScheduledAtDate('runAutoCheckOrderZaloPay'), message});
  };

  Meteor.methods({
    autoCheckOrderZaloPay() {
      SyncedCron.add({
        name: 'autoCheckOrderZaloPay',
        schedule(parser) {
          // Run every 20 minutes.
          return parser.cron(`0 0/20 0/1 1/1 * ? *`, true);
        },
        job() {
          runAutoCheckOrderZaloPay();
        },
      });
	    console.log("Added autoCheckOrderZaloPay cron");
    },
    'runNow.autoCheckOrderZaloPay'() {
      runAutoCheckOrderZaloPay();
    },
  });
