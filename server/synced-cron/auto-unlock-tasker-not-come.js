import { Meteor } from 'meteor/meteor';
import _ from 'lodash';
import {
  trackingScheduleStatus,
  revertTimeZone,
  sendNotificationByIds
} from '/server/helper-function';

const autoUnlockTaskerNotCome = () => {
  return;
	console.log("Start autoUnlockTaskerNotCome ...");
  var message = null;
  try {

    const historyIds = Meteor.appCollection.NotComeLockHistory.find({
      status: 'LOCKED',
      reactiveDate: {$lte: new Date()},
    }).forEach((lockHistory) => {
      const tasker = Meteor.appCollection.users.findOne({ _id: lockHistory.taskerId, type: 'TASKER', status: 'LOCKED' });
      if ( tasker ) {
        // Unlock Tasker
        Meteor.appCollection.users.update({ _id: tasker._id }, {
          $set: {
            status: 'ACTIVE',
            updatedAt: new Date(),
            lastActiveAt: new Date(),
          }
        });
        // Update history status
        Meteor.appCollection.NotComeLockHistory.update({ _id: lockHistory._id }, {$set: {status: 'UNLOCKED', updatedAt: new Date()}});
       
        const taskerCity = tasker.workingPlaces && tasker.workingPlaces.length > 0 ? tasker.workingPlaces[0].city : '';
        Meteor.appCollection.UserActionHistory.insert({
          phone: tasker.phone,
          name: tasker.name,
          action: 'ACTIVE_TASKER',
          data: {
            taskerId: tasker._id,
            reason: 'Auto unlock for Taskers locked by reason not came to work',
            city: taskerCity,
            supporter: 'Auto System',
          },
          createdAt: new Date(),
        });

        // Send notification
        sendNotificationByIds([tasker._id], {
          title: {
            vi: 'Tài khoản của bạn đã được mở',
            en: 'Your account has been active',
            ko: 'Your account has been active',
            th: 'Your account has been active',
          },
          text: {
            vi: `Tài khoản của bạn đã được mở lại.`,
            en: `Tài khoản của bạn đã được mở lại.`,
            ko: `Tài khoản của bạn đã được mở lại.`,
            th: `Tài khoản của bạn đã được mở lại.`,
          },
          payload: {},
        }, {isForceView: true, type: 30});
      } else {
        // Tasker is active by backend. Just update NotComeLockHistory status
        Meteor.appCollection.NotComeLockHistory.update({ _id: lockHistory._id }, {$set: {status: 'UNLOCKED', updatedAt: new Date()}});
      }
    });
  } catch (e) {
    message = e;
  }
	console.log("Finished autoUnlockTaskerNotCome ");
  // Tracking
  trackingScheduleStatus({name: 'autoUnlockTaskerNotCome', nextRunAt: SyncedCron.nextScheduledAtDate('autoUnlockTaskerNotCome'), message});
};

  Meteor.methods({
    // Auto lock Tasker not come
    autoUnlockTaskerNotCome() {
      SyncedCron.add({
        name: 'autoUnlockTaskerNotCome',
        schedule(parser) {
          // Run at 40' every hour GMT+7
          return parser.cron(`0 40 0/1 1/1 * ? *`, true);
        },
        job() {
          autoUnlockTaskerNotCome();
        },
      });
	    console.log("Added autoUnlockTaskerNotCome cron");
    },
    'runNow.autoUnlockTaskerNotCome'() {
      autoUnlockTaskerNotCome();
    },
  });
