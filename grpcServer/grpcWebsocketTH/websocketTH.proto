syntax = "proto3";
package grpcWebsocketTH;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcWebsocketTH";

import "google/protobuf/empty.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage/websocketMessage.proto";

service WebsocketTH {
  rpc SendChatMessage (websocketMessage.WebsocketMessage) returns (google.protobuf.Empty) {}
  rpc SendSocketMessage (websocketMessage.WebsocketMessage) returns (google.protobuf.Empty) {}
}