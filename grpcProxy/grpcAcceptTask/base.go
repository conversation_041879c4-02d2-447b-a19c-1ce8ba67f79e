package grpcAcceptTask

import (
	"context"
	"errors"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcAcceptTaskTH"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/acceptBookingRequest"
	"google.golang.org/grpc"
	"google.golang.org/protobuf/types/known/emptypb"
)

type AcceptTaskTHClient interface {
	ChooseTasker(ctx context.Context, in *acceptBookingRequest.AcceptBookingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

func Connect(isoCode, endpoint string) (AcceptTaskTHClient, *grpc.ClientConn, error) {
	switch isoCode {
	case globalConstant.ISO_CODE_TH:
		return ConnectGRPCAcceptTaskTH(endpoint)
	default:
		return nil, nil, errors.New("iso code incorrect")
	}
}

func ConnectGRPCAcceptTaskTH(endpoint string) (grpcAcceptTaskTH.AcceptTaskTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcAcceptTaskTH.NewAcceptTaskTHClient(connect)
	return client, connect, err
}
