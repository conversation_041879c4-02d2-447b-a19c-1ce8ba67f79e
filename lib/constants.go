package lib

import "gitlab.com/btaskee/go-services-model-v2/globalConstant"

const (
	RUN_NOW = iota + 1
	START
	STOP
	TRAINING_TASKER_PREMIUM_SLACK_CHANNEL = "csng-premium"
	TASKER_JOURNEY_SLACK_CHANNEL          = "go-journey-tasker"

	GO_SERVICE_SYNC_CRON_CHANNEL   = "go-service-sync-cron"
	GO_SERVICE_CHANNEL             = "go-services"
	GO_SERVICE_SYNC_CRON_USER_NAME = "bTaskee System"
	BTASKEE_SYSTEM_CHANNEL         = "btaskee-system"

	TIME_LAYOUT_YYYY_MM_DD       = "20060102"
	TIME_LAYOUT_YYYY_MM_DD_HH_MM = "200601021504"

	LIMIT_TASK_DATE = 8
)

var PREMIUM_SUPPORT_SERVICE = []string{
	globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
}

var PREMIUM_SUPPORT_CITY = []string{
	"<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON>ội",
	"<PERSON><PERSON> Nẵng",
}

var TASKER_CONTACT_INFO = map[string]map[string]string{
	globalConstant.ISO_CODE_VN: {
		"hotline":    "0901300501",
		"socialName": "Zalo OA",
		"socialLink": "https://zalo.me/784043022254331849",
	},
	globalConstant.ISO_CODE_TH: {
		"hotline":    "021146111",
		"socialName": "Line OA",
		"socialLink": "https://lin.ee/2IigDT7",
	},
	globalConstant.ISO_CODE_INDO: {
		"hotline":    "02139524592",
		"socialName": "Whatsapp",
		"socialLink": "https://api.whatsapp.com/send/?phone=6281110010272&text&type=phone_number&app_absent=0",
	},
}

const (
	PERCENT_ONE_THIRD_PROCESS = 0.3
	PERCENT_HALF_PROCESS      = 0.5
	PERCENT_COMPLETE_PROCESS  = 1.0

	JOURNEY_YOUNG_BE = "LV1"
)

const (
	NUMBER_OF_DATE_TO_DEADLINE = 7
)
