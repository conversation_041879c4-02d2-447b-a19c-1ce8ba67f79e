package giveRewardTaskerDoneSpecialCampaign

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelSpecialCampaignTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/specialCampaignTransaction"
)

func insertSpecialCampaignTransaction(taskerId, phone, campaignId, campaignName, specialCampaignTransactionStatus string, percentProcess float64) (string, error) {
	specialCampaignTransaction := &modelSpecialCampaignTransaction.SpecialCampaignTransaction{
		XId:            globalLib.GenerateObjectId(),
		Phone:          phone,
		TaskerId:       taskerId,
		CampaignId:     campaignId,
		CampaignName:   campaignName,
		Status:         specialCampaignTransactionStatus,
		CreatedAt:      globalLib.GetCurrentTimestamp(local.TimeZone),
		PercentProcess: percentProcess,
	}

	if specialCampaignTransactionStatus == globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED {
		specialCampaignTransaction.CompletedDate = globalLib.GetCurrentTimestamp(local.TimeZone)
	}

	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], &specialCampaignTransaction)
	return specialCampaignTransaction.XId, err
}
