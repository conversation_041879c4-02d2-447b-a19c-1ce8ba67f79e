export GOOS=linux
export CGO_ENABLED=0

go build -o email-vn-v3-linux-amd64

export GOOS=darwin

export IMAGE_DOCKER=linhnhdocker/go-email-service:vn-3.0.0

docker build \
      --build-arg ELASTIC_APM_SERVICE_NAME="email-vn-v3" \
      --build-arg ELASTIC_APM_SERVER_URL="http://apm-server:8200" \
      --build-arg MODE="dev" \
      -t $IMAGE_DOCKER .

docker push $IMAGE_DOCKER

kubectl apply -f deploy/k8s/deployment_dev.yml