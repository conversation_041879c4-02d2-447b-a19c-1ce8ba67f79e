package grpcScheduleVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcScheduleVN"
	"google.golang.org/grpc"
)

func ConnectGRPCScheduleVN(endpoint string) (grpcScheduleVN.ScheduleVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcScheduleVN.NewScheduleVNClient(connect)
	return client, connect, err
}
