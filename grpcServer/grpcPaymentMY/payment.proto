syntax = "proto3";
package grpcPaymentMY;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPaymentMY";

import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentRequest/payment-request.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response/response.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/vatRequest/vatRequest.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/refundTransaction/refundTransaction.proto";

service Payment {
  rpc CreateGiftByComboVoucher (paymentRequest.PaymentRequest) returns (response.Response) {}
  rpc CreateVatRequest (vatRequest.VatRequest) returns (response.Response) {}
  rpc Refund (refundTransaction.RefundTransaction) returns (response.Response) {}
  rpc SubscriptionPayment (paymentRequest.PaymentRequest) returns (response.Response) {}
}
