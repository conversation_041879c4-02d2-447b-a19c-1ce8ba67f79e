// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcPricingVN

import (
	context "context"
	pricingSubscriptionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionRequest"
	pricingSubscriptionResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionResponse"
	pricingrequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingrequest"
	pricingresponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PricingVNClient is the client API for PricingVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PricingVNClient interface {
	GetPricingWashingMachine(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingAirConditioner(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingHomeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingSubscription(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingDeepCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingHomeCooking(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingLaundry(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingLaundry(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingGroceryAssistant(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingSofa(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingHousekeeping(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingElderlyCare(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingPatientCare(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingDisinfection(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	APIGetPricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	ReCalculatePricing(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	ReCalculatePricingForIncreaseDuration(ctx context.Context, in *pricingrequest.PricingIncreaseDurationRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingSubscriptionPatientCare(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingSubscriptionElderlyCare(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingChildCare(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingSubscriptionChildCare(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingSubscriptionOfficeCleaning(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	APIGetPricingSubscriptionOfficeCleaning(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingWaterHeater(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingCarpetCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingHomeMoving(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingHomeMoving(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	APIGetPricingTaskDateOptions(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingAirConditionerV2(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingHousekeepingV2(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingIndustrialCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingBeautyCare(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingBeautyCare(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	// pricing makeup
	GetPricingMakeup(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	// pricing nail
	GetPricingNail(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	// pricing hair styling
	GetPricingHairStyling(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingIroning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
}

type pricingVNClient struct {
	cc grpc.ClientConnInterface
}

func NewPricingVNClient(cc grpc.ClientConnInterface) PricingVNClient {
	return &pricingVNClient{cc}
}

func (c *pricingVNClient) GetPricingWashingMachine(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingWashingMachine", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingAirConditioner(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingAirConditioner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingHomeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingHomeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingSubscription(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	out := new(pricingSubscriptionResponse.PricingSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingDeepCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingDeepCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingHomeCooking(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingHomeCooking", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingLaundry(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingLaundry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) RePricingLaundry(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/RePricingLaundry", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingGroceryAssistant(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingGroceryAssistant", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingSofa(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingSofa", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingHousekeeping(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingHousekeeping", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingElderlyCare(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingElderlyCare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingPatientCare(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingPatientCare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingDisinfection(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingDisinfection", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingOfficeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) APIGetPricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/APIGetPricingOfficeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) ReCalculatePricing(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/ReCalculatePricing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) ReCalculatePricingForIncreaseDuration(ctx context.Context, in *pricingrequest.PricingIncreaseDurationRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/ReCalculatePricingForIncreaseDuration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingSubscriptionPatientCare(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	out := new(pricingSubscriptionResponse.PricingSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingSubscriptionPatientCare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingSubscriptionElderlyCare(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	out := new(pricingSubscriptionResponse.PricingSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingSubscriptionElderlyCare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingChildCare(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingChildCare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingSubscriptionChildCare(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	out := new(pricingSubscriptionResponse.PricingSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingSubscriptionChildCare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingSubscriptionOfficeCleaning(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	out := new(pricingSubscriptionResponse.PricingSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingSubscriptionOfficeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) APIGetPricingSubscriptionOfficeCleaning(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	out := new(pricingSubscriptionResponse.PricingSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/APIGetPricingSubscriptionOfficeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingWaterHeater(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingWaterHeater", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingCarpetCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingCarpetCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingHomeMoving(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingHomeMoving", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) RePricingHomeMoving(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/RePricingHomeMoving", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) APIGetPricingTaskDateOptions(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/APIGetPricingTaskDateOptions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingAirConditionerV2(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingAirConditionerV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingHousekeepingV2(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingHousekeepingV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingIndustrialCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingIndustrialCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingBeautyCare(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingBeautyCare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) RePricingBeautyCare(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/RePricingBeautyCare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingMakeup(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingMakeup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingNail(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingNail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingHairStyling(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingHairStyling", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingVNClient) GetPricingIroning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingVN.PricingVN/GetPricingIroning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PricingVNServer is the server API for PricingVN service.
// All implementations must embed UnimplementedPricingVNServer
// for forward compatibility
type PricingVNServer interface {
	GetPricingWashingMachine(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingAirConditioner(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingHomeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingSubscription(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingDeepCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingHomeCooking(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingLaundry(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingLaundry(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingGroceryAssistant(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingSofa(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingHousekeeping(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingElderlyCare(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingPatientCare(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingDisinfection(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	APIGetPricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	ReCalculatePricing(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	ReCalculatePricingForIncreaseDuration(context.Context, *pricingrequest.PricingIncreaseDurationRequest) (*pricingresponse.CostResult, error)
	GetPricingSubscriptionPatientCare(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingSubscriptionElderlyCare(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingChildCare(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingSubscriptionChildCare(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingSubscriptionOfficeCleaning(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	APIGetPricingSubscriptionOfficeCleaning(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingWaterHeater(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingCarpetCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingHomeMoving(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingHomeMoving(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	APIGetPricingTaskDateOptions(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingAirConditionerV2(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingHousekeepingV2(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingIndustrialCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingBeautyCare(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingBeautyCare(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	// pricing makeup
	GetPricingMakeup(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	// pricing nail
	GetPricingNail(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	// pricing hair styling
	GetPricingHairStyling(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingIroning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	mustEmbedUnimplementedPricingVNServer()
}

// UnimplementedPricingVNServer must be embedded to have forward compatible implementations.
type UnimplementedPricingVNServer struct {
}

func (UnimplementedPricingVNServer) GetPricingWashingMachine(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingWashingMachine not implemented")
}
func (UnimplementedPricingVNServer) GetPricingAirConditioner(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingAirConditioner not implemented")
}
func (UnimplementedPricingVNServer) GetPricingHomeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingHomeCleaning not implemented")
}
func (UnimplementedPricingVNServer) GetPricingSubscription(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingSubscription not implemented")
}
func (UnimplementedPricingVNServer) GetPricingDeepCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingDeepCleaning not implemented")
}
func (UnimplementedPricingVNServer) GetPricingHomeCooking(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingHomeCooking not implemented")
}
func (UnimplementedPricingVNServer) GetPricingLaundry(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingLaundry not implemented")
}
func (UnimplementedPricingVNServer) RePricingLaundry(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingLaundry not implemented")
}
func (UnimplementedPricingVNServer) GetPricingGroceryAssistant(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingGroceryAssistant not implemented")
}
func (UnimplementedPricingVNServer) GetPricingSofa(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingSofa not implemented")
}
func (UnimplementedPricingVNServer) GetPricingHousekeeping(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingHousekeeping not implemented")
}
func (UnimplementedPricingVNServer) GetPricingElderlyCare(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingElderlyCare not implemented")
}
func (UnimplementedPricingVNServer) GetPricingPatientCare(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingPatientCare not implemented")
}
func (UnimplementedPricingVNServer) GetPricingDisinfection(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingDisinfection not implemented")
}
func (UnimplementedPricingVNServer) GetPricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingOfficeCleaning not implemented")
}
func (UnimplementedPricingVNServer) APIGetPricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method APIGetPricingOfficeCleaning not implemented")
}
func (UnimplementedPricingVNServer) ReCalculatePricing(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReCalculatePricing not implemented")
}
func (UnimplementedPricingVNServer) ReCalculatePricingForIncreaseDuration(context.Context, *pricingrequest.PricingIncreaseDurationRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReCalculatePricingForIncreaseDuration not implemented")
}
func (UnimplementedPricingVNServer) GetPricingSubscriptionPatientCare(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingSubscriptionPatientCare not implemented")
}
func (UnimplementedPricingVNServer) GetPricingSubscriptionElderlyCare(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingSubscriptionElderlyCare not implemented")
}
func (UnimplementedPricingVNServer) GetPricingChildCare(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingChildCare not implemented")
}
func (UnimplementedPricingVNServer) GetPricingSubscriptionChildCare(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingSubscriptionChildCare not implemented")
}
func (UnimplementedPricingVNServer) GetPricingSubscriptionOfficeCleaning(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingSubscriptionOfficeCleaning not implemented")
}
func (UnimplementedPricingVNServer) APIGetPricingSubscriptionOfficeCleaning(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method APIGetPricingSubscriptionOfficeCleaning not implemented")
}
func (UnimplementedPricingVNServer) GetPricingWaterHeater(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingWaterHeater not implemented")
}
func (UnimplementedPricingVNServer) GetPricingCarpetCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingCarpetCleaning not implemented")
}
func (UnimplementedPricingVNServer) GetPricingHomeMoving(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingHomeMoving not implemented")
}
func (UnimplementedPricingVNServer) RePricingHomeMoving(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingHomeMoving not implemented")
}
func (UnimplementedPricingVNServer) APIGetPricingTaskDateOptions(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method APIGetPricingTaskDateOptions not implemented")
}
func (UnimplementedPricingVNServer) GetPricingAirConditionerV2(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingAirConditionerV2 not implemented")
}
func (UnimplementedPricingVNServer) GetPricingHousekeepingV2(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingHousekeepingV2 not implemented")
}
func (UnimplementedPricingVNServer) GetPricingIndustrialCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingIndustrialCleaning not implemented")
}
func (UnimplementedPricingVNServer) GetPricingBeautyCare(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingBeautyCare not implemented")
}
func (UnimplementedPricingVNServer) RePricingBeautyCare(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingBeautyCare not implemented")
}
func (UnimplementedPricingVNServer) GetPricingMakeup(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingMakeup not implemented")
}
func (UnimplementedPricingVNServer) GetPricingNail(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingNail not implemented")
}
func (UnimplementedPricingVNServer) GetPricingHairStyling(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingHairStyling not implemented")
}
func (UnimplementedPricingVNServer) GetPricingIroning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingIroning not implemented")
}
func (UnimplementedPricingVNServer) mustEmbedUnimplementedPricingVNServer() {}

// UnsafePricingVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PricingVNServer will
// result in compilation errors.
type UnsafePricingVNServer interface {
	mustEmbedUnimplementedPricingVNServer()
}

func RegisterPricingVNServer(s *grpc.Server, srv PricingVNServer) {
	s.RegisterService(&_PricingVN_serviceDesc, srv)
}

func _PricingVN_GetPricingWashingMachine_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingWashingMachine(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingWashingMachine",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingWashingMachine(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingAirConditioner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingAirConditioner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingAirConditioner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingAirConditioner(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingHomeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingHomeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingHomeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingHomeCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingSubscriptionRequest.PricingSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingSubscription(ctx, req.(*pricingSubscriptionRequest.PricingSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingDeepCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingDeepCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingDeepCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingDeepCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingHomeCooking_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingHomeCooking(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingHomeCooking",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingHomeCooking(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingLaundry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingLaundry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingLaundry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingLaundry(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_RePricingLaundry_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).RePricingLaundry(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/RePricingLaundry",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).RePricingLaundry(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingGroceryAssistant_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingGroceryAssistant(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingGroceryAssistant",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingGroceryAssistant(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingSofa_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingSofa(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingSofa",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingSofa(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingHousekeeping_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingHousekeeping(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingHousekeeping",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingHousekeeping(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingElderlyCare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingElderlyCare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingElderlyCare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingElderlyCare(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingPatientCare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingPatientCare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingPatientCare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingPatientCare(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingDisinfection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingDisinfection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingDisinfection",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingDisinfection(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingOfficeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingOfficeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingOfficeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingOfficeCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_APIGetPricingOfficeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).APIGetPricingOfficeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/APIGetPricingOfficeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).APIGetPricingOfficeCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_ReCalculatePricing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).ReCalculatePricing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/ReCalculatePricing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).ReCalculatePricing(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_ReCalculatePricingForIncreaseDuration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingIncreaseDurationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).ReCalculatePricingForIncreaseDuration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/ReCalculatePricingForIncreaseDuration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).ReCalculatePricingForIncreaseDuration(ctx, req.(*pricingrequest.PricingIncreaseDurationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingSubscriptionPatientCare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingSubscriptionRequest.PricingSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingSubscriptionPatientCare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingSubscriptionPatientCare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingSubscriptionPatientCare(ctx, req.(*pricingSubscriptionRequest.PricingSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingSubscriptionElderlyCare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingSubscriptionRequest.PricingSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingSubscriptionElderlyCare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingSubscriptionElderlyCare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingSubscriptionElderlyCare(ctx, req.(*pricingSubscriptionRequest.PricingSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingChildCare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingChildCare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingChildCare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingChildCare(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingSubscriptionChildCare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingSubscriptionRequest.PricingSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingSubscriptionChildCare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingSubscriptionChildCare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingSubscriptionChildCare(ctx, req.(*pricingSubscriptionRequest.PricingSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingSubscriptionOfficeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingSubscriptionRequest.PricingSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingSubscriptionOfficeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingSubscriptionOfficeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingSubscriptionOfficeCleaning(ctx, req.(*pricingSubscriptionRequest.PricingSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_APIGetPricingSubscriptionOfficeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingSubscriptionRequest.PricingSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).APIGetPricingSubscriptionOfficeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/APIGetPricingSubscriptionOfficeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).APIGetPricingSubscriptionOfficeCleaning(ctx, req.(*pricingSubscriptionRequest.PricingSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingWaterHeater_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingWaterHeater(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingWaterHeater",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingWaterHeater(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingCarpetCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingCarpetCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingCarpetCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingCarpetCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingHomeMoving_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingHomeMoving(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingHomeMoving",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingHomeMoving(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_RePricingHomeMoving_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).RePricingHomeMoving(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/RePricingHomeMoving",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).RePricingHomeMoving(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_APIGetPricingTaskDateOptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).APIGetPricingTaskDateOptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/APIGetPricingTaskDateOptions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).APIGetPricingTaskDateOptions(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingAirConditionerV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingAirConditionerV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingAirConditionerV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingAirConditionerV2(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingHousekeepingV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingHousekeepingV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingHousekeepingV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingHousekeepingV2(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingIndustrialCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingIndustrialCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingIndustrialCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingIndustrialCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingBeautyCare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingBeautyCare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingBeautyCare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingBeautyCare(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_RePricingBeautyCare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).RePricingBeautyCare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/RePricingBeautyCare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).RePricingBeautyCare(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingMakeup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingMakeup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingMakeup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingMakeup(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingNail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingNail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingNail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingNail(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingHairStyling_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingHairStyling(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingHairStyling",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingHairStyling(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingVN_GetPricingIroning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingVNServer).GetPricingIroning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingVN.PricingVN/GetPricingIroning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingVNServer).GetPricingIroning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PricingVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcPricingVN.PricingVN",
	HandlerType: (*PricingVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPricingWashingMachine",
			Handler:    _PricingVN_GetPricingWashingMachine_Handler,
		},
		{
			MethodName: "GetPricingAirConditioner",
			Handler:    _PricingVN_GetPricingAirConditioner_Handler,
		},
		{
			MethodName: "GetPricingHomeCleaning",
			Handler:    _PricingVN_GetPricingHomeCleaning_Handler,
		},
		{
			MethodName: "GetPricingSubscription",
			Handler:    _PricingVN_GetPricingSubscription_Handler,
		},
		{
			MethodName: "GetPricingDeepCleaning",
			Handler:    _PricingVN_GetPricingDeepCleaning_Handler,
		},
		{
			MethodName: "GetPricingHomeCooking",
			Handler:    _PricingVN_GetPricingHomeCooking_Handler,
		},
		{
			MethodName: "GetPricingLaundry",
			Handler:    _PricingVN_GetPricingLaundry_Handler,
		},
		{
			MethodName: "RePricingLaundry",
			Handler:    _PricingVN_RePricingLaundry_Handler,
		},
		{
			MethodName: "GetPricingGroceryAssistant",
			Handler:    _PricingVN_GetPricingGroceryAssistant_Handler,
		},
		{
			MethodName: "GetPricingSofa",
			Handler:    _PricingVN_GetPricingSofa_Handler,
		},
		{
			MethodName: "GetPricingHousekeeping",
			Handler:    _PricingVN_GetPricingHousekeeping_Handler,
		},
		{
			MethodName: "GetPricingElderlyCare",
			Handler:    _PricingVN_GetPricingElderlyCare_Handler,
		},
		{
			MethodName: "GetPricingPatientCare",
			Handler:    _PricingVN_GetPricingPatientCare_Handler,
		},
		{
			MethodName: "GetPricingDisinfection",
			Handler:    _PricingVN_GetPricingDisinfection_Handler,
		},
		{
			MethodName: "GetPricingOfficeCleaning",
			Handler:    _PricingVN_GetPricingOfficeCleaning_Handler,
		},
		{
			MethodName: "APIGetPricingOfficeCleaning",
			Handler:    _PricingVN_APIGetPricingOfficeCleaning_Handler,
		},
		{
			MethodName: "ReCalculatePricing",
			Handler:    _PricingVN_ReCalculatePricing_Handler,
		},
		{
			MethodName: "ReCalculatePricingForIncreaseDuration",
			Handler:    _PricingVN_ReCalculatePricingForIncreaseDuration_Handler,
		},
		{
			MethodName: "GetPricingSubscriptionPatientCare",
			Handler:    _PricingVN_GetPricingSubscriptionPatientCare_Handler,
		},
		{
			MethodName: "GetPricingSubscriptionElderlyCare",
			Handler:    _PricingVN_GetPricingSubscriptionElderlyCare_Handler,
		},
		{
			MethodName: "GetPricingChildCare",
			Handler:    _PricingVN_GetPricingChildCare_Handler,
		},
		{
			MethodName: "GetPricingSubscriptionChildCare",
			Handler:    _PricingVN_GetPricingSubscriptionChildCare_Handler,
		},
		{
			MethodName: "GetPricingSubscriptionOfficeCleaning",
			Handler:    _PricingVN_GetPricingSubscriptionOfficeCleaning_Handler,
		},
		{
			MethodName: "APIGetPricingSubscriptionOfficeCleaning",
			Handler:    _PricingVN_APIGetPricingSubscriptionOfficeCleaning_Handler,
		},
		{
			MethodName: "GetPricingWaterHeater",
			Handler:    _PricingVN_GetPricingWaterHeater_Handler,
		},
		{
			MethodName: "GetPricingCarpetCleaning",
			Handler:    _PricingVN_GetPricingCarpetCleaning_Handler,
		},
		{
			MethodName: "GetPricingHomeMoving",
			Handler:    _PricingVN_GetPricingHomeMoving_Handler,
		},
		{
			MethodName: "RePricingHomeMoving",
			Handler:    _PricingVN_RePricingHomeMoving_Handler,
		},
		{
			MethodName: "APIGetPricingTaskDateOptions",
			Handler:    _PricingVN_APIGetPricingTaskDateOptions_Handler,
		},
		{
			MethodName: "GetPricingAirConditionerV2",
			Handler:    _PricingVN_GetPricingAirConditionerV2_Handler,
		},
		{
			MethodName: "GetPricingHousekeepingV2",
			Handler:    _PricingVN_GetPricingHousekeepingV2_Handler,
		},
		{
			MethodName: "GetPricingIndustrialCleaning",
			Handler:    _PricingVN_GetPricingIndustrialCleaning_Handler,
		},
		{
			MethodName: "GetPricingBeautyCare",
			Handler:    _PricingVN_GetPricingBeautyCare_Handler,
		},
		{
			MethodName: "RePricingBeautyCare",
			Handler:    _PricingVN_RePricingBeautyCare_Handler,
		},
		{
			MethodName: "GetPricingMakeup",
			Handler:    _PricingVN_GetPricingMakeup_Handler,
		},
		{
			MethodName: "GetPricingNail",
			Handler:    _PricingVN_GetPricingNail_Handler,
		},
		{
			MethodName: "GetPricingHairStyling",
			Handler:    _PricingVN_GetPricingHairStyling_Handler,
		},
		{
			MethodName: "GetPricingIroning",
			Handler:    _PricingVN_GetPricingIroning_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pricingVN.proto",
}
