syntax = "proto3";
package grpcDataAccess;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcDataAccess";

import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/dataAccess/dataAccess.proto";
import "google/protobuf/empty.proto";

service DataAccess {
  rpc IsExistById (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc IsExistByQuery (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc CountByQuery (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc GetOneById (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc GetOneByQuery (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc GetOneByQueryMap (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc GetOneByQuerySort (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc GetAllByQuery (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc GetAllByQueryPaging (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc GetAllByQuerySort (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc GetAllByQueryPagingSort (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc GetAllByQueryLimitSort (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc GetAllByQuerySkipLimitSort (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc GetDistinctByQuery (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc Aggregate (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc InsertOne (dataAccess.DataAccessRequest) returns (google.protobuf.Empty) {}
  rpc InsertAll (dataAccess.DataAccessRequest) returns (google.protobuf.Empty) {}
  rpc UpdateOneById (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc UpdateOneByQuery (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc UpdateAllByQuery (dataAccess.DataAccessRequest) returns (dataAccess.DataAccessResponse) {}
  rpc UpsertOneById (dataAccess.DataAccessRequest) returns (google.protobuf.Empty) {}
  rpc UpsertOneByQuery (dataAccess.DataAccessRequest) returns (google.protobuf.Empty) {}
  rpc DeleteOneById (dataAccess.DataAccessRequest) returns (google.protobuf.Empty) {}
  rpc DeleteOneByQuery (dataAccess.DataAccessRequest) returns (google.protobuf.Empty) {}
  rpc DeleteAllByQuery (dataAccess.DataAccessRequest) returns (google.protobuf.Empty) {}
}