// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcDoneTaskMY

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	doneBookingRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/doneBookingRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// DoneTaskClient is the client API for DoneTask service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DoneTaskClient interface {
	PartnerDone(ctx context.Context, in *doneBookingRequest.DoneBookingRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type doneTaskClient struct {
	cc grpc.ClientConnInterface
}

func NewDoneTaskClient(cc grpc.ClientConnInterface) DoneTaskClient {
	return &doneTaskClient{cc}
}

func (c *doneTaskClient) PartnerDone(ctx context.Context, in *doneBookingRequest.DoneBookingRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcDoneTaskMY.DoneTask/PartnerDone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DoneTaskServer is the server API for DoneTask service.
// All implementations must embed UnimplementedDoneTaskServer
// for forward compatibility
type DoneTaskServer interface {
	PartnerDone(context.Context, *doneBookingRequest.DoneBookingRequest) (*empty.Empty, error)
	mustEmbedUnimplementedDoneTaskServer()
}

// UnimplementedDoneTaskServer must be embedded to have forward compatible implementations.
type UnimplementedDoneTaskServer struct {
}

func (UnimplementedDoneTaskServer) PartnerDone(context.Context, *doneBookingRequest.DoneBookingRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartnerDone not implemented")
}
func (UnimplementedDoneTaskServer) mustEmbedUnimplementedDoneTaskServer() {}

// UnsafeDoneTaskServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DoneTaskServer will
// result in compilation errors.
type UnsafeDoneTaskServer interface {
	mustEmbedUnimplementedDoneTaskServer()
}

func RegisterDoneTaskServer(s *grpc.Server, srv DoneTaskServer) {
	s.RegisterService(&_DoneTask_serviceDesc, srv)
}

func _DoneTask_PartnerDone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(doneBookingRequest.DoneBookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoneTaskServer).PartnerDone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDoneTaskMY.DoneTask/PartnerDone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoneTaskServer).PartnerDone(ctx, req.(*doneBookingRequest.DoneBookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _DoneTask_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcDoneTaskMY.DoneTask",
	HandlerType: (*DoneTaskServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PartnerDone",
			Handler:    _DoneTask_PartnerDone_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "doneTask.proto",
}
