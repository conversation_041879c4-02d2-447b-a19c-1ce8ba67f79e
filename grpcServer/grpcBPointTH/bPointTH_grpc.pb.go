// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcBPointTH

import (
	context "context"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// BPointTHClient is the client API for BPointTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BPointTHClient interface {
	AccumulatePoints(ctx context.Context, in *BPointRequest, opts ...grpc.CallOption) (*response.Response, error)
	TaskerAccumulatePoints(ctx context.Context, in *BPointRequest, opts ...grpc.CallOption) (*response.Response, error)
	AddUserPoint(ctx context.Context, in *BPointRequest, opts ...grpc.CallOption) (*response.Response, error)
}

type bPointTHClient struct {
	cc grpc.ClientConnInterface
}

func NewBPointTHClient(cc grpc.ClientConnInterface) BPointTHClient {
	return &bPointTHClient{cc}
}

func (c *bPointTHClient) AccumulatePoints(ctx context.Context, in *BPointRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcBPointTH.BPointTH/AccumulatePoints", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bPointTHClient) TaskerAccumulatePoints(ctx context.Context, in *BPointRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcBPointTH.BPointTH/TaskerAccumulatePoints", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bPointTHClient) AddUserPoint(ctx context.Context, in *BPointRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcBPointTH.BPointTH/AddUserPoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BPointTHServer is the server API for BPointTH service.
// All implementations must embed UnimplementedBPointTHServer
// for forward compatibility
type BPointTHServer interface {
	AccumulatePoints(context.Context, *BPointRequest) (*response.Response, error)
	TaskerAccumulatePoints(context.Context, *BPointRequest) (*response.Response, error)
	AddUserPoint(context.Context, *BPointRequest) (*response.Response, error)
	mustEmbedUnimplementedBPointTHServer()
}

// UnimplementedBPointTHServer must be embedded to have forward compatible implementations.
type UnimplementedBPointTHServer struct {
}

func (UnimplementedBPointTHServer) AccumulatePoints(context.Context, *BPointRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AccumulatePoints not implemented")
}
func (UnimplementedBPointTHServer) TaskerAccumulatePoints(context.Context, *BPointRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskerAccumulatePoints not implemented")
}
func (UnimplementedBPointTHServer) AddUserPoint(context.Context, *BPointRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddUserPoint not implemented")
}
func (UnimplementedBPointTHServer) mustEmbedUnimplementedBPointTHServer() {}

// UnsafeBPointTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BPointTHServer will
// result in compilation errors.
type UnsafeBPointTHServer interface {
	mustEmbedUnimplementedBPointTHServer()
}

func RegisterBPointTHServer(s *grpc.Server, srv BPointTHServer) {
	s.RegisterService(&_BPointTH_serviceDesc, srv)
}

func _BPointTH_AccumulatePoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BPointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BPointTHServer).AccumulatePoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBPointTH.BPointTH/AccumulatePoints",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BPointTHServer).AccumulatePoints(ctx, req.(*BPointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BPointTH_TaskerAccumulatePoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BPointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BPointTHServer).TaskerAccumulatePoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBPointTH.BPointTH/TaskerAccumulatePoints",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BPointTHServer).TaskerAccumulatePoints(ctx, req.(*BPointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BPointTH_AddUserPoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BPointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BPointTHServer).AddUserPoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBPointTH.BPointTH/AddUserPoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BPointTHServer).AddUserPoint(ctx, req.(*BPointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _BPointTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcBPointTH.BPointTH",
	HandlerType: (*BPointTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AccumulatePoints",
			Handler:    _BPointTH_AccumulatePoints_Handler,
		},
		{
			MethodName: "TaskerAccumulatePoints",
			Handler:    _BPointTH_TaskerAccumulatePoints_Handler,
		},
		{
			MethodName: "AddUserPoint",
			Handler:    _BPointTH_AddUserPoint_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "bPointTH.proto",
}
