syntax = "proto3";
package grpcAcceptTaskIndo;

option go_package = "gitlab.com/btaskee/go-accept-task-indo-v3/grpcAcceptTaskIndo";

import "google/protobuf/empty.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/acceptBookingRequest/acceptBookingRequest.proto";

service AcceptTaskIndo {
  rpc ChooseTasker (acceptBookingRequest.AcceptBookingRequest) returns (google.protobuf.Empty) {}
}