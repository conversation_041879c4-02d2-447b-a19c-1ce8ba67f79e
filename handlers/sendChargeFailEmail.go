package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-email-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	modelPointTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pointTransaction"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
* @Description: Migrate and send receipt email
* @CreatedAt: 04/09/2020
* @Author: linhnh
* @UpdatedAt: 24/02/2021
* @UpdatedBy: ngoctb3
 */
func SendChargeFailEmail(req *modelTask.Task) (*modelEmailResponse.EmailResponse, error) {
	defer local.Logger.Sync()

	// Check server config
	resErr := CheckEmailConfig()
	if resErr != nil {
		local.Logger.Error(resErr.Message)
		return resErr, nil
	}

	result := &modelEmailResponse.EmailResponse{}
	var err error
	if req != nil && req.XId != "" {
		var task *modelTask.Task
		globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], req.XId, bson.M{"_id": 1, "askerId": 1, "date": 1, "serviceText": 1, "cost": 1, "promotion": 1, "address": 1, "duration": 1, "detail": 1, "detailHostel": 1, "autoChooseTasker": 1, "requirements": 1, "tip": 1, "payment": 1, "acceptedTasker": 1, "isoCode": 1, "originCurrency": 1, "currency": 1}, &task)
		if task != nil {
			asker, _ := modelUser.GetOneById(local.ISO_CODE, task.AskerId, bson.M{"emails": 1, "ignoreEmails": 1, "language": 1, "name": 1, "avatar": 1, "referralCode": 1})
			if asker != nil && asker.Emails != nil && len(asker.Emails) > 0 && asker.Emails[0].Verified && (asker.IgnoreEmails == nil || !asker.IgnoreEmails.TaskReceipt) {
				globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], task.XId, bson.M{"$set": bson.M{"emailSentReceipt": asker.Emails[0].Address}})
				body, err := getChargeFailEmailContent(task, asker)
				if err != nil {
					local.Logger.Error(lib.ERROR_CONTENT_NIL,
						zap.Error(err),
					)
					return &modelEmailResponse.EmailResponse{
						StatusCode: http.StatusBadRequest,
						Message:    lib.ERROR_CONTENT_NIL,
					}, err
				}
				subject := getChargeFailEmailSubject(task, asker)
				emailSending := &modelEmailSending.EmailSending{
					From: "bTaskee Receipts <<EMAIL>>",
					To:   asker.Emails[0].Address,
					// Bcc:     []string{"<EMAIL>"},
					Subject: subject,
					Content: body,
					ReplyTo: "<EMAIL>",
				}
				return SendEmail(emailSending)
			}
		}
	} else {
		local.Logger.Warn(lib.ERROR_EMAIL_RECEIPT_TASK_EMPTY)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_EMAIL_RECEIPT_TASK_EMPTY,
		}, nil
	}
	return result, err
}

/*
* @Description: Get receipt email subject by asker language
* @CreatedAt: 04/09/2020
* @Author: linhnh
* @UpdatedAt: 07/09/2020
* @UpdatedBy: linhnh
 */
func getChargeFailEmailSubject(task *modelTask.Task, asker *modelUser.Users) string {
	lang := globalConstant.LANG_EN
	if asker != nil && asker.Language != "" {
		lang = asker.Language
	}
	key := lib.DATE_FORMAT_RECEIPT
	date := lib.TimeLocale(globalLib.ParseDateFromTimeStamp(task.Date, local.TimeZone).Format(key), lang)
	return localization.T(lang, "RECEIPT_EMAIL_SUBJECT", date)
}

/*
* @Description: Get receipt email content by asker language
* @CreatedAt: 04/09/2020
* @Author: linhnh
* @UpdatedAt: 07/09/2020
* @UpdatedBy: linhnh
 */
func getChargeFailEmailContent(task *modelTask.Task, asker *modelUser.Users) (string, error) {
	lang := globalConstant.LANG_EN
	if asker != nil && asker.Language != "" {
		lang = asker.Language
	}
	var serviceText string
	if task != nil && task.ServiceText != nil {
		serviceText = task.ServiceText.Vi
		switch lang {
		case globalConstant.LANG_EN:
			serviceText = task.ServiceText.En
		case globalConstant.LANG_KO:
			serviceText = task.ServiceText.Ko
		case globalConstant.LANG_TH:
			serviceText = task.ServiceText.Th
		}
	}
	// Set email data
	emailData := &model.EmailReceiptData{}

	// emailData.Hello = localization.T(lang, "RECEIPT_EMAIL_WELCOME")
	emailData.ChargeFailContent = localization.T(lang, "CHARGE_FAILED_EMAIL_CONTENT")
	emailData.ChargeFailNote = localization.T(lang, "CHARGE_FAILED_EMAIL_NOTE")
	emailData.PaymentTotalText = localization.T(lang, "RECEIPT_EMAIL_PAYMENT_TOTAL")
	emailData.AskerNameTitle = localization.T(lang, "CANCEL_EMAIL_ASKER_NAME_TITLE")
	emailData.PaymentPaidText = localization.T(lang, "CANCEL_EMAIL_PAYMENT_METHOD_TITLE")
	emailData.TaskDetailText = localization.T(lang, "RECEIPT_EMAIL_TASK_DETAIL")
	emailData.TaskerLabel = localization.T(lang, "RECEIPT_EMAIL_TASKER_LABEL")
	emailData.TaskTitle = localization.T(lang, "TASK_TITLE")
	emailData.TaskPlaceTitle = localization.T(lang, "CANCEL_EMAIL_TASK_PLACE_TITLE")
	emailData.TaskDateTitle = localization.T(lang, "CANCEL_EMAIL_TASK_DATE_TITLE")
	emailData.TaskServiceTitle = localization.T(lang, "CANCEL_EMAIL_SERVICE_TITLE")
	emailData.TaskDurationTitle = localization.T(lang, "CANCEL_EMAIL_DURATION_TITLE")
	emailData.TaskDurationAirConTitle = localization.T(lang, "RECEIPT_EMAIL_TASK_DURATION_AIR_CON_TITLE")
	emailData.TaskQuantityTitle = localization.T(lang, "QUANTITY_TITLE")
	emailData.TaskHostelDetailTitle = localization.T(lang, "RECEIPT_EMAIL_HOSTEL_DETAIL")
	emailData.PaymentDetailText = localization.T(lang, "RECEIPT_EMAIL_PAYMENT_DETAIL")
	emailData.PaymentTaskPriceText = localization.T(lang, "RECEIPT_EMAIL_PAYMENT_TASK_PRICE")
	emailData.PaymentChooseTaskerTitle = localization.T(lang, "RECEIPT_EMAIL_PAYMENT_CHOOSE_TASKER_PRICE")
	emailData.PaymentToolsReqTitle = localization.T(lang, "RECEIPT_EMAIL_PAYMENT_TOOLS_REQUIREMENT")
	emailData.TipsTitle = localization.T(lang, "RECEIPT_EMAIL_PAYMENT_TIPS")
	emailData.PromotionTitle = localization.T(lang, "EMAIL_PROMOTION_TITLE")
	emailData.SupportTitle = localization.T(lang, "EMAIL_SUPPORT_TEXT")
	emailData.LostAndFoundText = localization.T(lang, "EMAIL_LOST_AND_FOUND_TEXT")
	emailData.PropertyDamageText = localization.T(lang, "EMAIL_PROPERTY_DAMAGE_TEXT")
	emailData.OtherSupportText = localization.T(lang, "EMAIL_OTHER_SUPPORT")
	emailData.ReferralTitle = localization.T(lang, "EMAIL_REFERRAL_TITLE")
	emailData.FollowBtaskeeAt = localization.T(lang, "FOLLOW_BTASKEE_AT")
	emailData.IsAirConditioner = task.ServiceText.En == globalConstant.SERVICE_NAME_AIR_CONDITIONER
	emailData.IsHousekeeping = task.ServiceText.En == globalConstant.SERVICE_NAME_HOUSEKEEPING
	emailData.PaymentMethodCard = localization.T(lang, "BOOKING_PAYMENT_METHOD_CARD_2")
	emailData.AskerName = asker.Name

	var promotionDiscount float64
	if task.Promotion != nil && task.Promotion.DecreasedCost > 0 {
		promotionDiscount = task.Cost - task.Promotion.DecreasedCost
	}
	key := lib.DATE_FORMAT_RECEIPT
	if lang == globalConstant.LANG_VI {
		key = lib.DATE_FORMAT_RECEIPT
	}
	currency := task.Currency
	if task.OriginCurrency != nil && task.OriginCurrency.Code != "" {
		currency = task.OriginCurrency.Code
	}
	if currency == "" {
		currency = globalConstant.CURRENCY_VN
	}
	emailData.MoneyTotal = fmt.Sprintf("%s %s", globalLib.FormatMoney(task.Cost-promotionDiscount), currency)

	var tasker *modelUser.Users
	if task.AcceptedTasker != nil && len(task.AcceptedTasker) > 0 {
		tasker, _ = modelUser.GetOneById(local.ISO_CODE, task.AcceptedTasker[0].TaskerId, bson.M{"name": 1, "avatar": 1})
	}
	emailData.TaskerAvatar = lib.DEFAULT_AVATAR
	if tasker != nil && tasker.Avatar != "" && tasker.Avatar != "/avatars/avatarDefault.png" {
		emailData.TaskerAvatar = tasker.Avatar
	}
	emailData.TaskerName = localization.T(lang, "RECEIPT_EMAIL_TASKER_NAME_DEFAULT")
	if tasker != nil && tasker.Name != "" {
		emailData.TaskerName = tasker.Name
	}
	emailData.TaskPlace = task.Address
	key = lib.DATETIME_FORMAT_RECEIPT
	emailData.TaskTime = lib.TimeLocale(globalLib.ParseDateFromTimeStamp(task.Date, local.TimeZone).Format(key), lang)
	emailData.TaskName = strings.ToUpper(serviceText)

	taskDuration := int64(task.Duration)

	emailData.TaskDurationCount = taskDuration
	emailData.TaskDurationText = localization.T(lang, "CANCEL_EMAIL_DURATION", taskDuration)
	detailAC := []*model.EmailReceiptDetailAC{}
	for _, v := range task.Detail {
		dram := "HP"
		if lang == globalConstant.LANG_TH {
			dram = "BTU"
		}
		hp := fmt.Sprintf("%v - %v %s", v.Hp.From, v.Hp.To, dram)
		if v.Hp.From == 0 {
			hp = localization.T(lang, "CAPACITY_LESS_THAN", v.Hp.To, dram)
		} else if v.Hp.To == 0 {
			hp = localization.T(lang, "CAPACITY_GREATER_THAN", v.Hp.From, dram)
		}
		var acServices []string
		for _, s := range v.Services {
			srv := s.Text.Vi
			switch lang {
			case globalConstant.LANG_EN:
				srv = s.Text.En
			case globalConstant.LANG_KO:
				srv = s.Text.Ko
			case globalConstant.LANG_TH:
				srv = s.Text.Th
			}
			acServices = append(acServices, srv)
		}
		acType := v.Type.Text.Vi
		switch lang {
		case globalConstant.LANG_EN:
			acType = v.Type.Text.En
		case globalConstant.LANG_KO:
			acType = v.Type.Text.Ko
		case globalConstant.LANG_TH:
			acType = v.Type.Text.Th
		}
		detailACItem := &model.EmailReceiptDetailAC{
			TaskQuantityTitle: localization.T(lang, "QUANTITY_TITLE"),
			TaskTitle:         localization.T(lang, "TASK_TITLE"),
			Type:              fmt.Sprintf("%s - %s", acType, hp),
			Quantity:          strconv.Itoa(int(v.Quantity)),
			Services:          strings.Join(acServices, " ,"),
		}
		detailAC = append(detailAC, detailACItem)
	}
	emailData.DetailAC = detailAC

	if task.DetailHostel != nil && task.DetailHostel.Rooms != nil && len(task.DetailHostel.Rooms) > 0 {
		var numberOfRooms int
		var detailHousekeeping []*model.EmailReceiptDetailHousekeeping
		for _, v := range task.DetailHostel.Rooms {
			numberOfRooms++
			item := &model.EmailReceiptDetailHousekeeping{
				NameAndArea: fmt.Sprintf("%s - %.2f", v.Name, v.Area),
			}
			detailHousekeeping = append(detailHousekeeping, item)
		}
		emailData.DetailHousekeeping = detailHousekeeping
		emailData.TaskHostelSummaryText = localization.T(lang, "RECEIPT_EMAIL_HOSTEL_SUMMARY", numberOfRooms, task.DetailHostel.TotalArea)
	}

	var setting *modelSetting.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"costForChooseTasker": 1, "referralSetting": 1}, &setting)
	var costForChooseTasker float64
	if setting != nil {
		costForChooseTasker = setting.CostForChooseTasker
	}
	taskBasePrice := task.Cost
	if !task.AutoChooseTasker {
		taskBasePrice -= costForChooseTasker
	}
	var toolsRequirementCost float64
	if task.Requirements != nil && len(task.Requirements) > 0 {
		var toolsRequirement *modelService.ServiceTipRequirements
		for _, v := range task.Requirements {
			if v.Type == 3 {
				toolsRequirement = v
				break
			}
		}
		if toolsRequirement != nil && toolsRequirement.Cost > 0 {
			taskBasePrice -= toolsRequirement.Cost
			toolsRequirementCost = toolsRequirement.Cost
		}
	}
	if task.Tip > 0 {
		taskBasePrice -= task.Tip
	}
	emailData.TaskBasePrice = fmt.Sprintf("%s %s", globalLib.FormatMoney(taskBasePrice), currency)
	emailData.IsAutoChooseTasker = task.AutoChooseTasker
	emailData.CostForChooseTasker = fmt.Sprintf("%s %s", globalLib.FormatMoney(costForChooseTasker), currency)
	if toolsRequirementCost > 0 {
		emailData.ToolsRequirementCost = fmt.Sprintf("%s %s", globalLib.FormatMoney(toolsRequirementCost), currency)
	}
	if task.Tip > 0 {
		emailData.TaskTip = fmt.Sprintf("%s %s", globalLib.FormatMoney(task.Tip), currency)
	}
	if promotionDiscount > 0 {
		emailData.PromotionDiscount = fmt.Sprintf("%s %s", globalLib.FormatMoney(promotionDiscount), currency)
	}
	emailData.PaymentTotal = fmt.Sprintf("%s %s", globalLib.FormatMoney(task.Cost-promotionDiscount), currency)
	emailData.PaymentMethod = task.Payment.Method
	emailData.PromotionAccountText = localization.T(lang, "PROMOTION_ACCOUNT")
	emailData.PaymentMethod = globalLib.LocalizePaymentMethod(task.Payment.Method, lang)
	if task.Payment.Method == globalConstant.PAYMENT_METHOD_CARD {
		emailData.IsPaymentByCard = true
		emailData.PaymentCardNumberTitle = localization.T(lang, "PAY_BY_CARD")
		cardNumber := task.Payment.CardNumber
		if cardNumber == "" && task.Payment.CardInfo != nil {
			cardNumber = task.Payment.CardInfo.Number
		}
		emailData.PaymentCardNumber = cardNumber
		emailData.PaymentStatusTitle = localization.T(lang, "CARD_PAYMENT_STATUS")
		emailData.PaymentStatus = localization.T(lang, "CARD_PAYMENT_STATUS_FAILED")
	} else if task.Payment.Method == globalConstant.PAYMENT_METHOD_BANK_TRANSFER {
		emailData.IsPaymentByTransfer = true
		emailData.PaymentBankNameTitle = localization.T(lang, "BANK_TRANSFER_METHOD")
		emailData.PaymentBankName = task.Payment.Bank
		emailData.PaymentStatusTitle = localization.T(lang, "CARD_PAYMENT_STATUS")
		emailData.PaymentStatus = localization.T(lang, "CARD_PAYMENT_STATUS_FAILED")
	}

	emailData.Referral = strings.ToUpper(asker.ReferralCode)
	emailData.Language = lang
	emailData.Currency = currency

	value, currencyCode := getReferralValueAndCurrency(task.IsoCode, currency, setting)
	emailData.ReferralText = localization.T(lang, "EMAIL_REFERRAL_TEXT", globalLib.FormatMoney(value), currencyCode, globalLib.FormatMoney(value), currencyCode)

	var pointTransaction *modelPointTransaction.PointTransaction
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], bson.M{"source.taskId": task.XId}, bson.M{"point": 1}, &pointTransaction)
	if pointTransaction != nil {
		emailData.Bpoint = localization.T(lang, "BPOINT_TEXT", pointTransaction.Point)
	}
	// emailData.VerifyEmailContent = localization.T(lang, "VERIFY_EMAIL_CONTENT_5")

	// Parse email template
	emailTempalte := fmt.Sprintf("%s/chargeFailEmail.html", cfg.EmailTemplateURL)

	// Convert struct to map to use function updateInfo
	emailDataJson, _ := json.Marshal(emailData)
	emailDataMap := map[string]interface{}{}
	json.Unmarshal(emailDataJson, &emailDataMap)

	emailDataMap = globalLib.UpdateBtaskeeInfoInEmailByIsoCode(emailDataMap, task.IsoCode)
	body, err := ParseTemplate(emailTempalte, emailDataMap)

	// NOTE: Test write file with webhook-service "./test.sh 4 1"
	if err != nil {
		return "", err
	}
	return body, nil
}
