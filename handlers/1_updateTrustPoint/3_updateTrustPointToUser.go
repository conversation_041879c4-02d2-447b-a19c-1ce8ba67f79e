package updateTrustPoint

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTrustPointHistory "gitlab.com/btaskee/go-services-model-v2/grpcmodel/trustPointHistory"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

// Group list task by userId and recalculate numberPoint after each task
func updateTrustPointToUser(arrayTasksByUserId map[string]*ModelUserTrustPointByTask) {
	now := globalLib.GetCurrentTime(local.TimeZone)
	for userId := range arrayTasksByUserId {
		asker := getUser(userId)
		if asker == nil {
			continue
		}
		trustPointHistoryId := ""
		userTrustPoint := DEFAULT_TRUST_POINT
		// trustPoint not exists
		if asker.TrustPoint == nil {
			trustPointHistoryId = initTrustPointHistory(asker)
		} else if asker.TrustPoint != nil && asker.TrustPoint.Point >= 0 {
			//get old trust point from user
			userTrustPoint = asker.TrustPoint.Point
			// get trust point history
			objTrustPointHistory := getTrustPointHistory(userId)
			//make sure TrustPointHistory exist
			if objTrustPointHistory == nil {
				trustPointHistoryId = initTrustPointHistory(asker)
			} else {
				trustPointHistoryId = objTrustPointHistory.XId
			}
		}
		// numberPoint == 0 not action
		if arrayTasksByUserId[userId].NumberPoint != 0 {
			// check limit point
			newTrustPoint := checkLimitPoint(userTrustPoint + arrayTasksByUserId[userId].NumberPoint)
			// object trust point when update for user
			objTrustPoint := &modelUser.UsersTrustPoint{
				Point: int32(newTrustPoint),
				Rated: calculateAvg(newTrustPoint),
			}
			// send notification
			// sendNotificationWaring(userId, newTrustPoint);
			// update trustPoint to users
			modelUser.UpdateOneById(local.ISO_CODE, userId, bson.M{"$set": bson.M{"trustPoint": objTrustPoint}})
			// update TrustPointHistory
			var data []*modelTrustPointHistory.TrustPointHistoryHistoryData
			for _, task := range arrayTasksByUserId[userId].Task {
				// get reason when cancel
				reason := ""
				if task.CancellationReason != "" {
					reason = task.CancellationReason
				}
				if task.CancellationText != "" {
					reason = task.CancellationText
				}
				data = append(data, &modelTrustPointHistory.TrustPointHistoryHistoryData{
					TaskId:    task.XId,
					Reason:    reason,
					Status:    task.Status,
					CreatedAt: task.CreatedAt,
					UpdatedAt: task.UpdatedAt,
				})
			}

			history := &modelTrustPointHistory.TrustPointHistoryHistory{
				OldTrustPoint:   userTrustPoint,
				NewTrustPoint:   newTrustPoint,
				TotalTrustPoint: arrayTasksByUserId[userId].NumberPoint,
				CreatedAt:       globalLib.GetCurrentTimestamp(local.TimeZone),
				Data:            data,
			}

			// Update TrustPointHistory
			if trustPointHistoryId != "" {
				globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TRUST_POINT_HISTORY[local.ISO_CODE],
					trustPointHistoryId,
					bson.M{
						"$push": bson.M{"history": history},
						"$set":  bson.M{"updatedAt": now},
					},
				)

			}

		}
	}
}

func getUser(userId string) *modelUser.Users {
	asker, _ := modelUser.GetOneById(local.ISO_CODE, userId, bson.M{"trustPoint": 1})
	return asker
}

/*
RULE trustPoint
80 -> 100 : GOOD
65 -> 79: MEDIUM
<65 : BAD
*/
func calculateAvg(trustPoint int32) string {
	avg := ""
	if trustPoint >= 80 && trustPoint <= 100 {
		avg = "GOOD"
	} else if trustPoint >= 65 && trustPoint < 80 {
		avg = "MEDIUM"
	} else {
		avg = "BAD"
	}
	return avg
}

func checkLimitPoint(newTrustPoint int32) int32 {
	if newTrustPoint >= MAX_POINT {
		newTrustPoint = MAX_POINT
	} else if newTrustPoint <= MIN_POINT {
		newTrustPoint = MIN_POINT
	}
	return newTrustPoint
}

func getTrustPointHistory(userId string) *modelTrustPointHistory.TrustPointHistory {
	var objTrustPointHistory *modelTrustPointHistory.TrustPointHistory
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TRUST_POINT_HISTORY[local.ISO_CODE], bson.M{"userId": userId}, bson.M{"_id": 1}, &objTrustPointHistory)
	return objTrustPointHistory
}
