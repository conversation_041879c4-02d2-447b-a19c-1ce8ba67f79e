/*
 * @File: helpers.go
 * @Description: Helper functions for service
 * @CreatedAt: 21/08/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
package lib

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/config"
	libUser "gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/lib/user"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPushNotificationVN"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcWebsocket"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccessV2"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalRepo"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelServiceChannel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/serviceChannel"
	modelSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelTaskMetadata "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskMetadata"
	modelTaskerBNPL "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerBNPLProcess"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

var cfg = config.GetConfig()

/*
 * @Description: Send notification to tasker when have new task
 * @CreatedAt: 21/08/2020
 * @Author: linhnh
 * @UpdatedAt: 01/12/2020
 * @UpdatedBy: vinhnt
 */
func SendNotificationOfNewTask(serviceObject *modelService.Service, taskId string, favouriteTasker []string, isoCode string) {
	var settings *modelSetting.Settings
	err := globalRepo.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"useDistanceToSendNotification": 1, "servicesApplyDistance": 1, "sendNotificationConfig": 1}, &settings)
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[ERROR Push New Task v2]: func SendNotificationOfNewTask, settings nil: %v", err))
	}
	// Apply fav.Tasker for cleaning service and at least 3 fav.Taskers.
	if len(favouriteTasker) > 0 && settings != nil && len(settings.ServicesApplyDistance) > 0 {
		var isSendToFav bool
		for _, v := range settings.ServicesApplyDistance {
			if v == serviceObject.Text.En {
				isSendToFav = true
				break
			}
		}
		if isSendToFav {
			SendNotificationToFavTaskers(serviceObject, taskId, isoCode)
			return
		}
	}
	if settings != nil && settings.UseDistanceToSendNotification {
		SendNewTaskNotificationTopTasker(serviceObject, taskId, isoCode)
	} else {
		SendNotificationNormal(serviceObject, taskId)
	}
}

/*
 * @Description: Send notification to top 10 tasker when have new task, priority by score, district, favourite tasker
 * @CreatedAt: 21/08/2020
 * @Author: linhnh
 * @UpdatedAt: 01/12/2020
 * @UpdatedBy: vinhnt
 */
func SendNewTaskNotificationTopTasker(serviceObject *modelService.Service, taskId string, isoCode string) {
	/*
	   - Base on task.taskPlace find the tasker have these conditions:
	   1. Active tasker
	   2. In service channel
	   3. Have workingPlaces in district.
	   4. Sort by score and get top x (from settings)
	*/
	var settings *modelSetting.Settings
	err := globalRepo.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"topTaskersPriorityScore": 1, "citiesApplyDistance": 1, "timeInBetweenTask": 1, "limitNumberAcceptTaskInDay": 1, "servicesApplyDistance": 1, "limitNumberAcceptTaskInDayRush": 1, "sendNotificationConfig": 1}, &settings)
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[ERROR Push New Task v2]: func SendNewTaskNotificationTopTasker, settings nil: %v", err))
	}
	var task *modelTask.Task
	err = globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskId, IMPORTANT_FIELDS_OF_TASK, &task)
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[ERROR Push New Task v2]: func SendNewTaskNotificationTopTasker, task nil: %v", err))
		return
	}
	// Check for service and city
	if settings != nil && (len(settings.ServicesApplyDistance) > 0 || len(settings.CitiesApplyDistance) > 0) {
		isSendNormal := true
		for _, v := range settings.ServicesApplyDistance {
			if v == serviceObject.Text.En {
				isSendNormal = false
				break
			}
		}
		if isSendNormal {
			for _, v := range settings.CitiesApplyDistance {
				if v == task.TaskPlace.City {
					isSendNormal = false
					break
				}
			}
		}
		if isSendNormal {
			SendNotificationNormal(serviceObject, taskId)
			return
		}
	}
	var serviceChannel *modelServiceChannel.ServiceChannel
	globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": serviceObject.XId}, bson.M{"taskerList": 1}, &serviceChannel)

	asker, err := libUser.GetOneById(task.AskerId, bson.M{"favouriteTasker": 1})
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[ERROR Push New Task v2]: func SendNewTaskNotificationTopTasker, asker nil: %v", err))
	}
	var taskerList []string
	if serviceChannel != nil {
		taskerList = CopyArrayString(serviceChannel.TaskerList)
		taskerList = globalLib.RemoveTaskersFromList(taskerList, task.BlackList)
	}

	var favAndViewedTaskers []string
	if asker != nil && len(asker.FavouriteTasker) > 0 {
		favAndViewedTaskers = globalLib.IntersectionArray(task.ViewedTaskers, asker.FavouriteTasker)
		taskerList = globalLib.RemoveTaskersFromList(taskerList, favAndViewedTaskers)
	}
	// Remove busy and inactive Taskers
	busyTaskers := GetBusyTaskersForTask(serviceObject.XId, task, settings)
	inactiveTaskers := GetInactiveTaskerInWork(taskerList)
	taskerList = globalLib.RemoveTaskersFromList(taskerList, busyTaskers)
	taskerList = globalLib.RemoveTaskersFromList(taskerList, inactiveTaskers)
	// Remove taskers in excludedTaskers
	excludedTaskers := GetTaskMetadata(task.XId).GetExcludedTaskers()
	taskerList = globalLib.RemoveTaskersFromList(taskerList, excludedTaskers)
	// NOTE: Because when opening services in a new city, tasks with the payment method 'cash' will not be accepted by any tasker,
	// we will allow taskers using BNPL to take on cash tasks.
	// Remove Tasker BNPL
	// taskerHasBNPL := GetListTaskerHasBNPL(task)
	// taskerList = globalLib.RemoveTaskersFromList(taskerList, taskerHasBNPL)

	// Find Taskers
	findUserTaskerCondition := bson.M{
		"_id":                    bson.M{"$in": taskerList},
		"type":                   globalConstant.USER_TYPE_TASKER,
		"status":                 globalConstant.USER_STATUS_ACTIVE,
		"workingPlaces.country":  task.TaskPlace.Country,
		"workingPlaces.city":     task.TaskPlace.City,
		"workingPlaces.district": task.TaskPlace.District,
	}
	// Premium task need premium tasker
	if task.IsPremium {
		findUserTaskerCondition["isPremiumTasker"] = true
	}
	var topTaskersPrioritize int64 = 10
	if settings != nil && settings.TopTaskersPriorityScore > 0 {
		topTaskersPrioritize = int64(settings.TopTaskersPriorityScore)
	}
	taskers, _ := libUser.GetAll(findUserTaskerCondition, bson.M{"language": 1, "company": 1}, &globalDataAccessV2.QueryOptions{Limit: topTaskersPrioritize, Sort: bson.M{"score": -1}})
	if taskers != nil && len(taskers) < int(topTaskersPrioritize) {
		// Top Taskers prioritize are not enough, send to all Taskers in district
		SendNewTaskNotificationToDistrict(serviceObject, taskId)
		return
	}

	// Get male tasker for deepcleaning service only
	if globalLib.IsDeepCleaningServiceByKeyName(task.ServiceName) {
		maleTasker := GetMaleTasker(serviceChannel, asker, task, settings, busyTaskers, excludedTaskers)
		if len(maleTasker) > 0 {
			taskers = append(taskers, maleTasker...)
		}
	}

	// Add fav.Taskers were sent to next sending list
	if asker != nil && len(asker.FavouriteTasker) > 0 {
		findFavTaskerCondition := bson.M{"_id": bson.M{"$in": favAndViewedTaskers}}
		if task.IsPremium {
			findFavTaskerCondition["isPremiumTasker"] = true
		}
		favTaskers, _ := libUser.GetAll(findFavTaskerCondition, bson.M{"language": 1, "company": 1})
		if len(favTaskers) > 0 {
			taskers = append(taskers, favTaskers...)
		}
	}
	// Get notification data
	taskerIds, listOfTaskerId, listOfMessage := GetPushNotificationData(task, serviceObject, taskers)
	// Update viewedTaskers

	// globalRepo.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskId, bson.M{"$set": bson.M{"viewedTaskers": taskerIds, "visibility": 2}})
	UpdateTask(taskId, globalConstant.CHANGES_HISTORY_KEY_RESEND_FAV_TOPTASKER, taskerIds, 2)
	// go func() {
	// 	callResendTask(taskId)
	// }()
	title := &modelService.ServiceText{
		Vi: localization.T("vi", "NOTIFICATION_NEW_TASK"),
		En: localization.T("en", "NOTIFICATION_NEW_TASK"),
		Ko: localization.T("ko", "NOTIFICATION_NEW_TASK"),
		Th: localization.T("th", "NOTIFICATION_NEW_TASK"),
	}
	mapDistrict := MapDistrictText(task.TaskPlace)
	body := &modelService.ServiceText{
		Vi: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("vi", serviceObject.Text), mapDistrict.Vi),
		En: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("en", serviceObject.Text), mapDistrict.En),
		Ko: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("ko", serviceObject.Text), mapDistrict.Ko),
		Th: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("th", serviceObject.Text), mapDistrict.Th),
		Id: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("id", serviceObject.Text), mapDistrict.Id),
	}
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       0,
		TaskId:     taskId,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
	}
	SendNotification(listOfMessage, listOfTaskerId, title, body, payload, "", settings)

	// Send socket new task
	SendSocket(task, taskerIds)
}

/*
 * @Description: Send notification to tasker when have new task
 * @CreatedAt: 21/08/2020
 * @Author: linhnh
 * @UpdatedAt: 01/12/2020
 * @UpdatedBy: vinhnt
 */
func SendNotificationNormal(serviceObject *modelService.Service, taskId string) {
	var task *modelTask.Task
	err := globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskId, IMPORTANT_FIELDS_OF_TASK, &task)
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[ERROR Push New Task v2]: func SendNotificationNormal, task nil: %v", err))
		return
	}
	var serviceChannel *modelServiceChannel.ServiceChannel
	globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": serviceObject.XId}, bson.M{"taskerList": 1}, &serviceChannel)
	var settings *modelSetting.Settings
	err = globalRepo.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"skipSendTaskCity": 1, "timeInBetweenTask": 1, "limitNumberAcceptTaskInDay": 1, "limitNumberAcceptTaskInDayRush": 1, "sendNotificationConfig": 1}, &settings)
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[ERROR Push New Task v2]: func SendNotificationNormal, settings nil: %v", err))
	}
	// Check for service and city about skiping send task to city => send to district only
	if settings != nil && settings.SkipSendTaskCity != nil && len(settings.SkipSendTaskCity) > 0 {
		var isSkip bool
		for _, v := range settings.SkipSendTaskCity {
			if v.ServiceId == task.ServiceId {
				var isCityContain bool
				for _, c := range v.City {
					if c == task.TaskPlace.City {
						isCityContain = true
						break
					}
				}
				if isCityContain {
					isSkip = true
				}
				break
			}
		}
		if isSkip {
			sendNewTaskNotificationToDistrictOnly(serviceObject, settings, task, serviceChannel)
			return
		}
	}
	// Remove Taskers in blacklist from push notifications list
	var taskerList []string
	if serviceChannel != nil {
		taskerList = CopyArrayString(serviceChannel.TaskerList)
		taskerList = globalLib.RemoveTaskersFromList(taskerList, task.BlackList)
	}
	busyTaskers := GetBusyTaskersForTask(serviceObject.XId, task, settings)
	taskerList = globalLib.RemoveTaskersFromList(taskerList, busyTaskers)
	// Remove taskers in excludedTaskers
	excludedTaskers := GetTaskMetadata(task.XId).GetExcludedTaskers()
	taskerList = globalLib.RemoveTaskersFromList(taskerList, excludedTaskers)
	// NOTE: Because when opening services in a new city, tasks with the payment method 'cash' will not be accepted by any tasker,
	// we will allow taskers using BNPL to take on cash tasks.
	// Remove Tasker BNPL
	// taskerHasBNPL := GetListTaskerHasBNPL(task)
	// taskerList = globalLib.RemoveTaskersFromList(taskerList, taskerHasBNPL)

	// use service text instead of task description
	findTaskerCondition := bson.M{
		"_id":                   bson.M{"$in": taskerList},
		"type":                  globalConstant.USER_TYPE_TASKER,
		"status":                globalConstant.USER_STATUS_ACTIVE,
		"workingPlaces.country": task.TaskPlace.Country,
		"workingPlaces.city":    task.TaskPlace.City,
	}
	if task.IsPremium {
		findTaskerCondition["isPremiumTasker"] = true
	}
	users, _ := libUser.GetAll(findTaskerCondition, bson.M{"language": 1, "company": 1})
	// Get notification data
	taskerIds, lstOfTaskers, lstOfNotifications := GetPushNotificationDataForAllCity(task, serviceObject, users)
	// Update viewedTaskers
	// globalRepo.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskId, bson.M{"$set": bson.M{"viewedTaskers": taskerIds, "visibility": 4}})
	UpdateTask(taskId, globalConstant.CHANGES_HISTORY_KEY_RESEND_CITY, taskerIds, 4)
	// go func() {
	// 	callResendTask(taskId)
	// }()
	title := &modelService.ServiceText{
		Vi: localization.T("vi", "NOTIFICATION_NEW_TASK"),
		En: localization.T("en", "NOTIFICATION_NEW_TASK"),
		Ko: localization.T("ko", "NOTIFICATION_NEW_TASK"),
		Th: localization.T("th", "NOTIFICATION_NEW_TASK"),
	}
	mapDistrict := MapDistrictText(task.TaskPlace)
	body := &modelService.ServiceText{
		Vi: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("vi", serviceObject.Text), mapDistrict.Vi),
		En: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("en", serviceObject.Text), mapDistrict.En),
		Ko: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("ko", serviceObject.Text), mapDistrict.Ko),
		Th: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("th", serviceObject.Text), mapDistrict.Th),
		Id: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("id", serviceObject.Text), mapDistrict.Id),
	}
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       0,
		TaskId:     taskId,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
	}
	SendNotification(lstOfNotifications, lstOfTaskers, title, body, payload, "", settings)

	// Send socket new task
	SendSocket(task, taskerIds)
}

/*
 * @Description: Send notification to tasker in task.District when have new task
 * @CreatedAt: 21/08/2020
 * @Author: linhnh
 * @UpdatedAt: 01/12/2020
 * @UpdatedBy: vinhnt
 */
func SendNewTaskNotificationToDistrict(serviceObject *modelService.Service, taskId string) {
	/*
	   - Base on task.taskPlace find the tasker have these conditions:
	   1. Active tasker
	   2. In service channel
	   3. Have workingPlaces in district.
	*/
	var task *modelTask.Task
	err := globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskId, IMPORTANT_FIELDS_OF_TASK, &task)
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[ERROR Push New Task v2]: func SendNewTaskNotificationToDistrict, task nil: %v", err))
		return
	}
	var serviceChannel *modelServiceChannel.ServiceChannel
	globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": serviceObject.XId}, bson.M{"taskerList": 1}, &serviceChannel)
	asker, err := libUser.GetOneById(task.AskerId, bson.M{"favouriteTasker": 1})
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[ERROR Push New Task v2]: func SendNewTaskNotificationToDistrict, asker nil: %v", err))
		return
	}
	var taskerList []string
	if serviceChannel != nil {
		taskerList = CopyArrayString(serviceChannel.TaskerList)
		taskerList = globalLib.RemoveTaskersFromList(taskerList, task.BlackList)
	}
	var favAndViewedTaskers []string
	if asker != nil && len(asker.FavouriteTasker) > 0 {
		favAndViewedTaskers = globalLib.IntersectionArray(task.ViewedTaskers, asker.FavouriteTasker)
		taskerList = globalLib.RemoveTaskersFromList(taskerList, favAndViewedTaskers)
	}
	var settings *modelSetting.Settings
	err = globalRepo.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"topTaskersPriorityScore": 1, "citiesApplyDistance": 1, "timeInBetweenTask": 1, "limitNumberAcceptTaskInDay": 1, "servicesApplyDistance": 1, "limitNumberAcceptTaskInDayRush": 1, "sendNotificationConfig": 1}, &settings)
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[ERROR Push New Task v2]: func SendNewTaskNotificationToDistrict, settings nil: %v", err))
	}
	busyTaskers := GetBusyTaskersForTask(serviceObject.XId, task, settings)
	taskerList = globalLib.RemoveTaskersFromList(taskerList, busyTaskers)
	// Remove taskers in excludedTaskers
	excludedTaskers := GetTaskMetadata(task.XId).GetExcludedTaskers()
	taskerList = globalLib.RemoveTaskersFromList(taskerList, excludedTaskers)
	// NOTE: Because when opening services in a new city, tasks with the payment method 'cash' will not be accepted by any tasker,
	// we will allow taskers using BNPL to take on cash tasks.
	// Remove Tasker BNPL
	// taskerHasBNPL := GetListTaskerHasBNPL(task)
	// taskerList = globalLib.RemoveTaskersFromList(taskerList, taskerHasBNPL)

	// Find Taskers
	findTaskerCondition := bson.M{
		"_id":                    bson.M{"$in": taskerList},
		"type":                   globalConstant.USER_TYPE_TASKER,
		"status":                 globalConstant.USER_STATUS_ACTIVE,
		"workingPlaces.country":  task.TaskPlace.Country,
		"workingPlaces.city":     task.TaskPlace.City,
		"workingPlaces.district": task.TaskPlace.District,
	}
	if task.IsPremium {
		findTaskerCondition["isPremiumTasker"] = true
	}
	taskers, _ := libUser.GetAll(findTaskerCondition, bson.M{"language": 1, "company": 1})
	if asker != nil && len(asker.FavouriteTasker) > 0 {
		findFavTaskerCondition := bson.M{"_id": bson.M{"$in": favAndViewedTaskers}}
		if task.IsPremium {
			findFavTaskerCondition["isPremiumTasker"] = true
		}
		favTaskers, _ := libUser.GetAll(findFavTaskerCondition, bson.M{"language": 1, "company": 1})
		if len(favTaskers) > 0 {
			taskers = append(taskers, favTaskers...)
		}
	}
	if len(taskers) == 0 {
		SendNotificationNormal(serviceObject, taskId)
		return
	}
	// Get notification data
	taskerIds, listOfTaskerId, listOfMessage := GetPushNotificationData(task, serviceObject, taskers)
	// Update viewedTasker
	// globalRepo.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskId, bson.M{"$set": bson.M{"viewedTaskers": taskerIds, "visibility": 3}})
	UpdateTask(taskId, globalConstant.CHANGES_HISTORY_KEY_RESEND_FAV_DISTRICT, taskerIds, 3)
	title := &modelService.ServiceText{
		Vi: localization.T("vi", "NOTIFICATION_NEW_TASK"),
		En: localization.T("en", "NOTIFICATION_NEW_TASK"),
		Ko: localization.T("ko", "NOTIFICATION_NEW_TASK"),
		Th: localization.T("th", "NOTIFICATION_NEW_TASK"),
	}
	mapDistrict := MapDistrictText(task.TaskPlace)
	body := &modelService.ServiceText{
		Vi: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("vi", serviceObject.Text), mapDistrict.Vi),
		En: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("en", serviceObject.Text), mapDistrict.En),
		Ko: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("ko", serviceObject.Text), mapDistrict.Ko),
		Th: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("th", serviceObject.Text), mapDistrict.Th),
		Id: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("id", serviceObject.Text), mapDistrict.Id),
	}
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       0,
		TaskId:     taskId,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
	}
	SendNotification(listOfMessage, listOfTaskerId, title, body, payload, "", settings)

	// Send socket new task
	SendSocket(task, taskerIds)
}

/*
 * @Description: Send notification to tasker in task city when have new task
 * @CreatedAt: 20/07/2022
 * @Author: ngoctb
 * @UpdatedAt:
 * @UpdatedBy:
 */
func SendNewTaskNotificationToCity(serviceObject *modelService.Service, taskId string) {
	/*
	   - Base on task.taskPlace find the tasker have these conditions:
	   1. Active tasker
	   2. In service channel
	   3. Have workingPlaces in city (include favTasker).
	*/
	var task *modelTask.Task
	err := globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskId, IMPORTANT_FIELDS_OF_TASK, &task)
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[ERROR Push New Task v2]: func SendNewTaskNotificationToCity, task nil: %v", err))
		return
	}
	var serviceChannel *modelServiceChannel.ServiceChannel
	globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": serviceObject.XId}, bson.M{"taskerList": 1}, &serviceChannel)
	var taskerList []string
	if serviceChannel != nil {
		taskerList = CopyArrayString(serviceChannel.TaskerList)
		taskerList = globalLib.RemoveTaskersFromList(taskerList, task.BlackList)
	}
	var settings *modelSetting.Settings
	err = globalRepo.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"topTaskersPriorityScore": 1, "citiesApplyDistance": 1, "timeInBetweenTask": 1, "limitNumberAcceptTaskInDay": 1, "servicesApplyDistance": 1, "limitNumberAcceptTaskInDayRush": 1, "sendNotificationConfig": 1}, &settings)
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[ERROR Push New Task v2]: func SendNewTaskNotificationToCity, settings nil: %v", err))
	}
	busyTaskers := GetBusyTaskersForTask(serviceObject.XId, task, settings)
	taskerList = globalLib.RemoveTaskersFromList(taskerList, busyTaskers)
	// Remove taskers in excludedTaskers
	excludedTaskers := GetTaskMetadata(task.XId).GetExcludedTaskers()
	taskerList = globalLib.RemoveTaskersFromList(taskerList, excludedTaskers)
	// NOTE: Because when opening services in a new city, tasks with the payment method 'cash' will not be accepted by any tasker,
	// we will allow taskers using BNPL to take on cash tasks.
	// Remove Tasker BNPL
	// taskerHasBNPL := GetListTaskerHasBNPL(task)
	// taskerList = globalLib.RemoveTaskersFromList(taskerList, taskerHasBNPL)

	// Find Taskers
	findTaskerCondition := bson.M{
		"_id":                   bson.M{"$in": taskerList},
		"type":                  globalConstant.USER_TYPE_TASKER,
		"status":                globalConstant.USER_STATUS_ACTIVE,
		"workingPlaces.country": task.TaskPlace.Country,
		"workingPlaces.city":    task.TaskPlace.City,
	}
	if task.IsPremium {
		findTaskerCondition["isPremiumTasker"] = true
	}
	taskers, _ := libUser.GetAll(findTaskerCondition, bson.M{"language": 1, "company": 1})
	if len(taskers) == 0 {
		return
	}
	// Get notification data
	taskerIds, listOfTaskerId, listOfMessage := GetPushNotificationDataForAllCity(task, serviceObject, taskers)
	// Update viewedTasker
	// globalRepo.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskId, bson.M{"$set": bson.M{"viewedTaskers": taskerIds, "visibility": 4}})
	UpdateTask(taskId, globalConstant.CHANGES_HISTORY_KEY_RESEND_CITY, taskerIds, 4)
	title := &modelService.ServiceText{
		Vi: localization.T("vi", "NOTIFICATION_NEW_TASK"),
		En: localization.T("en", "NOTIFICATION_NEW_TASK"),
		Ko: localization.T("ko", "NOTIFICATION_NEW_TASK"),
		Th: localization.T("th", "NOTIFICATION_NEW_TASK"),
	}
	mapDistrict := MapDistrictText(task.TaskPlace)
	body := &modelService.ServiceText{
		Vi: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("vi", serviceObject.Text), mapDistrict.Vi),
		En: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("en", serviceObject.Text), mapDistrict.En),
		Ko: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("ko", serviceObject.Text), mapDistrict.Ko),
		Th: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("th", serviceObject.Text), mapDistrict.Th),
		Id: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("id", serviceObject.Text), mapDistrict.Id),
	}
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       0,
		TaskId:     taskId,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
	}
	SendNotification(listOfMessage, listOfTaskerId, title, body, payload, "", settings)

	SendSocket(task, taskerIds)
}

// =================================
/*
 * @Description: Send notification to asker.favouriteTaskers in task.District when have new task
 * @CreatedAt: 21/08/2020
 * @Author: linhnh
 * @UpdatedAt: 01/12/2020
 * @UpdatedBy: vinhnt
 */
func SendNotificationToFavTaskers(serviceObject *modelService.Service, taskId string, isoCode string) {
	var serviceChannel *modelServiceChannel.ServiceChannel
	globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": serviceObject.XId}, bson.M{"taskerList": 1}, &serviceChannel)
	var settings *modelSetting.Settings
	err := globalRepo.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"useDistanceToSendNotification": 1, "topTaskersPriorityScore": 1, "citiesApplyDistance": 1, "timeInBetweenTask": 1, "limitNumberAcceptTaskInDay": 1, "servicesApplyDistance": 1, "limitNumberAcceptTaskInDayRush": 1, "sendNotificationConfig": 1}, &settings)
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[ERROR Push New Task v2]: func SendNotificationToFavTaskers, settings nil: %v", err))
	}
	var task *modelTask.Task
	err = globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskId, IMPORTANT_FIELDS_OF_TASK, &task)
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[ERROR Push New Task v2]: func SendNotificationToFavTaskers, task nil: %v", err))
		return
	}
	asker, err := libUser.GetOneById(task.AskerId, bson.M{"favouriteTasker": 1})
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[ERROR Push New Task v2]: func SendNotificationToFavTaskers, asker nil: %v", err))
		return
	}
	// Match fav.Taskers and Taskers in service channel.
	var favTaskersInChannel []string
	if serviceChannel != nil {
		favTaskersInChannel = globalLib.IntersectionArray(asker.FavouriteTasker, serviceChannel.TaskerList)
	}
	// Remove Taskers in blackList
	favTaskersInChannel = globalLib.RemoveTaskersFromList(favTaskersInChannel, task.BlackList)
	if len(favTaskersInChannel) == 0 {
		if settings != nil && settings.UseDistanceToSendNotification {
			SendNewTaskNotificationTopTasker(serviceObject, taskId, isoCode)
		} else {
			SendNotificationNormal(serviceObject, taskId)
		}
		return
	}
	busyTaskers := GetBusyTaskersForTask(serviceObject.XId, task, settings)
	favTaskersInChannel = globalLib.RemoveTaskersFromList(favTaskersInChannel, busyTaskers)
	// Remove taskers in excludedTaskers
	excludedTaskers := GetTaskMetadata(task.XId).GetExcludedTaskers()
	favTaskersInChannel = globalLib.RemoveTaskersFromList(favTaskersInChannel, excludedTaskers)
	// NOTE: Because when opening services in a new city, tasks with the payment method 'cash' will not be accepted by any tasker,
	// we will allow taskers using BNPL to take on cash tasks.
	// Remove Tasker BNPL
	// taskerHasBNPL := GetListTaskerHasBNPL(task)
	// favTaskersInChannel = globalLib.RemoveTaskersFromList(favTaskersInChannel, taskerHasBNPL)

	findCondition := bson.M{
		"_id":    bson.M{"$in": favTaskersInChannel},
		"status": globalConstant.USER_STATUS_ACTIVE,
	}
	if task != nil && task.TaskPlace != nil && task.TaskPlace.City != "" {
		findCondition["workingPlaces.city"] = task.TaskPlace.City
	}
	if task.IsPremium {
		findCondition["isPremiumTasker"] = true
	}
	sendingList, _ := libUser.GetAll(findCondition, bson.M{"_id": 1, "language": 1, "company": 1})
	if len(sendingList) == 0 {
		if settings != nil && settings.UseDistanceToSendNotification {
			SendNewTaskNotificationTopTasker(serviceObject, taskId, isoCode)
		} else {
			SendNotificationNormal(serviceObject, taskId)
		}
		return
	}
	// Get notification data
	taskerIds, favTaskerIds, messages := GetPushNotificationData(task, serviceObject, sendingList)
	// Update viewedTaskers and visibility first.
	// globalRepo.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskId, bson.M{"$set": bson.M{"viewedTaskers": taskerIds, "visibility": 1}})
	UpdateTask(taskId, globalConstant.CHANGES_HISTORY_KEY_RESEND_FAV_TASKER, taskerIds, 1)
	// go func() {
	// 	callResendTask(taskId)
	// }()
	title := &modelService.ServiceText{
		Vi: localization.T("vi", "NOTIFICATION_NEW_TASK"),
		En: localization.T("en", "NOTIFICATION_NEW_TASK"),
		Ko: localization.T("ko", "NOTIFICATION_NEW_TASK"),
		Th: localization.T("th", "NOTIFICATION_NEW_TASK"),
	}
	mapDistrict := MapDistrictText(task.TaskPlace)
	body := &modelService.ServiceText{
		Vi: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("vi", serviceObject.Text), mapDistrict.Vi),
		En: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("en", serviceObject.Text), mapDistrict.En),
		Ko: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("ko", serviceObject.Text), mapDistrict.Ko),
		Th: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("th", serviceObject.Text), mapDistrict.Th),
		Id: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("id", serviceObject.Text), mapDistrict.Id),
	}
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       0,
		TaskId:     taskId,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
	}
	SendNotification(messages, favTaskerIds, title, body, payload, "", settings)

	// Send socket new task
	SendSocket(task, taskerIds)
}

/*
 * @Description: Get busy taskers for task to exclude when send notifications
 * @CreatedAt: 21/08/2020
 * @Author: linhnh
 * @UpdatedAt: 01/12/2020
 * @UpdatedBy: vinhnt
 */
func GetBusyTaskersForTask(serviceId string, task *modelTask.Task, settings *modelSetting.Settings) []string {
	// Get begin and end of date
	taskDate := globalLib.ParseDateFromTimeStamp(task.Date, local.TimeZone)
	beginDate := time.Date(taskDate.Year(), taskDate.Month(), taskDate.Day(), 0, 0, 0, 0, taskDate.Location())
	endDate := time.Date(taskDate.Year(), taskDate.Month(), taskDate.Day(), 23, 59, 59, 0, taskDate.Location())
	// Get the confirmed task in taskDate to get Taskers list

	var confirmedTasksInDay []*modelTask.Task
	globalRepo.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		bson.M{"date": bson.M{"$gte": beginDate, "$lte": endDate}, "serviceId": serviceId, "status": globalConstant.TASK_STATUS_CONFIRMED},
		bson.M{"acceptedTasker.taskerId": 1, "date": 1, "duration": 1},
		&confirmedTasksInDay,
	)
	// Get the Taskers were overload in taskDate
	var overloadTaskers []string
	if len(confirmedTasksInDay) > 0 {
		type CountTaskAccepted struct {
			TaskerId string
			Count    int32
		}
		// Init array TaskerIds
		var taskerAndNumOfTask []CountTaskAccepted
		for _, v := range confirmedTasksInDay {
			if len(v.AcceptedTasker) > 0 {
				item := CountTaskAccepted{
					TaskerId: v.AcceptedTasker[0].TaskerId,
				}
				index := -1
				for i, vNum := range taskerAndNumOfTask {
					if vNum.TaskerId == v.AcceptedTasker[0].TaskerId {
						index = i
						break
					}
				}
				if index == -1 {
					taskerAndNumOfTask = append(taskerAndNumOfTask, item)
				}
			}
		}
		// Count number of task
		for _, v := range confirmedTasksInDay {
			if len(v.AcceptedTasker) > 0 {
				index := -1
				for i, vNum := range taskerAndNumOfTask {
					if vNum.TaskerId == v.AcceptedTasker[0].TaskerId {
						index = i
						break
					}
				}
				if index >= 0 {
					taskerAndNumOfTask[index].Count++
				}
			}
		}
		// Get overload tasker
		var taskLimit int32
		if settings != nil && settings.LimitNumberAcceptTaskInDay > 0 {
			taskLimit = settings.LimitNumberAcceptTaskInDay
		}
		if taskDate.Weekday() == time.Monday || taskDate.Weekday() == time.Friday {
			taskLimit = 4
			if settings != nil && settings.LimitNumberAcceptTaskInDayRush > 0 {
				taskLimit = settings.LimitNumberAcceptTaskInDayRush
			}
		}
		for _, v := range taskerAndNumOfTask {
			if v.Count >= taskLimit {
				overloadTaskers = append(overloadTaskers, v.TaskerId)
			}
		}
	}
	// Get the Taskers were busy at taskTime
	var timeInBetweenTask int32
	if settings != nil && settings.TimeInBetweenTask > 0 {
		timeInBetweenTask = settings.TimeInBetweenTask
	}
	var busyTaskers []string
	for _, v := range confirmedTasksInDay {
		isConfilct := isConflictTask(task, v, timeInBetweenTask)
		if isConfilct && len(v.AcceptedTasker) > 0 {
			busyTaskers = append(busyTaskers, v.AcceptedTasker[0].TaskerId)
		}
	}
	// Exclude Tasker list: The active Company account
	listCompany, _ := libUser.GetAll(
		bson.M{
			"type":        globalConstant.USER_TYPE_TASKER,
			"status":      globalConstant.USER_STATUS_ACTIVE,
			"employeeIds": bson.M{"$exists": true},
		},
		bson.M{"_id": 1},
	)
	var excludeList []string
	for _, v := range listCompany {
		excludeList = append(excludeList, v.XId)
	}

	// Return the union of 2 Tasker list (unique taskerId) and exclude the company owner accounts
	union := globalLib.Union(overloadTaskers, busyTaskers)
	return globalLib.DifferenceArray(union, excludeList)
}

/*
 * @Description: Check if 2 task date is conflict
 * @CreatedAt: 21/08/2020
 * @Author: linhnh
 */
func isConflictTask(task *modelTask.Task, confirmedTask *modelTask.Task, timeInBetweenTask int32) bool {
	taskDate := globalLib.ParseDateFromTimeStamp(task.Date, local.TimeZone)
	taskStart_1 := taskDate.Add(time.Duration(-timeInBetweenTask) * time.Minute)
	taskEnd_1 := taskDate.Add(time.Duration(task.Duration*60+float64(timeInBetweenTask)) * time.Minute)

	taskStart_2 := globalLib.ParseDateFromTimeStamp(confirmedTask.Date, local.TimeZone)
	taskEnd_2 := taskStart_2.Add(time.Duration(confirmedTask.Duration) * time.Hour)

	if (taskStart_1.After(taskStart_2) && taskStart_1.Before(taskEnd_2)) || (taskStart_2.After(taskStart_1) && taskStart_2.Before(taskEnd_1)) {
		return true
	}
	return false
}

/*
 * @Description: Get inactive taskers for task to exclude when send notifications
 * @CreatedAt: 21/08/2020
 * @Author: linhnh
 * @UpdatedAt: 01/12/2020
 * @UpdatedBy: vinhnt
 */
func GetInactiveTaskerInWork(taskerIds []string) []string {
	var arrayTaskerIds []string
	// Get last 2 weeks date
	now := globalLib.GetCurrentTime(local.TimeZone)
	last2Weeks := now.AddDate(0, 0, -14)
	taskers, _ := libUser.GetAll(
		bson.M{
			"_id":           bson.M{"$in": taskerIds},
			"firstVerifyAt": bson.M{"$lt": last2Weeks},
			"$or": []bson.M{
				{"lastDoneTask": bson.M{"$lt": last2Weeks}},
				{"lastDoneTask": nil},
			},
		},
		bson.M{"_id": 1},
	)
	if len(taskers) > 0 {
		for _, v := range taskers {
			arrayTaskerIds = append(arrayTaskerIds, v.XId)
		}
	}
	return arrayTaskerIds
}

/*
 * @Description: Send new task notification to task.District only
 * @CreatedAt: 21/08/2020
 * @Author: linhnh
 * @UpdatedAt: 01/12/2020
 * @UpdatedBy: vinhnt
 */
func sendNewTaskNotificationToDistrictOnly(serviceObject *modelService.Service, settings *modelSetting.Settings, task *modelTask.Task, serviceChannel *modelServiceChannel.ServiceChannel) {
	/*
	   - Base on task.taskPlace find the tasker have these conditions:
	   1. Active tasker
	   2. In service channel
	   3. Have workingPlaces in district.

	   *** Don't send to all city inside
	*/
	asker, _ := libUser.GetOneById(task.AskerId, bson.M{"favouriteTasker": 1})
	var taskerList []string
	if serviceChannel != nil {
		taskerList = CopyArrayString(serviceChannel.TaskerList)
		taskerList = globalLib.RemoveTaskersFromList(taskerList, task.BlackList)
	}
	var favAndViewedTaskers []string
	if asker != nil && asker.FavouriteTasker != nil && len(asker.FavouriteTasker) > 0 {
		favAndViewedTaskers = globalLib.IntersectionArray(task.ViewedTaskers, asker.FavouriteTasker)
		taskerList = globalLib.RemoveTaskersFromList(taskerList, favAndViewedTaskers)
	}
	busyTaskers := GetBusyTaskersForTask(serviceObject.XId, task, settings)
	taskerList = globalLib.RemoveTaskersFromList(taskerList, busyTaskers)
	// Remove taskers in excludedTaskers
	excludedTaskers := GetTaskMetadata(task.XId).GetExcludedTaskers()
	taskerList = globalLib.RemoveTaskersFromList(taskerList, excludedTaskers)

	// Find Taskers
	findTaskerCondition := bson.M{
		"_id":                    bson.M{"$in": taskerList},
		"type":                   globalConstant.USER_TYPE_TASKER,
		"status":                 globalConstant.USER_STATUS_ACTIVE,
		"workingPlaces.country":  task.TaskPlace.Country,
		"workingPlaces.city":     task.TaskPlace.City,
		"workingPlaces.district": task.TaskPlace.District,
	}
	if task.IsPremium {
		findTaskerCondition["isPremiumTasker"] = true
	}
	taskers, _ := libUser.GetAll(findTaskerCondition, bson.M{"language": 1, "company": 1})
	if asker != nil && asker.FavouriteTasker != nil && len(asker.FavouriteTasker) > 0 {
		findFavTaskerCondition := bson.M{"_id": bson.M{"$in": favAndViewedTaskers}}
		if task.IsPremium {
			findFavTaskerCondition["isPremiumTasker"] = true
		}
		favTaskers, _ := libUser.GetAll(findFavTaskerCondition, bson.M{"language": 1, "company": 1})
		if len(favTaskers) > 0 {
			taskers = append(taskers, favTaskers...)
		}
	}
	if len(taskers) == 0 {
		return
	}
	// Get push notification data
	taskerIds, lstOfTaskers, lstOfNotifications := GetPushNotificationData(task, serviceObject, taskers)
	// Update viewed tasker
	// globalRepo.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], task.XId, bson.M{"$set": bson.M{"viewedTaskers": taskerIds, "visibility": 3}})
	UpdateTask(task.XId, globalConstant.CHANGES_HISTORY_KEY_RESEND_FAV_DISTRICT, taskerIds, 3)
	title := &modelService.ServiceText{
		Vi: localization.T("vi", "NOTIFICATION_NEW_TASK"),
		En: localization.T("en", "NOTIFICATION_NEW_TASK"),
		Ko: localization.T("ko", "NOTIFICATION_NEW_TASK"),
		Th: localization.T("th", "NOTIFICATION_NEW_TASK"),
	}
	mapDistrict := MapDistrictText(task.TaskPlace)
	body := &modelService.ServiceText{
		Vi: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("vi", serviceObject.Text), mapDistrict.Vi),
		En: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("en", serviceObject.Text), mapDistrict.En),
		Ko: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("ko", serviceObject.Text), mapDistrict.Ko),
		Th: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("th", serviceObject.Text), mapDistrict.Th),
		Id: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName("id", serviceObject.Text), mapDistrict.Id),
	}
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       0,
		TaskId:     task.XId,
		NavigateTo: globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
	}
	SendNotification(lstOfNotifications, lstOfTaskers, title, body, payload, "", settings)

	SendSocket(task, taskerIds)
}

/*
 * @Description: Connect GRPC to notification-service to send notify to users by userIds
 * @CreatedAt: 21/08/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
func SendNotification(arrayNotification []*modelNotification.Notification, userIds []*modelPushNotificationRequest.PushNotificationRequestUserIds, title *modelService.ServiceText, body *modelService.ServiceText, payload *modelPushNotificationRequest.PushNotificationRequestPayload, option string, settings *modelSetting.Settings) {
	if len(arrayNotification) > 0 {
		var listNotify []interface{}
		for _, v := range arrayNotification {
			if v.Type != 0 {
				listNotify = append(listNotify, v)
			}
		}
		if len(listNotify) > 0 {
			// repoNotification.New().Insert(listNotify)
			globalRepo.InsertAll(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], listNotify)
		}
	}

	if !IsTimeToSendNotification(settings) {
		return
	}

	if len(userIds) > 0 && option == "" {
		defer local.Logger.Sync()

		client, connect, err := grpcPushNotificationVN.ConnectGRPCPushNotificationVN(cfg.GRPC_Push_Notification_URL)
		if err != nil {
			local.Logger.Warn("Connect GRPC Push Notification error",
				zap.Error(err),
				zap.Any("title", title),
				zap.Any("body", body),
				zap.Any("payload", payload),
			)
		}
		defer connect.Close()

		reqNotify := &modelPushNotificationRequest.PushNotificationRequest{
			UserIds: userIds,
			Title:   title,
			Body:    body,
			Payload: payload,
		}
		_, err = client.Send(context.Background(), reqNotify)
		if err != nil {
			local.Logger.Warn("Push Notification error",
				zap.Error(err),
				zap.Any("title", title),
				zap.Any("body", body),
				zap.Any("payload", payload),
			)
		}
	}
}

/*
 * @Description: Check if now is time to send notification
 * @CreatedAt: 21/08/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
func IsTimeToSendNotification(settings *modelSetting.Settings) bool {
	// Not have sendNotificationConfig -> Send
	if settings == nil || settings.SendNotificationConfig == nil {
		return true
	}
	// isEnable == false -> send
	if !settings.SendNotificationConfig.IsEnableStopNotification {
		return true
	}
	// isEnable == true
	from := "23:00"
	if settings.SendNotificationConfig.StopNotificationFrom != "" {
		from = settings.SendNotificationConfig.StopNotificationFrom
	}

	to := "05:00"
	if settings.SendNotificationConfig.StopNotificationTo != "" {
		to = settings.SendNotificationConfig.StopNotificationTo
	}

	now := globalLib.GetCurrentTime(local.TimeZone)
	nowTime := time.Date(2021, 1, 1, now.Hour(), now.Minute(), 0, 0, local.TimeZone)

	froms := strings.Split(from, ":")
	fromHour, _ := strconv.Atoi(froms[0])
	fromMinute, _ := strconv.Atoi(froms[1])
	fromTime := time.Date(2021, 1, 1, fromHour, fromMinute, 0, 0, local.TimeZone)

	tos := strings.Split(to, ":")
	toHour, _ := strconv.Atoi(tos[0])
	toMinute, _ := strconv.Atoi(tos[1])
	toTime := time.Date(2021, 1, 1, toHour, toMinute, 0, 0, local.TimeZone)

	if nowTime.After(fromTime) && nowTime.Before(toTime) {
		return false
	}
	return true
}

/*
 * @Description: Get all data to push notification
 * @CreatedAt: 07/05/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func GetPushNotificationData(task *modelTask.Task, service *modelService.Service, taskers []*modelUser.Users) ([]string, []*modelPushNotificationRequest.PushNotificationRequestUserIds, []*modelNotification.Notification) {
	var taskerIds []string
	lstOfTaskers := []*modelPushNotificationRequest.PushNotificationRequestUserIds{}
	lstOfNotifications := []*modelNotification.Notification{}
	for _, v := range taskers {
		// tasker notify
		lang := globalConstant.LANG_EN
		if v.Language != "" {
			lang = v.Language
		}
		lstOfTaskers = append(lstOfTaskers, &modelPushNotificationRequest.PushNotificationRequestUserIds{UserId: v.XId, Language: lang})
		// company notify
		if v.Company != nil && v.Company.CompanyId != "" {
			lstOfTaskers = append(lstOfTaskers, &modelPushNotificationRequest.PushNotificationRequestUserIds{UserId: v.Company.CompanyId, Language: lang})
		}
	}

	if len(lstOfTaskers) > 0 {
		lstOfTaskers = UniqNotificationRequestUserIds(lstOfTaskers)
		for _, tasker := range lstOfTaskers {
			notify := &modelNotification.Notification{
				XId:         globalLib.GenerateObjectId(),
				UserId:      tasker.UserId,
				TaskId:      task.XId,
				Type:        0,
				Description: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName(tasker.Language, service.Text), localizeTaskDistrictText(tasker.Language, MapDistrictText(task.TaskPlace))),
				NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
				CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
			}
			taskerIds = append(taskerIds, tasker.UserId)
			// lstOfTaskers = append(lstOfTaskers, &modelPushNotificationRequest.PushNotificationRequestUserIds{UserId: tasker.UserId, Language: tasker.Language})
			lstOfNotifications = append(lstOfNotifications, notify)
		}
	}

	return taskerIds, lstOfTaskers, lstOfNotifications
}

/*
 * @Description: Remove duplicate NotificationRequestUserIds
 * @CreatedAt: 07/05/2021
 * @Author: ngoctb3
 * @UpdatedAt:
 * @UpdatedBy:
 */
func UniqNotificationRequestUserIds(input []*modelPushNotificationRequest.PushNotificationRequestUserIds) []*modelPushNotificationRequest.PushNotificationRequestUserIds {
	u := make([]*modelPushNotificationRequest.PushNotificationRequestUserIds, 0, len(input))
	m := make(map[string]bool)
	for _, val := range input {
		if _, ok := m[val.UserId]; !ok {
			u = append(u, val)
			m[val.UserId] = true
		}
	}
	return u
}

func SendSocket(task *modelTask.Task, messageTo []string) {
	// Send notification to tasker app
	grpcWebsocket.SendSocketMessageTask(cfg.GRPC_Websocket_Service_URL, globalConstant.WEBSOCKET_KEY_TASK, globalConstant.WEBSOCKET_TASK_SOURCE_NEW_TASK, task.XId, messageTo, task)
}

func localizeTaskDistrictText(language string, districtText *modelService.ServiceText) string {
	switch language {
	case "vi", "VN":
		return districtText.Vi
	case "ko", "KO":
		return districtText.Ko
	case "th", "TH":
		return districtText.Th
	case "id", "ID":
		return districtText.Id
	default:
		return districtText.En
	}
}

func GetPushNotificationDataForAllCity(task *modelTask.Task, service *modelService.Service, taskers []*modelUser.Users) ([]string, []*modelPushNotificationRequest.PushNotificationRequestUserIds, []*modelNotification.Notification) {
	var taskerIds []string
	lstOfTaskers := []*modelPushNotificationRequest.PushNotificationRequestUserIds{}
	lstOfNotifications := []*modelNotification.Notification{}
	for _, v := range taskers {
		// tasker notify
		lang := globalConstant.LANG_EN
		if v.Language != "" {
			lang = v.Language
		}
		lstOfTaskers = append(lstOfTaskers, &modelPushNotificationRequest.PushNotificationRequestUserIds{UserId: v.XId, Language: lang})
	}

	if len(lstOfTaskers) > 0 {
		for _, tasker := range lstOfTaskers {
			notify := &modelNotification.Notification{
				XId:         globalLib.GenerateObjectId(),
				UserId:      tasker.UserId,
				TaskId:      task.XId,
				Type:        0,
				Description: fmt.Sprintf("%s - %s", globalLib.LocalizeServiceName(tasker.Language, service.Text), localizeTaskDistrictText(tasker.Language, MapDistrictText(task.TaskPlace))),
				NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL,
				CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
			}
			taskerIds = append(taskerIds, tasker.UserId)
			lstOfNotifications = append(lstOfNotifications, notify)
		}
	}

	return taskerIds, lstOfTaskers, lstOfNotifications
}

func MapDistrictText(taskPlace *modelTask.TaskPlace) *modelService.ServiceText {
	if taskPlace.DistrictText != nil {
		return taskPlace.DistrictText
	}
	taskItemDistrict := &modelService.ServiceText{
		Vi: taskPlace.District,
		En: taskPlace.District,
		Th: taskPlace.District,
		Id: taskPlace.District,
		Ko: taskPlace.District,
	}
	return taskItemDistrict
}

func GetMaleTasker(serviceChannel *modelServiceChannel.ServiceChannel, asker *modelUser.Users, task *modelTask.Task, settings *modelSetting.Settings, busyTaskers, excludedTaskers []string) []*modelUser.Users {
	var taskerList []string
	if serviceChannel != nil {
		taskerList = CopyArrayString(serviceChannel.TaskerList)
		taskerList = globalLib.RemoveTaskersFromList(taskerList, task.BlackList)
	}
	if asker != nil && len(asker.FavouriteTasker) > 0 {
		taskerList = globalLib.RemoveTaskersFromList(taskerList, asker.FavouriteTasker)
	}
	inactiveTaskers := GetInactiveTaskerInWork(taskerList)
	taskerList = globalLib.RemoveTaskersFromList(taskerList, busyTaskers)
	taskerList = globalLib.RemoveTaskersFromList(taskerList, inactiveTaskers)
	// Remove taskers in excludedTaskers
	taskerList = globalLib.RemoveTaskersFromList(taskerList, excludedTaskers)

	findUserTaskerCondition := bson.M{
		"_id":                    bson.M{"$in": taskerList},
		"type":                   globalConstant.USER_TYPE_TASKER,
		"status":                 globalConstant.USER_STATUS_ACTIVE,
		"workingPlaces.country":  task.TaskPlace.Country,
		"workingPlaces.city":     task.TaskPlace.City,
		"workingPlaces.district": task.TaskPlace.District,
		"gender":                 globalConstant.GENDER_MALE,
	}
	// Premium task need premium tasker
	if task.IsPremium {
		findUserTaskerCondition["isPremiumTasker"] = true
	}
	taskers, _ := libUser.GetAll(findUserTaskerCondition, bson.M{"language": 1, "company": 1}, &globalDataAccessV2.QueryOptions{Sort: bson.M{"score": -1}})
	return taskers

}

func CopyArrayString(source []string) []string {
	dst := make([]string, len(source))
	copy(dst, source)
	return dst
}

// Get list tasker has BNPL
func GetListTaskerHasBNPL(task *modelTask.Task) []string {
	if task != nil && task.Payment != nil && task.Payment.Method != globalConstant.PAYMENT_METHOD_CASH {
		return []string{}
	}
	var taskerIds []string
	var taskersBNPLProcess []*modelTaskerBNPL.TaskerBNPLProcess
	globalRepo.GetAllByQuery(globalCollection.COLLECTION_TASKER_BNPL_PROCESS[local.ISO_CODE], bson.M{"status": globalConstant.TASK_BNPL_PROCESS_STATUS_PAYING}, bson.M{"taskerId": 1}, &taskersBNPLProcess)
	for _, v := range taskersBNPLProcess {
		taskerIds = append(taskerIds, v.TaskerId)
	}
	return taskerIds
}

func UpdateTask(taskId, key string, taskerIds []string, visibility int) {
	update := bson.M{
		"$set": bson.M{"viewedTaskers": taskerIds, "visibility": visibility},
		"$push": bson.M{"changesHistory": map[string]interface{}{
			"from":      globalConstant.CHANGES_HISTORY_FROM_SYSTEM,
			"key":       key,
			"content":   map[string]interface{}{"visibility": visibility},
			"createdAt": globalLib.GetCurrentTime(local.TimeZone),
		}},
	}
	globalRepo.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskId, update)
}

func GetTaskMetadata(taskId string) *modelTaskMetadata.TaskMetadata {
	var task *modelTaskMetadata.TaskMetadata
	globalRepo.GetOneById(globalCollection.COLLECTION_TASK_METADATA[local.ISO_CODE], taskId, bson.M{"excludedTaskers": 1}, &task)
	return task
}
