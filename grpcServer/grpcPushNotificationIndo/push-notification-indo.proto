syntax = "proto3";
package grpcPushNotificationIndo;

option go_package = "gitlab.com/btaskee/go-push-notification-indo-v3/grpcPushNotificationIndo";

import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest/pushNotificationRequest.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response/response.proto";

service PushNotificationIndo {
  rpc Send (pushNotificationRequest.PushNotificationRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/push-notification-indo/send"
      body: "*"
    };
  }
}