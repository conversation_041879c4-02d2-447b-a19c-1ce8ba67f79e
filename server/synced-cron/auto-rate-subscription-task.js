import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import {
  trackingScheduleStatus,
  revertTimeZone,
} from '/server/helper-function';

const runAutoRate = () => {
  return;
	console.log("Start runAutoRate subscription tasks ...");
  var message = null;
  try {
    const last7Days = moment().subtract(7, 'days').toDate();
    const last8Days = moment(last7Days).subtract(1, 'days').toDate();

    const now = new Date();
    const arrayOfTask = [];
    // Find DONE task didn't rated from last 7 days.
    Meteor.appCollection.Task.find({
      date: { $gte: last8Days, $lte: last7Days },
      status: 'DONE',
      rated: false,
      subscriptionId: {$exists: true}
    },{
      fields: {
        askerId: 1,
        acceptedTasker: 1,
        subscriptionId: 1,
      }
    }).forEach((task) => {
      if (task.acceptedTasker && task.acceptedTasker.length > 0 && task.acceptedTasker[0].taskerId) {
        // Make sure that task had acceptedTasker.
        const taskerId = task.acceptedTasker[0].taskerId;

        const subscription = Meteor.appCollection.Subscription.findOne({_id: task.subscriptionId}, {
          fields: {
            forceAcceptTaskerId: 1
          }
        });

        if ( subscription && subscription.forceAcceptTaskerId === taskerId ) {
          const existsRating = Meteor.appCollection.Rating.findOne({ askerId: task.askerId, taskerId, taskId: task._id }, {
            fields: {
              _id: 1
            }
          });
          if (!existsRating) {
            // Make sure that task hadn't any rating.
            arrayOfTask.push(task._id);
            // Insert into rating
            Meteor.appCollection.Rating.insert({
              askerId: task.askerId,
              taskerId,
              taskId: task._id,
              rate: 5,
              feedBack: [],
              createdAt: now,
              autoRate: true
            });
          }
        }
      }
    });
    // Update rated status of the tasks.
    if (arrayOfTask.length > 0) {
      Meteor.appCollection.Task.update({ _id: { $in: arrayOfTask } }, {
        $set: { rated: true, updatedAt: now },
      }, { multi: true });
    }
  } catch (e) {
    message = e;
  }
	console.log("Finished runAutoRate subscription tasks ");
  // Tracking
  trackingScheduleStatus({name: 'autoRateSubscriptionTask', nextRunAt: SyncedCron.nextScheduledAtDate('autoRateSubscriptionTask'), message});
};

  Meteor.methods({
    // Auto Rate Subscription Task
    autoRateSubscriptionTask() {
      SyncedCron.add({
        name: 'autoRateSubscriptionTask',
        schedule(parser) {
          // Run at 00:10 every day.
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 0)).getHours();
          return parser.cron(`0 10 ${hourInTimeZoneServer} 1/1 * ? *`, true);
        },
        job() {
          runAutoRate();
        },
      });
	    console.log("Added autoRateSubscriptionTask cron");
    },
    'runNow.autoRateSubscriptionTask'() {
      runAutoRate();
    },
  });
