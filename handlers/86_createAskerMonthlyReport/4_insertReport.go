package createAskerMonthlyReport

import (
	"fmt"
	"sync"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelAskerMonthlyReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/askerMonthlyReport"
	"go.mongodb.org/mongo-driver/bson"
)

func processInsertDB(w *sync.WaitGroup, dataChan chan *modelReport, startAMonth time.Time) {
	go func() {
		defer w.Done()
		askerIds := []string{}
		mapDataByAskerId := make(map[string]*modelReport)
		mapAskerHasMonthlyReport := make(map[string]bool)
		for data := range dataChan {
			askerIds = append(askerIds, data.AskerId)
			mapDataByAskerId[data.AskerId] = data
			if len(askerIds) >= 100 {
				processUpsertReport(askerIds, startAMonth, mapDataByAskerId, mapAskerHasMonthlyReport)
				askerIds = []string{}
			}
		}
		if len(askerIds) > 0 {
			processUpsertReport(askerIds, startAMonth, mapDataByAskerId, mapAskerHasMonthlyReport)
		}
	}()
}

func processUpsertReport(askerIds []string, startAMonth time.Time, mapDataByAskerId map[string]*modelReport, mapAskerHasMonthlyReport map[string]bool) {
	// Get data report by askerId
	var askerReports []*modelAskerMonthlyReport.AskerMonthlyReport
	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_ASKER_MONTHLY_REPORT[local.ISO_CODE], bson.M{
		"userId": bson.M{"$in": askerIds}, "year": int32(startAMonth.Year()),
	}, bson.M{}, &askerReports)
	// Post slack if err
	if err != nil {
		msg := fmt.Sprintf("CreateAskerMonthlyReport%s Get data report err: %v, askerIds: %v", local.ISO_CODE, err, askerIds)
		globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
	}
	// Check data report map by askerId
	for _, askerReport := range askerReports {
		mapAskerHasMonthlyReport[askerReport.UserId] = false // Đã có report của năm
		for _, v := range askerReport.MonthlyReport {
			if v.Month == int32(startAMonth.Month()) {
				mapAskerHasMonthlyReport[askerReport.UserId] = true // Đã có report của năm / tháng
			}
		}
	}

	insertData := []interface{}{}
	// Upsert data report monthly for asker
	for _, askerId := range askerIds {
		dataAskerReport, ok := mapDataByAskerId[askerId]
		if !ok {
			msg := fmt.Sprintf("CreateAskerMonthlyReportVN Error dataAskerReport is nil - data: %v, taskerId: %s", dataAskerReport, askerId)
			globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
			continue
		}
		hasMonthlyReport, hasYearReport := mapAskerHasMonthlyReport[askerId]
		if hasMonthlyReport {
			continue
		}
		if !hasYearReport {
			insertData = append(insertData, &modelAskerMonthlyReport.AskerMonthlyReport{
				XId:    globalLib.GenerateObjectId(),
				UserId: askerId,
				Year:   dataAskerReport.Year,
				MonthlyReport: []*modelAskerMonthlyReport.AskerMonthlyReportMonthlyReport{
					dataAskerReport.MonthlyReportInfo,
				},
				CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
			})
			delete(mapDataByAskerId, askerId)
		}
		if hasYearReport && !hasMonthlyReport {
			updateDataReportByAskerId(askerId, dataAskerReport)
			delete(mapDataByAskerId, askerId)
			delete(mapAskerHasMonthlyReport, askerId)
		}
	}

	if len(insertData) > 0 {
		insertMonthlyReportData(insertData)
	}
}

func updateDataReportByAskerId(askerId string, data *modelReport) {
	// Update data report by askerId
	_, err := globalDataAccess.UpdateOneByQuery(
		globalCollection.COLLECTION_ASKER_MONTHLY_REPORT[local.ISO_CODE],
		bson.M{"userId": askerId, "year": data.Year},
		bson.M{
			"$push": bson.M{"monthlyReport": data.MonthlyReportInfo},
		},
	)
	if err != nil {
		msg := fmt.Sprintf("CreateAskerMonthlyReport%s Update data report err: %v, askerId: %v", local.ISO_CODE, err, askerId)
		globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
	}
}

func insertMonthlyReportData(data []interface{}) {
	err := globalDataAccess.InsertAll(globalCollection.COLLECTION_ASKER_MONTHLY_REPORT[local.ISO_CODE], data)
	if err != nil {
		msg := fmt.Sprintf("CreateAskerMonthlyReport%s Insert data report err: %v, data: %v", local.ISO_CODE, err, data)
		globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
	}
}
