import { Meteor } from 'meteor/meteor';
import _ from 'lodash';
import moment from 'moment';
import {
  trackingScheduleStatus,
  postToSlack,
} from '/server/helper-function';

const runAlertLaundryTask = () => {
  return;
	console.log("Start runAlertLaundryTask ...");
  var message = null;

  const result = Meteor.appCollection.Task.find({
    status: 'CONFIRMED',
    detailLaundry: {$exists: true},
    collectionDate: {$lt: new Date()},
    'detailLaundry.isReceived': {$ne: true}
  }, {
    fields: {
      phone: 1,
      contactName: 1,
      acceptedTasker: 1,
      taskPlace: 1,
    }
  }).map(task => {
    const tasker = Meteor.appCollection.users.findOne({ _id: task.acceptedTasker[0].taskerId }, {fields: {phone: 1, name: 1}});
    return `${task.taskPlace.city} - ${task.phone} - ${task.contactName}. <PERSON><PERSON><PERSON> tác giặt ủi: ${tasker.phone} - ${tasker.name}`;
  });

  if ( result && result.length > 0 ) {
    postToSlack({
      channel: 'laundry-collecting-alert',
      text: `<PERSON><PERSON><PERSON> đối tác giặt ủi không nhận đồ đúng hẹn:\n${result.join('\n')}`,
    });
  }
  
	console.log("Finished runAlertLaundryTask ...");
  // Tracking
  trackingScheduleStatus({name: 'autoAlertLaundryNotCollect', nextRunAt: SyncedCron.nextScheduledAtDate('autoAlertLaundryNotCollect'), message});
};

  Meteor.methods({
    autoAlertLaundryNotCollect () {
      SyncedCron.add({
        name: 'autoAlertLaundryNotCollect',
        schedule(parser) {
          // Run every 30 minutes
          return parser.cron('0 0/30 * 1/1 * ? *', true);
        },
        job() {
          runAlertLaundryTask();
        },
      });
	    console.log("Added autoAlertLaundryNotCollect cron");
    },
    'runNow.autoAlertLaundryNotCollect'() {
      runAlertLaundryTask();
    },
  });
