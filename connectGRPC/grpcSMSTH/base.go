package grpcSMSTH

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcSMSTH"
	"google.golang.org/grpc"
)

func ConnectGRPCSMSTH(endpoint string) (grpcSMSTH.SMSTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcSMSTH.NewSMSTHClient(connect)
	return client, connect, err
}
