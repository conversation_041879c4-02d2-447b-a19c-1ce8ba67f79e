// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcAcceptTaskMY

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	acceptBookingRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/acceptBookingRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// AcceptTaskClient is the client API for AcceptTask service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AcceptTaskClient interface {
	ChooseTasker(ctx context.Context, in *acceptBookingRequest.AcceptBookingRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type acceptTaskClient struct {
	cc grpc.ClientConnInterface
}

func NewAcceptTaskClient(cc grpc.ClientConnInterface) AcceptTaskClient {
	return &acceptTaskClient{cc}
}

func (c *acceptTaskClient) ChooseTasker(ctx context.Context, in *acceptBookingRequest.AcceptBookingRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcAcceptTaskMY.AcceptTask/ChooseTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AcceptTaskServer is the server API for AcceptTask service.
// All implementations must embed UnimplementedAcceptTaskServer
// for forward compatibility
type AcceptTaskServer interface {
	ChooseTasker(context.Context, *acceptBookingRequest.AcceptBookingRequest) (*empty.Empty, error)
	mustEmbedUnimplementedAcceptTaskServer()
}

// UnimplementedAcceptTaskServer must be embedded to have forward compatible implementations.
type UnimplementedAcceptTaskServer struct {
}

func (UnimplementedAcceptTaskServer) ChooseTasker(context.Context, *acceptBookingRequest.AcceptBookingRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChooseTasker not implemented")
}
func (UnimplementedAcceptTaskServer) mustEmbedUnimplementedAcceptTaskServer() {}

// UnsafeAcceptTaskServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AcceptTaskServer will
// result in compilation errors.
type UnsafeAcceptTaskServer interface {
	mustEmbedUnimplementedAcceptTaskServer()
}

func RegisterAcceptTaskServer(s *grpc.Server, srv AcceptTaskServer) {
	s.RegisterService(&_AcceptTask_serviceDesc, srv)
}

func _AcceptTask_ChooseTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(acceptBookingRequest.AcceptBookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AcceptTaskServer).ChooseTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcAcceptTaskMY.AcceptTask/ChooseTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AcceptTaskServer).ChooseTasker(ctx, req.(*acceptBookingRequest.AcceptBookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _AcceptTask_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcAcceptTaskMY.AcceptTask",
	HandlerType: (*AcceptTaskServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ChooseTasker",
			Handler:    _AcceptTask_ChooseTasker_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "acceptTask.proto",
}
