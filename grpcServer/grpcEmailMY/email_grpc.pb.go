// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcEmailMY

import (
	context "context"
	emailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	emailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	task "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	users "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// EmailClient is the client API for Email service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EmailClient interface {
	EmailTest(ctx context.Context, in *emailSending.EmailSending, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendEmail(ctx context.Context, in *emailSending.EmailSending, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendReceiptEmail(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendCancelFeeEmail(ctx context.Context, in *emailSending.EmailCancelFeeRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendRenewSubscriptionEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendVerifyEmail(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
}

type emailClient struct {
	cc grpc.ClientConnInterface
}

func NewEmailClient(cc grpc.ClientConnInterface) EmailClient {
	return &emailClient{cc}
}

func (c *emailClient) EmailTest(ctx context.Context, in *emailSending.EmailSending, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailMY.Email/EmailTest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailClient) SendEmail(ctx context.Context, in *emailSending.EmailSending, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailMY.Email/SendEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailClient) SendReceiptEmail(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailMY.Email/SendReceiptEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailClient) SendCancelFeeEmail(ctx context.Context, in *emailSending.EmailCancelFeeRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailMY.Email/SendCancelFeeEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailClient) SendRenewSubscriptionEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailMY.Email/SendRenewSubscriptionEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailClient) SendVerifyEmail(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailMY.Email/SendVerifyEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EmailServer is the server API for Email service.
// All implementations must embed UnimplementedEmailServer
// for forward compatibility
type EmailServer interface {
	EmailTest(context.Context, *emailSending.EmailSending) (*emailResponse.EmailResponse, error)
	SendEmail(context.Context, *emailSending.EmailSending) (*emailResponse.EmailResponse, error)
	SendReceiptEmail(context.Context, *task.Task) (*emailResponse.EmailResponse, error)
	SendCancelFeeEmail(context.Context, *emailSending.EmailCancelFeeRequest) (*emailResponse.EmailResponse, error)
	SendRenewSubscriptionEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error)
	SendVerifyEmail(context.Context, *users.Users) (*emailResponse.EmailResponse, error)
	mustEmbedUnimplementedEmailServer()
}

// UnimplementedEmailServer must be embedded to have forward compatible implementations.
type UnimplementedEmailServer struct {
}

func (UnimplementedEmailServer) EmailTest(context.Context, *emailSending.EmailSending) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EmailTest not implemented")
}
func (UnimplementedEmailServer) SendEmail(context.Context, *emailSending.EmailSending) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendEmail not implemented")
}
func (UnimplementedEmailServer) SendReceiptEmail(context.Context, *task.Task) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendReceiptEmail not implemented")
}
func (UnimplementedEmailServer) SendCancelFeeEmail(context.Context, *emailSending.EmailCancelFeeRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendCancelFeeEmail not implemented")
}
func (UnimplementedEmailServer) SendRenewSubscriptionEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendRenewSubscriptionEmail not implemented")
}
func (UnimplementedEmailServer) SendVerifyEmail(context.Context, *users.Users) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendVerifyEmail not implemented")
}
func (UnimplementedEmailServer) mustEmbedUnimplementedEmailServer() {}

// UnsafeEmailServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EmailServer will
// result in compilation errors.
type UnsafeEmailServer interface {
	mustEmbedUnimplementedEmailServer()
}

func RegisterEmailServer(s *grpc.Server, srv EmailServer) {
	s.RegisterService(&_Email_serviceDesc, srv)
}

func _Email_EmailTest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailSending)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailServer).EmailTest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailMY.Email/EmailTest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailServer).EmailTest(ctx, req.(*emailSending.EmailSending))
	}
	return interceptor(ctx, in, info, handler)
}

func _Email_SendEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailSending)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailServer).SendEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailMY.Email/SendEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailServer).SendEmail(ctx, req.(*emailSending.EmailSending))
	}
	return interceptor(ctx, in, info, handler)
}

func _Email_SendReceiptEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(task.Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailServer).SendReceiptEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailMY.Email/SendReceiptEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailServer).SendReceiptEmail(ctx, req.(*task.Task))
	}
	return interceptor(ctx, in, info, handler)
}

func _Email_SendCancelFeeEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailCancelFeeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailServer).SendCancelFeeEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailMY.Email/SendCancelFeeEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailServer).SendCancelFeeEmail(ctx, req.(*emailSending.EmailCancelFeeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Email_SendRenewSubscriptionEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailServer).SendRenewSubscriptionEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailMY.Email/SendRenewSubscriptionEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailServer).SendRenewSubscriptionEmail(ctx, req.(*emailSending.EmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Email_SendVerifyEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(users.Users)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailServer).SendVerifyEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailMY.Email/SendVerifyEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailServer).SendVerifyEmail(ctx, req.(*users.Users))
	}
	return interceptor(ctx, in, info, handler)
}

var _Email_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcEmailMY.Email",
	HandlerType: (*EmailServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "EmailTest",
			Handler:    _Email_EmailTest_Handler,
		},
		{
			MethodName: "SendEmail",
			Handler:    _Email_SendEmail_Handler,
		},
		{
			MethodName: "SendReceiptEmail",
			Handler:    _Email_SendReceiptEmail_Handler,
		},
		{
			MethodName: "SendCancelFeeEmail",
			Handler:    _Email_SendCancelFeeEmail_Handler,
		},
		{
			MethodName: "SendRenewSubscriptionEmail",
			Handler:    _Email_SendRenewSubscriptionEmail_Handler,
		},
		{
			MethodName: "SendVerifyEmail",
			Handler:    _Email_SendVerifyEmail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "email.proto",
}
