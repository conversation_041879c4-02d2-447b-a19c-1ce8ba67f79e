package grpcDataTransactionIndo

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcDataTransactionIndo"
	"google.golang.org/grpc"
)

func ConnectGRPCDataTransaction(endpoint string) (grpcDataTransactionIndo.DataTransactionClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcDataTransactionIndo.NewDataTransactionClient(connect)
	return client, connect, err
}
