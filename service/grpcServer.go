package service

import (
	"context"
	"fmt"
	"runtime/debug"

	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcSendTaskVN"
	modelPushNotificationNewTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
)

type GRPCServer struct {
	grpcSendTaskVN.UnimplementedPushNotificationNewTaskVNServer
}

var cfg = config.GetConfig()

func (s *GRPCServer) NewTask(ctx context.Context, req *modelPushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	defer errorHandling(req)
	return NewTask(req)
}

func (s *GRPCServer) TopTasker(ctx context.Context, req *modelPushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	defer errorHandling(req)
	return TopTasker(req)
}

func (s *GRPCServer) NewTaskToDistrict(ctx context.Context, req *modelPushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	defer errorHandling(req)
	return NewTaskToDistrict(req)
}

func (s *GRPCServer) FavTasker(ctx context.Context, req *modelPushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	defer errorHandling(req)
	return FavTasker(req)
}

func (s *GRPCServer) Normal(ctx context.Context, req *modelPushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	defer errorHandling(req)
	return Normal(req)
}

func (s *GRPCServer) FavAndTaskerDistrict(ctx context.Context, req *modelPushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	defer errorHandling(req)
	return FavAndTaskerDistrict(req)
}

func (s *GRPCServer) FavAndTopTasker(ctx context.Context, req *modelPushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	defer errorHandling(req)
	return FavAndTopTasker(req)
}

func (s *GRPCServer) NewTaskToCity(ctx context.Context, req *modelPushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	defer errorHandling(req)
	return NewTaskToCity(req)
}

func errorHandling(reqBody *modelPushNotificationNewTask.NewTaskRequest) {
	if err := recover(); err != nil {
		msg := fmt.Sprintf("[ERROR] Send Task VN v3, request: %v, err: %v, Debug: %v", reqBody, err, string(debug.Stack()))
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
	}
}
