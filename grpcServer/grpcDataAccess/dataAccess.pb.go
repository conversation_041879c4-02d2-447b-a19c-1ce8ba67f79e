// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.7.1
// source: dataAccess.proto

package grpcDataAccess

import (
	empty "github.com/golang/protobuf/ptypes/empty"
	dataAccess "gitlab.com/btaskee/go-services-model-v2/grpcmodel/dataAccess"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_dataAccess_proto protoreflect.FileDescriptor

var file_dataAccess_proto_rawDesc = []byte{
	0x0a, 0x10, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0e, 0x67, 0x72, 0x70, 0x63, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x1a, 0x4d, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62,
	0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x2f, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0x8a,
	0x10, 0x0a, 0x0a, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x12, 0x4e, 0x0a,
	0x0b, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x42, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x51, 0x0a,
	0x0e, 0x49, 0x73, 0x45, 0x78, 0x69, 0x73, 0x74, 0x42, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x4f, 0x0a, 0x0c, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x42, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x12, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x4d, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x42, 0x79, 0x49, 0x64, 0x12,
	0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x50, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x42, 0x79, 0x51, 0x75, 0x65, 0x72,
	0x79, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x53, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x4f, 0x6e, 0x65, 0x42, 0x79, 0x51, 0x75,
	0x65, 0x72, 0x79, 0x4d, 0x61, 0x70, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x54, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4f, 0x6e,
	0x65, 0x42, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x6f, 0x72, 0x74, 0x12, 0x1d, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x50, 0x0a,
	0x0d, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x42, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1d,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x56, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x42, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79,
	0x50, 0x61, 0x67, 0x69, 0x6e, 0x67, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x54, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x41, 0x6c,
	0x6c, 0x42, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x6f, 0x72, 0x74, 0x12, 0x1d, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a,
	0x17, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x42, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x50, 0x61,
	0x67, 0x69, 0x6e, 0x67, 0x53, 0x6f, 0x72, 0x74, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x16, 0x47, 0x65, 0x74,
	0x41, 0x6c, 0x6c, 0x42, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53,
	0x6f, 0x72, 0x74, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x42, 0x79,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x53, 0x6b, 0x69, 0x70, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x53, 0x6f,
	0x72, 0x74, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x55, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x44, 0x69, 0x73, 0x74, 0x69, 0x6e,
	0x63, 0x74, 0x42, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4c, 0x0a, 0x09, 0x41, 0x67,
	0x67, 0x72, 0x65, 0x67, 0x61, 0x74, 0x65, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x09, 0x49, 0x6e, 0x73, 0x65,
	0x72, 0x74, 0x4f, 0x6e, 0x65, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x44,
	0x0a, 0x09, 0x49, 0x6e, 0x73, 0x65, 0x72, 0x74, 0x41, 0x6c, 0x6c, 0x12, 0x1d, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x00, 0x12, 0x50, 0x0a, 0x0d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4f, 0x6e,
	0x65, 0x42, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x4f, 0x6e, 0x65, 0x42, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x53, 0x0a, 0x10, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x42, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12,
	0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74,
	0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x48, 0x0a, 0x0d, 0x55, 0x70, 0x73, 0x65, 0x72, 0x74, 0x4f, 0x6e, 0x65, 0x42, 0x79, 0x49,
	0x64, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44,
	0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x10, 0x55, 0x70,
	0x73, 0x65, 0x72, 0x74, 0x4f, 0x6e, 0x65, 0x42, 0x79, 0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1d,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x4f, 0x6e, 0x65, 0x42, 0x79, 0x49, 0x64, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x00, 0x12, 0x4b, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4f, 0x6e, 0x65, 0x42, 0x79,
	0x51, 0x75, 0x65, 0x72, 0x79, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x4b,
	0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x6c, 0x6c, 0x42, 0x79, 0x51, 0x75, 0x65,
	0x72, 0x79, 0x12, 0x1d, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x42, 0x43, 0x5a, 0x41, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65,
	0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x44, 0x61, 0x74, 0x61, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_dataAccess_proto_goTypes = []interface{}{
	(*dataAccess.DataAccessRequest)(nil),  // 0: dataAccess.DataAccessRequest
	(*dataAccess.DataAccessResponse)(nil), // 1: dataAccess.DataAccessResponse
	(*empty.Empty)(nil),                   // 2: google.protobuf.Empty
}
var file_dataAccess_proto_depIdxs = []int32{
	0,  // 0: grpcDataAccess.DataAccess.IsExistById:input_type -> dataAccess.DataAccessRequest
	0,  // 1: grpcDataAccess.DataAccess.IsExistByQuery:input_type -> dataAccess.DataAccessRequest
	0,  // 2: grpcDataAccess.DataAccess.CountByQuery:input_type -> dataAccess.DataAccessRequest
	0,  // 3: grpcDataAccess.DataAccess.GetOneById:input_type -> dataAccess.DataAccessRequest
	0,  // 4: grpcDataAccess.DataAccess.GetOneByQuery:input_type -> dataAccess.DataAccessRequest
	0,  // 5: grpcDataAccess.DataAccess.GetOneByQueryMap:input_type -> dataAccess.DataAccessRequest
	0,  // 6: grpcDataAccess.DataAccess.GetOneByQuerySort:input_type -> dataAccess.DataAccessRequest
	0,  // 7: grpcDataAccess.DataAccess.GetAllByQuery:input_type -> dataAccess.DataAccessRequest
	0,  // 8: grpcDataAccess.DataAccess.GetAllByQueryPaging:input_type -> dataAccess.DataAccessRequest
	0,  // 9: grpcDataAccess.DataAccess.GetAllByQuerySort:input_type -> dataAccess.DataAccessRequest
	0,  // 10: grpcDataAccess.DataAccess.GetAllByQueryPagingSort:input_type -> dataAccess.DataAccessRequest
	0,  // 11: grpcDataAccess.DataAccess.GetAllByQueryLimitSort:input_type -> dataAccess.DataAccessRequest
	0,  // 12: grpcDataAccess.DataAccess.GetAllByQuerySkipLimitSort:input_type -> dataAccess.DataAccessRequest
	0,  // 13: grpcDataAccess.DataAccess.GetDistinctByQuery:input_type -> dataAccess.DataAccessRequest
	0,  // 14: grpcDataAccess.DataAccess.Aggregate:input_type -> dataAccess.DataAccessRequest
	0,  // 15: grpcDataAccess.DataAccess.InsertOne:input_type -> dataAccess.DataAccessRequest
	0,  // 16: grpcDataAccess.DataAccess.InsertAll:input_type -> dataAccess.DataAccessRequest
	0,  // 17: grpcDataAccess.DataAccess.UpdateOneById:input_type -> dataAccess.DataAccessRequest
	0,  // 18: grpcDataAccess.DataAccess.UpdateOneByQuery:input_type -> dataAccess.DataAccessRequest
	0,  // 19: grpcDataAccess.DataAccess.UpdateAllByQuery:input_type -> dataAccess.DataAccessRequest
	0,  // 20: grpcDataAccess.DataAccess.UpsertOneById:input_type -> dataAccess.DataAccessRequest
	0,  // 21: grpcDataAccess.DataAccess.UpsertOneByQuery:input_type -> dataAccess.DataAccessRequest
	0,  // 22: grpcDataAccess.DataAccess.DeleteOneById:input_type -> dataAccess.DataAccessRequest
	0,  // 23: grpcDataAccess.DataAccess.DeleteOneByQuery:input_type -> dataAccess.DataAccessRequest
	0,  // 24: grpcDataAccess.DataAccess.DeleteAllByQuery:input_type -> dataAccess.DataAccessRequest
	1,  // 25: grpcDataAccess.DataAccess.IsExistById:output_type -> dataAccess.DataAccessResponse
	1,  // 26: grpcDataAccess.DataAccess.IsExistByQuery:output_type -> dataAccess.DataAccessResponse
	1,  // 27: grpcDataAccess.DataAccess.CountByQuery:output_type -> dataAccess.DataAccessResponse
	1,  // 28: grpcDataAccess.DataAccess.GetOneById:output_type -> dataAccess.DataAccessResponse
	1,  // 29: grpcDataAccess.DataAccess.GetOneByQuery:output_type -> dataAccess.DataAccessResponse
	1,  // 30: grpcDataAccess.DataAccess.GetOneByQueryMap:output_type -> dataAccess.DataAccessResponse
	1,  // 31: grpcDataAccess.DataAccess.GetOneByQuerySort:output_type -> dataAccess.DataAccessResponse
	1,  // 32: grpcDataAccess.DataAccess.GetAllByQuery:output_type -> dataAccess.DataAccessResponse
	1,  // 33: grpcDataAccess.DataAccess.GetAllByQueryPaging:output_type -> dataAccess.DataAccessResponse
	1,  // 34: grpcDataAccess.DataAccess.GetAllByQuerySort:output_type -> dataAccess.DataAccessResponse
	1,  // 35: grpcDataAccess.DataAccess.GetAllByQueryPagingSort:output_type -> dataAccess.DataAccessResponse
	1,  // 36: grpcDataAccess.DataAccess.GetAllByQueryLimitSort:output_type -> dataAccess.DataAccessResponse
	1,  // 37: grpcDataAccess.DataAccess.GetAllByQuerySkipLimitSort:output_type -> dataAccess.DataAccessResponse
	1,  // 38: grpcDataAccess.DataAccess.GetDistinctByQuery:output_type -> dataAccess.DataAccessResponse
	1,  // 39: grpcDataAccess.DataAccess.Aggregate:output_type -> dataAccess.DataAccessResponse
	2,  // 40: grpcDataAccess.DataAccess.InsertOne:output_type -> google.protobuf.Empty
	2,  // 41: grpcDataAccess.DataAccess.InsertAll:output_type -> google.protobuf.Empty
	1,  // 42: grpcDataAccess.DataAccess.UpdateOneById:output_type -> dataAccess.DataAccessResponse
	1,  // 43: grpcDataAccess.DataAccess.UpdateOneByQuery:output_type -> dataAccess.DataAccessResponse
	1,  // 44: grpcDataAccess.DataAccess.UpdateAllByQuery:output_type -> dataAccess.DataAccessResponse
	2,  // 45: grpcDataAccess.DataAccess.UpsertOneById:output_type -> google.protobuf.Empty
	2,  // 46: grpcDataAccess.DataAccess.UpsertOneByQuery:output_type -> google.protobuf.Empty
	2,  // 47: grpcDataAccess.DataAccess.DeleteOneById:output_type -> google.protobuf.Empty
	2,  // 48: grpcDataAccess.DataAccess.DeleteOneByQuery:output_type -> google.protobuf.Empty
	2,  // 49: grpcDataAccess.DataAccess.DeleteAllByQuery:output_type -> google.protobuf.Empty
	25, // [25:50] is the sub-list for method output_type
	0,  // [0:25] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_dataAccess_proto_init() }
func file_dataAccess_proto_init() {
	if File_dataAccess_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_dataAccess_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_dataAccess_proto_goTypes,
		DependencyIndexes: file_dataAccess_proto_depIdxs,
	}.Build()
	File_dataAccess_proto = out.File
	file_dataAccess_proto_rawDesc = nil
	file_dataAccess_proto_goTypes = nil
	file_dataAccess_proto_depIdxs = nil
}
