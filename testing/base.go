//Package testing file base
/*
* @File: base.go
* @Description: Handler function before Test
 * @CreatedAt: 22/02/2021
 * @Author: vinhnt
*/
package testing

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

var apiSyncCron = "/api/v3/sync-cron-vn"

func ResetData() {
	// Remove User
	modelUser.DeleteAllByQuery(local.ISO_CODE, bson.M{})

	// Remove task
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{})

	// Remove taskSchedule
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASK_SCHEDULE[local.ISO_CODE], bson.M{})

	// Remove FAccount
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], bson.M{})

	// Remove PaymentCard
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_PAYMENT_CARD[local.ISO_CODE], bson.M{})

	// Remove Notifiaction
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{})

	// Reset Service Channel
	globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{}, bson.M{"$unset": bson.M{"taskerList": 1}})

	// Remove FATransaction
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], bson.M{})

	// Remove trust point history
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TRUST_POINT_HISTORY[local.ISO_CODE], bson.M{})

	// Reset Promotion History
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_PROMOTION_HISTORY[local.ISO_CODE], bson.M{})

	// Reset AskerRating
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_ASKER_RATING[local.ISO_CODE], bson.M{})

	// Reset Rating
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_RATING[local.ISO_CODE], bson.M{})

	// Reset Not Come Lock History
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_NOT_COME_LOCK_HISTORY[local.ISO_CODE], bson.M{})

	// Remove Subscription
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], bson.M{})

	// Remove Tasker Referral
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_REFERRAL[local.ISO_CODE], bson.M{})

	// Remove FAccount
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], bson.M{})

	// Remove Tasker Violate
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_VIOLATE[local.ISO_CODE], bson.M{})

	// Remove Reward
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_REWARD[local.ISO_CODE], bson.M{})

	// Remove CHAT_MESSAGE
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_CHAT_MESSAGE[globalConstant.ISO_CODE_VN], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_CHAT_MESSAGE[globalConstant.ISO_CODE_TH], bson.M{})
	// Remove Notification
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{})

	// Remove UserActionHistory
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_USER_ACTION_HISTORY[local.ISO_CODE], bson.M{})

	// Remove th_FATransaction
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], bson.M{})

	// Remove th_reward
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_REWARD[local.ISO_CODE], bson.M{})

	// Remove th_taskerViolate
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_NOT_VIOLATE[local.ISO_CODE], bson.M{})

	// Remove th_taskerNotViolate
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_VIOLATE[local.ISO_CODE], bson.M{})

	lastYear := globalLib.GetCurrentTime(local.TimeZone).Year() - 1
	// Remove taskerYearEndReport
	collectionTaskerYearEndReport := fmt.Sprintf("%s%d", globalCollection.COLLECTION_TASKER_YEAR_END_REPORT[local.ISO_CODE], lastYear)
	globalDataAccess.DeleteAllByQuery(collectionTaskerYearEndReport, bson.M{})

	// Remove th_taskerYearEndReport
	collectionTHTaskerYearEndReport := fmt.Sprintf("%s%d", globalCollection.COLLECTION_TASKER_YEAR_END_REPORT[local.ISO_CODE], lastYear)
	globalDataAccess.DeleteAllByQuery(collectionTHTaskerYearEndReport, bson.M{})

	// Remove askerYearEndReport
	collectionAskerYearEndReport := fmt.Sprintf("%s%d", globalCollection.COLLECTION_ASKER_YEAR_END_REPORT[local.ISO_CODE], lastYear)
	globalDataAccess.DeleteAllByQuery(collectionAskerYearEndReport, bson.M{})

	// Remove th_askerYearEndReport
	collectionTHAskerYearEndReport := fmt.Sprintf("%s%d", globalCollection.COLLECTION_ASKER_YEAR_END_REPORT[local.ISO_CODE], lastYear)
	globalDataAccess.DeleteAllByQuery(collectionTHAskerYearEndReport, bson.M{})

	// Remove COLLECTION_OUTSTANDING_PAYMENT
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_OUTSTANDING_PAYMENT[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_PAYMENT_TRANSACTION
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_PAYMENT_TRANSACTION, bson.M{})

	// Remove COLLECTION_GIFT
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_GIFT[globalConstant.ISO_CODE_VN], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_GIFT[globalConstant.ISO_CODE_TH], bson.M{})

	// Remove COLLECTION_PROMOTION_CODE
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_BEMPLOYEE
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BEMPLOYEE[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_SETTING_SYNC_CRON
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_SETTING_SYNC_CRON[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_QUIZ_FOR_TASKER_PREMIUM_HISTORY
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_QUIZ_FOR_TASKER_PREMIUM_HISTORY[local.ISO_CODE], bson.M{})

	// Remove shopeePayTransaction
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_SHOPEEPAY_TRANSACTION[local.ISO_CODE], bson.M{})

	// Remove vn_shopeePayTransaction
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_SHOPEEPAY_TRANSACTION[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_USERS_DELETED
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_USERS_DELETED[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_TASK_REPORT_TASKER
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASK_REPORT_TASKER[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_POINT_TRANSACTION
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_REFUND_REQUEST
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_REFUND_REQUEST[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_REFUND_TRANSACTION
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_REFUND_TRANSACTION[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_MOMO_TRANSACTION
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_MOMO_TRANSACTION, bson.M{})

	// Remove COLLECTION_ZALO_PAY_TRANSACTION
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_ZALOPAY_TRANSACTION, bson.M{})

	// Remove COLLECTION_PAY_TRANSACTION
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_VN_PAY_TRANSACTION, bson.M{})

	// Remove COLLECTION_REFUND_REQUEST
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_REFUND_REQUEST[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_REFUND_TRANSACTION
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_REFUND_REQUEST[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_TASKER_WORKING_HISTORY
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_WORKING_HISTORY[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_REFUND_TRANSACTION
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_REFUND_REQUEST[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_FINANCIAL_ACCOUNT_HISTORY
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_FINANCIAL_ACCOUNT_HISTORY[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_FINANCIAL_ACCOUNT_HISTORY
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_FINANCIAL_ACCOUNT_HISTORY[local.ISO_CODE], bson.M{})
	// Remove COLLECTION_COMBO_VOUCHER
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_COMBO_VOUCHER[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_COMBO_VOUCHER_TRANSACTION
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_COMBO_VOUCHER_TRANSACTION[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_PROMOTION_CODE
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_PROMOTION_CODE[local.ISO_CODE], bson.M{})

	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_JOURNEY_LEADER_BOARD[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_JOURNEY_LEADER_BOARD_WEEKLY_REPORT[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_JOURNEY_LEADER_BOARD_BACKUP[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_JOURNEY_SETTING[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_PURCHASE_ORDER[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_POINT_TRANSACTION[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_ASKER_FINANCIAL_ACCOUNT_HISTORY[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_ASKER_FINANCIAL_ACCOUNT_HISTORY[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_POINT_TRANSACTION[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASK[globalConstant.ISO_CODE_VN], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_FATRANSACTION[globalConstant.ISO_CODE_VN], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_NOTIFICATION[globalConstant.ISO_CODE_VN], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_SUBSCRIPTION[globalConstant.ISO_CODE_VN], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASK_SCHEDULE[globalConstant.ISO_CODE_VN], bson.M{})

	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_BNPL_TRANSACTION[globalConstant.ISO_CODE_VN], bson.M{})

	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_BNPL_PROCESS[globalConstant.ISO_CODE_VN], bson.M{})

	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_CHAT_MESSAGE[globalConstant.ISO_CODE_VN], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_PAYMENT_MB_TRANSACTION
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_PAYMENT_MB_TRANSACTION, bson.M{})

	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_ASKER_MONTHLY_REPORT[globalConstant.ISO_CODE_VN], bson.M{})

	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_CHAT_CONVERSATION[globalConstant.ISO_CODE_VN], bson.M{})

	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_HISTORY_CHAT_CONVERSATIONS[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_HISTORY_CHAT_MESSAGES[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN[local.ISO_CODE], bson.M{})

	//Remove rating
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_RATING[local.ISO_CODE], bson.M{})
	// tasker lucky draw
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_GAME_CAMPAIGN[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_GAME_CAMPAIGN_HISTORY[globalConstant.ISO_CODE_VN], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_USER_GAME_CAMPAIGN[local.ISO_CODE], bson.M{})
	// Business
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BUSINESS_MEMBER_TRANSACTION[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_BUSINESS_TRANSACTION[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TASKER_MONTHLY_REPORT[local.ISO_CODE], bson.M{})

	// Remove COLLECTION_COMMUNITY
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_COMMUNITY_TAG[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_COMMUNITY_POST[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_COMMUNITY_USER[local.ISO_CODE], bson.M{})

	// Remove CommunityUser
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_COMMUNITY_USER[local.ISO_CODE], bson.M{})

	// Remove Medals
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_COMMUNITY_MEDAL[local.ISO_CODE], bson.M{})

	// Remove community notifications
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_COMMUNITY_NOTIFICATION[local.ISO_CODE], bson.M{})

	// Remove course start date
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TRAINING_TASKER_COURSE_START_DATE[globalConstant.ISO_CODE_VN], bson.M{})

	// Remove course
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TRAINING_TASKER_COURSE[local.ISO_CODE], bson.M{})

	// Remove submission
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_TRAINING_TASKER_SUBMISSION[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_COMBO_VOUCHER[local.ISO_CODE], bson.M{})
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_USER_COMBO_VOUCHER[local.ISO_CODE], bson.M{})
}

// ============================= TASK

func CreateTask(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		var service *modelService.Service
		collectionServiceName := globalCollection.COLLECTION_SERVICE[local.ISO_CODE]
		collectionTask := globalCollection.COLLECTION_TASK[local.ISO_CODE]
		isoCode := globalConstant.ISO_CODE_VN
		if v["isoCode"] != nil {
			isoCode = v["isoCode"].(string)
		}

		globalDataAccess.GetOneByQuery(collectionServiceName, bson.M{"name": v["serviceName"]}, bson.M{"_id": 1, "text": 1, "name": 1}, &service)
		existAsker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["askerPhone"]}, bson.M{"_id": 1, "name": 1})
		existTasker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["taskerPhone"]}, bson.M{"_id": 1, "name": 1})
		taskDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		if v["date"] != nil {
			if _, ok := v["date"].(string); ok {
				date := strings.Split(v["date"].(string), ",")
				y, _ := strconv.Atoi(date[0])
				m, _ := strconv.Atoi(date[1])
				d, _ := strconv.Atoi(date[2])
				h, _ := strconv.Atoi(date[3])
				mi, _ := strconv.Atoi(date[4])
				taskDate = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
			} else {
				taskDate = v["date"].(time.Time)
			}
		}
		if v["dateTime"] != nil {
			taskDate = v["dateTime"].(time.Time)
		}
		duration := 2
		if v["duration"] != nil {
			duration = v["duration"].(int)
		}
		cost := 200000
		if v["cost"] != nil {
			cost = v["cost"].(int)
		}
		address := "69 D1, Tân Hưng, quận 7, TPHCM"
		if v["address"] != nil {
			address = v["address"].(string)
		}
		status := globalConstant.TASK_STATUS_POSTED
		if v["status"] != nil {
			status = v["status"].(string)
		}
		rated := false
		if v["rated"] != nil {
			rated = v["rated"].(bool)
		}
		homeType := globalConstant.HOME_TYPE_HOME
		if v["homeType"] != nil {
			homeType = v["homeType"].(string)
		}
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			if _, ok := v["createdAt"].(string); ok {
				date := strings.Split(v["createdAt"].(string), ",")
				y, _ := strconv.Atoi(date[0])
				m, _ := strconv.Atoi(date[1])
				d, _ := strconv.Atoi(date[2])
				h, _ := strconv.Atoi(date[3])
				mi, _ := strconv.Atoi(date[4])
				createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
			} else {
				createdAt = cast.ToTime(v["createdAt"])
			}
		}
		countryCode := globalConstant.COUNTRY_CODE_VN
		if v["countryCode"] != nil {
			countryCode = v["countryCode"].(string)
		}
		acceptedTasker := []map[string]interface{}{}
		if v["acceptedTasker"] != nil {
			acceptedTasker = v["acceptedTasker"].([]map[string]interface{})
		}
		payment := map[string]interface{}{
			"method": globalConstant.PAYMENT_METHOD_CASH,
		}
		if v["payment"] != nil {
			payment = v["payment"].(map[string]interface{})
		}
		if existAsker != nil {
			v["contactName"] = existAsker.Name
		}
		if v["askerId"] == nil {
			v["askerId"] = existAsker.XId
		}
		taskItem := map[string]interface{}{
			"serviceId":      service.XId,
			"serviceText":    service.Text,
			"serviceName":    service.Name,
			"description":    v["description"],
			"date":           taskDate,
			"duration":       duration,
			"cost":           cost,
			"address":        address,
			"lat":            10.741432,
			"lng":            106.702033,
			"phone":          v["askerPhone"],
			"createdAt":      createdAt,
			"status":         status,
			"rated":          rated,
			"visibility":     1,
			"payment":        payment,
			"homeType":       homeType,
			"isoCode":        isoCode,
			"acceptedTasker": acceptedTasker,
			"taskPlace":      v["taskPlace"],
			"viewedTaskers":  v["viewedTaskers"],
			"countryCode":    countryCode,
		}
		if v["_id"] != nil {
			taskItem["_id"] = v["_id"]
		} else {
			taskItem["_id"] = globalLib.GenerateObjectId()
		}
		if v["collectionDate"] != nil {
			if _, ok := v["collectionDate"].(string); ok {
				date := strings.Split(v["collectionDate"].(string), ",")
				y, _ := strconv.Atoi(date[0])
				m, _ := strconv.Atoi(date[1])
				d, _ := strconv.Atoi(date[2])
				h, _ := strconv.Atoi(date[3])
				mi, _ := strconv.Atoi(date[4])
				taskItem["collectionDate"] = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
			} else {
				taskItem["collectionDate"] = v["collectionDate"]
			}
		}
		if existTasker != nil {
			taskItem["taskerId"] = existTasker.XId
		}
		if v["scheduleId"] != nil {
			taskItem["scheduleId"] = v["scheduleId"]
		}
		if v["subscriptionId"] != nil {
			taskItem["subscriptionId"] = v["subscriptionId"]
		}
		if v["detailLaundry"] != nil {
			taskItem["detailLaundry"] = v["detailLaundry"]
		}
		if v["isCloseRating"] != nil {
			taskItem["isCloseRating"] = v["isCloseRating"]
		}
		if v["voiceCallHistory"] != nil {
			taskItem["voiceCallHistory"] = v["voiceCallHistory"]
		}
		if v["taskerRated"] != nil {
			taskItem["taskerRated"] = v["taskerRated"]
		}
		if v["detailSofa"] != nil {
			taskItem["detailSofa"] = v["detailSofa"]
		}
		if v["detailHostel"] != nil {
			taskItem["detailHostel"] = v["detailHostel"]
		}
		if v["serviceId"] != nil {
			taskItem["serviceId"] = v["serviceId"]
		}
		if v["serviceText"] != nil {
			taskItem["serviceText"] = v["serviceText"]
		}
		if v["report"] != nil {
			taskItem["report"] = v["report"]
		}
		if v["goMarketDetailTranslate"] != nil {
			taskItem["goMarketDetailTranslate"] = v["goMarketDetailTranslate"]
		}
		if v["goMarketDetail"] != nil {
			taskItem["goMarketDetail"] = v["goMarketDetail"]
		}
		if v["taskNoteTranslated"] != nil {
			taskItem["taskNoteTranslated"] = v["taskNoteTranslated"]
		}
		if v["taskNote"] != nil {
			taskItem["taskNote"] = v["taskNote"]
		}
		if v["askerId"] != nil {
			taskItem["askerId"] = v["askerId"]
		}
		if v["isTaskerRefuse"] != nil {
			taskItem["isTaskerRefuse"] = v["isTaskerRefuse"]
		}
		if v["startWorking"] != nil {
			taskItem["startWorking"] = v["startWorking"]
		}
		if v["costDetail"] != nil {
			taskItem["costDetail"] = v["costDetail"]
		}
		if v["cookingDetail"] != nil {
			taskItem["cookingDetail"] = v["cookingDetail"]
		}
		if v["requirements"] != nil {
			taskItem["requirements"] = v["requirements"]
		}
		if v["contactName"] != nil {
			taskItem["contactName"] = v["contactName"]
		}
		if v["detailDeepCleaning"] != nil {
			taskItem["detailDeepCleaning"] = v["detailDeepCleaning"]
		}
		if v["promotion"] != nil {
			taskItem["promotion"] = v["promotion"]
		}
		if v["maximumTimeAcceptTask"] != nil {
			taskItem["maximumTimeAcceptTask"] = v["maximumTimeAcceptTask"]
		}
		if v["source"] != nil {
			taskItem["source"] = v["source"]
		}
		if v["updatedAt"] != nil {
			taskItem["updatedAt"] = v["updatedAt"]
		}
		if v["cancellationReason"] != nil {
			taskItem["cancellationReason"] = v["cancellationReason"]
		}
		if v["subscriptionId"] != nil {
			taskItem["subscriptionId"] = v["subscriptionId"]
		}
		if v["visibility"] != nil {
			taskItem["visibility"] = v["visibility"]
		}
		if v["requirements"] != nil {
			taskItem["requirements"] = v["requirements"]
		}
		if v["changesHistory"] != nil {
			taskItem["changesHistory"] = v["changesHistory"]
		}
		if v["autoChooseTasker"] != nil {
			taskItem["autoChooseTasker"] = v["autoChooseTasker"]
		}
		if v["lastPostedAlertAt"] != nil {
			taskItem["lastPostedAlertAt"] = v["lastPostedAlertAt"]
		}
		if v["isPrepayTask"] != nil {
			taskItem["isPrepayTask"] = v["isPrepayTask"]
		}
		if v["unset_visibility"] != nil && v["unset_visibility"].(bool) {
			delete(taskItem, "visibility")
		}
		if v["isSendToFavTaskers"] != nil {
			taskItem["isSendToFavTaskers"] = v["isSendToFavTaskers"]
		}
		if v["lastResendAt"] != nil {
			taskItem["lastResendAt"] = v["lastResendAt"]
		}
		if v["isBlacklistAsker"] != nil {
			taskItem["isBlacklistAsker"] = v["isBlacklistAsker"]
		}
		if v["isTetBooking"] != nil {
			taskItem["isTetBooking"] = v["isTetBooking"]
		}
		if v["isPremium"] != nil {
			taskItem["isPremium"] = v["isPremium"]
		}
		if v["newCostDetail"] != nil {
			taskItem["newCostDetail"] = v["newCostDetail"]
		}
		if v["serviceKeyName"] != nil {
			taskItem["serviceName"] = v["serviceKeyName"]
			delete(v, "serviceKeyName")
		}
		if v["fromPartner"] != nil {
			taskItem["fromPartner"] = v["fromPartner"]
		}
		if v["originCurrency"] != nil {
			taskItem["originCurrency"] = v["originCurrency"]
		}
		if v["taskDate"] != nil {
			taskItem["date"] = v["taskDate"]
		}
		if v["requestVatInfo"] != nil {
			taskItem["requestVatInfo"] = v["requestVatInfo"]
		}
		if v["detailOfficeCleaning"] != nil {
			taskItem["detailOfficeCleaning"] = v["detailOfficeCleaning"]
		}
		if v["isTesting"] != nil {
			taskItem["isTesting"] = v["isTesting"]
		}
		if v["relatedTasks"] != nil {
			taskItem["relatedTasks"] = v["relatedTasks"]
		}
		if v["forceTasker"] != nil {
			taskItem["forceTasker"] = v["forceTasker"]
		}
		if v["dateOptions"] != nil {
			taskItem["dateOptions"] = v["dateOptions"]
		}
		if v["increaseDurationData"] != nil {
			taskItem["increaseDurationData"] = v["increaseDurationData"]
		}
		globalDataAccess.InsertOne(collectionTask, taskItem)
		ids = append(ids, taskItem["_id"].(string))

	}
	return ids
}

// ============================= USER

func CreateUser(data []map[string]interface{}) {
	for k, v := range data {
		userId := v["phone"]
		language := "vi"
		if v["language"] != nil {
			language = v["language"].(string)
		}
		favouriteTasker := []string{}
		if v["favouriteTasker"] != nil {
			favouriteTasker = v["favouriteTasker"].([]string)
		}
		backList := []string{}
		if v["blackList"] != nil {
			backList = v["blackList"].([]string)
		}

		avgRating := 2.5
		if v["avgRating"] != nil {
			avgRating = v["avgRating"].(float64)
		}

		referralCode := fmt.Sprintf("%s%d", v["type"].(string), k)
		if v["referralCode"] != nil {
			referralCode = v["referralCode"].(string)
		}

		friendCode := fmt.Sprintf("%s%s", v["type"].(string), v["phone"].(string))
		if v["friendCode"] != nil {
			friendCode = v["friendCode"].(string)
		}

		noReceiveNotification := true
		if v["noReceiveNotification"] != nil {
			noReceiveNotification = v["noReceiveNotification"].(bool)
		}

		taskDone := 2
		if v["taskDone"] != nil {
			taskDone = v["taskDone"].(int)
		}

		totalTaskDone := 2
		if v["totalTaskDone"] != nil {
			totalTaskDone = v["totalTaskDone"].(int)
		}

		createdAt := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		if _, ok := v["createdAt"].(string); ok {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		} else if _, ok := v["createdAt"].(time.Time); ok {
			createdAt = v["createdAt"].(time.Time)
		}
		status := globalConstant.USER_STATUS_ACTIVE
		if v["status"] != nil {
			status = v["status"].(string)
		}
		nDoneTaskInMonth := 0
		if v["nDoneTaskInMonth"] != nil {
			nDoneTaskInMonth = v["nDoneTaskInMonth"].(int)
		}
		user := map[string]interface{}{
			"username":              v["phone"],
			"type":                  v["type"],
			"status":                status,
			"isoCode":               globalConstant.ISO_CODE_VN,
			"createdAt":             createdAt,
			"phone":                 v["phone"],
			"name":                  v["name"],
			"address":               "104 Mai Thi Luu.",
			"countryCode":           "+84",
			"nDoneTaskInMonth":      nDoneTaskInMonth,
			"language":              language,
			"referralCode":          referralCode,
			"friendCode":            friendCode,
			"favouriteTasker":       favouriteTasker,
			"blackList":             backList,
			"avgRating":             avgRating,
			"noReceiveNotification": noReceiveNotification,
			"taskDone":              taskDone,
			"totalTaskDone":         totalTaskDone,
			"score":                 v["score"],
			"scoreRate":             v["scoreRate"],
			"lastDoneTask":          globalLib.GetCurrentTime(local.TimeZone),
		}
		if v["type"] == globalConstant.USER_TYPE_TASKER && v["workingPlaces"] == nil {
			user["workingPlaces"] = []map[string]interface{}{
				{
					"city": "Hồ Chí Minh",
				},
			}
		}
		if v["isoCode"] != nil {
			user["isoCode"] = v["isoCode"]
		}
		if v["countryCode"] != nil {
			user["countryCode"] = v["countryCode"]
		}
		if v["locations"] != nil {
			user["locations"] = v["locations"]
		}
		fAccountId := globalLib.GenerateObjectId()
		if v["fAccountId"] != nil {
			fAccountId = v["fAccountId"].(string)
		} else {
			globalDataAccess.InsertOne(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], bson.M{"_id": fAccountId, "FMainAccount": 0, "Promotion": 0})
		}
		if v["taskCancelByTasker"] != nil {
			user["taskCancelByTasker"] = v["taskCancelByTasker"]
		}
		if v["emails"] != nil {
			user["emails"] = v["emails"]
		}
		if v["idNumber"] != nil {
			user["idNumber"] = v["idNumber"]
		}
		if v["point"] != nil {
			user["point"] = v["point"]
		}
		if v["rankInfo"] != nil {
			user["rankInfo"] = v["rankInfo"]
		}
		if v["lastPostedTask"] != nil {
			user["lastPostedTask"] = v["lastPostedTask"]
		}
		if v["workingPlaces"] != nil {
			user["workingPlaces"] = v["workingPlaces"]
		}
		if v["freeSchedule"] != nil {
			user["freeSchedule"] = v["freeSchedule"]
		}
		if v["employeeIds"] != nil {
			user["employeeIds"] = v["employeeIds"]
		}
		if v["voiceCallToken"] != nil {
			user["voiceCallToken"] = v["voiceCallToken"]
		}
		if v["company"] != nil {
			user["company"] = v["company"]
		}
		if v["level"] != nil {
			user["level"] = v["level"]
		}
		if v["cancelBlockedAt"] != nil {
			user["cancelBlockedAt"] = v["cancelBlockedAt"]
		}
		if v["trustPoint"] != nil {
			user["trustPoint"] = v["trustPoint"]
		}
		if v["taskNote"] != nil {
			user["taskNote"] = v["taskNote"]
		}
		if v["isBlacklist"] != nil {
			user["isBlacklist"] = v["isBlacklist"]
		}
		if v["lastDoneTask"] != nil {
			user["lastDoneTask"] = v["lastDoneTask"]
		}
		if v["notComeLockNumber"] != nil {
			user["notComeLockNumber"] = v["notComeLockNumber"]
		}
		if v["lastActiveAt"] != nil {
			user["lastActiveAt"] = v["lastActiveAt"]
		}
		if v["lastLockedAt"] != nil {
			user["lastLockedAt"] = v["lastLockedAt"]
		}
		if v["dob"] != nil {
			user["dob"] = v["dob"]
		}
		if v["badges"] != nil {
			user["badges"] = v["badges"]
		}
		if v["isPremiumTasker"] != nil {
			user["isPremiumTasker"] = v["isPremiumTasker"]
		}
		if v["bTaskeeVoucher"] != nil {
			user["bTaskeeVoucher"] = v["bTaskeeVoucher"]
		}
		if v["taskerPremiumOption"] != nil {
			user["taskerPremiumOption"] = v["taskerPremiumOption"]
		}
		if v["favouritedAsker"] != nil {
			user["favouritedAsker"] = v["favouritedAsker"]
		}
		if v["cleaningOption"] != nil {
			user["cleaningOption"] = v["cleaningOption"]
		}
		if v["isReadyTaskerPremium"] != nil {
			user["isReadyTaskerPremium"] = v["isReadyTaskerPremium"]
		}
		if v["isOldUser"] != nil {
			user["isOldUser"] = v["isOldUser"]
		}
		if v["rankInfoByCountry"] != nil {
			user["rankInfoByCountry"] = v["rankInfoByCountry"]
		}
		if v["bPoint"] != nil {
			user["bPoint"] = v["bPoint"]
		}
		if v["language"] != nil {
			user["language"] = v["language"]
		}
		if v["suggestSubscription"] != nil {
			user["suggestSubscription"] = v["suggestSubscription"]
		}
		if v["journeyInfo"] != nil {
			user["journeyInfo"] = v["journeyInfo"]
		}
		if v["resetRankHistory"] != nil {
			user["resetRankHistory"] = v["resetRankHistory"]
		}
		if v["firstVerifyAt"] != nil {
			user["firstVerifyAt"] = v["firstVerifyAt"].(time.Time)
		}
		if v["isIndefiniteLocking"] != nil {
			user["isIndefiniteLocking"] = v["isIndefiniteLocking"]
		}
		if v["address"] != nil {
			user["address"] = v["address"]
		}
		if v["changeHistory"] != nil {
			user["changeHistory"] = v["changeHistory"]
			user["changeHistory"] = v["changeHistory"]
		}
		if v["workingPlaces"] != nil {
			user["workingPlaces"] = v["workingPlaces"]
		}
		if v["friendCode"] != nil {
			user["friendCode"] = friendCode
		}
		if v["avatar"] != nil {
			user["avatar"] = v["avatar"]
		}
		if v["workingPlaces"] != nil {
			user["workingPlaces"] = v["workingPlaces"]
		}
		isoCode, _ := v["isoCode"].(string)
		if isoCode == globalConstant.ISO_CODE_TH {
			user["countryCode"] = globalConstant.COUNTRY_CODE_TH
		}
		user["fAccountId"] = fAccountId
		user["_id"] = userId
		modelUser.InsertOne(local.ISO_CODE, user)
		if v["type"].(string) == globalConstant.USER_TYPE_TASKER {
			serviceName := globalConstant.SERVICE_KEY_NAME_HOME_CLEANING
			if v["serviceName"] != nil {
				serviceName = v["serviceName"].(string)
			}
			var service *modelService.Service
			globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": serviceName}, bson.M{"_id": 1}, &service)
			globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": service.XId}, bson.M{"$push": bson.M{"taskerList": userId}})
		}
	}
}

// ======================= AKSER_RATINGS

func CreateAskerRating(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		existAsker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["askerPhone"]}, bson.M{"_id": 1, "name": 1})
		existTasker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["taskerPhone"]}, bson.M{"_id": 1, "name": 1})
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["createdAt"] = createdAt
		v["_id"] = globalLib.GenerateObjectId()
		v["askerId"] = existAsker.XId
		v["taskerId"] = "DEFAULT_RATING"
		if existTasker != nil {
			v["taskerId"] = existTasker.XId
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_ASKER_RATING[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ======================= SETTINGS
// UpdateSettings func update data Collection SettingSystem
func UpdateSettings(data interface{}) {
	globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, data)
}

func CreateChatConversation(data []map[string]interface{}) []string {
	var ids []string

	messagesByChatid := map[string][]map[string]interface{}{}
	for _, v := range data {
		existAsker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["askerPhone"]}, bson.M{"_id": 1, "name": 1})
		existTasker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["taskerPhone"]}, bson.M{"_id": 1, "name": 1})
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["createdAt"] = createdAt

		// Generate chatId
		chatId := ""
		if v["_id"] == nil {
			chatId = globalLib.GenerateObjectId()
			v["_id"] = chatId
		} else {
			chatId = v["_id"].(string)
		}
		ids = append(ids, chatId)
		v["askerId"] = existAsker.XId
		v["askerName"] = existAsker.Name
		v["taskerId"] = existTasker.XId
		v["taskerName"] = existTasker.Name

		// Get data messages to insert to another collection and delete from this collection (Not to insert messages to this collection)
		if v["messages"] != nil {
			for _, vMessages := range cast.ToSlice(v["messages"]) {
				message := toMap(vMessages)
				message["chatId"] = chatId

				if messagesByChatid[chatId] == nil {
					messagesByChatid[chatId] = []map[string]interface{}{}
				}
				messagesByChatid[chatId] = append(messagesByChatid[chatId], message)
			}
			delete(v, "messages")
		}

		// Insert conversation to this collection
		globalDataAccess.InsertOne(globalCollection.COLLECTION_CHAT_CONVERSATION[globalConstant.ISO_CODE_VN], v)
	}

	// Insert messages to another collection
	if len(messagesByChatid) > 0 {
		for chatId, messages := range messagesByChatid {
			CreateChatMessage(chatId, messages)
		}
	}

	return ids
}

func CreateChatMessage(chatId string, data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		if v["chatId"] == nil {
			v["chatId"] = chatId
		}
		if v["createdAt"] != nil {
			v["createdAt"] = cast.ToTime(v["createdAt"])
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_CHAT_MESSAGE[globalConstant.ISO_CODE_VN], insertData)
}

// func convert interface to map
func toMap(data interface{}) map[string]interface{} {
	var result map[string]interface{}
	b, _ := json.Marshal(data)
	json.Unmarshal(b, &result)
	return result
}

func CreateJourneyLeaderBoard(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			id := globalLib.GenerateObjectId()
			v["_id"] = id
		}
		if v["createdAt"] == nil {
			v["createdAt"] = globalLib.GetCurrentTime(local.TimeZone)
		}
		ids = append(ids, v["_id"].(string))
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_JOURNEY_LEADER_BOARD[local.ISO_CODE], insertData)
	return ids
}

func CreateJourneyLeaderBoardBK(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			id := globalLib.GenerateObjectId()
			v["_id"] = id
		}
		if v["createdAt"] == nil {
			v["createdAt"] = globalLib.GetCurrentTime(local.TimeZone)
		}
		ids = append(ids, v["_id"].(string))
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_JOURNEY_LEADER_BOARD_BACKUP[local.ISO_CODE], insertData)
	return ids
}

// ======================= RATINGS

func CreateRating(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		existAsker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["askerPhone"]}, bson.M{"_id": 1, "name": 1})
		existTasker, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": v["taskerPhone"]}, bson.M{"_id": 1, "name": 1})
		createdAt := globalLib.GetCurrentTime(local.TimeZone)
		if v["createdAt"] != nil {
			date := strings.Split(v["createdAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			createdAt = time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
		}
		v["createdAt"] = createdAt

		if v["updatedAt"] != nil {
			date := strings.Split(v["updatedAt"].(string), ",")
			y, _ := strconv.Atoi(date[0])
			m, _ := strconv.Atoi(date[1])
			d, _ := strconv.Atoi(date[2])
			h, _ := strconv.Atoi(date[3])
			mi, _ := strconv.Atoi(date[4])
			updatedAt := time.Date(y, time.Month(m), d, h, mi, 0, 0, local.TimeZone)
			v["updatedAt"] = updatedAt
		}

		v["_id"] = globalLib.GenerateObjectId()
		v["askerId"] = existAsker.XId
		v["taskerId"] = existTasker.XId
		globalDataAccess.InsertOne(globalCollection.COLLECTION_RATING[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

func AddTaskersToServiceChannel(isoCode string, serviceNames []string, taskerList []string) {
	var services []*modelService.Service
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE[isoCode], bson.M{"name": bson.M{"$in": serviceNames}}, bson.M{"_id": 1}, &services)
	for _, v := range services {
		globalDataAccess.UpsertOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[isoCode], bson.M{"serviceId": v.XId}, bson.M{"$addToSet": bson.M{"taskerList": bson.M{"$each": taskerList}}})
	}
}

func CreateSpecialCampaign(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}

		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN[globalConstant.ISO_CODE_VN], insertData)
}

func CreateSpecialCampaignTransaction(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}

		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[globalConstant.ISO_CODE_VN], insertData)
}

func CreateNotification(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}

		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_NOTIFICATION[globalConstant.ISO_CODE_VN], insertData)
}
func GetChatConversationsWithMessages(isoCode string, query, fields bson.M) ([]*chatMessage.ChatMessage, error) {
	// 1. Get chat conversation
	chatConversations, err := pkgChatMessage.GetChatConversations(isoCode, query, fields)
	if err != nil {
		return nil, err
	}

	// 2. Get chat messages
	for _, v := range chatConversations {
		chatMessageFields := parseChatMessageFields(fields)
		messages, err := pkgChatMessage.GetChatMessages(isoCode, bson.M{"chatId": v.XId}, chatMessageFields)
		if err != nil {
			return nil, err
		}
		v.Messages = messages
	}

	return chatConversations, nil
}

func parseChatMessageFields(chatConversationFields bson.M) bson.M {
	// If chatConversationFields is nil or empty -> Its mean get all fields (include messages' fields)
	if len(chatConversationFields) == 0 {
		return bson.M{}
	}

	// Get fields of message in original fields
	result := bson.M{}
	for field := range chatConversationFields {
		// This field mean get all fields of messages
		if field == "messages" {
			return bson.M{}
		}

		// Cut messages. prefix because we move message to another collection
		if value, ok := strings.CutPrefix(field, "messages."); ok {
			result[value] = 1
		}
	}
	return result
}

func CreateTaskerGameCampaign(data map[string]interface{}) []string {
	data["_id"] = globalLib.GenerateObjectId()
	if data["status"] == nil {
		data["status"] = globalConstant.GAME_CAMPAIGN_STATUS_ACTIVE
	}
	globalDataAccess.InsertOne(globalCollection.COLLECTION_TASKER_GAME_CAMPAIGN[local.ISO_CODE], data)
	return []string{data["_id"].(string)}
}

func CreateTaskerUserGameCampaign(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_TASKER_USER_GAME_CAMPAIGN[local.ISO_CODE], insertData)
	return ids
}

func CreateBusiness(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], insertData)
}

func CreateBusinessLevel(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_BUSINESS_LEVEL[local.ISO_CODE], insertData)
}

func CreateBusinessMember(data []map[string]interface{}) {
	userMember := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		userMember = append(userMember, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_BUSINESS_MEMBER[local.ISO_CODE], userMember)
}

func CreateBusinessMemberTransactions(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_BUSINESS_MEMBER_TRANSACTION[local.ISO_CODE], insertData)
}

func CreateBusinessTransactions(data []map[string]interface{}) {
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_BUSINESS_TRANSACTION[local.ISO_CODE], insertData)
}

func CreatePointTransaction(data []map[string]interface{}) []string {
	insertData := []interface{}{}
	ids := []string{}
	for _, v := range data {
		if v["_id"] == nil {
			id := globalLib.GenerateObjectId()
			v["_id"] = id
		}
		ids = append(ids, v["_id"].(string))
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], insertData)
	return ids
}

func CreateTaskerReward(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		ids = append(ids, v["_id"].(string))
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_REWARD[local.ISO_CODE], insertData)
	return ids
}

func CreateTaskerPointTransaction(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		ids = append(ids, v["_id"].(string))
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_TASKER_POINT_TRANSACTION[local.ISO_CODE], insertData)
	return ids
}

// ==================================== Community Tags
func CreateTags(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_COMMUNITY_TAG[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ==================================== Community User
func CreateCommunityUser(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_COMMUNITY_USER[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

// ==================================== Community Post
func CreateCommunityPost(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		status := globalConstant.COMMUNITY_POST_STATUS_ACTIVE
		if v["status"] == nil {
			v["status"] = status
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_COMMUNITY_POST[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}
	return ids
}

func CreateMedal(data []map[string]interface{}) []string {
	var ids []string

	for _, v := range data {
		if v["_id"] == nil {
			v["_id"] = globalLib.GenerateObjectId()
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_COMMUNITY_MEDAL[local.ISO_CODE], v)
		ids = append(ids, v["_id"].(string))
	}

	return ids
}

func CreateCommunityNotifications(data []map[string]interface{}, isoCode string) []string {
	var ids []string
	notifications := []interface{}{}
	for i, v := range data {
		if v["createdAt"] == nil {
			v["createdAt"] = globalLib.GetCurrentTime(local.TimeZone).Add(time.Duration(-i) * time.Minute)
		}
		v["_id"] = globalLib.GenerateObjectId()
		ids = append(ids, v["_id"].(string))
		notifications = append(notifications, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_COMMUNITY_NOTIFICATION[isoCode], notifications)
	return ids
}

func CreateCourseStartDate(data []map[string]interface{}) []string {
	insertData := []interface{}{}
	var ids []string
	for _, v := range data {
		if v["_id"] == nil || v["_id"].(string) == "" {
			v["_id"] = globalLib.GenerateObjectId()
			ids = append(ids, v["_id"].(string))
		}
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_TRAINING_TASKER_COURSE_START_DATE[globalConstant.ISO_CODE_VN], insertData)
	return ids
}

// ======================= Training Tasker Course
func CreateTrainingTaskerCourse(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		courseItem := map[string]interface{}{}
		if v["_id"] != nil {
			courseItem["_id"] = v["_id"]
		} else {
			courseItem["_id"] = globalLib.GenerateObjectId()
		}
		if v["title"] != nil {
			courseItem["title"] = v["title"]
		}
		if v["description"] != nil {
			courseItem["description"] = v["description"]
		}
		if v["code"] != nil {
			courseItem["code"] = v["code"]
		}

		if v["type"] != nil {
			courseItem["type"] = v["type"]
		}

		if v["maximumNumberOfRetries"] != nil {
			courseItem["maximumNumberOfRetries"] = v["maximumNumberOfRetries"]
		}

		if v["percentageToPass"] != nil {
			courseItem["percentageToPass"] = v["percentageToPass"]
		}

		if v["deadlineIn"] != nil {
			courseItem["deadlineIn"] = v["deadlineIn"]
		}

		if v["isDisplayAllAnswer"] != nil {
			courseItem["isDisplayAllAnswer"] = v["isDisplayAllAnswer"]
		}

		if v["status"] != nil {
			courseItem["status"] = v["status"]
		}

		if v["isRecommended"] != nil {
			courseItem["isRecommended"] = v["isRecommended"]
		}
		if v["relatedServices"] != nil {
			courseItem["relatedServices"] = v["relatedServices"]
		}

		if v["quizCollections"] != nil {
			courseItem["quizCollections"] = v["quizCollections"]
		}

		if v["condition"] != nil {
			courseItem["condition"] = v["condition"]
		}

		if v["timeToCompleteByMinutes"] != nil {
			courseItem["timeToCompleteByMinutes"] = v["timeToCompleteByMinutes"]
		}

		if v["displayPosition"] != nil {
			courseItem["displayPosition"] = v["displayPosition"]
		}
		if v["createdAt"] != nil {
			courseItem["createdAt"] = v["createdAt"]
		}
		if v["updatedAt"] != nil {
			courseItem["updatedAt"] = v["updatedAt"]
		}
		if v["cities"] != nil {
			courseItem["cities"] = v["cities"]
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_TRAINING_TASKER_COURSE[globalConstant.ISO_CODE_VN], courseItem)
		ids = append(ids, courseItem["_id"].(string))
	}
	return ids
}

// ======================= Training Tasker Submission
func CreateTrainingTaskerSubmission(data []map[string]interface{}) []string {
	var ids []string
	for _, v := range data {
		courseItem := map[string]interface{}{}
		if v["_id"] != nil {
			courseItem["_id"] = v["_id"]
		} else {
			courseItem["_id"] = globalLib.GenerateObjectId()
		}

		if v["numberOfSubmissions"] != nil {
			courseItem["numberOfSubmissions"] = v["numberOfSubmissions"]
		}

		if v["course"] != nil {
			courseItem["course"] = v["course"]
		}

		if v["displayPosition"] != nil {
			courseItem["displayPosition"] = v["displayPosition"]
		}

		if v["taskerId"] != nil {
			courseItem["taskerId"] = v["taskerId"]
		}

		if v["status"] != nil {
			courseItem["status"] = v["status"]
		}

		if v["createdAt"] != nil {
			courseItem["createdAt"] = v["createdAt"]
		}

		if v["changeHistories"] != nil {
			courseItem["changeHistories"] = v["changeHistories"]
		}

		globalDataAccess.InsertOne(globalCollection.COLLECTION_TRAINING_TASKER_SUBMISSION[globalConstant.ISO_CODE_VN], courseItem)
		ids = append(ids, courseItem["_id"].(string))
	}
	return ids
}

//============================= Service

func GetService(query interface{}, fields interface{}) *modelService.Service {
	var service *modelService.Service
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[globalConstant.ISO_CODE_VN], query, fields, &service)
	return service
}

// ======================= Service Channel
func UpdateServiceChannel(query interface{}, data interface{}) {
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[globalConstant.ISO_CODE_VN], query, data)
}

func CreateFinancialAccount(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], insertData)
	return ids
}

func CreateComboVoucher(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_COMBO_VOUCHER[local.ISO_CODE], insertData)
	return ids
}

func CreateUserComboVoucher(data []map[string]interface{}) []string {
	ids := []string{}
	insertData := []interface{}{}
	for _, v := range data {
		id := globalLib.GenerateObjectId()
		ids = append(ids, id)
		v["_id"] = id
		insertData = append(insertData, v)
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_USER_COMBO_VOUCHER[local.ISO_CODE], insertData)
	return ids
}

func CreateFATransaction(data []map[string]interface{}) []string {
	var ids []string
	insertData := []interface{}{}
	for _, v := range data {
		v["_id"] = globalLib.GenerateObjectId()
		insertData = append(insertData, v)
		ids = append(ids, v["_id"].(string))
	}
	globalDataAccess.InsertAll(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], insertData)
	return ids
}
