package modelEventConfig

import "google.golang.org/protobuf/types/known/timestamppb"

type EventConfig struct {
	XId            string                 `json:"_id,omitempty" bson:"_id,omitempty"`
	Name           string                 `json:"name,omitempty" bson:"name,omitempty"`
	Status         string                 `json:"status,omitempty" bson:"status,omitempty"`
	StartDate      *timestamppb.Timestamp `json:"startDate,omitempty" bson:"startDate,omitempty"`
	EndDate        *timestamppb.Timestamp `json:"endDate,omitempty" bson:"endDate,omitempty"`
	ActiveServices []string               `json:"activeServices,omitempty" bson:"activeServices,omitempty"`
	AppVersion     string                 `json:"appVersion,omitempty" bson:"appVersion,omitempty"`
	CreatedAt      *timestamppb.Timestamp `json:"createdAt,omitempty" bson:"createdAt,omitempty"`
	AppConfig      map[string]interface{} `json:"appConfig,omitempty" bson:"appConfig,omitempty"`
	PaymentMethods []string               `json:"paymentMethods,omitempty" bson:"paymentMethods,omitempty"`
}
