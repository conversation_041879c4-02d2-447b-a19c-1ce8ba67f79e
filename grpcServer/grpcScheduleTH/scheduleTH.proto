syntax = "proto3";
package grpcScheduleTH;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcScheduleTH";

import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response/response.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskSchedule/taskSchedule.proto";

service ScheduleTH {
  rpc AddHomeCleaningSchedule (taskSchedule.TaskSchedule) returns (response.Response) {}
  rpc AddHomeCookingSchedule (taskSchedule.TaskSchedule) returns (response.Response) {}
  rpc AddHousekeepingSchedule (taskSchedule.TaskSchedule) returns (response.Response) {}
  rpc AddOfficeCleaningSchedule (taskSchedule.TaskSchedule) returns (response.Response) {}
}