package grpcPricingTH

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPricingTH"
	"google.golang.org/grpc"
)

func ConnectGRPCPricingTH(endpoint string) (grpcPricingTH.PricingTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPricingTH.NewPricingTHClient(connect)
	return client, connect, err
}
