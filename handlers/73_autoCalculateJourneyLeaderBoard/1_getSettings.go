package autoCalculateJourneyLeaderBoard

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelJourneySetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/journeySetting"
	"go.mongodb.org/mongo-driver/bson"
)

// type LevelSetting map[CityName]map[LevelName]*modelJourneySetting.JourneySettingLevel
type LevelSetting map[string]map[string]*modelJourneySetting.JourneySettingLevel

func getJourneySettings() (citySupportJourney []string, levelSetting LevelSetting, defaultLevelSetting map[string]*modelJourneySetting.JourneySettingLevel, startLevelByCity map[string]string, err error) {
	defaultCity := "Hồ Chí Minh"
	var settings []*modelJourneySetting.JourneySetting
	err = globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_JOURNEY_SETTING[local.ISO_CODE], bson.M{"status": globalConstant.JOURNEY_SETTING_STATUS_ACTIVE, "isTesting": bson.M{"$ne": true}}, bson.M{"levels.name": 1, "levels.title": 1, "levels.icon": 1, "levels.text": 1, "cityName": 1}, &settings)
	if len(settings) == 0 || err != nil {
		err = fmt.Errorf("getJourneySettings - can not get setting: %v", err)
		return
	}

	levelSetting = make(LevelSetting)
	startLevelByCity = make(map[string]string)
	// Get config
	for _, city := range settings {
		// list city
		citySupportJourney = append(citySupportJourney, city.CityName)

		// list level
		levelSetting[city.CityName] = make(map[string]*modelJourneySetting.JourneySettingLevel)
		for _, level := range city.Levels {
			levelSetting[city.CityName][level.Name] = level

			if _, ok := startLevelByCity[city.CityName]; !ok {
				startLevelByCity[city.CityName] = level.Name
			}
		}
		defaultLevelSetting = levelSetting[defaultCity]
	}
	return citySupportJourney, levelSetting, defaultLevelSetting, startLevelByCity, nil
}
