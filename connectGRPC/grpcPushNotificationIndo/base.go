package grpcPushNotificationIndo

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPushNotificationIndo"
	"google.golang.org/grpc"
)

func ConnectGRPCPushNotification(endpoint string) (grpcPushNotificationIndo.PushNotificationIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPushNotificationIndo.NewPushNotificationIndoClient(connect)
	return client, connect, err
}
