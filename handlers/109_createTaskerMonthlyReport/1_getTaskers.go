package createTaskerMonthlyReport

import (
	"fmt"
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccessV2"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getTaskerCh(w *sync.WaitGroup, reqBody *model.RequestAction) chan *modelUser.Users {
	var page int64 = 1
	var errCount, totalTasker, doneCount int
	askerCh := make(chan *modelUser.Users, LIMIT)
	var runUsers []string
	if reqBody != nil {
		runUsers = reqBody.RunUsers
	}
	go func() {
		defer w.Done()
		// Close channel khi đã get xong data users
		defer close(askerCh)
		for {
			// Get data users theo page tăng gần
			askersData, err := getTaskers(page, runUsers)
			if err == nil {
				for _, user := range askersData {
					// If is company tasker, not owner, skip
					if user.Company != nil && len(user.EmployeeIds) == 0 {
						continue
					}
					askerCh <- user
				}
				if len(askersData) > 0 {
					totalTasker += len(askersData)
					// tăng số page lên +1
					page++
					errCount = 0
					doneCount = 0
					if totalTasker%1000 == 0 {
						msg := fmt.Sprintf("CreateTaskerMonthlyReportVN Đã chạy xong monthly report cho %d asker", totalTasker)
						globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
					}
				} else {
					msg := fmt.Sprintf("CreateTaskerMonthlyReportVN data trả về danh sách asker 0 - page:%v", page)
					globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
					doneCount++
					if doneCount >= 2 {
						msg := fmt.Sprintf("CreateTaskerMonthlyReportVN đã chạy xong monthly report cho %d asker", totalTasker)
						globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
						// Break for khi đã lấy đủ data
						break
					}
				}
			} else {
				errCount++
				if errCount >= 5 {
					errCount = 0
					msg := fmt.Sprintf("CreateTaskerMonthlyReportVN Get data asker for monthly report - page:%v error: %v", page, err)
					globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
					break
				}
			}
		}
	}()
	// Trả về channel userCh
	return askerCh
}

func getTaskers(page int64, userIds []string) ([]*modelUser.Users, error) {
	// Get data askers
	query := bson.M{
		"type":    globalConstant.USER_TYPE_TASKER,
		"status":  bson.M{"$in": []string{globalConstant.USER_STATUS_ACTIVE, globalConstant.USER_STATUS_BLOCKED}},
		"isoCode": globalConstant.ISO_CODE_VN,
	}
	if len(userIds) > 0 {
		query = bson.M{
			"_id": bson.M{"$in": userIds},
		}
	}
	taskers, err := modelUser.GetAll(globalConstant.ISO_CODE_VN,
		query,
		bson.M{"badges": 1, "isPremiumTasker": 1, "avgRating": 1, "company": 1, "employeeIds": 1, "lastDoneTask": 1, "status": 1, "lastOnline": 1},
		&globalDataAccessV2.QueryOptions{Page: page, Limit: LIMIT},
	)
	return taskers, err
}

func isTaskerCompanyOwner(user *modelUser.Users) bool {
	if user.Company != nil && len(user.EmployeeIds) > 0 {
		return true
	}
	return false
}
