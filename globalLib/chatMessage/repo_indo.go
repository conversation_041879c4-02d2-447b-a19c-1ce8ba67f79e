package chatMessage

import (
	"errors"
	"time"

	"github.com/jinzhu/copier"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/indo"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

type RepoINDO struct{}

var rINDO Repo = &RepoINDO{}

// Deprecated: Use GetChatConversation and GetChatMessages instead
// NOTE: Support get chat task conversation with message. Old version
func (r *RepoINDO) GetChatConversationWithMessages(isoCode string, query, fields bson.M) (*modelChatMessage.ChatMessage, error) {
	// 1. Get chat conversation
	chatConversation, err := r.GetChatConversation(isoCode, query, fields)
	if err != nil {
		return nil, err
	}

	// 2. Get chat messages
	if chatConversation != nil {
		chatMessageFields := parseChatMessageFields(fields)
		messages, err := r.GetChatMessagesSortMap(isoCode, bson.M{"chatId": chatConversation.XId}, chatMessageFields, bson.M{"createdAt": 1})
		if err != nil {
			return nil, err
		}
		chatConversation.Messages = convertToMessages(messages)
	}

	return chatConversation, nil
}

func (r *RepoINDO) GetChatConversation(isoCode string, query, fields bson.M) (*modelChatMessage.ChatMessage, error) {
	var chatConversation *modelChatMessage.ChatMessage
	// 1. Get chat message
	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_CHAT_CONVERSATION[isoCode], query, fields, &chatConversation)
	if err != nil {
		return nil, err
	}
	return chatConversation, nil
}

func (r *RepoINDO) GetChatConversations(isoCode string, query, fields bson.M) ([]*modelChatMessage.ChatMessage, error) {
	var chatConversations []*modelChatMessage.ChatMessage
	// 1. Get chat message
	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_CHAT_CONVERSATION[isoCode], query, fields, &chatConversations)
	if err != nil {
		return nil, err
	}
	return chatConversations, nil
}

func (r *RepoINDO) GetChatMessages(isoCode string, query, fields bson.M) (messages []*modelChatMessage.ChatMessageMessages, err error) {
	var messagesMap []map[string]interface{}
	messagesMap, err = globalDataAccess.GetAllByQueryMap(globalCollection.COLLECTION_CHAT_MESSAGE[isoCode], query, fields)
	if err != nil {
		return nil, err
	}
	messages = convertToMessages(messagesMap)
	return
}

func (r *RepoINDO) GetChatMessage(isoCode string, query, fields bson.M) (message *modelChatMessage.ChatMessageMessages, err error) {
	var messageMap map[string]interface{}
	messageMap, err = globalDataAccess.GetOneByQueryMap(globalCollection.COLLECTION_CHAT_MESSAGE[isoCode], query, fields)
	if err != nil {
		return nil, err
	}
	message = ConvertToMessage(messageMap)
	return
}

func (r *RepoINDO) GetChatMessageMap(isoCode string, query, fields bson.M) (message map[string]interface{}, err error) {
	message, err = globalDataAccess.GetOneByQueryMap(globalCollection.COLLECTION_CHAT_MESSAGE[isoCode], query, fields)
	return
}

func (r *RepoINDO) GetChatMessagesSortMap(isoCode string, query, fields, sort bson.M) (messages []map[string]interface{}, err error) {
	messages, err = globalDataAccess.GetAllByQuerySortMap(globalCollection.COLLECTION_CHAT_MESSAGE[isoCode], query, fields, sort)
	if err != nil {
		return nil, err
	}
	return
}

func (r *RepoINDO) GetChatMessagesSort(isoCode string, query, fields, sort bson.M) (messages []*modelChatMessage.ChatMessageMessages, err error) {
	var messagesMap []map[string]interface{}
	messagesMap, err = globalDataAccess.GetAllByQuerySortMap(globalCollection.COLLECTION_CHAT_MESSAGE[isoCode], query, fields, sort)
	if err != nil {
		return nil, err
	}
	messages = convertToMessages(messagesMap)
	return
}

func (r *RepoINDO) GetChatMessagesSkipLimitSort(isoCode string, query, fields bson.M, skip, limit int64, sort bson.M) (messages []*modelChatMessage.ChatMessageMessages, err error) {
	var messagesMap []map[string]interface{}
	err = globalDataAccess.GetAllByQuerySkipLimitSort(globalCollection.COLLECTION_CHAT_MESSAGE[isoCode], query, fields, skip, limit, sort, &messagesMap)
	if err != nil {
		return nil, err
	}
	messages = convertToMessages(messagesMap)
	return
}

// UPDATE ==============================================================================================================
func (r *RepoINDO) UpdateChatMessages(isoCode string, query, fields bson.M) (int64, error) {
	return globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_CHAT_MESSAGE[isoCode], query, fields)
}

func (r *RepoINDO) UpdateChatMessage(isoCode string, query, fields bson.M) (int64, error) {
	return globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_CHAT_MESSAGE[isoCode], query, fields)
}

func (r *RepoINDO) UpdateChatConversation(isoCode string, query, fields bson.M) (int64, error) {
	return globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_CHAT_CONVERSATION[isoCode], query, fields)
}

// CREATE ==============================================================================================================
func (r *RepoINDO) SendMessageToConversation(isoCode string, chatId string, messages []*modelChatMessage.ChatMessageMessages) error {
	insertData := []interface{}{}
	for _, v := range messages {
		v.ChatId = chatId
		insertData = append(insertData, v)
	}
	err := globalDataAccess.InsertAll(globalCollection.COLLECTION_CHAT_MESSAGE[isoCode], insertData)
	if err != nil {
		return err
	}

	// Update chat conversation
	_, err = r.UpdateChatConversation(
		isoCode,
		bson.M{"_id": chatId},
		bson.M{
			"$set": bson.M{
				"lastMessageAt": globalLib.GetCurrentTime(globalLib.GetTimeZone()),
				"lastMessage":   messages[len(messages)-1],
			},
			"$unset": bson.M{
				"archivedData": 1,
			},
		},
	)
	return err
}

func (r *RepoINDO) SendMessageMapToConversation(isoCode string, chatId string, messages []map[string]interface{}) error {
	insertData := []interface{}{}
	for _, v := range messages {
		v["chatId"] = chatId
		insertData = append(insertData, v)
	}
	err := globalDataAccess.InsertAll(globalCollection.COLLECTION_CHAT_MESSAGE[isoCode], insertData)
	if err != nil {
		return err
	}

	// Update chat conversation
	_, err = r.UpdateChatConversation(
		isoCode,
		bson.M{"_id": chatId},
		bson.M{
			"$set": bson.M{
				"lastMessageAt": globalLib.GetCurrentTime(globalLib.GetTimeZone()),
				"lastMessage":   messages[len(messages)-1],
			},
			"$unset": bson.M{
				"archivedData": 1,
			},
		},
	)
	return err
}

func (r *RepoINDO) CreateChatConversation(isoCode string, chatMessage *modelChatMessage.ChatMessage) error {
	return globalDataAccess.InsertOne(globalCollection.COLLECTION_CHAT_CONVERSATION[isoCode], chatMessage)
}

func (r *RepoINDO) DeleteChatMessages(isoCode string, query bson.M) error {
	if len(query) == 0 {
		return errors.New("query is empty")
	}
	return globalDataAccess.DeleteAllByQuery(isoCode, query)
}

func (r *RepoINDO) GetChatMessagesPagingSort(isoCode string, query, fields bson.M, page, limit int64, sort bson.M) ([]*modelChatMessage.ChatMessage, error) {
	var chatMessages []*modelChatMessage.ChatMessage
	err := globalDataAccess.GetAllByQueryPagingSort(globalCollection.COLLECTION_CHAT_CONVERSATION[isoCode], query, fields, page, limit, sort, &chatMessages)
	return chatMessages, err
}

func (r *RepoINDO) InsertChatMessage(isoCode string, chatMessage interface{}) error {
	return globalDataAccess.InsertOne(globalCollection.COLLECTION_CHAT_MESSAGE[isoCode], chatMessage)
}

func (r *RepoINDO) CountMessagesByQuery(isoCode string, query bson.M) (int, error) {
	countUnreadMessage, err := globalDataAccess.CountByQuery(globalCollection.COLLECTION_CHAT_MESSAGE[isoCode], query)
	return int(countUnreadMessage), err
}

// Send list messages to list chat
func (r *RepoINDO) SendMessageToConversations(isoCode string, chatIds []string, messages []*modelChatMessage.ChatMessageMessages) error {
	insertData := []interface{}{}
	for _, chatId := range chatIds {
		for _, v := range messages {
			message := &modelChatMessage.ChatMessageMessages{}
			copier.Copy(message, v)
			message.XId = globalLib.GenerateObjectId()
			message.ChatId = chatId
			insertData = append(insertData, message)
		}
	}
	err := globalDataAccess.InsertAll(globalCollection.COLLECTION_CHAT_MESSAGE[isoCode], insertData)
	if err != nil {
		return err
	}

	// Update chat conversation
	_, err = r.UpdateChatConversations(
		isoCode,
		bson.M{"_id": bson.M{"$in": chatIds}},
		bson.M{
			"$set": bson.M{
				"lastMessageAt": globalLib.GetCurrentTime(globalLib.GetTimeZone()),
				"lastMessage":   messages[len(messages)-1],
			},
			"$unset": bson.M{
				"archivedData": 1,
			},
		},
	)
	return err
}

func (r *RepoINDO) UpdateChatConversations(isoCode string, query, fields bson.M) (int64, error) {
	return globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_CHAT_CONVERSATION[isoCode], query, fields)
}
func (r *RepoINDO) CreateFirstMessageForFavChat(isoCode string, asker, tasker *modelUsers.Users) (string, error) {
	chatId := globalLib.GenerateObjectId()
	dataChatMessageAsker := &modelChatMessage.ChatMessage{
		XId:        chatId,
		AskerId:    asker.XId,
		AskerName:  asker.Name,
		TaskerId:   tasker.XId,
		TaskerName: tasker.Name,
		Members: []*modelChatMessage.ChatMessageMember{
			{
				XId:    asker.XId,
				Name:   asker.Name,
				Type:   asker.Type,
				Avatar: asker.Avatar,
			},
			{
				XId:    tasker.XId,
				Name:   tasker.Name,
				Type:   tasker.Type,
				Avatar: tasker.Avatar,
			},
		},
		CreatedAt: globalLib.GetCurrentTimestamp(globalLib.GetTimeZone()),
	}
	err := r.CreateChatConversation(isoCode, dataChatMessageAsker)
	if err != nil {
		return "", err
	}
	messageToAsker := &modelChatMessage.ChatMessageMessages{
		XId:       globalLib.GenerateObjectId(),
		From:      globalConstant.CHAT_MESSAGE_FROM_SYSTEM,
		ChatId:    chatId,
		CreatedAt: globalLib.GetCurrentTimestamp(globalLib.GetTimeZone()),
		MessageBySystem: &modelChatMessage.ChatMessageMessagesMessageBySystem{
			Title:  localization.GetLocalizeObject("FAVOURITE_CHAT_NEW_MESSAGE_ASKER_TITLE", tasker.Name),
			Text:   localization.GetLocalizeObject("FAVOURITE_CHAT_NEW_MESSAGE_ASKER_BODY"),
			SendTo: globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_ASKER,
			Image:  "https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/notificationImages/Ah9BWjtPDp7fGaRYv",
			Key:    globalConstant.MESSAGE_KEY_WELCOME,
			Actions: []*modelChatMessage.ChatMessageMessagesMessageBySystemActions{
				{
					Type:  globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_TYPE_PRIMARY,
					Title: localization.GetLocalizeObject("BOOK_TASK_NOW"),
					Key:   globalConstant.KEY_BOOK_WITH_FAV,
				},
			},
		},
	}

	// Insert new message
	err = r.SendMessageToConversation(isoCode, chatId, []*modelChatMessage.ChatMessageMessages{messageToAsker})
	if err != nil {
		return "", err
	}

	return chatId, nil
}

func (r *RepoINDO) SendMessageToFavChat(params FavMessageParams) (*modelChatMessage.ChatMessageMessages, error) {
	if params.ChatId == "" || params.Title == nil || params.Text == nil || params.SendTo == "" || params.IsoCode == "" || params.TimeZone == nil {
		return nil, errors.New("missing required params")
	}
	chatId := params.ChatId
	var message *modelChatMessage.ChatMessageMessages
	message, err := r.SendFavMessage(params.SendTo, params.Task, chatId, params.Title, params.Text, params.IsoCode, params.TimeZone, params.Actions, params.Status)
	return message, err
}

func (r *RepoINDO) SendFavMessage(sendTo string, task *modelTask.Task, chatId string, title, text *modelService.ServiceText, isoCode string, timeZone *time.Location, actions []*modelChatMessage.ChatMessageMessagesMessageBySystemActions, status string) (*modelChatMessage.ChatMessageMessages, error) {
	//create chat message
	message := &modelChatMessage.ChatMessageMessages{
		ChatId: chatId,
		XId:    globalLib.GenerateObjectId(),
		From:   globalConstant.CHAT_MESSAGE_FROM_SYSTEM,
		MessageBySystem: &modelChatMessage.ChatMessageMessagesMessageBySystem{
			Title:  title,
			Text:   text,
			SendTo: sendTo,
			Key:    globalConstant.KEY_BOOK_WITH_FAV,
		},
		CreatedAt: globalLib.GetCurrentTimestamp(timeZone),
	}
	if task != nil {
		message.TaskRequestData = &modelChatMessage.ChatMessageMessagesTaskRequestData{
			TaskInfo: &modelChatMessage.ChatMessageMessagesTaskRequestDataInfo{
				TaskId:      task.XId,
				District:    task.TaskPlace.District,
				Duration:    task.Duration,
				ServiceText: task.ServiceText,
			},
		}
		if len(task.DateOptions) > 0 {
			for _, option := range task.DateOptions {
				dateOption := &modelChatMessage.TaskDateOptions{
					XId:  option.XId,
					Date: option.Date,
				}
				message.TaskRequestData.TaskInfo.DateOptions = append(message.TaskRequestData.TaskInfo.DateOptions, dateOption)
			}
		} else {
			dateOption := &modelChatMessage.TaskDateOptions{
				Date: task.Date,
			}
			message.TaskRequestData.TaskInfo.DateOptions = append(message.TaskRequestData.TaskInfo.DateOptions, dateOption)
		}
		if status != "" {
			message.TaskRequestData.Status = status
		}
	}

	if len(actions) > 0 {
		message.MessageBySystem.Actions = actions
	}

	// Insert new message
	err := r.SendMessageToConversation(isoCode, chatId, []*modelChatMessage.ChatMessageMessages{message})
	if err != nil {
		return nil, err
	}
	return message, nil
}
