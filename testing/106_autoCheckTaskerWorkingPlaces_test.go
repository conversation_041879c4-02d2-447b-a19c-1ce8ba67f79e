package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
)

func TestAutoCheckTaskerWorkingPlaces(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		apiURL := apiSyncCron + "/auto-check-tasker-working-places"
		ResetData()

		CreateUser([]map[string]interface{}{
			{ // Asker -> Not process
				"phone":   "0834567890",
				"name":    "<PERSON><PERSON> 01",
				"type":    globalConstant.USER_TYPE_ASKER,
				"isoCode": local.ISO_CODE,
			},
			{ // Tasker TH -> Not process
				"phone":       "0834567880",
				"name":        "Tasker 00",
				"type":        globalConstant.USER_TYPE_TASKER,
				"countryCode": globalConstant.COUNTRY_CODE_TH,
				"isoCode":     globalConstant.ISO_CODE_TH,
			},
			{ // Tasker HCM -> Process
				"phone":       "0834567881",
				"name":        "Tasker 01",
				"type":        globalConstant.USER_TYPE_TASKER,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"isoCode":     local.ISO_CODE,
				"workingPlaces": []map[string]interface{}{
					{
						"city":     "Hồ Chí Minh",
						"district": "Quận 1",
					},
					{
						"city":     "Hồ Chí Minh",
						"district": "Quận 2",
					},
					{
						"city":     "Hồ Chí Minh",
						"district": "Quận 3",
					},
				},
			},
			{ // Tasker HCM + BD -> Process
				"phone":       "0834567882",
				"name":        "Tasker 02",
				"type":        globalConstant.USER_TYPE_TASKER,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"isoCode":     local.ISO_CODE,
				"workingPlaces": []map[string]interface{}{
					{
						"city":     "Hồ Chí Minh",
						"district": "Quận 1",
					},
					{
						"city":     "Hồ Chí Minh",
						"district": "Quận 2",
					},
					{
						"city":     "Bình Dương",
						"district": "Thủ Dầu Một",
					},
					{
						"city":     "Bình Dương",
						"district": "Bến Cát",
					},
					{
						"city":     "Bình Dương",
						"district": "Thuận An",
					},
				},
			},
			{ // Tasker BD -> Process
				"phone":       "0834567883",
				"name":        "Tasker 03",
				"type":        globalConstant.USER_TYPE_TASKER,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"isoCode":     local.ISO_CODE,
				"workingPlaces": []map[string]interface{}{
					{
						"city":     "Bình Dương",
						"district": "Thủ Dầu Một",
					},
					{
						"city":     "Bình Dương",
						"district": "Bến Cát",
					},
				},
			},
			{ // Tasker HN -> Process
				"phone":       "0834567884",
				"name":        "Tasker 04",
				"type":        globalConstant.USER_TYPE_TASKER,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"isoCode":     local.ISO_CODE,
				"workingPlaces": []map[string]interface{}{
					{
						"city":     "Hà Nội",
						"district": "Hoàng Mai",
					},
				},
			},
			{ // Tasker HN -> Process
				"phone":       "0834567885",
				"name":        "Tasker 05",
				"type":        globalConstant.USER_TYPE_TASKER,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"isoCode":     local.ISO_CODE,
				"workingPlaces": []map[string]interface{}{
					{
						"city":     "Hà Nội",
						"district": "Hoàng Mai",
					},
					{
						"city":     "Hà Nội",
						"district": "Hoàn Kiếm",
					},
				},
			},
			{ // Tasker Long An có 2 huyện -> Process (Không post)
				"phone":       "0834567886",
				"name":        "Tasker 06",
				"type":        globalConstant.USER_TYPE_TASKER,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"isoCode":     local.ISO_CODE,
				"workingPlaces": []map[string]interface{}{
					{
						"city":     "Long An",
						"district": "Hoàng Mai",
					},
					{
						"city":     "Long An",
						"district": "Hoàn Kiếm",
					},
				},
			},
			{ // Tasker Long An có 1 huyện -> Process (Post)
				"phone":       "0834567887",
				"name":        "Tasker 07",
				"type":        globalConstant.USER_TYPE_TASKER,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"isoCode":     local.ISO_CODE,
				"workingPlaces": []map[string]interface{}{
					{
						"city":     "Long An",
						"district": "Hoàng Mai",
					},
				},
			},
			{ // Tasker Thừa Thiên Huế có 1 huyện -> Process (Không Post)
				"phone":       "0834567888",
				"name":        "Tasker 08",
				"type":        globalConstant.USER_TYPE_TASKER,
				"countryCode": globalConstant.COUNTRY_CODE_VN,
				"isoCode":     local.ISO_CODE,
				"workingPlaces": []map[string]interface{}{
					{
						"city":     "Thừa Thiên Huế",
						"district": "Hoàng Mai",
					},
				},
			},
		})

		body := map[string]interface{}{
			"action": lib.RUN_NOW,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					resultMap := map[string]interface{}{}
					b, _ := io.ReadAll(res.Body)
					json.Unmarshal(b, &resultMap)

					resultMapData := map[string][]map[string]interface{}{}
					b, _ = json.Marshal(resultMap["data"])
					json.Unmarshal(b, &resultMapData)

					So(resultMap["totalTaskerLessThan3Area"], ShouldEqual, 5)

					So(len(resultMapData["Hồ Chí Minh"]), ShouldEqual, 1)
					for _, tasker := range resultMapData["Hồ Chí Minh"] {
						taskerPhone := cast.ToString(tasker["taskerPhone"])
						taskerName := cast.ToString(tasker["taskerName"])
						workingPlaces := cast.ToSlice(tasker["workingAreas"])

						switch taskerPhone {
						case "0834567882":
							So(taskerName, ShouldEqual, "Tasker 02")
							So(len(workingPlaces), ShouldEqual, 2)
							for _, district := range workingPlaces {
								So(district, ShouldBeIn, []string{"Quận 1", "Quận 2"})
							}
						default:
							panic("invalid tasker phone for Hồ Chí Minh")
						}
					}

					So(len(resultMapData["Bình Dương"]), ShouldEqual, 1)
					for _, tasker := range resultMapData["Bình Dương"] {
						taskerPhone := cast.ToString(tasker["taskerPhone"])
						taskerName := cast.ToString(tasker["taskerName"])
						workingPlaces := cast.ToSlice(tasker["workingAreas"])

						switch taskerPhone {
						case "0834567883":
							So(taskerName, ShouldEqual, "Tasker 03")
							So(len(workingPlaces), ShouldEqual, 2)
							for _, district := range workingPlaces {
								So(district, ShouldBeIn, []string{"Thủ Dầu Một", "Bến Cát"})
							}
						default:
							panic("invalid tasker phone for Bình Dương")
						}
					}

					So(len(resultMapData["Hà Nội"]), ShouldEqual, 2)
					for _, tasker := range resultMapData["Hà Nội"] {
						taskerPhone := cast.ToString(tasker["taskerPhone"])
						taskerName := cast.ToString(tasker["taskerName"])
						workingPlaces := cast.ToSlice(tasker["workingAreas"])

						switch taskerPhone {
						case "0834567884":
							So(taskerName, ShouldEqual, "Tasker 04")
							So(len(workingPlaces), ShouldEqual, 1)
							for _, district := range workingPlaces {
								So(district, ShouldBeIn, []string{"Hoàng Mai"})
							}
						case "0834567885":
							So(taskerName, ShouldEqual, "Tasker 05")
							So(len(workingPlaces), ShouldEqual, 2)
							for _, district := range workingPlaces {
								So(district, ShouldBeIn, []string{"Hoàng Mai", "Hoàn Kiếm"})
							}
						default:
							panic("invalid tasker phone for Hà Nội")
						}
					}

					So(len(resultMapData["Long An"]), ShouldEqual, 1)
					for _, tasker := range resultMapData["Long An"] {
						taskerPhone := cast.ToString(tasker["taskerPhone"])
						taskerName := cast.ToString(tasker["taskerName"])
						workingPlaces := cast.ToSlice(tasker["workingAreas"])

						switch taskerPhone {
						case "0834567887":
							So(taskerName, ShouldEqual, "Tasker 07")
							So(len(workingPlaces), ShouldEqual, 1)
							for _, district := range workingPlaces {
								So(district, ShouldBeIn, []string{"Hoàng Mai"})
							}
						default:
							panic("invalid tasker phone for Long An")
						}
					}

					So(resultMapData["Thừa Thiên Huế"], ShouldBeNil)
				})
			})
		})
	})
}
