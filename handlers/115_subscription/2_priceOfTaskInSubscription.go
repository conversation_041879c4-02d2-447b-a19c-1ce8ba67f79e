package subscription

import (
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelPricingResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse"
	modelSubscription "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscription"
)

// priceOfTaskInSubscription calculates the price for a task in subscription - STEP 4: Price Calculation
func priceOfTaskInSubscription(subs *modelSubscription.Subscription, date time.Time) *modelSubscription.SubscriptionPricing {
	remainRate := 1.0 - subs.Discount
	if remainRate <= 0 {
		remainRate = 1.0
	}

	// Check pricing array for existing pricing
	if subs.CostDetail != nil && len(subs.CostDetail.Pricing) > 0 {
		for _, pricing := range subs.CostDetail.Pricing {
			if isSameDateTime(pricing.Date.AsTime(), date) && remainRate > 0 && remainRate < 1 {
				pricing.CostDetail.Cost = globalLib.RoundMoney(pricing.CostDetail.Cost*remainRate, local.ISO_CODE)
				pricing.CostDetail.FinalCost = globalLib.RoundMoney(pricing.CostDetail.FinalCost*remainRate, local.ISO_CODE)
				return pricing
			}
		}
	}

	// Default calculation
	price := globalLib.RoundMoney(subs.Price/float64(len(subs.Schedule)), local.ISO_CODE)
	return &modelSubscription.SubscriptionPricing{
		Date:     globalLib.ParseTimestampFromDate(date),
		Duration: subs.Duration,
		CostDetail: &modelPricingResponse.CostResult{
			BaseCost:  globalLib.RoundMoney(price/remainRate, local.ISO_CODE),
			Cost:      price,
			FinalCost: price,
			Duration:  subs.Duration,
		},
	}
}

// isSameDateTime checks if two times are the same down to the minute
func isSameDateTime(t1, t2 time.Time) bool {
	return t1.Year() == t2.Year() &&
		t1.Month() == t2.Month() &&
		t1.Day() == t2.Day() &&
		t1.Hour() == t2.Hour() &&
		t1.Minute() == t2.Minute()
}
