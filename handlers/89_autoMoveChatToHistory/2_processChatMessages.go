package autoMoveChatToHistory

import (
	"fmt"
	"sync"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.mongodb.org/mongo-driver/bson"
)

// moveChatMessagesToHistory retrieves chat data for each task from the task channel and returns a channel of chat.
func processChatMessages(w *sync.WaitGroup, chatCh chan string) chan string {
	// Create a channel to store the output data
	outDataCh := make(chan string, LIMIT)

	go func() {
		defer w.Done()
		defer close(outDataCh)

		var chatIds []string
		// get channel chat
		for chat := range chatCh {
			chatIds = append(chatIds, chat)
			if len(chatIds) >= int(LIMIT) {
				moveChatMessagesToHistory(chatIds)
				// Send chats to the output channel
				for _, c := range chatIds {
					outDataCh <- c
				}
				chatIds = []string{}
			}
		}
		// Check if there are remaining chat IDs
		if len(chatIds) > 0 {
			moveChatMessagesToHistory(chatIds)
			// Send chats to the output channel
			for _, c := range chatIds {
				outDataCh <- c
			}
		}
	}()

	return outDataCh
}

func moveChatMessagesToHistory(chatIds []string) {
	// match condition
	matchCondition := bson.M{
		"$match": bson.M{
			"chatId": bson.M{"$in": chatIds},
		},
	}
	// merge condition (merge or insert)
	mergeCondition := bson.M{
		"$merge": bson.M{
			"into":           globalCollection.COLLECTION_HISTORY_CHAT_MESSAGES[local.ISO_CODE],
			"on":             "_id",
			"whenMatched":    "merge",
			"whenNotMatched": "insert",
		},
	}
	// move data to history
	var results []interface{}
	err := globalDataAccess.Aggregate(globalCollection.COLLECTION_CHAT_MESSAGE[local.ISO_CODE], []bson.M{matchCondition, mergeCondition}, &results)
	if err != nil {
		// Post to slack
		globalLib.PostToSlack(cfg.SlackToken, lib.GO_SERVICE_CHANNEL, "bTaskee System", fmt.Sprintf("AutoMoveChatToHistory-%s: Move chatMessage to history - error: %v - chatIds: %v", local.ISO_CODE, err, chatIds))
	}
	// else {
	// 	// Delete chat messages
	// 	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_CHAT_MESSAGE[local.ISO_CODE], bson.M{"chatId": bson.M{"$in": chatIds}})
	// }
}
