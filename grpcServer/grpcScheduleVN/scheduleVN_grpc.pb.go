// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcScheduleVN

import (
	context "context"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	taskSchedule "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskSchedule"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// ScheduleVNClient is the client API for ScheduleVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ScheduleVNClient interface {
	AddHomeCleaningSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error)
	AddHomeCookingSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error)
	AddHousekeepingSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error)
	AddOfficeCleaningSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error)
}

type scheduleVNClient struct {
	cc grpc.ClientConnInterface
}

func NewScheduleVNClient(cc grpc.ClientConnInterface) ScheduleVNClient {
	return &scheduleVNClient{cc}
}

func (c *scheduleVNClient) AddHomeCleaningSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcScheduleVN.ScheduleVN/AddHomeCleaningSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleVNClient) AddHomeCookingSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcScheduleVN.ScheduleVN/AddHomeCookingSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleVNClient) AddHousekeepingSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcScheduleVN.ScheduleVN/AddHousekeepingSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleVNClient) AddOfficeCleaningSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcScheduleVN.ScheduleVN/AddOfficeCleaningSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ScheduleVNServer is the server API for ScheduleVN service.
// All implementations must embed UnimplementedScheduleVNServer
// for forward compatibility
type ScheduleVNServer interface {
	AddHomeCleaningSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error)
	AddHomeCookingSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error)
	AddHousekeepingSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error)
	AddOfficeCleaningSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error)
	mustEmbedUnimplementedScheduleVNServer()
}

// UnimplementedScheduleVNServer must be embedded to have forward compatible implementations.
type UnimplementedScheduleVNServer struct {
}

func (UnimplementedScheduleVNServer) AddHomeCleaningSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddHomeCleaningSchedule not implemented")
}
func (UnimplementedScheduleVNServer) AddHomeCookingSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddHomeCookingSchedule not implemented")
}
func (UnimplementedScheduleVNServer) AddHousekeepingSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddHousekeepingSchedule not implemented")
}
func (UnimplementedScheduleVNServer) AddOfficeCleaningSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddOfficeCleaningSchedule not implemented")
}
func (UnimplementedScheduleVNServer) mustEmbedUnimplementedScheduleVNServer() {}

// UnsafeScheduleVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ScheduleVNServer will
// result in compilation errors.
type UnsafeScheduleVNServer interface {
	mustEmbedUnimplementedScheduleVNServer()
}

func RegisterScheduleVNServer(s *grpc.Server, srv ScheduleVNServer) {
	s.RegisterService(&_ScheduleVN_serviceDesc, srv)
}

func _ScheduleVN_AddHomeCleaningSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskSchedule.TaskSchedule)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleVNServer).AddHomeCleaningSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcScheduleVN.ScheduleVN/AddHomeCleaningSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleVNServer).AddHomeCleaningSchedule(ctx, req.(*taskSchedule.TaskSchedule))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleVN_AddHomeCookingSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskSchedule.TaskSchedule)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleVNServer).AddHomeCookingSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcScheduleVN.ScheduleVN/AddHomeCookingSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleVNServer).AddHomeCookingSchedule(ctx, req.(*taskSchedule.TaskSchedule))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleVN_AddHousekeepingSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskSchedule.TaskSchedule)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleVNServer).AddHousekeepingSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcScheduleVN.ScheduleVN/AddHousekeepingSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleVNServer).AddHousekeepingSchedule(ctx, req.(*taskSchedule.TaskSchedule))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleVN_AddOfficeCleaningSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskSchedule.TaskSchedule)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleVNServer).AddOfficeCleaningSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcScheduleVN.ScheduleVN/AddOfficeCleaningSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleVNServer).AddOfficeCleaningSchedule(ctx, req.(*taskSchedule.TaskSchedule))
	}
	return interceptor(ctx, in, info, handler)
}

var _ScheduleVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcScheduleVN.ScheduleVN",
	HandlerType: (*ScheduleVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddHomeCleaningSchedule",
			Handler:    _ScheduleVN_AddHomeCleaningSchedule_Handler,
		},
		{
			MethodName: "AddHomeCookingSchedule",
			Handler:    _ScheduleVN_AddHomeCookingSchedule_Handler,
		},
		{
			MethodName: "AddHousekeepingSchedule",
			Handler:    _ScheduleVN_AddHousekeepingSchedule_Handler,
		},
		{
			MethodName: "AddOfficeCleaningSchedule",
			Handler:    _ScheduleVN_AddOfficeCleaningSchedule_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "scheduleVN.proto",
}
