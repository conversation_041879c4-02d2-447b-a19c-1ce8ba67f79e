package autoCheckTaskerWorkingPlaces

import (
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccessV2"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

const CHUNK_SIZE_GET_TASKERS = 1000

func getTaskers() ([]*modelUser.Users, []string) {
	var taskers []*modelUser.Users
	var page int64 = 1
	tryCount := 0
	var errList []string

	for {
		tryCount++

		// Do collection users co index {{"type" : 1.0, "countryCode" : 1.0, "createdAt" : -1.0} nên dùng countryCode thay cho isoCode
		chunk, err := modelUser.GetAll(local.ISO_CODE,
			bson.M{
				"type":        globalConstant.USER_TYPE_TASKER,
				"countryCode": globalConstant.ISO_CODE_MAP_COUNTRY_CODE[local.ISO_CODE],
				"status":      globalConstant.USER_STATUS_ACTIVE,
			},
			bson.M{"_id": 1, "workingPlaces": 1, "name": 1, "phone": 1},
			&globalDataAccessV2.QueryOptions{
				Page:  page,
				Limit: CHUNK_SIZE_GET_TASKERS,
				Sort:  bson.M{"createdAt": -1},
			},
		)
		if err != nil && err != mongo.ErrNoDocuments {
			if tryCount < 3 {
				// Sleep then retry this page
				time.Sleep(100 * time.Millisecond)
				continue
			}

			errList = append(errList, err.Error())
			return nil, errList
		}

		// No more tasker
		if len(chunk) == 0 {
			break
		}

		// Filter tasker by city
		taskers = append(taskers, chunk...)
		page++
		tryCount = 0
	}
	return taskers, nil
}
