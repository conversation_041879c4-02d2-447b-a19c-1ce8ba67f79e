// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcPaymentTH

import (
	context "context"
	payment2C2PTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/payment2C2PTransaction"
	paymentRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentRequest"
	paymentTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentTransaction"
	refundTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/refundTransaction"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	shopeePayTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/shopeePayTransaction"
	vatRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/vatRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PaymentTHClient is the client API for PaymentTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PaymentTHClient interface {
	SubscriptionPaymentTH(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error)
	ChargePaymentTH(ctx context.Context, in *paymentTransaction.PaymentTransaction, opts ...grpc.CallOption) (*response.Response, error)
	RefundTH(ctx context.Context, in *refundTransaction.RefundTransaction, opts ...grpc.CallOption) (*response.Response, error)
	CheckTransactionStatus2C2P(ctx context.Context, in *payment2C2PTransaction.Payment2C2PTransaction, opts ...grpc.CallOption) (*response.Response, error)
	CheckTransactionStatusShopeePayTH(ctx context.Context, in *shopeePayTransaction.ShopeePayTransaction, opts ...grpc.CallOption) (*response.Response, error)
	CreateGiftByComboVoucherTH(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error)
	CreateTHVatRequest(ctx context.Context, in *vatRequest.VatRequest, opts ...grpc.CallOption) (*response.Response, error)
}

type paymentTHClient struct {
	cc grpc.ClientConnInterface
}

func NewPaymentTHClient(cc grpc.ClientConnInterface) PaymentTHClient {
	return &paymentTHClient{cc}
}

func (c *paymentTHClient) SubscriptionPaymentTH(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentTH.PaymentTH/SubscriptionPaymentTH", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentTHClient) ChargePaymentTH(ctx context.Context, in *paymentTransaction.PaymentTransaction, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentTH.PaymentTH/ChargePaymentTH", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentTHClient) RefundTH(ctx context.Context, in *refundTransaction.RefundTransaction, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentTH.PaymentTH/RefundTH", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentTHClient) CheckTransactionStatus2C2P(ctx context.Context, in *payment2C2PTransaction.Payment2C2PTransaction, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentTH.PaymentTH/CheckTransactionStatus2C2P", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentTHClient) CheckTransactionStatusShopeePayTH(ctx context.Context, in *shopeePayTransaction.ShopeePayTransaction, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentTH.PaymentTH/CheckTransactionStatusShopeePayTH", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentTHClient) CreateGiftByComboVoucherTH(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentTH.PaymentTH/CreateGiftByComboVoucherTH", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentTHClient) CreateTHVatRequest(ctx context.Context, in *vatRequest.VatRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentTH.PaymentTH/CreateTHVatRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PaymentTHServer is the server API for PaymentTH service.
// All implementations must embed UnimplementedPaymentTHServer
// for forward compatibility
type PaymentTHServer interface {
	SubscriptionPaymentTH(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error)
	ChargePaymentTH(context.Context, *paymentTransaction.PaymentTransaction) (*response.Response, error)
	RefundTH(context.Context, *refundTransaction.RefundTransaction) (*response.Response, error)
	CheckTransactionStatus2C2P(context.Context, *payment2C2PTransaction.Payment2C2PTransaction) (*response.Response, error)
	CheckTransactionStatusShopeePayTH(context.Context, *shopeePayTransaction.ShopeePayTransaction) (*response.Response, error)
	CreateGiftByComboVoucherTH(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error)
	CreateTHVatRequest(context.Context, *vatRequest.VatRequest) (*response.Response, error)
	mustEmbedUnimplementedPaymentTHServer()
}

// UnimplementedPaymentTHServer must be embedded to have forward compatible implementations.
type UnimplementedPaymentTHServer struct {
}

func (UnimplementedPaymentTHServer) SubscriptionPaymentTH(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubscriptionPaymentTH not implemented")
}
func (UnimplementedPaymentTHServer) ChargePaymentTH(context.Context, *paymentTransaction.PaymentTransaction) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChargePaymentTH not implemented")
}
func (UnimplementedPaymentTHServer) RefundTH(context.Context, *refundTransaction.RefundTransaction) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RefundTH not implemented")
}
func (UnimplementedPaymentTHServer) CheckTransactionStatus2C2P(context.Context, *payment2C2PTransaction.Payment2C2PTransaction) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckTransactionStatus2C2P not implemented")
}
func (UnimplementedPaymentTHServer) CheckTransactionStatusShopeePayTH(context.Context, *shopeePayTransaction.ShopeePayTransaction) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckTransactionStatusShopeePayTH not implemented")
}
func (UnimplementedPaymentTHServer) CreateGiftByComboVoucherTH(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGiftByComboVoucherTH not implemented")
}
func (UnimplementedPaymentTHServer) CreateTHVatRequest(context.Context, *vatRequest.VatRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTHVatRequest not implemented")
}
func (UnimplementedPaymentTHServer) mustEmbedUnimplementedPaymentTHServer() {}

// UnsafePaymentTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PaymentTHServer will
// result in compilation errors.
type UnsafePaymentTHServer interface {
	mustEmbedUnimplementedPaymentTHServer()
}

func RegisterPaymentTHServer(s *grpc.Server, srv PaymentTHServer) {
	s.RegisterService(&_PaymentTH_serviceDesc, srv)
}

func _PaymentTH_SubscriptionPaymentTH_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentRequest.PaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentTHServer).SubscriptionPaymentTH(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentTH.PaymentTH/SubscriptionPaymentTH",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentTHServer).SubscriptionPaymentTH(ctx, req.(*paymentRequest.PaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentTH_ChargePaymentTH_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentTransaction.PaymentTransaction)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentTHServer).ChargePaymentTH(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentTH.PaymentTH/ChargePaymentTH",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentTHServer).ChargePaymentTH(ctx, req.(*paymentTransaction.PaymentTransaction))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentTH_RefundTH_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(refundTransaction.RefundTransaction)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentTHServer).RefundTH(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentTH.PaymentTH/RefundTH",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentTHServer).RefundTH(ctx, req.(*refundTransaction.RefundTransaction))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentTH_CheckTransactionStatus2C2P_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(payment2C2PTransaction.Payment2C2PTransaction)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentTHServer).CheckTransactionStatus2C2P(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentTH.PaymentTH/CheckTransactionStatus2C2P",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentTHServer).CheckTransactionStatus2C2P(ctx, req.(*payment2C2PTransaction.Payment2C2PTransaction))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentTH_CheckTransactionStatusShopeePayTH_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(shopeePayTransaction.ShopeePayTransaction)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentTHServer).CheckTransactionStatusShopeePayTH(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentTH.PaymentTH/CheckTransactionStatusShopeePayTH",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentTHServer).CheckTransactionStatusShopeePayTH(ctx, req.(*shopeePayTransaction.ShopeePayTransaction))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentTH_CreateGiftByComboVoucherTH_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentRequest.PaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentTHServer).CreateGiftByComboVoucherTH(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentTH.PaymentTH/CreateGiftByComboVoucherTH",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentTHServer).CreateGiftByComboVoucherTH(ctx, req.(*paymentRequest.PaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentTH_CreateTHVatRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(vatRequest.VatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentTHServer).CreateTHVatRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentTH.PaymentTH/CreateTHVatRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentTHServer).CreateTHVatRequest(ctx, req.(*vatRequest.VatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PaymentTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcPaymentTH.PaymentTH",
	HandlerType: (*PaymentTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SubscriptionPaymentTH",
			Handler:    _PaymentTH_SubscriptionPaymentTH_Handler,
		},
		{
			MethodName: "ChargePaymentTH",
			Handler:    _PaymentTH_ChargePaymentTH_Handler,
		},
		{
			MethodName: "RefundTH",
			Handler:    _PaymentTH_RefundTH_Handler,
		},
		{
			MethodName: "CheckTransactionStatus2C2P",
			Handler:    _PaymentTH_CheckTransactionStatus2C2P_Handler,
		},
		{
			MethodName: "CheckTransactionStatusShopeePayTH",
			Handler:    _PaymentTH_CheckTransactionStatusShopeePayTH_Handler,
		},
		{
			MethodName: "CreateGiftByComboVoucherTH",
			Handler:    _PaymentTH_CreateGiftByComboVoucherTH_Handler,
		},
		{
			MethodName: "CreateTHVatRequest",
			Handler:    _PaymentTH_CreateTHVatRequest_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "paymentTH.proto",
}
