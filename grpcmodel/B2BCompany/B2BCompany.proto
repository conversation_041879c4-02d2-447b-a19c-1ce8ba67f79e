syntax = "proto3";
package B2BCompany;

import "google/protobuf/timestamp.proto";

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcmodel/B2BCompany";

message B2BCompany {
  // @inject_tag: bson:"_id,omitempty"
  string _id = 1;
  // @inject_tag: bson:"name,omitempty"
  string name = 2;
  // @inject_tag: bson:"phone,omitempty"
  string phone = 3;
  // @inject_tag: bson:"listEmployee,omitempty"
  repeated string listEmployee = 4;
  // @inject_tag: bson:"monthlySubsidies,omitempty"
  double monthlySubsidies = 5;
  // @inject_tag: bson:"status,omitempty"
  string status = 6;
  // @inject_tag: bson:"balance,omitempty"
  double balance = 7;
  // @inject_tag: bson:"createdAt,omitempty"
  google.protobuf.Timestamp createdAt = 8;
}