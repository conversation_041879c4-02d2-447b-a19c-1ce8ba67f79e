/*
 * @File: errorCodes.go
 * @Description: Define errorCode for service
 * @CreatedAt: 21/08/2020
 * @Author: linhnh
 */
package lib

import (
	"net/http"

	globalResponse "gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

var ERROR_CODE_CLIENT = http.StatusBadRequest
var ERROR_CODE_SERVER = http.StatusInternalServerError
var ERROR_CODE_NOT_FOUND = http.StatusNotFound

var ERROR_CAN_NOT_PARSE_API_PARAMS = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "CAN_NOT_PARSE_API_PARAMS", Message: "Server can not parse api parameters"}

var ERROR_SERVICE_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "SERVICE_ID_REQUIRED", Message: "Service id is required"}
var ERROR_BOOKING_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "BOOKING_ID_REQUIRED", Message: "Booking id is required"}
var ERROR_BOOKING_ISOCODE_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "BOOKING_ISOCODE_REQUIRED", Message: "Booking isoCode is required"}

var ERROR_SERVICE_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "SERVICE_NOT_FOUND", Message: "Service not found"}

var ERROR_UNKNOWN = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "UNKNOWN_ERROR", Message: "Unknown error"}
var ERROR_WEBSOCKET_ERROR = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "WEBSOCKET_ERROR", Message: "Websocket error"}
