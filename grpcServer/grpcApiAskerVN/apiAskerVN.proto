syntax = "proto3";
package grpcApiAskerVN;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcApiAskerVN";

import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/redeemGiftRequest/redeemGiftRequest.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/redeemGiftResponse/redeemGiftResponse.proto";

service ApiAskerVN {
  rpc RedeemGift (redeemGiftRequest.RedeemGiftRequest) returns (redeemGiftResponse.RedeemGiftResponse) {}
}