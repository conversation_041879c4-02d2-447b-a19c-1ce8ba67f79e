import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import _ from 'lodash';
import {
  revertTimeZone,
  trackingScheduleStatus,
  changeTimeZone,
  postToSlack,
  sendNotificationByIds,
} from '/server/helper-function';

const hourByMilSec = 60 * 60 * 1000;

/**
 * To push New Asker post task after register 24h:
 * 
 * createdAt: >= last 48h: Sign up from Sun, Mon -> Send on Tue. Sign up from Thu, Fri -> Send on Sat
 * type: ASKER
 * status: ACTIVE
 * lastPostedTask: null
 * isSentPromotion24: false
 * 
 */ 
const findNewAskerNotPostTask = () => {
  return Meteor.appCollection.users.find({
    createdAt: {
      $gte: moment().subtract(24, 'hours').startOf('hour').toDate(),
      $lte: moment().subtract(12, 'hours').endOf('hour').toDate()
    },
    type: 'ASKER',
    status: 'ACTIVE',
    lastPostedTask: null,
    isSentPromotion24: null
  }, {fields: {emails: 1}}).fetch();
};



// Send notification to Ask<PERSON> after apply promotion
const sendPromotionNotification = ({ userIds, template }) => {
  // sendNotificationByIds(userIds, {
  //   title: {
  //     vi: template.title.vi,
  //     en: template.title.en,
  //     ko: template.title.ko,
  //   },
  //   text: {
  //     vi: template.text.vi,
  //     en: template.text.en,
  //     ko: template.text.ko,
  //   },
  //   payload: {},
  // }, { isForceView: false});
};

const resendPromotionNofitication = ({ userIds, template }) => {
  // const askerNotPostIds = Meteor.appCollection.users.find({ _id: {$in: userIds}, lastPostedTask: null }, {fields: {_id: 1}}).map((asker) => asker._id);
  // sendNotificationByIds(askerNotPostIds, {
  //   title: {
  //     vi: template.title.vi,
  //     en: template.title.en,
  //     ko: template.title.ko,
  //   },
  //   text: {
  //     vi: template.text.vi,
  //     en: template.text.en,
  //     ko: template.text.ko,
  //   },
  //   payload: {},
  // }, { isForceView: false});
}

const makeId = (length) => {
  var result = '';
  var characters = 'ABCDEFGHIJKLMNPQRSTUVWXYZ123456789'; // Exclude 0 and O
  var charactersLength = characters.length;
  for ( var i = 0; i < length; i++ ) {
     result += characters.charAt(Math.floor(Math.random() * charactersLength));
  }
  return result;
}

// Create daily promotion code
const createPromotionCode = () => {
  const today = moment(changeTimeZone(new Date())).format('YYYY_MM_DD');
  const cleaningService = Meteor.appCollection.Service.findOne({ 'text.en': 'Cleaning' }, {fields: {_id: 1}});
  const code = makeId(5);

  const now = new Date();
  const startOfDate = revertTimeZone(moment().startOf('date').toDate());
  const endOfDate = revertTimeZone(moment().add(1, 'days').endOf('date').toDate());

  Meteor.appCollection.PromotionCode.insert({
    code, // Create ramdom code with 5 chars
    typeOfPromotion: 'NEW',
    value: {
      type: 'PERCENTAGE',
      value: 0.3,
      maxValue: 50000
    },
    description: `AUTO_CODE_${today}`, // Description to compare
    target: 'ASKER',
    startDate: now, // From now
    endDate: endOfDate, // End of next 2 days
    cities: [],
    limit: 100, // Fix litmit 100 usage
    serviceId: cleaningService._id, // Fix cleaning service only
    taskStartDate: startOfDate, // From now
    taskEndDate: endOfDate, // End of next 2 days
    createdAt: now,
  });

  return code;
};

const createMarketingCampaign = (promoCode) => {
  const code = Meteor.appCollection.PromotionCode.findOne({ code: promoCode });
  if ( !code ) {
    throw new Meteor.Error('AutoGenerateTaskSchedule', `Code not found ${promoCodes}`);
  }

  Meteor.appCollection.MarketingCampaign.insert({
    name: code.description, // Same with code description
    startDate: code.startDate,
    endDate: code.endDate,
    description: {
      vi: '🎁ƯU ĐÃI ĐẾN TỪ BTASKEE🎁',
      en: '🎁A SPECIAL DISCOUNT FROM BTASKEE🎁',
      ko: '🎁A SPECIAL DISCOUNT FROM BTASKEE🎁'
    },
    subDescription: {
      vi: `Nhập mã ${promoCode} để nhận ưu đãi lên đến 30% cho lần đầu sử dụng. Tối đa 50,000vnd. Mỗi code được sử dụng 01 lần, áp dụng cho công việc từ hôm nay đến hết ngày ${moment(changeTimeZone(code.taskEndDate)).format('DD/MM/YYYY')}. Số lượng có hạn, nhanh chóng đặt lịch để nhận ưu đãi.\n\nChỉ dành riêng cho khách hàng nhận được thông báo.`,
      en: `Enter ${promoCode} to get 30% OFF on the first use cleaning service. Max 50.000VND. Each code is valid for 01 time usage only, available for service from now until the end of ${moment(changeTimeZone(code.taskEndDate)).format('MM/DD/YYYY')}. Limited availability.`,
      ko: `Enter ${promoCode} to get 30% OFF on the first use cleaning service. Max 50.000VND. Each code is valid for 01 time usage only, available for service from now until the end of ${moment(changeTimeZone(code.taskEndDate)).format('MM/DD/YYYY')}. Limited availability.`
    },
    action: 'app_direction',
    actionText: {
        vi: 'Đăng việc ngay',
        en: 'Book now',
        ko: 'Book now'
    },
    secondaryButton: {
        vi: 'Đóng',
        en: 'Close',
        ko: 'Close'
    },
    image: 'https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/JxaHL7H3u5CHk2By7',
    promotion: {
      serviceId: code.serviceId,
      serviceText: {
            vi: 'Dọn dẹp nhà',
            en: 'Cleaning',
            ko: 'Cleaning'
        },
        code: code.code
    },
    includeNewUsers: false,
    createdAt: new Date(),
    status: 'ACTIVE',
    notificationTemplate: {
      title: {
            vi: '🎁ƯU ĐÃI ĐẾN TỪ BTASKEE',
            en: '🎁A SPECIAL DISCOUNT FROM BTASKEE',
            ko: '🎁A SPECIAL DISCOUNT FROM BTASKEE'
        },
        text: {
            vi: `Nhận ngay Ưu đãi lên đến 30% cho lần đầu đặt lịch Dọn dẹp nhà trên ứng dụng. Áp dụng từ hôm nay đến hết ngày ${moment(changeTimeZone(code.taskEndDate)).format('DD/MM/YYYY')}. Số lượng có hạn, nhanh chóng đặt lịch để nhận ưu đãi.`,
            en: `Open bTaskee application to get a discount 30% for the first use Home Cleaning Service. Applied from now to ${moment(changeTimeZone(code.taskEndDate)).format('MM/DD/YYYY')}. Limited availability.`,
            ko: `Open bTaskee application to get a discount 30% for the first use Home Cleaning Service. Applied from now to ${moment(changeTimeZone(code.taskEndDate)).format('MM/DD/YYYY')}. Limited availability.`
        }
    },
    actionLocalize: {
        vi: '',
        en: '',
        ko: ''
    },
    updatedAt: new Date(),
    notificationSentNumber: 0
  });
};

/**
 * Assume the promotionCode and marketingCampaign are ready
 * 
 * Update marketingCampaign userIds{}, promotionCode userIds[]
 */
const updateNewUserToCampagin = (newAskerIn24h) => {
  // Find today promotion and campaign
  const today = moment(changeTimeZone(new Date())).format('YYYY_MM_DD');
  const todayPromotion = Meteor.appCollection.PromotionCode.findOne({ description: `AUTO_CODE_${today}` });
  if ( !todayPromotion ) {
    // Create today promotion code
    const code = createPromotionCode();
    // Create the marketing campaign link with today promotion code
    createMarketingCampaign(code);

  }

  const campaignPromo24 = Meteor.appCollection.MarketingCampaign.findOne({
    name: `AUTO_CODE_${today}`,
    status: 'ACTIVE',
    startDate: {$lt: new Date()},
    endDate: {$gt: new Date()}
  }, {fields: {_id: 1, 'promotion.code': 1, notificationTemplate: 1, userIds: 1}});
  if ( campaignPromo24 ) {
    if ( _.keys(campaignPromo24.userIds || {}).length > 0 ) {
      // Resend to exists Askers still not post any task
      resendPromotionNofitication({
        userIds: _.keys(campaignPromo24.userIds),
        template: campaignPromo24.notificationTemplate,
      });
    }

    let campaignUpdateUserIds = {};
    let promotionUpdateUserIds = [];
    newAskerIn24h.forEach((asker) => {
      campaignUpdateUserIds[`userIds.${asker._id}`] = 0;
      promotionUpdateUserIds.push(asker._id);
    });

    if ( _.keys(campaignUpdateUserIds).length > 0 && promotionUpdateUserIds.length > 0 ) {
      
      // Update new userIds in MarketingCampaign
      Meteor.appCollection.MarketingCampaign.update({ _id: campaignPromo24._id }, {
        $set: campaignUpdateUserIds
      });

      // Update new userIds in PromotionCode
      Meteor.appCollection.PromotionCode.update({ code:  campaignPromo24.promotion.code}, {
        $addToSet: {
          userIds: { $each: promotionUpdateUserIds }
        }
      });

      // Mark the Askers as isSentPromotion24 true
      Meteor.appCollection.users.update({
        _id: {$in: promotionUpdateUserIds}
      }, {
        $set: {
          isSentPromotion24: true
        }
      }, {
        multi: true
      });
      
      // Send notification to new Asker
      sendPromotionNotification({
        userIds: promotionUpdateUserIds,
        template: campaignPromo24.notificationTemplate
      });
    }
  }
};

const runSendPromotion24h = () => {
  var message = null;
  try {
    const dayInVNTime = changeTimeZone(new Date());
    if ( dayInVNTime.getHours() < 8 || dayInVNTime.getHours() > 18 ) {
      // Not run out of 8:00 - 18:59
      return;
    }
    
    // Query New Asker not post task >= 24h
    const newAskerIn24h = findNewAskerNotPostTask();
    if ( newAskerIn24h.length >= 0 ) {
      updateNewUserToCampagin(newAskerIn24h);
    }
  } catch(e) {
    message = e;
    postToSlack({
      channel: 'cron-jobs-alert',
      text: `Workflow: There was an error on autoSendPromotionNewAsker. Message: ${message}`,
    });
  }

  // Tracking
  trackingScheduleStatus({name: 'autoSendPromotionNewAsker', nextRunAt: SyncedCron.nextScheduledAtDate('autoSendPromotionNewAsker'), message});
};

  Meteor.methods({
    autoSendPromotionNewAsker() {
      SyncedCron.add({
        name: 'autoSendPromotionNewAsker',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 8)).getHours();
          // Run synced cron 
          return parser.cron(`0 45 ${hourInTimeZoneServer}/6 1/1 * ? *s`, true);
          
        },
        job() {
          // runSendPromotion24h();

        },
      });
    },
    'runNow.autoSendPromotionNewAsker'() {
      // runSendPromotion24h();
    },
  });
