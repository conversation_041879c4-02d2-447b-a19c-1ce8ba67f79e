import { Meteor } from 'meteor/meteor';
import _ from 'lodash';
import {
  revertTimeZone,
  trackingScheduleStatus,
} from '/server/helper-function';
import {
  sendNotificationByIds,
} from '/server/push-methods';

const dailyNotification = () => {
  const template = {
    name: 'Remind Tasker Open App',
    title: {
      vi: 'Nhận việc ngay',
      en: 'Choose your task now',
      ko: 'Choose your task now',
      th: 'Choose your task now',
    },
    text: {
      vi: 'Hãy mở ứng dụng để nhận những công việc phù hợp',
      en: 'Open app to choose your proper tasks',
      ko: 'Open app to choose your proper tasks',
      th: 'Open app to choose your proper tasks',
    },
    type: 201,
  };
  if (template) {
    const taskerIds = Meteor.appCollection.Task.find({ status: 'POSTED' })
      .map(task => (task.viewedTaskers))
      .reduce((arr, viewedTasker) => (arr.concat(viewedTasker || [])), []);
    if (taskerIds.length > 0) {
      const data = {
        from: 'system',
        userIds: _.uniq(taskerIds),
        message: {
          title: template.title,
          text: template.text,
        },
        payload: {
        },
      };
      sendNotificationByIds(data);
    }
  }
};

export default function () {
  Meteor.methods({
    dailyNotification20h() {
      SyncedCron.add({
        name: 'resendNotification8h',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 19)).getHours();
          // Run at 19:55 every day with timezone GMT+7.
          return parser.cron(`0 55 ${hourInTimeZoneServer} 1/1 * ? *`, true);
        },
        job() {
          var message = null;
          try {
            // dailyNotification();
          } catch (e) {
            message = e;
          }
          // Tracking
          trackingScheduleStatus({name: 'NotificationTaskerAt20h', nextRunAt: SyncedCron.nextScheduledAtDate('resendNotification8h'), message});
        }
      });
    },
    dailyNotification21h() {
      SyncedCron.add({
        name: 'resendNotification9h',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 20)).getHours();
          // Run at 20:55 every day with timezone GMT+7.
          return parser.cron(`0 55 ${hourInTimeZoneServer} 1/1 * ? *`, true);
        },
        job() {
          var message = null;
          try {
            // dailyNotification();
          } catch (e) {
            message = e;
          }
          // Tracking
          trackingScheduleStatus({name: 'NotificationTaskerAt21h', nextRunAt: SyncedCron.nextScheduledAtDate('resendNotification9h'), message});
        }
      });
    },
    dailyNotification22h() {
      SyncedCron.add({
        name: 'resendNotification10h',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 21)).getHours();
          // Run at 21:55 every day with timezone GMT+7.
          return parser.cron(`0 55 ${hourInTimeZoneServer} 1/1 * ? *`, true);
        },
        job() {
          var message = null;
          try {
            // dailyNotification();
          } catch (e) {
            message = e;
          }
          // Tracking
          trackingScheduleStatus({name: 'NotificationTaskerAt22h', nextRunAt: SyncedCron.nextScheduledAtDate('resendNotification10h'), message});
        }
      });
    },
    dailyNotification23h() {
      SyncedCron.add({
        name: 'resendNotification11h',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016,1,1,22)).getHours();
          // Run at 22:55 every day with timezone GMT+7.
          return parser.cron(`0 55 ${hourInTimeZoneServer} 1/1 * ? *`, true);
        },
        job() {
          var message = null;
          try {
            // dailyNotification();
          } catch (e) {
            message = e;
          }
          // Tracking
          trackingScheduleStatus({name: 'NotificationTaskerAt23h', nextRunAt: SyncedCron.nextScheduledAtDate('resendNotification11h'), message});
        }
      });
    },
    dailyNotification() {
      // Meteor.call('dailyNotification20h');
      // Meteor.call('dailyNotification21h');
      // Meteor.call('dailyNotification22h');
      // Meteor.call('dailyNotification23h');
    },
    'runNow.dailyNotification'() {
      // dailyNotification();
    },
  });
}
