package autoUpgradeMedalForCommunityUser

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.mongodb.org/mongo-driver/bson"
)

func processUpgradeMedalForUser(userId string, medalId string, tagIds []string) error {
	if userId != "" && medalId != "" {
		now := globalLib.GetCurrentTime(local.TimeZone)
		dataUpdate := bson.M{
			"medals": bson.M{
				"_id":    medalId,
				"isUsed": false,
				"date":   now,
			},
		}
		if len(tagIds) > 0 {
			// append thêm Newfeeds Visibility vào fav tag user
			dataUpdate["favouriteTagIds"] = bson.M{
				"$each": tagIds,
			}
		}
		// Get list of community users
		_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_COMMUNITY_USER[local.ISO_CODE], userId, bson.M{"$push": dataUpdate})
		if err != nil {
			textErr := fmt.Sprintf("[Sync Cron VN v3] AutoUpgradeMedalForCommunityUser %s: can not update medal for user userId: %s , err: %s ", local.ISO_CODE, userId, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], textErr)
			return err
		}
	}
	return nil
}
