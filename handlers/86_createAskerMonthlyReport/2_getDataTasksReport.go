package createAskerMonthlyReport

import (
	"fmt"
	"sync"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelAskerMonthlyReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/askerMonthlyReport"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getDataTaskReport(w *sync.WaitGroup, askerCh chan *modelUser.Users, startAMonth, endAMonth time.Time) chan *modelReport {
	outc := make(chan *modelReport, LIMIT)
	limitRoutineCh := make(chan struct{}, 3)
	go func() {
		defer w.Done()
		// Close channel khi chạy hết vòng lặp askers
		defer close(limitRoutineCh)
		defer close(outc)
		wUser := &sync.WaitGroup{}
		for asker := range askerCh {
			wUser.Add(1)
			limitRoutineCh <- struct{}{}
			go func(askerId string) {
				defer wUser.Done()
				// get tasks done
				var tasks []*modelTask.Task
				query := bson.M{
					"askerId": askerId,
					"status":  globalConstant.TASK_STATUS_DONE,
					"date":    bson.M{"$gte": startAMonth, "$lte": endAMonth},
				}

				err := globalDataAccess.GetAllByQuery(
					globalCollection.COLLECTION_TASK[local.ISO_CODE],
					query,
					bson.M{"_id": 1, "serviceText": 1, "serviceId": 1, "duration": 1, "serviceName": 1, "cost": 1, "costDetail.Cost": 1},
					&tasks,
				)
				if err != nil {
					msg := fmt.Sprintf("CreateAskerMonthlyReportVN Get data task for asker report - askerId:%v error: %v", askerId, err)
					globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
					<-limitRoutineCh
					return
				}
				// Next nếu asker không có data task
				if len(tasks) == 0 {
					// Create data report by month
					askerReportData := &modelAskerMonthlyReport.AskerMonthlyReportMonthlyReport{
						Month:     int32(startAMonth.Month()),
						CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
					}
					askerMonthlyReport := &modelReport{
						AskerId:           askerId,
						Year:              int32(startAMonth.Year()),
						MonthlyReportInfo: askerReportData,
					}
					// Đưa data vào trong channel
					outc <- askerMonthlyReport
					<-limitRoutineCh
					return
				}
				var totalTaskDuration, totalTaskCost float64
				var totalTaskDone int32
				serviceCountMap := make(map[string]int32)
				var numberOfUsedOnService []*modelAskerMonthlyReport.AskerReportMonthlyReportNumberOfUsedOnService
				// Get data report task by asker
				for _, task := range tasks {
					serviceCountMap[task.ServiceName]++
					totalTaskDone++
					taskDuration := task.Duration
					if taskDuration == 0 {
						taskDuration = 3
					}
					totalTaskDuration += task.Duration
					taskCost := task.Cost
					if task.CostDetail != nil {
						taskCost = task.CostDetail.Cost
					}
					totalTaskCost += taskCost
				}
				for serviceName, value := range serviceCountMap {
					numberOfUsedOnService = append(numberOfUsedOnService, &modelAskerMonthlyReport.AskerReportMonthlyReportNumberOfUsedOnService{
						ServiceName:  serviceName,
						NumberOfUsed: value,
					})
				}
				// Create data report by month
				askerReportData := &modelAskerMonthlyReport.AskerMonthlyReportMonthlyReport{
					Month:                 int32(startAMonth.Month()),
					TotalTaskDone:         totalTaskDone,
					TotalTaskDuration:     totalTaskDuration,
					TotalTaskCost:         totalTaskCost,
					NumberOfUsedOnService: numberOfUsedOnService,
					CreatedAt:             globalLib.GetCurrentTimestamp(local.TimeZone),
				}
				askerMonthlyReport := &modelReport{
					AskerId:           askerId,
					Year:              int32(startAMonth.Year()),
					MonthlyReportInfo: askerReportData,
				}
				// Đưa data vào trong channel
				outc <- askerMonthlyReport
				<-limitRoutineCh
			}(asker.XId)
		}
		wUser.Wait()
	}()
	return outc
}
