# Meteor packages used by this project, one per line.
# Check this file (and the other files in this directory) into your repository.
#
# 'meteor add' and 'meteor remove' will edit this file for you,
# but you can also edit it by hand.

meteor-base@1.5.1             # Packages every Meteor app needs to have
mobile-experience@1.1.1       # Packages for a great mobile UX
mongo@1.16.8                   # The database Meteor supports right now
reactive-var@1.0.12            # Reactive variable for tracker

standard-minifier-css@1.9.2   # CSS minifier run for production mode
standard-minifier-js@2.8.1    # JS minifier run for production mode
es5-shim@4.8.0                # ECMAScript 5 compatibility for older browsers
ecmascript@0.16.8              # Enable ECMAScript2015+ syntax in app code
typescript@4.9.5              # Enable TypeScript syntax in .ts and .tsx modules
shell-server@0.5.0            # Server-side component of the `meteor shell` command
hot-module-replacement@0.5.3  # Update client in development without reloading the page

static-html@1.3.2             # Define static page content in .html files
react-meteor-data       # React higher-order component for reactively tracking Meteor data
maka:rest
akarda:synced-cron
http
fetch@0.1.4
universe:i18n
