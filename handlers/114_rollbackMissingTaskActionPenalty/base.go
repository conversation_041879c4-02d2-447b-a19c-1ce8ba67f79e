package rollBackMissingTaskActionPenalty

import (
	"fmt"
	"strings"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

var cfg = config.GetConfig()

func RollbackMissingTaskActionPenalty(reqBody *model.RequestAction) interface{} {
	defer local.Logger.Sync()

	local.Logger.Info("RollbackMissingTaskActionPenalty Started")
	defer local.Logger.Info("RollbackMissingTaskActionPenalty Finished")

	// 0. Validate request.
	// Send list transactionIds need to be rollback.
	if reqBody == nil || reqBody.TransactionIds == nil {
		return nil
	}

	// 1. Get transactions by transactionIds.
	transactions := getTransactions(reqBody.TransactionIds)
	if len(transactions) == 0 {
		return nil
	}

	// 2. Get users (_id, fAccountId) by transaction.userId
	userIds := make([]string, 0)
	for _, transaction := range transactions {
		userIds = append(userIds, transaction.GetUserId())
	}
	mapUsersById := getUsers(userIds)
	if len(mapUsersById) == 0 {
		return nil
	}

	// 3. Rollback transactions (create transaction + inc FMainAccount)
	successIds, errorIds := rollbackTransactions(transactions, mapUsersById)

	msg := fmt.Sprintf("[go-sync-cron-vn-v3] RollbackMissingTaskActionPenalty - successIds:\n%v", strings.Join(successIds, "|"))
	globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)

	return map[string]interface{}{
		"successIds": successIds,
		"errorIds":   errorIds,
	}
}
