package globalLib

import (
	"strings"

	"go.mongodb.org/mongo-driver/bson"
)

var IMPORTANT_FIELDS_TO_SEND_SOCKET_TASK = bson.M{
	"_id":                         1,
	"serviceText":                 1,
	"taskPlace":                   1,
	"date":                        1,
	"duration":                    1,
	"requirements":                1,
	"isoCode":                     1,
	"status":                      1,
	"pet":                         1,
	"subscriptionId":              1,
	"isPremium":                   1,
	"description":                 1,
	"serviceName":                 1,
	"isTetBooking":                1,
	"collectionDate":              1,
	"originCurrency":              1,
	"cost":                        1,
	"costDetail.cost":             1,
	"costDetail.newFinalCost":     1,
	"costDetail.decreasedReasons": 1,
	"costDetail.vat":              1,
	"costDetail.totalCost":        1,
	"costDetail.promotionBy":      1,
	"newCostDetail.cost":          1,
	"newCostDetail.finalCost":     1,
	"newCostDetail.vat":           1,
	"newCostDetail.totalCost":     1,
	"detailDeepCleaning":          1,
	"cookingDetail.isGoMarket":    1,
	"cookingDetail.haveFruit":     1,
	"acceptedTasker.taskerId":     1,
	"acceptedTasker.avatar":       1,
	"acceptedTasker.name":         1,
	"acceptedTasker.isLeader":     1,
	"detailLaundry.isReceived":    1,
	"detailOfficeCleaning":        1,
	"forceTasker":                 1,
	"dateOptions":                 1,
	"isEco":                       1,
	"addons":                      1,
	"timezone":                    1,
	"excludedTaskers":             1,
	"detailMassage":               1,
	"detailBeautyCare":            1,
	"detailMakeup":                1,
	"detailHairStyling":           1,
	"detailNail":                  1,
}

func MergeQuerySocketTask(rootFields bson.M) bson.M {
	result := make(bson.M)
	for k, v := range IMPORTANT_FIELDS_TO_SEND_SOCKET_TASK {
		result[k] = v
	}

	for k := range rootFields {
		// if this key is already in in1, skip it
		if _, ok := result[k]; ok {
			continue
		}

		// otherwise, add it
		result[k] = 1
	}

	for k := range result {
		// find case key has dot in it
		if !strings.Contains(k, ".") {
			continue
		}

		// split key by dot
		keys := strings.Split(k, ".")
		isExistInResult := false
		for i := 0; i < len(keys); i++ {
			currentKey := strings.Join(keys[:i], ".")

			// check if this key already exists in in1 -> skip it and delete k in in1
			if _, ok := result[currentKey]; ok {
				isExistInResult = true
				break
			}
		}
		if isExistInResult {
			delete(result, k)
		}
	}

	return result
}
