package sendBusinessReportEmail

import (
	"time"

	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessTransaction"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	"go.mongodb.org/mongo-driver/bson"
)

func getBusinessTransactions(req *modelEmailSending.EmailSendBusinessReportRequest) (depositTransaction, creditTransaction []*businessTransaction.BusinessTransaction) {
	depositTransaction = []*businessTransaction.BusinessTransaction{}
	creditTransaction = []*businessTransaction.BusinessTransaction{}

	providedTime := time.Date(int(req.Year), time.Month(req.Month), 1, 0, 0, 0, 0, local.TimeZone)
	start, end := globalLib.GetStartEndOfMonth(local.TimeZone, providedTime)

	query := bson.M{
		"businessId": req.BusinessId,
		"createdAt": bson.M{
			"$gte": start,
			"$lte": end,
		},
	}
	fields := bson.M{
		"type":      1,
		"amount":    1,
		"name":      1,
		"memberId":  1,
		"levelInfo": 1,
		"createdAt": 1,
	}
	var transactions []*businessTransaction.BusinessTransaction
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_BUSINESS_TRANSACTION[local.ISO_CODE],
		query, fields, bson.M{"createdAt": 1}, &transactions)

	for _, transaction := range transactions {
		if transaction.Type == globalConstant.BUSINESS_TRANSACTION_TYPE_DEPOSIT {
			depositTransaction = append(depositTransaction, transaction)
		} else if transaction.Type == globalConstant.BUSINESS_TRANSACTION_TYPE_CREDIT {
			creditTransaction = append(creditTransaction, transaction)
		}
	}
	return
}
