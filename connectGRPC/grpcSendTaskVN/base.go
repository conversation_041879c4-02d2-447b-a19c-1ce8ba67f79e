package grpcSendTaskVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcSendTaskVN"
	"google.golang.org/grpc"
)

func ConnectGRPCSendTaskVN(endpoint string) (grpcSendTaskVN.PushNotificationNewTaskVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcSendTaskVN.NewPushNotificationNewTaskVNClient(connect)
	return client, connect, err
}
