package giveRewardTaskerDoneSpecialCampaign

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelSpecialCampaignTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/specialCampaignTransaction"
	"go.mongodb.org/mongo-driver/bson"
)

func updateStatusSpecialCampaignTransaction(specialCampaignId, specialCampaignTransactionStatus string, changeHistoryKey string, oldData *modelSpecialCampaignTransaction.SpecialCampaignTransaction, percentProcess float64) error {
	now := globalLib.GetCurrentTime(local.TimeZone)
	dataUpdate := bson.M{
		"status":         specialCampaignTransactionStatus,
		"percentProcess": percentProcess,
	}

	if specialCampaignTransactionStatus == globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_REWARDED {
		dataUpdate["rewardedDate"] = now
	}

	if specialCampaignTransactionStatus == globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED {
		dataUpdate["completedDate"] = now
	}

	changeHistory := bson.M{
		"createdAt": now,
		"content": bson.M{
			"oldPercentProcess": oldData.PercentProcess,
			"newPercentProcess": percentProcess,
			"oldStatus":         oldData.Status,
			"newStatus":         specialCampaignTransactionStatus,
		},
		"from": globalConstant.CHANGES_HISTORY_FROM_SYSTEM,
		"key":  changeHistoryKey,
	}

	query := bson.M{
		"$set": dataUpdate,
		"$push": bson.M{
			"changeHistory": changeHistory,
		},
	}
	_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], specialCampaignId, query)
	return err
}
