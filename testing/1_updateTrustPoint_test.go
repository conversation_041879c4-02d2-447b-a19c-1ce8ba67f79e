package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTrustPointHistory "gitlab.com/btaskee/go-services-model-v2/grpcmodel/trustPointHistory"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func TestUpdateTrustPoint(t *testing.T) {

	// case:
	// 3 task done
	// not task rate
	// asker not have trustPoint
	t.Run("1", func(t *testing.T) {
		ResetData()
		apiURL := apiSyncCron + "/update-trust-point"

		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 01",
			}, {
				"phone":     "0823456789",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker 01",
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		last1Days := now.AddDate(0, 0, -1)

		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,14,3", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,14,3", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,14,3", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When the service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the database", func() {
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"trustPoint": 1})
					So(user.TrustPoint.Point, ShouldEqual, 83)
					So(user.TrustPoint.Rated, ShouldEqual, "GOOD")

					var trustPointHistory *modelTrustPointHistory.TrustPointHistory
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TRUST_POINT_HISTORY[local.ISO_CODE], bson.M{"userId": "0834567890"}, bson.M{"history": 1}, &trustPointHistory)
					So(trustPointHistory, ShouldNotBeNil)
					So(len(trustPointHistory.History), ShouldEqual, 1)
					So(trustPointHistory.History[0].OldTrustPoint, ShouldEqual, 80)
					So(trustPointHistory.History[0].NewTrustPoint, ShouldEqual, 83)
					So(trustPointHistory.History[0].TotalTrustPoint, ShouldEqual, 3)

					So(trustPointHistory.History[0].Data, ShouldNotBeNil)
					So(len(trustPointHistory.History[0].Data), ShouldEqual, 3)
					for _, v := range trustPointHistory.History[0].Data {
						So(v.TaskId, ShouldBeIn, ids)
						So(v.Status, ShouldEqual, globalConstant.TASK_STATUS_DONE)
					}
				})
			})
		})
	})

	// case:
	// 2 task done (1*2)
	// 2 task cancel (-2*2)
	// not task rate
	// asker not have trustPoint
	t.Run("2", func(t *testing.T) {
		ResetData()
		apiURL := apiSyncCron + "/update-trust-point"

		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 01",
			}, {
				"phone":     "0823456789",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker 01",
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		last1Days := now.AddDate(0, 0, -1)

		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,2,0", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,2,0", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
			{
				"askerPhone":         "0834567890",
				"serviceName":        globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":            "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":             globalConstant.TASK_STATUS_CANCELED,
				"cancellationReason": globalConstant.CANCELLATION_REASON_ASKER_BUSY,
				"rated":              true,
				"taskerRated":        false,
				"date":               fmt.Sprintf("%d,%d,%d,2,0", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
			{
				"askerPhone":         "0834567890",
				"serviceName":        globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":            "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":             globalConstant.TASK_STATUS_CANCELED,
				"cancellationReason": globalConstant.CANCELLATION_REASON_ASKER_BUSY,
				"rated":              true,
				"taskerRated":        false,
				"date":               fmt.Sprintf("%d,%d,%d,2,0", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When the service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the database", func() {
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"trustPoint": 1})
					So(user.TrustPoint.Point, ShouldEqual, 78)
					So(user.TrustPoint.Rated, ShouldEqual, "MEDIUM")

					var trustPointHistory *modelTrustPointHistory.TrustPointHistory
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TRUST_POINT_HISTORY[local.ISO_CODE], bson.M{"userId": "0834567890"}, bson.M{"history": 1}, &trustPointHistory)
					So(trustPointHistory, ShouldNotBeNil)
					So(len(trustPointHistory.History), ShouldEqual, 1)
					So(trustPointHistory.History[0].OldTrustPoint, ShouldEqual, 80)
					So(trustPointHistory.History[0].NewTrustPoint, ShouldEqual, 78)
					So(trustPointHistory.History[0].TotalTrustPoint, ShouldEqual, -2)

					So(trustPointHistory.History[0].Data, ShouldNotBeNil)
					So(len(trustPointHistory.History[0].Data), ShouldEqual, 4)
					for _, v := range trustPointHistory.History[0].Data {
						if globalLib.FindStringInSlice(ids[0:2], v.TaskId) >= 0 {
							So(v.Status, ShouldEqual, globalConstant.TASK_STATUS_DONE)
						} else {
							So(v.Status, ShouldEqual, globalConstant.TASK_STATUS_CANCELED)
							So(v.Reason, ShouldEqual, globalConstant.CANCELLATION_REASON_ASKER_BUSY)
						}
					}
				})
			})
		})
	})

	// case:
	// 2 task done + rate 4 star (-1*2)
	// 2 task cancel (-2*2)
	// asker not have trustPoint
	t.Run("3", func(t *testing.T) {
		ResetData()
		apiURL := apiSyncCron + "/update-trust-point"

		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 01",
			}, {
				"phone":     "0823456789",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker 01",
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		last1Days := now.AddDate(0, 0, -1)

		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,14,3", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,14,3", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
			{
				"askerPhone":         "0834567890",
				"serviceName":        globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":            "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":             globalConstant.TASK_STATUS_CANCELED,
				"cancellationReason": globalConstant.CANCELLATION_REASON_ASKER_BUSY,
				"rated":              true,
				"taskerRated":        false,
				"date":               fmt.Sprintf("%d,%d,%d,14,3", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
			{
				"askerPhone":         "0834567890",
				"serviceName":        globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":            "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":             globalConstant.TASK_STATUS_CANCELED,
				"cancellationReason": globalConstant.CANCELLATION_REASON_ASKER_BUSY,
				"rated":              true,
				"taskerRated":        false,
				"date":               fmt.Sprintf("%d,%d,%d,14,3", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
		})

		createdAt := fmt.Sprintf("%d,%d,%d,10,0", last1Days.Year(), int(last1Days.Month()), last1Days.Day())
		CreateAskerRating([]map[string]interface{}{
			{
				"createdAt":   createdAt,
				"taskerPhone": "0823456789",
				"askerPhone":  "0834567890",
				"rate":        4,
				"taskId":      ids[0],
			},
			{
				"createdAt":   createdAt,
				"taskerPhone": "0823456789",
				"askerPhone":  "0834567890",
				"rate":        4,
				"taskId":      ids[1],
			},
		})
		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When the service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the database", func() {
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"trustPoint": 1})
					So(user.TrustPoint.Point, ShouldEqual, 74)
					So(user.TrustPoint.Rated, ShouldEqual, "MEDIUM")

					var trustPointHistory *modelTrustPointHistory.TrustPointHistory
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TRUST_POINT_HISTORY[local.ISO_CODE], bson.M{"userId": "0834567890"}, bson.M{"history": 1}, &trustPointHistory)
					So(trustPointHistory, ShouldNotBeNil)
					So(len(trustPointHistory.History), ShouldEqual, 1)
					So(trustPointHistory.History[0].OldTrustPoint, ShouldEqual, 80)
					So(trustPointHistory.History[0].NewTrustPoint, ShouldEqual, 74)
					So(trustPointHistory.History[0].TotalTrustPoint, ShouldEqual, -6)

					So(trustPointHistory.History[0].Data, ShouldNotBeNil)
					So(len(trustPointHistory.History[0].Data), ShouldEqual, 4)
					for _, v := range trustPointHistory.History[0].Data {
						if globalLib.FindStringInSlice(ids[0:2], v.TaskId) >= 0 {
							So(v.Status, ShouldEqual, globalConstant.TASK_STATUS_DONE)
						} else {
							So(v.Status, ShouldEqual, globalConstant.TASK_STATUS_CANCELED)
							So(v.Reason, ShouldEqual, globalConstant.CANCELLATION_REASON_ASKER_BUSY)
						}
					}
				})
			})
		})
	})

	// case:
	// 3 task done
	// not task rate
	// asker have trustPoint 99
	t.Run("4", func(t *testing.T) {
		ResetData()
		apiURL := apiSyncCron + "/update-trust-point"

		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 01",
				"trustPoint": map[string]interface{}{
					"point": 99,
					"rated": "GOOD",
				},
			}, {
				"phone":     "0823456789",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker 01",
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		last1Days := now.AddDate(0, 0, -1)

		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,14,3", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,14,3", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,14,3", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When the service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the database", func() {
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"trustPoint": 1})
					So(user.TrustPoint.Point, ShouldEqual, 100)
					So(user.TrustPoint.Rated, ShouldEqual, "GOOD")

					var trustPointHistory *modelTrustPointHistory.TrustPointHistory
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TRUST_POINT_HISTORY[local.ISO_CODE], bson.M{"userId": "0834567890"}, bson.M{"history": 1}, &trustPointHistory)
					So(trustPointHistory, ShouldNotBeNil)
					So(len(trustPointHistory.History), ShouldEqual, 1)
					So(trustPointHistory.History[0].OldTrustPoint, ShouldEqual, 99)
					So(trustPointHistory.History[0].NewTrustPoint, ShouldEqual, 100)
					So(trustPointHistory.History[0].TotalTrustPoint, ShouldEqual, 3)

					So(trustPointHistory.History[0].Data, ShouldNotBeNil)
					So(len(trustPointHistory.History[0].Data), ShouldEqual, 3)
					for _, v := range trustPointHistory.History[0].Data {
						So(v.Status, ShouldEqual, globalConstant.TASK_STATUS_DONE)
					}
				})
			})
		})
	})

	// case:
	// 5 task done
	// 5 task rate < 4
	// asker have trustPoint 70
	t.Run("5", func(t *testing.T) {
		ResetData()
		apiURL := apiSyncCron + "/update-trust-point"

		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 01",
				"trustPoint": map[string]interface{}{
					"point": 70,
					"rated": "GOOD",
				},
			}, {
				"phone":     "0823456789",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker 01",
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		last1Days := now.AddDate(0, 0, -1)

		ids := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,14,3", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,14,3", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,14,3", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,14,3", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        fmt.Sprintf("%d,%d,%d,14,3", last1Days.Year(), last1Days.Month(), last1Days.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
				"updatedAt": last1Days,
			},
		})

		createdAt := fmt.Sprintf("%d,%d,%d,10,0", last1Days.Year(), int(last1Days.Month()), last1Days.Day())
		CreateAskerRating([]map[string]interface{}{
			{
				"createdAt":   createdAt,
				"taskerPhone": "0823456789",
				"askerPhone":  "0834567890",
				"rate":        3,
				"taskId":      ids[0],
			},
			{
				"createdAt":   createdAt,
				"taskerPhone": "0823456789",
				"askerPhone":  "0834567890",
				"rate":        3,
				"taskId":      ids[1],
			},
			{
				"createdAt":   createdAt,
				"taskerPhone": "0823456789",
				"askerPhone":  "0834567890",
				"rate":        3,
				"taskId":      ids[2],
			},
			{
				"createdAt":   createdAt,
				"taskerPhone": "0823456789",
				"askerPhone":  "0834567890",
				"rate":        3,
				"taskId":      ids[3],
			},
			{
				"createdAt":   createdAt,
				"taskerPhone": "0823456789",
				"askerPhone":  "0834567890",
				"rate":        3,
				"taskId":      ids[4],
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When the service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the database", func() {
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"phone": "0834567890"}, bson.M{"trustPoint": 1})
					So(user.TrustPoint.Point, ShouldEqual, 60)
					So(user.TrustPoint.Rated, ShouldEqual, "BAD")

					var trustPointHistory *modelTrustPointHistory.TrustPointHistory
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TRUST_POINT_HISTORY[local.ISO_CODE], bson.M{"userId": "0834567890"}, bson.M{"history": 1}, &trustPointHistory)
					So(trustPointHistory, ShouldNotBeNil)
					So(len(trustPointHistory.History), ShouldEqual, 1)
					So(trustPointHistory.History[0].OldTrustPoint, ShouldEqual, 70)
					So(trustPointHistory.History[0].NewTrustPoint, ShouldEqual, 60)
					So(trustPointHistory.History[0].TotalTrustPoint, ShouldEqual, -10)

					So(trustPointHistory.History[0].Data, ShouldNotBeNil)
					So(len(trustPointHistory.History[0].Data), ShouldEqual, 5)
					for _, v := range trustPointHistory.History[0].Data {
						So(v.Status, ShouldEqual, globalConstant.TASK_STATUS_DONE)
					}
				})
			})
		})
	})

}
