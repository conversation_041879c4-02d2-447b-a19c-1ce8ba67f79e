import { Meteor } from 'meteor/meteor';
import _ from 'lodash';
import {
  trackingScheduleStatus,
  postToSlack,
  sendNotificationByIds
} from '/server/helper-function';

const autoSendNotification = () => {
 	console.log("Start autoSendNotification ...");
  var message = null;
  var taskIds = [];
  var userIds = [];
  try {
    const tasks = Meteor.appCollection.Task.find({
      status: 'POSTED',
      'source.from': 'QUICK_POST_TASK',
	//TODO: fix wating -> waiting?
      'source.options.watingTaskerConfirmation': true,
      isShowSendToOtherTasker: null, // get undefined
      maximumTimeAcceptTask: {
        $lte: new Date(),
      }
    }, {
      fields: { askerId: 1 },
      sort: {date: 1}
    }).fetch();
    
    if (tasks && tasks.length > 0) {
      tasks.forEach(task => {
        taskIds.push(task._id);
        userIds.push(task.askerId); 
      });
    }
    taskIds = _.uniq(taskIds);
    userIds = _.uniq(userIds);
  
    //update task
    Meteor.appCollection.Task.update(
    {
      _id: { $in: taskIds }
    }, {
      $set: {
        isShowSendToOtherTasker: true,
        updatedAt: new Date(),
      },
    }, {
      multi : true,
    })
    
    // Send notification
    sendNotificationByIds(userIds, {
      title: {
        vi: 'Rất tiếc!',
        en: 'Sorry!',
        ko: '죄송합니다!',
        th: 'Sorry!',
      },
      text: {
        vi: `Cộng tác viên đã không phản hồi công việc của bạn. Bạn có thể gửi công việc đến những Cộng tác viên khác.`,
        en: `This tasker did not respond to your task. You can send this task to other taskers.`,
        ko: `작업자님이 고객님의 예약을 수락하지 않으셨습니다. 다른 작업자님에게 고객님의 예약 상황을 보내주세요.`,
        th: `This tasker did not respond to your task. You can send this task to other taskers.`,
      },
      payload: {},
    }, {type: 25, isForceView: true,});

  } catch (error) {
    message = error;
    postToSlack({
      channel: 'cron-jobs-alert',
      text: `Workflow: There was an error on autoSendNotificationQuickPostTask: taskId: ${task._id} Message: ${message}`,
    });
  }
 	console.log("Finished autoSendNotification");
  // Tracking
  trackingScheduleStatus({name: 'autoSendNotificationQuickPostTask', nextRunAt: SyncedCron.nextScheduledAtDate('autoSendNotificationQuickPostTask'), message});
};

  Meteor.methods({
    autoSendNotificationQuickPostTask() {
      SyncedCron.add({
        name: 'autoSendNotificationQuickPostTask',
        schedule(parser) {
          // Run every 10 minutes
          return parser.cron('0 0/10 0/1 1/1 * ? *', true);
        },
        job() {
          autoSendNotification();
        },
      });
	    console.log("Added autoSendNotificationQuickPostTask cron");
    },
    'runNow.autoSendNotificationQuickPostTask'() {
      autoSendNotification();
    },
  });
