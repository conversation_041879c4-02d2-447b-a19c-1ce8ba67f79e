// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcSMSVN

import (
	context "context"
	smsSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/smsSending"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// SMSVNClient is the client API for SMSVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SMSVNClient interface {
	Send(ctx context.Context, in *smsSending.SmsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	SendSMSViaVMG(ctx context.Context, in *smsSending.SmsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type sMSVNClient struct {
	cc grpc.ClientConnInterface
}

func NewSMSVNClient(cc grpc.ClientConnInterface) SMSVNClient {
	return &sMSVNClient{cc}
}

func (c *sMSVNClient) Send(ctx context.Context, in *smsSending.SmsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/grpcSMSVN.SMSVN/Send", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sMSVNClient) SendSMSViaVMG(ctx context.Context, in *smsSending.SmsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/grpcSMSVN.SMSVN/SendSMSViaVMG", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SMSVNServer is the server API for SMSVN service.
// All implementations must embed UnimplementedSMSVNServer
// for forward compatibility
type SMSVNServer interface {
	Send(context.Context, *smsSending.SmsRequest) (*emptypb.Empty, error)
	SendSMSViaVMG(context.Context, *smsSending.SmsRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedSMSVNServer()
}

// UnimplementedSMSVNServer must be embedded to have forward compatible implementations.
type UnimplementedSMSVNServer struct {
}

func (UnimplementedSMSVNServer) Send(context.Context, *smsSending.SmsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Send not implemented")
}
func (UnimplementedSMSVNServer) SendSMSViaVMG(context.Context, *smsSending.SmsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSMSViaVMG not implemented")
}
func (UnimplementedSMSVNServer) mustEmbedUnimplementedSMSVNServer() {}

// UnsafeSMSVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SMSVNServer will
// result in compilation errors.
type UnsafeSMSVNServer interface {
	mustEmbedUnimplementedSMSVNServer()
}

func RegisterSMSVNServer(s *grpc.Server, srv SMSVNServer) {
	s.RegisterService(&_SMSVN_serviceDesc, srv)
}

func _SMSVN_Send_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(smsSending.SmsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SMSVNServer).Send(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSMSVN.SMSVN/Send",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SMSVNServer).Send(ctx, req.(*smsSending.SmsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SMSVN_SendSMSViaVMG_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(smsSending.SmsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SMSVNServer).SendSMSViaVMG(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSMSVN.SMSVN/SendSMSViaVMG",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SMSVNServer).SendSMSViaVMG(ctx, req.(*smsSending.SmsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _SMSVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcSMSVN.SMSVN",
	HandlerType: (*SMSVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Send",
			Handler:    _SMSVN_Send_Handler,
		},
		{
			MethodName: "SendSMSViaVMG",
			Handler:    _SMSVN_SendSMSViaVMG_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "smsVN.proto",
}
