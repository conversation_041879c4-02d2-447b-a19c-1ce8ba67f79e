{"SMS": {"ESMSUrl": "http://btaskee.freeoda.com/ESMS.xml", "ESMSAPIKey": "dumbkey", "ESMSSecretKey": "dumbkey", "ESMSSmSType": "7"}, "public": {"staleSessionInactivityTimeout": 3000000, "staleSessionHeartbeatInterval": 30000, "staleSessionPurgeInterval": 3000000, "staleSessionActivityEvents": "click keydown", "requestOrigin": ["http://localhost:1313"]}, "twoFactorExpireTime": 600000, "MAIL_URL": "smtp://lanterns.tester:<EMAIL>:587/", "MAIL_FROM": "<EMAIL>", "MAIL_DEFAULT": "<EMAIL>", "Master_Key": "Linh+<PERSON><PERSON>ong=Vuon", "DEFAULT_MONGO_SERVER": "mongodb://127.0.0.1:27017/db", "API_SERVER": "http://localhost:3000/", "RUN_SYNCEDCRON_SCHEDULY": true, "SCHEDULE_RANGE": 48, "SCHEDULE_RUN_EVERY": 6, "DEFAULT": {"ADMIN_USERNAME": "admin", "ADMIN_PASSWORD": "Btaskee.123@", "ADMIN_EMAIL": "<EMAIL>"}, "AWS3": {"AWSAccessKeyId": "dumbkey", "AWSSecretAccessKey": "dumbkey", "AWSBucket": "dumbkey"}, "socialConfig": {"facebook": {"appId": "1109712982429958", "secret": "640383a9305abb54512051f088cc025d"}}, "EMAIL": {"MailGunAPIUrl": "https://api.mailgun.net/v3/sandbox5fd8742e99ac4f5c98a4420b121bfd6c.mailgun.org/messages", "MailGunAPIKey": "**************************************************"}, "WinstonLogLevel": "info", "SLACK_POST": {"API_URL": "https://slack.com/api/chat.postMessage", "TOKEN": "dump"}, "PushNotification": {"apn": [{"appName": "main", "CERT": "ios/apn-dev/btaskee-dev-cert.pem", "KEY": "ios/apn-dev/btaskee-dev-key.pem", "passphrase": "8T@ske3%"}], "fcm": [{"appName": "main", "API_KEY": "AAAAsdxkZXQ:APA91bHiLI1wd8Q5JjMrv_23UJ3_9uOo7DG6kFD_Ktb1U3CKBjkXWyahO85YDJou3ysCr858IIGrL-5xl1_LOWS7SKc_jQVk_qgXqWJQmIg42RDeMOABriEh9ycBqsx_FigWxbM5WRdX"}], "sound": {"apn": [{"appName": "main", "sound": "/www/application/app/sounds/bells-message.mp3"}], "fcm": [{"appName": "main", "sound": "bells"}]}}, "hostName": "unicorn_workflow", "askerServerAPI": "http://localhost:3000", "ipstack": {"url": "http://api.ipstack.com", "accessKey": "dump"}, "Adyen": {"adyenUserName": "<EMAIL>", "adyenPassword": "thisisaTestthatis20char^#@$", "refundEndPoint": "https://pal-test.adyen.com/pal/servlet/Payment/v18/refund"}, "URBOX": {"app_id": "dump", "app_secret": "dump", "urlGetListGift": "https://api.urbox.vn/4.0/gift/lists"}, "ZALO_PAY": {"endpoint": "https://sandbox.zalopay.com.vn/v001/tpe/getstatusbyapptransid", "appId": "789", "macKey": "vOjBToIc0lphpxqiXsYf10RFijzH14dh"}, "GO_SERVICE": {"API_URL": "http://localhost:8080/api", "API_KEY": "dumpkey"}, "taskerServer": {"host": "http://localhost:3900", "APIKey": "UJUNnP62Bp3UqPabjzhXVFg6pXCDqq.fDKABBTURP22FWEbBvPKYUaGZj8WR5.C9FbjyynF9qBP2wd8Ntb6EzaEeKUHy.Bq5mPdjYXR8rLwMhF7JN6wAKXBa5DE.fT3juEPZhQXF526qndy7FEaTBhZv7g"}}