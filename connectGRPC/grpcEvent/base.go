package grpcEvent

import (
	"gitlab.com/btaskee/go-event-service/grpcEvent"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"google.golang.org/grpc"
)

func ConnectGRPCEvent(endpoint string) (grpcEvent.EventClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcEvent.NewEventClient(connect)
	return client, connect, err
}
