package sendMessageBeforeCancelTaskForceTasker

import (
	"fmt"
	"log"
	"time"

	"github.com/robfig/cron/v3"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
)

var (
	c            *cron.Cron
	cfg          = config.GetConfig()
	isRunning    = false
	paramsEnough = true
	runAt        string
)

func init() {
	var cronConfig map[string]interface{}
	if cfg != nil {
		cronConfig = cfg.ScheduleSyncCron["autosendmessagebeforecanceltaskforcetasker"]
	}

	// Check if params enough
	if cronConfig["run_at"] == nil {
		paramsEnough = false
		lib.PostToSlackNotStart("AutoSendMessageBeforeCancelTaskForceTasker")
		return
	}

	// Get runAt string for sync cron
	runAt = fmt.Sprintf(cronConfig["run_at"].(string))

	// Check if config has is_run = true
	if cronConfig["is_run"] == nil || !cronConfig["is_run"].(bool) {
		lib.PostToSlackNotStart("AutoSendMessageBeforeCancelTaskForceTasker")
		return
	}

	StartCron()
}

func StartCron() {
	c := cron.New(cron.WithLocation(local.TimeZone))
	c.AddFunc(runAt, AutoSendMessageBeforeCancelTaskForceTasker)
	c.Start()
	isRunning = true
}

func Run(action int) {
	// action == 1 -> Run sync cron once now
	if action == lib.RUN_NOW {
		AutoSendMessageBeforeCancelTaskForceTasker()
	}
	// action == 2 -> Start sync cron with schedule config
	if action == lib.START && paramsEnough && !isRunning {
		StartCron()
	}
	// action == 3 -> Stop running sync cron
	if action == lib.STOP && isRunning {
		c.Stop()
		c = nil
		isRunning = false
	}
}

func AutoSendMessageBeforeCancelTaskForceTasker() {
	log.Println("Start AutoSendMessageBeforeCancelTaskForceTasker Process")
	defer log.Println("Finish AutoSendMessageBeforeCancelTaskForceTasker Process")
	processSendMessageBeforeCancelTaskPostedLongTime()
}

func processSendMessageBeforeCancelTaskPostedLongTime() {
	// 1. get tasks
	tasks, err := getTasksBeforeCancel()
	if err != nil {
		message := fmt.Sprintf("Sync Cron Set Cancel Task Force Tasker. Error get list task: %s", err.Error())
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[globalConstant.ISO_CODE_VN], lib.GO_SERVICE_SYNC_CRON_USER_NAME, message)
	}

	if len(tasks) == 0 {
		return
	}
	var satisfiedTasks []*modelTask.Task
	for _, task := range tasks {
		//Field RemindMessageCreatedAt is created or updated in 2 cases:
		//after 1 hour since task is created to remind asker about tasker is not responding yet
		//remind asker about task will be cancelled after 1 hour

		//The message to remind asker before cancel task will be sent in 2 cases:
		//5 hours after task is created and tasker is not responding
		//1 hour after tasker rejected or suggested new date option
		timeForLastMessage := globalLib.GetCurrentTime(local.TimeZone).Add(-1*time.Hour - 15*time.Minute)
		if task.TaskPlace == nil || task.ForceTasker.RemindMessageCreatedAt != nil && globalLib.ParseDateFromTimeStamp(task.ForceTasker.RemindMessageCreatedAt, local.TimeZone).After(timeForLastMessage) {
			continue
		}

		//case 1: 1 hour after tasker rejected or suggested new date option
		if len(task.ChangesHistory) > 0 {
			flag := false
			for _, history := range task.ChangesHistory {
				if history.Key == globalConstant.CHAT_HISTORY_KEY_TASKER_REJECT || history.Key == globalConstant.CHANGES_HISTORY_KEY_TASKER_SUGGESTED_NEW_DATE_OPTION {
					flag = true
					timeToSendMessageAfterTaskerRejectedOrSuggestedNewDateOption := globalLib.GetCurrentTime(local.TimeZone).Add(-1 * time.Hour)
					if globalLib.ParseDateFromTimeStamp(history.CreatedAt, local.TimeZone).Before(timeToSendMessageAfterTaskerRejectedOrSuggestedNewDateOption) {
						satisfiedTasks = append(satisfiedTasks, task)
					}
					break
				}
			}
			if flag {
				continue
			}
		}
		//case 2: 5 hours after task is created and tasker is not responding
		//Only send message before cancel task if task is created 5 hours excluding range 23.00 to 6.00
		if globalLib.CalculateHoursExcludingMidnight(globalLib.ParseDateFromTimeStamp(task.CreatedAt, local.TimeZone), globalLib.GetCurrentTime(local.TimeZone)) >= 5 {
			satisfiedTasks = append(satisfiedTasks, task)
		}
	}

	if len(satisfiedTasks) == 0 {
		return
	}

	initMessageForChatBeforeCancelTask(satisfiedTasks)
}
