// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcChatServerIndo

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	websocketMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// ChatServerIndoClient is the client API for ChatServerIndo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ChatServerIndoClient interface {
	SendSocketChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error)
}

type chatServerIndoClient struct {
	cc grpc.ClientConnInterface
}

func NewChatServerIndoClient(cc grpc.ClientConnInterface) ChatServerIndoClient {
	return &chatServerIndoClient{cc}
}

func (c *chatServerIndoClient) SendSocketChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcChatServerIndo.ChatServerIndo/SendSocketChatMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChatServerIndoServer is the server API for ChatServerIndo service.
// All implementations must embed UnimplementedChatServerIndoServer
// for forward compatibility
type ChatServerIndoServer interface {
	SendSocketChatMessage(context.Context, *websocketMessage.WebsocketMessage) (*empty.Empty, error)
	mustEmbedUnimplementedChatServerIndoServer()
}

// UnimplementedChatServerIndoServer must be embedded to have forward compatible implementations.
type UnimplementedChatServerIndoServer struct {
}

func (UnimplementedChatServerIndoServer) SendSocketChatMessage(context.Context, *websocketMessage.WebsocketMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSocketChatMessage not implemented")
}
func (UnimplementedChatServerIndoServer) mustEmbedUnimplementedChatServerIndoServer() {}

// UnsafeChatServerIndoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChatServerIndoServer will
// result in compilation errors.
type UnsafeChatServerIndoServer interface {
	mustEmbedUnimplementedChatServerIndoServer()
}

func RegisterChatServerIndoServer(s *grpc.Server, srv ChatServerIndoServer) {
	s.RegisterService(&_ChatServerIndo_serviceDesc, srv)
}

func _ChatServerIndo_SendSocketChatMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(websocketMessage.WebsocketMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatServerIndoServer).SendSocketChatMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcChatServerIndo.ChatServerIndo/SendSocketChatMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatServerIndoServer).SendSocketChatMessage(ctx, req.(*websocketMessage.WebsocketMessage))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChatServerIndo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcChatServerIndo.ChatServerIndo",
	HandlerType: (*ChatServerIndoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendSocketChatMessage",
			Handler:    _ChatServerIndo_SendSocketChatMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "chatServerIndo.proto",
}
