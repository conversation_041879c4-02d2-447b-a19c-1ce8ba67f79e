package grpcPromotionIndo

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPromotionIndo"
	"google.golang.org/grpc"
)

func ConnectGRPCPromotion(endpoint string) (grpcPromotionIndo.PromotionIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPromotionIndo.NewPromotionIndoClient(connect)
	return client, connect, err
}
