package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	"go.mongodb.org/mongo-driver/bson"
)

func TestAutoSendNotiAboutNewTestAndReviewToTasker(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		apiURL := "/api/v3/sync-cron-vn/auto-send-noti-about-new-test-and-review-to-tasker"
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567870",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "HCM",
					},
					{
						"city": "DN",
					},
				},
			},
			{
				"phone":     "0834567880",
				"name":      "Tasker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "HCM",
					},
					{
						"city": "DN",
					},
				},
			},
			{
				"phone":     "0834567890",
				"name":      "Asker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"avgRating": 3.0,
				"status":    globalConstant.USER_STATUS_ACTIVE,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "HCM",
					},
					{
						"city": "DN",
					},
				},
			},
		})

		CreateTrainingTaskerCourse([]map[string]interface{}{
			{
				"_id":                     "test1",
				"title":                   "bài test số 1",
				"description":             "bài test dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "TEST",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "ACTIVE",
				"cities":                  []string{"HCM"},
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "pcZRQ6PqmjrAPe5gt",
						"name": "CLEANING",
					},
				},
				"displayPosition": "TRAINING_COURSE",
				"condition": map[string]interface{}{
					"byTasker": map[string]interface{}{
						"minimumStar": 3.9,
					},
				},
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},
			{
				"_id":                     "review1",
				"title":                   "bài review số 1",
				"description":             "bài review dành cho tasker của dịch vụ dọn dẹp nhà",
				"code":                    "Basic1",
				"timeToCompleteByMinutes": 90,
				"type":                    "REVIEW",
				"maximumNumberOfRetries":  3,
				"percentageToPass":        80,
				"deadlineIn":              30,
				"status":                  "ACTIVE",
				"cities":                  []string{"HCM"},
				"relatedServices": []map[string]interface{}{
					{
						"_id":  "pcZRQ6PqmjrAPe5gt",
						"name": "CLEANING",
					},
				},
				"displayPosition": "TRAINING_COURSE",
				"condition": map[string]interface{}{
					"byTasker": map[string]interface{}{
						"minimumStar": 3.9,
					},
				},
				"quizCollections": []map[string]interface{}{
					{
						"id":    "quiz1",
						"order": 2,
					},
				},
			},
		})
		CreateCourseStartDate([]map[string]interface{}{
			{
				"courseId":   "test1",
				"taskerId":   "0834567880",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
				"deadlineIn": 30,
			},
			{
				"courseId":   "test1",
				"taskerId":   "0834567890",
				"startDate":  globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
				"deadlineIn": 30,
			},
			{
				"courseId":  "review1",
				"taskerId":  "0834567870",
				"startDate": globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
			},
			{
				"courseId":    "test1",
				"taskerId":    "0834567870",
				"startDate":   globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
				"deadlineIn":  30,
				"isUnblocked": true,
			},
		})
		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)
				So(res.Code, ShouldEqual, 200)
				var result []*modelNotification.Notification
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[globalConstant.ISO_CODE_VN], bson.M{}, bson.M{}, &result)
				So(len(result), ShouldEqual, 3)
				So(result[0].UserId, ShouldEqual, "0834567880")
				So(result[0].Description, ShouldEqual, "Bạn đã mở khóa được bài kiểm tra bài test số 1. Hãy làm ngay nào!")
				So(result[1].UserId, ShouldEqual, "0834567890")
				So(result[1].Description, ShouldEqual, "Bạn đã mở khóa được bài kiểm tra bài test số 1. Hãy làm ngay nào!")
				So(result[2].UserId, ShouldEqual, "0834567870")
				So(result[2].Description, ShouldEqual, "Bạn đã mở khóa được bài ôn tập bài review số 1. Hãy làm ngay nào!")
			})
		})
	})
}
