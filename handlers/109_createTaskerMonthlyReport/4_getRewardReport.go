package createTaskerMonthlyReport

import (
	"sync"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelPointTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pointTransaction"
	modelReward "gitlab.com/btaskee/go-services-model-v2/grpcmodel/reward"
	"go.mongodb.org/mongo-driver/bson"
)

// getReviewReport returns a channel of TaskerYearEndReport objects.
// It takes in a WaitGroup pointer, a channel of TaskerYearEndReport objects,
// and startYear and endYear time.Time values as parameters.
// The returned channel contains TaskerYearEndReport objects with updated
// TotalBPoint and TotalMonthlyRewardAmount values.
func getTaskerReward(w *sync.WaitGroup, taskCh chan *modelReport, startAMonth, endAMonth time.Time) chan *modelReport {
	// Create a channel to store the updated TaskerYearEndReport objects
	outC := make(chan *modelReport, 100)

	// Start a goroutine to process the TaskerYearEndReport objects
	go func() {
		defer w.Done()
		// Close the output channel when the input channel is closed
		defer close(outC)

		// Process each TaskerYearEndReport object from the input channel
		for rp := range taskCh {
			// If normal tasker and doesn't have task done last month - 1, skip
			if !rp.IsCompanyReport && rp.LastDoneTask.Before(startAMonth.AddDate(0, -1, 0)) {
				// Đưa data vào trong channel
				outC <- rp
				continue
			}
			if rp.MonthlyReportInfo != nil {
				// Update the TotalBPoint value of the TaskerYearEndReport object
				rp.MonthlyReportInfo.TotalBPoint = getTotalBPointByTasker(rp, startAMonth, endAMonth)

				// Update the TotalMonthlyRewardAmount value of the TaskerYearEndReport object
				rp.MonthlyReportInfo.TotalMonthlyRewardAmount = getTotalMonthlyReward(rp, startAMonth, endAMonth)
			}

			// Send the updated TaskerYearEndReport object to the output channel
			outC <- rp
		}
	}()

	// Return the output channel
	return outC
}

// getTotalBPointByTasker calculates the total BPoint earned by a tasker within a given time period.
// It takes the tasker ID, start year, and end year as input parameters.
// It returns the total BPoint earned by the tasker.
func getTotalBPointByTasker(report *modelReport, startAMonth, endAMonth time.Time) float64 {
	if report.IsCompanyReport { // Company not has bPoint
		return 0
	}
	// Define the query to filter the point transactions.
	query := bson.M{
		"userId":    report.TaskerId,
		"createdAt": bson.M{"$gte": startAMonth, "$lte": endAMonth},
		"type":      "D",
	}
	// Retrieve the point transactions that match the query.
	var pointTrans []*modelPointTransaction.PointTransaction
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASKER_POINT_TRANSACTION[local.ISO_CODE], query, bson.M{"point": 1}, &pointTrans)
	// Calculate the total point earned by summing up the points from each transaction.
	var totalPoint float64 = 0
	for _, tran := range pointTrans {
		totalPoint += tran.Point
	}

	return totalPoint
}

// getTotalMonthlyReward calculates the total monthly reward amount for a given tasker and time range.
// The function takes in the tasker ID, start and end year as parameters and returns the total monthly reward amount as a float64.
func getTotalMonthlyReward(report *modelReport, startAMonth, endAMonth time.Time) float64 {
	// Construct the query to find rewards for the given tasker, type and time range
	query := bson.M{ // Bỏ type vì kg có index. Lọc lại bằng source go
		"userId":    report.TaskerId,
		"createdAt": bson.M{"$gte": startAMonth, "$lte": endAMonth},
	}
	if report.IsCompanyReport {
		query["userId"] = bson.M{"$in": report.EmployeeIds}
	}
	// Get all rewards matching the query
	var rewards []*modelReward.Reward
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_REWARD[globalConstant.ISO_CODE_VN], query, bson.M{"award": 1, "type": 1}, &rewards)
	// Calculate the total monthly reward amount
	var totalMonthlyRewardAmount float64 = 0
	for _, r := range rewards {
		if r.Type == "TASKER_MONTHLY_REWARD" { // Type kg có index nên dùng Go để lọc lại
			totalMonthlyRewardAmount += r.Award
		}
	}

	return totalMonthlyRewardAmount
}
