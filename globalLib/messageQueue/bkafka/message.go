package bkafka

import (
	"encoding/json"

	"github.com/IBM/sarama"
	"go.uber.org/zap"
)

type Message struct {
	message *sarama.ConsumerMessage
	session sarama.ConsumerGroupSession
}

func (m *Message) getRetryCountHeader() int {
	for _, header := range m.message.Headers {
		if string(header.Key) == "X-RetryCount" {
			return byteToInt(header.Value[0])
		}
	}
	return 0
}

func (m *Message) setRetryCountHeader(retryCount int) {
	// Update if exists
	for _, header := range m.message.Headers {
		if string(header.Key) == "X-RetryCount" {
			header.Value = []byte{intToByte(retryCount)}
			return
		}
	}

	// Append if not exists
	m.message.Headers = append(m.message.Headers, &sarama.RecordHeader{
		Key:   []byte("X-RetryCount"),
		Value: []byte{intToByte(retryCount)},
	})
}

func (m *Message) getErrorsHeader() []byte {
	for _, header := range m.message.Headers {
		if string(header.Key) == "X-Errors" {
			return header.Value
		}
	}
	return nil
}

func (m *Message) setErrorsHeader(errs []interface{}) {
	// Update if exists
	errByte, _ := json.Marshal(errs)
	for _, header := range m.message.Headers {
		if string(header.Key) == "X-Errors" {
			header.Value = errByte
			return
		}
	}

	// Append if not exists
	m.message.Headers = append(m.message.Headers, &sarama.RecordHeader{
		Key:   []byte("X-Errors"),
		Value: errByte,
	})
}

func (m *Message) pushErrorToHeader(err error) {
	errList := []interface{}{}
	errHeader := m.getErrorsHeader()
	if errHeader != nil {
		funcErr := json.Unmarshal(errHeader, &errList)
		if funcErr != nil {
			logger.Error("Failed to unmarshal error header", zap.Error(funcErr))
		}
	}
	errList = append(errList, err.Error())
	m.setErrorsHeader(errList)
}

func intToByte(i int) byte {
	return byte(i)
}

func byteToInt(b byte) int {
	return int(b)
}
