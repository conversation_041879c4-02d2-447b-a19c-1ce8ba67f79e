import { Meteor } from 'meteor/meteor';
import _ from 'lodash';
import accounting from 'accounting';
import moment from 'moment';
import {
  revertTimeZone,
  sendNotificationByIds,
} from '/server/helper-function';

const checkFirstDone = function(firstDone, from) {
  if ( moment(firstDone).isSameOrAfter( moment(from)) ) {
    // First done is >= from
    return true;
  }
  return false;
};

const runTaskerReferralsDaily = function() {
	console.log("Start runTaskerReferralDaily ...");
  // Each Active Referral
  Meteor.appCollection.TaskerReferral.find({
    status: 'ACTIVE',
    // fromDate: {$lte: new Date()},
    // toDate: {$gte: new Date()},
  }).forEach((referral) => {
    
      // Get exists taskerId list
      let existsTaskerIds = referral.taskerIds || [];
      if ( referral.test_taskerIds && referral.test_taskerIds.length > 0 ) {
        existsTaskerIds = existsTaskerIds.concat(referral.test_taskerIds);
      }
      Meteor.appCollection.users.find({
      type: 'TASKER', // Tasker only
      status: 'ACTIVE', // Active only
      createdAt: {$gte: referral.signUpFromDate}, // Sign up from referral range dates
      friendCode: {$exists: true},// Is referred by someone.
      taskDone: {$gte: referral.numberOfDone}, // Done tasks estimate >= referral.numberOfDone
      company: {$exists: false}, // Is not in company
      employeeIds: {$exists: false}, // Is not company
      _id: {$nin: existsTaskerIds}, // Make sure Tasker not in taskerIds.
      avgRating: {$gte: referral.minAvgRating},
    }, { fields: { _id: 1, friendCode: 1, name: 1, phone: 1 } }).forEach((tasker) => {
      
      // Count exact done tasks
      const exactDoneTasks = Meteor.appCollection.Task.find({
        'acceptedTasker.taskerId': tasker._id,
        status: 'DONE'
      }, {
        fields: {_id: 1, date: 1}, // Get id and date only
        sort: {date: 1} // Sort by date
      }).fetch();

      // Check first done task and last done task with fromDate, toDate
      // If Tasker enough contion, set passCondition flag is true
      let passCondition = false;

      if ( exactDoneTasks.length >= referral.numberOfDone ) {
        const firstDoneTask = exactDoneTasks[0].date;
        const endTask = exactDoneTasks[referral.numberOfDone - 1].date;
        const dateEnd = moment(firstDoneTask).add(referral.inDays, 'days');

        passCondition = checkFirstDone(firstDoneTask, referral.fromDate) && moment(endTask).isSameOrBefore(dateEnd);
      }

      if ( passCondition ) {
        // Tasker enough referral reward condition
        const referralUser = Meteor.appCollection.users.findOne({
          referralCode: tasker.friendCode,
          type: 'TASKER',
          status: 'ACTIVE',
          company: {$exists: false}, // Is not in company
          employeeIds: {$exists: false}, // Is not company
        });
        if ( referralUser && referralUser.fAccountId ) {
          if ( !referral.isTesting ) {// Not in testing

            // Push to taskerIds and results of this referrals
            Meteor.appCollection.TaskerReferral.update({ _id: referral._id }, { $push: {
              taskerIds: tasker._id,
              results: {
                referralUserId: referralUser._id,
                phone: referralUser.phone,
                name: referralUser.name,
                refCode: referralUser.referralCode,
                numberOfDone: exactDoneTasks.length,
                money: referral.reward,
                createdAt: new Date(),
                fromTaskerId: tasker._id,
                fromTaskerPhone: tasker.phone,
                fromTaskerName: tasker.name,
                isApproved: false,
                isPaid: false,
              },
            }});
          } else {
            
            // Push to taskerIds and results of this referrals
            Meteor.appCollection.TaskerReferral.update({ _id: referral._id }, { $push: {
              test_taskerIds: tasker._id,
              test_results: {
                referralUserId: referralUser._id,
                phone: referralUser.phone,
                name: referralUser.name,
                refCode: referralUser.referralCode,
                numberOfDone: exactDoneTasks.length,
                money: referral.reward,
                createdAt: new Date(),
                fromTaskerId: tasker._id,
                fromTaskerPhone: tasker.phone,
                fromTaskerName: tasker.name,
              },
            }});
          }
        }
      }
    });
  });
	console.log("Finished runTaskerReferralDaily ...");
};

const payApprovedReward = function() {
  Meteor.appCollection.TaskerReferral.find({
    status: 'ACTIVE',
    isTesting: {$ne: true},
  }).forEach((referral) => {
    if ( referral && referral.results && referral.results.length > 0 ) {
      let lastInvNumber = referral.lastInvoiceNumber || referral.code + '_0000';
      lastInvNumber = Number(lastInvNumber.split('_')[1]);

      referral.results.forEach((reward) => {
        if ( !reward.isPaid && reward.isApproved ) {
          // Deposit reward to promotion account
          const referralUser = Meteor.appCollection.users.findOne({ _id: reward.referralUserId });
          var updateFA = { Promotion: reward.money };
          const isTHCountry = referralUser.countryCode === "+66";
          if (isTHCountry) {
            updateFA = { TH_Promotion: reward.money };
          }
          Meteor.appCollection.FinancialAccount.update({ _id: referralUser.fAccountId }, { $inc: updateFA });
          
          lastInvNumber += 1;
          const invoiceString = referral.code + '_' + lastInvNumber.toString().padStart(4, '0');
          // Update paid status
          Meteor.appCollection.TaskerReferral.update({ _id: referral._id, 'results.fromTaskerId': reward.fromTaskerId }, {
            $set: {
              'results.$.isPaid': true,
              'results.$.invoice': invoiceString,
              'results.$.paidAt': new Date(),
            },
          });
          // Insert FA transaction
          const insertData = {
            userId: referralUser._id,
            accountType: 'P',
            type: 'D',
            source: {
              name: 'REFERRAL',
              value: invoiceString,
              tranferType: 'cash',
              depositDate: new Date(),
              cashierId: 'System',
              cashierName: 'System',
              rewardId: referral.code,
            },
            amount: reward.money,
            date: new Date(),
          };
          if (isTHCountry) {
            insertData.isoCode = "TH";
            Meteor.appCollection.TH_FATransaction.insert(insertData);
          } else {
            insertData.isoCode = "VN";
            Meteor.appCollection.FATransaction.insert(insertData);
          }

          // Push notification to referral user
          sendNotificationByIds([referralUser._id], {
            title: {
              vi: `Xin chúc mừng, ${reward.fromTaskerName} đã đạt mốc ${referral.numberOfDone} công việc đầu tiên. Để thay lời cảm ơn bạn đã giới thiệu, bTaskee tặng bạn ${accounting.formatNumber(reward.money)} VND vào tài khoản khuyến mãi.`,
              en: `Congratulation! ${reward.fromTaskerName} has been reached first ${referral.numberOfDone} tasks. As a reward, ${accounting.formatNumber(reward.money)} VND has been added to your promotion account.`,
              ko: `Congratulation! ${reward.fromTaskerName} has been reached first ${referral.numberOfDone} tasks. As a reward, ${accounting.formatNumber(reward.money)} VND has been added to your promotion account.`,
              th: `Congratulation! ${reward.fromTaskerName} has been reached first ${referral.numberOfDone} tasks. As a reward, ${accounting.formatNumber(reward.money)} VND has been added to your promotion account.`,
            },
            text: {
              vi: `Xin chúc mừng, ${reward.fromTaskerName} đã đạt mốc ${referral.numberOfDone} công việc đầu tiên. Để thay lời cảm ơn bạn đã giới thiệu, bTaskee tặng bạn ${accounting.formatNumber(reward.money)} VND vào tài khoản khuyến mãi.`,
              en: `Congratulation! ${reward.fromTaskerName} has been reached first ${referral.numberOfDone} tasks. As a reward, ${accounting.formatNumber(reward.money)} VND has been added to your promotion account.`,
              ko: `Congratulation! ${reward.fromTaskerName} has been reached first ${referral.numberOfDone} tasks. As a reward, ${accounting.formatNumber(reward.money)} VND has been added to your promotion account.`,
              th: `Congratulation! ${reward.fromTaskerName} has been reached first ${referral.numberOfDone} tasks. As a reward, ${accounting.formatNumber(reward.money)} VND has been added to your promotion account.`,
            },
            payload: {},
          }, {isForceView: true, type: 30});
        }
      });
      // Update last invoice number
      Meteor.appCollection.TaskerReferral.update({ _id: referral._id }, {
        $set: {
          lastInvoiceNumber: referral.code + '_' + lastInvNumber.toString().padStart(4, '0')
        }
      });
    }
  });
};

  Meteor.methods({
    taskerReferrals() {
      SyncedCron.add({
        name: 'taskerReferrals',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 20)).getHours();
          
          // Run at 20:00 every day in +7 GMT
          return parser.cron(`0 0 ${hourInTimeZoneServer} 1/1 * ? *`, true);
        },
        job() {
          runTaskerReferralsDaily();
		//TODO: need to check and rewrite this payout for referrals
          //payApprovedReward();
        },
      });
	    console.log("Added taskerReferrals cron");
    },
    'runNow.taskerReferrals'() {
      runTaskerReferralsDaily();
		//TODO: need to check and rewrite this payout for referrals
          //payApprovedReward();
    },
  });
