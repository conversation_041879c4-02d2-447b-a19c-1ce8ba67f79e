package grpcBookingTH

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcBookingTH"
	"google.golang.org/grpc"
)

func ConnectGRPCBookingTH(endpoint string) (grpcBookingTH.BookingTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcBookingTH.NewBookingTHClient(connect)
	return client, connect, err
}
