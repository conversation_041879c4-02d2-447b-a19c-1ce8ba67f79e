// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcBPointIndo

import (
	context "context"
	bPointRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/bPointRequest"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// BPointIndoClient is the client API for BPointIndo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BPointIndoClient interface {
	AccumulatePoints(ctx context.Context, in *bPointRequest.BPointRequest, opts ...grpc.CallOption) (*response.Response, error)
	TaskerAccumulatePoints(ctx context.Context, in *bPointRequest.BPointRequest, opts ...grpc.CallOption) (*response.Response, error)
	AddUserPoint(ctx context.Context, in *BPointRequest, opts ...grpc.CallOption) (*response.Response, error)
}

type bPointIndoClient struct {
	cc grpc.ClientConnInterface
}

func NewBPointIndoClient(cc grpc.ClientConnInterface) BPointIndoClient {
	return &bPointIndoClient{cc}
}

func (c *bPointIndoClient) AccumulatePoints(ctx context.Context, in *bPointRequest.BPointRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcBPointIndo.BPointIndo/AccumulatePoints", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bPointIndoClient) TaskerAccumulatePoints(ctx context.Context, in *bPointRequest.BPointRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcBPointIndo.BPointIndo/TaskerAccumulatePoints", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bPointIndoClient) AddUserPoint(ctx context.Context, in *BPointRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcBPointIndo.BPointIndo/AddUserPoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BPointIndoServer is the server API for BPointIndo service.
// All implementations must embed UnimplementedBPointIndoServer
// for forward compatibility
type BPointIndoServer interface {
	AccumulatePoints(context.Context, *bPointRequest.BPointRequest) (*response.Response, error)
	TaskerAccumulatePoints(context.Context, *bPointRequest.BPointRequest) (*response.Response, error)
	AddUserPoint(context.Context, *BPointRequest) (*response.Response, error)
	mustEmbedUnimplementedBPointIndoServer()
}

// UnimplementedBPointIndoServer must be embedded to have forward compatible implementations.
type UnimplementedBPointIndoServer struct {
}

func (UnimplementedBPointIndoServer) AccumulatePoints(context.Context, *bPointRequest.BPointRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AccumulatePoints not implemented")
}
func (UnimplementedBPointIndoServer) TaskerAccumulatePoints(context.Context, *bPointRequest.BPointRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskerAccumulatePoints not implemented")
}
func (UnimplementedBPointIndoServer) AddUserPoint(context.Context, *BPointRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddUserPoint not implemented")
}
func (UnimplementedBPointIndoServer) mustEmbedUnimplementedBPointIndoServer() {}

// UnsafeBPointIndoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BPointIndoServer will
// result in compilation errors.
type UnsafeBPointIndoServer interface {
	mustEmbedUnimplementedBPointIndoServer()
}

func RegisterBPointIndoServer(s *grpc.Server, srv BPointIndoServer) {
	s.RegisterService(&_BPointIndo_serviceDesc, srv)
}

func _BPointIndo_AccumulatePoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(bPointRequest.BPointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BPointIndoServer).AccumulatePoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBPointIndo.BPointIndo/AccumulatePoints",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BPointIndoServer).AccumulatePoints(ctx, req.(*bPointRequest.BPointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BPointIndo_TaskerAccumulatePoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(bPointRequest.BPointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BPointIndoServer).TaskerAccumulatePoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBPointIndo.BPointIndo/TaskerAccumulatePoints",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BPointIndoServer).TaskerAccumulatePoints(ctx, req.(*bPointRequest.BPointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BPointIndo_AddUserPoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BPointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BPointIndoServer).AddUserPoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBPointIndo.BPointIndo/AddUserPoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BPointIndoServer).AddUserPoint(ctx, req.(*BPointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _BPointIndo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcBPointIndo.BPointIndo",
	HandlerType: (*BPointIndoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AccumulatePoints",
			Handler:    _BPointIndo_AccumulatePoints_Handler,
		},
		{
			MethodName: "TaskerAccumulatePoints",
			Handler:    _BPointIndo_TaskerAccumulatePoints_Handler,
		},
		{
			MethodName: "AddUserPoint",
			Handler:    _BPointIndo_AddUserPoint_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "bPointIndo.proto",
}
