package autoPostNewFeedCommunity

import (
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelCommunityPost "gitlab.com/btaskee/go-services-model-v2/grpcmodel/communityPost"
	"go.mongodb.org/mongo-driver/bson"
)

func getPosts() []*modelCommunityPost.CommunityPost {
	now := globalLib.GetCurrentTime(local.TimeZone).Add(1 * time.Minute)
	query := bson.M{
		"scheduleTime": bson.M{
			"$lte": now,
		},
		"status": globalConstant.COMMUNITY_POST_STATUS_NOT_POSTED,
	}

	var posts []*modelCommunityPost.CommunityPost
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_COMMUNITY_POST[local.ISO_CODE],
		query,
		bson.M{
			"status": 1,
		},
		&posts,
	)

	return posts
}
