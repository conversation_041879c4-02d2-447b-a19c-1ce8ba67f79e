package extendExpiredComboVoucher

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/userComboVoucher"
	"go.mongodb.org/mongo-driver/bson"
)

func expiredComboVoucher(comboVoucherToExpire []*userComboVoucher.UserComboVoucher) {
	userComboVoucherIds := []string{}
	for _, comboVoucher := range comboVoucherToExpire {
		userComboVoucherIds = append(userComboVoucherIds, comboVoucher.XId)
	}

	_, err := globalDataAccess.UpdateAllByQuery(
		globalCollection.COLLECTION_USER_COMBO_VOUCHER[local.ISO_CODE],
		bson.M{"_id": bson.M{"$in": userComboVoucherIds}},
		bson.M{"$set": bson.M{"status": globalConstant.USER_COMBO_VOUCHER_STATUS_EXPIRED}},
	)
	if err != nil {
		msg := fmt.Sprintf("Sync cron expiredComboVoucher: Lỗi expired userComboVoucher: %v", err)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
	}
}
