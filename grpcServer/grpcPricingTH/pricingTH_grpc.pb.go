// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcPricingTH

import (
	context "context"
	pricingSubscriptionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionRequest"
	pricingSubscriptionResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionResponse"
	pricingrequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingrequest"
	pricingresponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PricingTHClient is the client API for PricingTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PricingTHClient interface {
	GetPricingAirConditioner(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingHomeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingSubscription(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingDeepCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingDisinfection(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingSofaTH(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	APIGetPricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	ReCalculatePricing(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingSubscriptionOfficeCleaning(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	APIGetPricingSubscriptionOfficeCleaning(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingHomeMoving(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingHomeMoving(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingMassage(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	RePricingMassage(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	ReCalculatePricingForIncreaseDuration(ctx context.Context, in *pricingrequest.PricingIncreaseDurationRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	APIGetPricingTaskDateOptions(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingPatientCare(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingElderlyCare(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingSubscriptionPatientCare(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingSubscriptionElderlyCare(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingAirConditionerV2(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
	GetPricingChildCare(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error)
}

type pricingTHClient struct {
	cc grpc.ClientConnInterface
}

func NewPricingTHClient(cc grpc.ClientConnInterface) PricingTHClient {
	return &pricingTHClient{cc}
}

func (c *pricingTHClient) GetPricingAirConditioner(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/GetPricingAirConditioner", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) GetPricingHomeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/GetPricingHomeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) GetPricingSubscription(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	out := new(pricingSubscriptionResponse.PricingSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/GetPricingSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) GetPricingDeepCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/GetPricingDeepCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) GetPricingDisinfection(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/GetPricingDisinfection", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) GetPricingSofaTH(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/GetPricingSofaTH", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) GetPricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/GetPricingOfficeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) APIGetPricingOfficeCleaning(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/APIGetPricingOfficeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) ReCalculatePricing(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/ReCalculatePricing", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) GetPricingSubscriptionOfficeCleaning(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	out := new(pricingSubscriptionResponse.PricingSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/GetPricingSubscriptionOfficeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) APIGetPricingSubscriptionOfficeCleaning(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	out := new(pricingSubscriptionResponse.PricingSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/APIGetPricingSubscriptionOfficeCleaning", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) GetPricingHomeMoving(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/GetPricingHomeMoving", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) RePricingHomeMoving(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/RePricingHomeMoving", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) GetPricingMassage(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/GetPricingMassage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) RePricingMassage(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/RePricingMassage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) ReCalculatePricingForIncreaseDuration(ctx context.Context, in *pricingrequest.PricingIncreaseDurationRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/ReCalculatePricingForIncreaseDuration", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) APIGetPricingTaskDateOptions(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/APIGetPricingTaskDateOptions", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) GetPricingPatientCare(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/GetPricingPatientCare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) GetPricingElderlyCare(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/GetPricingElderlyCare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) GetPricingSubscriptionPatientCare(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	out := new(pricingSubscriptionResponse.PricingSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/GetPricingSubscriptionPatientCare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) GetPricingSubscriptionElderlyCare(ctx context.Context, in *pricingSubscriptionRequest.PricingSubscriptionRequest, opts ...grpc.CallOption) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	out := new(pricingSubscriptionResponse.PricingSubscriptionResponse)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/GetPricingSubscriptionElderlyCare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) GetPricingAirConditionerV2(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/GetPricingAirConditionerV2", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pricingTHClient) GetPricingChildCare(ctx context.Context, in *pricingrequest.PricingRequest, opts ...grpc.CallOption) (*pricingresponse.CostResult, error) {
	out := new(pricingresponse.CostResult)
	err := c.cc.Invoke(ctx, "/grpcPricingTH.PricingTH/GetPricingChildCare", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PricingTHServer is the server API for PricingTH service.
// All implementations must embed UnimplementedPricingTHServer
// for forward compatibility
type PricingTHServer interface {
	GetPricingAirConditioner(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingHomeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingSubscription(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingDeepCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingDisinfection(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingSofaTH(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	APIGetPricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	ReCalculatePricing(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingSubscriptionOfficeCleaning(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	APIGetPricingSubscriptionOfficeCleaning(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingHomeMoving(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingHomeMoving(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingMassage(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	RePricingMassage(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	ReCalculatePricingForIncreaseDuration(context.Context, *pricingrequest.PricingIncreaseDurationRequest) (*pricingresponse.CostResult, error)
	APIGetPricingTaskDateOptions(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingPatientCare(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingElderlyCare(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingSubscriptionPatientCare(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingSubscriptionElderlyCare(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error)
	GetPricingAirConditionerV2(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	GetPricingChildCare(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error)
	mustEmbedUnimplementedPricingTHServer()
}

// UnimplementedPricingTHServer must be embedded to have forward compatible implementations.
type UnimplementedPricingTHServer struct {
}

func (UnimplementedPricingTHServer) GetPricingAirConditioner(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingAirConditioner not implemented")
}
func (UnimplementedPricingTHServer) GetPricingHomeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingHomeCleaning not implemented")
}
func (UnimplementedPricingTHServer) GetPricingSubscription(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingSubscription not implemented")
}
func (UnimplementedPricingTHServer) GetPricingDeepCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingDeepCleaning not implemented")
}
func (UnimplementedPricingTHServer) GetPricingDisinfection(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingDisinfection not implemented")
}
func (UnimplementedPricingTHServer) GetPricingSofaTH(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingSofaTH not implemented")
}
func (UnimplementedPricingTHServer) GetPricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingOfficeCleaning not implemented")
}
func (UnimplementedPricingTHServer) APIGetPricingOfficeCleaning(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method APIGetPricingOfficeCleaning not implemented")
}
func (UnimplementedPricingTHServer) ReCalculatePricing(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReCalculatePricing not implemented")
}
func (UnimplementedPricingTHServer) GetPricingSubscriptionOfficeCleaning(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingSubscriptionOfficeCleaning not implemented")
}
func (UnimplementedPricingTHServer) APIGetPricingSubscriptionOfficeCleaning(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method APIGetPricingSubscriptionOfficeCleaning not implemented")
}
func (UnimplementedPricingTHServer) GetPricingHomeMoving(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingHomeMoving not implemented")
}
func (UnimplementedPricingTHServer) RePricingHomeMoving(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingHomeMoving not implemented")
}
func (UnimplementedPricingTHServer) GetPricingMassage(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingMassage not implemented")
}
func (UnimplementedPricingTHServer) RePricingMassage(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RePricingMassage not implemented")
}
func (UnimplementedPricingTHServer) ReCalculatePricingForIncreaseDuration(context.Context, *pricingrequest.PricingIncreaseDurationRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReCalculatePricingForIncreaseDuration not implemented")
}
func (UnimplementedPricingTHServer) APIGetPricingTaskDateOptions(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method APIGetPricingTaskDateOptions not implemented")
}
func (UnimplementedPricingTHServer) GetPricingPatientCare(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingPatientCare not implemented")
}
func (UnimplementedPricingTHServer) GetPricingElderlyCare(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingElderlyCare not implemented")
}
func (UnimplementedPricingTHServer) GetPricingSubscriptionPatientCare(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingSubscriptionPatientCare not implemented")
}
func (UnimplementedPricingTHServer) GetPricingSubscriptionElderlyCare(context.Context, *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingSubscriptionElderlyCare not implemented")
}
func (UnimplementedPricingTHServer) GetPricingAirConditionerV2(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingAirConditionerV2 not implemented")
}
func (UnimplementedPricingTHServer) GetPricingChildCare(context.Context, *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPricingChildCare not implemented")
}
func (UnimplementedPricingTHServer) mustEmbedUnimplementedPricingTHServer() {}

// UnsafePricingTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PricingTHServer will
// result in compilation errors.
type UnsafePricingTHServer interface {
	mustEmbedUnimplementedPricingTHServer()
}

func RegisterPricingTHServer(s *grpc.Server, srv PricingTHServer) {
	s.RegisterService(&_PricingTH_serviceDesc, srv)
}

func _PricingTH_GetPricingAirConditioner_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).GetPricingAirConditioner(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/GetPricingAirConditioner",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).GetPricingAirConditioner(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_GetPricingHomeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).GetPricingHomeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/GetPricingHomeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).GetPricingHomeCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_GetPricingSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingSubscriptionRequest.PricingSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).GetPricingSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/GetPricingSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).GetPricingSubscription(ctx, req.(*pricingSubscriptionRequest.PricingSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_GetPricingDeepCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).GetPricingDeepCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/GetPricingDeepCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).GetPricingDeepCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_GetPricingDisinfection_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).GetPricingDisinfection(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/GetPricingDisinfection",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).GetPricingDisinfection(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_GetPricingSofaTH_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).GetPricingSofaTH(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/GetPricingSofaTH",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).GetPricingSofaTH(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_GetPricingOfficeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).GetPricingOfficeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/GetPricingOfficeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).GetPricingOfficeCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_APIGetPricingOfficeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).APIGetPricingOfficeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/APIGetPricingOfficeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).APIGetPricingOfficeCleaning(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_ReCalculatePricing_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).ReCalculatePricing(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/ReCalculatePricing",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).ReCalculatePricing(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_GetPricingSubscriptionOfficeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingSubscriptionRequest.PricingSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).GetPricingSubscriptionOfficeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/GetPricingSubscriptionOfficeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).GetPricingSubscriptionOfficeCleaning(ctx, req.(*pricingSubscriptionRequest.PricingSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_APIGetPricingSubscriptionOfficeCleaning_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingSubscriptionRequest.PricingSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).APIGetPricingSubscriptionOfficeCleaning(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/APIGetPricingSubscriptionOfficeCleaning",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).APIGetPricingSubscriptionOfficeCleaning(ctx, req.(*pricingSubscriptionRequest.PricingSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_GetPricingHomeMoving_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).GetPricingHomeMoving(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/GetPricingHomeMoving",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).GetPricingHomeMoving(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_RePricingHomeMoving_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).RePricingHomeMoving(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/RePricingHomeMoving",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).RePricingHomeMoving(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_GetPricingMassage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).GetPricingMassage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/GetPricingMassage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).GetPricingMassage(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_RePricingMassage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).RePricingMassage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/RePricingMassage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).RePricingMassage(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_ReCalculatePricingForIncreaseDuration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingIncreaseDurationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).ReCalculatePricingForIncreaseDuration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/ReCalculatePricingForIncreaseDuration",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).ReCalculatePricingForIncreaseDuration(ctx, req.(*pricingrequest.PricingIncreaseDurationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_APIGetPricingTaskDateOptions_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).APIGetPricingTaskDateOptions(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/APIGetPricingTaskDateOptions",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).APIGetPricingTaskDateOptions(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_GetPricingPatientCare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).GetPricingPatientCare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/GetPricingPatientCare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).GetPricingPatientCare(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_GetPricingElderlyCare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).GetPricingElderlyCare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/GetPricingElderlyCare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).GetPricingElderlyCare(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_GetPricingSubscriptionPatientCare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingSubscriptionRequest.PricingSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).GetPricingSubscriptionPatientCare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/GetPricingSubscriptionPatientCare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).GetPricingSubscriptionPatientCare(ctx, req.(*pricingSubscriptionRequest.PricingSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_GetPricingSubscriptionElderlyCare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingSubscriptionRequest.PricingSubscriptionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).GetPricingSubscriptionElderlyCare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/GetPricingSubscriptionElderlyCare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).GetPricingSubscriptionElderlyCare(ctx, req.(*pricingSubscriptionRequest.PricingSubscriptionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_GetPricingAirConditionerV2_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).GetPricingAirConditionerV2(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/GetPricingAirConditionerV2",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).GetPricingAirConditionerV2(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PricingTH_GetPricingChildCare_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pricingrequest.PricingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PricingTHServer).GetPricingChildCare(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPricingTH.PricingTH/GetPricingChildCare",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PricingTHServer).GetPricingChildCare(ctx, req.(*pricingrequest.PricingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PricingTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcPricingTH.PricingTH",
	HandlerType: (*PricingTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetPricingAirConditioner",
			Handler:    _PricingTH_GetPricingAirConditioner_Handler,
		},
		{
			MethodName: "GetPricingHomeCleaning",
			Handler:    _PricingTH_GetPricingHomeCleaning_Handler,
		},
		{
			MethodName: "GetPricingSubscription",
			Handler:    _PricingTH_GetPricingSubscription_Handler,
		},
		{
			MethodName: "GetPricingDeepCleaning",
			Handler:    _PricingTH_GetPricingDeepCleaning_Handler,
		},
		{
			MethodName: "GetPricingDisinfection",
			Handler:    _PricingTH_GetPricingDisinfection_Handler,
		},
		{
			MethodName: "GetPricingSofaTH",
			Handler:    _PricingTH_GetPricingSofaTH_Handler,
		},
		{
			MethodName: "GetPricingOfficeCleaning",
			Handler:    _PricingTH_GetPricingOfficeCleaning_Handler,
		},
		{
			MethodName: "APIGetPricingOfficeCleaning",
			Handler:    _PricingTH_APIGetPricingOfficeCleaning_Handler,
		},
		{
			MethodName: "ReCalculatePricing",
			Handler:    _PricingTH_ReCalculatePricing_Handler,
		},
		{
			MethodName: "GetPricingSubscriptionOfficeCleaning",
			Handler:    _PricingTH_GetPricingSubscriptionOfficeCleaning_Handler,
		},
		{
			MethodName: "APIGetPricingSubscriptionOfficeCleaning",
			Handler:    _PricingTH_APIGetPricingSubscriptionOfficeCleaning_Handler,
		},
		{
			MethodName: "GetPricingHomeMoving",
			Handler:    _PricingTH_GetPricingHomeMoving_Handler,
		},
		{
			MethodName: "RePricingHomeMoving",
			Handler:    _PricingTH_RePricingHomeMoving_Handler,
		},
		{
			MethodName: "GetPricingMassage",
			Handler:    _PricingTH_GetPricingMassage_Handler,
		},
		{
			MethodName: "RePricingMassage",
			Handler:    _PricingTH_RePricingMassage_Handler,
		},
		{
			MethodName: "ReCalculatePricingForIncreaseDuration",
			Handler:    _PricingTH_ReCalculatePricingForIncreaseDuration_Handler,
		},
		{
			MethodName: "APIGetPricingTaskDateOptions",
			Handler:    _PricingTH_APIGetPricingTaskDateOptions_Handler,
		},
		{
			MethodName: "GetPricingPatientCare",
			Handler:    _PricingTH_GetPricingPatientCare_Handler,
		},
		{
			MethodName: "GetPricingElderlyCare",
			Handler:    _PricingTH_GetPricingElderlyCare_Handler,
		},
		{
			MethodName: "GetPricingSubscriptionPatientCare",
			Handler:    _PricingTH_GetPricingSubscriptionPatientCare_Handler,
		},
		{
			MethodName: "GetPricingSubscriptionElderlyCare",
			Handler:    _PricingTH_GetPricingSubscriptionElderlyCare_Handler,
		},
		{
			MethodName: "GetPricingAirConditionerV2",
			Handler:    _PricingTH_GetPricingAirConditionerV2_Handler,
		},
		{
			MethodName: "GetPricingChildCare",
			Handler:    _PricingTH_GetPricingChildCare_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pricingTH.proto",
}
