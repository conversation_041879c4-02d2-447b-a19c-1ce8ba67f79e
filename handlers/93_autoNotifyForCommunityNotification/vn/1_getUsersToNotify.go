package autoNotifyForCommunityNotification

import (
	"encoding/json"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.mongodb.org/mongo-driver/bson"
)

func getUsersToNotify() []string {
	now := globalLib.GetCurrentTime(local.TimeZone)
	hour := now.Hour()

	query := bson.M{
		"isRead":    bson.M{"$ne": true},
		"createdAt": bson.M{"$gt": now.Add(-3 * time.Hour)},
	}

	// If the cronjob runs at 8:00, get unread notifications from 23:00 to 8:00, otherwise unread notifications from 3 hours ago
	if hour == 8 {
		query["createdAt"] = bson.M{"$gt": now.Add(-9 * time.Hour)}
	}

	usersMap, _ := globalDataAccess.GetDistinctByQuery(globalCollection.COLLECTION_COMMUNITY_NOTIFICATION[globalConstant.ISO_CODE_VN], "userId", query)
	var listUser []string
	usersMapData, _ := json.Marshal(usersMap)
	json.Unmarshal(usersMapData, &listUser)
	return listUser
}
