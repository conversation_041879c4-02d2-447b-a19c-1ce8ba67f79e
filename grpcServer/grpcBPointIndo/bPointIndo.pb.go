// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.7.1
// source: bPointIndo.proto

package grpcBPointIndo

import (
	bPointRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/bPointRequest"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type BPointRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId            string  `protobuf:"bytes,1,opt,name=taskId,proto3" json:"taskId,omitempty"`
	TaskerId          string  `protobuf:"bytes,2,opt,name=taskerId,proto3" json:"taskerId,omitempty"`
	Point             float64 `protobuf:"fixed64,3,opt,name=point,proto3" json:"point,omitempty"`
	SpecialCampaignId string  `protobuf:"bytes,4,opt,name=specialCampaignId,proto3" json:"specialCampaignId,omitempty"`
	UserId            string  `protobuf:"bytes,5,opt,name=userId,proto3" json:"userId,omitempty"`
	SourceName        string  `protobuf:"bytes,6,opt,name=sourceName,proto3" json:"sourceName,omitempty"`
}

func (x *BPointRequest) Reset() {
	*x = BPointRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_bPointIndo_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BPointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BPointRequest) ProtoMessage() {}

func (x *BPointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_bPointIndo_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BPointRequest.ProtoReflect.Descriptor instead.
func (*BPointRequest) Descriptor() ([]byte, []int) {
	return file_bPointIndo_proto_rawDescGZIP(), []int{0}
}

func (x *BPointRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *BPointRequest) GetTaskerId() string {
	if x != nil {
		return x.TaskerId
	}
	return ""
}

func (x *BPointRequest) GetPoint() float64 {
	if x != nil {
		return x.Point
	}
	return 0
}

func (x *BPointRequest) GetSpecialCampaignId() string {
	if x != nil {
		return x.SpecialCampaignId
	}
	return ""
}

func (x *BPointRequest) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *BPointRequest) GetSourceName() string {
	if x != nil {
		return x.SourceName
	}
	return ""
}

var File_bPointIndo_proto protoreflect.FileDescriptor

var file_bPointIndo_proto_rawDesc = []byte{
	0x0a, 0x10, 0x62, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x6e, 0x64, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0e, 0x67, 0x72, 0x70, 0x63, 0x42, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x6e,
	0x64, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x49, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61,
	0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x53, 0x67, 0x69, 0x74,
	0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f,
	0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x62,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2f, 0x62, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xbf, 0x01, 0x0a, 0x0d, 0x42, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x74, 0x61,
	0x73, 0x6b, 0x65, 0x72, 0x49, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61,
	0x73, 0x6b, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x01, 0x52, 0x05, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x2c, 0x0a, 0x11,
	0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49,
	0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x73, 0x70, 0x65, 0x63, 0x69, 0x61, 0x6c,
	0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x75, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x32, 0x90, 0x02, 0x0a, 0x0a, 0x42, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x6e, 0x64,
	0x6f, 0x12, 0x6f, 0x0a, 0x10, 0x41, 0x63, 0x63, 0x75, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50,
	0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x1c, 0x2e, 0x62, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x29, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x22,
	0x1e, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x62, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x2d,
	0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x61, 0x63, 0x63, 0x75, 0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x3a,
	0x01, 0x2a, 0x12, 0x4c, 0x0a, 0x16, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x41, 0x63, 0x63, 0x75,
	0x6d, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x1c, 0x2e, 0x62,
	0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x42, 0x50, 0x6f,
	0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x43, 0x0a, 0x0c, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x50, 0x6f, 0x69, 0x6e, 0x74,
	0x12, 0x1d, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x42, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x6e, 0x64,
	0x6f, 0x2e, 0x42, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x43, 0x5a, 0x41, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32,
	0x2f, 0x67, 0x72, 0x70, 0x63, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x67, 0x72, 0x70, 0x63,
	0x42, 0x50, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x6e, 0x64, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_bPointIndo_proto_rawDescOnce sync.Once
	file_bPointIndo_proto_rawDescData = file_bPointIndo_proto_rawDesc
)

func file_bPointIndo_proto_rawDescGZIP() []byte {
	file_bPointIndo_proto_rawDescOnce.Do(func() {
		file_bPointIndo_proto_rawDescData = protoimpl.X.CompressGZIP(file_bPointIndo_proto_rawDescData)
	})
	return file_bPointIndo_proto_rawDescData
}

var file_bPointIndo_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_bPointIndo_proto_goTypes = []interface{}{
	(*BPointRequest)(nil),               // 0: grpcBPointIndo.BPointRequest
	(*bPointRequest.BPointRequest)(nil), // 1: bPointRequest.BPointRequest
	(*response.Response)(nil),           // 2: response.Response
}
var file_bPointIndo_proto_depIdxs = []int32{
	1, // 0: grpcBPointIndo.BPointIndo.AccumulatePoints:input_type -> bPointRequest.BPointRequest
	1, // 1: grpcBPointIndo.BPointIndo.TaskerAccumulatePoints:input_type -> bPointRequest.BPointRequest
	0, // 2: grpcBPointIndo.BPointIndo.AddUserPoint:input_type -> grpcBPointIndo.BPointRequest
	2, // 3: grpcBPointIndo.BPointIndo.AccumulatePoints:output_type -> response.Response
	2, // 4: grpcBPointIndo.BPointIndo.TaskerAccumulatePoints:output_type -> response.Response
	2, // 5: grpcBPointIndo.BPointIndo.AddUserPoint:output_type -> response.Response
	3, // [3:6] is the sub-list for method output_type
	0, // [0:3] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_bPointIndo_proto_init() }
func file_bPointIndo_proto_init() {
	if File_bPointIndo_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_bPointIndo_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BPointRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_bPointIndo_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_bPointIndo_proto_goTypes,
		DependencyIndexes: file_bPointIndo_proto_depIdxs,
		MessageInfos:      file_bPointIndo_proto_msgTypes,
	}.Build()
	File_bPointIndo_proto = out.File
	file_bPointIndo_proto_rawDesc = nil
	file_bPointIndo_proto_goTypes = nil
	file_bPointIndo_proto_depIdxs = nil
}
