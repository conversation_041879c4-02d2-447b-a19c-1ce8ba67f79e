{"name": "meteor-workflow", "private": true, "scripts": {"start": "meteor run", "test": "meteor test --once --driver-package meteortesting:mocha", "test-app": "TEST_WATCH=1 meteor test --full-app --driver-package meteortesting:mocha", "visualize": "meteor --production --extra-packages bundle-visualizer"}, "dependencies": {"@babel/runtime": "^7.15.4", "@mapbox/node-pre-gyp": "^1.0.8", "accounting": "^0.4.1", "form-data": "^4.0.0", "lodash": "^4.17.21", "mailgun.js": "^4.2.2", "meteor-node-stubs": "^1.1.0", "moment": "^2.29.4", "moment-timezone": "^0.5.43", "react": "^17.0.2", "react-dom": "^17.0.2"}, "meteor": {"mainModule": {"server": "server/main.js"}, "testModule": "tests/main.js"}}