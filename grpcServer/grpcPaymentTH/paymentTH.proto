syntax = "proto3";
package grpcPaymentTH;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPaymentTH";

import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentRequest/payment-request.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response/response.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentTransaction/payment-transaction.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/payment2C2PTransaction/payment2C2PTransaction.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/shopeePayTransaction/shopeePayTransaction.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/refundTransaction/refundTransaction.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/vatRequest/vatRequest.proto";

service PaymentTH {
  rpc SubscriptionPaymentTH (paymentRequest.PaymentRequest) returns (response.Response) {}
  rpc ChargePaymentTH (paymentTransaction.PaymentTransaction) returns (response.Response) {}
  rpc RefundTH (refundTransaction.RefundTransaction) returns (response.Response) {}
  rpc CheckTransactionStatus2C2P (payment2C2PTransaction.Payment2C2PTransaction) returns (response.Response) {}
  rpc CheckTransactionStatusShopeePayTH (shopeePayTransaction.ShopeePayTransaction) returns (response.Response) {}
  rpc CreateGiftByComboVoucherTH (paymentRequest.PaymentRequest) returns (response.Response) {}
  rpc CreateTHVatRequest (vatRequest.VatRequest) returns (response.Response) {}
}