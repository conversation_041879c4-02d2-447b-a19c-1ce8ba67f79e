package grpcSendTask

import (
	"context"

	"gitlab.com/btaskee/go-push-notification-new-task-service-v2/grpcPushNotificationNewTask"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcSendTaskIndo"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	"google.golang.org/grpc"
)

type PushNotificationNewTaskClient interface {
	NewTask(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	TopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	NewTaskToDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	FavTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	Normal(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	FavAndTaskerDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	FavAndTopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	NewTaskToCity(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	ForceTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
}

func Connect(isoCode, endpoint string) (PushNotificationNewTaskClient, *grpc.ClientConn, error) {
	switch isoCode {
	case globalConstant.ISO_CODE_VN:
		return ConnectGRPCSendTaskVN(endpoint)
	case globalConstant.ISO_CODE_TH:
		return ConnectGRPCSendTaskTH(endpoint)
	case globalConstant.ISO_CODE_INDO:
		return ConnectGRPCSendTaskINDO(endpoint)
	}
	return nil, nil, nil
}

func ConnectGRPCSendTaskVN(endpoint string) (grpcPushNotificationNewTask.PushNotificationNewTaskClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPushNotificationNewTask.NewPushNotificationNewTaskClient(connect)
	return client, connect, err
}

func ConnectGRPCSendTaskTH(endpoint string) (grpcPushNotificationNewTask.PushNotificationNewTaskClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPushNotificationNewTask.NewPushNotificationNewTaskClient(connect)
	return client, connect, err
}

func ConnectGRPCSendTaskINDO(endpoint string) (grpcSendTaskIndo.SendTaskIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcSendTaskIndo.NewSendTaskIndoClient(connect)
	return client, connect, err
}
