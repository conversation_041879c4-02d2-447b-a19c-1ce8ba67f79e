package weeklyResetLeaderBoard

import (
	"fmt"

	"github.com/robfig/cron/v3"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

var c *cron.Cron
var cfg = config.GetConfig()
var isRunning = false
var paramsEnough = true
var runAt string

func init() {
	var cronConfig map[string]interface{}
	if cfg != nil {
		cronConfig = cfg.ScheduleSyncCron["weeklyresetleaderboard"]
	}

	// Check if params enough
	if cronConfig["run_at"] == nil {
		paramsEnough = false
		lib.PostToSlackNotStart("AutoWeeklyResetLeaderBoard")
		return
	}

	// Get runAt string for sync cron
	runAt = cronConfig["run_at"].(string)

	// Check if config has is_run = true
	if cronConfig["is_run"] == nil || !cronConfig["is_run"].(bool) {
		lib.PostToSlackNotStart("AutoWeeklyResetLeaderBoard")
		return
	}

	StartCron()
}

func StartCron() {
	c := cron.New(cron.WithLocation(local.TimeZone))
	c.AddFunc(runAt, autoWeeklyResetLeaderBoard)
	c.Start()
	isRunning = true
}

func Run(reqBody *model.RequestAction) interface{} {
	// action == 1 -> Run sync cron once now
	if reqBody.Action == lib.RUN_NOW {
		err := weeklyResetLeaderboard(reqBody.RunUsers)
		if err != nil {
			msg := fmt.Sprintf("AUTO_WEEKLY_RESET_LEADER_BOARD_%s error: %v", reqBody.IsoCode, err)
			globalLib.PostToSlack(cfg.SlackToken, "go-journey-tasker", globalConstant.SLACK_USER_NAME, msg)
			return nil
		}
		return err
	}
	// action == 2 -> Start sync cron with schedule config
	if reqBody.Action == lib.START && paramsEnough && !isRunning {
		StartCron()
	}
	// action == 3 -> Stop running sync cron
	if reqBody.Action == lib.STOP && isRunning {
		c.Stop()
		c = nil
		isRunning = false
	}
	return nil
}

const (
	CHUNK_SIZE_GET_RATINGS           = 500
	CHUNK_SIZE_GET_TASKERS           = 300
	CHUNK_SIZE_GET_LEADER_BOARD      = 300
	CHUNK_SIZE_TASKERS_GET_DONE_TASK = 100
	CHUNK_SIZE_INSERT_LEADER_BOARD   = 50
	CHUNK_SIZE_DELETE_LEADER_BOARD   = 300
)

func autoWeeklyResetLeaderBoard() {
	weeklyResetLeaderboard(nil)
}

func weeklyResetLeaderboard(taskerList []string) error {
	var err error
	err = resetLeaderBoard(taskerList)
	if err != nil {
		return err
	}
	_, err = weeklyInsertLeaderBoardData(taskerList)
	return err
}
