package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/journeyLeaderBoard"
	"go.mongodb.org/mongo-driver/bson"
)

func TestCalculateJourneyLeaderBoard(t *testing.T) {
	apiUrl := apiSyncCron + "/auto-calculate-journey-leader-board"

	t.Run("1", func(t *testing.T) {
		ResetData()
		initJourneySettingTest()
		lastDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1)

		// ================= Hồ Chí Minh =================== 1 - > 9
		CreateUser([]map[string]interface{}{
			{ // Asker
				"phone":  "0834567000",
				"name":   "Asker 00",
				"type":   globalConstant.USER_TYPE_ASKER,
				"avatar": "abc_0834567000",
			},
			{ // 00. tasker not in service channel
				"phone": "0834567800",
				"name":  "Tasker 00",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"avatar": "abc_0834567800",
			},
			{ // 01. tasker not in city support journey
				"phone": "0834567801",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Đắk Lắk",
					},
					{
						"city": "Hồ Chí Minh",
					},
				},
				"avatar": "abc_0834567801",
			},
			{ // 02. tasker not have point in day
				"phone": "0834567802",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"avatar": "abc_0834567802",
			},
			{ // 03. tasker upgrade to first level in day (TASKER JUST ACTIVE, NOT IN LEADER_BOARD YET)
				"phone": "0834567803",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV0",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV1",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
				"avatar": "abc_0834567803",
			},
			{ // 04. tasker upgrade to high level in day (HAVE BEEN IN LEADER_BOARD YET)
				"phone": "0834567804",
				"name":  "Tasker 04",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV2",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
				"avatar": "abc_0834567804",
			},
			{ // 05. tasker upgrade to first level in day (TASKER JUST ACTIVE, NOT IN LEADER_BOARD YET) + have point in day
				"phone": "0834567805",
				"name":  "Tasker 05",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV1",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV1",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
				"avatar": "abc_0834567805",
			},
			{ // 06. tasker upgrade to high level in day (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567806",
				"name":  "Tasker 06",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV2",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
				"avatar": "abc_0834567806",
			},
			{ // 07. tasker (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567807",
				"name":  "Tasker 07",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
					{
						"city": "Hà Nội",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV1",
				},
				"avatar": "abc_0834567807",
			},
			{ // 08. tasker (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567808",
				"name":  "Tasker 08",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
				},
				"avatar": "abc_0834567808",
			},
			{ // 09. tasker (HAVE NOT BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567809",
				"name":  "Tasker 09",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"avatar": "abc_0834567809",
			},
		})

		CreateJourneyLeaderBoard([]map[string]interface{}{
			{
				"_id":      "0834567804",
				"level":    "LV1",
				"rank":     1,
				"point":    19,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 04",
			},
			{
				"_id":      "0834567806",
				"level":    "LV1",
				"rank":     4,
				"point":    7,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 06",
			},
			{
				"_id":      "0834567807",
				"level":    "LV1",
				"rank":     2,
				"point":    9,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 07",
			},
			{
				"_id":      "0834567802",
				"level":    "LV1",
				"rank":     3,
				"point":    8,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 02",
			},
			{
				"_id":      "0834567808",
				"level":    "LV2",
				"rank":     1,
				"point":    2,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 08",
			},
		})

		CreateJourneyLeaderBoardBK([]map[string]interface{}{
			{
				"_id":      "0834567804",
				"level":    "LV1",
				"rank":     1,
				"point":    9,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 04",
			},
			{
				"_id":      "0834567806",
				"level":    "LV1",
				"rank":     4,
				"point":    4,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 06",
			},
			{
				"_id":      "0834567807",
				"level":    "LV1",
				"rank":     2,
				"point":    5,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 07",
			},
			{
				"_id":      "0834567802",
				"level":    "LV1",
				"rank":     3,
				"point":    3,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 02",
			},
			{
				"_id":      "0834567808",
				"level":    "LV2",
				"rank":     1,
				"point":    2,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 08",
			},
		})
		// Tasker 02: point = (1 + 1) + (2 + 2) = 6
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567802",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567802",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 05: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567805",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567805",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 06: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567806",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567806",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 07: point = (1 + 1 + 1) + (2 + 4 + 2) + (15 + 15 - 5) = 46
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567807",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567807",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567807",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})
		CreateRating([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567807",
				"rate":        5,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567807",
				"rate":        5,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567807",
				"rate":        3,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
		})

		// Tasker 08: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567808",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567808",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 09: point = (1) + (2) = 3
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567809",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// ================= Hà Nội =================== 10 -> 17
		CreateUser([]map[string]interface{}{
			{ // 10. tasker not have point in day
				"phone": "0834567810",
				"name":  "Tasker 10",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"avatar": "abc_0834567810",
			},
			{ // 11. tasker upgrade to first level in day (TASKER JUST ACTIVE, NOT IN LEADER_BOARD YET)
				"phone": "0834567811",
				"name":  "Tasker 11",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV0",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV1",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
				"avatar": "abc_0834567811",
			},
			{ // 12. tasker upgrade to high level in day (HAVE BEEN IN LEADER_BOARD YET)
				"phone": "0834567812",
				"name":  "Tasker 12",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV2",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
				"avatar": "abc_0834567812",
			},
			{ // 13. tasker upgrade to first level in day (TASKER JUST ACTIVE, NOT IN LEADER_BOARD YET) + have point in day
				"phone": "0834567813",
				"name":  "Tasker 13",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV1",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV1",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
				"avatar": "abc_0834567813",
			},
			{ // 14. tasker upgrade to high level in day (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567814",
				"name":  "Tasker 14",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV2",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
				"avatar": "abc_0834567814",
			},
			{ // 15. tasker (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567815",
				"name":  "Tasker 15",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV1",
				},
				"avatar": "abc_0834567815",
			},
			{ // 16. tasker (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567816",
				"name":  "Tasker 16",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
				},
				"avatar": "abc_0834567816",
			},
			{ // 17. tasker (HAVE NOT BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567817",
				"name":  "Tasker 17",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"avatar": "abc_0834567817",
			},
		})

		CreateJourneyLeaderBoard([]map[string]interface{}{
			{
				"_id":      "0834567812",
				"level":    "LV1",
				"rank":     1,
				"point":    19,
				"cityName": "Hà Nội",
				"name":     "Tasker 12",
			},
			{
				"_id":      "0834567814",
				"level":    "LV1",
				"rank":     4,
				"point":    7,
				"cityName": "Hà Nội",
				"name":     "Tasker 14",
			},
			{
				"_id":      "0834567815",
				"level":    "LV1",
				"rank":     2,
				"point":    9,
				"cityName": "Hà Nội",
				"name":     "Tasker 15",
			},
			{
				"_id":      "0834567810",
				"level":    "LV1",
				"rank":     3,
				"point":    8,
				"cityName": "Hà Nội",
				"name":     "Tasker 10",
			},
			{
				"_id":      "0834567816",
				"level":    "LV2",
				"rank":     1,
				"point":    2,
				"cityName": "Hà Nội",
				"name":     "Tasker 16",
			},
			{
				"_id":       "0834567834",
				"level":     "LV2",
				"rank":      1,
				"point":     2,
				"cityName":  "Hà Nội",
				"name":      "Tasker 34",
				"createdAt": globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
			},
		})

		// Tasker 10: point = (1 + 1) + (2 + 2) = 6
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567810",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567810",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 13: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567813",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567813",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 14: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567814",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567814",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 15: point = (1 + 1 + 1) + (2 + 4 + 2) + (5 + 5 - 3) = 18
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567815",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567815",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567815",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})
		CreateRating([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567815",
				"rate":        5,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567815",
				"rate":        5,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567815",
				"rate":        3,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
		})

		// Tasker 16: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567816",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567816",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 17: point = (1) + (2) = 3
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567817",
					}, {
						"taskerId": "0834567807",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		CreateUser([]map[string]interface{}{
			{ // 34. tasker not have point in day
				"phone": "0834567834",
				"name":  "Tasker 34",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"avatar": "abc_0834567834",
			}, { // 35. tasker not have point in day
				"phone": "0834567835",
				"name":  "Tasker 35",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"avatar": "abc_0834567835",
			},
		})

		// Tasker 34: point = (1) + (2) = 3
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567834",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hà Nội",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})
		// Tasker 34: point = (1) + (2) = 3
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567835",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})
		// Add tasker to service channel VN
		taskerInServiceChannel := []string{
			"0834567801", "0834567802", "0834567803", "0834567804", "0834567805", "0834567806", "0834567807", "0834567808", "0834567809", // Hồ Chí Minh
			"0834567810", "0834567811", "0834567812", "0834567813", "0834567814", "0834567815", "0834567816", "0834567817", // Hà Nội
		}
		serviceSupportJourney := []string{globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}
		AddTaskersToServiceChannel(globalConstant.ISO_CODE_VN, serviceSupportJourney, taskerInServiceChannel)

		AddTaskersToServiceChannel(globalConstant.ISO_CODE_VN, globalConstant.SERVICES_NOT_SUPPORT_JOURNEY, []string{"0834567834", "0834567835"})
		// Expect result
		/*
			Hồ Chí Minh
				LV1:
				1  |  0834567807  |  90.0 // 3 task 8 duration
				2  |  0834567806  |  51.0 // Journey LV2 but in leaderBoard LV1 => still LV1
				3  |  0834567805  |  44 // 2 task 6 duration
				4  |  0834567802  |  42 // 2 task 4 duration + 8
				5  |  0834567804  |  19 // old point
				6  |  0834567809  |  17.0 // 1 task 2 duration

				LV2:
				1  |  0834567808  |  46 // 2 task 6 duration 44 + 2

			Hà Nội
				LV1:
				1  |  0834567815  |  90.0 // 3 task 8 duration
				2  |  0834567814  |  51.0 // Journey LV2 but in leaderBoard LV1 => still LV1
				3  |  0834567813  |  44 // 2 task 6 duration
				4  |  0834567810  |  42 // 2 task 4 duration + 8
				5  |  0834567812  |  19 // old point
				6  |  0834567817  |  17.0 // 1 task 2 duration

				LV2:
				1  |  0834567816  |  46 // 2 task 6 duration 44 + 2

		*/

		Convey(fmt.Sprintf("When give a http request to %s isoCode VN", apiUrl), t, func() {
			body := map[string]interface{}{
				"action":  lib.RUN_NOW,
				"isoCode": globalConstant.ISO_CODE_VN,
			}
			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// Check Hồ Chí Minh LV1
					cityName := "Hồ Chí Minh"
					level := "LV1"
					hcmLv1 := getLeaderBoardVN(cityName, level)
					So(len(hcmLv1), ShouldEqual, 6)
					So(hcmLv1[0].XId, ShouldEqual, "0834567807")
					So(hcmLv1[0].Point, ShouldEqual, 107)
					So(hcmLv1[0].RankChange, ShouldEqual, "UP")
					So(hcmLv1[0].Avatar, ShouldEqual, "abc_0834567807")
					So(hcmLv1[1].XId, ShouldEqual, "0834567806")
					So(hcmLv1[1].Point, ShouldEqual, 51)
					So(hcmLv1[1].RankChange, ShouldEqual, "UP")
					So(hcmLv1[1].Avatar, ShouldEqual, "abc_0834567806")
					So(hcmLv1[2].XId, ShouldEqual, "0834567805")
					So(hcmLv1[2].Point, ShouldEqual, 44)
					So(hcmLv1[2].RankChange, ShouldEqual, "UP")
					So(hcmLv1[2].Avatar, ShouldEqual, "abc_0834567805")
					So(hcmLv1[3].XId, ShouldEqual, "0834567802")
					So(hcmLv1[3].Point, ShouldEqual, 42)
					So(hcmLv1[3].RankChange, ShouldEqual, "DOWN")
					So(hcmLv1[3].Avatar, ShouldEqual, "abc_0834567802")
					So(hcmLv1[4].XId, ShouldEqual, "0834567804")
					So(hcmLv1[4].Point, ShouldEqual, 19)
					So(hcmLv1[4].RankChange, ShouldEqual, "DOWN")
					So(hcmLv1[4].Avatar, ShouldEqual, "abc_0834567804")
					So(hcmLv1[5].XId, ShouldEqual, "0834567809")
					So(hcmLv1[5].Point, ShouldEqual, 17)
					So(hcmLv1[5].RankChange, ShouldEqual, "UP")
					So(hcmLv1[5].Avatar, ShouldEqual, "abc_0834567809")

					for _, v := range hcmLv1 {
						So(v.XId, ShouldNotBeIn, []string{"0834567834", "0834567835"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 1")
						So(v.Text.En, ShouldEqual, "Young Bee")
					}

					// Check Hồ Chí Minh LV2
					level = "LV2"
					hcmLv2 := getLeaderBoardVN(cityName, level)
					So(len(hcmLv2), ShouldEqual, 1)
					So(hcmLv2[0].XId, ShouldEqual, "0834567808")
					So(hcmLv2[0].Point, ShouldEqual, 46)
					So(hcmLv2[0].RankChange, ShouldEqual, "UNCHANGED")
					So(hcmLv2[0].Avatar, ShouldEqual, "abc_0834567808")
					for _, v := range hcmLv2 {
						So(v.XId, ShouldNotBeIn, []string{"0834567834", "0834567835"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 2")
						So(v.Text.En, ShouldEqual, "Mature Bee")
					}

					// Check Hà Nội LV1
					cityName = "Hà Nội"
					level = "LV1"
					hnLv1 := getLeaderBoardVN(cityName, level)
					So(hnLv1[0].XId, ShouldEqual, "0834567815")
					So(hnLv1[0].Point, ShouldEqual, 90)
					So(hnLv1[0].RankChange, ShouldEqual, "UP")
					So(hnLv1[0].Avatar, ShouldEqual, "abc_0834567815")
					So(hnLv1[1].XId, ShouldEqual, "0834567814")
					So(hnLv1[1].Point, ShouldEqual, 51)
					So(hnLv1[1].RankChange, ShouldEqual, "UP")
					So(hnLv1[1].Avatar, ShouldEqual, "abc_0834567814")
					So(hnLv1[2].XId, ShouldEqual, "0834567813")
					So(hnLv1[2].Point, ShouldEqual, 44)
					So(hnLv1[2].RankChange, ShouldEqual, "UP")
					So(hnLv1[2].Avatar, ShouldEqual, "abc_0834567813")
					So(hnLv1[3].XId, ShouldEqual, "0834567810")
					So(hnLv1[3].Point, ShouldEqual, 42)
					So(hnLv1[3].RankChange, ShouldEqual, "DOWN")
					So(hnLv1[3].Avatar, ShouldEqual, "abc_0834567810")
					So(hnLv1[4].XId, ShouldEqual, "0834567812")
					So(hnLv1[4].Point, ShouldEqual, 19)
					So(hnLv1[4].RankChange, ShouldEqual, "DOWN")
					So(hnLv1[4].Avatar, ShouldEqual, "abc_0834567812")
					So(hnLv1[5].XId, ShouldEqual, "0834567817")
					So(hnLv1[5].Point, ShouldEqual, 17)
					So(hnLv1[5].RankChange, ShouldEqual, "UP")
					So(hnLv1[5].Avatar, ShouldEqual, "abc_0834567817")
					for _, v := range hnLv1 {
						So(v.XId, ShouldNotBeIn, []string{"0834567834", "0834567835"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 1")
						So(v.Text.En, ShouldEqual, "Young Bee")
					}

					// Check Ha Noi LV2
					level = "LV2"
					hnLv2 := getLeaderBoardVN(cityName, level)
					So(hnLv2[0].XId, ShouldEqual, "0834567816")
					So(hnLv2[0].Point, ShouldEqual, 46)
					So(hnLv2[0].RankChange, ShouldEqual, "UNCHANGED")
					So(hnLv2[0].Avatar, ShouldEqual, "abc_0834567816")
					for _, v := range hnLv2 {
						So(v.XId, ShouldNotBeIn, []string{"0834567834", "0834567835"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 2")
						So(v.Text.En, ShouldEqual, "Mature Bee")
					}
				})
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		ResetData()
		initJourneySettingTest()
		lastDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1)

		// ================= Hồ Chí Minh =================== 1 - > 9
		CreateUser([]map[string]interface{}{
			{ // Asker
				"phone": "0834567000",
				"name":  "Asker 00",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{ // 00. tasker not in service channel
				"phone": "0834567800",
				"name":  "Tasker 00",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
			},
			{ // 01. tasker not in city support journey
				"phone": "0834567801",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Đắk Lắk",
					},
					{
						"city": "Hồ Chí Minh",
					},
				},
			},
			{ // 02. tasker not have point in day
				"phone": "0834567802",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
			},
			{ // 03. tasker upgrade to first level in day (TASKER JUST ACTIVE, NOT IN LEADER_BOARD YET)
				"phone": "0834567803",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV0",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV1",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
			},
			{ // 04. tasker upgrade to high level in day (HAVE BEEN IN LEADER_BOARD YET)
				"phone": "0834567804",
				"name":  "Tasker 04",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV2",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
			},
			{ // 05. tasker upgrade to first level in day (TASKER JUST ACTIVE, NOT IN LEADER_BOARD YET) + have point in day
				"phone": "0834567805",
				"name":  "Tasker 05",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV1",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV1",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
			},
			{ // 06. tasker upgrade to high level in day (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567806",
				"name":  "Tasker 06",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV2",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
			},
			{ // 07. tasker (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567807",
				"name":  "Tasker 07",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV1",
				},
			},
			{ // 08. tasker (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567808",
				"name":  "Tasker 08",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
				},
			},
			{ // 09. tasker (HAVE NOT BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567809",
				"name":  "Tasker 09",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
			},
		})

		CreateJourneyLeaderBoard([]map[string]interface{}{
			{
				"_id":      "0834567804",
				"level":    "LV1",
				"rank":     1,
				"point":    19,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 04",
			},
			{
				"_id":      "0834567806",
				"level":    "LV1",
				"rank":     4,
				"point":    7,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 06",
			},
			{
				"_id":      "0834567807",
				"level":    "LV1",
				"rank":     2,
				"point":    9,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 07",
			},
			{
				"_id":      "0834567802",
				"level":    "LV1",
				"rank":     3,
				"point":    8,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 02",
			},
			{
				"_id":      "0834567808",
				"level":    "LV2",
				"rank":     1,
				"point":    2,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 08",
			},
		})

		// Tasker 02: point = (1 + 1) + (2 + 2) = 6
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567802",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567802",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 05: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567805",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567805",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 06: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567806",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567806",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		last3Date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -3)
		last2Date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -2)
		// Tasker 07: point = (1 + 1 + 1) + (2 + 4 + 2) + (15 + 15 - 5) = 46
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", last3Date.Year(), last3Date.Month(), last3Date.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567807",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", last2Date.Year(), last2Date.Month(), last2Date.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567807",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567807",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})
		CreateRating([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567807",
				"rate":        5,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567807",
				"rate":        5,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", last3Date.Year(), last3Date.Month(), last3Date.Day()),
			},
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567807",
				"rate":        3,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", last2Date.Year(), last2Date.Month(), last2Date.Day()),
			},
		})

		// Tasker 08: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567808",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567808",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 09: point = (1) + (2) = 3
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567809",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 34: point = (1) + (2) = 3
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567835",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})
		// Add tasker to service channel VN
		taskerInServiceChannel := []string{
			"0834567801", "0834567802", "0834567803", "0834567804", "0834567805", "0834567806", "0834567807", "0834567808", "0834567809", // Hồ Chí Minh
		}
		serviceSupportJourney := []string{globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}
		AddTaskersToServiceChannel(globalConstant.ISO_CODE_VN, serviceSupportJourney, taskerInServiceChannel)
		AddTaskersToServiceChannel(globalConstant.ISO_CODE_VN, globalConstant.SERVICES_NOT_SUPPORT_JOURNEY, []string{"0834567834", "0834567835"})

		Convey(fmt.Sprintf("When give a http request to %s isoCode VN", apiUrl), t, func() {
			body := map[string]interface{}{
				"action":   lib.RUN_NOW,
				"isoCode":  globalConstant.ISO_CODE_VN,
				"runUsers": []string{"0834567801", "0834567802", "0834567803", "0834567804", "0834567805", "0834567806", "0834567807", "0834567808"},
				"fromDate": last3Date,
			}
			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// Check Hồ Chí Minh LV1
					cityName := "Hồ Chí Minh"
					level := "LV1"
					hcmLv1 := getLeaderBoardVN(cityName, level)
					So(len(hcmLv1), ShouldEqual, 5)
					So(hcmLv1[0].XId, ShouldEqual, "0834567807")
					So(hcmLv1[0].Point, ShouldEqual, 90)
					So(hcmLv1[0].RankChange, ShouldEqual, "UP")
					So(hcmLv1[1].XId, ShouldEqual, "0834567806")
					So(hcmLv1[1].Point, ShouldEqual, 51)
					So(hcmLv1[1].RankChange, ShouldEqual, "UP")
					So(hcmLv1[2].XId, ShouldEqual, "0834567805")
					So(hcmLv1[2].Point, ShouldEqual, 44)
					So(hcmLv1[2].RankChange, ShouldEqual, "UP")
					So(hcmLv1[3].XId, ShouldEqual, "0834567802")
					So(hcmLv1[3].Point, ShouldEqual, 42)
					So(hcmLv1[3].RankChange, ShouldEqual, "DOWN")
					So(hcmLv1[4].XId, ShouldEqual, "0834567804")
					So(hcmLv1[4].Point, ShouldEqual, 19)
					So(hcmLv1[4].RankChange, ShouldEqual, "DOWN")

					for _, v := range hcmLv1 {
						So(v.XId, ShouldNotBeIn, []string{"0834567834", "0834567835"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 1")
						So(v.Text.En, ShouldEqual, "Young Bee")
					}

					// Check Hồ Chí Minh LV2
					level = "LV2"
					hcmLv2 := getLeaderBoardVN(cityName, level)
					So(len(hcmLv2), ShouldEqual, 1)
					So(hcmLv2[0].XId, ShouldEqual, "0834567808")
					So(hcmLv2[0].Point, ShouldEqual, 46)
					So(hcmLv2[0].RankChange, ShouldEqual, "UNCHANGED")
					for _, v := range hcmLv2 {
						So(v.XId, ShouldNotBeIn, []string{"0834567834", "0834567835"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 2")
						So(v.Text.En, ShouldEqual, "Mature Bee")
					}
				})
			})
		})
	})

	t.Run("3", func(t *testing.T) {
		ResetData()
		initJourneySettingTest()
		lastDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1)

		// ================= Hồ Chí Minh =================== 1 - > 9
		CreateUser([]map[string]interface{}{
			{ // Asker
				"phone": "0834567000",
				"name":  "Asker 00",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
			{ // 00. tasker not in service channel
				"phone": "08567800",
				"name":  "Tasker 00",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
			},
			{ // 01. tasker not in city support journey
				"phone": "0834567801",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Đắk Lắk",
					},
					{
						"city": "Hồ Chí Minh",
					},
				},
			},
			{ // 02. tasker not have point in day
				"phone": "0834567802",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
			},
			{ // 03. tasker upgrade to first level in day (TASKER JUST ACTIVE, NOT IN LEADER_BOARD YET)
				"phone": "0834567803",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV1",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV1",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
			},
			{ // 04. tasker upgrade to high level in day (HAVE BEEN IN LEADER_BOARD YET)
				"phone": "0834567804",
				"name":  "Tasker 04",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV2",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
			},
			{ // 05. tasker upgrade to first level in day (TASKER JUST ACTIVE, NOT IN LEADER_BOARD YET) + have point in day
				"phone": "0834567805",
				"name":  "Tasker 05",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV1",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV1",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
			},
			{ // 06. tasker upgrade to high level in day (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567806",
				"name":  "Tasker 06",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV2",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
			},
			{ // 07. tasker (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567807",
				"name":  "Tasker 07",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV1",
				},
			},
			{ // 08. tasker (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567808",
				"name":  "Tasker 08",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
				},
			},
			{ // 09. tasker (HAVE NOT BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567809",
				"name":  "Tasker 09",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV0",
				},
			},
		})

		CreateJourneyLeaderBoard([]map[string]interface{}{
			{
				"_id":      "0834567804",
				"level":    "LV1",
				"rank":     -1,
				"point":    0,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 04",
			},
			{
				"_id":      "0834567806",
				"level":    "LV1",
				"rank":     -1,
				"point":    0,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 06",
			},
			{
				"_id":      "0834567807",
				"level":    "LV1",
				"rank":     -1,
				"point":    0,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 07",
			},
			{
				"_id":      "0834567802",
				"level":    "LV1",
				"rank":     -1,
				"point":    0,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 02",
			},
			{
				"_id":      "0834567808",
				"level":    "LV2",
				"rank":     -1,
				"point":    0,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 08",
			},
		})

		// Tasker 02: point = (1 + 1) + (2 + 2) = 6
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567802",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567802",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 05: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567805",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567805",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 06: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567806",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567806",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567806",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		CreateRating([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567806",
				"rate":        3,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			}, {
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567806",
				"rate":        4,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
		})
		// Tasker 07: point = (1 + 1 + 1) + (2 + 4 + 2) + (15 + 15 - 5) = 46
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567807",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567807",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567807",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})
		CreateRating([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567807",
				"rate":        5,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567807",
				"rate":        5,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567807",
				"rate":        3,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
		})

		// Tasker 08: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567808",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567808",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 09: point = (1) + (2) = 3
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567809",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		CreateUser([]map[string]interface{}{
			{ // 34. tasker not have point in day
				"phone": "0834567834",
				"name":  "Tasker 34",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
			}, { // 35. tasker not have point in day
				"phone": "0834567835",
				"name":  "Tasker 35",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
			},
		})

		// Add tasker to service channel VN
		taskerInServiceChannel := []string{
			"0834567801", "0834567802", "0834567803", "0834567804", "0834567805", "0834567806", "0834567807", "0834567808", "0834567809", // Hồ Chí Minh
		}
		serviceSupportJourney := []string{globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}
		AddTaskersToServiceChannel(globalConstant.ISO_CODE_VN, serviceSupportJourney, taskerInServiceChannel)
		// Expect result
		/*
			Hồ Chí Minh
				LV1:
				1  |  0834567807  |  90.0 // 3 task 8 duration + 20 rate
				2  |  0834567806  |  46.0 // Journey LV2 but in leaderBoard LV1 => still LV1 3*7+10*5-10
				3  |  0834567805  |  49 // 2 task 7 duration
				4  |  0834567802  |  42 // 2 task 4 duration
				5  |  0834567804  |  0 // old point
				5  |  0834567804  |  0 // old point

				LV2:
				1  |  0834567808  |  44 // 2 task 6 duration 44

				0834567809 => LV0 not run
		*/

		Convey(fmt.Sprintf("When give a http request to %s isoCode VN", apiUrl), t, func() {
			body := map[string]interface{}{
				"action":  lib.RUN_NOW,
				"isoCode": globalConstant.ISO_CODE_VN,
			}
			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// Check Hồ Chí Minh LV1
					cityName := "Hồ Chí Minh"
					level := "LV1"
					hcmLv1 := getLeaderBoardVN(cityName, level)
					So(len(hcmLv1), ShouldEqual, 6)
					So(hcmLv1[0].XId, ShouldEqual, "0834567807")
					So(hcmLv1[0].Point, ShouldEqual, 81)
					So(hcmLv1[0].RankChange, ShouldEqual, "UP")
					So(hcmLv1[1].XId, ShouldEqual, "0834567806")
					So(hcmLv1[1].Point, ShouldEqual, 61)
					So(hcmLv1[1].RankChange, ShouldEqual, "UP")
					So(hcmLv1[2].XId, ShouldEqual, "0834567805")
					So(hcmLv1[2].Point, ShouldEqual, 44)
					So(hcmLv1[2].RankChange, ShouldEqual, "UP")
					So(hcmLv1[3].XId, ShouldEqual, "0834567802")
					So(hcmLv1[3].Point, ShouldEqual, 34)
					So(hcmLv1[3].RankChange, ShouldEqual, "UP")
					So(hcmLv1[4].XId, ShouldBeIn, []string{"0834567803", "0834567804"})
					So(hcmLv1[4].Point, ShouldEqual, 0)
					So(hcmLv1[4].RankChange, ShouldEqual, "UP")
					So(hcmLv1[5].XId, ShouldBeIn, []string{"0834567803", "0834567804"})
					So(hcmLv1[5].XId, ShouldNotEqual, hcmLv1[4].XId)
					So(hcmLv1[5].Point, ShouldEqual, 0)
					So(hcmLv1[5].RankChange, ShouldEqual, "UP")

					// Check Hồ Chí Minh LV2
					level = "LV2"
					hcmLv2 := getLeaderBoardVN(cityName, level)
					So(len(hcmLv2), ShouldEqual, 1)
					So(hcmLv2[0].XId, ShouldEqual, "0834567808")
					So(hcmLv2[0].Point, ShouldEqual, 44)
					So(hcmLv2[0].RankChange, ShouldEqual, "UP")
				})
			})
		})
	})
}

func getLeaderBoardVN(cityName, level string) (result []*journeyLeaderBoard.JourneyLeaderBoard) {
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_JOURNEY_LEADER_BOARD[local.ISO_CODE], bson.M{"cityName": cityName, "level": level}, bson.M{}, bson.M{"rank": 1}, &result)
	return result
}

func initJourneySettingTest() {
	data := bson.M{
		"cityName":  "Hà Nội",
		"status":    "ACTIVE",
		"createdAt": globalLib.GetCurrentTime(local.TimeZone),
		"levels": []map[string]interface{}{
			{
				"name": "LV1",
				"title": map[string]interface{}{
					"vi": "Cấp 1",
					"en": "Level 1",
					"th": "Level 1",
					"id": "Level 1",
				},
				"text": map[string]interface{}{
					"vi": "Ong Non",
					"en": "Young Bee",
					"th": "Young Bee",
					"id": "Young Bee",
				},
				"conditions": []map[string]interface{}{
					{
						"name": "TASK",
						"text": map[string]interface{}{
							"vi": "**20** công việc",
							"en": "**20** tasks",
							"th": "**20** tasks",
							"id": "**20** tasks",
						},
						"target":    20,
						"icon":      "https://e7.pngegg.com/pngimages/923/240/png-clipart-computer-icons-graphics-computer-software-illustration-task-list-text-rectangle-thumbnail.png",
						"avgRating": 4.7,
					},
					{
						"name": "TRAINING",
						"text": map[string]interface{}{
							"vi": "Đào tạo và kiểm tra",
							"en": "Training & Test",
							"th": "Training & Test",
							"id": "Training & Test",
						},
						"target": 1,
						"training": []map[string]interface{}{
							{
								"name": "LV1_TEST_1",
								"text": map[string]interface{}{
									"vi": "Đào tạo chất lượng",
									"en": "Đào tạo chất lượng",
									"th": "Đào tạo chất lượng",
									"id": "Đào tạo chất lượng",
								},
							},
							{
								"name": "LV1_TEST_2",
								"text": map[string]interface{}{
									"vi": "Đào tạo quy định",
									"en": "Đào tạo quy định",
									"th": "ĐĐào tạo quy định",
									"id": "Đào tạo quy định",
								},
							},
							{
								"name": "LV1_TEST_3",
								"text": map[string]interface{}{
									"vi": "Thái độ làm việc",
									"en": "Thái độ làm việc",
									"th": "Thái độ làm việc",
									"id": "Thái độ làm việc",
								},
							},
						},
						"icon": "https://cdn-icons-png.flaticon.com/512/1039/1039328.png",
					},
				},
				"bonus": []map[string]interface{}{
					{
						"id":   "LV1_BREWARD_1",
						"name": "BREWARD",
						"text": map[string]interface{}{
							"vi": "Tặng **1 áo mưa**(nhận tại văn phòng)",
							"en": "Tặng **1 áo mưa**(nhận tại văn phòng)",
							"th": "Tặng **1 áo mưa**(nhận tại văn phòng)",
							"id": "Tặng **1 áo mưa**(nhận tại văn phòng)",
						},
					},
					{
						"name":   "BPOINT",
						"amount": 500,
						"text": map[string]interface{}{
							"vi": "Nhận thêm **500** bPoint",
							"en": "Receive **500** bPoint",
							"th": "Receive **500** bPoint",
							"id": "Receive **500** bPoint",
						},
					},
					{
						"name": "BONUS_PRIZE",
						"detail": []map[string]interface{}{
							{
								"name": "LV1_BONUS_PRIZE_1",
								"text": map[string]interface{}{
									"vi": "Đạt **20** công việc đầu tiên trong 10 ngày",
									"en": "Đạt **20** công việc đầu tiên trong 10 ngày",
									"th": "Đạt **20** công việc đầu tiên trong 10 ngày",
									"id": "Đạt **20** công việc đầu tiên trong 10 ngày",
								},
								"numberOfDay":  10.0,
								"numberOfTask": 20.0,
								"bPoint":       500.0,
							},
							{
								"name": "LV1_BONUS_PRIZE_2",
								"text": map[string]interface{}{
									"vi": "Đạt **20** công việc đầu tiên trong 20 ngày",
									"en": "Đạt **20** công việc đầu tiên trong 20 ngày",
									"th": "Đạt **20** công việc đầu tiên trong 20 ngày",
									"id": "Đạt **20** công việc đầu tiên trong 20 ngày",
								},
								"numberOfDay":  20.0,
								"numberOfTask": 20.0,
								"bPoint":       300.0,
							},
							{
								"name": "LV1_BONUS_PRIZE_3",
								"text": map[string]interface{}{
									"vi": "Đạt **20** công việc đầu tiên trong 30 ngày",
									"en": "Đạt **20** công việc đầu tiên trong 30 ngày",
									"th": "Đạt **20** công việc đầu tiên trong 30 ngày",
									"id": "Đạt **20** công việc đầu tiên trong 30 ngày",
								},
								"numberOfDay":  30.0,
								"numberOfTask": 20.0,
								"bPoint":       200.0,
							},
						},
					},
				},
				"icon": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSa7Zk_thyuVhD_2YImYSeiF1F9wx48UA3I39b24V0Chqv-40YuezMBVLRZAL81Iihq7IY&usqp=CAU",
			},
			{
				"name": "LV2",
				"title": map[string]interface{}{
					"vi": "Cấp 2",
					"en": "Level 2",
					"th": "Level 2",
					"id": "Level 2",
				},
				"text": map[string]interface{}{
					"vi": "Ong Trưởng Thành",
					"en": "Mature Bee",
					"th": "Mature Bee",
					"id": "Mature Bee",
				},
				"reward": []map[string]interface{}{
					{
						"name": "BPOINT",
						"text": map[string]interface{}{
							"vi": "Tăng **20%** khi tích điểm bPoint",
							"en": "Increase by **20%** when accumulating bPoints",
							"th": "Increase by **20%** when accumulating bPoints",
							"id": "Increase by **20%** when accumulating bPoints",
						},
						"point": map[string]interface{}{
							"percentage": 0.2,
						},
					},
				},
				"conditions": []map[string]interface{}{
					{
						"name": "TASK",
						"text": map[string]interface{}{
							"vi": "**200** công việc",
							"en": "**200** tasks",
							"th": "**200** tasks",
							"id": "**200** tasks",
						},
						"target":    200,
						"icon":      "https://e7.pngegg.com/pngimages/923/240/png-clipart-computer-icons-graphics-computer-software-illustration-task-list-text-rectangle-thumbnail.png",
						"avgRating": 4.7,
					},
					{
						"name": "TRAINING",
						"text": map[string]interface{}{
							"vi": "Đào tạo và kiểm tra",
							"en": "Training & Test",
							"th": "Training & Test",
							"id": "Training & Test",
						},
						"target": 1,
						"training": []map[string]interface{}{
							{
								"name": "LV2_TEST_1",
								"text": map[string]interface{}{
									"vi": "Đào tạo dịch vụ thứ 2",
									"en": "Đào tạo dịch vụ thứ 2",
									"th": "Đào tạo dịch vụ thứ 2",
									"id": "Đào tạo dịch vụ thứ 2",
								},
							},
							{
								"name": "LV2_TEST_2",
								"text": map[string]interface{}{
									"vi": "Đào tạo dịch vụ thứ 3",
									"en": "Đào tạo dịch vụ thứ 3",
									"th": "Đào tạo dịch vụ thứ 3",
									"id": "Đào tạo dịch vụ thứ 3",
								},
							},
							{
								"name": "LV2_TEST_3",
								"text": map[string]interface{}{
									"vi": "Đào tạo dịch vụ cao cấp",
									"en": "Đào tạo dịch vụ cao cấp",
									"th": "Đào tạo dịch vụ cao cấp",
									"id": "Đào tạo dịch vụ cao cấp",
								},
							},
						},
						"icon": "https://cdn-icons-png.flaticon.com/512/194/194618.png",
					},
				},
				"bonus": []map[string]interface{}{
					{
						"id":   "LV2_BREWARD_1",
						"name": "BREWARD",
						"text": map[string]interface{}{
							"vi": "Tặng 1 nón bảo hiểm(nhận tại văn phòng)",
							"en": "Tặng 1 nón bảo hiểm(nhận tại văn phòng)",
							"th": "Tặng 1 nón bảo hiểm(nhận tại văn phòng)",
							"id": "Tặng 1 nón bảo hiểm(nhận tại văn phòng)",
						},
					},
					{
						"name":   "BPOINT",
						"amount": 3000,
						"text": map[string]interface{}{
							"vi": "Nhận thêm **3,000 bPoint**",
							"en": "Receive **3,000 bPoint**",
							"th": "Receive **3,000 bPoint**",
							"id": "Receive **3,000 bPoint**",
						},
					},
					{
						"name":   "MONEY",
						"amount": 50000,
						"text": map[string]interface{}{
							"vi": "Nhận **50,000 VND** vào tài khoản khuyến mãi",
							"en": "Receive **50,000 VND** to the promotion account",
							"th": "Receive **50,000 VND** to the promotion account",
							"id": "Receive **50,000 VND** to the promotion account",
						},
					},
				},
				"icon": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRircywqqgMi-6h_tGdwwahNzSu-HhJalmL0h6MxbVDIJyCBiP9IQeqrxOC2f0sHnfp-ak&usqp=CAU",
			},
			{
				"name": "LV3",
				"title": map[string]interface{}{
					"vi": "Cấp 3",
					"en": "Level 3",
					"th": "Level 3",
					"id": "Level 3",
				},
				"text": map[string]interface{}{
					"vi": "Ong Thợ",
					"en": "Worker Bee",
					"th": "Worker Bee",
					"id": "Worker Bee",
				},
				"reward": []map[string]interface{}{
					{
						"name": "BPOINT",
						"text": map[string]interface{}{
							"vi": "Tăng **30%** khi tích điểm bPoint",
							"en": "Increase by **30%** when accumulating bPoints",
							"th": "Increase by **30%** when accumulating bPoints",
							"id": "Increase by **30%** when accumulating bPoints",
						},
						"point": map[string]interface{}{
							"percentage": 0.3,
						},
					},
				},
				"conditions": []map[string]interface{}{
					{
						"name": "TASK",
						"text": map[string]interface{}{
							"vi": "**1,000** công việc",
							"en": "**1,000** tasks",
							"th": "**1,000** tasks",
							"id": "**1,000** tasks",
						},
						"target":    1000,
						"icon":      "https://e7.pngegg.com/pngimages/923/240/png-clipart-computer-icons-graphics-computer-software-illustration-task-list-text-rectangle-thumbnail.png",
						"avgRating": 4.8,
					},
					{
						"name": "TRAINING",
						"text": map[string]interface{}{
							"vi": "Đào tạo và kiểm tra",
							"en": "Training & Test",
							"th": "Training & Test",
							"id": "Training & Test",
						},
						"target": 1,
						"training": []map[string]interface{}{
							{
								"name": "LV3_TEST_1",
								"text": map[string]interface{}{
									"vi": "Đào tạo tư duy dịch vụ",
									"en": "Đào tạo tư duy dịch vụ",
									"th": "Đào tạo tư duy dịch vụ",
									"id": "Đào tạo tư duy dịch vụ",
								},
							},
							{
								"name": "LV3_TEST_2",
								"text": map[string]interface{}{
									"vi": "Giải tỏa căng thẳng",
									"en": "Giải tỏa căng thẳng",
									"th": "Giải tỏa căng thẳng",
									"id": "Giải tỏa căng thẳng",
								},
							},
							{
								"name": "LV3_TEST_3",
								"text": map[string]interface{}{
									"vi": "Tư duy tích cực",
									"en": "Tư duy tích cực",
									"th": "Tư duy tích cực",
									"id": "Tư duy tích cực",
								},
							},
						},
						"icon": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRircywqqgMi-6h_tGdwwahNzSu-HhJalmL0h6MxbVDIJyCBiP9IQeqrxOC2f0sHnfp-ak&usqp=CAU",
					},
				},
				"bonus": []map[string]interface{}{
					{
						"id":   "LV3_BREWARD_1",
						"name": "BREWARD",
						"text": map[string]interface{}{
							"vi": "Tặng 1 túi đụng dụng cụ(nhận tại văn phòng)",
							"en": "Tặng 1 túi đụng dụng cụ(nhận tại văn phòng)",
							"th": "Tặng 1 túi đụng dụng cụ(nhận tại văn phòng)",
							"id": "Tặng 1 túi đụng dụng cụ(nhận tại văn phòng)",
						},
					},
					{
						"name":   "BPOINT",
						"amount": 5000,
						"text": map[string]interface{}{
							"vi": "Nhận thêm **5,000 bPoint**",
							"en": "Receive **5,000 bPoint**",
							"th": "Receive **5,000 bPoint**",
							"id": "Receive **5,000 bPoint**",
						},
					},
					{
						"name":   "MONEY",
						"amount": 80000,
						"text": map[string]interface{}{
							"vi": "Nhận **80,000 VND** vào tài khoản khuyến mãi",
							"en": "Receive **80,000 VND** to the promotion account",
							"th": "Receive **80,000 VND** to the promotion account",
							"id": "Receive **80,000 VND** to the promotion account",
						},
					},
				},
				"icon": "https://cdn2.vectorstock.com/i/1000x1000/82/01/bee-icon-flat-vector-********.jpg",
			},
			{
				"name": "LV4",
				"title": map[string]interface{}{
					"vi": "Cấp 4",
					"en": "Level 4",
					"th": "Level 4",
					"id": "Level 4",
				},
				"text": map[string]interface{}{
					"vi": "Ong Chiến Binh",
					"en": "Warrior Bee",
					"th": "Warrior Bee",
					"id": "Warrior Bee",
				},
				"reward": []map[string]interface{}{
					{
						"name": "BPOINT",
						"text": map[string]interface{}{
							"vi": "Tăng **40%** khi tích điểm bPoint",
							"en": "Increase by **40%** when accumulating bPoints",
							"th": "Increase by **40%** when accumulating bPoints",
							"id": "Increase by **40%** when accumulating bPoints",
						},
						"point": map[string]interface{}{
							"percentage": 0.4,
						},
					},
				},
				"conditions": []map[string]interface{}{
					{
						"name": "TASK",
						"text": map[string]interface{}{
							"vi": "**2,000** công việc",
							"en": "**2,000** tasks",
							"th": "**2,000** tasks",
							"id": "**2,000** tasks",
						},
						"target":    2000,
						"icon":      "https://e7.pngegg.com/pngimages/923/240/png-clipart-computer-icons-graphics-computer-software-illustration-task-list-text-rectangle-thumbnail.png",
						"avgRating": 4.8,
					},
					{
						"name": "TRAINING",
						"text": map[string]interface{}{
							"vi": "Đào tạo và kiểm tra",
							"en": "Training & Test",
							"th": "Training & Test",
							"id": "Training & Test",
						},
						"target": 1,
						"training": []map[string]interface{}{
							{
								"name": "LV4_TEST_1",
								"text": map[string]interface{}{
									"vi": "Trí tuệ cảm xúc",
									"en": "Trí tuệ cảm xúc",
									"th": "Trí tuệ cảm xúc",
									"id": "Trí tuệ cảm xúc",
								},
							},
							{
								"name": "LV4_TEST_2",
								"text": map[string]interface{}{
									"vi": "Công tác truyền thông",
									"en": "Công tác truyền thông",
									"th": "Công tác truyền thông",
									"id": "Công tác truyền thông",
								},
							},
							{
								"name": "LV4_TEST_3",
								"text": map[string]interface{}{
									"vi": "Tài chính cá nhân",
									"en": "Tài chính cá nhân",
									"th": "Tài chính cá nhân",
									"id": "Tài chính cá nhân",
								},
							},
						},
						"icon": "https://cdn-icons-png.flaticon.com/512/1039/1039328.png",
					},
				},
				"bonus": []map[string]interface{}{
					{
						"id":   "LV4_BREWARD_1",
						"name": "BREWARD",
						"text": map[string]interface{}{
							"vi": "Tặng 1 áo khoác(nhận tại văn phòng)",
							"en": "Tặng 1 áo khoác(nhận tại văn phòng)",
							"th": "Tặng 1 áo khoác(nhận tại văn phòng)",
							"id": "Tặng 1 áo khoác(nhận tại văn phòng)",
						},
					},
					{
						"name":   "BPOINT",
						"amount": 10000,
						"text": map[string]interface{}{
							"vi": "Nhận thêm **10,000 bPoint**",
							"en": "Receive **10,000 bPoint**",
							"th": "Receive **10,000 bPoint**",
							"id": "Receive **10,000 bPoint**",
						},
					},
					{
						"name":   "MONEY",
						"amount": 100000,
						"text": map[string]interface{}{
							"vi": "Nhận **100,000 VND** vào tài khoản khuyến mãi",
							"en": "Receive **100,000 VND** to the promotion account",
							"th": "Receive **100,000 VND** to the promotion account",
							"id": "Receive **100,000 VND** to the promotion account",
						},
					},
				},
				"icon": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQzFUj-m-q5lNsHZGNdDunvC8Lv12jzlhGQqewMHu4fHwcyOVrAsWoJFkCu3NS8eVIuAHI&usqp=CAU",
			},
			{
				"name": "LV5",
				"title": map[string]interface{}{
					"vi": "Cấp 5",
					"en": "Level 5",
					"th": "Level 5",
					"id": "Level 5",
				},
				"text": map[string]interface{}{
					"vi": "Ong Chúa",
					"en": "Queen Bee",
					"th": "Queen Bee",
					"id": "Queen Bee",
				},
				"missions": []map[string]interface{}{
					{
						"text": map[string]interface{}{
							"vi": "Duy trì điểm đánh giá trung bình của bạn phải từ **4.8 sao**  trở lên, bên cạnh đó thời gian làm công việc của bạn mỗi tháng phải trên **70 công việc** và phải duy trì liên tục trong 3 tháng liên tiếp",
							"en": "Duy trì điểm đánh giá trung bình của bạn phải từ **4.8 sao** trở lên, bên cạnh đó thời gian làm công việc của bạn mỗi tháng phải trên **70 công việc** và phải duy trì liên tục trong 3 tháng liên tiếp",
							"th": "Duy trì điểm đánh giá trung bình của bạn phải từ **4.8 sao** trở lên, bên cạnh đó thời gian làm công việc của bạn mỗi tháng phải trên **70 công việc** và phải duy trì liên tục trong 3 tháng liên tiếp",
							"id": "Duy trì điểm đánh giá trung bình của bạn phải từ **4.8 sao** trở lên, bên cạnh đó thời gian làm công việc của bạn mỗi tháng phải trên **70 công việc** và phải duy trì liên tục trong 3 tháng liên tiếp",
						},
					},
				},
				"conditions": []map[string]interface{}{
					{
						"name": "TASK_IN_MONTH",
						"text": map[string]interface{}{
							"vi": "**70** công việc/tháng",
							"en": "**70** công việc/tháng",
							"th": "*70** công việc/tháng",
							"id": "**70** công việc/tháng",
						},
						"target":    70,
						"avgRating": 4.8,
						"icon":      "https://e7.pngegg.com/pngimages/923/240/png-clipart-computer-icons-graphics-computer-software-illustration-task-list-text-rectangle-thumbnail.png",
					},
					{
						"name": "MONTH_IN_ROW",
						"text": map[string]interface{}{
							"vi": "**3 tháng** liên tiếp",
							"en": "**3 tháng** liên tiếp",
							"th": "**3 tháng** liên tiếp",
							"id": "**3 tháng** liên tiếp",
						},
						"target":      3,
						"avgRating":   4.8,
						"taskInMonth": 70,
						"icon":        "https://cdn-icons-png.flaticon.com/512/5776/5776621.png",
					},
				},
				"bonus": []map[string]interface{}{
					{
						"id":   "LV5_BREWARD_1",
						"name": "BREWARD",
						"text": map[string]interface{}{
							"vi": "Bảo hiểm sức khỏe trong 3 tháng",
							"en": "Health Insurance within 3 months",
							"th": "Health Insurance within 3 months",
							"id": "Health Insurance within 3 months",
						},
					},
				},
				"icon": "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRXOitXl9AM6y7N6HlBRXVZuwdNylc25qdT-g0aVGeGJaT9tio2Qi7xYOumy-VzsF0DDg4&usqp=CAU",
			},
		},
	}
	data["_id"] = globalLib.GenerateObjectId()
	data["cityName"] = "Hồ Chí Minh"
	globalDataAccess.InsertOne(globalCollection.COLLECTION_JOURNEY_SETTING[local.ISO_CODE], data)
	data["_id"] = globalLib.GenerateObjectId()
	data["cityName"] = "Hà Nội"
	globalDataAccess.InsertOne(globalCollection.COLLECTION_JOURNEY_SETTING[local.ISO_CODE], data)
}
