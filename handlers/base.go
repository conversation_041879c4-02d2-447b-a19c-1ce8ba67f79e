package handlers

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/trainingTaskerV2"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func GetCourses(query, fields bson.M) (result []*trainingTaskerV2.Course, err error) {
	err = globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TRAINING_TASKER_COURSE[local.ISO_CODE], query, fields, &result)
	return
}

func GetUsers(query, fields bson.M) (result []*modelUsers.Users, err error) {
	err = globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_USERS, query, fields, &result)
	return
}
func IsExistCourseStartDate(query bson.M) (exist bool, err error) {
	exist, err = globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_TRAINING_TASKER_COURSE_START_DATE[local.ISO_CODE], query)
	return
}

func CreateCoursesStartDate(coursesStartDate []interface{}) (err error) {
	err = globalDataAccess.InsertAll(globalCollection.COLLECTION_TRAINING_TASKER_COURSE_START_DATE[local.ISO_CODE], coursesStartDate)
	return
}
func GetCoursesStartDate(query, fields bson.M) (coursesStartDate []*trainingTaskerV2.CourseStartDate, err error) {
	err = globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TRAINING_TASKER_COURSE_START_DATE[local.ISO_CODE], query, fields, &coursesStartDate)
	return
}

func GetCourse(query, fields bson.M) (result *trainingTaskerV2.Course, err error) {
	err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TRAINING_TASKER_COURSE[local.ISO_CODE], query, fields, &result)
	return
}
func IsExistTaskerInServiceChannel(query bson.M) (result bool, err error) {
	result, err = globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], query)
	return
}
func IsExistSubmissionByQuery(query bson.M) (exist bool) {
	exist, _ = globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_TRAINING_TASKER_SUBMISSION[local.ISO_CODE], query)
	return
}

func GetUserById(id string, fields bson.M) (user *users.Users, err error) {
	err = globalDataAccess.GetOneById(globalCollection.COLLECTION_USERS, id, fields, &user)
	return
}
func GetSubmissionByQuery(query, fields bson.M) (submission *trainingTaskerV2.Submission, err error) {
	err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TRAINING_TASKER_SUBMISSION[local.ISO_CODE], query, fields, &submission)
	return submission, err
}
func UpdateCoursesStartDateByQuery(query bson.M, data bson.M) (err error) {
	_, err = globalDataAccess.UpdateAllByQuery(globalCollection.COLLECTION_TRAINING_TASKER_COURSE_START_DATE[local.ISO_CODE], query, data)
	return
}
