import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import _ from 'lodash';
import {
  revertTimeZone,
  trackingScheduleStatus,
  postToSlack,
} from '/server/helper-function';

const runAutoAccumulatePoint = () => {
  return;
	console.log("Start runAutoAccumulatePoint ...");
  var message = null;
  const startDate = revertTimeZone(new Date(2019, 6, 1, 0));

  // Don't run from 22PM to next 6AM
  const runningHour = revertTimeZone(new Date()).getHours();
  if ( runningHour >= 22 || runningHour < 6 ) {
    return;
  }

  Meteor.appCollection.Task.find({
    date: {
      $gte: moment().subtract(2, 'days').toDate(),
      $lte: moment().subtract(2, 'hours').toDate(),
    },
    status: 'DONE',
    accumulatedPoints: null,
    isoCode: {$ne: 'TH'},
  }, {fields: {date: 1}, sort: {date: 1}}).forEach((task) => {
    try {
      if ( moment(task.date).isAfter(moment(startDate)) ) {
        const url = Meteor.settings.GO_SERVICE.API_URL + '/v2/bpoint/accumulate';
        const headers = {
          'content-type': 'application/json',
          'accessKey': Meteor.settings.GO_SERVICE.API_KEY,
        };
        const requestData = {
          taskId: task._id,
        };
        HTTP.post(url, {headers: headers, data: requestData});
      }
    } catch (e) {
      message = e;
      postToSlack({
        channel: 'cron-jobs-alert',
        text: `Workflow: There was an error on autoAccumulatePoint: taskId: ${task._id} Message: ${message}`,
      });
    }
  });

	console.log("Finished runAutoAccumulatePoint ");
  // Tracking
  trackingScheduleStatus({name: 'autoAccumulatePoint', nextRunAt: SyncedCron.nextScheduledAtDate('autoAccumulatePoint'), message});
};

  Meteor.methods({
    autoAccumulatePoint() {
      SyncedCron.add({
        name: 'autoAccumulatePoint',
        schedule(parser) {
          // Run at 25' every 2 hour from 7AM to 22PM
          return parser.cron('0 25 0/2 1/1 * ? *', true);
        },
        job() {
          runAutoAccumulatePoint();
        },
      });
	    console.log("Added autoAccumulatePoint cron.");
    },
    'runNow.autoAccumulatePoint'() {
      runAutoAccumulatePoint();
    },
  });
