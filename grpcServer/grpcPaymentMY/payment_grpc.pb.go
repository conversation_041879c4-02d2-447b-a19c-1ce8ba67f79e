// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcPaymentMY

import (
	context "context"
	paymentRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentRequest"
	refundTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/refundTransaction"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	vatRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/vatRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PaymentClient is the client API for Payment service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PaymentClient interface {
	CreateGiftByComboVoucher(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error)
	CreateVatRequest(ctx context.Context, in *vatRequest.VatRequest, opts ...grpc.CallOption) (*response.Response, error)
	Refund(ctx context.Context, in *refundTransaction.RefundTransaction, opts ...grpc.CallOption) (*response.Response, error)
	SubscriptionPayment(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error)
}

type paymentClient struct {
	cc grpc.ClientConnInterface
}

func NewPaymentClient(cc grpc.ClientConnInterface) PaymentClient {
	return &paymentClient{cc}
}

func (c *paymentClient) CreateGiftByComboVoucher(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentMY.Payment/CreateGiftByComboVoucher", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentClient) CreateVatRequest(ctx context.Context, in *vatRequest.VatRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentMY.Payment/CreateVatRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentClient) Refund(ctx context.Context, in *refundTransaction.RefundTransaction, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentMY.Payment/Refund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentClient) SubscriptionPayment(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentMY.Payment/SubscriptionPayment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PaymentServer is the server API for Payment service.
// All implementations must embed UnimplementedPaymentServer
// for forward compatibility
type PaymentServer interface {
	CreateGiftByComboVoucher(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error)
	CreateVatRequest(context.Context, *vatRequest.VatRequest) (*response.Response, error)
	Refund(context.Context, *refundTransaction.RefundTransaction) (*response.Response, error)
	SubscriptionPayment(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error)
	mustEmbedUnimplementedPaymentServer()
}

// UnimplementedPaymentServer must be embedded to have forward compatible implementations.
type UnimplementedPaymentServer struct {
}

func (UnimplementedPaymentServer) CreateGiftByComboVoucher(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGiftByComboVoucher not implemented")
}
func (UnimplementedPaymentServer) CreateVatRequest(context.Context, *vatRequest.VatRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVatRequest not implemented")
}
func (UnimplementedPaymentServer) Refund(context.Context, *refundTransaction.RefundTransaction) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Refund not implemented")
}
func (UnimplementedPaymentServer) SubscriptionPayment(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubscriptionPayment not implemented")
}
func (UnimplementedPaymentServer) mustEmbedUnimplementedPaymentServer() {}

// UnsafePaymentServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PaymentServer will
// result in compilation errors.
type UnsafePaymentServer interface {
	mustEmbedUnimplementedPaymentServer()
}

func RegisterPaymentServer(s *grpc.Server, srv PaymentServer) {
	s.RegisterService(&_Payment_serviceDesc, srv)
}

func _Payment_CreateGiftByComboVoucher_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentRequest.PaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServer).CreateGiftByComboVoucher(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentMY.Payment/CreateGiftByComboVoucher",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServer).CreateGiftByComboVoucher(ctx, req.(*paymentRequest.PaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Payment_CreateVatRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(vatRequest.VatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServer).CreateVatRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentMY.Payment/CreateVatRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServer).CreateVatRequest(ctx, req.(*vatRequest.VatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Payment_Refund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(refundTransaction.RefundTransaction)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServer).Refund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentMY.Payment/Refund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServer).Refund(ctx, req.(*refundTransaction.RefundTransaction))
	}
	return interceptor(ctx, in, info, handler)
}

func _Payment_SubscriptionPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentRequest.PaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentServer).SubscriptionPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentMY.Payment/SubscriptionPayment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentServer).SubscriptionPayment(ctx, req.(*paymentRequest.PaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Payment_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcPaymentMY.Payment",
	HandlerType: (*PaymentServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateGiftByComboVoucher",
			Handler:    _Payment_CreateGiftByComboVoucher_Handler,
		},
		{
			MethodName: "CreateVatRequest",
			Handler:    _Payment_CreateVatRequest_Handler,
		},
		{
			MethodName: "Refund",
			Handler:    _Payment_Refund_Handler,
		},
		{
			MethodName: "SubscriptionPayment",
			Handler:    _Payment_SubscriptionPayment_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "payment.proto",
}
