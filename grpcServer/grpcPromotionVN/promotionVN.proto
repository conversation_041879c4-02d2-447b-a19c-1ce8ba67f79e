syntax = "proto3";
package grpcPromotionVN;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPromotionVN";

import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task/task.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotionResponse/promotionResponse.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/signUpPromotionRequest/signUpPromotionRequest.proto";

service PromotionVN {
  rpc CheckPostTaskPromotion (task.Task) returns (promotionResponse.PromotionResponse) {
    option (google.api.http) = {
      post: "/api/v3/promotion-vn/post-task"
      body: "*"
    };
  }

  rpc CheckSignUpPromotion (signUpPromotionRequest.SignUpPromotionRequest) returns (promotionResponse.PromotionResponse) {
    option (google.api.http) = {
      post: "/api/v3/promotion-vn/sign-up"
      body: "*"
    };
  }
}