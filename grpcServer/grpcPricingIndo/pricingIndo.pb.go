// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.7.1
// source: pricingIndo.proto

package grpcPricingIndo

import (
	pricingSubscriptionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionRequest"
	pricingSubscriptionResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionResponse"
	pricingrequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingrequest"
	pricingresponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_pricingIndo_proto protoreflect.FileDescriptor

var file_pricingIndo_proto_rawDesc = []byte{
	0x0a, 0x11, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x64, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x67, 0x72, 0x70, 0x63, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x49, 0x6e, 0x64, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x56, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62,
	0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x58, 0x67, 0x69, 0x74, 0x6c,
	0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67,
	0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x6f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72,
	0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x71, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67,
	0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xef, 0x1e, 0x0a, 0x0b, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x64, 0x6f, 0x12, 0x88, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x41, 0x69, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74,
	0x69, 0x6f, 0x6e, 0x65, 0x72, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x22, 0x24, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f,
	0x2f, 0x61, 0x69, 0x72, 0x2d, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72,
	0x3a, 0x01, 0x2a, 0x12, 0x84, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x48, 0x6f, 0x6d, 0x65, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1e,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2d, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x27, 0x22, 0x22, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x68, 0x6f, 0x6d, 0x65, 0x2d, 0x63,
	0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0x84, 0x01, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x65, 0x70, 0x43, 0x6c, 0x65,
	0x61, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x22, 0x22, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f,
	0x2f, 0x64, 0x65, 0x65, 0x70, 0x2d, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x3a, 0x01,
	0x2a, 0x12, 0x8e, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x48,
	0x6f, 0x6d, 0x65, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x38, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x32,
	0x22, 0x2d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x72, 0x65, 0x2d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x2d, 0x68, 0x6f, 0x6d, 0x65, 0x2d, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x3a,
	0x01, 0x2a, 0x12, 0x92, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x41, 0x69, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x12, 0x1e,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x3a, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x34, 0x22, 0x2f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x72, 0x65, 0x2d, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2d, 0x61, 0x69, 0x72, 0x2d, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69,
	0x6f, 0x6e, 0x65, 0x72, 0x3a, 0x01, 0x2a, 0x12, 0x8e, 0x01, 0x0a, 0x15, 0x52, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x65, 0x70, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e,
	0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x38,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x32, 0x22, 0x2d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x72, 0x65, 0x2d,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x64, 0x65, 0x65, 0x70, 0x2d, 0x63, 0x6c, 0x65,
	0x61, 0x6e, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0x7f, 0x0a, 0x12, 0x52, 0x65, 0x43, 0x61,
	0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x12, 0x1e,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2c, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x26, 0x22, 0x21, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x72, 0x65, 0x2d, 0x63, 0x61, 0x6c,
	0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x59, 0x0a, 0x18, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x43, 0x6c, 0x65,
	0x61, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x00, 0x12, 0x8b, 0x01, 0x0a, 0x1b, 0x41, 0x50, 0x49, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x43, 0x6c, 0x65, 0x61,
	0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x22, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f,
	0x6f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x2d, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x3a,
	0x01, 0x2a, 0x12, 0x92, 0x01, 0x0a, 0x17, 0x52, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x4f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1e,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x3a, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x34, 0x22, 0x2f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x72, 0x65, 0x2d, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2d, 0x6f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x2d, 0x63, 0x6c, 0x65, 0x61,
	0x6e, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0xb8, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x36, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x22, 0x21, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e,
	0x64, 0x6f, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x3a,
	0x01, 0x2a, 0x12, 0x88, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x57, 0x61, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x12,
	0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2f, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x29, 0x22, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x77, 0x61, 0x73, 0x68, 0x69,
	0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x92, 0x01,
	0x0a, 0x17, 0x52, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x73, 0x68, 0x69,
	0x6e, 0x67, 0x4d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x3a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x34, 0x22, 0x2f,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d,
	0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x72, 0x65, 0x2d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d,
	0x77, 0x61, 0x73, 0x68, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x63, 0x68, 0x69, 0x6e, 0x65, 0x3a,
	0x01, 0x2a, 0x12, 0x73, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x53, 0x6f, 0x66, 0x61, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1e, 0x22, 0x19, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f,
	0x73, 0x6f, 0x66, 0x61, 0x3a, 0x01, 0x2a, 0x12, 0x7d, 0x0a, 0x0d, 0x52, 0x65, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x53, 0x6f, 0x66, 0x61, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x22, 0x24, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69,
	0x6e, 0x64, 0x6f, 0x2f, 0x72, 0x65, 0x2d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x73,
	0x6f, 0x66, 0x61, 0x3a, 0x01, 0x2a, 0x12, 0x83, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x73, 0x69, 0x6e, 0x66, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2c,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x22, 0x21, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x64, 0x69, 0x73,
	0x69, 0x6e, 0x66, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x3a, 0x01, 0x2a, 0x12, 0x8d, 0x01, 0x0a,
	0x15, 0x52, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x69, 0x73, 0x69, 0x6e, 0x66,
	0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x37, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x31, 0x22, 0x2c, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64,
	0x6f, 0x2f, 0x72, 0x65, 0x2d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x64, 0x69, 0x73,
	0x69, 0x6e, 0x66, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x3a, 0x01, 0x2a, 0x12, 0x79, 0x0a, 0x11,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x27,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x22, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x6d, 0x61, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x83, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x4d, 0x61, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1e, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43,
	0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x2c, 0x22, 0x27, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x72, 0x65, 0x2d, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2d, 0x6d, 0x61, 0x73, 0x73, 0x61, 0x67, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x80, 0x01,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x6d, 0x65,
	0x4d, 0x6f, 0x76, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73,
	0x75, 0x6c, 0x74, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x22, 0x20, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64,
	0x6f, 0x2f, 0x68, 0x6f, 0x6d, 0x65, 0x2d, 0x6d, 0x6f, 0x76, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a,
	0x12, 0x82, 0x01, 0x0a, 0x13, 0x52, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x48, 0x6f,
	0x6d, 0x65, 0x4d, 0x6f, 0x76, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x28, 0x22, 0x23, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69,
	0x6e, 0x64, 0x6f, 0x2f, 0x72, 0x65, 0x2d, 0x68, 0x6f, 0x6d, 0x65, 0x2d, 0x6d, 0x6f, 0x76, 0x69,
	0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0x8d, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x41, 0x69, 0x72, 0x43, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x65, 0x72, 0x56, 0x32, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x22, 0x27, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f,
	0x61, 0x69, 0x72, 0x2d, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x2d,
	0x76, 0x32, 0x3a, 0x01, 0x2a, 0x12, 0x79, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x4c, 0x61, 0x75, 0x6e, 0x64, 0x72, 0x79, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x22,
	0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x6c, 0x61, 0x75, 0x6e, 0x64, 0x72, 0x79, 0x3a, 0x01, 0x2a,
	0x12, 0x83, 0x01, 0x0a, 0x10, 0x52, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x4c, 0x61,
	0x75, 0x6e, 0x64, 0x72, 0x79, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75,
	0x6c, 0x74, 0x22, 0x32, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2c, 0x22, 0x27, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f,
	0x2f, 0x72, 0x65, 0x2d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x6c, 0x61, 0x75, 0x6e,
	0x64, 0x72, 0x79, 0x3a, 0x01, 0x2a, 0x12, 0x76, 0x0a, 0x25, 0x52, 0x65, 0x43, 0x61, 0x6c, 0x63,
	0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72, 0x49,
	0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x2e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65,
	0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x12, 0x8e,
	0x01, 0x0a, 0x1c, 0x41, 0x50, 0x49, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x54, 0x61, 0x73, 0x6b, 0x44, 0x61, 0x74, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x31, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x2b, 0x22, 0x26, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2d,
	0x64, 0x61, 0x74, 0x65, 0x2d, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x01, 0x2a, 0x12,
	0x88, 0x01, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x48, 0x6f,
	0x75, 0x73, 0x65, 0x6b, 0x65, 0x65, 0x70, 0x69, 0x6e, 0x67, 0x56, 0x32, 0x12, 0x1e, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43,
	0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x29, 0x22, 0x24, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f, 0x68, 0x6f, 0x75, 0x73, 0x65, 0x6b, 0x65, 0x65,
	0x70, 0x69, 0x6e, 0x67, 0x2d, 0x76, 0x32, 0x3a, 0x01, 0x2a, 0x12, 0x82, 0x01, 0x0a, 0x15, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x74, 0x65, 0x72, 0x48, 0x65,
	0x61, 0x74, 0x65, 0x72, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x2c, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x26, 0x22, 0x21, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f,
	0x77, 0x61, 0x74, 0x65, 0x72, 0x2d, 0x68, 0x65, 0x61, 0x74, 0x65, 0x72, 0x3a, 0x01, 0x2a, 0x12,
	0x79, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x49, 0x72, 0x6f,
	0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x27, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x21, 0x22, 0x1c, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x35, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f, 0x2f,
	0x69, 0x72, 0x6f, 0x6e, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x42, 0x37, 0x5a, 0x35, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65,
	0x2f, 0x67, 0x6f, 0x2d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x69, 0x6e, 0x64, 0x6f,
	0x2d, 0x76, 0x33, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x49,
	0x6e, 0x64, 0x6f, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_pricingIndo_proto_goTypes = []interface{}{
	(*pricingrequest.PricingRequest)(nil),                           // 0: pricingrequest.PricingRequest
	(*pricingSubscriptionRequest.PricingSubscriptionRequest)(nil),   // 1: pricingSubscriptionRequest.PricingSubscriptionRequest
	(*pricingrequest.PricingIncreaseDurationRequest)(nil),           // 2: pricingrequest.PricingIncreaseDurationRequest
	(*pricingresponse.CostResult)(nil),                              // 3: pricingresponse.CostResult
	(*pricingSubscriptionResponse.PricingSubscriptionResponse)(nil), // 4: pricingSubscriptionResponse.PricingSubscriptionResponse
}
var file_pricingIndo_proto_depIdxs = []int32{
	0,  // 0: grpcPricingIndo.PricingIndo.GetPricingAirConditioner:input_type -> pricingrequest.PricingRequest
	0,  // 1: grpcPricingIndo.PricingIndo.GetPricingHomeCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 2: grpcPricingIndo.PricingIndo.GetPricingDeepCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 3: grpcPricingIndo.PricingIndo.RePricingHomeCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 4: grpcPricingIndo.PricingIndo.RePricingAirConditioner:input_type -> pricingrequest.PricingRequest
	0,  // 5: grpcPricingIndo.PricingIndo.RePricingDeepCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 6: grpcPricingIndo.PricingIndo.ReCalculatePricing:input_type -> pricingrequest.PricingRequest
	0,  // 7: grpcPricingIndo.PricingIndo.GetPricingOfficeCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 8: grpcPricingIndo.PricingIndo.APIGetPricingOfficeCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 9: grpcPricingIndo.PricingIndo.RePricingOfficeCleaning:input_type -> pricingrequest.PricingRequest
	1,  // 10: grpcPricingIndo.PricingIndo.GetPricingSubscription:input_type -> pricingSubscriptionRequest.PricingSubscriptionRequest
	0,  // 11: grpcPricingIndo.PricingIndo.GetPricingWashingMachine:input_type -> pricingrequest.PricingRequest
	0,  // 12: grpcPricingIndo.PricingIndo.RePricingWashingMachine:input_type -> pricingrequest.PricingRequest
	0,  // 13: grpcPricingIndo.PricingIndo.GetPricingSofa:input_type -> pricingrequest.PricingRequest
	0,  // 14: grpcPricingIndo.PricingIndo.RePricingSofa:input_type -> pricingrequest.PricingRequest
	0,  // 15: grpcPricingIndo.PricingIndo.GetPricingDisinfection:input_type -> pricingrequest.PricingRequest
	0,  // 16: grpcPricingIndo.PricingIndo.RePricingDisinfection:input_type -> pricingrequest.PricingRequest
	0,  // 17: grpcPricingIndo.PricingIndo.GetPricingMassage:input_type -> pricingrequest.PricingRequest
	0,  // 18: grpcPricingIndo.PricingIndo.RePricingMassage:input_type -> pricingrequest.PricingRequest
	0,  // 19: grpcPricingIndo.PricingIndo.GetPricingHomeMoving:input_type -> pricingrequest.PricingRequest
	0,  // 20: grpcPricingIndo.PricingIndo.RePricingHomeMoving:input_type -> pricingrequest.PricingRequest
	0,  // 21: grpcPricingIndo.PricingIndo.GetPricingAirConditionerV2:input_type -> pricingrequest.PricingRequest
	0,  // 22: grpcPricingIndo.PricingIndo.GetPricingLaundry:input_type -> pricingrequest.PricingRequest
	0,  // 23: grpcPricingIndo.PricingIndo.RePricingLaundry:input_type -> pricingrequest.PricingRequest
	2,  // 24: grpcPricingIndo.PricingIndo.ReCalculatePricingForIncreaseDuration:input_type -> pricingrequest.PricingIncreaseDurationRequest
	0,  // 25: grpcPricingIndo.PricingIndo.APIGetPricingTaskDateOptions:input_type -> pricingrequest.PricingRequest
	0,  // 26: grpcPricingIndo.PricingIndo.GetPricingHousekeepingV2:input_type -> pricingrequest.PricingRequest
	0,  // 27: grpcPricingIndo.PricingIndo.GetPricingWaterHeater:input_type -> pricingrequest.PricingRequest
	0,  // 28: grpcPricingIndo.PricingIndo.GetPricingIroning:input_type -> pricingrequest.PricingRequest
	3,  // 29: grpcPricingIndo.PricingIndo.GetPricingAirConditioner:output_type -> pricingresponse.CostResult
	3,  // 30: grpcPricingIndo.PricingIndo.GetPricingHomeCleaning:output_type -> pricingresponse.CostResult
	3,  // 31: grpcPricingIndo.PricingIndo.GetPricingDeepCleaning:output_type -> pricingresponse.CostResult
	3,  // 32: grpcPricingIndo.PricingIndo.RePricingHomeCleaning:output_type -> pricingresponse.CostResult
	3,  // 33: grpcPricingIndo.PricingIndo.RePricingAirConditioner:output_type -> pricingresponse.CostResult
	3,  // 34: grpcPricingIndo.PricingIndo.RePricingDeepCleaning:output_type -> pricingresponse.CostResult
	3,  // 35: grpcPricingIndo.PricingIndo.ReCalculatePricing:output_type -> pricingresponse.CostResult
	3,  // 36: grpcPricingIndo.PricingIndo.GetPricingOfficeCleaning:output_type -> pricingresponse.CostResult
	3,  // 37: grpcPricingIndo.PricingIndo.APIGetPricingOfficeCleaning:output_type -> pricingresponse.CostResult
	3,  // 38: grpcPricingIndo.PricingIndo.RePricingOfficeCleaning:output_type -> pricingresponse.CostResult
	4,  // 39: grpcPricingIndo.PricingIndo.GetPricingSubscription:output_type -> pricingSubscriptionResponse.PricingSubscriptionResponse
	3,  // 40: grpcPricingIndo.PricingIndo.GetPricingWashingMachine:output_type -> pricingresponse.CostResult
	3,  // 41: grpcPricingIndo.PricingIndo.RePricingWashingMachine:output_type -> pricingresponse.CostResult
	3,  // 42: grpcPricingIndo.PricingIndo.GetPricingSofa:output_type -> pricingresponse.CostResult
	3,  // 43: grpcPricingIndo.PricingIndo.RePricingSofa:output_type -> pricingresponse.CostResult
	3,  // 44: grpcPricingIndo.PricingIndo.GetPricingDisinfection:output_type -> pricingresponse.CostResult
	3,  // 45: grpcPricingIndo.PricingIndo.RePricingDisinfection:output_type -> pricingresponse.CostResult
	3,  // 46: grpcPricingIndo.PricingIndo.GetPricingMassage:output_type -> pricingresponse.CostResult
	3,  // 47: grpcPricingIndo.PricingIndo.RePricingMassage:output_type -> pricingresponse.CostResult
	3,  // 48: grpcPricingIndo.PricingIndo.GetPricingHomeMoving:output_type -> pricingresponse.CostResult
	3,  // 49: grpcPricingIndo.PricingIndo.RePricingHomeMoving:output_type -> pricingresponse.CostResult
	3,  // 50: grpcPricingIndo.PricingIndo.GetPricingAirConditionerV2:output_type -> pricingresponse.CostResult
	3,  // 51: grpcPricingIndo.PricingIndo.GetPricingLaundry:output_type -> pricingresponse.CostResult
	3,  // 52: grpcPricingIndo.PricingIndo.RePricingLaundry:output_type -> pricingresponse.CostResult
	3,  // 53: grpcPricingIndo.PricingIndo.ReCalculatePricingForIncreaseDuration:output_type -> pricingresponse.CostResult
	3,  // 54: grpcPricingIndo.PricingIndo.APIGetPricingTaskDateOptions:output_type -> pricingresponse.CostResult
	3,  // 55: grpcPricingIndo.PricingIndo.GetPricingHousekeepingV2:output_type -> pricingresponse.CostResult
	3,  // 56: grpcPricingIndo.PricingIndo.GetPricingWaterHeater:output_type -> pricingresponse.CostResult
	3,  // 57: grpcPricingIndo.PricingIndo.GetPricingIroning:output_type -> pricingresponse.CostResult
	29, // [29:58] is the sub-list for method output_type
	0,  // [0:29] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_pricingIndo_proto_init() }
func file_pricingIndo_proto_init() {
	if File_pricingIndo_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pricingIndo_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pricingIndo_proto_goTypes,
		DependencyIndexes: file_pricingIndo_proto_depIdxs,
	}.Build()
	File_pricingIndo_proto = out.File
	file_pricingIndo_proto_rawDesc = nil
	file_pricingIndo_proto_goTypes = nil
	file_pricingIndo_proto_depIdxs = nil
}
