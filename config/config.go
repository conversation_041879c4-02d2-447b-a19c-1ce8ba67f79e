/*
 * @File: config.go
 * @Description: Define config of service
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 11/12/2020
 * @UpdatedBy: ngoctb3
 */
package config

import (
	"fmt"
	"os"

	Viper "github.com/spf13/viper"
)

type Config struct {
	REST_Port         string `mapstructure:"running_rest_email_service_port"`
	GRPC_Port         string `mapstructure:"running_grpc_email_service_port"`
	VerifyEmailApiKey string
	EmailVerifyURL    string
	MailGunDomain     string
	MailGunAPIKey     string
	EmailTemplateURL  string
	FileTemplateURL   string
	SlackToken        string
}

var cfg *Config

/*
 * @Description: Init application config when the package is called
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 11/12/2020
 * @UpdatedBy: ngoctb3
 */
func init() {
	var folder string
	mode := os.Getenv("APPLICATION_MODE")

	switch mode {
	case "dev", "prod", "test", "ci":
		folder = mode
	default:
		folder = "local"
		os.Setenv("APPLICATION_MODE", folder)
	}

	cfgPath := os.Getenv("EMAIL_SERVICE_CONFIG")
	if cfgPath == "" {
		cfgPath = "config"
	}
	path := fmt.Sprintf("%v/%v", cfgPath, folder)

	cfg = new(Config)

	if folder == "local" || folder == "test" {
		cfg.MailGunDomain = "sandbox5fd8742e99ac4f5c98a4420b121bfd6c.mailgun.org"
		cfg.MailGunAPIKey = "**************************************************"
		cfg.EmailVerifyURL = "http://localhost:8080/api"
		cfg.EmailTemplateURL = "./lib/emailTemplate"
		cfg.FileTemplateURL = "./lib/fileTemplate"
	} else if mode != "test" {
		cfg.MailGunDomain = os.Getenv("MAILGUN_DOMAIN")
		cfg.MailGunAPIKey = os.Getenv("MAILGUN_API_KEY")
		cfg.EmailVerifyURL = os.Getenv("EMAIL_VERIFY_URL")
		cfg.EmailTemplateURL = "emailTemplate"
		cfg.FileTemplateURL = "fileTemplate"
		cfg.VerifyEmailApiKey = os.Getenv("EMAIL_VERIFY_API_KEY")
		cfg.SlackToken = os.Getenv("SLACK_TOKEN_VN")
	}

	initConfig(path, "base", &cfg)
}

/*
 * @Description: Init config with configPath
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 */
func initConfig(configPath, configName string, result interface{}) {
	viper := Viper.New()
	viper.AddConfigPath(configPath)
	viper.SetConfigName(configName)

	err := viper.ReadInConfig() // Find and read the config file
	if err == nil {             // Handle errors reading the config file
		err = viper.Unmarshal(result)
		if err != nil { // Handle errors reading the config file
			panic(fmt.Errorf("fatal error config file: %s", err))
		}
	}
}

/*
 * @Description: Return config
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 */
func GetConfig() *Config {
	return cfg
}
