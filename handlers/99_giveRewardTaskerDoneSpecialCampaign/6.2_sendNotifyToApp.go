package giveRewardTaskerDoneSpecialCampaign

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
)

func sendNotification(user *modelUser.Users, keyTitle, keyBody string, navigateTo string, typ int32) {
	lang := globalConstant.LANG_EN
	if user != nil && user.Language != "" {
		lang = user.Language
	}
	navigate := globalConstant.PAYLOAD_NAVIGATE_TO_NOTIFICATION_DETAIL
	if navigateTo != "" {
		navigate = navigateTo
	}
	// 25 là thông báo, 6 là cộng trừ tiền or bpoint, mặc định là 25.
	var typePayload int32
	typePayload = 25
	if typ > 0 {
		typePayload = typ
	}

	title := localization.GetLocalizeObject(keyTitle)
	body := localization.GetLocalizeObject(keyBody)

	// Khởi tạo data Notification
	var arrayNotification []interface{}
	notify := &modelNotification.Notification{
		XId:         globalLib.GenerateObjectId(),
		UserId:      user.XId,
		Type:        typePayload,
		Title:       globalLib.LocalizeServiceName(lang, title),
		Description: globalLib.LocalizeServiceName(lang, body),
		NavigateTo:  navigate,
		CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
	}

	arrayNotification = append(arrayNotification, notify)
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       typePayload,
		NavigateTo: navigate,
	}
	userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{
		{UserId: user.XId, Language: lang},
	}
	// Send Notification
	lib.SendNotification(arrayNotification, userIds, title, body, payload, true)
}
