package sendBusinessReportEmail

import (
	"fmt"
	"net/http"

	"github.com/xuri/excelize/v2"
	"gitlab.com/btaskee/go-email-vn-v3/lib"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	"gitlab.com/btaskee/go-services-model-v2/localization"
)

func saveFile(language string, file *excelize.File) (filePath string, resErr *modelEmailResponse.EmailResponse, err error) {
	index, _ := file.GetSheetIndex("Sheet1")
	// Set the active sheet to the new sheet
	file.SetActiveSheet(index)

	file.SetSheetName("Sheet1", localization.T(language, "BUSINESS_REPORT_SHEET_1_NAME"))
	file.SetSheetName("Sheet2", localization.T(language, "BUSINESS_REPORT_SHEET_2_NAME"))
	file.SetSheetName("Sheet3", localization.T(language, "BUSINESS_REPORT_SHEET_3_NAME"))

	filePath = fmt.Sprintf("%s/%s.xlsx", cfg.FileTemplateURL, localization.T(language, "BUSINESS_REPORT_FILE_NAME"))
	err = file.SaveAs(filePath)
	if err != nil {
		return "", &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusInternalServerError,
			Message:    lib.ERROR_FAILED_TO_CREATE_FILE,
		}, err
	}

	return filePath, nil, nil
}
