syntax = "proto3";
package grpcPushNotificationVN;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPushNotificationVN";

import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest/pushNotificationRequest.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response/response.proto";

service PushNotificationVN {
  rpc Send (pushNotificationRequest.PushNotificationRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/push-notification-vn/send"
      body: "*"
    };
  }
}