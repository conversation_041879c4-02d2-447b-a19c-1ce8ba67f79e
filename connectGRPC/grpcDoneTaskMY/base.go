package grpcDoneTaskMY

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcDoneTaskMY"
	"google.golang.org/grpc"
)

func ConnectGRPCDoneTask(endpoint string) (grpcDoneTaskMY.DoneTaskClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcDoneTaskMY.NewDoneTaskClient(connect)
	return client, connect, err
}
