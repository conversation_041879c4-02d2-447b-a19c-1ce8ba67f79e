package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-email-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelSubscription "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscription"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func SendRenewSubscriptionEmail(req *modelEmailSending.EmailRequest) (*modelEmailResponse.EmailResponse, error) {
	defer local.Logger.Sync()

	// Check server config
	resErr := CheckEmailConfig()
	if resErr != nil {
		local.Logger.Warn(resErr.Message)
		return resErr, nil
	}

	if req.SubscriptionId == "" {
		local.Logger.Warn(lib.ERROR_SUBSCRIPTION_ID_REQUIRED,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_SUBSCRIPTION_ID_REQUIRED,
		}, nil
	}

	if req.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_USER_ID_REQUIRED,
		}, nil
	}

	var sub *modelSubscription.Subscription
	globalDataAccess.GetOneById(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], req.SubscriptionId, bson.M{"serviceText": 1, "userId": 1, "schedule": 1, "endDate": 1, "weekday": 1, "currency": 1, "isoCode": 1, "orderId": 1, "address": 1, "duration": 1, "payment": 1}, &sub)
	if sub == nil {
		local.Logger.Warn(lib.ERROR_SUBSCRIPTION_NOT_FOUND,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_SUBSCRIPTION_NOT_FOUND,
		}, nil
	}

	user, _ := modelUser.GetOneById(local.ISO_CODE, req.UserId, bson.M{"name": 1, "emails": 1, "language": 1, "referralCode": 1})
	if user != nil && user.Emails != nil && len(user.Emails) > 0 {
		if !user.Emails[0].Verified {
			return &modelEmailResponse.EmailResponse{
				StatusCode: http.StatusOK,
			}, nil
		}

		weekday := lib.GetDaysFromDateTimestamp(sub.Schedule)
		sub.EndDate = sub.Schedule[len(sub.Schedule)-1]
		sub.Weekday = weekday

		option, err := emailRenewSubscription(user, sub)
		if err != nil {
			local.Logger.Warn(lib.ERROR_CONTENT_NIL,
				zap.Error(err),
			)
			return &modelEmailResponse.EmailResponse{
				StatusCode: http.StatusBadRequest,
				Message:    lib.ERROR_CONTENT_NIL,
			}, err
		} else {
			return SendEmail(option)
		}
	}

	return &modelEmailResponse.EmailResponse{
		StatusCode: http.StatusNotFound,
		Message:    lib.ERROR_USER_EMAIL_NOT_EXISTS,
	}, nil
}

func emailRenewSubscription(user *modelUser.Users, sub *modelSubscription.Subscription) (*modelEmailSending.EmailSending, error) {
	language := globalConstant.LANG_EN
	if user.Language != "" {
		language = user.Language
	}

	timeFormat := ""
	dateFormat := ""

	if user.Language == globalConstant.LANG_VI {
		timeFormat = "15:04"
		dateFormat = "02/01/2006"
	} else {
		timeFormat = "3:04 PM"
		dateFormat = "01/02/2006"
	}

	dataStartTime := globalLib.ParseDateFromTimeStamp(sub.Schedule[0], local.TimeZone)
	startTime := dataStartTime.Format(timeFormat)
	endTime := dataStartTime.Add(time.Duration(sub.Duration) * time.Hour).Format(timeFormat)
	startDate := globalLib.ParseDateFromTimeStamp(sub.Schedule[0], local.TimeZone).Format(dateFormat)
	endDate := globalLib.ParseDateFromTimeStamp(sub.EndDate, local.TimeZone).Format(dateFormat)

	days := lib.GetIsoWeekDays(sub.Weekday)
	days = lib.DaysByLang(days, language)

	var settingSystem *modelSettings.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"referralSetting": 1, "subscriptionPaymentInstruction": 1}, &settingSystem)

	var serviceText string
	if sub != nil && sub.ServiceText != nil {
		serviceText = sub.ServiceText.Vi
		switch language {
		case globalConstant.LANG_EN:
			serviceText = sub.ServiceText.En
		case globalConstant.LANG_KO:
			serviceText = sub.ServiceText.Ko
		case globalConstant.LANG_TH:
			serviceText = sub.ServiceText.Th
		}
	}

	currency := globalConstant.CURRENCY_VN
	if sub.Currency != nil && sub.Currency.Code != "" {
		currency = sub.Currency.Code
	}

	emailData := &model.EmailRenewSubsData{}
	emailData.Name = localization.T(language, "RECEIPT_SUBS_EMAIL_SUBS_NAME")
	emailData.Renewing = localization.T(language, "RENEW_SUBS_EMAIL_RENEWING")
	emailData.Dear = localization.T(language, "RENEW_SUBS_EMAIL_DEAR")
	emailData.Thank = localization.T(language, "RENEW_SUBS_EMAIL_THANK_FOR")
	emailData.SubsInfoExpire1 = localization.T(language, "RENEW_SUBS_EMAIL_SUBS_INFO_EXPIRE_1")
	emailData.SubsInfoExpire2 = localization.T(language, "RENEW_SUBS_EMAIL_SUBS_INFO_EXPIRE_2")
	emailData.OrderNumberTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_OEDER_NUMBER")
	emailData.AskerNameTitle = localization.T(language, "CANCEL_EMAIL_ASKER_NAME_TITLE")
	emailData.PaidByTitle = localization.T(language, "RENEW_SUBS_EMAIL_PAID_BY")
	emailData.PaymentMethodBankTransfer = localization.T(language, "BOOKING_PAYMENT_METHOD_BANK_TRANSFER_1")
	emailData.PaymentMethodCard = localization.T(language, "BOOKINP_PAYMENT_METHOD_CARD_1")
	emailData.TasksCompleted = localization.T(language, "RENEW_SUBS_EMAIL_TASKS_COMPLETED")
	emailData.SubsDetailsTitle = localization.T(language, "RENEW_SUBS_EMAIL_SUBS_DETAILS_TITLE")
	emailData.SubsDetailTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_SUBS_DETAIL_TITLE")
	emailData.SubsPlaceTitle = localization.T(language, "CANCEL_EMAIL_TASK_PLACE_TITLE")
	emailData.SubsServiceTitle = localization.T(language, "CANCEL_EMAIL_SERVICE_TITLE")
	emailData.SubsBeginAtTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_START_TITLE")
	emailData.SubsDurationTitle = localization.T(language, "CANCEL_EMAIL_DURATION_TITLE")
	emailData.SubsDaysTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_DAYS_TITLE")
	emailData.SubsDateFromTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_DURATION_TITLE")
	emailData.ReferralTitle = localization.T(language, "EMAIL_REFERRAL_TITLE")
	emailData.FollowBtaskeeAt = localization.T(language, "FOLLOW_BTASKEE_AT")
	emailData.SubsDuration = localization.T(language, "RECEIPT_SUBS_DURATION", sub.Duration)
	emailData.SubsOrderId = sub.OrderId
	emailData.SubsName = serviceText
	emailData.AskerName = user.Name
	emailData.SubsEndDate = endDate
	emailData.SubsOrderId = sub.OrderId
	emailData.PaymentMethod = globalConstant.PAYMENT_METHOD_BANK_TRANSFER
	if sub.Payment != nil && sub.Payment.Method != "" {
		emailData.PaymentMethod = sub.Payment.Method
	}
	emailData.PromotionAccountText = localization.T(language, "PROMOTION_ACCOUNT")
	emailData.CashText = localization.T(language, "CASH")
	numberTaskDone, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"subscriptionId": sub.XId, "status": globalConstant.TASK_STATUS_DONE})
	emailData.SubsNumberTaskDoneText = localization.T(language, "RENEW_SUBS_EMAIL_NUMBER_TASKS_COMPLETED", numberTaskDone)
	emailData.SubsPlace = sub.Address
	emailData.SubsName = serviceText
	emailData.SubsBeginAt = fmt.Sprintf("%s %s %s %s", localization.T(language, "FROM_TIME"), startTime, localization.T(language, "TO_TIME"), endTime)
	emailData.SubsDays = strings.Join(days, ", ")
	emailData.Referral = strings.ToUpper(user.ReferralCode)
	emailData.SubsDateFrom = fmt.Sprintf("%s %s %s %s", localization.T(language, "FROM_DATE"), startDate, localization.T(language, "TO_DATE"), endDate)

	value, currency := getReferralValueAndCurrency(sub.IsoCode, currency, settingSystem)
	emailData.ReferralText = localization.T(language, "EMAIL_REFERRAL_TEXT", globalLib.FormatMoney(value), currency, globalLib.FormatMoney(value), currency)

	// Parse email template
	emailTempalte := fmt.Sprintf("%s/renewSubscription.html", cfg.EmailTemplateURL)

	// Convert to map to use func update btaskee info
	emailDataJson, _ := json.Marshal(emailData)
	emailDataMap := make(map[string]interface{})
	json.Unmarshal(emailDataJson, &emailDataMap)
	emailDataMap = globalLib.UpdateBtaskeeInfoInEmailByIsoCode(emailDataMap, sub.IsoCode)

	body, err := ParseTemplate(emailTempalte, emailDataMap)
	if err != nil {
		return nil, err
	}

	// NOTE: Test with sync cron service "./test.sh 9 3"
	return &modelEmailSending.EmailSending{
		From:    "No Reply <<EMAIL>>",
		To:      user.Emails[0].Address,
		Bcc:     []string{"<EMAIL>"},
		ReplyTo: "<EMAIL>",
		Subject: localization.T(language, "SUBSCRIPTION_EMAIL_RENEW_TITLE"),
		Content: body,
	}, nil
}
