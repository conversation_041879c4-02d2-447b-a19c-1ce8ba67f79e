import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import _ from 'lodash';
import {
  changeTimeZone,
  sendNotificationByIds,
  trackingScheduleStatus,
} from '/server/helper-function';

const remindAskerConfirm = () => {
  return;
	console.log("Start remindAskerConfirm ...");
  var message = null;
  try {
    if (changeTimeZone(new Date()).getHours() >= 23 && changeTimeZone(new Date()).getHours() <= 8) {
      // not run in night (23h -> 8h)
      return;
    }
    const cursor = changeTimeZone(new Date()).getHours() === 22 ?
      Meteor.appCollection.Task.find({
        date: {
          $lte: moment().add(12, 'hours').toDate(), // < 10h tommorow
        },
        status: 'WAITING_ASKER_CONFIRMATION',
        'serviceText.en': 'Cleaning', // Only remind for Cleaning service
      }) :
      Meteor.appCollection.Task.find({
        date: { $lte: moment().add(2, 'hours').toDate() },
        status: 'WAITING_ASKER_CONFIRMATION',
        'serviceText.en': 'Cleaning', // Only remind for Cleaning service
      });
    cursor.forEach((task) => {
      const nAcceptedTasker = _.get(task, 'acceptedTasker.length', 0);
      if (nAcceptedTasker >= 2) {
        Meteor.appCollection.Notification.remove({
          userId: task.askerId,
          type: 25,
          $or: [{
            description: new RegExp('Vui lòng chọn người ưa thích')
          }, {
            description: new RegExp('Please choose one')
          }]
        });
        sendNotificationByIds([task.askerId], {
          title: {
            vi: 'Thông báo',
            en: 'Announcement',
            ko: 'Announcement',
            th: 'Announcement',
          },
          text: {
            vi: `Đã có ${nAcceptedTasker} người nhận việc của bạn. Vui lòng chọn người ưa thích. Hệ thống sẽ tự chọn người đầu tiên nếu sau 30 phút bạn không chọn.`,
            en: `Your task was accepted by ${nAcceptedTasker} taskers. Please choose one. The system will automatically choose the first person if you do not choose after 30 minutes.`,
            ko: `Your task was accepted by ${nAcceptedTasker} taskers. Please choose one. The system will automatically choose the first person if you do not choose after 30 minutes.`,
            th: `Your task was accepted by ${nAcceptedTasker} taskers. Please choose one. The system will automatically choose the first person if you do not choose after 30 minutes.`,
          },
          payload: {taskId: task._id},
        }, { isForceView: true, removeDuplicate: true });
      }
    });
  } catch (e) {
    message = e;
  }

	console.log("Finished remindAskerConfirm ...");
  // Tracking
  trackingScheduleStatus({name: 'RemindAskerConfirm', nextRunAt: SyncedCron.nextScheduledAtDate('remindAskerConfirm'), message});
};

  Meteor.methods({
    remindAskerConfirm() {
      SyncedCron.add({
        name: 'remindAskerConfirm',
        schedule(parser) {
          // Run at 25' every hour
          return parser.cron('0 25 0/1 1/1 * ? *', true);
        },
        job() {
          remindAskerConfirm();
        },
      });
	console.log("Added remindAskerConfirm");
    },
    remindAskerConfirmNow() {
      remindAskerConfirm();
    },
  });
