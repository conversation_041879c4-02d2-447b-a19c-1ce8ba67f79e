package createTaskerMonthlyReport

import (
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTaskerMonthlyReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerMonthlyReport"
)

var c *cron.Cron
var cfg = config.GetConfig()
var isRunning = false
var paramsEnough = true
var runAt string
var LIMIT int64 = 10

type modelReport struct {
	TaskerId          string
	Year              int32
	MonthlyReportInfo *modelTaskerMonthlyReport.TaskerMonthlyReportMonthlyReport
	IsCompanyReport   bool
	EmployeeIds       []string
	LastDoneTask      time.Time
	LastOnline        time.Time
}

func init() {
	var cronConfig map[string]interface{}
	if cfg != nil {
		cronConfig = cfg.ScheduleSyncCron["createtaskermonthlyreport"]
	}

	// Check if params enough
	if cronConfig["run_at"] == nil {
		paramsEnough = false
		lib.PostToSlackNotStart("CreateTaskerMonthlyReport")
		return
	}

	// Get runAt string for sync cron
	runAt = cast.ToString(cronConfig["run_at"])

	// Check if config has is_run = true
	if cronConfig["is_run"] == nil || !cronConfig["is_run"].(bool) {
		lib.PostToSlackNotStart("CreateTaskerMonthlyReport")
		return
	}

	StartCron()
}

func StartCron() {
	c := cron.New(cron.WithLocation(local.TimeZone))
	c.AddFunc(runAt, AutoCreateTaskerYearEndReport)
	c.Start()
	isRunning = true
}

func Run(reqBody *model.RequestAction) {
	// action == 1 -> Run sync cron once now
	action := reqBody.Action
	if action == lib.RUN_NOW {
		CreateTaskerMonthlyReport(reqBody)
	}
	// action == 2 -> Start sync cron with schedule config
	if action == lib.START && paramsEnough && !isRunning {
		StartCron()
	}
	// action == 3 -> Stop running sync cron
	if action == lib.STOP && isRunning {
		c.Stop()
		c = nil
		isRunning = false
	}
}
func AutoCreateTaskerYearEndReport() {
	log.Println("Start CreateTaskerMonthlyReport Process")
	CreateTaskerMonthlyReport(nil)
	defer log.Println("Finish CreateTaskerMonthlyReport Process")
}

func CreateTaskerMonthlyReport(reqBody *model.RequestAction) {
	globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", fmt.Sprintf("Start CreateTaskerMonthlyReport%s Process", local.ISO_CODE))
	defer globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", fmt.Sprintf("Finish CreateTaskerMonthlyReport%s Process", local.ISO_CODE))
	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	lastMonth := currentTime.AddDate(0, -1, 0)
	if reqBody != nil && reqBody.Date != nil {
		lastMonth = reqBody.Date.In(local.TimeZone)
	}
	startAMonth, endAMonth := globalLib.GetStartEndOfMonth(local.TimeZone, lastMonth)
	w := &sync.WaitGroup{}
	w.Add(1)
	// Get askers VN
	taskerCh := getTaskerCh(w, reqBody)
	w.Add(1)
	// Get data report và đưa vào channel (dataTaskCh)
	dataTaskCh := getDataTaskReport(w, taskerCh, startAMonth, endAMonth)
	w.Add(1)
	// Get data review và đưa vào channel (rpCh)
	rpCh := getReviewReport(w, dataTaskCh, startAMonth, endAMonth)
	w.Add(1)
	// Get data bPoint và đưa vào channel (fiCh)
	fiCh := getTaskerReward(w, rpCh, startAMonth, endAMonth)
	w.Add(1)
	// Đọc data từ channel dataCh và insert vào DB
	processInsertDB(w, fiCh, startAMonth)
	w.Wait()
}
