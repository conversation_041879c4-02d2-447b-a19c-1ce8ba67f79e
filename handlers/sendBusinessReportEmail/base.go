package sendBusinessReportEmail

import (
	"os"
	"sync"

	"gitlab.com/btaskee/go-email-vn-v3/config"
	"gitlab.com/btaskee/go-email-vn-v3/handlers"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
)

const (
	formatTime = "02/01/2006"
)

var cfg = config.GetConfig()
var locker sync.Mutex

func SendBusinessReportEmail(req *modelEmailSending.EmailSendBusinessReportRequest) (*modelEmailResponse.EmailResponse, error) {
	defer local.Logger.Sync()

	// lock the progress to avoid race
	locker.Lock()
	defer locker.Unlock()

	// Check server config
	resErr := handlers.CheckEmailConfig()
	if resErr != nil {
		local.Logger.Warn(resErr.Message)
		return resErr, nil
	}

	// 1. validate
	resErr = validate(req)
	if resErr != nil {
		local.Logger.Warn(resErr.Message)
		return resErr, nil
	}

	// 2. get business info
	business, language, user, resErr, err := getBusinessInfo(req.BusinessId)
	if resErr != nil || err != nil {
		if err != nil {
			local.Logger.Warn(err.Error())
		}
		return resErr, err
	}

	// 3. open file
	file, resErr, err := openFile()
	if err != nil {
		local.Logger.Warn(err.Error())
		return resErr, err
	}

	// 4. get members info
	mapMemberDetail, activeMembers := getMembers(language, req.BusinessId)

	// 5. generate sheet 1: list business members
	listMemberSheet(language, file, activeMembers)

	// 6. get business transactions
	depositTransaction, creditTransaction := getBusinessTransactions(req)

	// 7. generate sheet 2: deposit transactions
	depositTransactionSheet(language, file, depositTransaction, mapMemberDetail, business.Name, req.Month, req.Year)

	// 8. generate sheet 3: credit transactions
	creditTransactionSheet(language, file, creditTransaction, mapMemberDetail, business.Name, req.Month, req.Year)

	// 9. save file
	filePath, resErr, err := saveFile(language, file)
	if err != nil {
		local.Logger.Warn(err.Error())
		return resErr, nil
	}
	// delete file after send email
	defer func() {
		file.Close()
		err := os.Remove(filePath)
		if err != nil {
			local.Logger.Warn(err.Error())
		}
	}()

	// 10. send email
	return sendEmail(filePath, language, business.Email, user, req.Month, req.Year)
}
