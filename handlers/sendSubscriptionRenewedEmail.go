package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-email-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelSubscription "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscription"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func SendSubscriptionRenewedEmail(req *modelEmailSending.EmailRequest) (*modelEmailResponse.EmailResponse, error) {
	defer local.Logger.Sync()

	// Check server config
	resErr := CheckEmailConfig()
	if resErr != nil {
		local.Logger.Warn(resErr.Message)
		return resErr, nil
	}

	if req.SubscriptionId == "" {
		local.Logger.Warn(lib.ERROR_SUBSCRIPTION_ID_REQUIRED,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_SUBSCRIPTION_ID_REQUIRED,
		}, nil
	}

	if req.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_USER_ID_REQUIRED,
		}, nil
	}

	var data *modelSubscription.Subscription
	globalDataAccess.GetOneById(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], req.SubscriptionId, bson.M{"serviceText": 1, "userId": 1, "schedule": 1, "endDate": 1, "weekday": 1, "currency": 1, "isoCode": 1, "orderId": 1, "address": 1, "duration": 1, "payment": 1}, &data)
	if data == nil {
		local.Logger.Warn(lib.ERROR_SUBSCRIPTION_NOT_FOUND,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_SUBSCRIPTION_NOT_FOUND,
		}, nil
	}

	user, _ := modelUser.GetOneById(local.ISO_CODE, req.UserId, bson.M{"name": 1, "emails": 1, "language": 1, "referralCode": 1})
	if user != nil && user.Emails != nil && len(user.Emails) > 0 {
		if !user.Emails[0].Verified {
			return &modelEmailResponse.EmailResponse{
				StatusCode: http.StatusOK,
			}, nil
		}

		data.EndDate = data.Schedule[len(data.Schedule)-1]

		option, err := emailSubscriptionRenewed(user, data)
		if err != nil {
			local.Logger.Warn(lib.ERROR_CONTENT_NIL,
				zap.Error(err),
			)
			return &modelEmailResponse.EmailResponse{
				StatusCode: http.StatusBadRequest,
				Message:    lib.ERROR_CONTENT_NIL,
			}, err
		} else {
			return SendEmail(option)
		}
	}

	return &modelEmailResponse.EmailResponse{
		StatusCode: http.StatusNotFound,
		Message:    lib.ERROR_USER_EMAIL_NOT_EXISTS,
	}, nil
}

func emailSubscriptionRenewed(user *modelUser.Users, data *modelSubscription.Subscription) (*modelEmailSending.EmailSending, error) {
	language := globalConstant.LANG_EN
	if user.Language != "" {
		language = user.Language
	}

	timeFormat := ""
	dateFormat := ""

	if user.Language == globalConstant.LANG_VI {
		timeFormat = "15:04"
		dateFormat = "02/01/2006"
	} else {
		timeFormat = "3:04 PM"
		dateFormat = "01/02/2006"
	}

	dataStartTime := globalLib.ParseDateFromTimeStamp(data.Schedule[0], local.TimeZone)
	startTime := dataStartTime.Format(timeFormat)
	endTime := dataStartTime.Add(time.Duration(data.Duration) * time.Hour).Format(timeFormat)
	startDate := globalLib.ParseDateFromTimeStamp(data.Schedule[0], local.TimeZone).Format(dateFormat)
	endDate := globalLib.ParseDateFromTimeStamp(data.EndDate, local.TimeZone).Format(dateFormat)

	days := lib.GetIsoWeekDays(data.Weekday)
	days = lib.DaysByLang(days, language)

	var settingSystem *modelSettings.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"referralSetting": 1, "subscriptionPaymentInstruction": 1}, &settingSystem)

	var serviceText string
	if data != nil && data.ServiceText != nil {
		serviceText = data.ServiceText.Vi
		switch language {
		case globalConstant.LANG_EN:
			serviceText = data.ServiceText.En
		case globalConstant.LANG_KO:
			serviceText = data.ServiceText.Ko
		case globalConstant.LANG_TH:
			serviceText = data.ServiceText.Th
		}
	}

	currency := globalConstant.CURRENCY_VN
	if data.Currency != nil && data.Currency.Code != "" {
		currency = data.Currency.Code
	}

	emailData := &model.EmailRenewSubsData{}
	emailData.Name = strings.ToUpper(serviceText)
	emailData.Renewing = localization.T(language, "SUBSCRIPTION_RENEWED_TITLE")
	emailData.Dear = localization.T(language, "RENEW_SUBS_EMAIL_DEAR")
	emailData.Thank = localization.T(language, "RENEW_SUBS_EMAIL_THANK_FOR")
	emailData.SubsInfoExpire1 = localization.T(language, "SUBSCRIPTION_RENEWED_DESCRIPTION_1")
	if language == globalConstant.LANG_KO {
		emailData.SubsInfoExpire1_1 = localization.T(language, "SUBSCRIPTION_RENEWED_DESCRIPTION_1.1")
	}
	emailData.SubsInfoExpire2 = localization.T(language, "SUBSCRIPTION_RENEWED_DESCRIPTION_2")
	emailData.OrderNumberTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_OEDER_NUMBER")
	emailData.SubsDetailsTitle = localization.T(language, "SUBSCRIPTION_RENEWED_DETAIL_TITLE")
	emailData.SubsDetailTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_SUBS_DETAIL_TITLE")
	emailData.SubsPlaceTitle = localization.T(language, "CANCEL_EMAIL_TASK_PLACE_TITLE")
	emailData.SubsServiceTitle = localization.T(language, "CANCEL_EMAIL_SERVICE_TITLE")
	emailData.SubsBeginAtTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_START_TITLE")
	emailData.SubsDurationTitle = localization.T(language, "CANCEL_EMAIL_DURATION_TITLE")
	emailData.SubsDaysTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_DAYS_TITLE")
	emailData.SubsDateFromTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_DURATION_TITLE")
	emailData.ReferralTitle = localization.T(language, "EMAIL_REFERRAL_TITLE")
	emailData.FollowBtaskeeAt = localization.T(language, "FOLLOW_BTASKEE_AT")
	emailData.SubsDuration = localization.T(language, "RECEIPT_SUBS_DURATION", data.Duration)
	emailData.SubsOrderId = data.OrderId
	emailData.SubsName = serviceText
	emailData.AskerName = user.Name
	emailData.SubsStartDate = startDate
	emailData.SubsOrderId = data.OrderId
	emailData.PaymentMethod = globalConstant.PAYMENT_METHOD_BANK_TRANSFER
	if data.Payment != nil && data.Payment.Method != "" {
		emailData.PaymentMethod = data.Payment.Method
	}
	emailData.PromotionAccountText = localization.T(language, "PROMOTION_ACCOUNT")
	emailData.CashText = localization.T(language, "CASH")
	numberTaskDone, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], bson.M{"subscriptionId": data.XId, "status": globalConstant.TASK_STATUS_DONE})
	emailData.SubsNumberTaskDoneText = localization.T(language, "RENEW_SUBS_EMAIL_NUMBER_TASKS_COMPLETED", numberTaskDone)
	emailData.SubsPlace = data.Address
	emailData.SubsName = serviceText
	emailData.SubsBeginAt = fmt.Sprintf("%s %s %s %s", localization.T(language, "FROM_TIME"), startTime, localization.T(language, "TO_TIME"), endTime)
	emailData.SubsDays = strings.Join(days, ", ")
	emailData.Referral = strings.ToUpper(user.ReferralCode)
	emailData.SubsDateFrom = fmt.Sprintf("%s %s %s %s", localization.T(language, "FROM_DATE"), startDate, localization.T(language, "TO_DATE"), endDate)

	value, currency := getReferralValueAndCurrency(data.IsoCode, currency, settingSystem)
	emailData.ReferralText = localization.T(language, "EMAIL_REFERRAL_TEXT", globalLib.FormatMoney(value), currency, globalLib.FormatMoney(value), currency)

	// Update contact info of bTaskee
	b, _ := json.Marshal(emailData)
	emailDataMap := make(map[string]interface{})
	json.Unmarshal(b, &emailDataMap)
	emailDataMap = globalLib.UpdateBtaskeeInfoInEmailByIsoCode(emailDataMap, data.IsoCode)

	// Parse email template
	emailTempalte := fmt.Sprintf("%s/subscriptionRenewed.html", cfg.EmailTemplateURL)

	body, err := ParseTemplate(emailTempalte, emailDataMap)
	if err != nil {
		return nil, err
	}

	return &modelEmailSending.EmailSending{
		From:    "No Reply <<EMAIL>>",
		To:      user.Emails[0].Address,
		Bcc:     []string{"<EMAIL>"},
		ReplyTo: "<EMAIL>",
		Subject: localization.T(language, "SUBSCRIPTION_EMAIL_RENEW_TITLE"),
		Content: body,
	}, nil
}
