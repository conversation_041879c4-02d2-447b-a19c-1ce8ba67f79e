package autoUpgradeMedalForCommunityUser

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelCommunityMedal "gitlab.com/btaskee/go-services-model-v2/grpcmodel/communityMedal"
	"go.mongodb.org/mongo-driver/bson"
)

func getCommunityMedalInfo() (medals []*modelCommunityMedal.CommunityMedal) {
	query := bson.M{
		"status":     globalConstant.COMMUNITY_MEDAL_STATUS_ACTIVE,
		"conditions": bson.M{"$exists": true},
	}
	field := bson.M{
		"_id":        1,
		"name":       1,
		"conditions": 1,
		"text":       1,
		"tagIds":     1,
	}
	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_COMMUNITY_MEDAL[local.ISO_CODE], query, field, &medals)
	if err != nil {
		textErr := fmt.Sprintf("[Sync Cron VN v3] AutoUpgradeMedalForCommunityUser %s: can not get medal community , err: %s ", local.ISO_CODE, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], textErr)
		return nil
	}
	return
}
