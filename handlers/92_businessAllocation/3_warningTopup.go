package businessAllocation

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcProxy/grpcWebsocket"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	modelBusinessLevel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func sendNotiWarningTopup(business *modelBusiness.Business) {
	sendNotificationSuccess(business.XId, "BUSINESS_NOT_ENOUGH_MONEY_TOPUP_TITLE", "BUSINESS_NOT_ENOUGH_MONEY_TOPUP_BODY", globalConstant.PAYLOAD_NAVIGATE_TO_TRANSACTION_BUSINESS_ACCOUNT)
}

func checkMoneyNeedTopup(business *modelBusiness.Business) float64 {
	levelInfos := getLevelInfo(business.XId)
	if len(levelInfos) == 0 {
		return 0
	}
	levelIds := []string{}
	for _, levelInfo := range levelInfos {
		levelIds = append(levelIds, levelInfo.XId)
	}
	membersByLevel := getMembersByLevel(levelIds)
	if len(membersByLevel) == 0 {
		return 0
	}
	bPayTopup := getBPayTopup(levelInfos, membersByLevel)

	return business.BPay - bPayTopup
}

func getBPayTopup(levelInfos []*modelBusinessLevel.BusinessLevel, mapMembersByLevelId map[string][]*modelBusinessMember.BusinessMember) float64 {
	// Check business bpay
	var totalAmount float64 = 0
	for _, level := range levelInfos {
		totalAmount += (level.Amount * float64(len(mapMembersByLevelId[level.XId])))
	}
	return totalAmount
}

func sendNotificationSuccess(userId, titleKey, bodyKey, navigateTo string, bodyParams ...interface{}) {
	asker, _ := modelUsers.GetOneById(local.ISO_CODE, userId, bson.M{"language": 1})
	lang := globalConstant.LANG_VI
	if asker != nil && asker.Language != "" {
		lang = asker.Language
	}
	notify := &modelNotification.Notification{
		XId:         globalLib.GenerateObjectId(),
		UserId:      userId,
		Type:        25,
		Title:       localization.T(lang, titleKey),
		Description: localization.T(lang, bodyKey, bodyParams...),
		CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
	}
	title := localization.GetLocalizeObject(titleKey)
	body := localization.GetLocalizeObject(bodyKey, bodyParams...)
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type: 25,
	}
	if navigateTo != "" {
		notify.NavigateTo = navigateTo
		payload.NavigateTo = navigateTo
	}
	userIds := []*modelPushNotificationRequest.PushNotificationRequestUserIds{{UserId: userId, Language: globalConstant.LANG_EN}}
	lib.SendNotification([]interface{}{notify}, userIds, title, body, payload, true)
	wsMessage, err := grpcWebsocket.SendSocketNotification(local.ISO_CODE, []interface{}{notify}, cfg.GRPC_Websocket_Service_V2_URL, userIds, payload.Type, title, body)
	if err != nil {
		local.Logger.Error(lib.ERROR_WEBSOCKET_ERROR.ErrorCode,
			zap.Error(err),
			zap.Any("body", wsMessage),
		)
	}
}
