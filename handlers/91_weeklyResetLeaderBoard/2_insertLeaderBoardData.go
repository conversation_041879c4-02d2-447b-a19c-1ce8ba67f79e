package weeklyResetLeaderBoard

import (
	"encoding/json"
	"fmt"
	"runtime/debug"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.uber.org/zap"
)

func weeklyInsertLeaderBoardData(taskerList []string) (interface{}, error) {
	defer local.Logger.Sync()

	// handle recover error
	defer func() {
		if r := recover(); r != nil {
			msg := fmt.Sprintf("WeeklyInsertLeaderBoardData %s: recover panic error: %v. debug stack: %v", local.ISO_CODE, r, string(debug.Stack()[:1000]))
			globalLib.PostToSlack(cfg.SlackToken, "go-journey-tasker", globalConstant.SLACK_USER_NAME, msg)
			local.Logger.Warn("WeeklyInsertLeaderBoardData",
				zap.String("isoCode", local.ISO_CODE),
				zap.String("stack", string(debug.Stack())),
			)
			return
		}
	}()

	var response map[string]interface{}
	var err error
	defer func() {
		responseData, _ := json.Marshal(response)
		msg := fmt.Sprintf("WeeklyInsertLeaderBoardData_%s. result: %s. error: %v", local.ISO_CODE, string(responseData), err)
		globalLib.PostToSlack(cfg.SlackToken, "go-journey-tasker", globalConstant.SLACK_USER_NAME, msg)
	}()

	// 1. get setting
	citySupportJourney, levelSettingByCity, defaultLevelSetting, startLevelByCity, err := getJourneySettings()
	_ = startLevelByCity
	if err != nil {
		return nil, err
	}
	// 3. get list tasker in serviceChannel
	if len(taskerList) == 0 {
		taskerList, err = getTaskersInServiceChannel()
		if err != nil || len(taskerList) == 0 {
			return nil, err
		}
	}

	cities := make(map[string]ModelGroupTaskerByLevel)
	responseCity := []map[string]interface{}{}

	for _, cityName := range citySupportJourney {
		// 6. Get tasker has changed level in day
		mapTaskers, err := getTaskers(cityName, taskerList)
		if err != nil {
			return nil, err
		}

		// 8. Calculate new leader board
		newLeaderBoard := calculateNewLeaderBoard(cityName, levelSettingByCity[cityName], defaultLevelSetting, mapTaskers)
		cities[cityName] = newLeaderBoard

		// add result to response
		numberTaskerByNewLevels := make(map[string]interface{})
		for levelName, taskersInLevel := range newLeaderBoard {
			numberTaskerByNewLevels[levelName] = len(taskersInLevel)
		}
		responseCity = append(responseCity, map[string]interface{}{
			"cityName":      cityName,
			"numberTaskers": len(mapTaskers),
		})
	}

	if len(cities) == 0 {
		return nil, nil
	}

	// 8. Update to database
	err = updateToDatabase(cities)
	if err != nil {
		return nil, err
	}

	response = map[string]interface{}{
		"cities": responseCity,
	}
	return response, nil
}
