package testing

import (
	"context"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-email-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	"go.mongodb.org/mongo-driver/bson"
)

func TestSendCancelFeeEmail(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		ResetData()
		UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$set": bson.M{"priceSetting.maxCancelTaskFee": 1000, "priceSetting.minCancelTaskFee": 200}})
		UpdateSettingSystem(bson.M{"$set": bson.M{"cancelTaskFeeInPercentage": 30, "cancelTaskFee": 30}})
		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"emails": []map[string]interface{}{
					{
						"address":  "<EMAIL>",
						"verified": true,
					},
				},
			},
		})
		taskIds := CreateTask([]map[string]interface{}{
			{
				"serviceName": globalConstant.SERVICE_NAME_HOME_CLEANING,
				"askerPhone":  "0834567890",
				"description": "Task 01",
				"originCurrency": map[string]interface{}{
					"sign": "₫",
					"code": "VND",
				},
				"isoCode": local.ISO_CODE,
			},
		})
		Convey("Given a request to check send receipt email", t, func() {
			req := &modelEmailSending.EmailCancelFeeRequest{
				TaskId:             taskIds[0],
				CancelFee:          20,
				CancellationReason: "No Reason",
			}
			Convey("Check the response if email content nil (no such file directory)", func() {
				s := service.Server{}
				_, err := s.SendCancelFeeEmail(context.Background(), req)
				UpdateService(bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"$set": bson.M{"priceSetting.maxCancelTaskFee": 1000, "priceSetting.minCancelTaskFee": 200}})
				UpdateSettingSystem(bson.M{"$set": bson.M{"cancelTaskFeeInPercentage": 30, "cancelTaskFee": 30}})
				So(err, ShouldNotBeNil)
			})
		})
	})
}
