// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcAcceptTaskIndo

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	acceptBookingRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/acceptBookingRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// AcceptTaskIndoClient is the client API for AcceptTaskIndo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AcceptTaskIndoClient interface {
	ChooseTasker(ctx context.Context, in *acceptBookingRequest.AcceptBookingRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type acceptTaskIndoClient struct {
	cc grpc.ClientConnInterface
}

func NewAcceptTaskIndoClient(cc grpc.ClientConnInterface) AcceptTaskIndoClient {
	return &acceptTaskIndoClient{cc}
}

func (c *acceptTaskIndoClient) ChooseTasker(ctx context.Context, in *acceptBookingRequest.AcceptBookingRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcAcceptTaskIndo.AcceptTaskIndo/ChooseTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AcceptTaskIndoServer is the server API for AcceptTaskIndo service.
// All implementations must embed UnimplementedAcceptTaskIndoServer
// for forward compatibility
type AcceptTaskIndoServer interface {
	ChooseTasker(context.Context, *acceptBookingRequest.AcceptBookingRequest) (*empty.Empty, error)
	mustEmbedUnimplementedAcceptTaskIndoServer()
}

// UnimplementedAcceptTaskIndoServer must be embedded to have forward compatible implementations.
type UnimplementedAcceptTaskIndoServer struct {
}

func (UnimplementedAcceptTaskIndoServer) ChooseTasker(context.Context, *acceptBookingRequest.AcceptBookingRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChooseTasker not implemented")
}
func (UnimplementedAcceptTaskIndoServer) mustEmbedUnimplementedAcceptTaskIndoServer() {}

// UnsafeAcceptTaskIndoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AcceptTaskIndoServer will
// result in compilation errors.
type UnsafeAcceptTaskIndoServer interface {
	mustEmbedUnimplementedAcceptTaskIndoServer()
}

func RegisterAcceptTaskIndoServer(s *grpc.Server, srv AcceptTaskIndoServer) {
	s.RegisterService(&_AcceptTaskIndo_serviceDesc, srv)
}

func _AcceptTaskIndo_ChooseTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(acceptBookingRequest.AcceptBookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AcceptTaskIndoServer).ChooseTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcAcceptTaskIndo.AcceptTaskIndo/ChooseTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AcceptTaskIndoServer).ChooseTasker(ctx, req.(*acceptBookingRequest.AcceptBookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _AcceptTaskIndo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcAcceptTaskIndo.AcceptTaskIndo",
	HandlerType: (*AcceptTaskIndoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ChooseTasker",
			Handler:    _AcceptTaskIndo_ChooseTasker_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "acceptTaskIndo.proto",
}
