syntax = "proto3";
package grpcEventMY;

option go_package = "gitlab.com/btaskee/go-event-service-indo-v3/grpcEventMY";

import "google/protobuf/empty.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/eventMessage/eventMessage.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users/users.proto";

service Event {
  rpc TrackingCleverTapPayComboSuccess(eventMessage.TrackingCleverTapPayComboMessage) returns (google.protobuf.Empty) {} 
  rpc TrackingCleverTapPayComboPaymentError(eventMessage.TrackingCleverTapPayComboMessage) returns (google.protobuf.Empty) {} 
  rpc TrackingCleverTapTaskerAcceptTask(eventMessage.TrackingCleverTapTaskMessage) returns (google.protobuf.Empty) {} 
  rpc TrackingCleverTapTaskerCancelTask(eventMessage.TrackingCleverTapTaskMessage) returns (google.protobuf.Empty) {} 
  rpc TrackingCleverTapTaskerWithdrawTask(eventMessage.TrackingCleverTapTaskMessage) returns (google.protobuf.Empty) {} 
  rpc TrackingCleverTapTaskerDoneTask(eventMessage.TrackingCleverTapTaskMessage) returns (google.protobuf.Empty) {} 
  rpc TrackingCleverTapTaskerRating(eventMessage.TrackingCleverTapRatingMessage) returns (google.protobuf.Empty) {} 
  rpc TrackingCleverTapTaskerFinishTraining(eventMessage.TrackingCleverTapTrainingMessage) returns (google.protobuf.Empty) {}
  rpc AddAskerDoneBookingReferralGift(users.Users) returns (google.protobuf.Empty) {} 
  rpc AddAskerSignUpReferralGift(users.Users) returns (google.protobuf.Empty) {} 
  rpc TrackingCleverTapTaskExpired(eventMessage.TrackingCleverTapTaskMessage) returns (google.protobuf.Empty) {}
}