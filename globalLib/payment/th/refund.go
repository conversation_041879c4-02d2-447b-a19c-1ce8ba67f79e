package th

import (
	"errors"
	"fmt"
	"time"

	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/th"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalModel/modelTransactionInfo"
	modelRefundRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/refundRequest"
)

func CreateRefundRequestTaskCard_TH(transactionInfo *modelTransactionInfo.TransactionInfo, cancelFee float64, reason string, timeZone *time.Location) (refundRequestId string, refundAmount float64, isBPayRefund bool, err error) {
	if !isTransactionSuccess(transactionInfo) {
		err = errors.New("transaction not paid")
		return
	}

	// 2. calculate refund amount
	refundAmount = transactionInfo.Amount - cancelFee
	if refundAmount <= 0 {
		return
	}

	// 4. refund wallet
	err = createdRefundRequestCard_TH(transactionInfo, refundAmount, cancelFee, reason, timeZone)
	return
}

func createdRefundRequestCard_TH(transactionInfo *modelTransactionInfo.TransactionInfo, refundAmount, cancelFee float64, reason string, timeZone *time.Location) (err error) {
	if refundAmount <= 0 {
		return
	}
	refundRequest := &modelRefundRequest.RefundRequest{
		XId:           globalLib.GenerateObjectId(),
		Type:          globalConstant.REFUND_REQUEST_TYPE_SYSTEM_REFUND,
		Reason:        reason,
		CancelTaskFee: cancelFee,
		Amount:        refundAmount,
		TaskId:        transactionInfo.TaskId,
		UserId:        transactionInfo.UserId,
		InvoiceNo:     transactionInfo.InvoiceNo,
		PaymentMethod: transactionInfo.PaymentMethod,
		Currency:      globalConstant.CURRENCY_SIGN_TH.Code,
		Status:        globalConstant.REFUND_REQUEST_STATUS_NEW,
		CreatedAt:     globalLib.GetCurrentTimestamp(timeZone),
	}
	err = globalDataAccess.InsertOne(globalCollection.COLLECTION_REFUND_REQUEST[globalConstant.ISO_CODE_TH], refundRequest)
	if err != nil {
		return fmt.Errorf("cannot create refund request for task, err: %v", err)
	}
	return
}
