package autoCalculateJourneyLeaderBoard

import (
	"encoding/json"
	"fmt"
	"runtime/debug"
	"sync"
	"time"

	"github.com/robfig/cron/v3"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelJourneyLeaderBoard "gitlab.com/btaskee/go-services-model-v2/grpcmodel/journeyLeaderBoard"
	"go.uber.org/zap"
)

var c *cron.Cron
var cfg = config.GetConfig()
var isRunning = false
var paramsEnough = true
var runAt string

func init() {
	var cronConfig map[string]interface{}
	if cfg != nil {
		cronConfig = cfg.ScheduleSyncCron["autocalculatejourneyleaderboard"]
	}

	// Check if params enough
	if cronConfig["run_at"] == nil {
		paramsEnough = false
		lib.PostToSlackNotStart("AutoCalculateJourneyLeaderBoard")
		return
	}

	// Get runAt string for sync cron
	runAt = cronConfig["run_at"].(string)

	// Check if config has is_run = true
	if cronConfig["is_run"] == nil || !cronConfig["is_run"].(bool) {
		lib.PostToSlackNotStart("AutoCalculateJourneyLeaderBoard")
		return
	}

	StartCron()
}

func StartCron() {
	c := cron.New(cron.WithLocation(local.TimeZone))
	c.AddFunc(runAt, autoCalculateJourneyLeaderBoard)
	c.Start()
	isRunning = true
}

func Run(reqBody *model.RequestAction) interface{} {
	// action == 1 -> Run sync cron once now
	if reqBody.Action == lib.RUN_NOW {
		var result interface{}
		var err error
		result, err = CalculateJourneyLeaderBoard(reqBody.FromDate, reqBody.ToDate, reqBody.RunUsers)
		if err != nil {
			msg := fmt.Sprintf("AUTO_CALCULATE_JOURNEY_LEADER_BOARD_%s error: %v", reqBody.IsoCode, err)
			globalLib.PostToSlack(cfg.SlackToken, "go-journey-tasker", globalConstant.SLACK_USER_NAME, msg)
			return nil
		}
		return result
	}
	// action == 2 -> Start sync cron with schedule config
	if reqBody.Action == lib.START && paramsEnough && !isRunning {
		StartCron()
	}
	// action == 3 -> Stop running sync cron
	if reqBody.Action == lib.STOP && isRunning {
		c.Stop()
		c = nil
		isRunning = false
	}
	return nil
}

const (
	CHUNK_SIZE_GET_RATINGS           = 500
	CHUNK_SIZE_GET_TASKERS           = 300
	CHUNK_SIZE_GET_LEADER_BOARD      = 300
	CHUNK_SIZE_TASKERS_GET_DONE_TASK = 100
	CHUNK_SIZE_INSERT_LEADER_BOARD   = 50
	CHUNK_SIZE_DELETE_LEADER_BOARD   = 300
)

func autoCalculateJourneyLeaderBoard() {
	CalculateJourneyLeaderBoard(nil, nil, nil)
}

func CalculateJourneyLeaderBoard(fromDate, toDate *time.Time, taskerList []string) (interface{}, error) {
	defer local.Logger.Sync()

	// handle recover error
	defer func() {
		if r := recover(); r != nil {
			msg := fmt.Sprintf("AUTO_CALCULATE_JOURNEY_LEADER_BOARD %s: recover panic error: %v. debug stack: %v", local.ISO_CODE, r, string(debug.Stack()[:1000]))
			globalLib.PostToSlack(cfg.SlackToken, "go-journey-tasker", globalConstant.SLACK_USER_NAME, msg)
			local.Logger.Warn("AUTO_CALCULATE_JOURNEY_LEADER_BOARD_ERROR",
				zap.String("isoCode", local.ISO_CODE),
				zap.String("stack", string(debug.Stack())),
			)
			return
		}
	}()

	var response map[string]interface{}
	var err error
	defer func() {
		responseData, _ := json.Marshal(response)
		msg := fmt.Sprintf("AUTO_CALCULATE_JOURNEY_LEADER_BOARD_%s. result: %s. error: %v", local.ISO_CODE, string(responseData), err)
		globalLib.PostToSlack(cfg.SlackToken, "go-journey-tasker", globalConstant.SLACK_USER_NAME, msg)
	}()

	var errDeleteBackup error
	wgWaitDeleteBackup := &sync.WaitGroup{}
	wgWaitDeleteBackup.Add(1)
	go func() {
		defer wgWaitDeleteBackup.Done()
		errDeleteBackup = deleteBackupBefore()
	}()

	// 1. get setting
	citySupportJourney, levelSettingByCity, defaultLevelSetting, startLevelByCity, err := getJourneySettings()
	if err != nil {
		return nil, err
	}
	// 3. get list tasker in serviceChannel
	if len(taskerList) == 0 {
		taskerList, err = getTaskersInServiceChannel()
		if err != nil || len(taskerList) == 0 {
			return nil, err
		}
	}
	// 4. Calculate point in day of tasker
	mapPointByTaskerId, taskerHasPointInDayIds, err := calculatePointInDayOfTasker(taskerList, fromDate, toDate)
	if err != nil {
		return nil, err
	}

	cities := make(map[string]ModelGroupTaskerByLevel)
	oldLeaderBoard := []*modelJourneyLeaderBoard.JourneyLeaderBoard{}
	responseCity := []map[string]interface{}{}

	for _, cityName := range citySupportJourney {
		// 5. get tasker in leaderBoard by cityName and level
		oldLeaderBoardByCity, mapTaskerInLeaderBoard, taskerInLeaderBoardIds, err := getTaskersInLeaderBoard(cityName, taskerList)
		if err != nil {
			return nil, err
		}
		oldLeaderBoard = append(oldLeaderBoard, oldLeaderBoardByCity...)
		// 6. Get tasker has changed level in day
		mapTaskers, err := getTaskers(cityName, taskerList)
		if err != nil {
			return nil, err
		}

		// 7. Get tasker has point in day
		mapTaskersHasPointInDay, err := getTaskersHasPointInDay(cityName, taskerHasPointInDayIds)
		if err != nil {
			return nil, err
		}

		// 8. Calculate new leader board
		newLeaderBoard := calculateNewLeaderBoard(cityName, levelSettingByCity[cityName], defaultLevelSetting, mapPointByTaskerId, mapTaskers, mapTaskerInLeaderBoard, mapTaskersHasPointInDay, startLevelByCity[cityName])
		cities[cityName] = newLeaderBoard

		// add result to response
		numberTaskerByNewLevels := make(map[string]interface{})
		for levelName, taskersInLevel := range newLeaderBoard {
			numberTaskerByNewLevels[levelName] = len(taskersInLevel)
		}
		responseCity = append(responseCity, map[string]interface{}{
			"cityName":                     cityName,
			"numberTaskerInOldLeaderBoard": len(taskerInLeaderBoardIds),
			"numberTasker":                 len(mapTaskers),
			"numberTaskerByNewLevels":      numberTaskerByNewLevels,
		})
	}

	if len(cities) == 0 {
		return nil, nil
	}

	// Check if delete old backup done
	wgWaitDeleteBackup.Wait()
	if errDeleteBackup != nil {
		return nil, errDeleteBackup
	}
	// 8. Update to database
	err = updateToDatabase(cities, oldLeaderBoard)
	if err != nil {
		return nil, err
	}

	response = map[string]interface{}{
		"numberTaskerHasPointInDay": len(taskerHasPointInDayIds),
		"cities":                    responseCity,
	}
	return response, nil
}

func getTimeRangeToCheckPoint(atDate time.Time) (time.Time, time.Time) {
	day := globalLib.GetCurrentTime(local.TimeZone)
	if !atDate.IsZero() {
		day = atDate
	}
	startADay := globalLib.StartADay(day)
	return startADay.AddDate(0, 0, -1), startADay.Add(-1 * time.Microsecond)
}
