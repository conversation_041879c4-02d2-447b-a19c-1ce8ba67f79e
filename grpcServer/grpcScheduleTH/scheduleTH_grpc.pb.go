// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcScheduleTH

import (
	context "context"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	taskSchedule "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskSchedule"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// ScheduleTHClient is the client API for ScheduleTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ScheduleTHClient interface {
	AddHomeCleaningSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error)
	AddHomeCookingSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error)
	AddHousekeepingSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error)
	AddOfficeCleaningSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error)
}

type scheduleTHClient struct {
	cc grpc.ClientConnInterface
}

func NewScheduleTHClient(cc grpc.ClientConnInterface) ScheduleTHClient {
	return &scheduleTHClient{cc}
}

func (c *scheduleTHClient) AddHomeCleaningSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcScheduleTH.ScheduleTH/AddHomeCleaningSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleTHClient) AddHomeCookingSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcScheduleTH.ScheduleTH/AddHomeCookingSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleTHClient) AddHousekeepingSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcScheduleTH.ScheduleTH/AddHousekeepingSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *scheduleTHClient) AddOfficeCleaningSchedule(ctx context.Context, in *taskSchedule.TaskSchedule, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcScheduleTH.ScheduleTH/AddOfficeCleaningSchedule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ScheduleTHServer is the server API for ScheduleTH service.
// All implementations must embed UnimplementedScheduleTHServer
// for forward compatibility
type ScheduleTHServer interface {
	AddHomeCleaningSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error)
	AddHomeCookingSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error)
	AddHousekeepingSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error)
	AddOfficeCleaningSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error)
	mustEmbedUnimplementedScheduleTHServer()
}

// UnimplementedScheduleTHServer must be embedded to have forward compatible implementations.
type UnimplementedScheduleTHServer struct {
}

func (UnimplementedScheduleTHServer) AddHomeCleaningSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddHomeCleaningSchedule not implemented")
}
func (UnimplementedScheduleTHServer) AddHomeCookingSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddHomeCookingSchedule not implemented")
}
func (UnimplementedScheduleTHServer) AddHousekeepingSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddHousekeepingSchedule not implemented")
}
func (UnimplementedScheduleTHServer) AddOfficeCleaningSchedule(context.Context, *taskSchedule.TaskSchedule) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddOfficeCleaningSchedule not implemented")
}
func (UnimplementedScheduleTHServer) mustEmbedUnimplementedScheduleTHServer() {}

// UnsafeScheduleTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ScheduleTHServer will
// result in compilation errors.
type UnsafeScheduleTHServer interface {
	mustEmbedUnimplementedScheduleTHServer()
}

func RegisterScheduleTHServer(s *grpc.Server, srv ScheduleTHServer) {
	s.RegisterService(&_ScheduleTH_serviceDesc, srv)
}

func _ScheduleTH_AddHomeCleaningSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskSchedule.TaskSchedule)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleTHServer).AddHomeCleaningSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcScheduleTH.ScheduleTH/AddHomeCleaningSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleTHServer).AddHomeCleaningSchedule(ctx, req.(*taskSchedule.TaskSchedule))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleTH_AddHomeCookingSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskSchedule.TaskSchedule)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleTHServer).AddHomeCookingSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcScheduleTH.ScheduleTH/AddHomeCookingSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleTHServer).AddHomeCookingSchedule(ctx, req.(*taskSchedule.TaskSchedule))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleTH_AddHousekeepingSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskSchedule.TaskSchedule)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleTHServer).AddHousekeepingSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcScheduleTH.ScheduleTH/AddHousekeepingSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleTHServer).AddHousekeepingSchedule(ctx, req.(*taskSchedule.TaskSchedule))
	}
	return interceptor(ctx, in, info, handler)
}

func _ScheduleTH_AddOfficeCleaningSchedule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskSchedule.TaskSchedule)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ScheduleTHServer).AddOfficeCleaningSchedule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcScheduleTH.ScheduleTH/AddOfficeCleaningSchedule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ScheduleTHServer).AddOfficeCleaningSchedule(ctx, req.(*taskSchedule.TaskSchedule))
	}
	return interceptor(ctx, in, info, handler)
}

var _ScheduleTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcScheduleTH.ScheduleTH",
	HandlerType: (*ScheduleTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AddHomeCleaningSchedule",
			Handler:    _ScheduleTH_AddHomeCleaningSchedule_Handler,
		},
		{
			MethodName: "AddHomeCookingSchedule",
			Handler:    _ScheduleTH_AddHomeCookingSchedule_Handler,
		},
		{
			MethodName: "AddHousekeepingSchedule",
			Handler:    _ScheduleTH_AddHousekeepingSchedule_Handler,
		},
		{
			MethodName: "AddOfficeCleaningSchedule",
			Handler:    _ScheduleTH_AddOfficeCleaningSchedule_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "scheduleTH.proto",
}
