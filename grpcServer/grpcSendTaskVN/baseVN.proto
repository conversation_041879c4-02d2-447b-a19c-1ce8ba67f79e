syntax = "proto3";
package grpcSendTaskVN;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcSendTaskVN";

import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask/pushNotificationNewTask.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response/response.proto";

service PushNotificationNewTaskVN {
  rpc NewTask (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-vn/new-task"
      body: "*"
    };
  }
  rpc TopTasker (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-vn/top-tasker"
      body: "*"
    };
  }
  rpc NewTaskToDistrict (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-vn/district"
      body: "*"
    };
  }
  rpc FavTasker (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-vn/favorite-tasker"
      body: "*"
    };
  }
  rpc Normal (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-vn/normal"
      body: "*"
    };
  }
  rpc FavAndTaskerDistrict (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-vn/favorite-and-tasker-district"
      body: "*"
    };
  }
  rpc FavAndTopTasker (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-vn/favorite-and-top-tasker"
      body: "*"
    };
  }
  rpc NewTaskToCity (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-vn/city"
      body: "*"
    };
  }
}