//Package service file routes
/*
 * @File: routes.go
 * @Description: Varb Route
 * @CreatedAt: 22/02/2021
 * @Author: vinhnt
 */
package service

var routes = Routes{
	Route{
		"SyncCronVNV3_RollbackMissingTaskActionPenalty",
		"POST",
		"/rollback-missing-task-action-penalty",
		RollbackMissingTaskActionPenalty,
	},
	Route{
		"SyncCronVNV3_AutoNotifyForCommunityNotification",
		"POST",
		"/auto-notify-for-community-notification",
		AutoNotifyForCommunityNotification,
	},
	// CreateTaskerMonthlyReport
	Route{
		"SyncCronVNV3_CreateTaskerMonthlyReport",
		"POST",
		"/create-tasker-monthly-report",
		CreateTaskerMonthlyReport,
	},
	// CreateAskerMonthlyReport
	Route{
		"SyncCronVNV3_CreateAskerMonthlyReport",
		"POST",
		"/create-asker-monthly-report",
		CreateAskerMonthlyReport,
	},
	//CreateTaskerWorkingHistory
	Route{
		"SyncCronVNV3_CreateTaskerWorkingHistory",
		"POST",
		"/create-tasker-working-history",
		CreateTaskerWorkingHistory,
	},
	// NotifyTaskerGameCampaign
	Route{
		"SyncCronVNV3_NotifyTaskerGameCampaign",
		"POST",
		"/notify-tasker-game-campaign",
		NotifyTaskerGameCampaign,
	},
	//BusinessAllocation
	Route{
		"SyncCronVNV3_BusinessAllocation",
		"POST",
		"/business-allocation",
		BusinessAllocation,
	},
	// WeeklyResetLeaderboard
	Route{
		"SyncCronVNV3_WeeklyResetLeaderBoard",
		"POST",
		"/weekly-reset-leader-board",
		WeeklyResetLeaderBoard,
	},
	// AutoCalculateJourneyLeaderBoard
	Route{
		"SyncCronVNV3_AutoCalculateJourneyLeaderBoard",
		"POST",
		"/auto-calculate-journey-leader-board",
		AutoCalculateJourneyLeaderBoard,
	},
	//AutoMoveChatToHistory
	Route{
		"SyncCronVNV3_ExtendExpiredComboVoucher",
		"POST",
		"/extend-expired-combo-voucher",
		ExtendExpiredComboVoucher,
	},
	Route{
		"SyncCronVNV3_VoucherComboSubscriptionNotify",
		"POST",
		"/voucher-combo-subscription-notify",
		VoucherComboSubscriptionNotify,
	},
	Route{
		"SyncCronVNV3_AutoMoveChatToHistory",
		"POST",
		"/auto-move-chat-to-history",
		AutoMoveChatToHistory,
	},
	//AutoCheckIncentive
	Route{
		"SyncCronVNV3_AutoCheckIncentive",
		"POST",
		"/auto-check-incentive",
		AutoCheckIncentive,
	},
	Route{
		"SyncCronVNV3_UpdateTrustPoint",
		"POST",
		"/update-trust-point",
		UpdateTrustPoint,
	},
	Route{
		"SyncCronVNV3_GiveRewardTaskerDoneSpecialCampaign",
		"POST",
		"/give-reward-tasker-done-special-campaign",
		GiveRewardTaskerDoneSpecialCampaign,
	},
	Route{
		"SyncCronVNV3_SendMessageFavTaskerNotResponding",
		"POST",
		"/send-message-fav-tasker-not-responding",
		SendMessageFavTaskerNotResponding,
	},
	Route{
		"SyncCronVNV3_AutoCheckTaskerWorkingPlaces",
		"POST",
		"/auto-check-tasker-working-places",
		AutoCheckTaskerWorkingPlaces,
	},
	Route{
		"SyncCronVNV3_AutoPostNewFeedCommunity",
		"POST",
		"/auto-post-new-feed",
		AutoPostNewFeedCommunity,
	},
	Route{
		"SyncCronVNV3_UpdateMedal",
		"POST",
		"/update-medal",
		AutoUpgradeMedalForCommunityUser,
	},
	Route{
		"SyncCronVNV3_SendNotiToRemindTaskerResponseTaskForceTasker",
		"POST",
		"/send-noti-to-remind-tasker-response-task-force-tasker",
		SendNotiToRemindTaskerResponseTaskForceTasker,
	},
	Route{
		"SyncCronVNV3_AutoFindTaskersHaveStarCanDoTestTraining",
		"POST",
		"/auto-find-taskers-have-star-can-do-test-training",
		AutoFindTaskersHaveStarCanDoTestTraining,
	},
	Route{
		"SyncCronVNV3_AutoSendNotiAboutNewTestAndReviewToTasker",
		"POST",
		"/auto-send-noti-about-new-test-and-review-to-tasker",
		AutoSendNotiAboutNewTestAndReviewToTasker,
	},
	Route{
		"SyncCronVNV3_AutoSendNotiAboutTestDeadline",
		"POST",
		"/auto-send-noti-about-test-deadline",
		AutoSendNotiAboutTestDeadline,
	},
	Route{
		"AutoCancelTaskForceTasker",
		"POST",
		"/cancel-task-force-tasker",
		CancelTaskForceTasker,
	},
	Route{
		"AutoSendMessageBeforeCancelTaskForceTasker",
		"POST",
		"/send-message-before-cancel-task-force-tasker",
		SendMessageBeforeCancelTaskForceTasker,
	},
	Route{
		"AutoReportTasksInfo",
		"POST",
		"/auto-report-tasks-info",
		AutoReportTasksInfo,
	},
}
