package grpcBackend

import (
	"gitlab.com/btaskee/go-backend-api-service/grpcBackend"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"google.golang.org/grpc"
)

func ConnectGRPCBackend(endpoint string) (grpcBackend.BackendClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcBackend.NewBackendClient(connect)
	return client, connect, err
}
