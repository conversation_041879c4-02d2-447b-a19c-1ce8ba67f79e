---
apiVersion: v1
kind: Service
metadata:
  name: go-email-vn-v3
  namespace: kong
  labels:
    app: go-email-vn-v3
spec:
  ports:
    - port: 80
      targetPort: 18100
      name: http
    - port: 81
      targetPort: 18101
      name: grpc
  selector:
    app: go-email-vn-v3

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: go-email-vn-v3
  namespace: kong
  labels:
    app: go-email-vn-v3
spec:
  replicas: 1
  selector:
    matchLabels:
      app: go-email-vn-v3
  template:
    metadata:
      labels:
        app: go-email-vn-v3
        log-label: "go-services"
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: go-services
                operator: NotIn
                values:
                - group-high-memory
                - group-websocket
      containers:
        - name: go-email-vn-v3
          image: btaskeehub/go-email-service:vn-3.5.1
          resources:
            requests:
              cpu: 10m
              memory: "10Mi"
            limits:
              # https://home.robusta.dev/blog/stop-using-cpu-limits
              # cpu: 100m
              memory: "100Mi"
          env:
            - name: TZ
              value: "7"
            - name: APPLICATION_MODE
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: APPLICATION_MODE
            - name: SLACK_TOKEN_VN
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: SLACK_TOKEN_VN
            - name: MAILGUN_DOMAIN
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: MAILGUN_DOMAIN
            - name: MAILGUN_API_KEY
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: MAILGUN_API_KEY
            - name: EMAIL_VERIFY_URL
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: EMAIL_VERIFY_URL
            - name: EMAIL_VERIFY_API_KEY
              valueFrom:
                secretKeyRef:
                  name: secret-env
                  key: EMAIL_VERIFY_API_KEY
          imagePullPolicy: "IfNotPresent"
          ports:
            - containerPort: 80
              name: http
            - containerPort: 81
              name: grpc
      imagePullSecrets:
            - name: btaskeehub-registrykey
