package th

import (
	"context"
	"encoding/json"
	"errors"
	"strings"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcDataAccess"
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccess"
	modelDataAccess "gitlab.com/btaskee/go-services-model-v2/grpcmodel/dataAccess"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// ======================================== IS EXIST

func IsExistById(collectionName string, id string) (bool, error) {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return false, err
	}
	defer connect.Close()

	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Id: id}
	response, err := client.IsExistById(context.Background(), request)
	if err != nil {
		return false, err
	}
	return response.IsExist, nil
}

func IsExistByQuery(collectionName string, query interface{}) (bool, error) {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return false, err
	}
	defer connect.Close()

	byteQuery, _ := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)

	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery}
	response, err := client.IsExistByQuery(context.Background(), request)
	if err != nil {
		return false, err
	}
	return response.IsExist, nil
}

func IsExistByQueryWithCollation(collectionName string, query interface{}) (bool, error) {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return false, err
	}
	defer connect.Close()

	byteQuery, _ := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)

	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, IsCollation: true}
	response, err := client.IsExistByQuery(context.Background(), request)
	if err != nil {
		return false, err
	}
	return response.IsExist, nil
}

// ======================================== COUNT

func CountByQuery(collectionName string, query interface{}) (int64, error) {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return 0, err
	}
	defer connect.Close()

	byteQuery, _ := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)

	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery}
	response, err := client.CountByQuery(context.Background(), request)
	if err != nil {
		return 0, err
	}
	return response.Count, nil
}

// ======================================== GET ONE

func GetOneById(collectionName, id string, fields, result interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	b, err := bson.Marshal(fields)
	if err != nil {
		return err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Id: id, Fields: b}
	response, err := client.GetOneById(context.Background(), request)
	if err != nil {
		if strings.Contains(err.Error(), mongo.ErrNoDocuments.Error()) {
			return mongo.ErrNoDocuments
		}
		return err
	}
	return bson.UnmarshalExtJSONWithRegistry(globalDataAccess.REGISTRY, response.Result, true, result)
}

func GetOneByQuery(collectionName string, query interface{}, fields interface{}, result interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return err
	}
	byteFields, err := bson.Marshal(fields)
	if err != nil {
		return err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Fields: byteFields}
	response, err := client.GetOneByQuery(context.Background(), request)
	if err != nil {
		if strings.Contains(err.Error(), mongo.ErrNoDocuments.Error()) {
			return mongo.ErrNoDocuments
		}
		return err
	}
	return bson.UnmarshalExtJSONWithRegistry(globalDataAccess.REGISTRY, response.Result, true, result)
}

func GetOneByQueryWithCollation(collectionName string, query interface{}, fields interface{}, result interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return err
	}
	byteFields, err := bson.Marshal(fields)
	if err != nil {
		return err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Fields: byteFields, IsCollation: true}
	response, err := client.GetOneByQuery(context.Background(), request)
	if err != nil {
		if strings.Contains(err.Error(), mongo.ErrNoDocuments.Error()) {
			return mongo.ErrNoDocuments
		}
		return err
	}
	return bson.UnmarshalExtJSONWithRegistry(globalDataAccess.REGISTRY, response.Result, true, result)
}

func GetOneByQueryMap(collectionName string, query interface{}, fields interface{}) (map[string]interface{}, error) {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return nil, err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return nil, err
	}
	byteFields, err := bson.Marshal(fields)
	if err != nil {
		return nil, err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Fields: byteFields}
	response, err := client.GetOneByQueryMap(context.Background(), request)
	if err != nil {
		if strings.Contains(err.Error(), mongo.ErrNoDocuments.Error()) {
			return nil, mongo.ErrNoDocuments
		}
		return nil, err
	}
	var result map[string]interface{}
	err = bson.Unmarshal(response.Result, &result)
	result = globalDataAccess.ConvertData(result)
	return result, err
}

func GetOneByQuerySort(collectionName string, query interface{}, fields interface{}, sort interface{}, result interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return err
	}
	byteFields, err := bson.Marshal(fields)
	if err != nil {
		return err
	}
	byteSort, err := bson.Marshal(sort)
	if err != nil {
		return err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Fields: byteFields, Sort: byteSort}
	response, err := client.GetOneByQuerySort(context.Background(), request)
	if err != nil {
		if strings.Contains(err.Error(), mongo.ErrNoDocuments.Error()) {
			return mongo.ErrNoDocuments
		}
		return err
	}
	return bson.UnmarshalExtJSONWithRegistry(globalDataAccess.REGISTRY, response.Result, true, result)
}

// ======================================== GET ALL

func GetAllByQuery(collectionName string, query, fields, result interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return err
	}
	byteFields, err := bson.Marshal(fields)
	if err != nil {
		return err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Fields: byteFields}
	response, err := client.GetAllByQuery(context.Background(), request)
	if err != nil {
		if strings.Contains(err.Error(), mongo.ErrNoDocuments.Error()) {
			return mongo.ErrNoDocuments
		}
		return err
	}
	return bson.UnmarshalExtJSONWithRegistry(globalDataAccess.REGISTRY, response.Result, true, result)
}

func GetAllByQueryMap(collectionName string, query, fields interface{}) ([]map[string]interface{}, error) {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return nil, err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return nil, err
	}
	byteFields, err := bson.Marshal(fields)
	if err != nil {
		return nil, err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Fields: byteFields}
	response, err := client.GetAllByQuery(context.Background(), request)
	if err != nil {
		if strings.Contains(err.Error(), mongo.ErrNoDocuments.Error()) {
			return nil, mongo.ErrNoDocuments
		}
		return nil, err
	}
	var result []map[string]interface{}
	err = json.Unmarshal(response.Result, &result)
	return result, err
}

func GetAllByQueryPaging(collectionName string, query interface{}, fields interface{}, page, limit int64, result interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return err
	}
	byteFields, err := bson.Marshal(fields)
	if err != nil {
		return err
	}

	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Fields: byteFields, Page: page, Limit: limit}
	response, err := client.GetAllByQueryPaging(context.Background(), request)
	if err != nil {
		if strings.Contains(err.Error(), mongo.ErrNoDocuments.Error()) {
			return mongo.ErrNoDocuments
		}
		return err
	}
	return bson.UnmarshalExtJSONWithRegistry(globalDataAccess.REGISTRY, response.Result, true, result)
}

func GetAllByQuerySort(collectionName string, query, fields, sort interface{}, result interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return err
	}
	byteFields, err := bson.Marshal(fields)
	if err != nil {
		return err
	}
	byteSort, err := bson.Marshal(sort)
	if err != nil {
		return err
	}

	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Fields: byteFields, Sort: byteSort}
	response, err := client.GetAllByQuerySort(context.Background(), request)
	if err != nil {
		if strings.Contains(err.Error(), mongo.ErrNoDocuments.Error()) {
			return mongo.ErrNoDocuments
		}
		return err
	}
	return bson.UnmarshalExtJSONWithRegistry(globalDataAccess.REGISTRY, response.Result, true, result)
}

func GetAllByQuerySortMap(collectionName string, query, fields, sort interface{}) ([]map[string]interface{}, error) {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return nil, err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return nil, err
	}
	byteFields, err := bson.Marshal(fields)
	if err != nil {
		return nil, err
	}
	byteSort, err := bson.Marshal(sort)
	if err != nil {
		return nil, err
	}

	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Fields: byteFields, Sort: byteSort}
	response, err := client.GetAllByQuerySort(context.Background(), request)
	if err != nil {
		if strings.Contains(err.Error(), mongo.ErrNoDocuments.Error()) {
			return nil, mongo.ErrNoDocuments
		}
		return nil, err
	}
	var result []map[string]interface{}
	err = json.Unmarshal(response.Result, &result)
	return result, err
}

func GetAllByQueryPagingSort(collectionName string, query interface{}, fields interface{}, page, limit int64, sort interface{}, result interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return err
	}
	byteFields, err := bson.Marshal(fields)
	if err != nil {
		return err
	}
	byteSort, err := bson.Marshal(sort)
	if err != nil {
		return err
	}

	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Fields: byteFields, Sort: byteSort, Page: page, Limit: limit}
	response, err := client.GetAllByQueryPagingSort(context.Background(), request)
	if err != nil {
		if strings.Contains(err.Error(), mongo.ErrNoDocuments.Error()) {
			return mongo.ErrNoDocuments
		}
		return err
	}
	return bson.UnmarshalExtJSONWithRegistry(globalDataAccess.REGISTRY, response.Result, true, result)
}

func GetAllByQueryPagingSortMap(collectionName string, query interface{}, fields interface{}, page, limit int64, sort interface{}) ([]map[string]interface{}, error) {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return nil, err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return nil, err
	}
	byteFields, err := bson.Marshal(fields)
	if err != nil {
		return nil, err
	}
	byteSort, err := bson.Marshal(sort)
	if err != nil {
		return nil, err
	}

	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Fields: byteFields, Sort: byteSort, Page: page, Limit: limit}
	response, err := client.GetAllByQueryPagingSort(context.Background(), request)
	if err != nil {
		if strings.Contains(err.Error(), mongo.ErrNoDocuments.Error()) {
			return nil, mongo.ErrNoDocuments
		}
		return nil, err
	}
	var result []map[string]interface{}
	err = json.Unmarshal(response.Result, &result)
	return result, err
}

func GetAllByQueryLimitSort(collectionName string, query interface{}, fields interface{}, limit int64, sort interface{}, result interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return err
	}
	byteFields, err := bson.Marshal(fields)
	if err != nil {
		return err
	}
	byteSort, err := bson.Marshal(sort)
	if err != nil {
		return err
	}

	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Fields: byteFields, Sort: byteSort, Limit: limit}
	response, err := client.GetAllByQueryLimitSort(context.Background(), request)
	if err != nil {
		if strings.Contains(err.Error(), mongo.ErrNoDocuments.Error()) {
			return mongo.ErrNoDocuments
		}
		return err
	}
	return bson.UnmarshalExtJSONWithRegistry(globalDataAccess.REGISTRY, response.Result, true, result)
}

func GetDistinctByQuery(collectionName string, distinctFieldName string, query interface{}) ([]interface{}, error) {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return nil, err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return nil, err
	}

	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, DistinctFieldName: distinctFieldName, Query: byteQuery}
	response, err := client.GetDistinctByQuery(context.Background(), request)
	if err != nil {
		return nil, err
	}
	var result []interface{}
	err = json.Unmarshal(response.Result, &result)
	return result, err
}

func Aggregate(collectionName string, query []bson.M, result interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	if len(query) == 0 {
		return errors.New("query is empty")
	}

	byteQuery, _ := globalDataAccess.EncodeBSONList(query)
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery}
	response, err := client.Aggregate(context.Background(), request)
	if err != nil {
		if strings.Contains(err.Error(), mongo.ErrNoDocuments.Error()) {
			return mongo.ErrNoDocuments
		}
		return err
	}
	return bson.UnmarshalExtJSONWithRegistry(globalDataAccess.REGISTRY, response.Result, true, result)
}

func GetAllByQuerySkipLimitSort(collectionName string, query interface{}, fields interface{}, skip, limit int64, sort interface{}, result interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return err
	}
	byteFields, err := bson.Marshal(fields)
	if err != nil {
		return err
	}
	byteSort, err := bson.Marshal(sort)
	if err != nil {
		return err
	}

	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Fields: byteFields, Sort: byteSort, Skip: skip, Limit: limit}
	response, err := client.GetAllByQuerySkipLimitSort(context.Background(), request)
	if err != nil {
		if strings.Contains(err.Error(), mongo.ErrNoDocuments.Error()) {
			return mongo.ErrNoDocuments
		}
		return err
	}
	return bson.UnmarshalExtJSONWithRegistry(globalDataAccess.REGISTRY, response.Result, true, result)
}
