syntax = "proto3";
package grpcPricingTH;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPricingTH";

import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingrequest/pricing-request.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse/pricing-response.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionRequest/pricing-subscription-request.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionResponse/pricing-subscription-response.proto";

service PricingTH {
  rpc GetPricingAirConditioner (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/air-conditioner"
      body: "*"
    };
  }
  rpc GetPricingHomeCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/home-cleaning"
      body: "*"
    };
  }
  rpc GetPricingSubscription (pricingSubscriptionRequest.PricingSubscriptionRequest) returns (pricingSubscriptionResponse.PricingSubscriptionResponse) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/subscription"
      body: "*"
    };
  }
  rpc GetPricingDeepCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/deep-cleaning"
      body: "*"
    };
  }
  rpc GetPricingDisinfection (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/disinfection"
      body: "*"
    };
  }
  rpc GetPricingSofaTH (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/sofa"
      body: "*"
    };
  }
  rpc GetPricingOfficeCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {}
  rpc APIGetPricingOfficeCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/office-cleaning"
      body: "*"
    };
  }
  rpc ReCalculatePricing (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/re-calculate"
      body: "*"
    };
  }
  rpc GetPricingSubscriptionOfficeCleaning (pricingSubscriptionRequest.PricingSubscriptionRequest) returns (pricingSubscriptionResponse.PricingSubscriptionResponse) {}
  rpc APIGetPricingSubscriptionOfficeCleaning (pricingSubscriptionRequest.PricingSubscriptionRequest) returns (pricingSubscriptionResponse.PricingSubscriptionResponse) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/subscription-office-cleaning"
      body: "*"
    };
  }
  rpc GetPricingHomeMoving (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/home-moving"
      body: "*"
    };
  }
  rpc RePricingHomeMoving (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/re-home-moving"
      body: "*"
    };
  }
  rpc GetPricingMassage (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/massage"
      body: "*"
    };
  }
  rpc RePricingMassage (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/re-pricing-massage"
      body: "*"
    };
  }
  rpc ReCalculatePricingForIncreaseDuration (pricingrequest.PricingIncreaseDurationRequest) returns (pricingresponse.CostResult) {}
  rpc APIGetPricingTaskDateOptions (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/task-date-options"
      body: "*"
    };
  }
  rpc GetPricingPatientCare (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/patient-care"
      body: "*"
    };
  }
  rpc GetPricingElderlyCare (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/elderly-care"
      body: "*"
    };
  }
  rpc GetPricingSubscriptionPatientCare (pricingSubscriptionRequest.PricingSubscriptionRequest) returns (pricingSubscriptionResponse.PricingSubscriptionResponse) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/subscription-patient-care"
      body: "*"
    };
  }
  rpc GetPricingSubscriptionElderlyCare (pricingSubscriptionRequest.PricingSubscriptionRequest) returns (pricingSubscriptionResponse.PricingSubscriptionResponse) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/subscription-elderly-care"
      body: "*"
    };
  }
  rpc GetPricingAirConditionerV2 (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/air-conditioner-v2"
      body: "*"
    };
  }
  rpc GetPricingChildCare (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-th/child-care"
      body: "*"
    };
  }
}