package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	"go.mongodb.org/mongo-driver/bson"
)

func TestSendNotiToRemindTaskerResponseTaskForceTasker(t *testing.T) {
	//send noti for task create after 1 hour
	t.Run("1", func(t *testing.T) {
		ResetData()
		apiURL := apiSyncCron + "/send-noti-to-remind-tasker-response-task-force-tasker"

		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 01",
			}, {
				"phone":     "0823456789",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker 01",
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		date := now.AddDate(0, 0, 7)
		createdAtTime := now.Add(-1*time.Hour - 20*time.Minute)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"forceTasker": map[string]interface{}{
					"taskerId": "0823456789",
					"name":     "Tasker",
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				// Check notification send message
				var chatNotification *notification.Notification
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"taskId": taskIds[0]}, bson.M{}, &chatNotification)
				So(chatNotification, ShouldNotBeNil)
				So(chatNotification.UserId, ShouldEqual, "0823456789")
				So(chatNotification.TaskId, ShouldEqual, taskIds[0])
				So(chatNotification.Type, ShouldEqual, 28)
				So(chatNotification.Description, ShouldEqual, "Công việc khách hàng đặt riêng cho bạn")
				So(chatNotification.NavigateTo, ShouldEqual, globalConstant.PAYLOAD_NAVIGATE_TO_TASK_DETAIL)
			})
		})
	})
	//task create before 1 hour won't be sent noti
	t.Run("2", func(t *testing.T) {
		ResetData()
		apiURL := apiSyncCron + "/send-noti-to-remind-tasker-response-task-force-tasker"

		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 01",
			}, {
				"phone":     "0823456789",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker 01",
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		date := now.AddDate(0, 0, 7)
		createdAtTime := now.Add(-20 * time.Minute)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"forceTasker": map[string]interface{}{
					"taskerId": "0823456789",
					"name":     "Tasker",
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				// Check notification send message
				var chatNotification *notification.Notification
				globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"taskId": taskIds[0]}, bson.M{}, &chatNotification)
				So(chatNotification, ShouldBeNil)
			})
		})
	})
}
