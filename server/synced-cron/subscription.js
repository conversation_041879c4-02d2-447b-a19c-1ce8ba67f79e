import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import _ from 'lodash';
import { History } from '/server/lib/collections';
import {
  revertTimeZone,
  sendNotificationToAsker,
  sendTaskToTasker,
  sendEmailSubscription,
  roundMoney,
  postToSlack,
  checkConflictTask,
  trackingScheduleStatus,
  changeTimeZone,
  fillTaskDescription,
  sendNotificationByIds,
} from '/server/helper-function';

const mapServiceName = (subServiceName) => {
  var serviceName = subServiceName;
  switch (subServiceName) {
    case "CLEANING_SUBSCRIPTION":
      serviceName = "CLEANING";
      break;
    case "ELDERLY_CARE_SUBSCRIPTION":
      serviceName = "ELDERLY_CARE";
      break;
    case "PATIENT_CARE_SUBSCRIPTION":
      serviceName = "PATIENT_CARE";
      break;
    case "CHILD_CARE_SUBSCRIPTION":
      serviceName = "CHILD_CARE";
      break;
    default:
      break;
  }
  return serviceName;
};

const priceOfTaskInSubscription = (subs, date) => {
  var remainRate = 1 - subs.discount;
  if (!remainRate || remainRate === 0) {
    remainRate = 1;
  }

  if (subs.costDetail && subs.costDetail.pricing && subs.costDetail.pricing.length > 0) {
    const foundPrice = subs.costDetail.pricing.find((eachPrice) => {
      eachPrice.date = new Date(eachPrice.date);
      return eachPrice.date.getFullYear() === date.getFullYear()
        && eachPrice.date.getMonth() === date.getMonth()
        && eachPrice.date.getDate() === date.getDate()
        && eachPrice.date.getHours() === date.getHours()
        && eachPrice.date.getMinutes() === date.getMinutes();
    });
    if (foundPrice) {
      if (remainRate > 0 && remainRate < 1) {
        foundPrice.costDetail.cost = roundMoney(foundPrice.costDetail.cost * remainRate);
        foundPrice.costDetail.finalCost = roundMoney(foundPrice.costDetail.finalCost * remainRate);
      }
      return foundPrice;
    }
  } else if (subs.pricing && subs.pricing.length > 0) {
    const foundPrice = subs.pricing.find((eachPrice) => {
      eachPrice.date = new Date(eachPrice.date);
      return eachPrice.date.getFullYear() === date.getFullYear()
        && eachPrice.date.getMonth() === date.getMonth()
        && eachPrice.date.getDate() === date.getDate()
        && eachPrice.date.getHours() === date.getHours()
        && eachPrice.date.getMinutes() === date.getMinutes();
    });
    if (foundPrice) {
      if (remainRate > 0 && remainRate < 1) {
        foundPrice.costDetail.cost = roundMoney(foundPrice.costDetail.cost * remainRate);
        foundPrice.costDetail.reason.subscriptionDiscount = subs.discount;
      }
      return foundPrice;
    }
  }

  const price = roundMoney(subs.price / subs.schedule.length);
  return {
    date: date,
    duration: subs.duration,
    costDetail: {
      baseCost: roundMoney(price / remainRate),
      cost: price,
      finalCost: price,
      duration: subs.duration,
      // reason: subs.discount > 0 ? {subscriptionDiscount: subs.discount} : {},
    },
  };
};

canTaskerAccept = (tasker) => {
  if (tasker.status !== 'ACTIVE' && tasker.status !== 'LOCKED' ) {
    return false;
  }
  // Tasker locked with not come reason >= 3 times
  if ( tasker.status === 'LOCKED' && tasker.notComeLockNumber > 2 ) {
    return false;
  }
  // Tasker is locked with other reason
  if ( tasker.status === 'LOCKED' && !tasker.notComeLockNumber ) {
    return false;
  }
  
  return true;
};

// Post subscription task (Move from runSubscription source code)
const postSubscriptionTask = (subs, nextDate, isoCode) => {
  const createdAt = new Date();
  let task = {
    serviceId: subs.taskServiceId || subs.serviceId,
    date: nextDate,
    duration: subs.duration,
    address: subs.address,
    lat: subs.location.lat,
    lng: subs.location.lng,
    askerId: subs.userId,
    status: 'POSTED',
    createdAt: createdAt,
    rated: false,
    subscriptionId: subs._id,
    taskPlace: subs.taskPlace,
    autoChooseTasker: true,
    payment: {
      method: 'BANK_TRANSFER',
    },
    description: subs.description || '',
    isoCode: isoCode,
    countryCode: subs.countryCode || '+84',
  };
  if (subs.serviceName) {
    task.serviceName = mapServiceName(subs.serviceName);
  }
  if (subs.taskNote) {
    task.taskNote = subs.taskNote;
  }
  if ( subs.cookingDetail ) {
    task.cookingDetail = subs.cookingDetail;
    task.cookingDetail.eatingTime = moment(task.date).add(task.duration, 'hours').toDate();
  }
  if (subs.detailChildCare) {
    task.detailChildCare = subs.detailChildCare;
  }
  if ( isoCode === 'TH' ) {
    task.originCurrency = { sign: '฿', code: 'THB' };
  } else {
    task.originCurrency = { sign: '₫', code: 'VND' };
  }
  const priceOfTask = priceOfTaskInSubscription(subs, nextDate);

  task.cost = priceOfTask.costDetail.cost;
  // Not used
  // task.pricing = {
  //   updatedAt: new Date(),
  //   costDetail: priceOfTask.costDetail,
  // };
  var taskPrice = task.cost;
  if ( priceOfTask && priceOfTask.costDetail ) {
    task.costDetail = priceOfTask.costDetail;
    if ( !task.costDetail.finalCost ) {
      task.costDetail.finalCost = task.costDetail.cost;
    }
    taskPrice = task.costDetail.finalCost;
  }
  
  
  // Update information of asker to task
  const user = Meteor.appCollection.users.findOne({_id: subs.userId}, {fields: {phone: 1, name: 1, taskNote: 1, blackList: 1, language: 1, favouriteTasker: 1, isBlacklist: 1}});
  if ( user && user.blackList && user.blackList.length > 0 ) {
    task.blackList = user.blackList;
  }
  task.phone = user.phone;
  task.contactName = user.name;
  if (user.taskNote) {
    task.taskNote = user.taskNote;
  }
  // Fill task description
  if ( task && (!task.description || task.description === '') ) {
    // Find description from old tasks
    const description = fillTaskDescription(task.phone, isoCode);
    if ( description ) {
      task.description = description;
    }
  }
  if ( subs.homeType ) {
    task.homeType = subs.homeType;
  }
  if (subs.isPremium) {
    task.isPremium = subs.isPremium;
  }
  if (subs.isEco) {
    task.isEco = subs.isEco;
  }
  if (subs.addons && subs.addons.length > 0) {
    task.addons = subs.addons;
  }
  if (subs.pet && subs.pet.length > 0) {
    task.pet = subs.pet;
  }
  if ( subs.serviceText ) {
    task.serviceText = subs.serviceText;
  } else {
    const service = Meteor.appCollection.Service.findOne({ _id: subs.serviceId }, {fields: {text: 1}});
    if ( service ) {
      task.serviceText = service.text;
    }
  }
  var collectionTask = Meteor.appCollection.VN_Task;
  var collectionSub = Meteor.appCollection.VN_Subscription;
  if (isoCode === 'TH') {
    collectionTask = Meteor.appCollection.TH_Task;
    collectionSub = Meteor.appCollection.TH_Subscription;
  }
  // Insert task
  const newTaskId = collectionTask.insert(task);
  // Update last posted task of user
  Meteor.appCollection.users.update({ _id: subs.userId }, { $set: { lastPostedTask: task.createdAt } });
  // Update history of this subscription
  collectionSub.update({_id: subs._id}, {$push: {taskIds: newTaskId, history: nextDate}});
  
  // Gửi thông báo cho Asker
  sendNotificationToAsker(newTaskId, subs.userId, user.language, task.serviceText, isoCode);

  if (subs.forceAcceptTaskerId) {
    const taskerId = subs.forceAcceptTaskerId;
    const tasker = Meteor.appCollection.users.findOne({
      _id: taskerId,
      status: {$in: ['ACTIVE', 'LOCKED']},
    }, {fields: {avatar: 1, name: 1, avgRating: 1, taskDone: 1, phone: 1, status: 1, notComeLockNumber: 1}});
    
    if ( tasker && canTaskerAccept(tasker)) {
      var isConflict = false;
      // Check conflict working time with other task of this Tasker.
      collectionTask.find({status: 'CONFIRMED', 'acceptedTasker.taskerId': taskerId}).forEach((confirmedTask) => {
        if (checkConflictTask(task, confirmedTask, 14)) {
          isConflict = true;
          return;
        }
      });
      if (isConflict === false) {
        // force tasker to accept this task
        collectionTask.update({ _id: newTaskId }, { $set: {
          acceptedTasker: [{
            taskerId,
            avatar: tasker.avatar,
            name: tasker.name,
            avgRating: tasker.avgRating || 0,
            taskDone: tasker.taskDone || 0,
            acceptedAt: new Date()
          }],
          status: 'CONFIRMED',
          updatedAt: new Date(),
        } });
        const data = {
          title: {
            vi: i18n.getTranslation('NOTIFICATION_MESSAGE_ASKER_CONFIRM', { _locale: 'vi' }),
            th: i18n.getTranslation('NOTIFICATION_MESSAGE_ASKER_CONFIRM', { _locale: 'th' }),
            ko: i18n.getTranslation('NOTIFICATION_MESSAGE_ASKER_CONFIRM', { _locale: 'ko' }),
            en: i18n.getTranslation('NOTIFICATION_MESSAGE_ASKER_CONFIRM', { _locale: 'en' }),
          },
          text: {
            vi: i18n.getTranslation('NOTIFICATION_MESSAGE_ASKER_CONFIRM', { _locale: 'vi' }),
            th: i18n.getTranslation('NOTIFICATION_MESSAGE_ASKER_CONFIRM', { _locale: 'th' }),
            ko: i18n.getTranslation('NOTIFICATION_MESSAGE_ASKER_CONFIRM', { _locale: 'ko' }),
            en: i18n.getTranslation('NOTIFICATION_MESSAGE_ASKER_CONFIRM', { _locale: 'en' }),
          },
          payload: {
            type: 2,
            taskId: newTaskId,
            navigateTo: "TaskDetail",
          },
        };
        sendNotificationByIds([taskerId], data, {});
        if ( tasker.status !== 'ACTIVE' ) {
          var channel = "vn-sub-schedule";
          var textAlert = `CS lưu ý: Tasker ${tasker.phone} status ${tasker.status} đã được thêm vào subscription task (Asker: ${task.phone})`;
          if (subs.isoCode === 'TH') {
            channel = "th-sub-schedule";
            textAlert = `SUBSCRIPTION: Tasker ${tasker.phone}, status ${tasker.status} has been added to subscription (Asker: ${task.phone})`;
          }
          postToSlack({
            channel: channel,
            text: textAlert,
          });
        }
      } else { // Conflict with other task
        var channel = "vn-sub-schedule";
        var message = `Cần CS xử lý: Công việc của Subscription đã đăng và KHÔNG confirm được do Tasker ${tasker.phone} - ${tasker.name} trùng giờ làm việc. CV của Asker: ${user.phone}`;
        if (subs.isoCode === 'TH') {
          channel = "th-sub-schedule";
          message = `SUBSCRIPTION: The task in subscription is conflicted with the other task. Tasker: ${tasker.phone}, Asker: ${user.phone}`;
        }
        postToSlack({
          channel: channel,
          text: message,
        });
      }
    } else { // Tasker not found or not ACTIVE
      postToSlack({
        channel: 'vn-sub-schedule',
        text: `Cần CS xử lý: Công việc của Subscription đã đăng và KHÔNG confirm được do Tasker ${tasker.phone} - ${tasker.name} đã bị khóa. CV của Asker: ${user.phone}`,
      });
    }
  } else {
    // Gửi task cho Tasker (đã có gửi thông báo cho Tasker)
    if (!user.isBlacklist) {
      sendTaskToTasker(newTaskId, task.serviceId, user.favouriteTasker, isoCode);
    }
  }
  // Check this subscription if it's finished or not
  const currentSubs = collectionSub.findOne({_id: subs._id}, {fields: {schedule: 1, history: 1}});
  const lastDateSchedule = moment(currentSubs.schedule[currentSubs.schedule.length - 1]).format('DD/MM/YYYY');
  const lastDateHistory = moment(currentSubs.history[currentSubs.history.length - 1]).format('DD/MM/YYYY');
  if (lastDateSchedule === lastDateHistory) {
    collectionSub.update({_id: subs._id}, {$set: {status: 'DONE'}});
    collectionTask.update({_id: newTaskId}, {$set: {finalSubscriptionTask: true}});
  }
  return taskPrice;
};

const runTHSubscription = () => {
  var messageStatus = null;
  const subsSettings = Meteor.appCollection.TH_SubscriptionSettings.findOne({}, {fields: {postTaskBefore: 1, renewBefore: 1, expiredDays: 1}});
  const postTaskBefore = subsSettings && subsSettings.postTaskBefore ? subsSettings.postTaskBefore : 2;
  Meteor.appCollection.TH_Subscription.find({
    serviceName: 'CLEANING_SUBSCRIPTION',
    status: {$in: ['NEW', 'ACTIVE']},
    isoCode: 'TH',
    }).forEach((subs) => {
    try {
      if (subs.status === 'NEW') {  // user have not paid
        const expiredDays = subsSettings.expiredDays || 2;
        const expiredDate = moment(subs.startDate).subtract(expiredDays, 'd').toDate();
        if (moment(expiredDate).format('YYYY/MM/DD') <= moment().format('YYYY/MM/DD') ||
            moment(subs.startDate).format('YYYY/MM/DD') <= moment().format('YYYY/MM/DD')) {
          Meteor.appCollection.TH_Subscription.update({_id: subs._id}, {$set: {status: 'EXPIRED'}});
          Meteor.appCollection.TH_PurchaseOrder.update({subscriptionId: subs._id}, {$set: {status: 'EXPIRED'}});
        }
      } else {
        // Check this date was created or not
        const currentDate = new Date();
        var nextDate = subs.schedule[0];
        var taskPricePosted = 0;
        for (var i = 0; i < subs.schedule.length; i++) {
          nextDate = subs.schedule[i];
          // Check the date that synced cron will post task
          const date = moment(nextDate).subtract(postTaskBefore, 'd').toDate();
          // If the next schedule date is over limit of days post before
          // Break the for loop.
          if (date.getTime() > currentDate.getTime()) {
            break;
          }
          const isHistoryDate = _.get(subs, 'history', []).find((item) => {
            if (moment(item).format('DD/MM/YYYY') === moment(nextDate).format('DD/MM/YYYY') ||
                nextDate.getTime() <= currentDate.getTime()) {
              return true;
            }
          });
          // If isHistoryDate exists: the next schedule date in the past or posted in history
          // If isHistoryDate is null: the next schedule date is valid and not posted yet.
          if (!isHistoryDate && nextDate.getTime() > currentDate.getTime()) {
            // Delay 1s
            Meteor.wrapAsync(cb => { Meteor.setTimeout(() => { cb(); }, 1000); })();
            // Call post subscription task. Many times for 1 subscription
            const taskPrice = postSubscriptionTask(subs, nextDate, "TH");
            taskPricePosted = taskPricePosted + taskPrice;
          }
        }
        if (subs.costRemaining && taskPricePosted > 0) {
          Meteor.appCollection.TH_Subscription.update({_id: subs._id}, {$inc: {costRemaining: -taskPricePosted}});
        }


        if (!subs.isSentRenewEmail) {
          // Renew at last posting of susbscription. 8 days by default
          const lastScheduleDate = new Date(subs.schedule[subs.schedule.length - 1]);
          const dateRenew = moment(lastScheduleDate).subtract(subsSettings.postTaskBefore || 8, 'd').toDate();
          if (dateRenew.getTime() <= currentDate.getTime()) {
            const endSubsDate = changeTimeZone(lastScheduleDate);
            const data = {
              title: {
                vi: i18n.getTranslation('SUBSCRIPTION_RENEW_TITLE', { _locale: 'vi' }),
                th: i18n.getTranslation('SUBSCRIPTION_RENEW_TITLE', { _locale: 'th' }),
                ko: i18n.getTranslation('SUBSCRIPTION_RENEW_TITLE', { _locale: 'ko' }),
                en: i18n.getTranslation('SUBSCRIPTION_RENEW_TITLE', { _locale: 'en' }),
              },
              text: {
                vi: i18n.getTranslation('SUBSCRIPTION_RENEW_CONTENT', { _locale: 'vi', date: moment(endSubsDate).format('DD/MM/YYYY') }),
                th: i18n.getTranslation('SUBSCRIPTION_RENEW_CONTENT', { _locale: 'th', date: moment(endSubsDate).format('DD/MM/YYYY') }),
                ko: i18n.getTranslation('SUBSCRIPTION_RENEW_CONTENT', { _locale: 'ko', date: moment(endSubsDate).format('DD/MM/YYYY') }),
                en: i18n.getTranslation('SUBSCRIPTION_RENEW_CONTENT', { _locale: 'en', date: moment(endSubsDate).format('DD/MM/YYYY') }),
              },
              payload: {
                type: 27,
                taskId: subs._id,
              },
            };
            sendNotificationByIds([subs.userId], data, {});
            sendEmailSubscription(subs.userId, subs._id);
            Meteor.appCollection.TH_Subscription.update({_id: subs._id}, {$set: {isSentRenewEmail: true}});
          }
        }
      }
    } catch (e) {
      // Loggly subscription error here
      messageStatus = e;
      postToSlack({
        channel: 'cron-jobs-alert',
        text: `Workflow: There is an error on subscription: subscriptionId: ${subs._id} error: ${messageStatus}`,
      });
      
      
    }
  });
  // Tracking
  trackingScheduleStatus({name: 'Subscription', nextRunAt: SyncedCron.nextScheduledAtDate('GenerateSubscription'), message: messageStatus});
}

const runSubscription = () => {
	console.log("Start runSubscription ...");
  var messageStatus = null;
  const subsSettings = Meteor.appCollection.SubscriptionSettings.findOne({}, {fields: {postTaskBefore: 1, renewBefore: 1, expiredDays: 1}});
  const postTaskBefore = subsSettings && subsSettings.postTaskBefore ? subsSettings.postTaskBefore : 2;
  Meteor.appCollection.VN_Subscription.find({
    status: {$in: ['NEW', 'ACTIVE']},
    isoCode: 'VN',
    serviceName: {$ne: "OFFICE_CLEANING"},
    }).forEach((subs) => {
    try {
      if (subs.status === 'NEW') {  // user have not paid
        const expiredDays = subsSettings.expiredDays || 2;
        const expiredDate = moment(subs.startDate).subtract(expiredDays, 'd').toDate();
        if (moment(expiredDate).format('YYYY/MM/DD') <= moment().format('YYYY/MM/DD') ||
            moment(subs.startDate).format('YYYY/MM/DD') <= moment().format('YYYY/MM/DD')) {
          Meteor.appCollection.VN_Subscription.update({_id: subs._id}, {$set: {status: 'EXPIRED'}});
          Meteor.appCollection.VN_PurchaseOrder.update({subscriptionId: subs._id}, {$set: {status: 'EXPIRED'}});
        }
      } else {
        // Check this date was created or not
        const currentDate = new Date();
        var nextDate = subs.schedule[0];
        var taskPricePosted = 0;
        for (var i = 0; i < subs.schedule.length; i++) {
          nextDate = subs.schedule[i];
          // Check the date that synced cron will post task
          const date = moment(nextDate).subtract(postTaskBefore, 'd').toDate();
          // If the next schedule date is over limit of days post before
          // Break the for loop.
          if (date.getTime() > currentDate.getTime()) {
            break;
          }
          const isHistoryDate = _.get(subs, 'history', []).find((item) => {
            if (moment(item).format('DD/MM/YYYY') === moment(nextDate).format('DD/MM/YYYY') ||
                nextDate.getTime() <= currentDate.getTime()) {
              return true;
            }
          });
          // If isHistoryDate exists: the next schedule date in the past or posted in history
          // If isHistoryDate is null: the next schedule date is valid and not posted yet.
          if (!isHistoryDate && nextDate.getTime() > currentDate.getTime()) {
            // Delay 1s
            Meteor.wrapAsync(cb => { Meteor.setTimeout(() => { cb(); }, 1000); })();
            // Call post subscription task. Many times for 1 subscription
            const taskPrice = postSubscriptionTask(subs, nextDate, "VN");
            taskPricePosted = taskPricePosted + taskPrice;
          }
        }
        if (subs.costRemaining && taskPricePosted > 0) {
          Meteor.appCollection.VN_Subscription.update({_id: subs._id}, {$inc: {costRemaining: -taskPricePosted}});
        }


        if (!subs.isSentRenewEmail) {
          // Renew at last posting of susbscription. 8 days by default
          const lastScheduleDate = new Date(subs.schedule[subs.schedule.length - 1]);
          const dateRenew = moment(lastScheduleDate).subtract(subsSettings.postTaskBefore || 8, 'd').toDate();
          if (dateRenew.getTime() <= currentDate.getTime()) {
            const endSubsDate = changeTimeZone(lastScheduleDate);
            const data = {
              title: {
                vi: i18n.getTranslation('SUBSCRIPTION_RENEW_TITLE', { _locale: 'vi' }),
                th: i18n.getTranslation('SUBSCRIPTION_RENEW_TITLE', { _locale: 'th' }),
                ko: i18n.getTranslation('SUBSCRIPTION_RENEW_TITLE', { _locale: 'ko' }),
                en: i18n.getTranslation('SUBSCRIPTION_RENEW_TITLE', { _locale: 'en' }),
              },
              text: {
                vi: i18n.getTranslation('SUBSCRIPTION_RENEW_CONTENT', { _locale: 'vi', date: moment(endSubsDate).format('DD/MM/YYYY') }),
                th: i18n.getTranslation('SUBSCRIPTION_RENEW_CONTENT', { _locale: 'th', date: moment(endSubsDate).format('DD/MM/YYYY') }),
                ko: i18n.getTranslation('SUBSCRIPTION_RENEW_CONTENT', { _locale: 'ko', date: moment(endSubsDate).format('DD/MM/YYYY') }),
                en: i18n.getTranslation('SUBSCRIPTION_RENEW_CONTENT', { _locale: 'en', date: moment(endSubsDate).format('DD/MM/YYYY') }),
              },
              payload: {
                type: 27,
                taskId: subs._id,
              },
            };
            sendNotificationByIds([subs.userId], data, {});
            sendEmailSubscription(subs.userId, subs._id);
            Meteor.appCollection.VN_Subscription.update({_id: subs._id}, {$set: {isSentRenewEmail: true}});
          }
        }
      }
    } catch (e) {
      // Loggly subscription error here
      messageStatus = e;
      postToSlack({
        channel: 'cron-jobs-alert',
        text: `Workflow: There is an error on subscription: subscriptionId: ${subs._id} error: ${messageStatus}`,
      });
      
      
    }
  });
	console.log("Finished runSubscription ");
  // Tracking
  trackingScheduleStatus({name: 'Subscription', nextRunAt: SyncedCron.nextScheduledAtDate('GenerateSubscription'), message: messageStatus});
};

  Meteor.methods({
    generateSubscription() {
      SyncedCron.add({
        name: 'GenerateSubscription',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 21)).getHours();
          // Run at 21:10 every day with timezone GMT+7.
          return parser.cron(`0 10 ${hourInTimeZoneServer} 1/1 * ? *`, true);
        },
        job() {
          runSubscription();
          runTHSubscription();
        },
      });
	    console.log("Added generateSubscription");
    },
    runSubscriptionNow() {
      runSubscription();
      runTHSubscription();
    },
  });
