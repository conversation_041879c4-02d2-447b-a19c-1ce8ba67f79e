package testing

import (
	"context"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/service"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
)

func TestSendVerifyEmail(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		Convey("Given a request to check send verify email", t, func() {
			Convey("Check the response if request is nil", func() {
				var req *modelUser.Users
				s := service.Server{}
				res, _ := s.SendVerifyEmail(context.Background(), req)

				So(res, ShouldNotBeNil)
				So(res.Message, ShouldEqual, lib.ERROR_EMAIL_VERIFY_USER_EMPTY)
			})

			Convey("Check the response if request.Emails is nil", func() {
				req := &modelUser.Users{
					Emails: nil,
				}
				s := service.Server{}
				res, _ := s.SendVerifyEmail(context.Background(), req)

				So(res, ShouldNotBeNil)
				So(res.Message, ShouldEqual, lib.ERROR_EMAIL_VERIFY_USER_EMPTY)
			})

			Convey("Check the response if request.Emails len == 0", func() {
				req := &modelUser.Users{
					Emails: []*modelUser.UsersEmail{},
				}
				s := service.Server{}
				res, _ := s.SendVerifyEmail(context.Background(), req)

				So(res, ShouldNotBeNil)
				So(res.Message, ShouldEqual, lib.ERROR_EMAIL_VERIFY_USER_EMPTY)
			})

			Convey("Check the response if email invalid", func() {
				req := &modelUser.Users{
					Emails: []*modelUser.UsersEmail{
						{
							Address: "tbgnoc",
						},
					},
				}
				s := service.Server{}
				res, _ := s.SendVerifyEmail(context.Background(), req)

				So(res, ShouldNotBeNil)
				So(res.Message, ShouldEqual, lib.ERROR_EMAIL_INVALID)
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		Convey("Given a request to check send verify email", t, func() {
			Convey("Check the response if content is nil", func() {
				req := &modelUser.Users{
					Emails: []*modelUser.UsersEmail{
						{
							Address: "<EMAIL>",
						},
					},
					Name:     "ngoc",
					Language: "vi",
				}
				s := service.Server{}
				res, _ := s.SendVerifyEmail(context.Background(), req)

				So(res, ShouldNotBeNil)
				So(res.Message, ShouldEqual, lib.ERROR_CONTENT_NIL)
			})
		})
	})

}
