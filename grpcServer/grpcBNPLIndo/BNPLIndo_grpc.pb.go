// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcBNPLIndo

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	taskerBNPLRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerBNPLRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// BNPLIndoClient is the client API for BNPLIndo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BNPLIndoClient interface {
	ChargeTaskerBNPLFee(ctx context.Context, in *taskerBNPLRequest.TaskerBNPLRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type bNPLIndoClient struct {
	cc grpc.ClientConnInterface
}

func NewBNPLIndoClient(cc grpc.ClientConnInterface) BNPLIndoClient {
	return &bNPLIndoClient{cc}
}

func (c *bNPLIndoClient) ChargeTaskerBNPLFee(ctx context.Context, in *taskerBNPLRequest.TaskerBNPLRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/BNPLIndo.BNPLIndo/ChargeTaskerBNPLFee", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BNPLIndoServer is the server API for BNPLIndo service.
// All implementations must embed UnimplementedBNPLIndoServer
// for forward compatibility
type BNPLIndoServer interface {
	ChargeTaskerBNPLFee(context.Context, *taskerBNPLRequest.TaskerBNPLRequest) (*empty.Empty, error)
	mustEmbedUnimplementedBNPLIndoServer()
}

// UnimplementedBNPLIndoServer must be embedded to have forward compatible implementations.
type UnimplementedBNPLIndoServer struct {
}

func (UnimplementedBNPLIndoServer) ChargeTaskerBNPLFee(context.Context, *taskerBNPLRequest.TaskerBNPLRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChargeTaskerBNPLFee not implemented")
}
func (UnimplementedBNPLIndoServer) mustEmbedUnimplementedBNPLIndoServer() {}

// UnsafeBNPLIndoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BNPLIndoServer will
// result in compilation errors.
type UnsafeBNPLIndoServer interface {
	mustEmbedUnimplementedBNPLIndoServer()
}

func RegisterBNPLIndoServer(s *grpc.Server, srv BNPLIndoServer) {
	s.RegisterService(&_BNPLIndo_serviceDesc, srv)
}

func _BNPLIndo_ChargeTaskerBNPLFee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskerBNPLRequest.TaskerBNPLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BNPLIndoServer).ChargeTaskerBNPLFee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/BNPLIndo.BNPLIndo/ChargeTaskerBNPLFee",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BNPLIndoServer).ChargeTaskerBNPLFee(ctx, req.(*taskerBNPLRequest.TaskerBNPLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _BNPLIndo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "BNPLIndo.BNPLIndo",
	HandlerType: (*BNPLIndoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ChargeTaskerBNPLFee",
			Handler:    _BNPLIndo_ChargeTaskerBNPLFee_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "BNPLIndo.proto",
}
