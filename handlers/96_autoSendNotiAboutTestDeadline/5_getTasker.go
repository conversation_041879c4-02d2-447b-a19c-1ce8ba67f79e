package autoSendNotiAboutTestDeadline

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getTasker(taskerId string) (*modelUser.Users, []string) {
	tasker, _ := modelUser.GetOneById(local.ISO_CODE, taskerId, bson.M{"_id": 1, "workingPlaces": 1, "avgRating": 1})
	if tasker == nil || len(tasker.WorkingPlaces) == 0 {
		return nil, []string{}
	}

	var cities []string
	for _, workingPlace := range tasker.WorkingPlaces {
		cities = append(cities, workingPlace.City)
	}
	return tasker, cities
}
