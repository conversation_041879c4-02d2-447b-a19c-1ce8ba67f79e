package grpcWebsocketMY

import (
	"context"
	"encoding/json"

	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/mapData"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage"
	"go.uber.org/zap"
)

type MessageModel struct {
	Data      map[string]interface{} `json:"data,omitempty"`
	MessageTo []string               `json:"messageTo,omitempty"`
}

func refactorTaskItem(source string, messageTo []string, task *task.Task) []*MessageModel {
	// Case source == "newTask"
	if source == globalConstant.WEBSOCKET_TASK_SOURCE_NEW_TASK {
		return []*MessageModel{
			{
				Data:      mapData.MapDataTaskItem(task, "", []string{}),
				MessageTo: messageTo,
			},
		}
	}

	if source == globalConstant.WEBSOCKET_TASK_SOURCE_CONFIRMED_TASK && task.Status == globalConstant.TASK_STATUS_POSTED {
		return []*MessageModel{
			{
				Data:      mapData.MapDataTaskItem(task, "", []string{}),
				MessageTo: messageTo,
			},
		}
	}

	// Case source == "confirmedTask"
	if source == globalConstant.WEBSOCKET_TASK_SOURCE_CONFIRMED_TASK {
		// Case not deepCleaning service
		if !globalLib.IsDeepCleaningServiceByKeyName(task.ServiceName) {
			return []*MessageModel{
				{
					Data:      mapData.MapDataTaskItem(task, "", []string{}),
					MessageTo: messageTo,
				},
			}
		}

		// Case deep cleaning service
		result := []*MessageModel{}
		var normalData map[string]interface{}
		leaderId := ""
		for _, v := range task.AcceptedTasker {
			// Case leader
			if v.IsLeader {
				leaderId = v.TaskerId
				result = append(result, &MessageModel{
					Data:      mapData.MapDataTaskItem(task, v.TaskerId, []string{}),
					MessageTo: []string{v.TaskerId},
				})
				continue
			}

			// Case not leader and not have normal tasker data. Can not append at this step because maybe have leader after this
			if normalData == nil {
				normalData = mapData.MapDataTaskItem(task, "", []string{})
			}
		}

		// return to client
		if normalData != nil {
			result = append(result, &MessageModel{
				Data:      normalData,
				MessageTo: globalLib.RemoveTaskersFromList(messageTo, []string{leaderId}),
			})
		}
		return result
	}

	if source == "" {
		taskMap := make(map[string]interface{})
		taskData, _ := json.Marshal(task)
		json.Unmarshal(taskData, &taskMap)
		return []*MessageModel{
			{
				Data:      taskMap,
				MessageTo: messageTo,
			},
		}
	}
	return nil
}

// NOTE: Send socket task only: Must refactor task data to map in this function. Cannot use same as another key ("chat", "notification", "payment")
func SendSocketMessageTask(endpoint string, key string, source string, value string, messageTo []string, data *task.Task) (*websocketMessage.WebsocketMessage, error) {
	defer globalLib.Logger.Sync()

	mapTaskItems := refactorTaskItem(source, messageTo, data)

	client, connect, err := ConnectGRPCWebsocket(endpoint)
	if err != nil {
		globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
			zap.Error(err),
			zap.String("endpoint", endpoint),
			zap.String("source", source),
			zap.Any("messageTo", messageTo),
			zap.Any("data", data),
		)
		return nil, err
	}
	defer connect.Close()

	for _, v := range mapTaskItems {
		var dataByte []byte
		if v != nil {
			dataByte, _ = json.Marshal(v.Data)
		}
		request := &websocketMessage.WebsocketMessage{
			Key:       key,
			Source:    source,
			Value:     value,
			Data:      dataByte,
			MessageTo: v.MessageTo,
		}

		_, err = client.SendSocketMessage(context.Background(), request)
		if err != nil {
			globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
				zap.Error(err),
				zap.Any("body", request),
			)
			return request, err
		}
	}
	return nil, nil
}
