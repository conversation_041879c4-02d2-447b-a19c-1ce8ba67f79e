Version 3.4.0
- Date: 26/03/2025
- Improvements:
- Features:
  - Issue #11: Improve: Update go version
- Bug fixes:

Version 3.3.4
- Date: 10/01/2025
- Improvements:
- Features:
  - Issue #8: [Feature] Add excludedtaskers to task
- Bug fixes:

Version 3.3.3
- Date: 31/12/2024
- Improvements:
- Features:
- Bug fixes:
  - Fix issue #7: [Hotfix] Add changeshistory when send task

Version 3.3.2
- Date: 19/12/2024
- Improvements:
- Features:
  - Issue: Improve: Update use user model
- Bug fixes:

Version 3.3.1
- Date: 16/12/2024
- Improvements:
- Features:
- Bug fixes:
  - Fix issue: Using globalRepo

Version 3.3.0
- Date: 12/12/2024
- Improvements:
- Features:
  - Issue #6: Feature: Split collections phase 5
- Bug fixes:

Version 3.2.1
- Date: 23/10/2024
- Improvements:
- Features:
  - Issue #5: Feature: allow BNPL accept task CASH
- Bug fixes:

Version 3.2.0
- Date: 15/10/2024
- Improvements:
- Features:
  - Issue #4: Feature: Split collections Phase 4
- Bug fixes:

Version 3.1.0
- Date: 01/08/2024
- Improvements:
- Features:
  - Issue #3: Feature: Split collections Phase 3
- Bug fixes:

Version 3.0.1
- Date: 22/07/2024
- Improvements:
- Features:
  - Issue #2: Feature: Update push new task case tasker has BNPL
- Bug fixes:

Version 3.0.0
- Date: 20/05/2024
- Improvements:
- Features:
  - Issue #1: Init service
- Bug fixes: