package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTaskerWorkingHistory "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerWorkingHistory"
	"go.mongodb.org/mongo-driver/bson"
)

func TestCreateTaskerWorkingHistory(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		apiURL := apiSyncCron + "/create-tasker-working-history"
		ResetData()

		now := globalLib.GetCurrentTime(local.TimeZone)
		name := fmt.Sprintf("%d%d", int(now.Month()), now.Year())
		body := map[string]interface{}{
			"action": lib.RUN_NOW,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					var taskerWorkingHistory *modelTaskerWorkingHistory.TaskerWorkingHistory
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_WORKING_HISTORY[local.ISO_CODE], bson.M{"name": name}, bson.M{}, &taskerWorkingHistory)
					So(taskerWorkingHistory, ShouldNotBeNil)
					So(len(taskerWorkingHistory.Taskers), ShouldEqual, 0)
				})
			})
		})
	})
}
