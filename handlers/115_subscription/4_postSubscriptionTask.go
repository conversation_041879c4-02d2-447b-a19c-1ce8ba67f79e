package subscription

import (
	"fmt"
	"log"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSubscription "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscription"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// postSubscriptionTask creates and posts a new task from subscription - STEP 2: Task Creation & Posting
func postSubscriptionTask(subs *modelSubscription.Subscription, nextDate time.Time) float64 {
	createdAt := globalLib.GetCurrentTime(local.TimeZone)

	// Create basic task data as map for insertion
	newTaskId := globalLib.GenerateObjectId()
	taskData := &modelTask.Task{
		XId:              newTaskId,
		ServiceId:        getServiceID(subs),
		Date:             globalLib.ParseTimestampFromDate(nextDate),
		Duration:         subs.Duration,
		Address:          subs.Address,
		Lat:              subs.Location.Lat,
		Lng:              subs.Location.Lng,
		AskerId:          subs.UserId,
		Status:           globalConstant.TASK_STATUS_POSTED,
		CreatedAt:        globalLib.GetCurrentTimestamp(local.TimeZone),
		Rated:            false,
		SubscriptionId:   subs.XId,
		AutoChooseTasker: true,
		Payment: &modelTask.TaskPayment{
			Method: globalConstant.PAYMENT_METHOD_BANK_TRANSFER,
		},
		Description:    subs.Description,
		IsoCode:        local.ISO_CODE,
		CountryCode:    globalConstant.COUNTRY_CODE_VN,
		OriginCurrency: &globalConstant.CURRENCY_SIGN_VN,
	}

	// Set service name if available
	if subs.ServiceName != "" {
		taskData.ServiceName = mapServiceName(subs.ServiceName)
	}

	// Set task note if available
	if subs.TaskNote != "" {
		taskData.TaskNote = subs.TaskNote
	}

	// Set cooking detail if available
	if subs.CookingDetail != nil {
		// Set eating time
		eatingTime := nextDate.Add(time.Duration(subs.Duration) * time.Hour)

		// Copy existing cooking detail fields if needed
		taskData.CookingDetail = &modelTask.TaskCookingDetail{EatingTime: timestamppb.New(eatingTime)}
	}

	// Set child care detail if available
	if subs.DetailChildCare != nil {
		taskData.DetailChildCare = subs.DetailChildCare
	}

	// Calculate price
	priceOfTask := priceOfTaskInSubscription(subs, nextDate)

	taskData.Cost = priceOfTask.CostDetail.Cost
	taskPrice := priceOfTask.CostDetail.Cost

	if priceOfTask.CostDetail.FinalCost > 0 {
		taskPrice = priceOfTask.CostDetail.FinalCost
	}

	// Get user information
	user, err := getUserInfo(subs.UserId)
	if user == nil {
		globalLib.PostToSlack(
			cfg.SlackToken,
			globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE],
			globalConstant.SLACK_USER_NAME,
			fmt.Sprintf("[Sync cron vn v3] Error getting user info: subscriptionId: %s, error: %v", subs.XId, err),
		)
		return 0
	}

	// Set user-related fields
	setUserRelatedFields(taskData, user, subs)

	// Fill task description if empty
	if taskData.Description == "" {
		description := fillTaskDescription(user.Phone)
		if description != "" {
			taskData.Description = description
		}
	}

	// Set additional fields
	setAdditionalFields(taskData, subs)

	// Get service text if not available
	if subs.ServiceText == nil {
		serviceText := getServiceText(subs.ServiceId)
		if serviceText != nil {
			taskData.ServiceText = serviceText
		}
	} else {
		taskData.ServiceText = subs.ServiceText
	}

	// Insert task
	err = insertTask(taskData)
	if err != nil {
		globalLib.PostToSlack(
			cfg.SlackToken,
			globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE],
			globalConstant.SLACK_USER_NAME,
			fmt.Sprintf("[Sync cron vn v3] Error inserting task: subscriptionId: %s, error: %v", subs.XId, err),
		)
		return 0
	}

	// Update user's last posted task
	updateUserLastPostedTask(subs.UserId, createdAt)

	// Update subscription history
	updateSubscriptionHistory(subs.XId, newTaskId, nextDate)

	// Send notification to asker
	sendNotificationToAsker(newTaskId, subs.UserId, user.Language, taskData.ServiceText)

	// Handle force accept tasker or send to available taskers
	handleTaskerAssignment(subs, taskData, newTaskId, user)

	// Check if subscription is finished
	checkSubscriptionCompletion(subs, newTaskId)

	return taskPrice
}

// Helper functions

func getServiceID(subs *modelSubscription.Subscription) string {
	if subs.TaskServiceId != "" {
		return subs.TaskServiceId
	}
	return subs.ServiceId
}

func getUserInfo(userID string) (*modelUser.Users, error) {
	var user *modelUser.Users
	query := bson.M{"_id": userID}
	fields := bson.M{
		"phone":           1,
		"name":            1,
		"taskNote":        1,
		"blackList":       1,
		"language":        1,
		"favouriteTasker": 1,
		"isBlacklist":     1,
	}

	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USERS, query, fields, &user)
	return user, err
}

func setUserRelatedFields(taskData *modelTask.Task, user *modelUser.Users, subs *modelSubscription.Subscription) {
	if len(user.BlackList) > 0 {
		taskData.BlackList = user.BlackList
	}
	taskData.Phone = user.Phone
	taskData.ContactName = user.Name
	if user.TaskNote != "" {
		taskData.TaskNote = user.TaskNote
	}
}

func setAdditionalFields(taskData *modelTask.Task, subs *modelSubscription.Subscription) {
	if subs.HomeType != "" {
		taskData.HomeType = subs.HomeType
	}
	if subs.IsPremium {
		taskData.IsPremium = subs.IsPremium
	}
	if subs.IsEco {
		taskData.IsEco = subs.IsEco
	}
	if len(subs.Addons) > 0 {
		taskData.Addons = subs.Addons
	}
	if len(subs.Pet) > 0 {
		taskData.Pet = subs.Pet
	}
}

func getServiceText(serviceID string) *modelService.ServiceText {
	var service *modelService.Service
	query := bson.M{"_id": serviceID}
	fields := bson.M{"text": 1}

	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], query, fields, &service)
	if service == nil {
		return nil
	}
	return service.Text
}

func insertTask(taskData *modelTask.Task) error {
	return globalDataAccess.InsertOne(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskData)
}

func updateUserLastPostedTask(userID string, createdAt time.Time) {
	query := bson.M{"_id": userID}
	update := bson.M{"$set": bson.M{"lastPostedTask": createdAt}}
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_USERS, query, update)
}

func updateSubscriptionHistory(subsID, taskID string, nextDate time.Time) {
	query := bson.M{"_id": subsID}
	update := bson.M{
		"$push": bson.M{
			"taskIds": taskID,
			"history": nextDate,
		},
	}
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], query, update)
}

func handleTaskerAssignment(subs *modelSubscription.Subscription, taskData *modelTask.Task, taskID string, user *modelUser.Users) {
	if subs.ForceAcceptTaskerId != "" {
		// Handle force accept tasker logic
		handleForceAcceptTasker(subs, taskData, taskID, user)
	} else {
		// Send task to available taskers
		if !user.IsBlacklist {
			sendTaskToTasker(taskID, subs.ServiceId, user.FavouriteTasker)
		}
	}
}

func handleForceAcceptTasker(subs *modelSubscription.Subscription, taskData *modelTask.Task, taskID string, user *modelUser.Users) {
	// Get tasker information
	var tasker *modelUser.Users
	query := bson.M{
		"_id":    subs.ForceAcceptTaskerId,
		"status": bson.M{"$in": [2]string{globalConstant.USER_STATUS_ACTIVE, globalConstant.USER_STATUS_BLOCKED}},
	}
	fields := bson.M{
		"avatar":            1,
		"name":              1,
		"avgRating":         1,
		"taskDone":          1,
		"phone":             1,
		"status":            1,
		"notComeLockNumber": 1,
		"language":          1,
	}

	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USERS, query, fields, &tasker)
	if err != nil || !canTaskerAccept(tasker) {
		// Post to Slack about tasker not available
		message := fmt.Sprintf("Cần CS xử lý: Công việc của Subscription đã đăng và KHÔNG confirm được do Tasker %s đã bị khóa. CV của Asker: %s", tasker.Phone, user.Phone)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
		return
	}

	// Check for conflicts with other tasks
	if hasTaskConflict(subs.ForceAcceptTaskerId, taskData) {
		message := fmt.Sprintf("Cần CS xử lý: Công việc của Subscription đã đăng và KHÔNG confirm được do Tasker %s - %s trùng giờ làm việc. CV của Asker: %s", tasker.Phone, tasker.Name, user.Phone)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
		return
	}

	// Force accept the task
	acceptedTasker := bson.M{
		"taskerId":   subs.ForceAcceptTaskerId,
		"avatar":     tasker.Avatar,
		"name":       tasker.Name,
		"avgRating":  tasker.AvgRating,
		"taskDone":   tasker.TaskDone,
		"acceptedAt": globalLib.GetCurrentTime(local.TimeZone),
	}

	update := bson.M{
		"$set": bson.M{
			"acceptedTasker": []bson.M{acceptedTasker},
			"status":         globalConstant.TASK_STATUS_CONFIRMED,
		},
	}

	globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskID, update)

	// Send notification to tasker
	sendConfirmationNotification(subs.ForceAcceptTaskerId, taskID, tasker.Language)

	// Alert if tasker status is not ACTIVE
	if tasker.Status != "ACTIVE" {
		message := fmt.Sprintf("CS lưu ý: Tasker %s status %s đã được thêm vào subscription task (Asker: %s)", tasker.Phone, tasker.Status, user.Phone)
		globalLib.PostToSlack(cfg.SlackToken, "vn-sub-schedule", globalConstant.SLACK_USER_NAME, message)
	}
}

func hasTaskConflict(taskerID string, taskData *modelTask.Task) bool {
	// Get task date and duration from taskData
	taskDate := taskData.Date.AsTime()
	taskDuration := taskData.Duration

	// Check for conflicting tasks with CONFIRMED status
	var confirmedTasks []bson.M
	query := bson.M{
		"status":                  globalConstant.TASK_STATUS_CONFIRMED,
		"acceptedTasker.taskerId": taskerID,
	}
	fields := bson.M{"date": 1, "duration": 1}

	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], query, fields, &confirmedTasks)
	if err != nil {
		return false
	}

	// Check conflict with each confirmed task
	timeInBetweenTask := 14 // 14 minutes buffer time (same as JavaScript)

	for _, confirmedTask := range confirmedTasks {
		if checkConflictTask(taskDate, taskDuration, confirmedTask, timeInBetweenTask) {
			return true
		}
	}

	return false
}

// checkConflictTask implements the same logic as JavaScript version
func checkConflictTask(taskDate time.Time, taskDuration float64, otherTask bson.M, timeInBetweenTask int) bool {
	// Get other task date and duration
	otherTaskDate, ok := otherTask["date"].(time.Time)
	if !ok {
		return false
	}

	otherTaskDuration, ok := otherTask["duration"].(float64)
	if !ok {
		return false
	}

	// Calculate task 1 time range with buffer
	// taskStart_1 = task.date - timeInBetweenTask minutes
	taskStart1 := taskDate.Add(-time.Duration(timeInBetweenTask) * time.Minute)
	// taskEnd_1 = task.date + (task.duration * 60 + timeInBetweenTask) minutes
	taskEnd1 := taskDate.Add(time.Duration(taskDuration*60+float64(timeInBetweenTask)) * time.Minute)

	// Calculate task 2 time range
	// taskStart_2 = otherTask.date
	taskStart2 := otherTaskDate
	// taskEnd_2 = otherTask.date + otherTask.duration hours
	taskEnd2 := otherTaskDate.Add(time.Duration(otherTaskDuration) * time.Hour)

	// Check for time overlap
	// if ((taskStart_1 >= taskStart_2 && taskStart_1 < taskEnd_2)
	//   || (taskStart_2 >= taskStart_1 && taskStart_2 < taskEnd_1))
	if (taskStart1.Equal(taskStart2) || taskStart1.After(taskStart2)) && taskStart1.Before(taskEnd2) {
		return true
	}
	if (taskStart2.Equal(taskStart1) || taskStart2.After(taskStart1)) && taskStart2.Before(taskEnd1) {
		return true
	}

	return false
}

func sendConfirmationNotification(taskerID, taskID, taskerLanguage string) {
	lang := globalConstant.LANG_VI
	if taskerLanguage != "" {
		lang = taskerLanguage
	}

	// Create notification data
	title := localization.GetLocalizeObject("NOTIFICATION_MESSAGE_TASKER_CONFIRMED_TITLE")
	text := localization.GetLocalizeObject("NOTIFICATION_MESSAGE_TASKER_CONFIRMED")

	// Create notification for database
	notiInfos := []interface{}{
		&notification.Notification{
			XId:         globalLib.GenerateObjectId(),
			UserId:      taskerID,
			TaskId:      taskID,
			Type:        2,
			Title:       globalLib.LocalizeServiceName(lang, title),
			Description: globalLib.LocalizeServiceName(lang, text),
			CreatedAt:   timestamppb.New(globalLib.GetCurrentTime(local.TimeZone)),
			NavigateTo:  "TaskDetail",
		},
	}

	// Create user IDs for push notification
	userIds := []*pushNotificationRequest.PushNotificationRequestUserIds{
		{
			UserId:   taskerID,
			Language: lang,
		},
	}

	// Create title and body with localization
	titleText := &modelService.ServiceText{
		Vi: globalLib.LocalizeServiceName(lang, title),
	}
	bodyText := &modelService.ServiceText{
		Vi: globalLib.LocalizeServiceName(lang, text),
	}

	// Create payload
	payload := &pushNotificationRequest.PushNotificationRequestPayload{
		Type:       2, // Task confirmation notification type
		TaskId:     taskID,
		NavigateTo: "TaskDetail",
	}

	// Use lib.SendNotification instead of custom gRPC implementation
	lib.SendNotification(notiInfos, userIds, titleText, bodyText, payload, true)
}

func checkSubscriptionCompletion(subs *modelSubscription.Subscription, taskID string) {
	// Use existing subscription data instead of querying DB again
	if len(subs.Schedule) == 0 {
		return
	}

	// Calculate current progress
	// After adding this task to history, check if all tasks are completed
	totalTasks := len(subs.Schedule)
	completedTasks := len(subs.History) + 1 // +1 for the task we just added

	// Check if this is the last task in the subscription
	if completedTasks >= totalTasks {
		// Mark subscription as DONE
		update := bson.M{"$set": bson.M{
			"status":    "DONE",
			"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
		}}
		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], subs.XId, update)

		// Mark task as final subscription task
		taskUpdate := bson.M{"$set": bson.M{
			"finalSubscriptionTask": true,
			"updatedAt":             globalLib.GetCurrentTime(local.TimeZone),
		}}
		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskID, taskUpdate)

		log.Printf("Subscription %s completed. Final task: %s (completed %d/%d tasks)",
			subs.XId, taskID, completedTasks, totalTasks)
	} else {
		log.Printf("Subscription %s progress: %d/%d tasks completed",
			subs.XId, completedTasks, totalTasks)
	}
}

// Additional helper functions for subscription processing
