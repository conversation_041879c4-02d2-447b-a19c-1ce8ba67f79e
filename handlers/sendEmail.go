package handlers

import (
	"context"
	"net/http"
	"time"

	//How to use the go mod

	"github.com/mailgun/mailgun-go/v4"
	"go.uber.org/zap"

	// https://www.kablamo.com.au/blog/2018/12/10/just-tell-me-how-to-use-go-modules

	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
)

/*
 * @Description: Check email input and send email
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 24/02/2021
 * @UpdatedBy: ngoctb3
 */
func SendEmail(req *modelEmailSending.EmailSending) (*modelEmailResponse.EmailResponse, error) {
	defer local.Logger.Sync()

	// Check email config
	resErr := CheckEmailConfig()
	if resErr != nil {
		local.Logger.Warn(resErr.Message)
		return resErr, nil
	}
	// 1. Check input
	res, err := checkSendEmailInput(req)
	if err != nil {
		local.Logger.Warn(err.Error(),
			zap.Error(err),
		)
		return res, err
	}
	// 2. Call the mailgun to send email
	mg := mailgun.NewMailgun(cfg.MailGunDomain, cfg.MailGunAPIKey)
	m := mg.NewMessage(
		req.From,
		req.Subject,
		lib.SENDING_EMAIL,
		req.To,
	)
	if req.ReplyTo != "" {
		m.AddHeader("Reply-To", req.ReplyTo)
	}
	m.SetHtml(req.Content)
	for i := 0; i < len(req.Cc); i++ {
		m.AddCC(req.Cc[i])
	}
	for i := 0; i < len(req.Bcc); i++ {
		m.AddBCC(req.Bcc[i])
	}
	if len(req.Attachments) > 0 {
		for _, attachment := range req.Attachments {
			m.AddAttachment(attachment)
		}
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*30)
	defer cancel()
	_, id, err := mg.Send(ctx, m)
	if id != "" {
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusOK,
			Message:    "",
			Data: &modelEmailResponse.DataResponse{
				MailId:     id,
				InvalidCc:  req.Cc,
				InvalidBcc: req.Bcc,
			},
		}, nil
	} else if err != nil {
		local.Logger.Warn(err.Error(),
			zap.Error(err),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    "",
			Data: &modelEmailResponse.DataResponse{
				InvalidCc:  req.Cc,
				InvalidBcc: req.Bcc,
				Error:      err.Error(),
			},
		}, err
	}
	return &modelEmailResponse.EmailResponse{
		StatusCode: http.StatusOK,
	}, nil
}
