import { Meteor } from 'meteor/meteor';
import { Setting } from '../lib/collections.js';
import '../synced-cron';

//======= WARNING : NEED TO review and rewrite the synced cron before comment out/run =========
  const setting = Setting.findOne({});
  Meteor.call('generateTaskBySchedule', setting.runEvery || 6); // run every 3 hours : at 7 - 10 - 13 - 16 - 22
//////  // Meteor.call('monthlyReward');
  // Meteor.call('autoDone');// Run every 10 minutes from 7AM to 22PM
////  // Meteor.call('autoRate');
  // Meteor.call('calculateTaskerScore');// Run at 3:00 every Monday with timezone GMT+7.
////  // Meteor.call('dailyNotification');
////  // Meteor.call('estimateRushHour'); // Paused as default
  // Meteor.call('remindAskerConfirm');// Run at 25' every hour
  // Meteor.call('calculateTaskerLevel'); // Run at 00:01 in the first day of the month
  Meteor.call('generateSubscription');// Run at 21:10 every day with timezone GMT+7. 
  // Meteor.call('suggestSubscription'); // Run at 9:30 AM every day
//  
//  // Meteor.call('weeklyPayout'); // Paused as default
  // Meteor.call('weeklyReport');// Run at 20:00 Sunday
  // Meteor.call('autoDisableTaskerRating');// Run at 5:00 every 3 days with timezone GMT+7.
// Meteor.call('autoExpirePostedTaskLongTime'); // Run at every 5 minutes
// Meteor.call('reactiveAskerAfterBlocked');// Run at 01:15 and 13:15.
// Meteor.call('autoCheckDatabase');// Run at 20' every 1 hour
// Meteor.call('updateTrustPoint');// Run at 2:10 AM every day with timezone GMT+7.
//  Meteor.call('taskerReferrals'); TODO : need to find out what is this syncron doing, rewrite it !
  // Meteor.call('autoUnlockTaskerNotCome');// Run at 40' every hour GMT+7
//  // Meteor.call('autoLockTaskerInactiveOneMonth');
  // Meteor.call('autoCleanUpNotification');// Run at 1:30 every 7 days
//  // Meteor.call('autoDisableAskerRating');
  // Meteor.call('autoCalculateAVGRating');// Run at 3:30 AM every setting days
//  Meteor.call('autoSendPromotionNewAsker'); TODO: Might not need this now, use CleverTap mkt flows instead
//  Meteor.call('autoRefundCardIntegrationFee'); TODO: Need to rewrite and apply for TH only for now
  // Meteor.call('autoAccumulatePoint');// Run at 25' every 2 hour from 7AM to 22PM
  // Meteor.call('autoRemindCardExpired');
  // Meteor.call('autoLockTaskerCancel2Task');// Run every 2 hours
//  Meteor.call('autoSendNotificationQuickPostTask'); // Run every 10 minutes TODO: Need to find out what is this syncedcron doing
  // Meteor.call('autoCheckConfirmedTasks');// Run every 10 minutes
  // Meteor.call('autoResetNotComeLocking');// Run every 8 hours
  // Meteor.call('autoCheckIncentive');// Run at 7 AM, 7PM every setting days
//  Meteor.call('autoRemoveOldRaixPushNotification');
//  // Meteor.call('autoLockTaskerLowAvgRating');
  // Meteor.call('autoCheckTaskPrice');// Run every 30 minutes
  // Meteor.call('autoAlertPostedTask');// Run every 10 minutes
//  
  // Meteor.call('autoCheckOrderZaloPay');// Run every 20 minutes.
  // Meteor.call('autoCkeckInvalidTaskStatus');// Run every 10 minutes
  // Meteor.call('autoCheckTaskNeedSupport');// Run every 1 hour
  // Meteor.call('autoRemindTaskerTopup');// Run at 7:10 AM every day with timezone GMT+7.
  // Meteor.call('autoCheckFinancialDaily');// Run at 2:20AM every day with timezone GMT+7.
//  Meteor.call('autoTopupEmployee'); // TODO: Figure out the schedule
//  Meteor.call('autoWithdrawEmployee'); TODO: combine with autoTopupEmployee to reset employee allowance/benefit
//  Meteor.call('autoAlertAskerScam'); // Might not need it now, need to update for TH
  // Meteor.call('autoRateSubscriptionTask');// Run at 00:10 every day.
  // Meteor.call('autoAlertLaundryNotCollect');// Run every 30 minutes
//  Meteor.call('autoSendLuckyDraw');
//  
  SyncedCron.start();
