package weeklyResetLeaderBoard

import (
	modelJourneyLeaderBoard "gitlab.com/btaskee/go-services-model-v2/grpcmodel/journeyLeaderBoard"
	modeJourneySetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/journeySetting"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
)

type ModelGroupTaskerByLevel map[string][]*modelJourneyLeaderBoard.JourneyLeaderBoard

func calculateNewLeaderBoard(cityName string, levelSetting, defaultLevelSetting map[string]*modeJourneySetting.JourneySettingLevel, mapTaskers map[string]*modelUser.Users) ModelGroupTaskerByLevel {
	mapTaskerInLeaderBoard := make(map[string]*modelJourneyLeaderBoard.JourneyLeaderBoard)
	// 1. Add new tasker to leader board and update old tasker level
	insertLeaderBoardForTaskers(cityName, mapTaskers, mapTaskerInLeaderBoard)

	// 4. Update user point and groupByCityAndLevel
	taskersGroupByLevel := groupTaskerInfoByLevel(mapTaskerInLeaderBoard, levelSetting, defaultLevelSetting)
	return taskersGroupByLevel
}

// 1
func insertLeaderBoardForTaskers(cityName string, mapTaskers map[string]*modelUser.Users, mapTaskerInLeaderBoard map[string]*modelJourneyLeaderBoard.JourneyLeaderBoard) {
	for _, tasker := range mapTaskers {
		taskerJourneyLevel := "LV1" // Set default level = LV1 // If tasker not has journey info => new tasker => LV1
		if tasker.GetJourneyInfo() != nil {
			taskerJourneyLevel = tasker.GetJourneyInfo().GetLevel()
		}
		mapTaskerInLeaderBoard[tasker.XId] = &modelJourneyLeaderBoard.JourneyLeaderBoard{
			XId:      tasker.XId,
			CityName: cityName,
			Name:     tasker.Name,
			Avatar:   tasker.Avatar,
			Level:    taskerJourneyLevel,
			Rank:     -1,
		}
	}
}

// 4
func groupTaskerInfoByLevel(mapTaskerInLeaderBoard map[string]*modelJourneyLeaderBoard.JourneyLeaderBoard, levelSetting, defaultLevelSetting map[string]*modeJourneySetting.JourneySettingLevel) ModelGroupTaskerByLevel {
	result := make(ModelGroupTaskerByLevel)
	for _, v := range mapTaskerInLeaderBoard {
		if result[v.Level] == nil {
			result[v.Level] = []*modelJourneyLeaderBoard.JourneyLeaderBoard{}
		}
		if levelSetting[v.Level] == nil {
			levelSetting[v.Level] = defaultLevelSetting[v.Level]
		}
		if levelSetting[v.Level] != nil {
			v.Icon = levelSetting[v.Level].Icon
			v.Title = levelSetting[v.Level].Title
			v.Text = levelSetting[v.Level].Text
			result[v.Level] = append(result[v.Level], v)
		}
	}

	return result
}
