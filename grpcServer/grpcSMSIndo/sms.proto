syntax = "proto3";
package grpcSMSIndo;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcSMSIndo";

import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/smsSending/smsSending.proto";

service SMS {
  rpc Send (smsSending.SmsRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/api/v3/sms-indo/send"
      body: "*"
    };
  }
  rpc SendViaTwillo (smsSending.SmsRequest) returns (google.protobuf.Empty) {}
}