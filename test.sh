cover="-coverprofile=cover -coverpkg=./service"
export EMAIL_SERVICE_CONFIG="../config"
case $1 in
  "1")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/ -run SendEmail
    else
      go test -v ./testing ${cover}/ -run SendEmail/^$2$
    fi
    ;;
  "2")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/ -run SendReceiptEmail
    else
      go test -v ./testing ${cover}/ -run SendReceiptEmail/^$2$
    fi
    ;;
    "3")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/ -run SendVerifyEmail
    else
      go test -v ./testing ${cover}/ -run SendVerifyEmail/^$2$
    fi
    ;;
    "4")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/ -run SendCancelFeeEmail
    else
      go test -v ./testing ${cover}/ -run SendCancelFeeEmail/^$2$
    fi
    ;;
    "5")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/ -run SendBusinessReport
    else
      go test -v ./testing ${cover}/ -run SendBusinessReport/^$2$
    fi
    ;;
  *)
    go test -v ./testing -timeout 1h ${cover}/...
    ;;
esac