// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.7.1
// source: pricingMY.proto

package grpcPricingMY

import (
	pricingSubscriptionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionRequest"
	pricingSubscriptionResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionResponse"
	pricingrequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingrequest"
	pricingresponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_pricingMY_proto protoreflect.FileDescriptor

var file_pricingMY_proto_rawDesc = []byte{
	0x0a, 0x0f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x4d, 0x59, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0d, 0x67, 0x72, 0x70, 0x63, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x4d, 0x59,
	0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x56,
	0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b,
	0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65,
	0x6c, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x58, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f,
	0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x2d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x6f, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61,
	0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2f, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x2d, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x71, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74,
	0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x2d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0xe8, 0x0e, 0x0a, 0x07, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x12, 0x82, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x48,
	0x6f, 0x6d, 0x65, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f,
	0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25,
	0x22, 0x20, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x2d, 0x6d, 0x79, 0x2f, 0x68, 0x6f, 0x6d, 0x65, 0x2d, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x69,
	0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0x82, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x65, 0x70, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67,
	0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2b, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x25, 0x22, 0x20, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x79, 0x2f, 0x64, 0x65, 0x65, 0x70, 0x2d, 0x63,
	0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0x8c, 0x01, 0x0a, 0x15, 0x52,
	0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x48, 0x6f, 0x6d, 0x65, 0x43, 0x6c, 0x65, 0x61,
	0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c,
	0x74, 0x22, 0x36, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x30, 0x22, 0x2b, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x79, 0x2f, 0x72, 0x65,
	0x2d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x68, 0x6f, 0x6d, 0x65, 0x2d, 0x63, 0x6c,
	0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0x8c, 0x01, 0x0a, 0x15, 0x52, 0x65,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x44, 0x65, 0x65, 0x70, 0x43, 0x6c, 0x65, 0x61, 0x6e,
	0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x36, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x30, 0x22, 0x2b, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76,
	0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x79, 0x2f, 0x72, 0x65, 0x2d,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x64, 0x65, 0x65, 0x70, 0x2d, 0x63, 0x6c, 0x65,
	0x61, 0x6e, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0x7d, 0x0a, 0x12, 0x52, 0x65, 0x43, 0x61,
	0x6c, 0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x12, 0x1e,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2a, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x24, 0x22, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x79, 0x2f, 0x72, 0x65, 0x2d, 0x63, 0x61, 0x6c, 0x63, 0x75,
	0x6c, 0x61, 0x74, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0x59, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x43, 0x6c, 0x65, 0x61, 0x6e,
	0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x22, 0x00, 0x12, 0x89, 0x01, 0x0a, 0x1b, 0x41, 0x50, 0x49, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x4f, 0x66, 0x66, 0x69, 0x63, 0x65, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69,
	0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22,
	0x2d, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x27, 0x22, 0x22, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33,
	0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x79, 0x2f, 0x6f, 0x66, 0x66, 0x69,
	0x63, 0x65, 0x2d, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x3a, 0x01, 0x2a, 0x12, 0x90,
	0x01, 0x0a, 0x17, 0x52, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x4f, 0x66, 0x66, 0x69,
	0x63, 0x65, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73,
	0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x38, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x32, 0x22,
	0x2d, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x2d, 0x6d, 0x79, 0x2f, 0x72, 0x65, 0x2d, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x6f,
	0x66, 0x66, 0x69, 0x63, 0x65, 0x2d, 0x63, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x3a, 0x01,
	0x2a, 0x12, 0xb6, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x36, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x38, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2a,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x24, 0x22, 0x1f, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f,
	0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x79, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x3a, 0x01, 0x2a, 0x12, 0x71, 0x0a, 0x0e, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x6f, 0x66, 0x61, 0x12, 0x1e, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72,
	0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70,
	0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43,
	0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x22, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1c, 0x22, 0x17, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2d, 0x6d, 0x79, 0x2f, 0x73, 0x6f, 0x66, 0x61, 0x3a, 0x01, 0x2a, 0x12, 0x7b, 0x0a,
	0x0d, 0x52, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x53, 0x6f, 0x66, 0x61, 0x12, 0x1e,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b,
	0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2d, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x27, 0x22, 0x22, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69,
	0x63, 0x69, 0x6e, 0x67, 0x2d, 0x6d, 0x79, 0x2f, 0x72, 0x65, 0x2d, 0x70, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x2d, 0x73, 0x6f, 0x66, 0x61, 0x3a, 0x01, 0x2a, 0x12, 0x8b, 0x01, 0x0a, 0x1a, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x41, 0x69, 0x72, 0x43, 0x6f, 0x6e, 0x64,
	0x69, 0x74, 0x69, 0x6f, 0x6e, 0x65, 0x72, 0x56, 0x32, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x22, 0x25,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d,
	0x6d, 0x79, 0x2f, 0x61, 0x69, 0x72, 0x2d, 0x63, 0x6f, 0x6e, 0x64, 0x69, 0x74, 0x69, 0x6f, 0x6e,
	0x65, 0x72, 0x2d, 0x76, 0x32, 0x3a, 0x01, 0x2a, 0x12, 0x8c, 0x01, 0x0a, 0x1c, 0x41, 0x50, 0x49,
	0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x54, 0x61, 0x73, 0x6b, 0x44, 0x61,
	0x74, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1e, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63,
	0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74,
	0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x2f, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x29, 0x22, 0x24,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x2d,
	0x6d, 0x79, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2d, 0x64, 0x61, 0x74, 0x65, 0x2d, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x3a, 0x01, 0x2a, 0x12, 0x76, 0x0a, 0x25, 0x52, 0x65, 0x43, 0x61, 0x6c,
	0x63, 0x75, 0x6c, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x46, 0x6f, 0x72,
	0x49, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73, 0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x2e, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x2e, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x49, 0x6e, 0x63, 0x72, 0x65, 0x61, 0x73,
	0x65, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1b, 0x2e, 0x70, 0x72, 0x69, 0x63, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x43, 0x6f, 0x73, 0x74, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x22, 0x00, 0x42,
	0x42, 0x5a, 0x40, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74,
	0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x50, 0x72, 0x69, 0x63, 0x69, 0x6e,
	0x67, 0x4d, 0x59, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_pricingMY_proto_goTypes = []interface{}{
	(*pricingrequest.PricingRequest)(nil),                           // 0: pricingrequest.PricingRequest
	(*pricingSubscriptionRequest.PricingSubscriptionRequest)(nil),   // 1: pricingSubscriptionRequest.PricingSubscriptionRequest
	(*pricingrequest.PricingIncreaseDurationRequest)(nil),           // 2: pricingrequest.PricingIncreaseDurationRequest
	(*pricingresponse.CostResult)(nil),                              // 3: pricingresponse.CostResult
	(*pricingSubscriptionResponse.PricingSubscriptionResponse)(nil), // 4: pricingSubscriptionResponse.PricingSubscriptionResponse
}
var file_pricingMY_proto_depIdxs = []int32{
	0,  // 0: grpcPricingMY.Pricing.GetPricingHomeCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 1: grpcPricingMY.Pricing.GetPricingDeepCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 2: grpcPricingMY.Pricing.RePricingHomeCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 3: grpcPricingMY.Pricing.RePricingDeepCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 4: grpcPricingMY.Pricing.ReCalculatePricing:input_type -> pricingrequest.PricingRequest
	0,  // 5: grpcPricingMY.Pricing.GetPricingOfficeCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 6: grpcPricingMY.Pricing.APIGetPricingOfficeCleaning:input_type -> pricingrequest.PricingRequest
	0,  // 7: grpcPricingMY.Pricing.RePricingOfficeCleaning:input_type -> pricingrequest.PricingRequest
	1,  // 8: grpcPricingMY.Pricing.GetPricingSubscription:input_type -> pricingSubscriptionRequest.PricingSubscriptionRequest
	0,  // 9: grpcPricingMY.Pricing.GetPricingSofa:input_type -> pricingrequest.PricingRequest
	0,  // 10: grpcPricingMY.Pricing.RePricingSofa:input_type -> pricingrequest.PricingRequest
	0,  // 11: grpcPricingMY.Pricing.GetPricingAirConditionerV2:input_type -> pricingrequest.PricingRequest
	0,  // 12: grpcPricingMY.Pricing.APIGetPricingTaskDateOptions:input_type -> pricingrequest.PricingRequest
	2,  // 13: grpcPricingMY.Pricing.ReCalculatePricingForIncreaseDuration:input_type -> pricingrequest.PricingIncreaseDurationRequest
	3,  // 14: grpcPricingMY.Pricing.GetPricingHomeCleaning:output_type -> pricingresponse.CostResult
	3,  // 15: grpcPricingMY.Pricing.GetPricingDeepCleaning:output_type -> pricingresponse.CostResult
	3,  // 16: grpcPricingMY.Pricing.RePricingHomeCleaning:output_type -> pricingresponse.CostResult
	3,  // 17: grpcPricingMY.Pricing.RePricingDeepCleaning:output_type -> pricingresponse.CostResult
	3,  // 18: grpcPricingMY.Pricing.ReCalculatePricing:output_type -> pricingresponse.CostResult
	3,  // 19: grpcPricingMY.Pricing.GetPricingOfficeCleaning:output_type -> pricingresponse.CostResult
	3,  // 20: grpcPricingMY.Pricing.APIGetPricingOfficeCleaning:output_type -> pricingresponse.CostResult
	3,  // 21: grpcPricingMY.Pricing.RePricingOfficeCleaning:output_type -> pricingresponse.CostResult
	4,  // 22: grpcPricingMY.Pricing.GetPricingSubscription:output_type -> pricingSubscriptionResponse.PricingSubscriptionResponse
	3,  // 23: grpcPricingMY.Pricing.GetPricingSofa:output_type -> pricingresponse.CostResult
	3,  // 24: grpcPricingMY.Pricing.RePricingSofa:output_type -> pricingresponse.CostResult
	3,  // 25: grpcPricingMY.Pricing.GetPricingAirConditionerV2:output_type -> pricingresponse.CostResult
	3,  // 26: grpcPricingMY.Pricing.APIGetPricingTaskDateOptions:output_type -> pricingresponse.CostResult
	3,  // 27: grpcPricingMY.Pricing.ReCalculatePricingForIncreaseDuration:output_type -> pricingresponse.CostResult
	14, // [14:28] is the sub-list for method output_type
	0,  // [0:14] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_pricingMY_proto_init() }
func file_pricingMY_proto_init() {
	if File_pricingMY_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pricingMY_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pricingMY_proto_goTypes,
		DependencyIndexes: file_pricingMY_proto_depIdxs,
	}.Build()
	File_pricingMY_proto = out.File
	file_pricingMY_proto_rawDesc = nil
	file_pricingMY_proto_goTypes = nil
	file_pricingMY_proto_depIdxs = nil
}
