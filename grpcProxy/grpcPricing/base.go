package grpcPricing

import (
	"context"
	"errors"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPricingIndo"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPricingMY"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPricingTH"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPricingVN"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionRequest"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingrequest"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse"
)

type PricingClient struct {
	isoCode, endpoint string
}

func Connect(isoCode, endpoint string) *PricingClient {
	return &PricingClient{
		isoCode:  isoCode,
		endpoint: endpoint,
	}
}

func (c *PricingClient) ReCalculatePricing(request *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	// Pricing VN, TH
	if c.isoCode == globalConstant.ISO_CODE_VN {
		client, connect, err := grpcPricingVN.ConnectGRPCPricingVN(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		if globalLib.IsHomeMovingServiceByKeyName(request.GetTask().GetServiceName()) {
			return client.RePricingHomeMoving(context.Background(), request)
		}
		return client.ReCalculatePricing(context.Background(), request)
	}

	if c.isoCode == globalConstant.ISO_CODE_TH {
		client, connect, err := grpcPricingTH.ConnectGRPCPricingTH(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.ReCalculatePricing(context.Background(), request)
	}

	// Pricing Indo
	if c.isoCode == globalConstant.ISO_CODE_INDO {
		client, connect, err := grpcPricingIndo.ConnectGRPCPricing(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.ReCalculatePricing(context.Background(), request)
	}

	// Pricing MY
	if c.isoCode == globalConstant.ISO_CODE_MY {
		client, connect, err := grpcPricingMY.ConnectGRPCPricing(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.ReCalculatePricing(context.Background(), request)
	}

	// Error cannot get price
	return nil, errors.New("ERROR_CAN_NOT_GET_PRICE")
}

func (c *PricingClient) GetPricingOfficeCleaning(request *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	// Pricing VN
	if c.isoCode == globalConstant.ISO_CODE_VN {
		client, connect, err := grpcPricingVN.ConnectGRPCPricingVN(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingOfficeCleaning(context.Background(), request)
	}

	// Pricing TH
	if c.isoCode == globalConstant.ISO_CODE_TH {
		client, connect, err := grpcPricingTH.ConnectGRPCPricingTH(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingOfficeCleaning(context.Background(), request)
	}

	// Pricing Indo
	if c.isoCode == globalConstant.ISO_CODE_INDO {
		client, connect, err := grpcPricingIndo.ConnectGRPCPricing(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingOfficeCleaning(context.Background(), request)
	}

	// Pricing MY
	if c.isoCode == globalConstant.ISO_CODE_MY {
		client, connect, err := grpcPricingMY.ConnectGRPCPricing(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingOfficeCleaning(context.Background(), request)
	}

	// Error cannot get price
	return nil, errors.New("ERROR_CAN_NOT_GET_PRICE")
}

func (c *PricingClient) GetPricingHomeCleaning(request *pricingrequest.PricingRequest) (*pricingresponse.CostResult, error) {
	// Pricing VN
	if c.isoCode == globalConstant.ISO_CODE_VN {
		client, connect, err := grpcPricingVN.ConnectGRPCPricingVN(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingHomeCleaning(context.Background(), request)
	}

	// Pricing TH
	if c.isoCode == globalConstant.ISO_CODE_TH {
		client, connect, err := grpcPricingTH.ConnectGRPCPricingTH(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingHomeCleaning(context.Background(), request)
	}

	// Pricing Indo
	if c.isoCode == globalConstant.ISO_CODE_INDO {
		client, connect, err := grpcPricingIndo.ConnectGRPCPricing(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingHomeCleaning(context.Background(), request)
	}

	// Pricing MY
	if c.isoCode == globalConstant.ISO_CODE_MY {
		client, connect, err := grpcPricingMY.ConnectGRPCPricing(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingHomeCleaning(context.Background(), request)
	}

	// Error cannot get price
	return nil, errors.New("ERROR_CAN_NOT_GET_PRICE")
}

func (c *PricingClient) GetPricingSubscriptionPatientCare(request *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	// Pricing VN, TH
	if c.isoCode == globalConstant.ISO_CODE_VN {
		client, connect, err := grpcPricingVN.ConnectGRPCPricingVN(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingSubscriptionPatientCare(context.Background(), request)
	}

	// Pricing TH
	if c.isoCode == globalConstant.ISO_CODE_TH {
		client, connect, err := grpcPricingTH.ConnectGRPCPricingTH(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingSubscriptionPatientCare(context.Background(), request)
	}
	// Error cannot get price
	return nil, errors.New("ERROR_CAN_NOT_GET_PRICE")
}

func (c *PricingClient) GetPricingSubscriptionElderlyCare(request *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	// Pricing VN, TH
	if c.isoCode == globalConstant.ISO_CODE_VN {
		client, connect, err := grpcPricingVN.ConnectGRPCPricingVN(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingSubscriptionElderlyCare(context.Background(), request)
	}

	// Pricing TH
	if c.isoCode == globalConstant.ISO_CODE_TH {
		client, connect, err := grpcPricingTH.ConnectGRPCPricingTH(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingSubscriptionElderlyCare(context.Background(), request)
	}

	// Error cannot get price
	return nil, errors.New("ERROR_CAN_NOT_GET_PRICE")
}

func (c *PricingClient) GetPricingSubscriptionChildCare(request *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	// Pricing VN, TH
	if c.isoCode == globalConstant.ISO_CODE_VN {
		client, connect, err := grpcPricingVN.ConnectGRPCPricingVN(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingSubscriptionChildCare(context.Background(), request)
	}

	// Error cannot get price
	return nil, errors.New("ERROR_CAN_NOT_GET_PRICE")
}

func (c *PricingClient) GetPricingSubscription(request *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	// Pricing VN
	if c.isoCode == globalConstant.ISO_CODE_VN {
		client, connect, err := grpcPricingVN.ConnectGRPCPricingVN(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingSubscription(context.Background(), request)
	}
	// Pricing TH
	if c.isoCode == globalConstant.ISO_CODE_TH {
		client, connect, err := grpcPricingTH.ConnectGRPCPricingTH(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingSubscription(context.Background(), request)
	}
	// Pricing Indo
	if c.isoCode == globalConstant.ISO_CODE_INDO {
		client, connect, err := grpcPricingIndo.ConnectGRPCPricing(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingSubscription(context.Background(), request)
	}
	// Pricing MY
	if c.isoCode == globalConstant.ISO_CODE_MY {
		client, connect, err := grpcPricingMY.ConnectGRPCPricing(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingSubscription(context.Background(), request)
	}
	// Error cannot get price
	return nil, errors.New("ERROR_CAN_NOT_GET_PRICE")
}

func (c *PricingClient) GetPricingSubscriptionOfficeCleaning(request *pricingSubscriptionRequest.PricingSubscriptionRequest) (*pricingSubscriptionResponse.PricingSubscriptionResponse, error) {
	// Pricing VN
	if c.isoCode == globalConstant.ISO_CODE_VN {
		client, connect, err := grpcPricingVN.ConnectGRPCPricingVN(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingSubscriptionOfficeCleaning(context.Background(), request)
	}
	// Pricing TH
	if c.isoCode == globalConstant.ISO_CODE_TH {
		client, connect, err := grpcPricingTH.ConnectGRPCPricingTH(c.endpoint)
		if err != nil {
			return nil, err
		}
		defer connect.Close()
		return client.GetPricingSubscriptionOfficeCleaning(context.Background(), request)
	}
	// Error cannot get price
	return nil, errors.New("ERROR_CAN_NOT_GET_PRICE")
}
