package autoCalculateJourneyLeaderBoard

import (
	"math"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelRating "gitlab.com/btaskee/go-services-model-v2/grpcmodel/rating"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
)

func calculatePointInDayOfTasker(taskerList []string, fromDate, toDate *time.Time) (map[string]int, []string, error) {
	var mapPointByTaskerId = make(map[string]int)
	var maptotalRate = make(map[string]int)
	var maptotalTask = make(map[string]int)
	if fromDate != nil {
		atDate := *fromDate
		toDateTime := globalLib.GetCurrentTime(local.TimeZone)
		if toDate != nil {
			toDateTime = *toDate
		}
		for {
			mapRatingsByTaskerId, mapTasksByTasker, err := calcPointAtDay(taskerList, atDate)
			if err != nil {
				return nil, nil, err
			}
			// Calculate
			for k, v := range mapRatingsByTaskerId {
				mapPointByTaskerId[k] += pointByRatings(v)
				maptotalRate[k] += pointByRatings(v)
			}
			for k, v := range mapTasksByTasker {
				mapPointByTaskerId[k] += pointByTasks(v, k)
				maptotalTask[k] += pointByTasks(v, k)
			}
			atDate = atDate.AddDate(0, 0, 1)
			if atDate.After(toDateTime) {
				break
			}
		}
	} else {
		mapRatingsByTaskerId, mapTasksByTasker, err := calcPointAtDay(taskerList, time.Time{})
		if err != nil {
			return nil, nil, err
		}
		// Calculate
		for k, v := range mapRatingsByTaskerId {
			mapPointByTaskerId[k] += pointByRatings(v)
		}
		for k, v := range mapTasksByTasker {
			mapPointByTaskerId[k] += pointByTasks(v, k)
		}
	}
	// list tasker has point in day
	taskerHasPointInDayIds := []string{}
	for k := range mapPointByTaskerId {
		taskerHasPointInDayIds = append(taskerHasPointInDayIds, k)
	}

	return mapPointByTaskerId, taskerHasPointInDayIds, nil
}

func calcPointAtDay(taskerList []string, atDate time.Time) (map[string][]*modelRating.Rating, map[string][]*modelTask.Task, error) {
	// 1. get rating in days
	mapRatingsByTaskerId, err := getRatingsInDay(taskerList, atDate)
	if err != nil {
		return nil, nil, err
	}
	// 2. get list task done of these tasker in day
	mapTasksByTasker, err := getDoneTasksInDay(taskerList, atDate)
	if err != nil {
		return nil, nil, err
	}
	return mapRatingsByTaskerId, mapTasksByTasker, nil
}

func pointByRatings(ratings []*modelRating.Rating) int {
	total := 0
	for _, v := range ratings {
		switch v.Rate {
		case 1:
			total -= 30
		case 2:
			total -= 20
		case 3:
			total -= 10
		case 5:
			total += 15
		}
	}
	return total
}

func pointByTasks(tasks []*modelTask.Task, taskerId string) int {
	total := 0
	for _, v := range tasks {
		duration := v.Duration
		if len(v.IncreaseDurationData) > 0 {
			duration -= lib.GetIncreaseDurationNotForTasker(v.IncreaseDurationData, taskerId)
		}
		total += 7                             // 1 cv = 7đ
		total += int(math.Round(duration)) * 5 // 1h = 5đ
	}
	return total
}
