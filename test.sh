export SYNC_CRON_VN_CONFIG="../config"
export APPLICATION_MODE="test"
cover="-coverprofile=cover -coverpkg=./handlers"
case $1 in
  "1")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/1_updateTrustPoint -run UpdateTrustPoint
    else
      go test -v ./testing ${cover}/1_updateTrustPoint -run UpdateTrustPoint/^$2$
    fi
    ;;
  "89")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/89_autoMoveChatToHistory -run AutoMoveChatToHistory -timeout 1h
    else
      go test -v ./testing ${cover}/89_autoMoveChatToHistory -run AutoMoveChatToHistory/^$2$ -timeout 1h
    fi
    ;;
  "73")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/73_autoCalculateJourneyLeaderBoard/... -run CalculateJourneyLeaderBoard
    else
      go test -v ./testing ${cover}/73_autoCalculateJourneyLeaderBoard/... -run CalculateJourneyLeaderBoard/^$2$
    fi
    ;;
  "91")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/91_weeklyResetLeaderboard/ -run WeeklyResetLeaderboard -timeout 1h
    else
      go test -v ./testing ${cover}/91_weeklyResetLeaderboard/ -run WeeklyResetLeaderboard/^$2$ -timeout 1h
    fi
    ;;
  "92")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/92_businessAllocation/ -run BusinessAllocation -timeout 1h
    else
      go test -v ./testing ${cover}/92_businessAllocation/ -run BusinessAllocation/^$2$ -timeout 1h
    fi
    ;;
  "93")
    if [[ -z $2 ]]; then
      go test -v ./testing ${cover}/93_autoNotifyForCommunityNotification -run TestAutoNotifyForCommunityNotification -timeout 1h
    else
      go test -v ./testing ${cover}/93_autoNotifyForCommunityNotification -run TestAutoNotifyForCommunityNotification/^$2$ -timeout 1h
    fi
    ;;
  "99")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/99_giveRewardTaskerDoneSpecialCampaign -run GiveRewardTaskerDoneSpecialCampaign -timeout 1h
    else
      go test -v ./testing ${cover}/99_giveRewardTaskerDoneSpecialCampaign -run GiveRewardTaskerDoneSpecialCampaign/^$2$ -timeout 1h
    fi
    ;;
   "100")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/100_sendMessageFavTaskerNotResponding -run SendMessageFavTaskerNotResponding -timeout 1h
    else
      go test -v ./testing ${cover}/100_sendMessageFavTaskerNotResponding -run SendMessageFavTaskerNotResponding/^$2$ -timeout 1h
    fi
    ;;
  "101")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/101_voucherComboSubscriptionNotify -run VoucherComboSubscriptionNotify -timeout 1h
    else
      go test -v ./testing ${cover}/101_voucherComboSubscriptionNotify -run VoucherComboSubscriptionNotify/^$2$ -timeout 1h
    fi
    ;;
  "102")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/102_expiredExtendComboVoucher -run ExtendExpiredComboVoucher -timeout 1h
    else
      go test -v ./testing ${cover}/102_expiredExtendComboVoucher -run ExtendExpiredComboVoucher/^$2$ -timeout 1h
    fi
    ;;
   "106")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/101_autoCheckTaskerWorkingPlaces -run AutoCheckTaskerWorkingPlaces -timeout 1h
    else
      go test -v ./testing ${cover}/101_autoCheckTaskerWorkingPlaces -run AutoCheckTaskerWorkingPlaces/^$2$ -timeout 1h
    fi
    ;;
   "107")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/107_notifyTaskerGameCampaign -run NotifyTaskerGameCampaign -timeout 1h
    else
      go test -v ./testing ${cover}/107_notifyTaskerGameCampaign -run NotifyTaskerGameCampaign/^$2$ -timeout 1h
    fi
    ;;
  "90")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/90_autoFindTaskersHaveStarCanDoTestTraining -run AutoFindTaskersHaveStarCanDoTestTraining -timeout 1h
    else
      go test -v ./testing ${cover}/90_autoFindTaskersHaveStarCanDoTestTraining -run AutoFindTaskersHaveStarCanDoTestTraining/^$2$ -timeout 1h
    fi
    ;;
  "97")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/91_autoSendNotiAboutNewTestAndReviewToTasker -run AutoSendNotiAboutNewTestAndReviewToTasker -timeout 1h
    else
      go test -v ./testing ${cover}/91_autoSendNotiAboutNewTestAndReviewToTasker -run AutoSendNotiAboutNewTestAndReviewToTasker/^$2$ -timeout 1h
    fi
    ;;
  "96")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/92_autoSendNotiAboutTestDeadline -run AutoSendNotiAboutTestDeadline -timeout 1h
    else
      go test -v ./testing ${cover}/92_autoSendNotiAboutTestDeadline -run AutoSendNotiAboutTestDeadline/^$2$ -timeout 1h
    fi
    ;;
    "86")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/86_createAskerMonthlyReport -run CreateAskerMonthlyReport -timeout 1h
    else
      go test -v ./testing ${cover}/86_createAskerMonthlyReport -run CreateAskerMonthlyReport/^$2$ -timeout 1h
    fi
    ;;
    "109")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/109_createTaskerMonthlyReport -run CreateTaskerMonthlyReport -timeout 1h
    else
      go test -v ./testing ${cover}/109_createTaskerMonthlyReport -run CreateTaskerMonthlyReport/^$2$ -timeout 1h
    fi
    ;;
    "94")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/94_autoPostNewFeedCommunity -run AutoPostNewFeedCommunity
    else
      go test -v ./testing ${cover}/94_autoPostNewFeedCommunity -run AutoPostNewFeedCommunity/^$2$
    fi
    ;;
  "95")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/95_autoUpgradeForCommunityUser -run AutoUpgradeForCommunityUser -timeout 1h
    else
      go test -v ./testing ${cover}/95_autoUpgradeForCommunityUser -run AutoUpgradeForCommunityUser/^$2$ -timeout 1h
    fi
    ;;
  "108")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/108_createTaskerWorkingHistory -run CreateTaskerWorkingHistory -timeout 1h
    else
      go test -v ./testing ${cover}/108_createTaskerWorkingHistory -run CreateTaskerWorkingHistory/^$2$ -timeout 1h
    fi
    ;;
  "110")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/110_autoSendNotiToRemindTaskerResponseTaskForceTasker -run SendNotiToRemindTaskerResponseTaskForceTasker -timeout 1h
    else
      go test -v ./testing ${cover}/110_autoSendNotiToRemindTaskerResponseTaskForceTasker -run SendNotiToRemindTaskerResponseTaskForceTasker/^$2$ -timeout 1h
    fi
    ;;
  "111")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/111_cancelTaskForceTasker -run CancelTaskForceTasker -timeout 1h
    else
      go test -v ./testing ${cover}/111_cancelTaskForceTasker -run CancelTaskForceTasker/^$2$ -timeout 1h
    fi
    ;;
   "112")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/112_sendMessageBeforeCancelTaskForceTasker -run SendMessageBeforeTaskForceTaskerIsCancelled -timeout 1h
    else
      go test -v ./testing ${cover}/112_sendMessageBeforeCancelTaskForceTasker -run SendMessageBeforeTaskForceTaskerIsCancelled/^$2$ -timeout 1h
    fi
    ;;
  "113")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/113_autoReportTasksInfo -run AutoReportTasksInfo -timeout 1h
    else
      go test -v ./testing ${cover}/113_autoReportTasksInfo -run AutoReportTasksInfo/^$2$ -timeout 1h
    fi
    ;;
  "114")
    if [[ -z $2 ]] ;
    then
      go test -v ./testing ${cover}/114_rollbackMissingTaskActionPenalty -run RollbackMissingTaskActionPenalty -timeout 1h
    else
      go test -v ./testing ${cover}/114_rollbackMissingTaskActionPenalty -run RollbackMissingTaskActionPenalty/^$2$ -timeout 1h
    fi
    ;;
  *)
    go test -v ./testing ${cover}/... -timeout 1h
    ;;
esac
