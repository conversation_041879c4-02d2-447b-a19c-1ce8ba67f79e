package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelFATransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fatransaction"
	modelFinancialAccountVN "gitlab.com/btaskee/go-services-model-v2/grpcmodel/financialAccountVN"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPointTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pointTransaction"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSpecialCampaignTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/specialCampaignTransaction"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func TestGiveRewardTaskerDoneSpecialCampaign(t *testing.T) {
	// update rankSettings
	rankSetting := []map[string]interface{}{
		{
			"rankName": "MEMBER",
			"point":    0,
			"rate":     1,
			"text": map[string]interface{}{
				"vi": "Thành viên",
				"en": "Member",
				"ko": "회원",
				"th": "สมาชิก",
			},
		},
		{
			"rankName": "SILVER",
			"point":    200,
			"rate":     1,
			"text": map[string]interface{}{
				"vi": "Bạc",
				"en": "Silver",
				"ko": "실버",
				"th": "ระดับ Silver",
			},
		},
		{
			"rankName": "GOLD",
			"point":    600,
			"rate":     1.2,
			"text": map[string]interface{}{
				"vi": "Vàng",
				"en": "Gold",
				"ko": "금",
				"th": "ระดับ Gold",
			},
		},
		{
			"rankName": "PLATINUM",
			"point":    3000,
			"rate":     1.4,
			"text": map[string]interface{}{
				"vi": "Bạch kim",
				"en": "Platinum",
				"ko": "플래티넘",
				"th": "ระดับ Platinum",
			},
		},
	}
	giftSetting := map[string]interface{}{
		"exchangePriceToPoint": map[string]interface{}{
			"price": 10000,
			"point": 1,
		},
	}
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{"_id": "idSetting"}, bson.M{"$set": bson.M{"rankSetting": rankSetting, "giftSetting": giftSetting}})

	apiURL := "/api/v3/sync-cron-vn/give-reward-tasker-done-special-campaign"
	createdAt := globalLib.GetCurrentTime(local.TimeZone)
	createData := func() {
		startDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -3)
		endDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 3)
		CreateUser([]map[string]interface{}{
			{
				"phone":        "**********",
				"name":         "Tasker 01",
				"type":         globalConstant.USER_TYPE_TASKER,
				"referralCode": "741963",
				"createdAt":    createdAt,
			},
			{
				"phone":     "**********",
				"name":      "Tasker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"createdAt": createdAt,
			},
		})

		CreateSpecialCampaign([]map[string]any{
			{
				"_id":       "specialCampaign1",
				"name":      "special campaign vi",
				"type":      globalConstant.SPECIAL_CAMPAIGN_TYPE_REFERRAL_CAMPAIGN,
				"status":    globalConstant.SPECIAL_CAMPAIGN_STATUS_ACTIVE,
				"value":     10,
				"startDate": startDate,
				"endDate":   endDate,
				"text": map[string]interface{}{
					"vi": map[string]interface{}{
						"name":        "special campaign vi",
						"description": "des",
					},
					"en": map[string]interface{}{
						"name":        "special campaign en",
						"description": "des",
					},
					"th": map[string]interface{}{
						"name":        "special campaign th",
						"description": "des",
					},
					"id": map[string]interface{}{
						"name":        "special campaign id",
						"description": "des",
					},
					"ko": map[string]interface{}{
						"name":        "special campaign ko",
						"description": "des",
					},
				},
				"rewards": []map[string]interface{}{
					{
						"type":   globalConstant.SPECIAL_CAMPAIGN_REWARD_BPOINT,
						"amount": 150,
					},
					{
						"type":   globalConstant.SPECIAL_CAMPAIGN_REWARD_MONEY,
						"amount": 100000,
					},
				},
				"createdAt": globalLib.GetCurrentTime(local.TimeZone),
			},
		})

		CreateSpecialCampaign([]map[string]any{
			{
				"_id":                 "specialCampaign2",
				"name":                "special campaign 02 vi",
				"type":                globalConstant.SPECIAL_CAMPAIGN_TYPE_TASK_CAMPAIGN,
				"status":              globalConstant.SPECIAL_CAMPAIGN_STATUS_ACTIVE,
				"value":               10,
				"startDate":           startDate,
				"endDate":             endDate,
				"city":                []string{"Hồ Chí Minh"},
				"taskerJourneyLevels": []string{"LV1", "LV3"},
				"minRateTask":         3,
				"text": map[string]interface{}{
					"vi": map[string]interface{}{
						"name":        "special campaign 02 vi",
						"description": "des",
					},
					"en": map[string]interface{}{
						"name":        "special campaign 02 vi en",
						"description": "des",
					},
					"th": map[string]interface{}{
						"name":        "special campaign 02 vi th",
						"description": "des",
					},
					"id": map[string]interface{}{
						"name":        "special campaign 02 vi id",
						"description": "des",
					},
					"ko": map[string]interface{}{
						"name":        "special campaign 02 vi ko",
						"description": "des",
					},
				},
				"rewards": []map[string]interface{}{
					{
						"type":   globalConstant.SPECIAL_CAMPAIGN_REWARD_BPOINT,
						"amount": 200,
					},
					{
						"type":   globalConstant.SPECIAL_CAMPAIGN_REWARD_MONEY,
						"amount": 250000,
					},
				},
				"createdAt": globalLib.GetCurrentTime(local.TimeZone),
			},
		})
	}
	// ===========SPECIAL CAMPAIGN TYPE REFERRAL CAMPAIGN

	// case: tasker chưa in-process trong special transaction, win campaign
	t.Run("1", func(t *testing.T) {
		ResetData()
		createData()
		CreateUser([]map[string]interface{}{
			{
				"phone":      "0834567881",
				"name":       "Asker 02",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567882",
				"name":       "Asker 03",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567883",
				"name":       "Asker 04",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567884",
				"name":       "Asker 05",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567885",
				"name":       "Asker 06",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567886",
				"name":       "Asker 07",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567887",
				"name":       "Asker 08",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567888",
				"name":       "Asker 09",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567889",
				"name":       "Asker 10",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567890",
				"name":       "Asker 11",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt.AddDate(0, 0, 3),
			},
			{
				"phone":      "0777777777",
				"name":       "Asker 12",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
		})
		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// check user
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"point": 1, "rankInfo": 1, "fAccountId": 1, "language": 1})
					So(user, ShouldNotBeNil)

					// check notify
					var notify *modelNotification.Notification
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify)
					So(notify, ShouldBeNil)

					// check specialCampaignTransaction
					var specialCampaignTransaction *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign1"}, bson.M{}, &specialCampaignTransaction)
					So(specialCampaignTransaction, ShouldNotBeNil)
					So(specialCampaignTransaction.TaskerId, ShouldEqual, "**********")
					So(specialCampaignTransaction.CampaignId, ShouldEqual, "specialCampaign1")
					So(specialCampaignTransaction.Status, ShouldEqual, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED)
					So(specialCampaignTransaction.PercentProcess, ShouldEqual, 1.0)
					So(specialCampaignTransaction.CompletedDate, ShouldNotBeNil)
					So(specialCampaignTransaction.RewardedDate, ShouldBeNil)
					So(specialCampaignTransaction.CreatedAt, ShouldNotBeNil)
				})
			})
		})
	})

	// case: tasker chưa in-process trong special transaction, win 50% campaign, gửi notify
	t.Run("2", func(t *testing.T) {
		ResetData()
		createData()
		CreateUser([]map[string]interface{}{
			{
				"phone":      "0834567881",
				"name":       "Asker 02",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567882",
				"name":       "Asker 03",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567883",
				"name":       "Asker 04",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567884",
				"name":       "Asker 05",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567885",
				"name":       "Asker 06",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567886",
				"name":       "Asker 07",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
		})
		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// check user
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"language": 1})
					So(user, ShouldNotBeNil)

					// check specialCampaignTransaction
					var specialCampaignTransaction *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign1"}, bson.M{}, &specialCampaignTransaction)
					So(specialCampaignTransaction, ShouldNotBeNil)
					So(specialCampaignTransaction.TaskerId, ShouldEqual, "**********")
					So(specialCampaignTransaction.CampaignId, ShouldEqual, "specialCampaign1")
					So(specialCampaignTransaction.Status, ShouldEqual, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_INPROCESS)
					So(specialCampaignTransaction.PercentProcess, ShouldEqual, 0.5)
					So(specialCampaignTransaction.CompletedDate, ShouldBeNil)
					So(specialCampaignTransaction.RewardedDate, ShouldBeNil)
					So(specialCampaignTransaction.CreatedAt, ShouldNotBeNil)

					// check notify
					var notify []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify)
					So(notify, ShouldNotBeNil)
					So(len(notify), ShouldEqual, 1)
					for i, noti := range notify {
						switch i {
						case 0:
							So(noti.Title, ShouldEqual, localization.T(user.Language, "NOTIFICATION_MESSAGE_DONE_HALF_SPECIAL_CAMPAIGN_TITLE"))
							So(noti.Description, ShouldEqual, localization.T(user.Language, "NOTIFICATION_MESSAGE_DONE_HALF_SPECIAL_CAMPAIGN_BODY"))
							So(noti.NavigateTo, ShouldEqual, globalConstant.PAYLOAD_NAVIGATE_TO_NOTIFICATION_DETAIL)
							So(noti.Type, ShouldEqual, 25)
						}
					}
				})
			})
		})
	})

	// case: tasker chưa in-process trong special transaction, win 30% campaign, gửi notify
	t.Run("3", func(t *testing.T) {
		ResetData()
		createData()
		CreateUser([]map[string]interface{}{
			{
				"phone":      "0834567881",
				"name":       "Asker 02",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567882",
				"name":       "Asker 03",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567883",
				"name":       "Asker 04",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567884",
				"name":       "Asker 05",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
		})
		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// check user
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"language": 1})
					So(user, ShouldNotBeNil)

					// check specialCampaignTransaction
					var specialCampaignTransaction *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign1"}, bson.M{}, &specialCampaignTransaction)
					So(specialCampaignTransaction, ShouldNotBeNil)
					So(specialCampaignTransaction.TaskerId, ShouldEqual, "**********")
					So(specialCampaignTransaction.CampaignId, ShouldEqual, "specialCampaign1")
					So(specialCampaignTransaction.Status, ShouldEqual, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_INPROCESS)
					So(specialCampaignTransaction.PercentProcess, ShouldEqual, 0.3)
					So(specialCampaignTransaction.CompletedDate, ShouldBeNil)
					So(specialCampaignTransaction.RewardedDate, ShouldBeNil)
					So(specialCampaignTransaction.CreatedAt, ShouldNotBeNil)

					// check notify
					var notify []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify)
					So(notify, ShouldNotBeNil)
					So(len(notify), ShouldEqual, 1)
					for i, noti := range notify {
						switch i {
						case 0:
							So(noti.Title, ShouldEqual, localization.T(user.Language, "NOTIFICATION_MESSAGE_DONE_ONE_THIRD_SPECIAL_CAMPAIGN_TITLE"))
							So(noti.Description, ShouldEqual, localization.T(user.Language, "NOTIFICATION_MESSAGE_DONE_ONE_THIRD_SPECIAL_CAMPAIGN_BODY"))
							So(noti.NavigateTo, ShouldEqual, globalConstant.PAYLOAD_NAVIGATE_TO_NOTIFICATION_DETAIL)
							So(noti.Type, ShouldEqual, 25)
						}
					}
				})
			})
		})
	})

	// case: tasker đã in-process 30% trong special transaction, win 50% campaign, gửi notify
	t.Run("4", func(t *testing.T) {
		ResetData()
		createData()
		CreateUser([]map[string]interface{}{
			{
				"phone":      "0834567881",
				"name":       "Asker 02",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567882",
				"name":       "Asker 03",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567883",
				"name":       "Asker 04",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567884",
				"name":       "Asker 05",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567885",
				"name":       "Asker 06",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567886",
				"name":       "Asker 07",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
		})

		CreateSpecialCampaignTransaction([]map[string]interface{}{
			{
				"phone":          "**********",
				"taskerId":       "**********",
				"campaignId":     "specialCampaign1",
				"campaignName":   "special campaign vi",
				"status":         globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_INPROCESS,
				"percentProcess": 0.3,
				"createdAt":      globalLib.GetCurrentTime(local.TimeZone),
			},
		})

		CreateNotification([]map[string]interface{}{
			{
				"userId":      "**********",
				"type":        25,
				"description": globalLib.LocalizeServiceName("vi", localization.GetLocalizeObject("NOTIFICATION_MESSAGE_DONE_ONE_THIRD_SPECIAL_CAMPAIGN_BODY")),
				"title":       globalLib.LocalizeServiceName("vi", localization.GetLocalizeObject("NOTIFICATION_MESSAGE_DONE_ONE_THIRD_SPECIAL_CAMPAIGN_TITLE")),
				"navigateTo":  "NotificationDetail",
			},
		})

		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// check user
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"language": 1})
					So(user, ShouldNotBeNil)

					// check specialCampaignTransaction
					var specialCampaignTransaction *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign1"}, bson.M{}, &specialCampaignTransaction)
					So(specialCampaignTransaction, ShouldNotBeNil)
					So(specialCampaignTransaction.TaskerId, ShouldEqual, "**********")
					So(specialCampaignTransaction.CampaignId, ShouldEqual, "specialCampaign1")
					So(specialCampaignTransaction.Status, ShouldEqual, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_INPROCESS)
					So(specialCampaignTransaction.PercentProcess, ShouldEqual, 0.5)
					So(specialCampaignTransaction.CompletedDate, ShouldBeNil)
					So(specialCampaignTransaction.RewardedDate, ShouldBeNil)
					So(specialCampaignTransaction.CreatedAt, ShouldNotBeNil)

					// check notify
					var notify []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify)
					So(notify, ShouldNotBeNil)
					So(len(notify), ShouldEqual, 2)
					for i, noti := range notify {
						switch i {
						case 0:
							So(noti.Title, ShouldEqual, localization.T(user.Language, "NOTIFICATION_MESSAGE_DONE_ONE_THIRD_SPECIAL_CAMPAIGN_TITLE"))
							So(noti.Description, ShouldEqual, localization.T(user.Language, "NOTIFICATION_MESSAGE_DONE_ONE_THIRD_SPECIAL_CAMPAIGN_BODY"))
							So(noti.NavigateTo, ShouldEqual, globalConstant.PAYLOAD_NAVIGATE_TO_NOTIFICATION_DETAIL)
							So(noti.Type, ShouldEqual, 25)
						case 1:
							So(noti.Title, ShouldEqual, localization.T(user.Language, "NOTIFICATION_MESSAGE_DONE_HALF_SPECIAL_CAMPAIGN_TITLE"))
							So(noti.Description, ShouldEqual, localization.T(user.Language, "NOTIFICATION_MESSAGE_DONE_HALF_SPECIAL_CAMPAIGN_BODY"))
							So(noti.NavigateTo, ShouldEqual, globalConstant.PAYLOAD_NAVIGATE_TO_NOTIFICATION_DETAIL)
							So(noti.Type, ShouldEqual, 25)
						}
					}
				})
			})
		})
	})

	// case: tasker đã in-process 50% trong special transaction, win 100% campaign
	t.Run("5", func(t *testing.T) {
		ResetData()
		createData()
		CreateUser([]map[string]interface{}{
			{
				"phone":      "0834567881",
				"name":       "Asker 02",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567882",
				"name":       "Asker 03",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567883",
				"name":       "Asker 04",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567884",
				"name":       "Asker 05",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567885",
				"name":       "Asker 06",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567886",
				"name":       "Asker 07",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567887",
				"name":       "Asker 08",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567888",
				"name":       "Asker 09",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567889",
				"name":       "Asker 10",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567890",
				"name":       "Asker 11",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt.AddDate(0, 0, 3),
			},
			{
				"phone":      "0777777777",
				"name":       "Asker 12",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
		})

		CreateSpecialCampaignTransaction([]map[string]interface{}{
			{
				"phone":          "**********",
				"taskerId":       "**********",
				"campaignId":     "specialCampaign1",
				"campaignName":   "special campaign vi",
				"status":         globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_INPROCESS,
				"percentProcess": 0.5,
				"createdAt":      globalLib.GetCurrentTime(local.TimeZone),
			},
		})

		CreateNotification([]map[string]interface{}{
			{
				"userId":      "**********",
				"type":        25,
				"description": globalLib.LocalizeServiceName("vi", localization.GetLocalizeObject("NOTIFICATION_MESSAGE_DONE_ONE_THIRD_SPECIAL_CAMPAIGN_BODY")),
				"title":       globalLib.LocalizeServiceName("vi", localization.GetLocalizeObject("NOTIFICATION_MESSAGE_DONE_ONE_THIRD_SPECIAL_CAMPAIGN_TITLE")),
				"navigateTo":  "NotificationDetail",
			},
			{
				"userId":      "**********",
				"type":        25,
				"description": globalLib.LocalizeServiceName("vi", localization.GetLocalizeObject("NOTIFICATION_MESSAGE_DONE_HALF_SPECIAL_CAMPAIGN_BODY")),
				"title":       globalLib.LocalizeServiceName("vi", localization.GetLocalizeObject("NOTIFICATION_MESSAGE_DONE_HALF_SPECIAL_CAMPAIGN_TITLE")),
				"navigateTo":  "NotificationDetail",
			},
		})

		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// check user
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"language": 1})
					So(user, ShouldNotBeNil)

					// check specialCampaignTransaction
					var specialCampaignTransaction *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign1"}, bson.M{}, &specialCampaignTransaction)
					So(specialCampaignTransaction, ShouldNotBeNil)
					So(specialCampaignTransaction.TaskerId, ShouldEqual, "**********")
					So(specialCampaignTransaction.CampaignId, ShouldEqual, "specialCampaign1")
					So(specialCampaignTransaction.Status, ShouldEqual, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED)
					So(specialCampaignTransaction.PercentProcess, ShouldEqual, 1.0)
					So(specialCampaignTransaction.CompletedDate, ShouldNotBeNil)
					So(specialCampaignTransaction.RewardedDate, ShouldBeNil)
					So(specialCampaignTransaction.CreatedAt, ShouldNotBeNil)

					// check notify
					var notify []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify)
					So(notify, ShouldNotBeNil)
					So(len(notify), ShouldEqual, 2)
					for i, noti := range notify {
						switch i {
						case 0:
							So(noti.Title, ShouldEqual, localization.T(user.Language, "NOTIFICATION_MESSAGE_DONE_ONE_THIRD_SPECIAL_CAMPAIGN_TITLE"))
							So(noti.Description, ShouldEqual, localization.T(user.Language, "NOTIFICATION_MESSAGE_DONE_ONE_THIRD_SPECIAL_CAMPAIGN_BODY"))
							So(noti.NavigateTo, ShouldEqual, globalConstant.PAYLOAD_NAVIGATE_TO_NOTIFICATION_DETAIL)
							So(noti.Type, ShouldEqual, 25)
						case 1:
							So(noti.Title, ShouldEqual, localization.T(user.Language, "NOTIFICATION_MESSAGE_DONE_HALF_SPECIAL_CAMPAIGN_TITLE"))
							So(noti.Description, ShouldEqual, localization.T(user.Language, "NOTIFICATION_MESSAGE_DONE_HALF_SPECIAL_CAMPAIGN_BODY"))
							So(noti.NavigateTo, ShouldEqual, globalConstant.PAYLOAD_NAVIGATE_TO_NOTIFICATION_DETAIL)
							So(noti.Type, ShouldEqual, 25)
						}
					}
				})
			})
		})
	})

	// case: tasker đã win 100% campaign
	t.Run("6", func(t *testing.T) {
		ResetData()
		createData()
		CreateUser([]map[string]interface{}{
			{
				"phone":      "0834567881",
				"name":       "Asker 02",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567882",
				"name":       "Asker 03",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567883",
				"name":       "Asker 04",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567884",
				"name":       "Asker 05",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567885",
				"name":       "Asker 06",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567886",
				"name":       "Asker 07",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567887",
				"name":       "Asker 08",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567888",
				"name":       "Asker 09",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567889",
				"name":       "Asker 10",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
			{
				"phone":      "0834567890",
				"name":       "Asker 11",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt.AddDate(0, 0, 3),
			},
			{
				"phone":      "0777777777",
				"name":       "Asker 12",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "741963",
				"createdAt":  createdAt,
			},
		})

		CreateSpecialCampaignTransaction([]map[string]interface{}{
			{
				"phone":          "**********",
				"taskerId":       "**********",
				"campaignId":     "specialCampaign1",
				"campaignName":   "special campaign vi",
				"status":         globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED,
				"completedDate":  globalLib.GetCurrentTime(local.TimeZone),
				"percentProcess": 1.0,
				"createdAt":      globalLib.GetCurrentTime(local.TimeZone),
			},
		})

		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// check user
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"language": 1})
					So(user, ShouldNotBeNil)

					// check specialCampaignTransaction
					var specialCampaignTransaction *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign1"}, bson.M{}, &specialCampaignTransaction)
					So(specialCampaignTransaction, ShouldNotBeNil)
					So(specialCampaignTransaction.TaskerId, ShouldEqual, "**********")
					So(specialCampaignTransaction.CampaignId, ShouldEqual, "specialCampaign1")
					So(specialCampaignTransaction.Status, ShouldEqual, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED)
					So(specialCampaignTransaction.PercentProcess, ShouldEqual, 1.0)
					So(specialCampaignTransaction.CompletedDate, ShouldNotBeNil)
					So(specialCampaignTransaction.RewardedDate, ShouldBeNil)
					So(specialCampaignTransaction.CreatedAt, ShouldNotBeNil)

					// check notify
					var notify []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify)
					So(notify, ShouldBeNil)
				})
			})
		})
	})

	// ===========SPECIAL CAMPAIGN TYPE TASK CAMPAIGN

	// case: tasker chưa in-process trong special transaction, win campaign (apply cho tất cả city)
	t.Run("7", func(t *testing.T) {
		ResetData()
		createData()
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567881",
				"name":      "Asker 02",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": createdAt,
			},
		})
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_OFFICE_CLEANING,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{ // task này không nằm trong thời gian chạy campaign không tính
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt.AddDate(0, 0, 5),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
		})

		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// check user
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"language": 1})
					So(user, ShouldNotBeNil)

					// check notify
					var notify *modelNotification.Notification
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify)
					So(notify, ShouldBeNil)

					// check specialCampaignTransaction
					var specialCampaignTransaction *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign2"}, bson.M{}, &specialCampaignTransaction)
					So(specialCampaignTransaction, ShouldNotBeNil)
					So(specialCampaignTransaction.TaskerId, ShouldEqual, "**********")
					So(specialCampaignTransaction.CampaignId, ShouldEqual, "specialCampaign2")
					So(specialCampaignTransaction.Status, ShouldEqual, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED)
					So(specialCampaignTransaction.PercentProcess, ShouldEqual, 1.0)
					So(specialCampaignTransaction.CompletedDate, ShouldNotBeNil)
					So(specialCampaignTransaction.RewardedDate, ShouldBeNil)
					So(specialCampaignTransaction.CreatedAt, ShouldNotBeNil)
				})
			})
		})
	})

	// case: tasker chưa in-process trong special transaction, win 50% campaign (có rating 2 sao thấp hơn minTaskRate => không win được campaign)
	t.Run("8", func(t *testing.T) {
		ResetData()
		createData()
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567881",
				"name":      "Asker 02",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": createdAt,
			},
		})
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_OFFICE_CLEANING,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{ // task này không nằm trong thời gian chạy campaign không tính
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt.AddDate(0, 0, 5),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
		})

		CreateRating([]map[string]interface{}{
			{
				"askerPhone":  "0834567881",
				"taskerPhone": "**********",
				"rate":        2,
				"taskId":      taskIds[0],
			},
		})

		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// check user
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"language": 1})
					So(user, ShouldNotBeNil)

					// check specialCampaignTransaction
					var specialCampaignTransaction *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign2"}, bson.M{}, &specialCampaignTransaction)
					So(specialCampaignTransaction, ShouldBeNil)
				})
			})
		})
	})

	// case: tasker chưa in-process trong special transaction, vì journey level đủ điều kiện không tham gia campaign
	t.Run("9", func(t *testing.T) {
		ResetData()
		createData()
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567881",
				"name":      "Asker 02",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": createdAt,
			},
		})
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_OFFICE_CLEANING,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{ // task này không nằm trong thời gian chạy campaign không tính
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt.AddDate(0, 0, 5),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
		})

		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// check user
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"language": 1})
					So(user, ShouldNotBeNil)

					// check specialCampaignTransaction
					var specialCampaignTransaction *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign2"}, bson.M{}, &specialCampaignTransaction)
					So(specialCampaignTransaction, ShouldNotBeNil)
					So(specialCampaignTransaction.TaskerId, ShouldEqual, "**********")
					So(specialCampaignTransaction.CampaignId, ShouldEqual, "specialCampaign2")
					So(specialCampaignTransaction.Status, ShouldEqual, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED)
					So(specialCampaignTransaction.PercentProcess, ShouldEqual, 1.0)
					So(specialCampaignTransaction.CompletedDate, ShouldNotBeNil)
					So(specialCampaignTransaction.RewardedDate, ShouldBeNil)
					So(specialCampaignTransaction.CreatedAt, ShouldNotBeNil)

					// check notify
					var notify []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify)
					So(notify, ShouldBeNil)
				})
			})
		})
	})

	// case: campaign chỉ apply VSML => tasker win 30%
	t.Run("10", func(t *testing.T) {
		ResetData()
		startDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -3)
		endDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 3)

		// list service có 1 lượt mở quà
		var listService []*modelService.Service
		services1 := []string{globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER}
		globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": bson.M{"$in": services1}}, bson.M{"_id": 1}, &listService)

		serviceIds := []string{}
		for _, s := range listService {
			serviceIds = append(serviceIds, s.GetXId())
		}

		CreateSpecialCampaign([]map[string]any{
			{
				"_id":                 "specialCampaign2",
				"name":                "special campaign 02 vi",
				"type":                globalConstant.SPECIAL_CAMPAIGN_TYPE_TASK_CAMPAIGN,
				"status":              globalConstant.SPECIAL_CAMPAIGN_STATUS_ACTIVE,
				"value":               10,
				"startDate":           startDate,
				"endDate":             endDate,
				"city":                []string{"Hồ Chí Minh"},
				"taskerJourneyLevels": []string{"LV2", "LV3"},
				"minRateTask":         3,
				"applyForServices":    serviceIds,
				"text": map[string]interface{}{
					"vi": map[string]interface{}{
						"name":        "special campaign 02 vi",
						"description": "des",
					},
					"en": map[string]interface{}{
						"name":        "special campaign 02 vi en",
						"description": "des",
					},
					"th": map[string]interface{}{
						"name":        "special campaign 02 vi th",
						"description": "des",
					},
					"id": map[string]interface{}{
						"name":        "special campaign 02 vi id",
						"description": "des",
					},
					"ko": map[string]interface{}{
						"name":        "special campaign 02 vi ko",
						"description": "des",
					},
				},
				"rewards": []map[string]interface{}{
					{
						"type":   globalConstant.SPECIAL_CAMPAIGN_REWARD_BPOINT,
						"amount": 200,
					},
					{
						"type":   globalConstant.SPECIAL_CAMPAIGN_REWARD_MONEY,
						"amount": 250000,
					},
				},
				"createdAt": globalLib.GetCurrentTime(local.TimeZone),
			},
		})

		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567881",
				"name":      "Asker 02",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": createdAt,
			},
			{
				"phone":     "**********",
				"name":      "Tasker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"createdAt": createdAt,
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
				},
			},
		})
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{ // task này không nằm trong thời gian chạy campaign không tính
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt.AddDate(0, 0, 5),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
		})

		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// check user
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"language": 1})
					So(user, ShouldNotBeNil)

					// check specialCampaignTransaction
					var specialCampaignTransaction *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign2"}, bson.M{}, &specialCampaignTransaction)
					So(specialCampaignTransaction, ShouldNotBeNil)
					So(specialCampaignTransaction.TaskerId, ShouldEqual, "**********")
					So(specialCampaignTransaction.CampaignId, ShouldEqual, "specialCampaign2")
					So(specialCampaignTransaction.Status, ShouldEqual, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_INPROCESS)
					So(specialCampaignTransaction.PercentProcess, ShouldEqual, 0.3)
					So(specialCampaignTransaction.CompletedDate, ShouldBeNil)
					So(specialCampaignTransaction.RewardedDate, ShouldBeNil)
					So(specialCampaignTransaction.CreatedAt, ShouldNotBeNil)

					// check notify
					var notify []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify)
					So(notify, ShouldNotBeNil)
					So(len(notify), ShouldEqual, 1)
					for i, noti := range notify {
						switch i {
						case 0:
							So(noti.Title, ShouldEqual, localization.T(user.Language, "NOTIFICATION_MESSAGE_DONE_ONE_THIRD_SPECIAL_CAMPAIGN_TITLE"))
							So(noti.Description, ShouldEqual, localization.T(user.Language, "NOTIFICATION_MESSAGE_DONE_ONE_THIRD_SPECIAL_CAMPAIGN_BODY"))
							So(noti.NavigateTo, ShouldEqual, globalConstant.PAYLOAD_NAVIGATE_TO_NOTIFICATION_DETAIL)
							So(noti.Type, ShouldEqual, 25)
						}
					}
				})
			})
		})
	})

	// case: tasker đã win 100% campaign
	t.Run("11", func(t *testing.T) {
		ResetData()
		createData()
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567881",
				"name":      "Asker 02",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": createdAt,
			},
		})

		CreateSpecialCampaignTransaction([]map[string]interface{}{
			{
				"phone":          "**********",
				"taskerId":       "**********",
				"campaignId":     "specialCampaign2",
				"campaignName":   "special campaign vi",
				"status":         globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED,
				"completedDate":  globalLib.GetCurrentTime(local.TimeZone),
				"percentProcess": 1.0,
				"createdAt":      globalLib.GetCurrentTime(local.TimeZone),
			},
		})

		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// check user
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"language": 1})
					So(user, ShouldNotBeNil)

					// check specialCampaignTransaction
					var specialCampaignTransaction *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign2"}, bson.M{}, &specialCampaignTransaction)
					So(specialCampaignTransaction, ShouldNotBeNil)
					So(specialCampaignTransaction.TaskerId, ShouldEqual, "**********")
					So(specialCampaignTransaction.CampaignId, ShouldEqual, "specialCampaign2")
					So(specialCampaignTransaction.Status, ShouldEqual, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED)
					So(specialCampaignTransaction.PercentProcess, ShouldEqual, 1.0)
					So(specialCampaignTransaction.CompletedDate, ShouldNotBeNil)
					So(specialCampaignTransaction.RewardedDate, ShouldBeNil)
					So(specialCampaignTransaction.CreatedAt, ShouldNotBeNil)

					// check notify
					var notify []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify)
					So(notify, ShouldBeNil)
				})
			})
		})
	})

	// SEND REWARD
	// case: gửi reward sau khi đã qua thời gian campaign
	t.Run("12", func(t *testing.T) {
		ResetData()
		startDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -10)
		endDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1)
		CreateUser([]map[string]interface{}{
			{
				"phone":        "**********",
				"name":         "Tasker 01",
				"type":         globalConstant.USER_TYPE_TASKER,
				"referralCode": "741963",
				"createdAt":    createdAt,
			},
			{
				"phone":     "**********",
				"name":      "Tasker 02",
				"type":      globalConstant.USER_TYPE_TASKER,
				"createdAt": createdAt,
			},
		})

		CreateSpecialCampaign([]map[string]any{
			{
				"_id":                 "specialCampaign1",
				"name":                "special campaign 01",
				"type":                globalConstant.SPECIAL_CAMPAIGN_TYPE_TASK_CAMPAIGN,
				"status":              globalConstant.SPECIAL_CAMPAIGN_STATUS_ACTIVE,
				"value":               10,
				"startDate":           startDate,
				"endDate":             endDate,
				"city":                []string{"Hồ Chí Minh"},
				"taskerJourneyLevels": []string{"LV2", "LV3"},
				"minRateTask":         3,
				"text": map[string]interface{}{
					"vi": map[string]interface{}{
						"name":        "special campaign 01 vi",
						"description": "des",
					},
					"en": map[string]interface{}{
						"name":        "special campaign 01 vi en",
						"description": "des",
					},
					"th": map[string]interface{}{
						"name":        "special campaign 01 vi th",
						"description": "des",
					},
					"id": map[string]interface{}{
						"name":        "special campaign 01 vi id",
						"description": "des",
					},
					"ko": map[string]interface{}{
						"name":        "special campaign 01 vi ko",
						"description": "des",
					},
				},
				"rewards": []map[string]interface{}{
					{
						"type":   globalConstant.SPECIAL_CAMPAIGN_REWARD_BPOINT,
						"amount": 200,
					},
					{
						"type":             globalConstant.SPECIAL_CAMPAIGN_REWARD_MONEY,
						"amount":           250000,
						"applyAccountType": globalConstant.FA_TRANSACTION_ACCOUNT_TYPE_PROMOTION,
					},
				},
				"createdAt": globalLib.GetCurrentTime(local.TimeZone),
			},
		})

		CreateSpecialCampaignTransaction([]map[string]interface{}{
			{
				"phone":          "**********",
				"taskerId":       "**********",
				"campaignId":     "specialCampaign1",
				"campaignName":   "special campaign 01",
				"status":         globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED,
				"completedDate":  globalLib.GetCurrentTime(local.TimeZone),
				"percentProcess": 1.0,
				"createdAt":      globalLib.GetCurrentTime(local.TimeZone),
			},
			{
				"phone":          "**********",
				"taskerId":       "**********",
				"campaignId":     "specialCampaign1",
				"campaignName":   "special campaign 01",
				"status":         globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED,
				"completedDate":  globalLib.GetCurrentTime(local.TimeZone),
				"percentProcess": 1.0,
				"createdAt":      globalLib.GetCurrentTime(local.TimeZone),
			},
		})

		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// check user
					user**********, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"point": 1, "rankInfo": 1, "fAccountId": 1, "language": 1})
					So(user**********, ShouldNotBeNil)

					// check faTransaction**********
					var faTransaction********** *modelFATransaction.FinancialAccountTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], bson.M{"userId": "**********", "source.specialCampaignId": "specialCampaign1"}, bson.M{}, &faTransaction**********)
					So(faTransaction**********, ShouldNotBeNil)
					So(faTransaction**********.UserId, ShouldEqual, "**********")
					So(faTransaction**********.Type, ShouldEqual, globalConstant.FA_TRANSACTION_TYPE_DEPOSIT)
					So(faTransaction**********.AccountType, ShouldEqual, globalConstant.FA_TRANSACTION_ACCOUNT_TYPE_PROMOTION)
					So(faTransaction**********.Amount, ShouldEqual, 250000)
					So(faTransaction**********.Source.Name, ShouldEqual, globalConstant.FA_TRANSACTION_SOURCE_NAME_SPECIAL_CAMPAIGN)
					So(faTransaction**********.Source.SpecialCampaignId, ShouldEqual, "specialCampaign1")

					// check financialAccountVN
					var financialAccountVN********** *modelFinancialAccountVN.FinancialAccountVN
					globalDataAccess.GetOneById(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], user**********.FAccountId, bson.M{}, &financialAccountVN**********)
					So(financialAccountVN**********, ShouldNotBeNil)
					So(financialAccountVN**********.Promotion, ShouldEqual, 250000)
					So(financialAccountVN**********.FMainAccount, ShouldEqual, 0)

					// check pointTransaction
					var pointTransaction********** *modelPointTransaction.PointTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_POINT_TRANSACTION[local.ISO_CODE], bson.M{"userId": "**********", "source.specialCampaignId": "specialCampaign1"}, bson.M{}, &pointTransaction**********)
					So(pointTransaction**********, ShouldNotBeNil)
					So(pointTransaction**********.UserId, ShouldEqual, "**********")
					So(pointTransaction**********.Type, ShouldEqual, globalConstant.FA_TRANSACTION_TYPE_DEPOSIT)
					So(pointTransaction**********.Point, ShouldEqual, 200)
					So(pointTransaction**********.Source.Name, ShouldEqual, globalConstant.FA_TRANSACTION_SOURCE_NAME_SPECIAL_CAMPAIGN)
					So(pointTransaction**********.Source.SpecialCampaignId, ShouldEqual, "specialCampaign1")

					// check point, rank user
					So(user**********.Point, ShouldEqual, 200)
					So(user**********.RankInfo.Point, ShouldEqual, 200)
					So(user**********.RankInfo.RankName, ShouldEqual, globalConstant.ASKER_RANK_NAME_MEMBER)

					// check specialCampaignTransaction**********
					var specialCampaignTransaction********** *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign1"}, bson.M{}, &specialCampaignTransaction**********)
					So(specialCampaignTransaction**********, ShouldNotBeNil)
					So(specialCampaignTransaction**********, ShouldNotBeNil)
					So(specialCampaignTransaction**********.TaskerId, ShouldEqual, "**********")
					So(specialCampaignTransaction**********.CampaignId, ShouldEqual, "specialCampaign1")
					So(specialCampaignTransaction**********.Status, ShouldEqual, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_REWARDED)
					So(specialCampaignTransaction**********.CompletedDate, ShouldNotBeNil)
					So(specialCampaignTransaction**********.RewardedDate, ShouldNotBeNil)
					So(specialCampaignTransaction**********.CreatedAt, ShouldNotBeNil)

					// check notify
					var notify********** []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify**********)
					So(notify**********, ShouldNotBeNil)
					So(len(notify**********), ShouldEqual, 2)

					for _, v := range notify********** {
						So(v.Description, ShouldBeIn, []string{localization.T("vi", "NOTIFICATION_MESSAGE_WIN_SPECIAL_CAMPAIGN_BODY")})
						So(v.Title, ShouldBeIn, []string{localization.T("vi", "NOTIFICATION_MESSAGE_WIN_SPECIAL_CAMPAIGN_TITLE")})
						So(v.NavigateTo, ShouldBeIn, []string{"bPointsHistory", "Finance"})
						So(v.Type, ShouldBeIn, []int32{6})
					}

					// ========= user **********
					// check user
					user**********, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"point": 1, "rankInfo": 1, "fAccountId": 1, "language": 1})
					So(user**********, ShouldNotBeNil)

					// check faTransaction**********
					var faTransaction********** *modelFATransaction.FinancialAccountTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], bson.M{"userId": "**********", "source.specialCampaignId": "specialCampaign1"}, bson.M{}, &faTransaction**********)
					So(faTransaction**********, ShouldNotBeNil)
					So(faTransaction**********.UserId, ShouldEqual, "**********")
					So(faTransaction**********.Type, ShouldEqual, globalConstant.FA_TRANSACTION_TYPE_DEPOSIT)
					So(faTransaction**********.AccountType, ShouldEqual, globalConstant.FA_TRANSACTION_ACCOUNT_TYPE_PROMOTION)
					So(faTransaction**********.Amount, ShouldEqual, 250000)
					So(faTransaction**********.Source.Name, ShouldEqual, globalConstant.FA_TRANSACTION_SOURCE_NAME_SPECIAL_CAMPAIGN)
					So(faTransaction**********.Source.SpecialCampaignId, ShouldEqual, "specialCampaign1")

					// check financialAccountVN
					var financialAccountVN********** *modelFinancialAccountVN.FinancialAccountVN
					globalDataAccess.GetOneById(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], user**********.FAccountId, bson.M{}, &financialAccountVN**********)
					So(financialAccountVN**********, ShouldNotBeNil)
					So(financialAccountVN**********.Promotion, ShouldEqual, 250000)
					So(financialAccountVN**********.FMainAccount, ShouldEqual, 0)

					// check pointTransaction
					var pointTransaction********** *modelPointTransaction.PointTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_POINT_TRANSACTION[local.ISO_CODE], bson.M{"userId": "**********", "source.specialCampaignId": "specialCampaign1"}, bson.M{}, &pointTransaction**********)
					So(pointTransaction**********, ShouldNotBeNil)
					So(pointTransaction**********.UserId, ShouldEqual, "**********")
					So(pointTransaction**********.Type, ShouldEqual, globalConstant.FA_TRANSACTION_TYPE_DEPOSIT)
					So(pointTransaction**********.Point, ShouldEqual, 200)
					So(pointTransaction**********.Source.Name, ShouldEqual, globalConstant.FA_TRANSACTION_SOURCE_NAME_SPECIAL_CAMPAIGN)
					So(pointTransaction**********.Source.SpecialCampaignId, ShouldEqual, "specialCampaign1")

					// check point, rank user
					So(user**********.Point, ShouldEqual, 200)
					So(user**********.RankInfo.Point, ShouldEqual, 200)
					So(user**********.RankInfo.RankName, ShouldEqual, globalConstant.ASKER_RANK_NAME_MEMBER)

					// check specialCampaignTransaction**********
					var specialCampaignTransaction********** *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign1"}, bson.M{}, &specialCampaignTransaction**********)
					So(specialCampaignTransaction**********, ShouldNotBeNil)
					So(specialCampaignTransaction**********, ShouldNotBeNil)
					So(specialCampaignTransaction**********.TaskerId, ShouldEqual, "**********")
					So(specialCampaignTransaction**********.CampaignId, ShouldEqual, "specialCampaign1")
					So(specialCampaignTransaction**********.Status, ShouldEqual, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_REWARDED)
					So(specialCampaignTransaction**********.CompletedDate, ShouldNotBeNil)
					So(specialCampaignTransaction**********.RewardedDate, ShouldNotBeNil)
					So(specialCampaignTransaction**********.CreatedAt, ShouldNotBeNil)

					// check notify
					var notify********** []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify**********)
					So(notify**********, ShouldNotBeNil)
					So(len(notify**********), ShouldEqual, 2)

					for _, v := range notify********** {
						So(v.Description, ShouldBeIn, []string{localization.T("vi", "NOTIFICATION_MESSAGE_WIN_SPECIAL_CAMPAIGN_BODY")})
						So(v.Title, ShouldBeIn, []string{localization.T("vi", "NOTIFICATION_MESSAGE_WIN_SPECIAL_CAMPAIGN_TITLE")})
						So(v.NavigateTo, ShouldBeIn, []string{"bPointsHistory", "Finance"})
						So(v.Type, ShouldBeIn, []int32{6})
					}
				})
			})
		})
	})

	// case: không gửi reward đang trong thời gian campaign
	t.Run("13", func(t *testing.T) {
		ResetData()
		startDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -10)
		endDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 10)
		CreateUser([]map[string]interface{}{
			{
				"phone":        "**********",
				"name":         "Tasker 01",
				"type":         globalConstant.USER_TYPE_TASKER,
				"referralCode": "741963",
				"createdAt":    createdAt,
			},
			{
				"phone":     "**********",
				"name":      "Tasker 02",
				"type":      globalConstant.USER_TYPE_TASKER,
				"createdAt": createdAt,
			},
		})

		CreateSpecialCampaign([]map[string]any{
			{
				"_id":                 "specialCampaign1",
				"name":                "special campaign 01",
				"type":                globalConstant.SPECIAL_CAMPAIGN_TYPE_TASK_CAMPAIGN,
				"status":              globalConstant.SPECIAL_CAMPAIGN_STATUS_ACTIVE,
				"value":               10,
				"startDate":           startDate,
				"endDate":             endDate,
				"city":                []string{"Hồ Chí Minh"},
				"taskerJourneyLevels": []string{"LV2", "LV3"},
				"minRateTask":         3,
				"text": map[string]interface{}{
					"vi": map[string]interface{}{
						"name":        "special campaign 01 vi",
						"description": "des",
					},
					"en": map[string]interface{}{
						"name":        "special campaign 01 vi en",
						"description": "des",
					},
					"th": map[string]interface{}{
						"name":        "special campaign 01 vi th",
						"description": "des",
					},
					"id": map[string]interface{}{
						"name":        "special campaign 01 vi id",
						"description": "des",
					},
					"ko": map[string]interface{}{
						"name":        "special campaign 01 vi ko",
						"description": "des",
					},
				},
				"rewards": []map[string]interface{}{
					{
						"type":   globalConstant.SPECIAL_CAMPAIGN_REWARD_BPOINT,
						"amount": 200,
					},
					{
						"type":             globalConstant.SPECIAL_CAMPAIGN_REWARD_MONEY,
						"amount":           250000,
						"applyAccountType": globalConstant.FA_TRANSACTION_ACCOUNT_TYPE_PROMOTION,
					},
				},
				"createdAt": globalLib.GetCurrentTime(local.TimeZone),
			},
		})

		CreateSpecialCampaignTransaction([]map[string]interface{}{
			{
				"phone":          "**********",
				"taskerId":       "**********",
				"campaignId":     "specialCampaign1",
				"campaignName":   "special campaign 01",
				"status":         globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED,
				"completedDate":  globalLib.GetCurrentTime(local.TimeZone),
				"percentProcess": 1.0,
				"createdAt":      globalLib.GetCurrentTime(local.TimeZone),
			},
			{
				"phone":          "**********",
				"taskerId":       "**********",
				"campaignId":     "specialCampaign1",
				"campaignName":   "special campaign 01",
				"status":         globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED,
				"completedDate":  globalLib.GetCurrentTime(local.TimeZone),
				"percentProcess": 1.0,
				"createdAt":      globalLib.GetCurrentTime(local.TimeZone),
			},
		})

		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// check user
					user**********, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"point": 1, "rankInfo": 1, "fAccountId": 1, "language": 1})
					So(user**********, ShouldNotBeNil)

					// check faTransaction**********
					var faTransaction********** *modelFATransaction.FinancialAccountTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], bson.M{"userId": "**********", "source.specialCampaignId": "specialCampaign1"}, bson.M{}, &faTransaction**********)
					So(faTransaction**********, ShouldBeNil)

					// check financialAccountVN
					var financialAccountVN********** *modelFinancialAccountVN.FinancialAccountVN
					globalDataAccess.GetOneById(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], user**********.FAccountId, bson.M{}, &financialAccountVN**********)
					So(financialAccountVN**********, ShouldNotBeNil)
					So(financialAccountVN**********.Promotion, ShouldEqual, 0)
					So(financialAccountVN**********.FMainAccount, ShouldEqual, 0)

					// check pointTransaction
					var pointTransaction********** *modelPointTransaction.PointTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_POINT_TRANSACTION[local.ISO_CODE], bson.M{"userId": "**********", "source.specialCampaignId": "specialCampaign1"}, bson.M{}, &pointTransaction**********)
					So(pointTransaction**********, ShouldBeNil)

					// check specialCampaignTransaction**********
					var specialCampaignTransaction********** *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign1"}, bson.M{}, &specialCampaignTransaction**********)
					So(specialCampaignTransaction**********, ShouldNotBeNil)
					So(specialCampaignTransaction**********.TaskerId, ShouldEqual, "**********")
					So(specialCampaignTransaction**********.CampaignId, ShouldEqual, "specialCampaign1")
					So(specialCampaignTransaction**********.Status, ShouldEqual, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED)
					So(specialCampaignTransaction**********.CompletedDate, ShouldNotBeNil)
					So(specialCampaignTransaction**********.RewardedDate, ShouldBeNil)
					So(specialCampaignTransaction**********.CreatedAt, ShouldNotBeNil)

					// check notify
					var notify********** []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify**********)
					So(notify**********, ShouldBeNil)

					// ========= user **********
					// check user
					user**********, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"point": 1, "rankInfo": 1, "fAccountId": 1, "language": 1})
					So(user**********, ShouldNotBeNil)

					// check faTransaction**********
					var faTransaction********** *modelFATransaction.FinancialAccountTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], bson.M{"userId": "**********", "source.specialCampaignId": "specialCampaign1"}, bson.M{}, &faTransaction**********)
					So(faTransaction**********, ShouldBeNil)

					// check financialAccountVN
					var financialAccountVN********** *modelFinancialAccountVN.FinancialAccountVN
					globalDataAccess.GetOneById(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], user**********.FAccountId, bson.M{}, &financialAccountVN**********)
					So(financialAccountVN**********, ShouldNotBeNil)
					So(financialAccountVN**********.Promotion, ShouldEqual, 0)
					So(financialAccountVN**********.FMainAccount, ShouldEqual, 0)

					// check pointTransaction
					var pointTransaction********** *modelPointTransaction.PointTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_POINT_TRANSACTION[local.ISO_CODE], bson.M{"userId": "**********", "source.specialCampaignId": "specialCampaign1"}, bson.M{}, &pointTransaction**********)
					So(pointTransaction**********, ShouldBeNil)

					// check specialCampaignTransaction**********
					var specialCampaignTransaction********** *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign1"}, bson.M{}, &specialCampaignTransaction**********)
					So(specialCampaignTransaction**********, ShouldNotBeNil)
					So(specialCampaignTransaction**********, ShouldNotBeNil)
					So(specialCampaignTransaction**********, ShouldNotBeNil)
					So(specialCampaignTransaction**********.TaskerId, ShouldEqual, "**********")
					So(specialCampaignTransaction**********.CampaignId, ShouldEqual, "specialCampaign1")
					So(specialCampaignTransaction**********.Status, ShouldEqual, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED)
					So(specialCampaignTransaction**********.CompletedDate, ShouldNotBeNil)
					So(specialCampaignTransaction**********.RewardedDate, ShouldBeNil)
					So(specialCampaignTransaction**********.CreatedAt, ShouldNotBeNil)

					// check notify
					var notify********** []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify**********)
					So(notify**********, ShouldBeNil)
				})
			})
		})
	})

	// case:user đã nhận được quà (không gửi) && user không win campaign (không gửi).
	t.Run("14", func(t *testing.T) {
		ResetData()
		startDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -10)
		endDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 10)
		CreateUser([]map[string]interface{}{
			{
				"phone":        "**********",
				"name":         "Tasker 01",
				"type":         globalConstant.USER_TYPE_TASKER,
				"referralCode": "741963",
				"createdAt":    createdAt,
			},
			{
				"phone":     "**********",
				"name":      "Tasker 02",
				"type":      globalConstant.USER_TYPE_TASKER,
				"createdAt": createdAt,
			},
		})

		CreateSpecialCampaign([]map[string]any{
			{
				"_id":                 "specialCampaign1",
				"name":                "special campaign 01",
				"type":                globalConstant.SPECIAL_CAMPAIGN_TYPE_TASK_CAMPAIGN,
				"status":              globalConstant.SPECIAL_CAMPAIGN_STATUS_ACTIVE,
				"value":               10,
				"startDate":           startDate,
				"endDate":             endDate,
				"city":                []string{"Hồ Chí Minh"},
				"taskerJourneyLevels": []string{"LV2", "LV3"},
				"minRateTask":         3,
				"text": map[string]interface{}{
					"vi": map[string]interface{}{
						"name":        "special campaign 01 vi",
						"description": "des",
					},
					"en": map[string]interface{}{
						"name":        "special campaign 01 vi en",
						"description": "des",
					},
					"th": map[string]interface{}{
						"name":        "special campaign 01 vi th",
						"description": "des",
					},
					"id": map[string]interface{}{
						"name":        "special campaign 01 vi id",
						"description": "des",
					},
					"ko": map[string]interface{}{
						"name":        "special campaign 01 vi ko",
						"description": "des",
					},
				},
				"rewards": []map[string]interface{}{
					{
						"type":   globalConstant.SPECIAL_CAMPAIGN_REWARD_BPOINT,
						"amount": 200,
					},
					{
						"type":             globalConstant.SPECIAL_CAMPAIGN_REWARD_MONEY,
						"amount":           250000,
						"applyAccountType": globalConstant.FA_TRANSACTION_ACCOUNT_TYPE_PROMOTION,
					},
				},
				"createdAt": globalLib.GetCurrentTime(local.TimeZone),
			},
		})

		CreateSpecialCampaignTransaction([]map[string]interface{}{
			{
				"phone":          "**********",
				"taskerId":       "**********",
				"campaignId":     "specialCampaign1",
				"campaignName":   "special campaign 01",
				"status":         globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_REWARDED,
				"completedDate":  globalLib.GetCurrentTime(local.TimeZone),
				"rewardedDate":   globalLib.GetCurrentTime(local.TimeZone),
				"percentProcess": 1.0,
				"createdAt":      globalLib.GetCurrentTime(local.TimeZone),
			},
			{
				"phone":          "**********",
				"taskerId":       "**********",
				"campaignId":     "specialCampaign1",
				"campaignName":   "special campaign 01",
				"status":         globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_INPROCESS,
				"percentProcess": 1.0,
				"createdAt":      globalLib.GetCurrentTime(local.TimeZone),
			},
		})

		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// check user
					user**********, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"point": 1, "rankInfo": 1, "fAccountId": 1, "language": 1})
					So(user**********, ShouldNotBeNil)

					// check faTransaction**********
					var faTransaction********** *modelFATransaction.FinancialAccountTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], bson.M{"userId": "**********", "source.specialCampaignId": "specialCampaign1"}, bson.M{}, &faTransaction**********)
					So(faTransaction**********, ShouldBeNil)

					// check financialAccountVN
					var financialAccountVN********** *modelFinancialAccountVN.FinancialAccountVN
					globalDataAccess.GetOneById(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], user**********.FAccountId, bson.M{}, &financialAccountVN**********)
					So(financialAccountVN**********, ShouldNotBeNil)
					So(financialAccountVN**********.Promotion, ShouldEqual, 0)
					So(financialAccountVN**********.FMainAccount, ShouldEqual, 0)

					// check pointTransaction
					var pointTransaction********** *modelPointTransaction.PointTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_POINT_TRANSACTION[local.ISO_CODE], bson.M{"userId": "**********", "source.specialCampaignId": "specialCampaign1"}, bson.M{}, &pointTransaction**********)
					So(pointTransaction**********, ShouldBeNil)

					// check specialCampaignTransaction**********
					var specialCampaignTransaction********** *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign1"}, bson.M{}, &specialCampaignTransaction**********)
					So(specialCampaignTransaction**********, ShouldNotBeNil)
					So(specialCampaignTransaction**********.TaskerId, ShouldEqual, "**********")
					So(specialCampaignTransaction**********.CampaignId, ShouldEqual, "specialCampaign1")
					So(specialCampaignTransaction**********.Status, ShouldEqual, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_REWARDED)
					So(specialCampaignTransaction**********.CompletedDate, ShouldNotBeNil)
					So(specialCampaignTransaction**********.RewardedDate, ShouldNotBeNil)
					So(specialCampaignTransaction**********.CreatedAt, ShouldNotBeNil)

					// check notify
					var notify********** []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify**********)
					So(notify**********, ShouldBeNil)

					// ========= user **********
					// check user
					user**********, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"point": 1, "rankInfo": 1, "fAccountId": 1, "language": 1})
					So(user**********, ShouldNotBeNil)

					// check faTransaction**********
					var faTransaction********** *modelFATransaction.FinancialAccountTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], bson.M{"userId": "**********", "source.specialCampaignId": "specialCampaign1"}, bson.M{}, &faTransaction**********)
					So(faTransaction**********, ShouldBeNil)

					// check financialAccountVN
					var financialAccountVN********** *modelFinancialAccountVN.FinancialAccountVN
					globalDataAccess.GetOneById(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], user**********.FAccountId, bson.M{}, &financialAccountVN**********)
					So(financialAccountVN**********, ShouldNotBeNil)
					So(financialAccountVN**********.Promotion, ShouldEqual, 0)
					So(financialAccountVN**********.FMainAccount, ShouldEqual, 0)

					// check pointTransaction
					var pointTransaction********** *modelPointTransaction.PointTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_POINT_TRANSACTION[local.ISO_CODE], bson.M{"userId": "**********", "source.specialCampaignId": "specialCampaign1"}, bson.M{}, &pointTransaction**********)
					So(pointTransaction**********, ShouldBeNil)

					// check specialCampaignTransaction**********
					var specialCampaignTransaction********** *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign1"}, bson.M{}, &specialCampaignTransaction**********)
					So(specialCampaignTransaction**********, ShouldNotBeNil)
					So(specialCampaignTransaction**********, ShouldNotBeNil)
					So(specialCampaignTransaction**********, ShouldNotBeNil)
					So(specialCampaignTransaction**********.TaskerId, ShouldEqual, "**********")
					So(specialCampaignTransaction**********.CampaignId, ShouldEqual, "specialCampaign1")
					So(specialCampaignTransaction**********.Status, ShouldEqual, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_INPROCESS)
					So(specialCampaignTransaction**********.CompletedDate, ShouldBeNil)
					So(specialCampaignTransaction**********.RewardedDate, ShouldBeNil)
					So(specialCampaignTransaction**********.CreatedAt, ShouldNotBeNil)

					// check notify
					var notify********** []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify**********)
					So(notify**********, ShouldBeNil)
				})
			})
		})
	})

	// case: tasker chưa in-process trong special transaction, task là đối tác không tham gia campaign
	t.Run("15", func(t *testing.T) {
		ResetData()
		createData()
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567881",
				"name":      "Asker 02",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": createdAt,
			},
		})
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_OFFICE_CLEANING,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{ // task này không nằm trong thời gian chạy campaign không tính
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt.AddDate(0, 0, 5),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
		})

		modelUser.UpdateOneById(local.ISO_CODE, "**********", bson.M{"$set": bson.M{"companyInfo": bson.M{"name": "COMPANY HOME MOVING"}}})
		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// check user
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"language": 1})
					So(user, ShouldNotBeNil)

					// check specialCampaignTransaction
					var specialCampaignTransaction *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign2"}, bson.M{}, &specialCampaignTransaction)
					So(specialCampaignTransaction, ShouldBeNil)

					// check notify
					var notify []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify)
					So(notify, ShouldBeNil)
				})
			})
		})
	})

	// case: tasker chưa in-process trong special transaction, journey level không đủ điều kiện tham gia campaign
	t.Run("16", func(t *testing.T) {
		ResetData()
		createData()
		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567881",
				"name":      "Asker 02",
				"type":      globalConstant.USER_TYPE_ASKER,
				"createdAt": createdAt,
			},
		})
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_OFFICE_CLEANING,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
			{ // task này không nằm trong thời gian chạy campaign không tính
				"askerPhone":  "0834567881",
				"serviceName": globalConstant.SERVICE_KEY_NAME_AIR_CONDITIONER,
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"taskerRated": false,
				"date":        createdAt.AddDate(0, 0, 5),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "**********",
					},
				},
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
		})

		modelUser.UpdateOneById(local.ISO_CODE, "**********", bson.M{"$set": bson.M{"journeyInfo": bson.M{"level": "LV2"}}})
		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)

		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// check user
					user, _ := modelUser.GetOneByQuery(local.ISO_CODE, bson.M{"_id": "**********"}, bson.M{"language": 1})
					So(user, ShouldNotBeNil)

					// check specialCampaignTransaction
					var specialCampaignTransaction *modelSpecialCampaignTransaction.SpecialCampaignTransaction
					globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], bson.M{"taskerId": "**********", "campaignId": "specialCampaign2"}, bson.M{}, &specialCampaignTransaction)
					So(specialCampaignTransaction, ShouldBeNil)

					// check notify
					var notify []*modelNotification.Notification
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"userId": "**********"}, bson.M{}, &notify)
					So(notify, ShouldBeNil)
				})
			})
		})
	})
}
