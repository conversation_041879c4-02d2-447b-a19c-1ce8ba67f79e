package updateTrustPoint

import (
	"fmt"
	"log"

	"github.com/robfig/cron/v3"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
)

var c *cron.Cron
var cfg = config.GetConfig()
var isRunning = false
var paramsEnough = true
var runAt string

func init() {
	var cronConfig map[string]interface{}
	if cfg != nil {
		cronConfig = cfg.ScheduleSyncCron["runtrustpoint"]
	}

	// Check if params enough
	if cronConfig["run_at"] == nil || cronConfig["params"] == nil {
		paramsEnough = false
		lib.PostToSlackNotStart("RunTrustPoint")
		return
	}

	// Get runAt string for sync cron
	params := cronConfig["params"].([]interface{})
	runAt = fmt.Sprintf(cronConfig["run_at"].(string), params[0].(int))

	// Check if config has is_run = true
	if cronConfig["is_run"] == nil || !cronConfig["is_run"].(bool) {
		lib.PostToSlackNotStart("RunTrustPoint")
		return
	}

	StartCron()
}

func StartCron() {
	c := cron.New(cron.WithLocation(local.TimeZone))
	c.AddFunc(runAt, RunTrustPoint)
	c.Start()
	isRunning = true
}

func Run(action int) {
	// action == 1 -> Run sync cron once now
	if action == lib.RUN_NOW {
		RunTrustPoint()
	}
	// action == 2 -> Start sync cron with schedule config
	if action == lib.START && paramsEnough && !isRunning {
		StartCron()
	}
	// action == 3 -> Stop running sync cron
	if action == lib.STOP && isRunning {
		c.Stop()
		c = nil
		isRunning = false
	}
}
func RunTrustPoint() {
	log.Println("Start RunTrustPoint Process")
	defer log.Println("Finish RunTrustPoint Process")

	tasks := getTasks()
	//group task by userID
	arrayTasksByUserId := groupByUserId(tasks)
	if len(arrayTasksByUserId) == 0 {
		return
	}
	updateTrustPointToUser(arrayTasksByUserId)
}

type ModelUserTrustPointByTask struct {
	Task        []*modelTask.Task
	NumberPoint int32
}
