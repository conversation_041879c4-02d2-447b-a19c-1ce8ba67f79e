package autoFindTaskersHaveStarCanDoTestTraining

import (
	"encoding/json"
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelServiceChannel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/serviceChannel"
	modelTrainingV2 "gitlab.com/btaskee/go-services-model-v2/grpcmodel/trainingTaskerV2"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getCourseStartDate_new(courses []*modelTrainingV2.Course) []interface{} {
	cacheTaskerListByServiceId := make(map[string][]string)

	var res []interface{}
	for _, course := range courses {
		// 1.
		taskersExistCourseStartDateIds, err := getTaskersExistCourseStartDate(course)
		if err != nil {
			msg := fmt.Sprintf("AutoFindTaskersHaveStarCanDoTestTraining %s: Lỗi getTaskersExistCourseStartDate , err: %s ", local.ISO_CODE, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
			// NOTE: Continue process another course
			continue
		}

		// 2.
		taskersInServiceChannelIds := getListTaskerInServiceChannel(course, cacheTaskerListByServiceId)

		// 3.
		taskersCourseStartDateOfThisCourse := initNewTaskersCourseStartDate(course, taskersInServiceChannelIds, taskersExistCourseStartDateIds)
		if len(taskersCourseStartDateOfThisCourse) > 0 {
			res = append(res, taskersCourseStartDateOfThisCourse...)
		}
	}

	return res
}

// 1. Get list tasker has task of this course
func getTaskersExistCourseStartDate(course *modelTrainingV2.Course) ([]string, error) {
	taskersCourseStartDate, err := handlers.GetCoursesStartDate(bson.M{"courseId": course.XId}, bson.M{"taskerId": 1})
	if err != nil {
		return nil, err
	}

	taskersExistCourseStartDateIds := []string{}
	for _, v := range taskersCourseStartDate {
		taskersExistCourseStartDateIds = append(taskersExistCourseStartDateIds, v.TaskerId)
	}
	return taskersExistCourseStartDateIds, nil
}

// 2. Get list tasker in service channel
func getListTaskerInServiceChannel(course *modelTrainingV2.Course, cacheTaskerListByServiceId map[string][]string) []string {
	if len(course.RelatedServices) == 0 {
		return nil
	}

	taskerListInServices := []string{}
	serviceIdsNeedToQuery := []string{}

	for _, relatedService := range course.RelatedServices {
		taskerList, ok := cacheTaskerListByServiceId[relatedService.XId]
		// Nếu danh sách tasker đã được cache trong request này thì chỉ cần thêm danh sách tasker vào trong toàn bộ những tasker cần thêm bài test
		if ok {
			taskerListInServices = append(taskerListInServices, taskerList...)
			continue
		}
		// Nếu danh sách tasker chưa được cache thì cần query từ db
		serviceIdsNeedToQuery = append(serviceIdsNeedToQuery, relatedService.XId)
	}

	// Lấy danh sách service cần dùng cho course này
	if len(serviceIdsNeedToQuery) > 0 {
		var serviceChannels []*modelServiceChannel.ServiceChannel
		err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE],
			bson.M{"serviceId": bson.M{"$in": serviceIdsNeedToQuery}},
			bson.M{"serviceId": 1, "taskerList": 1},
			&serviceChannels,
		)
		// Trường hợp lỗi không cần xử lý, chỉ cần log và tiếp tục. Những tasker này sẽ được process lại ở lần chạy tiếp theo
		if err != nil {
			msg := fmt.Sprintf("AutoFindTaskersHaveStarCanDoTestTraining %s: Lỗi getTaskersInServiceChannel , err: %s ", local.ISO_CODE, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
		}
		// Cache danh sách tasker theo serviceId để dùng cho loop tiếp theo
		for _, v := range serviceChannels {
			if len(v.GetTaskerList()) > 0 {
				cacheTaskerListByServiceId[v.GetServiceId()] = v.GetTaskerList()
				taskerListInServices = append(taskerListInServices, v.GetTaskerList()...)
			}
		}
	}
	return globalLib.UniqString(taskerListInServices)
}

// 3. Init new taskers course start date
func initNewTaskersCourseStartDate(course *modelTrainingV2.Course, taskersInServiceChannelIds, taskersExistCourseStartDateIds []string) []interface{} {
	var result []interface{}
	// Tasker in service channel and not exist in course start date
	query := bson.M{
		"type": globalConstant.USER_TYPE_TASKER,
		"status": bson.M{
			"$nin": []string{
				globalConstant.USER_STATUS_INACTIVE,
				globalConstant.USER_STATUS_BLOCKED,
				globalConstant.USER_STATUS_DISABLED,
			},
		},
		"isoCode": local.ISO_CODE,
	}
	// 1. Course condition
	if course.Condition != nil && course.Condition.ByTasker != nil && course.Condition.ByTasker.MinimumStar > 0 {
		query["avgRating"] = bson.M{"$lt": course.Condition.ByTasker.MinimumStar}
	}
	if len(course.Cities) > 0 {
		query["workingPlaces.city"] = bson.M{"$in": course.Cities}
	}

	// 2. List tasker
	if len(taskersExistCourseStartDateIds) > 0 {
		query["_id"] = bson.M{"$nin": taskersExistCourseStartDateIds}
	}

	// Case 1: Course không có service
	var users []*modelUsers.Users
	var err error
	if len(course.RelatedServices) == 0 {
		users, err = handlers.GetUsers(query, bson.M{"_id": 1})
		if err != nil && err != mongo.ErrNoDocuments {
			queryData, _ := json.Marshal(query)
			text := fmt.Sprintf("AutoFindTaskersHaveStarCanDoTestTraining %s: Lỗi getUsers. query: %s, err: %s ", local.ISO_CODE, string(queryData), err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], text)
		}
	} else {
		// Array string này đã loại trừ những tasker đã có trong course start date
		taskerIds := globalLib.DifferenceArray(taskersInServiceChannelIds, taskersExistCourseStartDateIds)
		if len(taskerIds) == 0 {
			return nil
		}
		taskerIds = globalLib.UniqString(taskerIds)

		// Get user by chunk
		chunkSize := 500
		chunkTaskerIds := globalLib.SplitArrayToChunk(taskerIds, chunkSize)
		for _, vChunkTaskerIds := range chunkTaskerIds {
			query["_id"] = bson.M{"$in": vChunkTaskerIds}
			chunkTaskers, err := handlers.GetUsers(query, bson.M{"_id": 1})
			if err != nil && err != mongo.ErrNoDocuments {
				queryData, _ := json.Marshal(query)
				text := fmt.Sprintf("AutoFindTaskersHaveStarCanDoTestTraining %s: Lỗi getUsers. query: %s, err: %s ", local.ISO_CODE, string(queryData), err.Error())
				globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], text)
			}
			users = append(users, chunkTaskers...)
		}
	}

	// Case 2: Course có service
	taskerIds := globalLib.DifferenceArray(taskersInServiceChannelIds, taskersExistCourseStartDateIds)
	if len(taskerIds) == 0 {
		return nil
	}

	for _, user := range users {
		courseStartDate := &modelTrainingV2.CourseStartDate{
			CourseId:   course.XId,
			TaskerId:   user.XId,
			StartDate:  globalLib.GetCurrentTimestamp(local.TimeZone),
			DeadlineIn: course.DeadlineIn,
		}
		result = append(result, courseStartDate)
	}

	return result
}
