import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import {
  trackingScheduleStatus,
} from '/server/helper-function';
import _ from 'lodash';

const getTimeRange = function () {
  const startFrom = moment().add(5, 'minutes').startOf('hour');
  const endTo = moment().add(8, 'd').endOf('day').startOf('hour');
  const timeRange = [];
  for (let t = startFrom; t <= endTo; t = t.add(1, 'h')) {
    timeRange.push(t.toDate());
  }
  return timeRange;
};

// Get number of all waiting tasks (posted, waiting asker confirmation)
const getWaitingTasks = function (time, city, serviceId) {
  return Meteor.appCollection.Task.find({
    status: {$in: ['POSTED', 'WAITING_ASKER_CONFIRMATION']},
    'taskPlace.city': city,
    serviceId: serviceId,
  }).count();
};

const getBusyTasker = function (time, city, serviceId) {
  const timeCompare = moment(time).add(1, 'seconds').toDate();

  const tasks = Meteor.appCollection.Task.aggregate(
    [
      {
        $match: {
          status: { $in: ['DONE', 'CONFIRMED'] },
          date: { $lte: timeCompare },
          serviceId,
          'taskPlace.city': city,
        },
      }, {
        $project: {
          start: '$date',
          end: { $add: ['$date', { $multiply: ['$duration', 3600000] }] },
          acceptedTasker: 1,
        }
      }, {
        $match: {
          end: { $gt: timeCompare }
        },
      }
    ],
  );
  return _.uniq(_.union.apply(this, tasks.map(task => (
    _.get(task, 'acceptedTasker[0].taskerId', null) ? [task.acceptedTasker[0].taskerId] : []
  ))));
};

const getFreeTasker = function (city, serviceId, busyTaskers) {
  const arrTaskerInService = Meteor.appCollection.ServiceChannel.findOne({ serviceId }).taskerList || [];
  const allTaskerIds = Meteor.appCollection.users.find({
    type: 'TASKER',
    status: 'ACTIVE',
    'workingPlaces.city': city,
  }).map(tasker => (tasker._id));
  return _.pullAll(_.intersection(arrTaskerInService, allTaskerIds), busyTaskers);
};

const getAvailableTasker = function (city, serviceId, busyTaskers) {
  const taskerWorkInTwoWeek = Meteor.appCollection.Task.find({
    serviceId,
    'taskPlace.city': city,
    date: {
      $gte: moment().subtract(7, 'days').toDate(),
      $lte: new Date(),
    }
  }).map(task => (_.get(task, 'acceptedTasker[0].taskerId', null) ? [task.acceptedTasker[0].taskerId] : []));
  return _.pullAll(_.uniq(_.union.apply(this, taskerWorkInTwoWeek)), busyTaskers);
};

const estParamInTime = function (time, city, serviceId) {
  const busyTaskers = getBusyTasker(time, city, serviceId);
  const freeTaskers = getFreeTasker(city, serviceId, busyTaskers);
  const availableTaskers = getAvailableTasker(city, serviceId, busyTaskers);
  return {
    busyTaskers,
    freeTaskers,
    availableTaskers,
  };
};

const estimateRushHour = () => {
  var message = null;
  try {
    // get workingPlaces.
    const workingPlaces = [];
    Meteor.appCollection.WorkingPlaces.find().forEach((country) => {
      country.cities.forEach((city) => {
        workingPlaces.push(city.name);
      });
    });
    const serviceIds = Meteor.appCollection.Service.find({
      status: 'ACTIVE'
    }).map(service => (service._id));
    const timeRanges = getTimeRange();

    // Remove
    Meteor.appCollection.TaskDistribution.remove({
      time: { $lt: moment().subtract(2, 'hours').toDate() }
    });

    workingPlaces.forEach((city) => {
      serviceIds.forEach((serviceId) => {
        timeRanges.forEach((time) => {

          // Number of pending tasks (posted + waiting)
          const numberPendingTasks = getWaitingTasks(time, city, serviceId);

          // Synced cron Run every 5 mins. So remove old records and insert the new one.
          const existsDistribution = Meteor.appCollection.TaskDistribution.findOne({ city, serviceId, time });
          if ( existsDistribution ) {
            Meteor.appCollection.TaskDistribution.remove({ _id: existsDistribution._id });
          }

          // if not exist, create new
          const { busyTaskers, freeTaskers, availableTaskers } = estParamInTime(time, city, serviceId);

          // endAvailableNumber = Available Taskers - all pendding Tasks at real time
          const endAvailableNumber = Math.max(0, availableTaskers.length - numberPendingTasks);

          // endTotalTaskers = Available Taskers + busy Taskers
          const endTotalTaskers = Math.max(1, availableTaskers.length + busyTaskers.length)

          // ration = available Taskers - all pending Tasks / total Taskers
          const ratio = ( endAvailableNumber / endTotalTaskers ) * 100;

          Meteor.appCollection.TaskDistribution.insert({ city, serviceId, time, busyTaskers, freeTaskers, availableTaskers, ratio });
        });
      });
    });

    // when task canceled => remove busyTaskers, add to availableTasker
    Meteor.appCollection.Task.find({
      updatedAt: {
        $gte: moment().startOf('hour').subtract(1, 'h').toDate(),
      },
      status: 'CANCELED',
    }).forEach((task) => {
      if (_.get(task, 'acceptedTasker.length', 0) === 1) {
        Meteor.appCollection.TaskDistribution.find({
          city: task.taskPlace.city,
          serviceId: task.serviceId,
          time: {
            $gte: moment(task.date).startOf('hour').toDate(),
            $lt: moment(task.date).add(task.duration, 'hour').startOf('hour').toDate(),
          },
        }).forEach((taskDistribution) => {
          const nAvailable = taskDistribution.availableTaskers.length + 1;
          const nBusy = taskDistribution.busyTaskers.length - 1;
          Meteor.appCollection.TaskDistribution.update({
            _id: taskDistribution._id,
          }, {
            $pull: { busyTaskers: task.acceptedTasker[0].taskerId },
            $addToSet: {
              freeTaskers: task.acceptedTasker[0].taskerId,
              availableTaskers: task.acceptedTasker[0].taskerId,
            },
            $set: {ratio: (nAvailable / (nAvailable + nBusy) * 100)}
          }, {
            multi: true,
          });
        });
      }
    });
    // when task confirmed => add busyTaskers, remove available tasker.
    Meteor.appCollection.Task.find({
      updatedAt: {
        $gte: moment().startOf('hour').subtract(1, 'h').toDate(),
      },
      status: 'CONFIRMED',
    }).forEach((task) => {
      if (task.acceptedTasker.length === 1) {
        Meteor.appCollection.TaskDistribution.find({
          city: task.taskPlace.city,
          serviceId: task.serviceId,
          time: {
            $gte: moment(task.date).startOf('hour').toDate(),
            $lt: moment(task.date).add(task.duration, 'hour').startOf('hour').toDate(),
          }
        }).forEach((taskDistribution) => {
          const nAvailable = taskDistribution.availableTaskers.length - 1;
          const nBusy = taskDistribution.busyTaskers.length + 1;
          Meteor.appCollection.TaskDistribution.update({
            _id: taskDistribution._id,
          }, {
            $addToSet: { busyTaskers: task.acceptedTasker[0].taskerId },
            $pull: {
              freeTaskers: task.acceptedTasker[0].taskerId,
              availableTaskers: task.acceptedTasker[0].taskerId,
            },
            $set: {ratio: (nAvailable / (nAvailable + nBusy) * 100)}
          }, {
            multi: true,
          });
        });
      }
    });
  } catch (e) {
    message = e;
  }
  // Tracking
  trackingScheduleStatus({name: 'EstimateRushHour', nextRunAt: SyncedCron.nextScheduledAtDate('estimateRushHour'), message});
};

export default function () {
  Meteor.methods({
    estimateRushHour() {
      SyncedCron.add({
        name: 'estimateRushHour',
        schedule(parser) {
          // Run at every 5 mins
          return parser.cron('0 0/5 * 1/1 * ? *', true);
        },
        job() {
          estimateRushHour();
        },
      });
    },
    estimateRushHourNow() {
      estimateRushHour();
    },
  });
}
