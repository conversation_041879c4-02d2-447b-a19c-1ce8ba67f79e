package bkafka

import (
	"fmt"
	"time"

	"github.com/IBM/sarama"
	"go.uber.org/zap"
)

// Consumer represents a Sarama consumer group consumer
type Consumer struct {
	ready chan bool

	handler      func([]byte) error
	retryHandler func([]byte) error

	topic           string
	retryTopic      string
	deadLetterTopic string

	brokers []string
}

const (
	MAX_RETRY      = 3
	RETRY_INTERVAL = 20 * time.Second
)

// Setup is run at the beginning of a new session, before ConsumeClaim
func (consumer *Consumer) Setup(sarama.ConsumerGroupSession) error {
	// Mark the consumer as ready
	close(consumer.ready)
	return nil
}

// Cleanup is run at the end of a session, once all ConsumeClaim goroutines have exited
func (consumer *Consumer) Cleanup(sarama.ConsumerGroupSession) error {
	return nil
}

// ConsumeClaim must start a consumer loop of ConsumerGroupClaim's Messages().
// Once the Messages() channel is closed, the Handler must finish its processing
// loop and exit.
func (consumer *Consumer) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	// NOTE:
	// Do not move the code below to a goroutine.
	// The `ConsumeClaim` itself is called within a goroutine, see:
	// https://github.com/IBM/sarama/blob/main/consumer_group.go#L27-L29
	for {
		select {
		case message, ok := <-claim.Messages():
			if !ok {
				logger.Debug("Message channel was closed")
				continue
			}

			// Convert to Message struct
			m := &Message{
				message: message,
				session: session,
			}
			err := consumer.processMessage(m)
			if err != nil {
				logger.Error("Error processing message", zap.Error(err))
				continue
			}

		// Should return when `session.Context()` is done.
		// If not, will raise `ErrRebalanceInProgress` or `read tcp <ip>:<port>: i/o timeout` when kafka rebalance. see:
		// https://github.com/IBM/sarama/issues/1192
		case <-session.Context().Done():
			return nil
		}
	}
}

func (consumer *Consumer) processMessage(m *Message) error {
	switch m.message.Topic {
	case consumer.topic:
		return consumer.processMessageMainTopic(m)
	case consumer.retryTopic:
		return consumer.processMessageRetryTopic(m)
	default:
		return fmt.Errorf("unknown topic %s", m.message.Topic)
	}
}

func (consumer *Consumer) processMessageMainTopic(m *Message) error {
	logger.Debug("Processing message from mainTopic", zap.String("message", string(m.message.Value)))

	// Main process
	err := consumer.handler(m.message.Value)

	// 1. If success
	if err == nil {
		logger.Debug("Message from mainTopic claiming success", zap.String("message", string(m.message.Value)))
		m.session.MarkMessage(m.message, "")
		return nil
	}

	m.session.MarkMessage(m.message, "")
	consumer.sendToRetryQueue(m, err)
	logger.Error("Message from mainTopic claiming failed", zap.String("message", string(m.message.Value)), zap.String("error", err.Error()))
	return nil
}

func (consumer *Consumer) processMessageRetryTopic(m *Message) error {
	logger.Debug("Processing message from retryTopic", zap.String("message", string(m.message.Value)))
	// Set retry backoff time
	retryCount := m.getRetryCountHeader()
	retryCount++
	m.setRetryCountHeader(retryCount)

	logger.Debug("Message from retryTopic claiming", zap.String("message", string(m.message.Value)), zap.Int("retryCount", retryCount))
	if retryCount > 0 {
		time.Sleep(time.Duration(retryCount) * RETRY_INTERVAL) // 5, 10, 15 seconds
	}

	// Main process
	err := consumer.retryHandler(m.message.Value)

	// 1. If success
	if err == nil {
		logger.Debug("Message from retryTopic claiming success", zap.String("message", string(m.message.Value)), zap.Int("retryCount", retryCount))
		m.session.MarkMessage(m.message, "")
		return nil
	}

	// 2. If failed and CAN retry
	if canRetryMessage(retryCount) {
		m.session.MarkMessage(m.message, "")
		consumer.sendToRetryQueue(m, err)
		logger.Error("Message claiming failed", zap.String("message", string(m.message.Value)), zap.String("error", err.Error()), zap.Int("retryCount", retryCount))
		return nil
	}

	// 3. If failed and CANNOT retry
	m.session.MarkMessage(m.message, "")
	consumer.sendToDeadLetterQueue(m, err)
	logger.Error("Message claiming failed max retry", zap.String("message", string(m.message.Value)), zap.Int("retryCount", retryCount))
	return nil
}

func canRetryMessage(retryCount int) bool {
	return retryCount < MAX_RETRY
}

func (consumer *Consumer) sendToDeadLetterQueue(m *Message, err error) error {
	if consumer.deadLetterTopic == "" {
		return nil
	}
	return consumer.sendToQueue(m, consumer.deadLetterTopic, err)
}

func (consumer *Consumer) sendToRetryQueue(m *Message, err error) error {
	if consumer.retryTopic == "" {
		return nil
	}
	return consumer.sendToQueue(m, consumer.retryTopic, err)
}

func (consumer *Consumer) sendToQueue(m *Message, topic string, err error) error {
	if err != nil {
		m.pushErrorToHeader(err)
	}
	// Produce message
	mq, err := NewKafkaMQ()
	if err != nil {
		return err
	}

	mq.brokers = consumer.brokers
	mq.newProducer()

	headers := []sarama.RecordHeader{}
	for _, header := range m.message.Headers {
		headers = append(headers, sarama.RecordHeader{
			Key:   header.Key,
			Value: header.Value,
		})
	}
	err = mq.produceMessage(&sarama.ProducerMessage{
		Topic:   topic,
		Key:     sarama.StringEncoder(m.message.Key),
		Value:   sarama.StringEncoder(m.message.Value),
		Headers: headers,
	})
	if err != nil {
		return err
	}
	return nil
}
