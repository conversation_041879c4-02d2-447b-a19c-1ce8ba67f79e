package grpcEventTH

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcEventTH"
	"google.golang.org/grpc"
)

func ConnectGRPCEventTH(endpoint string) (grpcEventTH.EventTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcEventTH.NewEventTHClient(connect)
	return client, connect, err
}
