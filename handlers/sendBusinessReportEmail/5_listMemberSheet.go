package sendBusinessReportEmail

import (
	"github.com/xuri/excelize/v2"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/localization"
)

func listMemberSheet(language string, f *excelize.File, activeMembers []*MemberInfo) {
	sheetName := "Sheet1" // from template
	defaultHeight := 24.0
	setDefaultCellMemberSheet(language, f, sheetName, len(activeMembers))
	noColumn, nameColumn, phoneColumn, groupColumn, createdAtColumn := 1, 2, 3, 4, 5 // Column index: A, B, C, D, E
	startRow := 4

	for i, member := range activeMembers {
		// No. column A
		cell, _ := excelize.CoordinatesToCellName(noColumn, i+startRow)
		f.SetCellValue(sheetName, cell, i+1)

		// Name column B
		cell, _ = excelize.CoordinatesToCellName(nameColumn, i+startRow)
		f.SetCellValue(sheetName, cell, member.Name)

		// Phone column C
		cell, _ = excelize.CoordinatesToCellName(phoneColumn, i+startRow)
		f.SetCellValue(sheetName, cell, member.Phone)

		// Group (LevelName) column D
		cell, _ = excelize.CoordinatesToCellName(groupColumn, i+startRow)
		f.SetCellValue(sheetName, cell, member.LevelName)

		// Date joined (CreatedAt) column E
		cell, _ = excelize.CoordinatesToCellName(createdAtColumn, i+startRow)
		f.SetCellValue(sheetName, cell, member.CreatedAt.AsTime().In(local.TimeZone).Format(formatTime))

		f.SetRowHeight(sheetName, i+startRow, defaultHeight)
	}

	// border
	startCell, _ := excelize.CoordinatesToCellName(noColumn, startRow)
	endCell, _ := excelize.CoordinatesToCellName(createdAtColumn, len(activeMembers)+startRow-1)
	setBorderStyle(f, sheetName, startCell, endCell, false)
}

func setDefaultCellMemberSheet(language string, f *excelize.File, sheetName string, totalMember int) {
	f.SetCellValue(sheetName, "A1", localization.T(language, "BUSINESS_REPORT_SHEET_1_TITLE"))
	f.SetCellValue(sheetName, "A2", localization.T(language, "BUSINESS_REPORT_SHEET_1_TOTAL_MEMBER", totalMember))
	f.SetCellValue(sheetName, "A3", localization.T(language, "BUSINESS_REPORT_SHEET_HEADER_NO"))
	f.SetCellValue(sheetName, "B3", localization.T(language, "BUSINESS_REPORT_SHEET_1_HEADER_NAME"))
	f.SetCellValue(sheetName, "C3", localization.T(language, "BUSINESS_REPORT_SHEET_HEADER_PHONE"))
	f.SetCellValue(sheetName, "D3", localization.T(language, "BUSINESS_REPORT_SHEET_HEADER_GROUP"))
	f.SetCellValue(sheetName, "E3", localization.T(language, "BUSINESS_REPORT_SHEET_1_HEADER_DATE_JOINED"))
}

func setBorderStyle(f *excelize.File, sheetName string, startCell string, endCell string, isBold bool) {
	styleOptions := &excelize.Style{
		Border: []excelize.Border{
			{Type: "left", Color: "000000", Style: 1},
			{Type: "right", Color: "000000", Style: 1},
			{Type: "top", Color: "000000", Style: 1},
			{Type: "bottom", Color: "000000", Style: 1},
		},
		Alignment: &excelize.Alignment{
			Horizontal: "center",
			Vertical:   "center",
		},
		Font: &excelize.Font{
			Size: 12,
		},
	}

	if isBold {
		styleOptions.Font = &excelize.Font{
			Size: 12,
			Bold: true,
		}
	}
	style, _ := f.NewStyle(styleOptions)

	f.SetCellStyle(sheetName, startCell, endCell, style)
}
