# gRPC Integration for Subscription Notifications

## Overview
Successfully integrated gRPC call to push-notification-vn service for sending notifications to askers when subscription tasks are posted.

## Implementation Details

### File Updated
- **`8_sendNotificationToAsker.go`**: Updated to use gRPC instead of simplified logging

### gRPC Service Used
- **Service**: `PushNotificationVN`
- **Method**: `Send`
- **Package**: `gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPushNotificationVN`

### Configuration
- **Config Field**: `cfg.GRPC_Push_Notification_URL`
- **Default Local**: `localhost:5556`
- **Default Dev**: `go-push-notification-vn-v3.kong.svc.cluster.local:81`

### Request Structure
```go
type PushNotificationRequest struct {
    UserIds []*PushNotificationRequestUserIds
    Title   *service.ServiceText
    Body    *service.ServiceText
    Sound   string
    Payload *PushNotificationRequestPayload
}

type PushNotificationRequestUserIds struct {
    UserId   string
    Language string
}

type PushNotificationRequestPayload struct {
    Type       int32  // 2 for task notifications
    TaskId     string
    NavigateTo string // "TaskDetail"
}
```

### Response Structure
```go
type Response struct {
    StatusCode int32
    Message    string
    Data       []byte
    Error      *ResponseError
}
```

## Implementation Features

### 1. Asynchronous Processing
- Uses `go func()` to send notifications asynchronously
- Prevents blocking the main subscription processing flow

### 2. Connection Management
- Establishes gRPC connection per request
- Properly closes connection with `defer conn.Close()`
- 30-second timeout for gRPC calls

### 3. Localization Support
- Supports Vietnamese language (`LANG_VI`)
- Uses `globalLib.LocalizeServiceName()` for proper text localization
- Fallback to user's preferred language if specified

### 4. Error Handling
- Logs connection errors
- Logs gRPC call errors
- Checks response status code (200 = success)
- Detailed error messages with status codes

### 5. Notification Content
- **Title**: `NOTIFICATION_MESSAGE_ASKER_POSTED_TITLE` with service text
- **Body**: `NOTIFICATION_MESSAGE_ASKER_POSTED` with service text
- **Type**: 2 (task notification)
- **Navigation**: "TaskDetail" screen
- **Sound**: "default"

## Usage Flow

1. **Subscription Task Posted**: When a subscription task is successfully created
2. **Notification Triggered**: `sendNotificationToAsker()` is called with:
   - `taskID`: ID of the posted task
   - `userID`: ID of the asker
   - `language`: User's preferred language
   - `serviceText`: Service name for localization

3. **gRPC Call**: Asynchronously connects to push-notification-vn service
4. **Notification Sent**: Push notification delivered to user's device
5. **Logging**: Success/failure logged for monitoring

## Integration Points

### Called From
- `postSubscriptionTask()` in `4_postSubscriptionTask.go`
- After task is successfully inserted into database
- Before tasker assignment

### Dependencies
- **Config**: `cfg.GRPC_Push_Notification_URL`
- **Localization**: `localization.GetLocalizeObject()`
- **Global Lib**: `globalLib.LocalizeServiceName()`
- **Constants**: `globalConstant.LANG_VI`

## Error Scenarios Handled

1. **Connection Failure**: Service unavailable or network issues
2. **gRPC Timeout**: 30-second timeout prevents hanging
3. **Service Error**: Non-200 status codes logged with details
4. **Invalid Response**: Malformed responses handled gracefully

## Monitoring & Logging

### Success Logs
```
Successfully sent notification to asker {userID} for task {taskID}
```

### Error Logs
```
Error connecting to push notification service: {error}
Error sending notification to asker {userID}: {error}
Failed to send notification to asker {userID}: {message} (status: {code})
```

## Configuration Examples

### Local Development
```yaml
grpc_push_notification_service_url: localhost:5556
```

### Development Environment
```yaml
grpc_push_notification_service_url: go-push-notification-vn-v3.kong.svc.cluster.local:81
```

### CI Environment
```yaml
grpc_push_notification_vn_v3_url: go-push-notification-vn-v3:5556
```

## Testing

### Manual Testing
1. Create a subscription with valid user ID
2. Trigger subscription processing
3. Verify task is posted
4. Check logs for notification success/failure
5. Verify user receives push notification

### Integration Testing
- Test with valid/invalid user IDs
- Test with different languages
- Test service unavailability scenarios
- Test timeout scenarios

## Benefits

1. **Real-time Notifications**: Users immediately know when tasks are posted
2. **Improved UX**: Better user engagement and task visibility
3. **Scalable**: Asynchronous processing doesn't block main flow
4. **Reliable**: Proper error handling and logging
5. **Localized**: Supports multiple languages
6. **Monitored**: Comprehensive logging for debugging

## Future Enhancements

1. **Retry Logic**: Add retry mechanism for failed notifications
2. **Batch Processing**: Send multiple notifications in batches
3. **Template Support**: Use notification templates
4. **Analytics**: Track notification delivery rates
5. **A/B Testing**: Test different notification content
