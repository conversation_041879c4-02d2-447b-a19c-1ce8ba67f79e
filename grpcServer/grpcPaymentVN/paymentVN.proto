syntax = "proto3";
package grpcPaymentVN;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPaymentVN";

import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentRequest/payment-request.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response/response.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentTransaction/payment-transaction.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/shopeePayTransaction/shopeePayTransaction.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/momoTransaction/momo-transaction.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/zaloPayTransaction/zaloPayTransaction.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/refundTransaction/refundTransaction.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task/task.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/vatRequest/vatRequest.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentMBTransaction/paymentMBTransaction.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember/businessMember.proto";

service PaymentVN {
  rpc SubscriptionPayment (paymentRequest.PaymentRequest) returns (response.Response) {}
  rpc ChargePayment (paymentTransaction.PaymentTransaction) returns (response.Response) {}
  rpc RefundVN (refundTransaction.RefundTransaction) returns (response.Response) {}
  rpc CancelTikiOrder (task.Task) returns (response.Response) {}
  rpc CompleteTikiOrder (task.Task) returns (response.Response) {}
  rpc CheckTransactionStatusShopeePayVN (shopeePayTransaction.ShopeePayTransaction) returns (response.Response) {}
  rpc CheckTransactionStatusMomo (momoTransaction.MomoTransaction) returns (response.Response) {}
  rpc CheckTransactionStatusZaloPay (zaloPayTransaction.ZaloPayTransaction) returns (response.Response) {}
  rpc CreateGiftByComboVoucherVN (paymentRequest.PaymentRequest) returns (response.Response) {}
  rpc CreateVNVatRequest (vatRequest.VatRequest) returns (response.Response) {}
  rpc CheckTransactionStatusMB (paymentMBTransaction.PaymentMBTransaction) returns (response.Response) {}
  rpc GetTransactionsMB (paymentMBTransaction.GetMBTransactionRequest) returns (paymentMBTransaction.PaymentMBTransactionReport) {}
  rpc GetTransactionsMBTaskerPayToolkit (paymentMBTransaction.GetMBTransactionRequest) returns (paymentMBTransaction.PaymentMBTransactionReport) {}
  rpc GetTransactionsMBAsker (paymentMBTransaction.GetMBTransactionRequest) returns (paymentMBTransaction.PaymentMBTransactionReport) {}
  rpc BusinessTopupMember (businessMember.MemberRequestPayment) returns (response.Response) {}
  rpc BusinessRevokeMember (businessMember.MemberRequestPayment) returns (response.Response) {}
  rpc PayTaskBybPayBusiness (paymentRequest.PaymentRequest) returns (response.Response) {}
  rpc PayComboVoucherVN (paymentRequest.PaymentRequest) returns (response.Response) {}
}
