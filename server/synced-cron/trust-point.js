import { Meteor } from 'meteor/meteor';
import _ from 'lodash';
import moment from 'moment';
import {
  revertTimeZone,
  trackingScheduleStatus,
  sendNotificationInApp
} from '/server/helper-function';

const MAX_POINT = 100;
const MIN_POINT = 0;
const DEFAULT_TRUST_POINT = 80; //medium default

const initTrustPointHistory = (userId) => {
  const objTrustPoint = {
    point: DEFAULT_TRUST_POINT,
    rated: caculateAvg(DEFAULT_TRUST_POINT),
  };
  //insert trust point to user if not exist
  const user = Meteor.appCollection.users.findOne({_id: userId}, {fields: {trustPoint: 1} });
  if (user && !user.trustPoint) {
	  Meteor.appCollection.users.update({_id: userId}, {$set: {trustPoint: objTrustPoint}  });
  }
  //check TrustPointHistory
  const trustPointHistoryExist = Meteor.appCollection.TrustPointHistory.findOne({userId: userId}, {fields: {_id: 1} });
  if (!trustPointHistoryExist) {
    return Meteor.appCollection.TrustPointHistory.insert({
			userId,
			createdAt: new Date(),
			updatedAt: new Date(),
		});
  }
	return false;
}

/*
  return number Point subtract or add
*/

const caculateTrustPoint = (task) => {
  if (task.status === 'DONE') {
    const rating = Meteor.appCollection.AskerRating.findOne({taskId: task._id}, {fields: {rate: 1} });
    if (!rating || (rating && rating.rate === 5) ) {
      return 1;
    }
    if (rating && rating.rate === 4) {
      return -1;
    }
    if (rating && rating.rate < 4) {
      return -2;
    }
    return 1;
  }
  if (task.status === 'CANCELED') {
    return -2;
  }
  return 0;
}

const groupByUserId = (tasks) => {
  try {
    if (tasks && tasks.length > 0) {
      const objUser = {};
      tasks.forEach((task) => {
        //check task.askerId exists in objUser
        if (!objUser[task.askerId]) {
          objUser[task.askerId] = {
            task: [{...task}],
            numberPoint: caculateTrustPoint(task),
          };
        } else {
          // push array
          const oldTrustPoint = objUser[task.askerId].numberPoint;
          //update numberPoint
          objUser[task.askerId].numberPoint = oldTrustPoint + caculateTrustPoint(task);
          //push task to task in objUser
          objUser[task.askerId].task.push({...task});
        }
      });
      return objUser;
    }
    return {};
  } catch (e) {

  }
}

const checkLimitPoint = (newTrustPoint) => {
  if (newTrustPoint >= MAX_POINT) {
    newTrustPoint = MAX_POINT;
  } else if (newTrustPoint <= MIN_POINT) {
    newTrustPoint = MIN_POINT;
  }
  return newTrustPoint;
}

/*
  RULE trustPoint
  80 -> 100 : GOOD
  65 -> 79: MEDIUM
  <65 : BAD
*/

const caculateAvg = (trustPoint) => {
  let avg = '';
  if (trustPoint >= 80 && trustPoint <= 100) {
    avg = 'GOOD';
  } else if (trustPoint >= 65 && trustPoint < 80) {
    avg = 'MEDIUM';
  } else {
    avg = 'BAD';
  }
  return avg;
}

const sendNotificationWaring = ({userId, currentTrustPoint, trustPointSubtract, reason}) => {
  // const title = "Thông báo";
  // let message = `Điểm uy tín của bạn bị giảm ${trustPointSubtract} do huỷ công việc lúc ${reason}.
  // Điểm uy tín hiện tại là ${currentTrustPoint}.
  // Bạn sẽ được tăng điểm tích luỹ khi công việc tiếp theo được hoàn thành.`;
  // if (trustPoint >= 80 && trustPoint < 90) {
  //   message = '';
  // } else if (trustPoint <= 70) {
  //   message = '';
  // }
  // sendNotificationInApp(userId, message, 31);
}

const runTrustPoint = () => {
  return;
	console.log("Start runTrustPoint ...");
  var message = null;
  try {
    const beforeTime = moment().subtract(1, 'days').startOf('day').toDate();
    const afterTime = moment().subtract(1, 'days').endOf('day').toDate();
    //get all task with status DONE, CANCELED, add trust point for user
    const taskCondition = {
      $or: [
        {status: 'DONE'},
        {
          status: 'CANCELED',
          cancellationReason: {$in: ['ASKER_BUSY', 'BACKEND_CANCEL', 'ASKER_DONT_NEED_ANYMORE', 'POSTED_WRONG_DATE', 'NO_TASKER_ACCEPT']}
        }
      ],
      updatedAt: { $gte: beforeTime, $lte: afterTime }
    };
    const tasks = Meteor.appCollection.Task.find(taskCondition, {fields: {askerId: 1, status: 1, updatedAt: 1, createdAt: 1, cancellationReason: 1, cancellationText: 1} }).fetch();
    //group task by userID
    const arrayTasksByUserId = groupByUserId(tasks);
    if (arrayTasksByUserId && Object.keys(arrayTasksByUserId).length === 0) {
      return;
    }
    for (let userId in arrayTasksByUserId) {
      const asker = Meteor.appCollection.users.findOne({_id: userId}, {fields: {trustPoint: 1} });
      let trustPointHistoryId = null;
      let userTrustPoint = DEFAULT_TRUST_POINT;
      // trustPoint not exist
      if (asker && asker.trustPoint === undefined) {
        // init TrustPointHistory and trustPoint in user
        trustPointHistoryId = initTrustPointHistory(asker._id);
      } else if (asker && asker.trustPoint && asker.trustPoint.point >= 0) {
      //   //get old trust point from user
        userTrustPoint = asker.trustPoint.point;
        const objtrustPointHistory = Meteor.appCollection.TrustPointHistory.findOne({userId: userId}, {fields: {_id: 1} });
        //make sure TrustPointHistory exist
        if (!objtrustPointHistory) {
          trustPointHistoryId = initTrustPointHistory(asker._id);
        } else {
          trustPointHistoryId = objtrustPointHistory._id;
        }
      }
      // numberPoint === 0 not action
      if (arrayTasksByUserId[userId].numberPoint !== 0) {
        //check limit point
        const newTrustPoint = checkLimitPoint(userTrustPoint + arrayTasksByUserId[userId].numberPoint);
        // object trust point when update for user 
        const objTrustPoint = {
          point: newTrustPoint,
          rated: caculateAvg(newTrustPoint),
        };
        // send notification
        // sendNotificationWaring(userId, newTrustPoint);
        // update trustPoint to users
        Meteor.appCollection.users.update({_id: userId}, {$set: {trustPoint: objTrustPoint} });
        // update TrustPointHistory
        const data = arrayTasksByUserId[userId].task.map((e) => {
          //get reason when cancel
          let reason = '';
          if (e.cancellationReason) {
            reason = e.cancellationReason;
          }
          if (e.cancellationText) {
            reason = e.cancellationText;
          }
          return {
            taskId: e._id,
            reason: reason,
            status: e.status,
            createdAt: e.createdAt,
            updatedAt: e.updatedAt,
          };
        })
        const history = {
          oldTrustPoint: userTrustPoint,
          newTrustPoint: newTrustPoint,
          totalTrustPoint: arrayTasksByUserId[userId].numberPoint,
          createdAt: new Date(),
          data: data,
        };
        // Update TrustPointHistory
        trustPointHistoryId && Meteor.appCollection.TrustPointHistory.update(
          {_id: trustPointHistoryId},
          {
            $push: { history: history},
            $set: { updatedAt: new Date() }
        });
      }
    }
  } catch (e) {
    message = e;
  }
	console.log("Finished runTrustPoint ");
  // Tracking
  trackingScheduleStatus({name: 'updateTrustPoint', nextRunAt: SyncedCron.nextScheduledAtDate('updateTrustPoint'), message});
};

  Meteor.methods({
    updateTrustPoint() {
      SyncedCron.add({
        name: 'updateTrustPoint',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 2)).getHours();
          // Run at 2:10 AM every day with timezone GMT+7.
          return parser.cron(`0 10 ${hourInTimeZoneServer} 1/1 * ? *`, true);
        },
        job() {
          runTrustPoint();
        }
      });
	    console.log("Added updateTrustPoint cron");
    },
    'runNow.updateTrustPoint'() {
      runTrustPoint();
    },
  });
