package autoCalculateJourneyLeaderBoard

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getTaskers(cityName string, taskerList []string) (map[string]*modelUser.Users, error) {
	// Get tasker in
	taskers, err := getTaskersByChunk(cityName, taskerList)
	if err != nil {
		return nil, err
	}

	mapTaskerById := make(map[string]*modelUser.Users)
	for _, v := range taskers {
		mapTaskerById[v.XId] = v
	}

	return mapTaskerById, err
}

func getTaskersByChunk(cityName string, taskerList []string) ([]*modelUser.Users, error) {
	chunkTaskerList := globalLib.SplitArrayToChunk(taskerList, CHUNK_SIZE_GET_TASKERS)
	var taskers []*modelUser.Users
	for _, taskerIds := range chunkTaskerList {
		chunk, err := modelUser.GetAll(local.ISO_CODE,
			bson.M{
				"_id":                  bson.M{"$in": taskerIds},
				"workingPlaces.0.city": cityName,
				// "isoCode":              local.ISO_CODE, // Không cần field này nữa
			},
			bson.M{"_id": 1, "journeyInfo.level": 1, "journeyInfo.levelHistories": 1, "name": 1, "avatar": 1},
		)
		if err != nil {
			err = fmt.Errorf("getTaskersByChunk: %v", err)
			return nil, err
		}
		for _, v := range chunk {
			if v.JourneyInfo == nil { // Tasker chua co Journey
				continue
			}
			taskers = append(taskers, v)
		}
	}
	return taskers, nil
}
