package handlers

import (
	"bytes"
	"errors"
	"net/http"
	"regexp"
	"strings"
	"text/template"

	//https://www.kablamo.com.au/blog/2018/12/10/just-tell-me-how-to-use-go-modules

	"gitlab.com/btaskee/go-email-vn-v3/config"
	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	modelSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

const emailPartern = "^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$"

var cfg = config.GetConfig()

/*
 * @Description: Check email input before send email
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 04/09/2020
 * @UpdatedBy: linhnh
 */
func checkSendEmailInput(req *modelEmailSending.EmailSending) (res *modelEmailResponse.EmailResponse, err error) {
	// Check subject
	if req.Subject == "" {
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_SUBJECT_EMAIL_NIL,
		}, errors.New(lib.ERROR_SUBJECT_EMAIL_NIL)
	}
	// Check from email
	if req.From == "" {
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_FROM_EMAIL_NIL,
		}, errors.New(lib.ERROR_FROM_EMAIL_NIL)
	}

	// Check "to" email
	if req.To == "" {
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_TO_EMAIL_NIL,
		}, errors.New(lib.ERROR_TO_EMAIL_NIL)
	}
	// Check email syntax
	re := regexp.MustCompile(emailPartern)
	if !re.MatchString(req.To) {
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_TO_EMAIL_INVALID + "_" + req.To,
		}, errors.New(lib.ERROR_TO_EMAIL_INVALID)
	}
	if strings.Contains(req.To, "@privaterelay.appleid.com") {
		// migrate user emails
		modelUser.UpdateOneByQuery(local.ISO_CODE, bson.M{"emails.address": req.To}, bson.M{"$unset": bson.M{"emails.$.verified": 1}})
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_TO_EMAIL_INVALID,
		}, errors.New(lib.ERROR_TO_EMAIL_INVALID)
	}

	// Check content
	if req.Content == "" {
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_CONTENT_NIL + "_" + req.From + "_" + req.To,
		}, errors.New(lib.ERROR_CONTENT_NIL)
	}

	// // Check cc email
	// invalidCc = []string{}
	// validCc = []string{}
	// if req.Cc != nil && len(req.Cc) > 0 {
	// 	for i := 0; i < len(req.Cc); i++ {
	// 		if re.MatchString(req.Cc[i]) == false {
	// 			invalidCc = append(invalidCc, req.Cc[i])
	// 		} else {
	// 			validCc = append(validCc, req.Cc[i])
	// 		}
	// 	}
	// }

	// // Check bcc email
	// invalidBcc = []string{}
	// validBcc = []string{}
	// if req.Bcc != nil && len(req.Bcc) > 0 {
	// 	for i := 0; i < len(req.Bcc); i++ {
	// 		if re.MatchString(req.Bcc[i]) == false {
	// 			invalidBcc = append(invalidBcc, req.Bcc[i])
	// 		} else {
	// 			validBcc = append(validBcc, req.Bcc[i])
	// 		}
	// 	}
	// }

	return nil, nil
}

/*
 * @Description: Parse HTML template and embedded data into template
 * @CreatedAt: 04/09/2020
 * @Author: linhnh
 */
func ParseTemplate(templateFileName string, data interface{}) (string, error) {
	t, err := template.ParseFiles(templateFileName)
	if err != nil {
		return "", err
	}
	buf := new(bytes.Buffer)
	if err = t.Execute(buf, data); err != nil {
		return "", err
	}
	return buf.String(), nil
}

/*
 * @Description: Check email server config
 * @CreatedAt: 02/03/2021
 * @Author: ngoctb3
 */
func CheckEmailConfig() *modelEmailResponse.EmailResponse {
	if cfg.MailGunDomain == "" || cfg.MailGunAPIKey == "" {
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_EMAIL_CONFIG_NOT_SET,
		}
	}
	return nil
}

func getReferralValueAndCurrency(isoCode string, currencyCode string, setting *modelSetting.Settings) (float64, string) {
	var referralValue float64
	if setting != nil && setting.ReferralSetting != nil && setting.ReferralSetting.Voucher != nil && setting.ReferralSetting.Voucher.Value > 0 {
		referralValue = setting.ReferralSetting.Voucher.Value
	}
	if referralValue == 0 {
		referralValue = 30000
	}
	if currencyCode == "" {
		currencyCode = globalConstant.CURRENCY_VN
	}
	return referralValue, currencyCode
}
