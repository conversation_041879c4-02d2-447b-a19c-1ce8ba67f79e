package modelAcceptTask

import (
	"github.com/golang/protobuf/ptypes/timestamp"
)

type AcceptRequest struct {
	BookingId  string       `json:"bookingId,omitempty"`
	TaskerId   string       `json:"taskerId,omitempty"`
	CompanyId  string       `json:"companyId,omitempty"`
	AppVersion string       `json:"appVersion,omitempty"`
	Option     *InputOption `json:"option,omitempty"`
	TaskId     string       `json:"taskId,omitempty"`
}

type TaskAcceptedTasker struct {
	TaskerId            string               `json:"taskerId,omitempty" bson:"taskerId,omitempty"`
	CompanyId           string               `json:"companyId,omitempty" bson:"companyId,omitempty"`
	Name                string               `json:"name,omitempty" bson:"name,omitempty"`
	Avatar              string               `json:"avatar,omitempty" bson:"avatar,omitempty"`
	AvgRating           float64              `json:"avgRating,omitempty" bson:"avgRating,omitempty"`
	TaskDone            int32                `json:"taskDone" bson:"taskDone"`
	AcceptedAt          *timestamp.Timestamp `json:"acceptedAt,omitempty" bson:"acceptedAt,omitempty"`
	IsHidden            bool                 `json:"isHidden,omitempty" bson:"isHidden,omitempty"`
	IsLeader            bool                 `json:"isLeader,omitempty" bson:"isLeader,omitempty"`
	IsVaccinatedCovid19 bool                 `json:"isVaccinatedCovid19,omitempty" bson:"isVaccinatedCovid19,omitempty"`
}

type ChooseTaskerModel struct {
	BookingId string `json:"bookingId,omitempty"`
	TaskerId  string `json:"taskerId,omitempty"`
	From      string `json:"from,omitempty"`
}

type InputOption struct {
	From          string      `json:"from,omitempty"`
	MustRePricing bool        `json:"mustRePricing,omitempty"`
	Data          interface{} `json:"data,omitempty"`
}
