// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcBookingTH

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	bookingRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/bookingRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// BookingTHClient is the client API for BookingTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BookingTHClient interface {
	BookFirstTaskInSubscription(ctx context.Context, in *bookingRequest.BookingRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type bookingTHClient struct {
	cc grpc.ClientConnInterface
}

func NewBookingTHClient(cc grpc.ClientConnInterface) BookingTHClient {
	return &bookingTHClient{cc}
}

func (c *bookingTHClient) BookFirstTaskInSubscription(ctx context.Context, in *bookingRequest.BookingRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcBookingTH.BookingTH/BookFirstTaskInSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BookingTHServer is the server API for BookingTH service.
// All implementations must embed UnimplementedBookingTHServer
// for forward compatibility
type BookingTHServer interface {
	BookFirstTaskInSubscription(context.Context, *bookingRequest.BookingRequest) (*empty.Empty, error)
	mustEmbedUnimplementedBookingTHServer()
}

// UnimplementedBookingTHServer must be embedded to have forward compatible implementations.
type UnimplementedBookingTHServer struct {
}

func (UnimplementedBookingTHServer) BookFirstTaskInSubscription(context.Context, *bookingRequest.BookingRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BookFirstTaskInSubscription not implemented")
}
func (UnimplementedBookingTHServer) mustEmbedUnimplementedBookingTHServer() {}

// UnsafeBookingTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BookingTHServer will
// result in compilation errors.
type UnsafeBookingTHServer interface {
	mustEmbedUnimplementedBookingTHServer()
}

func RegisterBookingTHServer(s *grpc.Server, srv BookingTHServer) {
	s.RegisterService(&_BookingTH_serviceDesc, srv)
}

func _BookingTH_BookFirstTaskInSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(bookingRequest.BookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingTHServer).BookFirstTaskInSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBookingTH.BookingTH/BookFirstTaskInSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingTHServer).BookFirstTaskInSubscription(ctx, req.(*bookingRequest.BookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _BookingTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcBookingTH.BookingTH",
	HandlerType: (*BookingTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BookFirstTaskInSubscription",
			Handler:    _BookingTH_BookFirstTaskInSubscription_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "bookingTH.proto",
}
