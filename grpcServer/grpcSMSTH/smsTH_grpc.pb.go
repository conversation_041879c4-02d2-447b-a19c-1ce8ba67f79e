// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcSMSTH

import (
	context "context"
	smsSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/smsSending"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// SMSTHClient is the client API for SMSTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SMSTHClient interface {
	Send(ctx context.Context, in *smsSending.SmsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	SendSMSViaVMG(ctx context.Context, in *smsSending.SmsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type sMSTHClient struct {
	cc grpc.ClientConnInterface
}

func NewSMSTHClient(cc grpc.ClientConnInterface) SMSTHClient {
	return &sMSTHClient{cc}
}

func (c *sMSTHClient) Send(ctx context.Context, in *smsSending.SmsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/grpcSMSTH.SMSTH/Send", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sMSTHClient) SendSMSViaVMG(ctx context.Context, in *smsSending.SmsRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/grpcSMSTH.SMSTH/SendSMSViaVMG", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SMSTHServer is the server API for SMSTH service.
// All implementations must embed UnimplementedSMSTHServer
// for forward compatibility
type SMSTHServer interface {
	Send(context.Context, *smsSending.SmsRequest) (*emptypb.Empty, error)
	SendSMSViaVMG(context.Context, *smsSending.SmsRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedSMSTHServer()
}

// UnimplementedSMSTHServer must be embedded to have forward compatible implementations.
type UnimplementedSMSTHServer struct {
}

func (UnimplementedSMSTHServer) Send(context.Context, *smsSending.SmsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Send not implemented")
}
func (UnimplementedSMSTHServer) SendSMSViaVMG(context.Context, *smsSending.SmsRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSMSViaVMG not implemented")
}
func (UnimplementedSMSTHServer) mustEmbedUnimplementedSMSTHServer() {}

// UnsafeSMSTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SMSTHServer will
// result in compilation errors.
type UnsafeSMSTHServer interface {
	mustEmbedUnimplementedSMSTHServer()
}

func RegisterSMSTHServer(s *grpc.Server, srv SMSTHServer) {
	s.RegisterService(&_SMSTH_serviceDesc, srv)
}

func _SMSTH_Send_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(smsSending.SmsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SMSTHServer).Send(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSMSTH.SMSTH/Send",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SMSTHServer).Send(ctx, req.(*smsSending.SmsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SMSTH_SendSMSViaVMG_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(smsSending.SmsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SMSTHServer).SendSMSViaVMG(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSMSTH.SMSTH/SendSMSViaVMG",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SMSTHServer).SendSMSViaVMG(ctx, req.(*smsSending.SmsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _SMSTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcSMSTH.SMSTH",
	HandlerType: (*SMSTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Send",
			Handler:    _SMSTH_Send_Handler,
		},
		{
			MethodName: "SendSMSViaVMG",
			Handler:    _SMSTH_SendSMSViaVMG_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "smsTH.proto",
}
