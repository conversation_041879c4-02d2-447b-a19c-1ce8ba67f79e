package autoCheckIncentive

import (
	"fmt"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

func postToSlack(newItemsNeedUpdateQuantity, itemsNeedUpdateStatus []UrBoxGift, listGiftIdNeedInactive []string) {
	if len(newItemsNeedUpdateQuantity) > 0 || len(listGiftIdNeedInactive) > 0 {
		textGiftNeedUpdateQuantity := ""
		for i, gift := range newItemsNeedUpdateQuantity {
			textGiftNeedUpdateQuantity += fmt.Sprintf("%d. %s. Số lượng: %d\n", i+1, gift.Title, cast.ToInt(gift.CodeQuantity))
		}
		textGiftInactive := ""
		tempTitleGiftInactive := []string{}
		mapGiftNeedInactive := make(map[string]bool)
		for _, giftId := range listGiftIdNeedInactive {
			mapGiftNeedInactive[giftId] = true
		}
		for _, gift := range itemsNeedUpdateStatus {
			if mapGiftNeedInactive[gift.ID] {
				tempTitleGiftInactive = append(tempTitleGiftInactive, gift.Title)
			}
		}

		for i, title := range tempTitleGiftInactive {
			textGiftInactive += fmt.Sprintf("%d. %s\n", i+1, title)
		}

		if textGiftInactive != "" {
			textGiftInactive = "\nDanh sách ưu đãi INACTIVE:\n" + textGiftInactive
		}

		globalLib.PostToSlack(
			cfg.SlackToken, "cron-jobs-alert", globalConstant.SLACK_USER_NAME,
			fmt.Sprintf("Danh sách ưu đãi cần bổ sung số lượng từ URBOX:\n%s%s", textGiftNeedUpdateQuantity, textGiftInactive),
		)
	}
}
