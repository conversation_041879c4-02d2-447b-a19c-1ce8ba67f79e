/*
 * @File: helpers.go
 * @Description: Helper functions for service
 * @CreatedAt: 07/09/2020
 * @Author: linhnh
 */
package lib

import (
	"strings"
	"time"

	"github.com/golang/protobuf/ptypes/timestamp"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

var monthsVI *strings.Replacer = strings.NewReplacer(
	"January", "Tháng 01",
	"February", "Tháng 02",
	"March", "Tháng 03",
	"April", "Tháng 04",
	"May", "Tháng 05",
	"June", "Tháng 06",
	"July", "Tháng 07",
	"August", "Tháng 08",
	"September", "Tháng 09",
	"October", "Tháng 10",
	"November", "Tháng 11",
	"December", "Tháng 12",
)

var monthsKO *strings.Replacer = strings.NewReplacer(
	"January", "1월",
	"February", "2월",
	"March", "3월",
	"April", "4월",
	"May", "5월",
	"June", "6월",
	"July", "7월",
	"August", "8월",
	"September", "9월",
	"October", "10월",
	"November", "11월",
	"December", "12월",
)

var monthsTH *strings.Replacer = strings.NewReplacer(
	"January", "มกราคม",
	"February", "กุมภาพันธ์",
	"March", "มีนาคม",
	"April", "เมษายน",
	"May", "พฤษภาคม",
	"June", "มิถุนายน",
	"July", "กรกฎาคม",
	"August", "สิงหาคม",
	"September", "กันยายน",
	"October", "ตุลาคม",
	"November", "พฤศจิกายน",
	"December", "ธันวาคม",
)

/*
 * @Description: Get TimeLocale string from time and language
 * @CreatedAt: 07/09/2020
 * @Author: linhnh
 */
func TimeLocale(time string, language string) string {
	switch language {
	case globalConstant.LANG_VI:
		return monthsVI.Replace(time)
	case globalConstant.LANG_KO:
		return monthsKO.Replace(time)
	case globalConstant.LANG_TH:
		return monthsTH.Replace(time)
	default:
		return time
	}
}

func GetDaysFromDateTimestamp(date []*timestamp.Timestamp) []int32 {
	result := []int32{}
	weekdayMap := make(map[int32]bool)
	for _, d := range date {
		weekday := globalLib.ParseDateFromTimeStamp(d, local.TimeZone).Weekday()
		weekdayMap[int32(weekday)] = true
	}

	for k := range weekdayMap {
		result = append(result, k)
	}
	return result
}

func GetIsoWeekDays(weekday []int32) []string {
	result := []string{}
	for _, w := range weekday {
		result = append(result, time.Weekday(w).String())
	}
	return result
}

func DaysByLang(weekday []string, language string) []string {
	days := weekday
	if language == globalConstant.LANG_VI {
		var daysVN []string
		for _, v := range days {
			if v == "Sunday" {
				daysVN = append(daysVN, "Chủ nhật")
			} else if v == "Monday" {
				daysVN = append(daysVN, "Thứ 2")
			} else if v == "Tuesday" {
				daysVN = append(daysVN, "Thứ 3")
			} else if v == "Wednesday" {
				daysVN = append(daysVN, "Thứ 4")
			} else if v == "Thursday" {
				daysVN = append(daysVN, "Thứ 5")
			} else if v == "Friday" {
				daysVN = append(daysVN, "Thứ 6")
			} else {
				daysVN = append(daysVN, "Thứ 7")
			}
		}
		days = daysVN
	} else if language == globalConstant.LANG_KO {
		var daysKO []string
		for _, v := range days {
			if v == "Sunday" {
				daysKO = append(daysKO, "일요일")
			} else if v == "Monday" {
				daysKO = append(daysKO, "월요일")
			} else if v == "Tuesday" {
				daysKO = append(daysKO, "화요일")
			} else if v == "Wednesday" {
				daysKO = append(daysKO, "수요일")
			} else if v == "Thursday" {
				daysKO = append(daysKO, "목요일")
			} else if v == "Friday" {
				daysKO = append(daysKO, "금요일")
			} else {
				daysKO = append(daysKO, "토요일")
			}
		}
		days = daysKO
	} else if language == globalConstant.LANG_TH {
		var daysTH []string
		for _, v := range days {
			if v == "Sunday" {
				daysTH = append(daysTH, "วันอาทิตย์")
			} else if v == "Monday" {
				daysTH = append(daysTH, "วันจันทร์")
			} else if v == "Tuesday" {
				daysTH = append(daysTH, "วันอังคาร")
			} else if v == "Wednesday" {
				daysTH = append(daysTH, "วันพุธ")
			} else if v == "Thursday" {
				daysTH = append(daysTH, "วันพฤหัสบดี")
			} else if v == "Friday" {
				daysTH = append(daysTH, "วันศุกร์")
			} else {
				daysTH = append(daysTH, "วันเสาร์")
			}
		}
		days = daysTH
	}
	return days
}
