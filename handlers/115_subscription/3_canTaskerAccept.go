package subscription

import (
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
)

// canTaskerAccept checks if a tasker can accept tasks - STEP 5: Tasker Validation
func canTaskerAccept(tasker *users.Users) bool {
	if tasker.Status != globalConstant.USER_STATUS_ACTIVE && tasker.Status != globalConstant.USER_STATUS_BLOCKED {
		return false
	}

	// Tasker locked with not come reason >= 3 times
	if tasker.Status == globalConstant.USER_STATUS_BLOCKED && tasker.NotComeLockNumber > 2 {
		return false
	}

	// Tasker is locked with other reason
	if tasker.Status == globalConstant.USER_STATUS_BLOCKED && tasker.NotComeLockNumber == 0 {
		return false
	}

	return true
}
