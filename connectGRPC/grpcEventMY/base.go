package grpcEventMY

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcEventMY"
	"google.golang.org/grpc"
)

func ConnectGRPCEvent(endpoint string) (grpcEventMY.EventClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcEventMY.NewEventClient(connect)
	return client, connect, err
}
