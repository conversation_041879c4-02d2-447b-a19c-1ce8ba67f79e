import { Meteor } from 'meteor/meteor';
import {
  revertTimeZone,
  trackingScheduleStatus,
  postToSlack,
  getTimestamp
} from '/server/helper-function';
import moment from 'moment';
import _ from 'lodash';
import { HTTP } from 'meteor/http';


const eventBeginDate = revertTimeZone(moment('2020-12-05').startOf('date').toDate());
const eventEndDate = revertTimeZone(moment('2020-12-25').endOf('date').toDate());

const getAskerDone = () => {
  const distinct = Meteor.wrapAsync(Meteor.appCollection.Task.rawCollection().distinct, Meteor.appCollection.Task.rawCollection());
  return distinct('askerId', {
    date: {$gte: eventBeginDate, $lte: new Date()},
    status: 'DONE',
    isoCode: 'VN',
    askerId: {$ne: null},
  });
};

const sendRemoteNotification = (askerId, askerLanguage, title, content) => {
  const headers = {
    'content-type': 'application/json',
    'accessKey': Meteor.settings.GO_SERVICE.API_KEY,
  };
  const option = {
    userIds: [{userId: askerId, language: askerLanguage || 'vi'}],
    title: title,
    body: content,
    payload: {
      type: 30
    }
  };

  const url = Meteor.settings.GO_SERVICE.API_URL + '/v2/push-notification/send';

  Meteor.defer(() => {
    HTTP.post(url, {headers: headers, data: option});
  });
  
};

const sendAskerTickets = (askerId) => {
  try {
    const distinct = Meteor.wrapAsync(Meteor.appCollection.Task.rawCollection().distinct, Meteor.appCollection.Task.rawCollection());
    const taskIds =  distinct('_id', {
      askerId,
      date: {$gte: eventBeginDate, $lte: new Date()},
      status: 'DONE',
    });
    const asker = Meteor.appCollection.users.findOne({_id: askerId}, {fields: {
      language: 1
    }});
    if ( taskIds && taskIds.length > 0 && asker ) {
      const askerLanguage = asker.language || 'en';
      const title = {
        vi: 'Christmas lucky draw',
        en: 'Christmas lucky draw',
        ko: 'Christmas lucky draw',
        th: 'Christmas lucky draw',
      };
      const codeText = taskIds.map((taskId, index) => `${index + 1}. ${taskId.slice(taskId.length - 10).toUpperCase()}`).sort().join('\n');
      const content = {
        vi: `Danh sách Phiếu May Mắn Quý Khách đã nhận được là:\n\n${codeText}\n\nThời gian để thu thập Phiếu May Mắn sẽ kết thúc vào ngày 25/12/2020. Hãy nhanh tay đặt lịch và thu thập thật nhiều Phiếu May Mắn để tăng cơ hội trúng thưởng những giải thưởng hấp dẫn từ bTaskee bạn nhé!\nQuý Khách hàng vui lòng tham khảo thêm chương trình \"Đặt lịch nhận mã - Rinh quà cực đã\" tại đây.`,
        en: `The Lucky Draw tickets you have received ${taskIds.length === 1 ? 'is' : 'are'}:\n\n${codeText}\n\nThe Lucky Draw ticket collection period will end on December 25th, 2020.\nHurry up, book your tasks and collect many Lucky Draw tickets to increase your chances of winning amazing prizes from bTaskee!\nCustomers can find out more details here about the promotion \"Book bTaskee – Win hot Xmas Prizes\".\n`,
        ko: `The Lucky Draw tickets you have received ${taskIds.length === 1 ? 'is' : 'are'}:\n\n${codeText}\n\nThe Lucky Draw ticket collection period will end on December 25th, 2020.\nHurry up, book your tasks and collect many Lucky Draw tickets to increase your chances of winning amazing prizes from bTaskee!\nCustomers can find out more details here about the promotion \"Book bTaskee – Win hot Xmas Prizes\".\n`,
        th: `The Lucky Draw tickets you have received ${taskIds.length === 1 ? 'is' : 'are'}:\n\n${codeText}\n\nThe Lucky Draw ticket collection period will end on December 25th, 2020.\nHurry up, book your tasks and collect many Lucky Draw tickets to increase your chances of winning amazing prizes from bTaskee!\nCustomers can find out more details here about the promotion \"Book bTaskee – Win hot Xmas Prizes\".\n`,
      };
      const url = {
        vi: 'https://www.btaskee.com/khuyen-mai/chuong-trinh-giang-sinh-2020',
        en: 'https://www.btaskee.com/en/promotion/christmas-campaign-2020',
        ko: 'https://www.btaskee.com/en/promotion/christmas-campaign-2020',
        th: 'https://www.btaskee.com/en/promotion/christmas-campaign-2020',
      };


      Meteor.appCollection.Notification.insert({
        userId: askerId,
        type: 30,
        title: title[askerLanguage],
        description: content[askerLanguage],
        createdAt: new Date(),
        isForce: true,
        url: url[askerLanguage],
        image: 'https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/campaigns/KxwPxsf93v4yQhtC5',
      });

      sendRemoteNotification(askerId, askerLanguage, title, content);

    }
    
  } catch(ex) {
    postToSlack({
      channel: 'cron-jobs-alert',
      text: `Lỗi gửi thông báo Lucky draw đến khách hàng ${askerId} ${ex}`,
    });
  }

};



const runSendLuckyDraw = () => {
  var message = null;
  try {
    // Event stopped
    if ( moment().isAfter(eventEndDate) ) {
      return;
    }

    // Clear old notifications before send the new one.
    Meteor.appCollection.Notification.remove({
      title: 'Christmas lucky draw',
      type: 30
    });

    const askerIds = getAskerDone();
    
    askerIds.forEach(askerId => {
      sendAskerTickets(askerId);
    });
    
  } catch(e) {
    message = e;
    postToSlack({
      channel: 'cron-jobs-alert',
      text: `Workflow: There was an error on autoSendLuckyDraw. Message: ${message}`,
    });
  }

  // Tracking
  trackingScheduleStatus({name: 'autoSendLuckyDraw', nextRunAt: SyncedCron.nextScheduledAtDate('autoSendLuckyDraw'), message});
};

export default function () {
  Meteor.methods({
    // Auto send lucky draw
    autoSendLuckyDraw() {
      SyncedCron.add({
        name: 'autoSendLuckyDraw',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 19)).getHours();
          // Run synced cron at 19:30 +7UTC every day
          return parser.cron(`0 30 ${hourInTimeZoneServer} 1/1 * ? *`, true);
          
        },
        job() {
          runSendLuckyDraw();

        },
      });
    },
    'runNow.autoSendLuckyDraw'() {
      runSendLuckyDraw();
    },
  });
}
