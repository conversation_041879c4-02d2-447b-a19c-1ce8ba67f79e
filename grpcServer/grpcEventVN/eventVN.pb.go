// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.7.1
// source: eventVN.proto

package grpcEventVN

import (
	empty "github.com/golang/protobuf/ptypes/empty"
	eventMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/eventMessage"
	users "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_eventVN_proto protoreflect.FileDescriptor

var file_eventVN_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x56, 0x4e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0b, 0x67, 0x72, 0x70, 0x63, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x56, 0x4e, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x51, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f,
	0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d,
	0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x43, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65,
	0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0x97, 0x11, 0x0a, 0x07, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x56, 0x4e, 0x12, 0x6c, 0x0a,
	0x20, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54,
	0x61, 0x70, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x12, 0x2e, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54,
	0x61, 0x70, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x71, 0x0a, 0x25, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70,
	0x50, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x2e, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76,
	0x65, 0x72, 0x54, 0x61, 0x70, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x69,
	0x0a, 0x21, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72,
	0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x12, 0x2a, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65,
	0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x21, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61,
	0x73, 0x6b, 0x65, 0x72, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2a,
	0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54,
	0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x23, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x57,
	0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2a, 0x2e, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x00, 0x12, 0x67, 0x0a, 0x1f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65,
	0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x44, 0x6f, 0x6e, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x12, 0x2a, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76,
	0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x1d, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54,
	0x61, 0x73, 0x6b, 0x65, 0x72, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x2c, 0x2e, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x52, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x00, 0x12, 0x71, 0x0a, 0x25, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43,
	0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x46, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x2e, 0x2e, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x87, 0x01, 0x0a, 0x2a, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x50, 0x72, 0x65, 0x70,
	0x61, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65,
	0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x50, 0x72, 0x65, 0x70, 0x61, 0x79, 0x54, 0x61, 0x73, 0x6b,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00,
	0x12, 0x49, 0x0a, 0x1f, 0x41, 0x64, 0x64, 0x41, 0x73, 0x6b, 0x65, 0x72, 0x44, 0x6f, 0x6e, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x47,
	0x69, 0x66, 0x74, 0x12, 0x0c, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x1a, 0x41,
	0x64, 0x64, 0x41, 0x73, 0x6b, 0x65, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x61, 0x6c, 0x47, 0x69, 0x66, 0x74, 0x12, 0x0c, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x00, 0x12, 0x60, 0x0a, 0x22, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x55, 0x73, 0x65, 0x72,
	0x47, 0x61, 0x6d, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x20, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x25, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x55, 0x73,
	0x65, 0x72, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x20, 0x2e, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x2b, 0x50, 0x75, 0x62, 0x6c,
	0x69, 0x73, 0x68, 0x55, 0x73, 0x65, 0x72, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61,
	0x69, 0x67, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x44,
	0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x20, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x25, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x55, 0x73,
	0x65, 0x72, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x12, 0x20, 0x2e, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x1c, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73,
	0x6b, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x12, 0x2a, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x6b,
	0x0a, 0x2d, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x55, 0x73, 0x65, 0x72, 0x47, 0x61, 0x6d,
	0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44,
	0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x12,
	0x20, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x25, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x55, 0x73, 0x65, 0x72, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x6f, 0x6c, 0x6c,
	0x43, 0x61, 0x6c, 0x6c, 0x12, 0x20, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00,
	0x12, 0x69, 0x0a, 0x2b, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x55, 0x73, 0x65, 0x72, 0x47,
	0x61, 0x6d, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x6f, 0x6c, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x41, 0x74, 0x4e, 0x6f, 0x65, 0x6c, 0x12,
	0x20, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x68, 0x0a, 0x2a, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x55, 0x73, 0x65, 0x72, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x6f, 0x6e, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x46, 0x69, 0x72, 0x73, 0x74, 0x12, 0x20, 0x2e, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d,
	0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x26, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68,
	0x55, 0x73, 0x65, 0x72, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e,
	0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70, 0x12,
	0x20, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x42, 0x40, 0x5a, 0x3e, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65,
	0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x56, 0x4e, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_eventVN_proto_goTypes = []interface{}{
	(*eventMessage.TrackingCleverTapPayComboMessage)(nil),                  // 0: eventMessage.TrackingCleverTapPayComboMessage
	(*eventMessage.TrackingCleverTapTaskMessage)(nil),                      // 1: eventMessage.TrackingCleverTapTaskMessage
	(*eventMessage.TrackingCleverTapRatingMessage)(nil),                    // 2: eventMessage.TrackingCleverTapRatingMessage
	(*eventMessage.TrackingCleverTapTrainingMessage)(nil),                  // 3: eventMessage.TrackingCleverTapTrainingMessage
	(*eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage)(nil), // 4: eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage
	(*users.Users)(nil),                     // 5: users.Users
	(*eventMessage.EventCommonMessage)(nil), // 6: eventMessage.EventCommonMessage
	(*empty.Empty)(nil),                     // 7: google.protobuf.Empty
}
var file_eventVN_proto_depIdxs = []int32{
	0,  // 0: grpcEventVN.EventVN.TrackingCleverTapPayComboSuccess:input_type -> eventMessage.TrackingCleverTapPayComboMessage
	0,  // 1: grpcEventVN.EventVN.TrackingCleverTapPayComboPaymentError:input_type -> eventMessage.TrackingCleverTapPayComboMessage
	1,  // 2: grpcEventVN.EventVN.TrackingCleverTapTaskerAcceptTask:input_type -> eventMessage.TrackingCleverTapTaskMessage
	1,  // 3: grpcEventVN.EventVN.TrackingCleverTapTaskerCancelTask:input_type -> eventMessage.TrackingCleverTapTaskMessage
	1,  // 4: grpcEventVN.EventVN.TrackingCleverTapTaskerWithdrawTask:input_type -> eventMessage.TrackingCleverTapTaskMessage
	1,  // 5: grpcEventVN.EventVN.TrackingCleverTapTaskerDoneTask:input_type -> eventMessage.TrackingCleverTapTaskMessage
	2,  // 6: grpcEventVN.EventVN.TrackingCleverTapTaskerRating:input_type -> eventMessage.TrackingCleverTapRatingMessage
	3,  // 7: grpcEventVN.EventVN.TrackingCleverTapTaskerFinishTraining:input_type -> eventMessage.TrackingCleverTapTrainingMessage
	4,  // 8: grpcEventVN.EventVN.TrackingCleverTapPrepayTaskPaymentResponse:input_type -> eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage
	5,  // 9: grpcEventVN.EventVN.AddAskerDoneBookingReferralGift:input_type -> users.Users
	5,  // 10: grpcEventVN.EventVN.AddAskerSignUpReferralGift:input_type -> users.Users
	6,  // 11: grpcEventVN.EventVN.PublishUserGameCampaignActionLogin:input_type -> eventMessage.EventCommonMessage
	6,  // 12: grpcEventVN.EventVN.PublishUserGameCampaignActionDoneTask:input_type -> eventMessage.EventCommonMessage
	6,  // 13: grpcEventVN.EventVN.PublishUserGameCampaignActionTaskerDoneTask:input_type -> eventMessage.EventCommonMessage
	6,  // 14: grpcEventVN.EventVN.PublishUserGameCampaignActionReferral:input_type -> eventMessage.EventCommonMessage
	1,  // 15: grpcEventVN.EventVN.TrackingCleverTapTaskExpired:input_type -> eventMessage.TrackingCleverTapTaskMessage
	6,  // 16: grpcEventVN.EventVN.PublishUserGameCampaignActionDoneTaskReferral:input_type -> eventMessage.EventCommonMessage
	6,  // 17: grpcEventVN.EventVN.PublishUserGameCampaignActionRollCall:input_type -> eventMessage.EventCommonMessage
	6,  // 18: grpcEventVN.EventVN.PublishUserGameCampaignActionRollCallAtNoel:input_type -> eventMessage.EventCommonMessage
	6,  // 19: grpcEventVN.EventVN.PublishUserGameCampaignActionDoneTaskFirst:input_type -> eventMessage.EventCommonMessage
	6,  // 20: grpcEventVN.EventVN.PublishUserGameCampaignActionNewSignUp:input_type -> eventMessage.EventCommonMessage
	7,  // 21: grpcEventVN.EventVN.TrackingCleverTapPayComboSuccess:output_type -> google.protobuf.Empty
	7,  // 22: grpcEventVN.EventVN.TrackingCleverTapPayComboPaymentError:output_type -> google.protobuf.Empty
	7,  // 23: grpcEventVN.EventVN.TrackingCleverTapTaskerAcceptTask:output_type -> google.protobuf.Empty
	7,  // 24: grpcEventVN.EventVN.TrackingCleverTapTaskerCancelTask:output_type -> google.protobuf.Empty
	7,  // 25: grpcEventVN.EventVN.TrackingCleverTapTaskerWithdrawTask:output_type -> google.protobuf.Empty
	7,  // 26: grpcEventVN.EventVN.TrackingCleverTapTaskerDoneTask:output_type -> google.protobuf.Empty
	7,  // 27: grpcEventVN.EventVN.TrackingCleverTapTaskerRating:output_type -> google.protobuf.Empty
	7,  // 28: grpcEventVN.EventVN.TrackingCleverTapTaskerFinishTraining:output_type -> google.protobuf.Empty
	7,  // 29: grpcEventVN.EventVN.TrackingCleverTapPrepayTaskPaymentResponse:output_type -> google.protobuf.Empty
	7,  // 30: grpcEventVN.EventVN.AddAskerDoneBookingReferralGift:output_type -> google.protobuf.Empty
	7,  // 31: grpcEventVN.EventVN.AddAskerSignUpReferralGift:output_type -> google.protobuf.Empty
	7,  // 32: grpcEventVN.EventVN.PublishUserGameCampaignActionLogin:output_type -> google.protobuf.Empty
	7,  // 33: grpcEventVN.EventVN.PublishUserGameCampaignActionDoneTask:output_type -> google.protobuf.Empty
	7,  // 34: grpcEventVN.EventVN.PublishUserGameCampaignActionTaskerDoneTask:output_type -> google.protobuf.Empty
	7,  // 35: grpcEventVN.EventVN.PublishUserGameCampaignActionReferral:output_type -> google.protobuf.Empty
	7,  // 36: grpcEventVN.EventVN.TrackingCleverTapTaskExpired:output_type -> google.protobuf.Empty
	7,  // 37: grpcEventVN.EventVN.PublishUserGameCampaignActionDoneTaskReferral:output_type -> google.protobuf.Empty
	7,  // 38: grpcEventVN.EventVN.PublishUserGameCampaignActionRollCall:output_type -> google.protobuf.Empty
	7,  // 39: grpcEventVN.EventVN.PublishUserGameCampaignActionRollCallAtNoel:output_type -> google.protobuf.Empty
	7,  // 40: grpcEventVN.EventVN.PublishUserGameCampaignActionDoneTaskFirst:output_type -> google.protobuf.Empty
	7,  // 41: grpcEventVN.EventVN.PublishUserGameCampaignActionNewSignUp:output_type -> google.protobuf.Empty
	21, // [21:42] is the sub-list for method output_type
	0,  // [0:21] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_eventVN_proto_init() }
func file_eventVN_proto_init() {
	if File_eventVN_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_eventVN_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_eventVN_proto_goTypes,
		DependencyIndexes: file_eventVN_proto_depIdxs,
	}.Build()
	File_eventVN_proto = out.File
	file_eventVN_proto_rawDesc = nil
	file_eventVN_proto_goTypes = nil
	file_eventVN_proto_depIdxs = nil
}
