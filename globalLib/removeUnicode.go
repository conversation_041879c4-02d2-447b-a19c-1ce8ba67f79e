package globalLib

import (
	"bytes"
	"regexp"
	"unicode/utf8"
)

var src_characters = "ÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚÝàáâãèéêìíòóôõùúýĂăĐđĨĩŨũƠơƯưẠạẢảẤấẦầẨẩẪẫẬậẮắẰằẲẳẴẵẶặẸẹẺẻẼẽẾếỀềỂểỄễỆệỈỉỊịỌọỎỏỐốỒồỔổỖỗỘộỚớỜờỞởỠỡỢợỤụỦủỨứỪừỬửỮữỰựỲỳỸỹỶỷỴỵ"
var des_characters = "AAAAEEEIIOOOOUUYaaaaeeeiioooouuyAaDdIiUuOoUuAaAaAaAaAaAaAaAaAaAaAaAaEeEeEeEeEeEeEeEeIiIiOoOoOoOoOoOoOoOoOoOoOoOoUuUuUuUuUuUuUuYyYyYyYy"

func RemoveUnicode(s string) string {
	var buffer bytes.Buffer
	for _, runeValue := range s {
		buffer.WriteString(removeUnicode<PERSON>har(string(runeValue)))
	}
	str := buffer.String()
	return removeSpecialLetter(str)
}
func ConvertVietnameseToLatin(s string) string {
	var buffer bytes.Buffer
	for _, runeValue := range s {
		buffer.WriteString(removeUnicodeChar(string(runeValue)))
	}
	str := buffer.String()
	return str
}
// ==================================

func removeUnicodeChar(ch string) string {
	SOURCE_CHARACTERS, LL_LENGTH := stringToRune(src_characters)
	DESTINATION_CHARACTERS, _ := stringToRune(des_characters)
	var index int = binarySearch(SOURCE_CHARACTERS, ch, 0, LL_LENGTH)
	if index >= 0 {
		ch = DESTINATION_CHARACTERS[index]
	}
	return ch
}

func removeSpecialLetter(s string) string {
	regex := regexp.MustCompile(`[^a-zA-Z0-9]+`)
	return regex.ReplaceAllString(s, " ")
}

func stringToRune(s string) ([]string, int) {
	count := utf8.RuneCountInString(s)
	var texts = make([]string, count+1)
	var index = 0
	for _, runeValue := range s {
		texts[index] = string(runeValue)
		index++
	}
	return texts, count
}

func binarySearch(sortedArray []string, key string, low int, high int) int {
	var middle int = (low + high) / 2
	if high < low {
		return -1
	}
	if key == sortedArray[middle] {
		return middle
	} else if key < sortedArray[middle] {
		return binarySearch(sortedArray, key, low, middle-1)
	} else {
		return binarySearch(sortedArray, key, middle+1, high)
	}
}
