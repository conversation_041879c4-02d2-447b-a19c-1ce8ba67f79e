// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.7.1
// source: grpcmodel/B2BTransaction/B2BTransaction.proto

package B2BTransaction

import (
	timestamp "github.com/golang/protobuf/ptypes/timestamp"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type B2BTransaction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @inject_tag: bson:"_id,omitempty"
	XId string `protobuf:"bytes,1,opt,name=_id,json=Id,proto3" json:"_id,omitempty" bson:"_id,omitempty"`
	// @inject_tag: bson:"from,omitempty"
	From string `protobuf:"bytes,2,opt,name=from,proto3" json:"from,omitempty" bson:"from,omitempty"`
	// @inject_tag: bson:"to,omitempty"
	To string `protobuf:"bytes,3,opt,name=to,proto3" json:"to,omitempty" bson:"to,omitempty"`
	// @inject_tag: bson:"amount,omitempty"
	Amount float64 `protobuf:"fixed64,4,opt,name=amount,proto3" json:"amount,omitempty" bson:"amount,omitempty"`
	// @inject_tag: bson:"createdAt,omitempty"
	CreatedAt *timestamp.Timestamp `protobuf:"bytes,5,opt,name=createdAt,proto3" json:"createdAt,omitempty" bson:"createdAt,omitempty"`
}

func (x *B2BTransaction) Reset() {
	*x = B2BTransaction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpcmodel_B2BTransaction_B2BTransaction_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *B2BTransaction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*B2BTransaction) ProtoMessage() {}

func (x *B2BTransaction) ProtoReflect() protoreflect.Message {
	mi := &file_grpcmodel_B2BTransaction_B2BTransaction_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use B2BTransaction.ProtoReflect.Descriptor instead.
func (*B2BTransaction) Descriptor() ([]byte, []int) {
	return file_grpcmodel_B2BTransaction_B2BTransaction_proto_rawDescGZIP(), []int{0}
}

func (x *B2BTransaction) GetXId() string {
	if x != nil {
		return x.XId
	}
	return ""
}

func (x *B2BTransaction) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *B2BTransaction) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

func (x *B2BTransaction) GetAmount() float64 {
	if x != nil {
		return x.Amount
	}
	return 0
}

func (x *B2BTransaction) GetCreatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

var File_grpcmodel_B2BTransaction_B2BTransaction_proto protoreflect.FileDescriptor

var file_grpcmodel_B2BTransaction_B2BTransaction_proto_rawDesc = []byte{
	0x0a, 0x2d, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x42, 0x32, 0x42, 0x54,
	0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2f, 0x42, 0x32, 0x42, 0x54, 0x72,
	0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0e, 0x42, 0x32, 0x42, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0x97, 0x01, 0x0a, 0x0e, 0x42, 0x32, 0x42, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x0f, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x0e, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74, 0x6f, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x6d, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x01, 0x52, 0x06, 0x61, 0x6d, 0x6f, 0x75, 0x6e, 0x74,
	0x12, 0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x42, 0x5a, 0x40, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65,
	0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f,
	0x42, 0x32, 0x42, 0x54, 0x72, 0x61, 0x6e, 0x73, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_grpcmodel_B2BTransaction_B2BTransaction_proto_rawDescOnce sync.Once
	file_grpcmodel_B2BTransaction_B2BTransaction_proto_rawDescData = file_grpcmodel_B2BTransaction_B2BTransaction_proto_rawDesc
)

func file_grpcmodel_B2BTransaction_B2BTransaction_proto_rawDescGZIP() []byte {
	file_grpcmodel_B2BTransaction_B2BTransaction_proto_rawDescOnce.Do(func() {
		file_grpcmodel_B2BTransaction_B2BTransaction_proto_rawDescData = protoimpl.X.CompressGZIP(file_grpcmodel_B2BTransaction_B2BTransaction_proto_rawDescData)
	})
	return file_grpcmodel_B2BTransaction_B2BTransaction_proto_rawDescData
}

var file_grpcmodel_B2BTransaction_B2BTransaction_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_grpcmodel_B2BTransaction_B2BTransaction_proto_goTypes = []interface{}{
	(*B2BTransaction)(nil),      // 0: B2BTransaction.B2BTransaction
	(*timestamp.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_grpcmodel_B2BTransaction_B2BTransaction_proto_depIdxs = []int32{
	1, // 0: B2BTransaction.B2BTransaction.createdAt:type_name -> google.protobuf.Timestamp
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_grpcmodel_B2BTransaction_B2BTransaction_proto_init() }
func file_grpcmodel_B2BTransaction_B2BTransaction_proto_init() {
	if File_grpcmodel_B2BTransaction_B2BTransaction_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_grpcmodel_B2BTransaction_B2BTransaction_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*B2BTransaction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_grpcmodel_B2BTransaction_B2BTransaction_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_grpcmodel_B2BTransaction_B2BTransaction_proto_goTypes,
		DependencyIndexes: file_grpcmodel_B2BTransaction_B2BTransaction_proto_depIdxs,
		MessageInfos:      file_grpcmodel_B2BTransaction_B2BTransaction_proto_msgTypes,
	}.Build()
	File_grpcmodel_B2BTransaction_B2BTransaction_proto = out.File
	file_grpcmodel_B2BTransaction_B2BTransaction_proto_rawDesc = nil
	file_grpcmodel_B2BTransaction_B2BTransaction_proto_goTypes = nil
	file_grpcmodel_B2BTransaction_B2BTransaction_proto_depIdxs = nil
}
