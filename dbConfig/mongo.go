package dbConfig

import (
	"context"
	"fmt"
	"log"
	"os"
	"sync"
	"time"

	"github.com/slack-go/slack"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
	"go.mongodb.org/mongo-driver/mongo/readpref"
	"go.uber.org/zap"
)

type MongoDriver struct {
	MongoURL        string `mapstructure:"mongo_url"`
	DBName          string `mapstructure:"db_name"`
	MaxPoolSize     uint64 `mapstructure:"max_pool_size"`
	Timeout         int    `mapstructure:"timeout"`
	MaxConnIdleTime int32  `mapstructure:"max_conn_idle_time"`
	Name            string
	Client          *mongo.Client
}

type MongosDriver map[string]*MongoDriver

var (
	onceMongo      *sync.Once
	onceMongoMutex = sync.RWMutex{}
	clientDriver   *mongo.Client
	dbName         string
	timeout        int
	dbOpts         *options.DatabaseOptions
)

func (adapters MongosDriver) Get(name string) (result *MongoDriver) {
	if adapter, ok := adapters[name]; ok {
		result = adapter
	} else {
		panic("Mongo config not exist " + name)
	}
	return
}

func (c *MongoDriver) InitDriverReadPreferenceDefault() {
	c.InitDriver(false)
}

func (c *MongoDriver) InitDriver(isReadPreferenceSecondary bool) {
	if onceMongo == nil {
		onceMongo = &sync.Once{}
	}
	onceMongo.Do(func() {
		onceMongoMutex.Lock()
		var mongoUrl string
		mode := os.Getenv("APPLICATION_MODE")
		timeout = 10
		if c.Timeout > 0 {
			timeout = c.Timeout
		}
		switch mode {
		case "dev", "prod", "ci":
			c.Name = mode
			dbName = os.Getenv("MONGO_DB_NAME")
			mongoUrl = os.Getenv("MONGO_CONNECTION")
		case "test":
			c.Name = mode
			dbName = c.DBName
			mongoUrl = c.MongoURL
			if v := os.Getenv("MONGO_CONNECTION_TEST"); v != "" {
				log.Println("Connecting to MONGO_CONNECTION_TEST:", v)
				mongoUrl = v
			}
		default:
			c.Name = "local"
			dbName = c.DBName
			mongoUrl = c.MongoURL
		}

		log.Printf("[%s] Mongo Driver [connecting]\n", c.Name)
		ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeout)*time.Second)
		defer cancel()
		opts := options.Client().ApplyURI(mongoUrl)
		var maxPoolSize uint64 = 100
		if c.MaxPoolSize > 0 {
			maxPoolSize = c.MaxPoolSize
		}
		opts.SetMaxPoolSize(maxPoolSize)
		reg := RegisterCodec(bson.NewRegistryBuilder()).Build()
		opts.SetRegistry(reg)

		// In minutes
		var maxConnIdleTime int32 = 5
		if c.MaxConnIdleTime > 0 {
			maxConnIdleTime = c.MaxConnIdleTime
		}
		opts.SetMaxConnIdleTime(time.Duration(maxConnIdleTime) * time.Minute)

		client, _ := mongo.Connect(ctx, opts)
		if err := client.Ping(ctx, readpref.Primary()); err != nil {
			dialErrorDriver(c, err, isReadPreferenceSecondary)
			return
		}
		clientDriver = client
		c.Client = client

		// Read Preference
		if isReadPreferenceSecondary {
			rp := readpref.Secondary()
			dbOpts = options.Database().SetReadPreference(rp)
		} else {
			rp := readpref.Primary()
			dbOpts = options.Database().SetReadPreference(rp)
		}

		log.Printf("[%s] Mongo Driver [connected]\n", c.Name)
		onceMongoMutex.Unlock()
	})
}

func GetCollection(name string) *mongo.Collection {
	opts := &options.DatabaseOptions{}
	if dbOpts != nil {
		opts = dbOpts
	}
	return clientDriver.Database(dbName, opts).Collection(name)
}

func DisconnectDB(isoCode, slackToken string, serviceName string) {
	now := time.Now().Format(time.RFC3339)
	// Alert to Slack
	if slackToken != "" {
		postToSlack(slackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[isoCode], globalConstant.SLACK_USER_NAME, fmt.Sprintf("%s - %s - %s", "SERVICE DOWN", serviceName, now))
	}
	// Disconnect DB
	if clientDriver != nil {
		logger, _ := zap.NewProduction()
		defer logger.Sync()
		logger.Info("DB_DISCONNECTED",
			zap.String("time", now),
		)
		if err := clientDriver.Disconnect(context.Background()); err != nil {
			panic(err)
		}
	}
}

func postToSlack(token string, channel string, username string, message string) {
	logger, _ := zap.NewProduction()
	defer logger.Sync()
	if token != "" {
		slackMessageOption := []slack.MsgOption{
			slack.MsgOptionText(message, false),
		}
		if username != "" {
			slackMessageOption = append(slackMessageOption, slack.MsgOptionUsername(username))
		}
		api := slack.New(token)
		_, _, err := api.PostMessage(channel, slackMessageOption...)
		if err != nil {
			logger.Info("SLACK_ERROR",
				zap.Error(err),
				zap.String("channel", channel),
				zap.String("message", message),
			)
		}
	} else {
		logger.Info("SLACK_ERROR_TOKEN_EMPTY",
			zap.String("channel", channel),
			zap.String("message", message),
		)
	}
}

// ===================================

func dialErrorDriver(c *MongoDriver, err error, isReadPreferenceSecondary bool) {
	log.Printf("[%s] Mongo Driver [error]: %v\n", c.Name, err)
	time.Sleep(time.Duration(timeout) * time.Second)
	onceMongo = &sync.Once{}
	onceMongoMutex.Unlock()
	c.InitDriver(isReadPreferenceSecondary)
}
