import { Meteor } from 'meteor/meteor';
import {
  trackingScheduleStatus,
  postToSlack,
} from '/server/helper-function';



const runCheckConfirmedTasks = () => {
  return;
	console.log("Start runCheckConfirmedTask ...");
  var message = null;
  try {
    const errorTasks = Meteor.appCollection.Task.find({
      status: 'CONFIRMED',
      "serviceText.en": {$ne: "Deep Cleaning"},
      "acceptedTasker.1": {$exists: true},
      joinEvent: null
    }, {fields: {
      phone: 1,
      contactName: 1,
      acceptedTasker: 1,
    }}).fetch();

    if ( errorTasks && errorTasks.length > 0 ) {
      let alertText = 'Cần CS xử lý những công việc CONFIRMED và có lỗi nhiều người làm:\n\n';
      errorTasks.forEach(task => {
        alertText += `${task.phone} - ${task.contactName} và có ${task.acceptedTasker.length} người làm.\n`
      });
      postToSlack({
        channel: 'btaskee-system',
        text: alertText,
      });
    }

  } catch (e) {
    message = e;
  }
	console.log("Finished runCheckConfirmedTask ");
  // Tracking
  trackingScheduleStatus({name: 'autoCheckConfirmedTasks', nextRunAt: SyncedCron.nextScheduledAtDate('autoCheckConfirmedTasks'), message});
};

  Meteor.methods({
    autoCheckConfirmedTasks() {
      SyncedCron.add({
        name: 'autoCheckConfirmedTasks',
        schedule(parser) {
          // Run every 10 minutes
          return parser.cron(`0 0/10 0/1 1/1 * ? *`, true);
        },
        job() {
          runCheckConfirmedTasks();
        },
      });
	    console.log("Added autoCheckConfirmedTask cron");
    },
    'runNow.autoCheckConfirmedTasks'() {
      runCheckConfirmedTasks();
    },
  });
