package rollBackMissingTaskActionPenalty

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelFATransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fatransaction"
	"go.mongodb.org/mongo-driver/bson"
)

func getTransactions(transactionIds []string) []*modelFATransaction.FinancialAccountTransaction {
	var transactions []*modelFATransaction.FinancialAccountTransaction
	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], bson.M{"_id": bson.M{"$in": transactionIds}}, bson.M{"_id": 1, "userId": 1, "source": 1, "amount": 1}, &transactions)
	if err != nil || len(transactions) == 0 {
		msg := fmt.Sprintf("[go-sync-cron-vn-v3] RollbackMissingTaskActionPenalty - getTransactions: transaction.length: %d, error: %v", len(transactions), err)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
	}
	return transactions
}
