// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.21.9
// source: smsTH.proto

package grpcSMSTH

import (
	smsSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/smsSending"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_smsTH_proto protoreflect.FileDescriptor

var file_smsTH_proto_rawDesc = []byte{
	0x0a, 0x0b, 0x73, 0x6d, 0x73, 0x54, 0x48, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x67,
	0x72, 0x70, 0x63, 0x53, 0x4d, 0x53, 0x54, 0x48, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x4d, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70,
	0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x73, 0x6d, 0x73, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2f, 0x73, 0x6d, 0x73, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x32, 0xa2, 0x01, 0x0a, 0x05, 0x53, 0x4d, 0x53, 0x54, 0x48, 0x12, 0x56, 0x0a, 0x04,
	0x53, 0x65, 0x6e, 0x64, 0x12, 0x16, 0x2e, 0x73, 0x6d, 0x73, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x53, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x1e, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x18, 0x22, 0x13, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x73, 0x6d, 0x73, 0x2d, 0x74, 0x68, 0x2f, 0x73, 0x65, 0x6e,
	0x64, 0x3a, 0x01, 0x2a, 0x12, 0x41, 0x0a, 0x0d, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x4d, 0x53, 0x56,
	0x69, 0x61, 0x56, 0x4d, 0x47, 0x12, 0x16, 0x2e, 0x73, 0x6d, 0x73, 0x53, 0x65, 0x6e, 0x64, 0x69,
	0x6e, 0x67, 0x2e, 0x53, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x42, 0x3e, 0x5a, 0x3c, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f,
	0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d,
	0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x67, 0x72,
	0x70, 0x63, 0x53, 0x4d, 0x53, 0x54, 0x48, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_smsTH_proto_goTypes = []interface{}{
	(*smsSending.SmsRequest)(nil), // 0: smsSending.SmsRequest
	(*emptypb.Empty)(nil),         // 1: google.protobuf.Empty
}
var file_smsTH_proto_depIdxs = []int32{
	0, // 0: grpcSMSTH.SMSTH.Send:input_type -> smsSending.SmsRequest
	0, // 1: grpcSMSTH.SMSTH.SendSMSViaVMG:input_type -> smsSending.SmsRequest
	1, // 2: grpcSMSTH.SMSTH.Send:output_type -> google.protobuf.Empty
	1, // 3: grpcSMSTH.SMSTH.SendSMSViaVMG:output_type -> google.protobuf.Empty
	2, // [2:4] is the sub-list for method output_type
	0, // [0:2] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_smsTH_proto_init() }
func file_smsTH_proto_init() {
	if File_smsTH_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_smsTH_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_smsTH_proto_goTypes,
		DependencyIndexes: file_smsTH_proto_depIdxs,
	}.Build()
	File_smsTH_proto = out.File
	file_smsTH_proto_rawDesc = nil
	file_smsTH_proto_goTypes = nil
	file_smsTH_proto_depIdxs = nil
}
