package autoUpgradeMedalForCommunityUser

import (
	"fmt"
	"log"

	"github.com/robfig/cron/v3"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/model"
)

var (
	c            *cron.Cron
	cfg          = config.GetConfig()
	isRunning    = false
	paramsEnough = true
	runAt        string
)

func init() {
	var cronConfig map[string]interface{}
	if cfg != nil {
		cronConfig = cfg.ScheduleSyncCron["autoupgrademedalforcommunityuser"]
	}
	// Check if params enough
	if cronConfig["run_at"] == nil {
		paramsEnough = false
		lib.PostToSlackNotStart("AutoUpgradeMedalForCommunityUser")
		return
	}

	// Get runAt string for sync cron
	runAt = fmt.Sprintf(cronConfig["run_at"].(string))

	// Check if config has is_run = true
	if cronConfig["is_run"] == nil || !cronConfig["is_run"].(bool) {
		lib.PostToSlackNotStart("AutoUpgradeMedalForCommunityUser")
		return
	}

	// Start cron if config["is_run"] = true
	StartCron()
}

func Run(reqBody *model.RequestAction) map[string]interface{} {
	// action == 1 -> Run sync cron once now
	if reqBody.Action == lib.RUN_NOW {
		autoUpgradeMedalForCommunityUser()
	}

	// action == 2 -> Start sync cron with schedule config
	if reqBody.Action == lib.START && paramsEnough && !isRunning {
		StartCron()
	}
	// action == 3 -> Stop running sync cron
	if reqBody.Action == lib.STOP && isRunning {
		c.Stop()
		c = nil
		isRunning = false
	}
	return nil
}

func autoRun() {
	log.Println("Start AutoUpgradeMedalForCommunityUser Process")
	defer log.Println("Finish AutoUpgradeMedalForCommunityUser Process")
	autoUpgradeMedalForCommunityUser()
}

func StartCron() {
	c := cron.New(cron.WithLocation(local.TimeZone))
	c.AddFunc(runAt, autoRun)
	c.Start()
	isRunning = true
}

func autoUpgradeMedalForCommunityUser() {
	communityUser := GetCommunityUserInfo()
	if communityUser == nil && len(communityUser) == 0 {
		return
	}

	communityMedal := getCommunityMedalInfo()
	if communityMedal == nil && len(communityMedal) == 0 {
		return
	}

	for _, user := range communityUser {
		for _, medal := range communityMedal {
			if user != nil && medal != nil && checkUserCanUpgradeMedal(medal, user) {
				err := processUpgradeMedalForUser(user.XId, medal.XId, medal.TagIds)
				if err == nil {
					insertNotification(user, medal.Text, medal.XId)
				}
			}
		}
	}
}
