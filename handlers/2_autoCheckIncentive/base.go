package autoCheckIncentive

import (
	"fmt"
	"log"

	"github.com/robfig/cron/v3"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
)

var c *cron.Cron
var cfg = config.GetConfig()
var isRunning = false
var paramsEnough = true
var runAt string

const (
	CODE_QUANTITY_ALERT         = 10
	CODE_QUANTITY_WILL_INACTIVE = 2
)

type UrBoxGift struct {
	ID           string      `json:"id"`
	Title        string      `json:"title"`
	CodeQuantity interface{} `json:"code_quantity"`
}
type Response struct {
	Data struct {
		Items []UrBoxGift `json:"items"`
	} `json:"data"`
}

func init() {
	var cronConfig map[string]interface{}
	if cfg != nil {
		cronConfig = cfg.ScheduleSyncCron["autocheckincentive"]
	}
	// Check if params enough
	if cronConfig["run_at"] == nil {
		paramsEnough = false
		lib.PostToSlackNotStart("AutoCheckIncentive")
		return
	}

	// Get runAt string for sync cron
	runAt = fmt.Sprintf(cronConfig["run_at"].(string))

	// Check if config has is_run = true
	if cronConfig["is_run"] == nil || !cronConfig["is_run"].(bool) {
		lib.PostToSlackNotStart("AutoCheckIncentive")
		return
	}

	StartCron()
}

func StartCron() {
	c := cron.New(cron.WithLocation(local.TimeZone))
	c.AddFunc(runAt, AutoCheckIncentive)
	c.Start()
	isRunning = true
}

func Run(action int) {
	// action == 1 -> Run sync cron once now
	if action == lib.RUN_NOW {
		AutoCheckIncentive()
	}
	// action == 2 -> Start sync cron with schedule config
	if action == lib.START && paramsEnough && !isRunning {
		StartCron()
	}
	// action == 3 -> Stop running sync cron
	if action == lib.STOP && isRunning {
		c.Stop()
		c = nil
		isRunning = false
	}
}

func AutoCheckIncentive() {
	log.Println("Start AutoCheckIncentive Process")
	defer log.Println("Finish AutoCheckIncentive Process")
	// Get list gift from URBOX
	listGiftsFromURBOX := getListGiftFromURBOX()
	if len(listGiftsFromURBOX) == 0 {
		return
	}
	//	Check if gift need update quantity
	newItemsNeedUpdateQuantity := getGiftsNeedUpdateQuantity(listGiftsFromURBOX)
	// Check if gift need inactive
	itemsNeedUpdateStatus := getGiftsNeedUpdateStatus(listGiftsFromURBOX)
	// Check if gift need inactive from incentive
	listGiftIdNeedInactive, listIncentiveIdNeedInactive := getGiftNeedInactiveFromIncentive(itemsNeedUpdateStatus)
	// Update gift
	updateGifts(listIncentiveIdNeedInactive)
	// Post to slack
	postToSlack(newItemsNeedUpdateQuantity, itemsNeedUpdateStatus, listGiftIdNeedInactive)

}
