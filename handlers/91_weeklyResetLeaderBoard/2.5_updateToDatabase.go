package weeklyResetLeaderBoard

import (
	"encoding/json"
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.mongodb.org/mongo-driver/bson"
)

func updateToDatabase(cities map[string]ModelGroupTaskerByLevel) error {
	// Insert new rank to database
	err := updateLeaderBoard(cities)
	if err != nil {
		return err
	}

	return nil
}

// 3. insert new leader board
func updateLeaderBoard(cities map[string]ModelGroupTaskerByLevel) error {
	// Delete leader board
	err := globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_JOURNEY_LEADER_BOARD[local.ISO_CODE], bson.M{})
	if err != nil {
		msg := fmt.Sprintf("RESET_WEEKLY_LEADER_BOARD_VN: Delete leaderBoard - err: %v", err)
		globalLib.PostToSlack(cfg.SlackToken, "go-journey-tasker", globalConstant.SLACK_USER_NAME, msg)
	}
	newRankData := []interface{}{}
	mapTaskerIsExist := make(map[string]bool)
	for _, levels := range cities {
		for _, level := range levels {
			for _, tasker := range level {
				if mapTaskerIsExist[tasker.XId] {
					continue
				}
				element := bson.M{}
				elementData, err := json.Marshal(tasker)
				if err != nil {
					err = fmt.Errorf("RESET_WEEKLY_LEADER_BOARD_VN: insertNewLeaderBoard - mashal element: %v", err)
					return err
				}
				err = json.Unmarshal(elementData, &element)
				if err != nil {
					err = fmt.Errorf("RESET_WEEKLY_LEADER_BOARD_VN: insertNewLeaderBoard - unmashal element data: %v", err)
					return err
				}
				element["createdAt"] = globalLib.GetCurrentTime(local.TimeZone)
				newRankData = append(newRankData, element)
				mapTaskerIsExist[tasker.XId] = true
			}
		}
	}

	for _, chunkData := range globalLib.SplitSliceByChunk(newRankData, CHUNK_SIZE_INSERT_LEADER_BOARD) {
		err := globalDataAccess.InsertAll(globalCollection.COLLECTION_JOURNEY_LEADER_BOARD[local.ISO_CODE], chunkData)
		if err != nil {
			msg := fmt.Sprintf("RESET_WEEKLY_LEADER_BOARD_VN: Insert leaderBoard data: %v - err: %v", chunkData, err)
			globalLib.PostToSlack(cfg.SlackToken, "go-journey-tasker", globalConstant.SLACK_USER_NAME, msg)
		}
	}
	return nil
}
