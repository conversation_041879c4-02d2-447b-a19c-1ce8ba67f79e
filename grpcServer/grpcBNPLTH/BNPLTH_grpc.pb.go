// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcBNPLTH

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	taskerBNPLRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerBNPLRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// BNPLTHClient is the client API for BNPLTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BNPLTHClient interface {
	ChargeTaskerBNPLFee(ctx context.Context, in *taskerBNPLRequest.TaskerBNPLRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type bNPLTHClient struct {
	cc grpc.ClientConnInterface
}

func NewBNPLTHClient(cc grpc.ClientConnInterface) BNPLTHClient {
	return &bNPLTHClient{cc}
}

func (c *bNPLTHClient) ChargeTaskerBNPLFee(ctx context.Context, in *taskerBNPLRequest.TaskerBNPLRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcBNPLTH.BNPLTH/ChargeTaskerBNPLFee", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BNPLTHServer is the server API for BNPLTH service.
// All implementations must embed UnimplementedBNPLTHServer
// for forward compatibility
type BNPLTHServer interface {
	ChargeTaskerBNPLFee(context.Context, *taskerBNPLRequest.TaskerBNPLRequest) (*empty.Empty, error)
	mustEmbedUnimplementedBNPLTHServer()
}

// UnimplementedBNPLTHServer must be embedded to have forward compatible implementations.
type UnimplementedBNPLTHServer struct {
}

func (UnimplementedBNPLTHServer) ChargeTaskerBNPLFee(context.Context, *taskerBNPLRequest.TaskerBNPLRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChargeTaskerBNPLFee not implemented")
}
func (UnimplementedBNPLTHServer) mustEmbedUnimplementedBNPLTHServer() {}

// UnsafeBNPLTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BNPLTHServer will
// result in compilation errors.
type UnsafeBNPLTHServer interface {
	mustEmbedUnimplementedBNPLTHServer()
}

func RegisterBNPLTHServer(s *grpc.Server, srv BNPLTHServer) {
	s.RegisterService(&_BNPLTH_serviceDesc, srv)
}

func _BNPLTH_ChargeTaskerBNPLFee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskerBNPLRequest.TaskerBNPLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BNPLTHServer).ChargeTaskerBNPLFee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBNPLTH.BNPLTH/ChargeTaskerBNPLFee",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BNPLTHServer).ChargeTaskerBNPLFee(ctx, req.(*taskerBNPLRequest.TaskerBNPLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _BNPLTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcBNPLTH.BNPLTH",
	HandlerType: (*BNPLTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ChargeTaskerBNPLFee",
			Handler:    _BNPLTH_ChargeTaskerBNPLFee_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "BNPLTH.proto",
}
