package autoMoveChatToHistory

import (
	"fmt"
	"log"
	"sync"

	"github.com/robfig/cron/v3"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTaskerBNPL "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerBNPLProcess"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
)

var (
	c            *cron.Cron
	cfg          = config.GetConfig()
	isRunning    = false
	paramsEnough = true
	runAt        string
	LIMIT        int64 = 2000
)

type TaskerBNPLInfo struct {
	Tasker     *modelUsers.Users
	TaskerBNPL *modelTaskerBNPL.TaskerBNPLProcess
}

func init() {
	var cronConfig map[string]interface{}
	if cfg != nil {
		cronConfig = cfg.ScheduleSyncCron["automovechattohistory"]
	}
	// Check if params enough
	if cronConfig["run_at"] == nil {
		paramsEnough = false
		lib.PostToSlackNotStart("AutoMoveChatToHistory")
		return
	}

	// Get runAt string for sync cron
	runAt = fmt.Sprintf(cronConfig["run_at"].(string))

	// Check if config has is_run = true
	if cronConfig["is_run"] == nil || !cronConfig["is_run"].(bool) {
		lib.PostToSlackNotStart("AutoMoveChatToHistory")
		return
	}

	// Start cron if config["is_run"] = true
	StartCron()
}

func StartCron() {
	c := cron.New(cron.WithLocation(local.TimeZone))
	c.AddFunc(runAt, autoRun)
	c.Start()
	isRunning = true
}

func Run(reqBody *model.RequestAction) map[string]interface{} {
	// action == 1 -> Run sync cron once now
	if reqBody.Action == lib.RUN_NOW {
		autoMoveChatToHistory()
	}

	// action == 2 -> Start sync cron with schedule config
	if reqBody.Action == lib.START && paramsEnough && !isRunning {
		StartCron()
	}
	// action == 3 -> Stop running sync cron
	if reqBody.Action == lib.STOP && isRunning {
		c.Stop()
		c = nil
		isRunning = false
	}
	return nil
}

func autoRun() {
	log.Println("Start AutoMoveChatToHistory Process")
	defer log.Println("Finish AutoMoveChatToHistory Process")
	autoMoveChatToHistory()
}

func autoMoveChatToHistory() {
	listChatIds := []string{}
	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	w := &sync.WaitGroup{}
	w.Add(1)
	chatId := getChatConversation(w, currentTime)
	w.Add(1)
	outDataCh := processChatMessages(w, chatId)
	w.Add(1)
	chatIds := processChatConversations(w, outDataCh)
	w.Add(1)
	go func(chatIds chan string) {
		defer w.Done()
		for c := range chatIds {
			listChatIds = append(listChatIds, c)
		}
	}(chatIds)
	w.Wait()
	// removeChatConversation(listChatIds)
}
