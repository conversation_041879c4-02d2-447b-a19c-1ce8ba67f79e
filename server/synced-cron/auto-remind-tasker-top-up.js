import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import _ from 'lodash';
import {
  trackingScheduleStatus,
  revertTimeZone,
  sendNotificationByIds,
} from '/server/helper-function';

const sendWarningNotification = (userIds) => {

  const title = {
    vi: `Thông báo nạp tiền.`,
    en: `Thông báo nạp tiền.`,
    ko: `Thông báo nạp tiền.`,
    th: `Thông báo nạp tiền.`,
  };
//TODO : translate the text below
  const text = {
    vi: `Bạn có lịch làm việc cố định của khách hàng, vì thế bạn nên duy trì tài khoản tối thiểu 200,000 vnd để đảm bảo công việc không bị gián đoạn vì thiếu tiền nhận việc. Ch<PERSON><PERSON> bạn một ngày làm việc thật tốt.`,
    en: `<PERSON>ạn có lịch làm việc cố định của khách hàng, vì thế bạn nên duy trì tài khoản tối thiểu 200,000 vnd để đảm bảo công việc không bị gián đoạn vì thiếu tiền nhận việc. Chúc bạn một ngày làm việc thật tốt.`,
    ko: `Bạn có lịch làm việc cố định của khách hàng, vì thế bạn nên duy trì tài khoản tối thiểu 200,000 vnd để đảm bảo công việc không bị gián đoạn vì thiếu tiền nhận việc. Chúc bạn một ngày làm việc thật tốt.`,
    th: `Bạn có lịch làm việc cố định của khách hàng, vì thế bạn nên duy trì tài khoản tối thiểu 200,000 vnd để đảm bảo công việc không bị gián đoạn vì thiếu tiền nhận việc. Chúc bạn một ngày làm việc thật tốt.`,
  };

  sendNotificationByIds(userIds, {
    title,
    text,
    payload: {},
  }, { isForceView: true});
};

const runRemindTaskerTopup = () => {
  return;
	console.log("Start runRemindTaskerTopup ...");
  var message = null;
  try {
    let lessMoneyTaskers = [];
    // Find subscription choose tasker
    const taskerIdArr = Meteor.appCollection.TaskSchedule.find({
      status: "ACTIVE",
      forceAcceptTaskerId: { $exists: 1 }
    }, { fields: { forceAcceptTaskerId: 1}}).map( schedule => schedule.forceAcceptTaskerId);
    
    // Filter for duplicate items in the array
    const uniqueTaskerIdArr = Array.from(new Set(taskerIdArr));
    uniqueTaskerIdArr.forEach( (taskerId) => {
      const tasker = Meteor.appCollection.users.findOne({ _id: taskerId }, { fields: { fAccountId: 1 } });
      const fAccount = Meteor.appCollection.FinancialAccount.findOne({_id: tasker.fAccountId});
      let holdingAmount = 0;
      let amountTasker = fAccount.FMainAccount + fAccount.Promotion;
      
      // get the confirmed and waiting task of that taskerId
      Meteor.appCollection.Task.find(
        { status: { $in: ['CONFIRMED', 'WAITING_ASKER_CONFIRMATION']},
        'acceptedTasker.taskerId': taskerId  }, 
        { fields: { detailDeepCleaning: 1, cost: 1, serviceText: 1}}).forEach(task => {
          //get task cost for holding
          const isDeepCleaningTask = _.get(task, 'serviceText.en', null) === 'Deep Cleaning';
          if (isDeepCleaningTask) {
            holdingAmount += _.get(task, 'detailDeepCleaning.costPerTasker.total', 0);
          } else {
            // Other services - NOT Deep Cleaning
            holdingAmount += task.cost;
          }
        });
      // get 15 % of the task cost
	    // TODO: use task rate instead of hardcode
      holdingAmount = Math.round(holdingAmount * 0.20);
      // the amount after deducting the waiting and confirmed tasks
      // If the amount is less than 50,000 vnd, notify the tasker.
      const moneyTotal = amountTasker - holdingAmount;
	    // TODO: fix this for TH
      if (moneyTotal < 200000) {
        lessMoneyTaskers.push(taskerId);
      }
    });
    if ( lessMoneyTaskers.length > 0 ) {
      sendWarningNotification(lessMoneyTaskers);
    }

  } catch (e) {
    message = e;
  }
	console.log("Finished runRemindTaskerTopup ");
  // Tracking
  trackingScheduleStatus({name: 'autoRemindTaskerTopup', nextRunAt: SyncedCron.nextScheduledAtDate('autoRemindTaskerTopup'), message});
};

  Meteor.methods({
    autoRemindTaskerTopup() {
      SyncedCron.add({
        name: 'autoRemindTaskerTopup',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016,1,1,7)).getHours();
          // Run at 7:10 AM every day with timezone GMT+7.
          return parser.cron(`0 10 ${hourInTimeZoneServer} 1/1 * ? *`, true);
        },
        job() {
          runRemindTaskerTopup();
        },
      });
	    console.log("Added autoRemindTaskerTopup cron");
    },
    'runNow.autoRemindTaskerTopup'() {
      runRemindTaskerTopup();
    },
  });
