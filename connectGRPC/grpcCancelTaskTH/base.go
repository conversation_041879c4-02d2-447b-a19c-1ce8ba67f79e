package grpcCancelTaskTH

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcCancelTaskTH"
	"google.golang.org/grpc"
)

func ConnectGRPCCancelTaskTH(endpoint string) (grpcCancelTaskTH.CancelTaskTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcCancelTaskTH.NewCancelTaskTHClient(connect)
	return client, connect, err
}
