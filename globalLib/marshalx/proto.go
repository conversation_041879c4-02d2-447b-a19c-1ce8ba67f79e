package marshalx

import (
	"io"

	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/proto"
)

// Description: Unmarshal request body to protobuf message
// @param r io.Reader: request body
// @param v proto.Message: protobuf message
// @return error: error
// @CreatedAt: 03/04/2025
// @Note: This function will only discard unknown fields, not discard invalid fields type
func ProtoUnmarshalDiscardUnknown(r io.Reader, v proto.Message) error {
	bodyBytes, err := io.ReadAll(r)
	if err != nil {
		return err
	}

	// Decode request body
	unmarshaler := protojson.UnmarshalOptions{DiscardUnknown: true}
	err = unmarshaler.Unmarshal(bodyBytes, v)
	if err != nil {
		return err
	}

	return nil
}
