package grpcEventVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcEventVN"
	"google.golang.org/grpc"
)

func ConnectGRPCEventVN(endpoint string) (grpcEventVN.EventVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcEventVN.NewEventVNClient(connect)
	return client, connect, err
}
