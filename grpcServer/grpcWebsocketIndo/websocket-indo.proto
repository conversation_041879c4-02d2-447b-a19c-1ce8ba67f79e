syntax = "proto3";
package grpcWebsocketIndo;

option go_package = "gitlab.com/btaskee/********************/grpcWebsocketIndo";

import "google/protobuf/empty.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage/websocketMessage.proto";

service WebsocketIndo {
  rpc SendChatMessage (websocketMessage.WebsocketMessage) returns (google.protobuf.Empty) {}
  rpc SendSocketMessage (websocketMessage.WebsocketMessage) returns (google.protobuf.Empty) {}
}