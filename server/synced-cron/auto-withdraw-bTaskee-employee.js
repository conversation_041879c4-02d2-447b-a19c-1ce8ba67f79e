import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import _ from 'lodash';
import {
  trackingScheduleStatus,
  revertTimeZone,
  changeTimeZone
} from '/server/helper-function';

const withdrawLastTopup = (asker) => {
  if ( asker ) {
    const timeInVN = changeTimeZone(new Date());
    const startOfMonth = moment(timeInVN).startOf('month').toDate();
    const endOfMonth = moment(timeInVN).endOf('month').toDate();
  
    const startDate = revertTimeZone(startOfMonth);
    const endDate = revertTimeZone(endOfMonth);
    const lastTopup = Meteor.appCollection.FATransaction.findOne({
      userId: asker._id,
      date: {
        $gte: startDate,
        $lte: endDate
      },
      'source.name': 'DEPOSIT',
      'source.value': 'BTASKEE_EMPLOYEE_TOPUP'
    }, {sort: {date: -1}});
    if ( lastTopup ) {
      const usage = Meteor.appCollection.FATransaction.find({
        userId: asker._id,
        date: {$gt: lastTopup.date},
        type: 'C',
        accountType: 'M'
      }).fetch().reduce((a, b) => {return a + b.amount}, 0);

      let remain = lastTopup.amount - usage;

      
      // Only withdraw when remain > 0
      if ( remain > 0 ) {
        // const finance = Meteor.appCollection.FinancialAccount.findOne({_id: asker.fAccountId});

        Meteor.appCollection.FinancialAccount.update({
          _id: asker.fAccountId
        }, {
          $inc: {
            FMainAccount: -remain
          }
        });

        Meteor.appCollection.FATransaction.insert({
          userId: asker._id,
          accountType: 'M',
          type: 'C',
          source: {
            name: 'WITHDRAW',
            value: 'BTASKEE_EMPLOYEE_RESET',
          },
          amount: remain,
          date: new Date(),
        });
      }
    }
  }
};


//TODO : Don't need to cancel , combine withdraw and topup -> reset allowance
const cancelTaskNewMonth = (askerId) => {
  // Cancel all tasks in new months
  Meteor.appCollection.Task.update({
    askerId: askerId,
    status: {$in: ['POSTED', 'WAITING_ASKER_CONFIRMATION', 'CONFIRMED']},
    date: {$gte: new Date()},
    'payment.method': 'CREDIT',
  }, {
    $set: {
      status: 'CANCELED',
      updatedAt: new Date(),
      isNotChargeAsker: true,
      cancellationText: 'Reset công việc đầu tháng',
      isCancelledByBackend: true,
    },
    $push: {
      changesHistory: {
        key: "CANCEL_TASK",
        from: 'BACKEND',
        user: 'admin',
        content: {
          reason: 'Reset công việc đầu tháng',
          isNotChargeAsker: true,
        },
        createdAt: new Date(),
        createdBy: 'SYSTEM',
      }
    }
  }, {
    multi: true
  });
};

const withdrawAndCancelTask = () => {
  return;
	console.log("Start withdrawAndCancelTask ...");
  var message = null;
  try {
    
    const timeInVN = changeTimeZone(new Date());
    let startDate = moment(timeInVN).endOf('month').startOf('date').toDate();
    let endDate = moment(timeInVN).endOf('month').endOf('date').toDate();
  
    startDate = revertTimeZone(startDate);
    endDate = revertTimeZone(endDate);

    // Only run on the end of month
    if ( moment().isAfter(startDate) && moment().isBefore(endDate) ) {
      Meteor.appCollection.BEmployee.find({}).forEach(employee => {
        // On each employee:
        const asker = Meteor.appCollection.users.findOne({phone: employee.phone});
        if (asker && asker._id && asker.fAccountId) {
          withdrawLastTopup(asker);
          cancelTaskNewMonth(asker._id);
        }
      });
    }


    
     
  } catch (ex) {
    console.log('==============withdrawAndCancelTask======================');
    console.log(ex);
    console.log('====================================');
    message = ex;

  }
	console.log("Finished withdrawAndCancelTask ");
  trackingScheduleStatus({name: 'autoWithdrawEmployee', nextRunAt: SyncedCron.nextScheduledAtDate('autoWithdrawEmployee'), message});
};


  Meteor.methods({
    // Auto withdraw bTaskee employees
    autoWithdrawEmployee() {
      SyncedCron.add({
        name: 'autoWithdrawEmployee',
        schedule(parser) {

          const hourInTimeZoneServer = revertTimeZone(new Date(2016,1,1,23)).getHours();
          return parser.cron(`0 0 ${hourInTimeZoneServer} 1/1 * ? *`, true);
        },
        job() {
          withdrawAndCancelTask();
        },
      });
	    console.log("Added autoWithdrawEmployee cron");
    },
    'runNow.autoWithdrawEmployee'() {
      withdrawAndCancelTask();
    },
  });
