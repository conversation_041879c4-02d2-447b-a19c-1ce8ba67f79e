syntax = "proto3";
package grpcPricingMY;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPricingMY";

import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingrequest/pricing-request.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse/pricing-response.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionRequest/pricing-subscription-request.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionResponse/pricing-subscription-response.proto";

service Pricing {
  rpc GetPricingHomeCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-my/home-cleaning"
      body: "*"
    };
  }
  rpc GetPricingDeepCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-my/deep-cleaning"
      body: "*"
    };
  }
  rpc RePricingHomeCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-my/re-pricing-home-cleaning"
      body: "*"
    };
  }
  rpc RePricingDeepCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-my/re-pricing-deep-cleaning"
      body: "*"
    };
  }
  rpc ReCalculatePricing (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-my/re-calculate"
      body: "*"
    };
  }
  rpc GetPricingOfficeCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {}
  rpc APIGetPricingOfficeCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-my/office-cleaning"
      body: "*"
    };
  }
  rpc RePricingOfficeCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-my/re-pricing-office-cleaning"
      body: "*"
    };
  }
  rpc GetPricingSubscription (pricingSubscriptionRequest.PricingSubscriptionRequest) returns (pricingSubscriptionResponse.PricingSubscriptionResponse) {
    option (google.api.http) = {
      post: "/api/v3/pricing-my/subscription"
      body: "*"
    };
  }
  rpc GetPricingSofa (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-my/sofa"
      body: "*"
    };
  }
  rpc RePricingSofa (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-my/re-pricing-sofa"
      body: "*"
    };
  }
  rpc GetPricingAirConditionerV2 (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-my/air-conditioner-v2"
      body: "*"
    };
  }
  rpc APIGetPricingTaskDateOptions (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-my/task-date-options"
      body: "*"
    };
  }
  rpc ReCalculatePricingForIncreaseDuration (pricingrequest.PricingIncreaseDurationRequest) returns (pricingresponse.CostResult) {}
}