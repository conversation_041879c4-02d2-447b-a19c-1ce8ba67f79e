package globalDataAccessV2

import (
	"context"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcDataAccess"
	modelDataAccess "gitlab.com/btaskee/go-services-model-v2/grpcmodel/dataAccess"
)

func (d *DataAccess) InsertOne(collectionName string, data interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(DATA_ACCESS_URL[d.isoCode][MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	b, err := EncodeData(data)
	if err != nil {
		return err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Data: b}
	_, err = client.InsertOne(context.Background(), request)
	return err
}

func (d *DataAccess) InsertAll(collectionName string, data []interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(DATA_ACCESS_URL[d.isoCode][MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	b, err := EncodeListData(data)
	if err != nil {
		return err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Data: b}
	_, err = client.InsertAll(context.Background(), request)
	return err
}
