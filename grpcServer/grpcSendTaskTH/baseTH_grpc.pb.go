// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcSendTaskTH

import (
	context "context"
	pushNotificationNewTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PushNotificationNewTaskTHClient is the client API for PushNotificationNewTaskTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PushNotificationNewTaskTHClient interface {
	NewTask(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	TopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	NewTaskToDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	FavTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	Normal(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	FavAndTaskerDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	FavAndTopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	NewTaskToCity(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
}

type pushNotificationNewTaskTHClient struct {
	cc grpc.ClientConnInterface
}

func NewPushNotificationNewTaskTHClient(cc grpc.ClientConnInterface) PushNotificationNewTaskTHClient {
	return &pushNotificationNewTaskTHClient{cc}
}

func (c *pushNotificationNewTaskTHClient) NewTask(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskTH.PushNotificationNewTaskTH/NewTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationNewTaskTHClient) TopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskTH.PushNotificationNewTaskTH/TopTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationNewTaskTHClient) NewTaskToDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskTH.PushNotificationNewTaskTH/NewTaskToDistrict", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationNewTaskTHClient) FavTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskTH.PushNotificationNewTaskTH/FavTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationNewTaskTHClient) Normal(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskTH.PushNotificationNewTaskTH/Normal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationNewTaskTHClient) FavAndTaskerDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskTH.PushNotificationNewTaskTH/FavAndTaskerDistrict", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationNewTaskTHClient) FavAndTopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskTH.PushNotificationNewTaskTH/FavAndTopTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationNewTaskTHClient) NewTaskToCity(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskTH.PushNotificationNewTaskTH/NewTaskToCity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PushNotificationNewTaskTHServer is the server API for PushNotificationNewTaskTH service.
// All implementations must embed UnimplementedPushNotificationNewTaskTHServer
// for forward compatibility
type PushNotificationNewTaskTHServer interface {
	NewTask(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	TopTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	NewTaskToDistrict(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	FavTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	Normal(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	FavAndTaskerDistrict(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	FavAndTopTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	NewTaskToCity(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	mustEmbedUnimplementedPushNotificationNewTaskTHServer()
}

// UnimplementedPushNotificationNewTaskTHServer must be embedded to have forward compatible implementations.
type UnimplementedPushNotificationNewTaskTHServer struct {
}

func (UnimplementedPushNotificationNewTaskTHServer) NewTask(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewTask not implemented")
}
func (UnimplementedPushNotificationNewTaskTHServer) TopTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TopTasker not implemented")
}
func (UnimplementedPushNotificationNewTaskTHServer) NewTaskToDistrict(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewTaskToDistrict not implemented")
}
func (UnimplementedPushNotificationNewTaskTHServer) FavTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FavTasker not implemented")
}
func (UnimplementedPushNotificationNewTaskTHServer) Normal(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Normal not implemented")
}
func (UnimplementedPushNotificationNewTaskTHServer) FavAndTaskerDistrict(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FavAndTaskerDistrict not implemented")
}
func (UnimplementedPushNotificationNewTaskTHServer) FavAndTopTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FavAndTopTasker not implemented")
}
func (UnimplementedPushNotificationNewTaskTHServer) NewTaskToCity(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewTaskToCity not implemented")
}
func (UnimplementedPushNotificationNewTaskTHServer) mustEmbedUnimplementedPushNotificationNewTaskTHServer() {
}

// UnsafePushNotificationNewTaskTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PushNotificationNewTaskTHServer will
// result in compilation errors.
type UnsafePushNotificationNewTaskTHServer interface {
	mustEmbedUnimplementedPushNotificationNewTaskTHServer()
}

func RegisterPushNotificationNewTaskTHServer(s *grpc.Server, srv PushNotificationNewTaskTHServer) {
	s.RegisterService(&_PushNotificationNewTaskTH_serviceDesc, srv)
}

func _PushNotificationNewTaskTH_NewTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationNewTaskTHServer).NewTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskTH.PushNotificationNewTaskTH/NewTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationNewTaskTHServer).NewTask(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotificationNewTaskTH_TopTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationNewTaskTHServer).TopTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskTH.PushNotificationNewTaskTH/TopTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationNewTaskTHServer).TopTasker(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotificationNewTaskTH_NewTaskToDistrict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationNewTaskTHServer).NewTaskToDistrict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskTH.PushNotificationNewTaskTH/NewTaskToDistrict",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationNewTaskTHServer).NewTaskToDistrict(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotificationNewTaskTH_FavTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationNewTaskTHServer).FavTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskTH.PushNotificationNewTaskTH/FavTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationNewTaskTHServer).FavTasker(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotificationNewTaskTH_Normal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationNewTaskTHServer).Normal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskTH.PushNotificationNewTaskTH/Normal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationNewTaskTHServer).Normal(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotificationNewTaskTH_FavAndTaskerDistrict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationNewTaskTHServer).FavAndTaskerDistrict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskTH.PushNotificationNewTaskTH/FavAndTaskerDistrict",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationNewTaskTHServer).FavAndTaskerDistrict(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotificationNewTaskTH_FavAndTopTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationNewTaskTHServer).FavAndTopTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskTH.PushNotificationNewTaskTH/FavAndTopTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationNewTaskTHServer).FavAndTopTasker(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotificationNewTaskTH_NewTaskToCity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationNewTaskTHServer).NewTaskToCity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskTH.PushNotificationNewTaskTH/NewTaskToCity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationNewTaskTHServer).NewTaskToCity(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PushNotificationNewTaskTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcSendTaskTH.PushNotificationNewTaskTH",
	HandlerType: (*PushNotificationNewTaskTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "NewTask",
			Handler:    _PushNotificationNewTaskTH_NewTask_Handler,
		},
		{
			MethodName: "TopTasker",
			Handler:    _PushNotificationNewTaskTH_TopTasker_Handler,
		},
		{
			MethodName: "NewTaskToDistrict",
			Handler:    _PushNotificationNewTaskTH_NewTaskToDistrict_Handler,
		},
		{
			MethodName: "FavTasker",
			Handler:    _PushNotificationNewTaskTH_FavTasker_Handler,
		},
		{
			MethodName: "Normal",
			Handler:    _PushNotificationNewTaskTH_Normal_Handler,
		},
		{
			MethodName: "FavAndTaskerDistrict",
			Handler:    _PushNotificationNewTaskTH_FavAndTaskerDistrict_Handler,
		},
		{
			MethodName: "FavAndTopTasker",
			Handler:    _PushNotificationNewTaskTH_FavAndTopTasker_Handler,
		},
		{
			MethodName: "NewTaskToCity",
			Handler:    _PushNotificationNewTaskTH_NewTaskToCity_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "baseTH.proto",
}
