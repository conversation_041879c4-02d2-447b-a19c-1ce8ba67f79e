package autoFindTaskersHaveStarCanDoTestTraining

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

func createCourseStartDate(courseStartDate []interface{}) {
	arrayCourseStartDate := globalLib.SplitSliceByChunk(courseStartDate, 300)
	for _, elementCourseStartDate := range arrayCourseStartDate {
		err := handlers.CreateCoursesStartDate(elementCourseStartDate)
		if err != nil {
			text := fmt.Sprintf("AutoFindTaskersHaveStarCanDoTestTraining %s: Lỗi createCourseStartDate , err: %s ", local.ISO_CODE, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], text)
		}
	}
}
