/*
 * @File: handlers.go
 * @Description: Handle request and call push new task notification request
 * @CreatedAt: 02/03/2020
 * @Author: linhnh
 * @UpdatedAt: 16/12/2020
 * @UpdatedBy: ngoctb3
 */
package service

import (
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/handlers"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalRepo"
	modelPushNotificationNewTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask"
	modelResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"go.mongodb.org/mongo-driver/bson"
)

/*
 * @Description: Handle new task request and call push new task notification
 * @CreatedAt: 21/08/2020
 * @Author: linhnh
 * @UpdatedAt: 01/12/2020
 * @UpdatedBy: vinhnt
 */
func NewTask(reqBody *modelPushNotificationNewTask.NewTaskRequest) (*modelResponse.Response, error) {
	defer local.Logger.Sync()

	err := validateRequest(reqBody)
	if err != nil {
		return err, nil
	}
	//Get data Service
	service, err := getService(reqBody.Booking.IsoCode, reqBody.Service.XId)
	if service == nil {
		return err, nil
	}
	lib.SendNotificationOfNewTask(service, reqBody.Booking.XId, reqBody.FavouriteTasker, reqBody.Booking.IsoCode)
	return &modelResponse.Response{
		StatusCode: 200,
	}, nil
}

/*
 * @Description: Handle top tasker request and call push new task notification to top tasker
 * @CreatedAt: 11/09/2020
 * @Author: linhnh
 */
func TopTasker(reqBody *modelPushNotificationNewTask.NewTaskRequest) (*modelResponse.Response, error) {
	defer local.Logger.Sync()

	err := validateRequest(reqBody)
	if err != nil {
		return err, nil
	}
	//Get data Service
	service, err := getService(reqBody.Booking.IsoCode, reqBody.Service.XId)
	if service == nil {
		return err, nil
	}

	lib.SendNewTaskNotificationTopTasker(service, reqBody.Booking.XId, reqBody.Booking.IsoCode)
	return &modelResponse.Response{
		StatusCode: 200,
	}, nil
}

/*
 * @Description: Handle new task to district request and call push new task notification to tasker in district of task
 * @CreatedAt: 02/11/2020
 * @Author: linhnh
 * @UpdatedAt: 11/12/2020
 * @UpdatedBy: linhnh
 */
func NewTaskToDistrict(reqBody *modelPushNotificationNewTask.NewTaskRequest) (*modelResponse.Response, error) {
	defer local.Logger.Sync()

	err := validateRequest(reqBody)
	if err != nil {
		return err, nil
	}
	//Get data Service
	service, err := getService(reqBody.Booking.IsoCode, reqBody.Service.XId)
	if service == nil {
		return err, nil
	}
	lib.SendNewTaskNotificationToDistrict(service, reqBody.Booking.XId)
	return &modelResponse.Response{
		StatusCode: 200,
	}, nil
}

/*
 * @Description: Handle FavTasker request and call push new task notification to tasker in asker's favourite taskers
 * @CreatedAt: 02/11/2020
 * @Author: linhnh
 * @UpdatedAt: 11/12/2020
 * @UpdatedBy: linhnh
 */
func FavTasker(reqBody *modelPushNotificationNewTask.NewTaskRequest) (*modelResponse.Response, error) {
	defer local.Logger.Sync()

	err := validateRequest(reqBody)
	if err != nil {
		return err, nil
	}
	//Get data Service
	service, err := getService(reqBody.Booking.IsoCode, reqBody.Service.XId)
	if service == nil {
		return err, nil
	}
	lib.SendNotificationToFavTaskers(service, reqBody.Booking.XId, reqBody.Booking.IsoCode)
	return &modelResponse.Response{
		StatusCode: 200,
	}, nil
}

/*
 * @Description: Handle Normal request and Send notification to tasker when have new task
 * @CreatedAt: 13/11/2020
 * @Author: linhnh
 * @UpdatedAt: 11/12/2020
 * @UpdatedBy: linhnh
 */
func Normal(reqBody *modelPushNotificationNewTask.NewTaskRequest) (*modelResponse.Response, error) {
	defer local.Logger.Sync()

	err := validateRequest(reqBody)
	if err != nil {
		return err, nil
	}
	//Get data Service
	service, err := getService(reqBody.Booking.IsoCode, reqBody.Service.XId)
	if service == nil {
		return err, nil
	}
	lib.SendNotificationNormal(service, reqBody.Booking.XId)
	return &modelResponse.Response{
		StatusCode: 200,
	}, nil
}

/*
 * @Description: Handle FavTasker request and call push new task notification to tasker in asker's favourite taskers
 * @CreatedAt: 02/11/2020
 * @Author: linhnh
 * @UpdatedAt: 11/12/2020
 * @UpdatedBy: linhnh
 */
func FavAndTaskerDistrict(reqBody *modelPushNotificationNewTask.NewTaskRequest) (*modelResponse.Response, error) {
	err := validateRequest(reqBody)
	if err != nil {
		return err, nil
	}
	//Get data Service
	service, err := getService(reqBody.Booking.IsoCode, reqBody.Service.XId)
	if service == nil {
		return err, nil
	}
	handlers.SendNotificationToFavAndTaskersDistrict(service, reqBody.Booking.XId, reqBody.Booking.IsoCode)
	return &modelResponse.Response{
		StatusCode: 200,
	}, nil
}

/*
 * @Description: Handle FavTasker and Top Taskers request, call push new task notification to taskers
 * @CreatedAt: 24/06/2022
 * @Author: linhnh
 */
func FavAndTopTasker(reqBody *modelPushNotificationNewTask.NewTaskRequest) (*modelResponse.Response, error) {
	err := validateRequest(reqBody)
	if err != nil {
		return err, nil
	}
	//Get data Service
	service, err := getService(reqBody.Booking.IsoCode, reqBody.Service.XId)
	if service == nil {
		return err, nil
	}
	handlers.SendNotificationToFavAndTopTasker(service, reqBody.Booking.XId, reqBody.Booking.IsoCode)
	return &modelResponse.Response{
		StatusCode: 200,
	}, nil
}

/*
 * @Description: Handle new task to city request and call push new task notification to tasker in city of task
 * @CreatedAt: 20/07/2022
 * @Author: ngoctb
 * @UpdatedAt:
 * @UpdatedBy:
 */
func NewTaskToCity(reqBody *modelPushNotificationNewTask.NewTaskRequest) (*modelResponse.Response, error) {
	defer local.Logger.Sync()

	err := validateRequest(reqBody)
	if err != nil {
		return err, nil
	}
	//Get data Service
	service, err := getService(reqBody.Booking.IsoCode, reqBody.Service.XId)
	if service == nil {
		return err, nil
	}
	lib.SendNewTaskNotificationToCity(service, reqBody.Booking.XId)
	return &modelResponse.Response{
		StatusCode: 200,
	}, nil
}

/*
 * @Description: Validate push new task notification request params
 * @CreatedAt: 21/08/2020
 * @Author: linhnh
 * @UpdatedAt: 11/09/2020
 * @UpdatedBy: linhnh
 */
func validateRequest(reqBody *modelPushNotificationNewTask.NewTaskRequest) *modelResponse.Response {
	if reqBody == nil {
		return &modelResponse.Response{
			StatusCode: int32(lib.ERROR_CAN_NOT_PARSE_API_PARAMS.StatusCode),
			Error: &modelResponse.ResponseError{
				Code: lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			},
		}
	}
	if reqBody.Service == nil || reqBody.Service.XId == "" {
		return &modelResponse.Response{
			StatusCode: int32(lib.ERROR_SERVICE_ID_REQUIRED.StatusCode),
			Error: &modelResponse.ResponseError{
				Code: lib.ERROR_SERVICE_ID_REQUIRED.ErrorCode,
			},
		}
	}
	if reqBody.Booking == nil || reqBody.Booking.XId == "" {
		return &modelResponse.Response{
			StatusCode: int32(lib.ERROR_BOOKING_ID_REQUIRED.StatusCode),
			Error: &modelResponse.ResponseError{
				Code: lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode,
			},
		}
	}
	if reqBody.Booking == nil || reqBody.Booking.IsoCode == "" {
		return &modelResponse.Response{
			StatusCode: int32(lib.ERROR_BOOKING_ISOCODE_REQUIRED.StatusCode),
			Error: &modelResponse.ResponseError{
				Code: lib.ERROR_BOOKING_ISOCODE_REQUIRED.ErrorCode,
			},
		}
	}
	return nil
}

// Get data Service
func getService(isoCode, serviceID string) (*modelService.Service, *modelResponse.Response) {
	serviceFields := bson.M{"_id": 1, "isSubscription": 1, "name": 1, "text": 1}

	var service *modelService.Service
	globalRepo.GetOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], serviceID, serviceFields, &service)

	if service == nil {
		return nil, &modelResponse.Response{
			StatusCode: int32(lib.ERROR_SERVICE_NOT_FOUND.StatusCode),
			Error: &modelResponse.ResponseError{
				Code: lib.ERROR_SERVICE_NOT_FOUND.ErrorCode,
			},
		}
	}

	if service.IsSubscription {
		normalServiceName := globalConstant.NORMAL_SERVICE_NAME_FROM_SUBSCRIPTION_SERVICE_NAME[service.Name]

		// Get service again with normal service name
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": normalServiceName}, serviceFields, &service)
	}

	return service, nil
}
