package autoCalculateJourneyLeaderBoard

import (
	"fmt"

	"github.com/jinzhu/copier"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelJourneyLeaderBoard "gitlab.com/btaskee/go-services-model-v2/grpcmodel/journeyLeaderBoard"
	"go.mongodb.org/mongo-driver/bson"
)

func getTaskersInLeaderBoard(cityName string, taskerList []string) ([]*modelJourneyLeaderBoard.JourneyLeaderBoard, map[string]*modelJourneyLeaderBoard.JourneyLeaderBoard, []string, error) {
	taskersInLeaderBoard, err := getTaskersInLeaderBoardByChunk(cityName, taskerList)
	if err != nil {
		return nil, nil, nil, err
	}

	mapTaskersInLeaderBoard := make(map[string]*modelJourneyLeaderBoard.JourneyLeaderBoard)
	taskerIds := []string{}
	for _, v := range taskersInLeaderBoard {
		taskerLB := &modelJourneyLeaderBoard.JourneyLeaderBoard{}
		copier.CopyWithOption(taskerLB, v, copier.Option{DeepCopy: true})
		mapTaskersInLeaderBoard[v.XId] = taskerLB
		taskerIds = append(taskerIds, v.XId)
	}

	return taskersInLeaderBoard, mapTaskersInLeaderBoard, taskerIds, nil
}

func getTaskersInLeaderBoardByChunk(cityName string, taskerList []string) ([]*modelJourneyLeaderBoard.JourneyLeaderBoard, error) {
	var result []*modelJourneyLeaderBoard.JourneyLeaderBoard
	var page int64 = 1
	var limit int64 = CHUNK_SIZE_GET_LEADER_BOARD
	for {
		var chunk []*modelJourneyLeaderBoard.JourneyLeaderBoard
		err := globalDataAccess.GetAllByQueryPaging(globalCollection.COLLECTION_JOURNEY_LEADER_BOARD[local.ISO_CODE], bson.M{"cityName": cityName}, bson.M{}, page, limit, &chunk)

		if err != nil {
			err = fmt.Errorf("getTaskersInLeaderBoardByChunk: %v", err)
			return nil, err
		}
		if len(chunk) == 0 {
			break
		}
		for _, tasker := range chunk {
			if globalLib.FindStringInSlice(taskerList, tasker.XId) < 0 {
				continue
			}
			result = append(result, tasker)
		}
		page++
	}

	return result, nil
}
