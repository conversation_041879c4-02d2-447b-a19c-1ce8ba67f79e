package testing

import (
	"context"
	"log"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/service"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
)

func TestSendEmail(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Subject email nil")
		Convey("Given a request if subject email is nil", t, func() {
			req := &modelEmailSending.EmailSending{}
			Convey("When the request is handled", func() {
				s := service.Server{}
				_, err := s.SendEmail(context.Background(), req)
				Convey("Then check the response if subject nil", func() {
					So(err.Error(), ShouldEqual, lib.ERROR_SUBJECT_EMAIL_NIL)
				})
			})
		})

		<PERSON>vey("Given a request if from email is nil", t, func() {
			req := &modelEmailSending.EmailSending{
				Subject: "bTaskee email",
			}
			Convey("When the request is handled", func() {
				s := service.Server{}
				_, err := s.SendEmail(context.Background(), req)
				Convey("Then check the response if subject nil", func() {
					So(err.Error(), ShouldEqual, lib.ERROR_FROM_EMAIL_NIL)
				})
			})
		})

		Convey("Given a request if to email is nil", t, func() {
			req := &modelEmailSending.EmailSending{
				Subject: "bTaskee email",
				From:    "bTaskee email <<EMAIL>>",
			}
			Convey("When the request is handled", func() {
				s := service.Server{}
				_, err := s.SendEmail(context.Background(), req)
				Convey("Then check the response if subject nil", func() {
					So(err.Error(), ShouldEqual, lib.ERROR_TO_EMAIL_NIL)
				})
			})
		})

		Convey("Given a request if to email is invalid", t, func() {
			req := &modelEmailSending.EmailSending{
				Subject: "bTaskee email",
				From:    "bTaskee email <<EMAIL>>",
				To:      "bangocntt",
			}
			Convey("When the request is handled", func() {
				s := service.Server{}
				_, err := s.SendEmail(context.Background(), req)
				Convey("Then check the response if subject nil", func() {
					So(err.Error(), ShouldEqual, lib.ERROR_TO_EMAIL_INVALID)
				})
			})
		})

		Convey("Given a request if to content is nil", t, func() {
			req := &modelEmailSending.EmailSending{
				Subject: "bTaskee email",
				From:    "bTaskee email <<EMAIL>>",
				To:      "<EMAIL>",
			}
			Convey("When the request is handled", func() {
				s := service.Server{}
				_, err := s.SendEmail(context.Background(), req)
				Convey("Then check the response if subject nil", func() {
					So(err.Error(), ShouldEqual, lib.ERROR_CONTENT_NIL)
				})
			})
		})
	})
}
