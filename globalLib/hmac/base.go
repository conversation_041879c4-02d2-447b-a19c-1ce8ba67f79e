package hmac

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"errors"
)

func EncryptWithHMAC(plaintext, secretKey string) (string, error) {
	// Tạo khóa từ secretKey
	key := sha256.Sum256([]byte(secretKey))

	// Tạo IV ngẫu nhiên (AES block size = 16 byte)
	iv := make([]byte, aes.BlockSize)
	_, err := rand.Read(iv)
	if err != nil {
		return "", err
	}

	// Tạo block AES
	block, err := aes.NewCipher(key[:])
	if err != nil {
		return "", err
	}

	// Padding dữ liệu trước khi mã hóa
	paddedData := pkcs7Pad([]byte(plaintext), aes.BlockSize)
	ciphertext := make([]byte, len(paddedData))

	// Mã hóa AES-CBC
	mode := cipher.NewCBCEncrypter(block, iv)
	mode.CryptBlocks(ciphertext, paddedData)

	// Tạo HMAC-SHA256
	hmacHash := hmac.New(sha256.New, key[:])
	hmacHash.Write(ciphertext)
	hmacValue := hmacHash.Sum(nil)

	// Kết hợp IV + Ciphertext + HMAC
	finalData := append(iv, append(ciphertext, hmacValue...)...)

	// Chuyển sang Base64
	return base64.StdEncoding.EncodeToString(finalData), nil
}

func DecryptWithHMAC(encryptedBase64, secretKey string) (string, error) {
	// Decode Base64
	encryptedData, err := base64.StdEncoding.DecodeString(encryptedBase64)
	if err != nil {
		return "", err
	}

	// Tạo khóa từ secretKey
	key := sha256.Sum256([]byte(secretKey))

	// Tách IV, Ciphertext và HMAC
	iv := encryptedData[:aes.BlockSize]
	ciphertext := encryptedData[aes.BlockSize : len(encryptedData)-sha256.Size]
	hmacReceived := encryptedData[len(encryptedData)-sha256.Size:]

	// Xác thực HMAC
	hmacHash := hmac.New(sha256.New, key[:])
	hmacHash.Write(ciphertext)
	hmacComputed := hmacHash.Sum(nil)

	if !hmac.Equal(hmacComputed, hmacReceived) {
		return "", errors.New("⚠️ Dữ liệu không hợp lệ!")
	}

	// Giải mã AES-CBC
	block, err := aes.NewCipher(key[:])
	if err != nil {
		return "", err
	}

	mode := cipher.NewCBCDecrypter(block, iv)
	decrypted := make([]byte, len(ciphertext))
	mode.CryptBlocks(decrypted, ciphertext)

	// Bỏ padding PKCS7
	decrypted, err = pkcs7Unpad(decrypted, aes.BlockSize)
	if err != nil {
		return "", err
	}

	return string(decrypted), nil
}

func pkcs7Pad(data []byte, blockSize int) []byte {
	padding := blockSize - len(data)%blockSize
	padText := bytes.Repeat([]byte{byte(padding)}, padding)
	return append(data, padText...)
}

func pkcs7Unpad(data []byte, blockSize int) ([]byte, error) {
	padding := int(data[len(data)-1])
	if padding > blockSize || padding == 0 {
		return nil, errors.New("Invalid padding")
	}
	return data[:len(data)-padding], nil
}
