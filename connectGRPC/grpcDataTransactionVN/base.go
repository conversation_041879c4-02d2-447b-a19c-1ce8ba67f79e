package grpcDataTransactionVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcDataTransactionVN"
	"google.golang.org/grpc"
)

func ConnectGRPCDataTransaction(endpoint string) (grpcDataTransactionVN.DataTransactionClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcDataTransactionVN.NewDataTransactionClient(connect)
	return client, connect, err
}
