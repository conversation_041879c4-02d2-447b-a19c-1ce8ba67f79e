package bkafka

import (
	"fmt"
	"sync"
	"time"

	"github.com/IBM/sarama"
	"go.uber.org/zap"
)

type producerProvider struct {
	transactionIdGenerator int32

	producersLock sync.Mutex
	producers     []sarama.AsyncProducer

	producerProvider func() (sarama.AsyncProducer, error)
}

func newProducerProvider(brokers []string, producerConfigurationProvider func() *sarama.Config) *producerProvider {
	provider := &producerProvider{}
	provider.producerProvider = func() (sarama.AsyncProducer, error) {
		config := producerConfigurationProvider()
		suffix := provider.transactionIdGenerator
		if config.Producer.Transaction.ID != "" {
			provider.transactionIdGenerator++
			config.Producer.Transaction.ID = config.Producer.Transaction.ID + "-" + fmt.Sprint(suffix)
		}
		producer, err := sarama.NewAsyncProducer(brokers, config)
		if err != nil {
			return nil, err
		}
		return producer, nil
	}
	return provider
}

func (p *producerProvider) borrow() (producer sarama.AsyncProducer, err error) {
	p.producersLock.Lock()
	defer p.producersLock.Unlock()

	retryCount := 0

	if len(p.producers) == 0 {
		for {
			producer, err = p.producerProvider()
			if err != nil {
				logger.Debug("Failed to create producer", zap.Any("error", err))
				if retryCount >= 3 {
					logger.Debug("Failed to create producer max retry", zap.Any("error", err))
					return
				}
				retryCount++
				continue
			}
			if producer != nil {
				return
			}
			time.Sleep(2 * time.Second)
		}
	}

	index := len(p.producers) - 1
	producer = p.producers[index]
	p.producers = p.producers[:index]
	return
}

func (p *producerProvider) release(producer sarama.AsyncProducer) {
	p.producersLock.Lock()
	defer p.producersLock.Unlock()

	// If released producer is erroneous close it and don't return it to the producer pool.
	if producer.TxnStatus()&sarama.ProducerTxnFlagInError != 0 {
		// Try to close it
		_ = producer.Close()
		return
	}
	p.producers = append(p.producers, producer)
}

func (p *producerProvider) clear() {
	p.producersLock.Lock()
	defer p.producersLock.Unlock()

	for _, producer := range p.producers {
		producer.Close()
	}
	p.producers = p.producers[:0]
}
