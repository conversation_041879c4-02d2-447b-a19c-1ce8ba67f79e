package weeklyResetLeaderBoard

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelJourneyLeaderBoard "gitlab.com/btaskee/go-services-model-v2/grpcmodel/journeyLeaderBoard"
	"go.mongodb.org/mongo-driver/bson"
)

func getTaskersInLeaderBoard(taskerList []string) ([]*modelJourneyLeaderBoard.JourneyLeaderBoard, error) {
	taskersInLeaderBoard, err := getTaskersInLeaderBoardByChunk(taskerList)
	if err != nil {
		return nil, err
	}
	return taskersInLeaderBoard, nil
}

func getTaskersInLeaderBoardByChunk(taskerList []string) ([]*modelJourneyLeaderBoard.JourneyLeaderBoard, error) {
	var result []*modelJourneyLeaderBoard.JourneyLeaderBoard
	var page int64 = 1
	var limit int64 = CHUNK_SIZE_GET_LEADER_BOARD
	query := bson.M{}
	if len(taskerList) > 0 {
		query["_id"] = bson.M{"$in": taskerList}
	}
	for {
		var chunk []*modelJourneyLeaderBoard.JourneyLeaderBoard
		err := globalDataAccess.GetAllByQueryPaging(globalCollection.COLLECTION_JOURNEY_LEADER_BOARD[local.ISO_CODE], query, bson.M{}, page, limit, &chunk)
		if err != nil {
			err = fmt.Errorf("getTaskersInLeaderBoardByChunk: %v", err)
			return nil, err
		}
		if len(chunk) == 0 {
			break
		}
		result = append(result, chunk...)
		page++
	}
	return result, nil
}
