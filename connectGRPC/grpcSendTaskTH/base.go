package grpcSendTaskTH

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcSendTaskTH"
	"google.golang.org/grpc"
)

func ConnectGRPCSendTaskTH(endpoint string) (grpcSendTaskTH.PushNotificationNewTaskTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcSendTaskTH.NewPushNotificationNewTaskTHClient(connect)
	return client, connect, err
}
