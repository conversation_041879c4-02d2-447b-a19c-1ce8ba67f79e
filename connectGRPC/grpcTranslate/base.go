package grpcTranslate

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcTranslate"
	"google.golang.org/grpc"
)

func ConnectGRPCTranslate(endpoint string) (grpcTranslate.TranslateClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcTranslate.NewTranslateClient(connect)
	return client, connect, err
}
