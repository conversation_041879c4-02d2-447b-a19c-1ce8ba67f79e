syntax = "proto3";
package grpcDataTransactionMY;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcDataTransactionMY";

import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/dataTransactionMessage/dataTransactionMessage.proto";

service DataTransaction {
  rpc DoneTask (dataTransactionMessage.DoneTaskRequest) returns (dataTransactionMessage.DoneTaskResponse) {}
  rpc UpdateFinancial (dataTransactionMessage.UpdateFinancialRequest) returns (dataTransactionMessage.UpdateFinancialResponse) {}
}