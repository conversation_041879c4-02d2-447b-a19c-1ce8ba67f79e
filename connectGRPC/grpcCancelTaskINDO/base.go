package grpcCancelTaskINDO

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcCancelTaskINDO"
	"google.golang.org/grpc"
)

func ConnectGRPCCancelTaskINDO(endpoint string) (grpcCancelTaskINDO.CancelTaskINDOClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcCancelTaskINDO.NewCancelTaskINDOClient(connect)
	return client, connect, err
}
