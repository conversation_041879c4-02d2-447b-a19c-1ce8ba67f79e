package giveRewardTaskerDoneSpecialCampaign

import (
	"time"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func processCountReferralCodeUsedInRangeDate(referralCode string, startDate, endDate time.Time) int32 {
	count, err := modelUser.CountByQuery(local.ISO_CODE, bson.M{
		"isoCode":     local.ISO_CODE,
		"countryCode": globalConstant.COUNTRY_CODE_VN,
		"status":      globalConstant.USER_STATUS_ACTIVE,
		"friendCode":  referralCode,
		"createdAt": bson.M{
			"$gte": startDate,
			"$lte": endDate,
		},
	})
	if err != nil {
		local.Logger.Info("[SYN CRON VN V3 - giveRewardTaskerDoneSpecialCampaign] error count user referral", zap.Error(err))
		return 0
	}

	return cast.ToInt32(count)
}
