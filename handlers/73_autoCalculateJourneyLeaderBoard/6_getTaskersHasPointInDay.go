package autoCalculateJourneyLeaderBoard

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getTaskersHasPointInDay(cityName string, taskerHasPointInDayIds []string) (map[string]*modelUser.Users, error) {
	chunkTaskerIds := globalLib.SplitArrayToChunk(taskerHasPointInDayIds, CHUNK_SIZE_GET_TASKERS)
	var taskers []*modelUser.Users
	for _, ids := range chunkTaskerIds {
		query := bson.M{
			"_id":                  bson.M{"$in": ids},
			"workingPlaces.0.city": cityName,
			// "isoCode":              local.ISO_CODE, // Không cần field này nữa
		}
		chunk, err := modelUser.GetAll(local.ISO_CODE,
			query,
			bson.M{"_id": 1, "name": 1, "avatar": 1},
		)
		if err != nil {
			err = fmt.Errorf("getTaskersHasPointInDay: %v", err)
			return nil, err
		}
		taskers = append(taskers, chunk...)
	}

	// Convert to map
	result := make(map[string]*modelUser.Users)
	for _, tasker := range taskers {
		result[tasker.XId] = tasker
	}
	return result, nil
}
