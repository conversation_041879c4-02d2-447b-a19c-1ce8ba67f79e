package businessAllocation

import (
	"context"
	"fmt"
	"strings"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPaymentVN"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	"go.mongodb.org/mongo-driver/bson"
)

func revokeMembers(business *modelBusiness.Business) {
	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	period := int(business.GetTopUpSetting().GetPeriod())
	_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], business.XId, bson.M{"$set": bson.M{"revokeSetting.nextTime": getNextTimeForUpdate(currentTime, business.GetRevokeSetting().GetDayInMonth(), period)}})
	if err != nil {
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, "Update revoke setting error: "+err.Error())
		return
	}

	members := getMembersByBusinessId(business.XId)
	if len(members) == 0 {
		return
	}
	processRevokeMembers(business.XId, members)
}

func processRevokeMembers(businessId string, members []*modelBusinessMember.BusinessMember) {
	listMemberFail := []string{}
	totalbPay := 0.0
	for _, v := range members {
		_, err := revokeMembersGRPC(&modelBusinessMember.MemberRequestPayment{
			BusinessId: businessId,
			MemberId:   v.XId,
			Amount:     v.BPay,
		})
		if err != nil {
			listMemberFail = append(listMemberFail, fmt.Sprintf("memberId: %s - error: %v", v.XId, err))
		} else {
			totalbPay += v.BPay
		}
	}
	if len(listMemberFail) > 0 {
		// Post slack
		msg := fmt.Sprintf("Lỗi thu hồi tiền bPay members business: id: %s - listMemberFail:\n%v", businessId, strings.Join(listMemberFail, "\n"))
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.BUSINESS_PARTNERSHIP_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
	} else {
		sendNotificationSuccess(businessId, "BUSINESS_REVOKE_SCHEDULE_SUCCESS_TITLE", "BUSINESS_REVOKE_SCHEDULE_SUCCESS_BODY", globalConstant.PAYLOAD_NAVIGATE_TO_TRANSACTION_BUSINESS_ACCOUNT, globalLib.FormatMoney(totalbPay), globalLib.GetOriginCurrencyByIsoCode(local.ISO_CODE).Sign)
	}
}

func revokeMembersGRPC(requestData *modelBusinessMember.MemberRequestPayment) (result *response.Response, err error) {
	client, connect, err := grpcPaymentVN.ConnectGRPCPaymentVN(cfg.GrpcPaymentPort)
	if err != nil {
		return nil, err
	}
	defer connect.Close()
	response, err := client.BusinessRevokeMember(context.Background(), requestData)
	if err != nil {
		return nil, err
	}
	return response, nil
}
