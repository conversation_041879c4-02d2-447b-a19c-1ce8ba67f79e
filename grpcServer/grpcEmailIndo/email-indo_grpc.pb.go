// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcEmailIndo

import (
	context "context"
	emailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	emailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	task "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	users "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// EmailIndoClient is the client API for EmailIndo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EmailIndoClient interface {
	EmailTest(ctx context.Context, in *emailSending.EmailSending, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendEmail(ctx context.Context, in *emailSending.EmailSending, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendReceiptEmail(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendCancelFeeEmail(ctx context.Context, in *emailSending.EmailCancelFeeRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendRenewSubscriptionEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendVerifyEmail(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
}

type emailIndoClient struct {
	cc grpc.ClientConnInterface
}

func NewEmailIndoClient(cc grpc.ClientConnInterface) EmailIndoClient {
	return &emailIndoClient{cc}
}

func (c *emailIndoClient) EmailTest(ctx context.Context, in *emailSending.EmailSending, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailIndo.EmailIndo/EmailTest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailIndoClient) SendEmail(ctx context.Context, in *emailSending.EmailSending, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailIndo.EmailIndo/SendEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailIndoClient) SendReceiptEmail(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailIndo.EmailIndo/SendReceiptEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailIndoClient) SendCancelFeeEmail(ctx context.Context, in *emailSending.EmailCancelFeeRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailIndo.EmailIndo/SendCancelFeeEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailIndoClient) SendRenewSubscriptionEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailIndo.EmailIndo/SendRenewSubscriptionEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailIndoClient) SendVerifyEmail(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailIndo.EmailIndo/SendVerifyEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EmailIndoServer is the server API for EmailIndo service.
// All implementations must embed UnimplementedEmailIndoServer
// for forward compatibility
type EmailIndoServer interface {
	EmailTest(context.Context, *emailSending.EmailSending) (*emailResponse.EmailResponse, error)
	SendEmail(context.Context, *emailSending.EmailSending) (*emailResponse.EmailResponse, error)
	SendReceiptEmail(context.Context, *task.Task) (*emailResponse.EmailResponse, error)
	SendCancelFeeEmail(context.Context, *emailSending.EmailCancelFeeRequest) (*emailResponse.EmailResponse, error)
	SendRenewSubscriptionEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error)
	SendVerifyEmail(context.Context, *users.Users) (*emailResponse.EmailResponse, error)
	mustEmbedUnimplementedEmailIndoServer()
}

// UnimplementedEmailIndoServer must be embedded to have forward compatible implementations.
type UnimplementedEmailIndoServer struct {
}

func (UnimplementedEmailIndoServer) EmailTest(context.Context, *emailSending.EmailSending) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method EmailTest not implemented")
}
func (UnimplementedEmailIndoServer) SendEmail(context.Context, *emailSending.EmailSending) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendEmail not implemented")
}
func (UnimplementedEmailIndoServer) SendReceiptEmail(context.Context, *task.Task) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendReceiptEmail not implemented")
}
func (UnimplementedEmailIndoServer) SendCancelFeeEmail(context.Context, *emailSending.EmailCancelFeeRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendCancelFeeEmail not implemented")
}
func (UnimplementedEmailIndoServer) SendRenewSubscriptionEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendRenewSubscriptionEmail not implemented")
}
func (UnimplementedEmailIndoServer) SendVerifyEmail(context.Context, *users.Users) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendVerifyEmail not implemented")
}
func (UnimplementedEmailIndoServer) mustEmbedUnimplementedEmailIndoServer() {}

// UnsafeEmailIndoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EmailIndoServer will
// result in compilation errors.
type UnsafeEmailIndoServer interface {
	mustEmbedUnimplementedEmailIndoServer()
}

func RegisterEmailIndoServer(s *grpc.Server, srv EmailIndoServer) {
	s.RegisterService(&_EmailIndo_serviceDesc, srv)
}

func _EmailIndo_EmailTest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailSending)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailIndoServer).EmailTest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailIndo.EmailIndo/EmailTest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailIndoServer).EmailTest(ctx, req.(*emailSending.EmailSending))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailIndo_SendEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailSending)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailIndoServer).SendEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailIndo.EmailIndo/SendEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailIndoServer).SendEmail(ctx, req.(*emailSending.EmailSending))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailIndo_SendReceiptEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(task.Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailIndoServer).SendReceiptEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailIndo.EmailIndo/SendReceiptEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailIndoServer).SendReceiptEmail(ctx, req.(*task.Task))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailIndo_SendCancelFeeEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailCancelFeeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailIndoServer).SendCancelFeeEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailIndo.EmailIndo/SendCancelFeeEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailIndoServer).SendCancelFeeEmail(ctx, req.(*emailSending.EmailCancelFeeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailIndo_SendRenewSubscriptionEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailIndoServer).SendRenewSubscriptionEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailIndo.EmailIndo/SendRenewSubscriptionEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailIndoServer).SendRenewSubscriptionEmail(ctx, req.(*emailSending.EmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailIndo_SendVerifyEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(users.Users)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailIndoServer).SendVerifyEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailIndo.EmailIndo/SendVerifyEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailIndoServer).SendVerifyEmail(ctx, req.(*users.Users))
	}
	return interceptor(ctx, in, info, handler)
}

var _EmailIndo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcEmailIndo.EmailIndo",
	HandlerType: (*EmailIndoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "EmailTest",
			Handler:    _EmailIndo_EmailTest_Handler,
		},
		{
			MethodName: "SendEmail",
			Handler:    _EmailIndo_SendEmail_Handler,
		},
		{
			MethodName: "SendReceiptEmail",
			Handler:    _EmailIndo_SendReceiptEmail_Handler,
		},
		{
			MethodName: "SendCancelFeeEmail",
			Handler:    _EmailIndo_SendCancelFeeEmail_Handler,
		},
		{
			MethodName: "SendRenewSubscriptionEmail",
			Handler:    _EmailIndo_SendRenewSubscriptionEmail_Handler,
		},
		{
			MethodName: "SendVerifyEmail",
			Handler:    _EmailIndo_SendVerifyEmail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "email-indo.proto",
}
