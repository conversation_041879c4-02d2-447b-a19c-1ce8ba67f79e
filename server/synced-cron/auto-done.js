import { Meteor } from 'meteor/meteor';
import { HTTP } from 'meteor/http';
import _ from 'lodash';
import {
  trackingScheduleStatus,
  postToSlack,
} from '/server/helper-function.js';

const hourByMilSec = 60 * 60 * 1000;

const doneDeepCleaning = (task) => {
  const leader = task.acceptedTasker.find(tasker => tasker.isLeader === true);
  if ( leader ) {
    var url = Meteor.settings.GO_SERVICE.API_URL + '/v2/done-booking/partner-done';
    const headers = {
      'content-type': 'application/json',
      'accessKey': Meteor.settings.GO_SERVICE.API_KEY,
    };
    const option = {taskId: task._id, userId: leader.taskerId};
    
    HTTP.post(url, {headers: headers, data: option});
  }
};

const doneOthers = (task) => {
  if ( _.get(task, 'serviceText.en', '') === 'Laundry' && !_.get(task, 'detailLaundry.isReceived', false) ) {
    return;
  }
  const taskerId = task.acceptedTasker[0].taskerId;
  var url = Meteor.settings.GO_SERVICE.API_URL + '/v2/done-booking/partner-done';
  const headers = {
    'content-type': 'application/json',
    'accessKey': Meteor.settings.GO_SERVICE.API_KEY,
  };
  const option = {taskId: task._id, userId: taskerId};
  
  HTTP.post(url, {headers: headers, data: option});
};

const runDoneTask = () => {
  return;
	console.log("Start autodone cron ...");
  var message = null;
  Meteor.appCollection.Task.find({
    status: 'CONFIRMED',
    // 'serviceText.en': {$ne: 'Deep Cleaning'},
    date: {
      $lt: new Date(),
    },
    isoCode: 'VN'
  }, {fields: {
    date: 1,
    duration: 1,
    'serviceText.en': 1,
    acceptedTasker: 1,
    'detailLaundry.isReceived': 1
  }, sort: {date: 1}}).forEach((task) => {
    try {
      if (new Date(task.date.getTime() + ((task.duration + 1) * hourByMilSec)) < new Date()) {
        if ( task.serviceText && task.serviceText.en === 'Deep Cleaning' ) {
          // Specially for run done Deep Cleaning task
          // Via Go service
          doneDeepCleaning(task);
        } else {
          // Done others
          // Via Go service
          doneOthers(task);
        }
      }
    } catch (e) {
      message = e;
      postToSlack({
        channel: 'cron-jobs-alert',
        text: `Workflow: There was an error on runDoneTask: taskId: ${task._id} Message: ${message}`,
      });
    }
  });
	console.log("End autodone cron.");
  // Tracking
  trackingScheduleStatus({name: 'autoDone', nextRunAt: SyncedCron.nextScheduledAtDate('autoDone'), message});
};

  Meteor.methods({
    // Auto Done Task
    autoDone() {
      SyncedCron.add({
        name: 'autoDone',
        schedule(parser) {
          // Run every 10 minutes from 7AM to 22PM
          return parser.cron('0 0/10 * 1/1 * ? *', true);
        },
        job() {
          runDoneTask();
        },
      });

	console.log("Added autodone cron job");
    },
    'runNow.autoDone'() {
	console.log("Run autodone now");
      runDoneTask();
    },
  });
