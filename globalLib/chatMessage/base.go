package chatMessage

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"github.com/jinzhu/copier"
	"github.com/spf13/cast"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccess"
	globalDataAccessAll "gitlab.com/btaskee/go-services-model-v2/globalDataAccessV2"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelKeyword "gitlab.com/btaskee/go-services-model-v2/grpcmodel/keyword"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse"
	modelSettingCountry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func MapChatMessageMessageForTasker(message *modelChatMessage.ChatMessageMessages, task *modelTask.Task) map[string]interface{} {
	// Default old version of this function not use language. If you want to use language, you can use the new version of this function "MapChatMessageMessageForTaskerByLanguage"
	return MapChatMessageMessageForTaskerByLanguage("", message, task)
}

func MapChatMessageMessageForTaskerByLanguage(language string, message *modelChatMessage.ChatMessageMessages, task *modelTask.Task) map[string]interface{} {
	// If request is template type
	mapMessageRequestDataByTemplate(language, message.GetRequestData(), globalConstant.CHAT_MESSAGE_FROM_TASKER)

	// Map message with task cost
	messageMap := map[string]interface{}{}
	messageData, _ := json.Marshal(message)
	var currency *modelSettingCountry.SettingCountryCurrency
	var isTaskSubscription bool
	json.Unmarshal(messageData, &messageMap)
	var taskCost float64 = 0
	if task != nil {
		taskCost = task.Cost
		currency = task.OriginCurrency
		isTaskSubscription = task.SubscriptionId != ""
	}
	if message.GetRequestData().GetCostDetail().GetOld() != nil && message.GetRequestData().GetCostDetail().GetNew() != nil {
		requestDataMap := map[string]interface{}{}
		requestDataMapData, _ := json.Marshal(message.GetRequestData())
		json.Unmarshal(requestDataMapData, &requestDataMap)
		requestDataMap["taskCost"] = getCost(message.GetRequestData().GetCostDetail().GetOld(), taskCost, isTaskSubscription)
		requestDataMap["newTaskCost"] = getCost(message.GetRequestData().GetCostDetail().GetNew(), taskCost, isTaskSubscription)
		if currency == nil {
			currency = message.GetRequestData().GetCostDetail().GetOld().GetCurrency()
		}
		requestDataMap["currency"] = currency
		messageMap["requestData"] = requestDataMap
	}
	return messageMap
}

func getCost(costDetail *pricingresponse.CostResult, taskCost float64, isTaskSubscription bool) float64 {
	if costDetail == nil {
		return taskCost
	}
	taskCost = costDetail.Cost
	// Case promotionBy:TASKER
	if costDetail.PromotionBy == globalConstant.PROMOTION_BY_TASKER {
		taskCost = costDetail.FinalCost
	}
	if isTaskSubscription && len(costDetail.DecreasedReasons) > 0 {
		for _, v := range costDetail.DecreasedReasons {
			if v.Key == "SESSION_DISCOUNT" {
				if v.PromotionBy == globalConstant.PROMOTION_BY_BTASKEE {
					taskCost = taskCost + v.Value
					break
				}
			}
		}
	}
	return taskCost
}

func mapMessageRequestDataByTemplate(language string, requestData *modelChatMessage.ChatMessageMessagesRequestData, chatFrom string) {
	if requestData.GetUpdateDetailTemplateName() == "" || requestData.GetUpdateDetailTemplateData() == nil {
		return
	}
	if requestData.GetUpdateDetailTemplateName() == globalConstant.CHAT_MESSAGE_REQUEST_DATA_TEMPLATE_NAME_UPDATE_TASK_DETAIL {
		requestData.UpdateDetailHTML = fmt.Sprintf("<div>%s</div>", getTextRecursive_UpdateDetail(language, requestData.GetUpdateDetailTemplateData(), chatFrom))
		requestData.UpdateDetailTemplateData = nil
		requestData.UpdateDetailTemplateName = ""
		return
	}
}

func mapMessageSuggestionByHour(chatMessage *modelChatMessage.ChatMessageMessages) {
	messageText := chatMessage.Message
	// The regex is designed to capture various ways of expressing hours, such as "1h", "12h", "1 giờ", "12gio", or "12h00".
	pattern := `\d{1,2}\s*giờ|\d{1,2}h\d{2}|\d{1,2}h|\d{1,2}gio`
	// Compile regex
	re := regexp.MustCompile(pattern)
	// Tìm kết quả đầu tiên
	match := re.FindString(messageText)
	if match != "" {
		messageFormatted := strings.Replace(messageText, match, fmt.Sprintf("[**%s**]()", match), 1)
		chatMessage.Message = ""
		chatMessage.Type = globalConstant.TYPE_TEXT_ACTION
		chatMessage.TextActionData = &modelChatMessage.TextActionData{
			Text: messageFormatted,
			Actions: []*modelChatMessage.TextActionDataAction{
				{
					Key:   globalConstant.KEY_BOOK_WITH_FAV,
					Type:  globalConstant.CHAT_MESSAGE_BY_SYSTEM_ACTION_KEY_PRIMARY,
					Title: localization.GetLocalizeObject("BOOK_TASK_NOW"),
				},
			},
		}
	}
	return
}

func mapMessageSuggestionByKeyword(chatMessage *modelChatMessage.ChatMessageMessages, keywordsChat *modelKeyword.Keyword) {
	defer func() {
		if r := recover(); r != nil {
			globalLib.Logger.Error("ERROR_MAP_MESSAGE_SUGGESTION_BY_KEYWORD",
				zap.Any("functionName", "mapMessageSuggestionByKeyword"),
				zap.Any("chatMessage", chatMessage),
				zap.Any("keywordsChat", keywordsChat),
				zap.Any("recover", r),
			)
		}
	}()

	messageText := strings.ToLower(globalLib.ConvertVietnameseToLatin(chatMessage.Message))

	// Hiển thị đặt lịch theo keyword
	if keywordsChat.GetAction() == nil {
		return
	}
	for _, keyword := range keywordsChat.GetKeywords() {
		keywordText := strings.ToLower(globalLib.ConvertVietnameseToLatin(keyword))
		if strings.Contains(messageText, keywordText) {
			startIndex := strings.Index(messageText, keywordText)
			runes := []rune(chatMessage.Message)
			originalText := string(runes[startIndex : startIndex+len(keywordText)])
			highlighted := fmt.Sprintf("[**%s**]()", originalText)
			message := strings.Replace(chatMessage.Message, originalText, highlighted, -1)
			chatMessage.Message = ""
			chatMessage.Type = globalConstant.TYPE_TEXT_ACTION
			chatMessage.TextActionData = &modelChatMessage.TextActionData{
				Text: message,
				Actions: []*modelChatMessage.TextActionDataAction{
					{
						Key:   keywordsChat.GetAction().GetKey(),
						Type:  keywordsChat.GetAction().GetType(),
						Title: keywordsChat.GetAction().GetTitle(),
					},
				},
			}
			return
		}
	}
}

func getTextRecursive_UpdateDetail(language string, data []*modelChatMessage.ChatMessageMessagesRequestDataUpdateDetailTemplateData, chatFrom string) string {
	result := ""
	for _, v := range data {
		if len(v.Data) == 0 {
			key := ""
			value := ""
			if v.OldValue != nil {
				result += genHtmlUpdateDetailHaveOldValue(language, v, chatFrom)
				continue
			}
			if v.Key != nil {
				key = fmt.Sprintf("<p style=\"margin: 0; color: #878787; width: 40%%; font-size: 12px; font-family: 'Montserrat-Medium';\">%s</p>", globalLib.LocalizeServiceName(language, v.GetKey()))
			}
			if v.Value != nil {
				value = fmt.Sprintf("<p style=\"margin: 0; text-align: right; width: 60%%; font-family: 'Montserrat-Medium'\">%s</p>", globalLib.LocalizeServiceName(language, v.Value))
			}
			if key == "" && value == "" {
				continue
			}

			elem := fmt.Sprintf("<div style=\"display: flex; flex-direction: row; margin-bottom: 4px;\">%s%s</div>", key, value)
			result += elem
			continue
		}

		key := ""
		if v.OldValue != nil {
			result += genHtmlUpdateDetailHaveOldValue(language, v, chatFrom)
			continue
		}
		if v.Key != nil {
			key = fmt.Sprintf("<div style=\"margin-top: 8px; margin-bottom: 8px\"><b style=\"font-family: 'Montserrat-Bold'\">%s</b></div>", globalLib.LocalizeServiceName(language, v.Key))
		}
		value := ""
		if v.Value != nil {
			value = fmt.Sprintf("<p style=\"margin: 0; text-align: right; width: 60%%; font-family: 'Montserrat-Medium'\">%s</p>", globalLib.LocalizeServiceName(language, v.Value))
		}
		if value != "" {
			elem := fmt.Sprintf("<div style=\"display: flex; flex-direction: row; margin-bottom: 4px;\">%s%s</div>", key, value)
			result += elem
		} else {
			result += key
		}

		result += getTextRecursive_UpdateDetail(language, v.Data, chatFrom)
	}
	return result
}

func genHtmlUpdateDetailHaveOldValue(language string, data *modelChatMessage.ChatMessageMessagesRequestDataUpdateDetailTemplateData, chatFrom string) string {
	var result string
	if data.Key != nil {
		result = fmt.Sprintf(`<div style="display: flex; flex-direction: row">
 <p style="margin: 0; font-size: 14px; font-family: 'Montserrat-Medium'; margin-right: 4px">%s: <p style="margin: 0; color: #878787; font-size: 14px; font-family: 'Montserrat-Medium'; padding-right: 10px; max-width: 45%%;">%s</p></p>
 <img src="https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/services/industrial_cleaning/arrow_fat_right_pink.png" alt="arrow" style="width: 16px;">
 <p style="margin: 0; color: %s; margin-left: 10px; font-size: 14px; font-family: 'Montserrat-Medium'; max-width: 45%%">%s</p>
 </div>`, globalLib.LocalizeServiceName(language, data.Key), globalLib.LocalizeServiceName(language, data.OldValue), getColorByApp(chatFrom), globalLib.LocalizeServiceName(language, data.Value))
	} else {
		result = fmt.Sprintf(`<div style="display: flex; flex-direction: row; margin-top: 4px">
<p style="margin: 0; color: #878787; font-size: 14px; font-family: 'Montserrat-Medium'; padding-right: 10px; max-width: 45%%">%s</p>
<img src="https://btaskee-stag.s3.ap-southeast-1.amazonaws.com/app/tasker/services/industrial_cleaning/arrow_fat_right_pink.png" alt="arrow" style="width: 16px;">
<p style="margin: 0; color: %s; margin-left: 10px; font-size: 14px; font-family: 'Montserrat-Medium'; max-width: 45%%">%s</p>
</div>`, globalLib.LocalizeServiceName(language, data.OldValue), getColorByApp(chatFrom), globalLib.LocalizeServiceName(language, data.Value))
	}
	return result
}

func getColorByApp(chatFrom string) string {
	// color default for app asker
	color := "#FF8228"
	if chatFrom == globalConstant.CHAT_MESSAGE_FROM_TASKER {
		color = "#FF7070"
	}
	return color
}

// Function này có thể sử dụng sau này để map thêm những phần khác cho asker
func MapListMessageForAsker(isoCode string, language string, messages []*modelChatMessage.ChatMessageMessages) {
	var keywordsChat *modelKeyword.Keyword
	query := bson.M{
		"name":   globalConstant.KEYWORDS_CHAT,
		"status": globalConstant.KEYWORD_STATUS_ACTIVE,
	}

	// keyword VN
	_ = globalDataAccessAll.New(isoCode).GetOneByQuery(globalCollection.COLLECTION_KEYWORDS, query, bson.M{"keywords": 1, "action": 1}, &keywordsChat)
	for _, message := range messages {
		mapMessageRequestDataByTemplate(language, message.RequestData, globalConstant.CHAT_MESSAGE_FROM_TASKER)
		if message.Message != "" && message.From == globalConstant.CHAT_MESSAGE_FROM_ASKER {
			mapMessageSuggestionByHour(message)
			if keywordsChat != nil {
				mapMessageSuggestionByKeyword(message, keywordsChat)
			}
		}
	}
}

// Function này có thể sử dụng sau này để map thêm những phần khác cho tasker
func MapListMessageForTasker(language string, messages []*modelChatMessage.ChatMessageMessages, task *modelTask.Task) []map[string]interface{} {
	result := []map[string]interface{}{}
	for _, message := range messages {
		item := MapChatMessageMessageForTaskerByLanguage(language, message, task)
		result = append(result, item)
	}
	return result
}

func ConvertToMessage(messageMapInput map[string]interface{}) *modelChatMessage.ChatMessageMessages {
	var messageMap map[string]interface{}
	copier.CopyWithOption(&messageMap, messageMapInput, copier.Option{DeepCopy: true})
	if messageMap == nil {
		return nil
	}
	requestData := cast.ToStringMap(messageMap["requestData"])
	if requestData != nil && requestData["taskDetail"] != nil {
		delete(requestData, "taskDetail")
	}
	// Convert map to struct
	messageData, _ := json.Marshal(messageMap)
	message := &modelChatMessage.ChatMessageMessages{}
	err := bson.UnmarshalExtJSONWithRegistry(globalDataAccess.REGISTRY, messageData, true, message)
	if err != nil {
		b, _ := json.Marshal(messageMap)
		globalLib.Logger.Error("ERROR_CONVERT_MAP_TO_MESSAGE",
			zap.Error(err),
			zap.String("messageMap", string(b)),
		)
		return nil
	}
	return message
}

func convertToMessages(messagesMap []map[string]interface{}) []*modelChatMessage.ChatMessageMessages {
	messages := []*modelChatMessage.ChatMessageMessages{}
	for _, v := range messagesMap {
		m := ConvertToMessage(v)
		if m != nil {
			messages = append(messages, m)
		}
	}
	return messages
}
