// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcSendTaskMY

import (
	context "context"
	pushNotificationNewTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// SendTaskClient is the client API for SendTask service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SendTaskClient interface {
	NewTask(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	TopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	NewTaskToDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	FavTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	Normal(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	FavAndTaskerDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	FavAndTopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	NewTaskToCity(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	ForceTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
}

type sendTaskClient struct {
	cc grpc.ClientConnInterface
}

func NewSendTaskClient(cc grpc.ClientConnInterface) SendTaskClient {
	return &sendTaskClient{cc}
}

func (c *sendTaskClient) NewTask(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskMY.SendTask/NewTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendTaskClient) TopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskMY.SendTask/TopTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendTaskClient) NewTaskToDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskMY.SendTask/NewTaskToDistrict", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendTaskClient) FavTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskMY.SendTask/FavTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendTaskClient) Normal(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskMY.SendTask/Normal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendTaskClient) FavAndTaskerDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskMY.SendTask/FavAndTaskerDistrict", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendTaskClient) FavAndTopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskMY.SendTask/FavAndTopTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendTaskClient) NewTaskToCity(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskMY.SendTask/NewTaskToCity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sendTaskClient) ForceTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskMY.SendTask/ForceTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SendTaskServer is the server API for SendTask service.
// All implementations must embed UnimplementedSendTaskServer
// for forward compatibility
type SendTaskServer interface {
	NewTask(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	TopTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	NewTaskToDistrict(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	FavTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	Normal(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	FavAndTaskerDistrict(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	FavAndTopTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	NewTaskToCity(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	ForceTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	mustEmbedUnimplementedSendTaskServer()
}

// UnimplementedSendTaskServer must be embedded to have forward compatible implementations.
type UnimplementedSendTaskServer struct {
}

func (UnimplementedSendTaskServer) NewTask(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewTask not implemented")
}
func (UnimplementedSendTaskServer) TopTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TopTasker not implemented")
}
func (UnimplementedSendTaskServer) NewTaskToDistrict(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewTaskToDistrict not implemented")
}
func (UnimplementedSendTaskServer) FavTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FavTasker not implemented")
}
func (UnimplementedSendTaskServer) Normal(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Normal not implemented")
}
func (UnimplementedSendTaskServer) FavAndTaskerDistrict(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FavAndTaskerDistrict not implemented")
}
func (UnimplementedSendTaskServer) FavAndTopTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FavAndTopTasker not implemented")
}
func (UnimplementedSendTaskServer) NewTaskToCity(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewTaskToCity not implemented")
}
func (UnimplementedSendTaskServer) ForceTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ForceTasker not implemented")
}
func (UnimplementedSendTaskServer) mustEmbedUnimplementedSendTaskServer() {}

// UnsafeSendTaskServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SendTaskServer will
// result in compilation errors.
type UnsafeSendTaskServer interface {
	mustEmbedUnimplementedSendTaskServer()
}

func RegisterSendTaskServer(s *grpc.Server, srv SendTaskServer) {
	s.RegisterService(&_SendTask_serviceDesc, srv)
}

func _SendTask_NewTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskServer).NewTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskMY.SendTask/NewTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskServer).NewTask(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendTask_TopTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskServer).TopTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskMY.SendTask/TopTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskServer).TopTasker(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendTask_NewTaskToDistrict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskServer).NewTaskToDistrict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskMY.SendTask/NewTaskToDistrict",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskServer).NewTaskToDistrict(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendTask_FavTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskServer).FavTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskMY.SendTask/FavTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskServer).FavTasker(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendTask_Normal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskServer).Normal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskMY.SendTask/Normal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskServer).Normal(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendTask_FavAndTaskerDistrict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskServer).FavAndTaskerDistrict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskMY.SendTask/FavAndTaskerDistrict",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskServer).FavAndTaskerDistrict(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendTask_FavAndTopTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskServer).FavAndTopTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskMY.SendTask/FavAndTopTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskServer).FavAndTopTasker(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendTask_NewTaskToCity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskServer).NewTaskToCity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskMY.SendTask/NewTaskToCity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskServer).NewTaskToCity(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SendTask_ForceTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SendTaskServer).ForceTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskMY.SendTask/ForceTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SendTaskServer).ForceTasker(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _SendTask_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcSendTaskMY.SendTask",
	HandlerType: (*SendTaskServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "NewTask",
			Handler:    _SendTask_NewTask_Handler,
		},
		{
			MethodName: "TopTasker",
			Handler:    _SendTask_TopTasker_Handler,
		},
		{
			MethodName: "NewTaskToDistrict",
			Handler:    _SendTask_NewTaskToDistrict_Handler,
		},
		{
			MethodName: "FavTasker",
			Handler:    _SendTask_FavTasker_Handler,
		},
		{
			MethodName: "Normal",
			Handler:    _SendTask_Normal_Handler,
		},
		{
			MethodName: "FavAndTaskerDistrict",
			Handler:    _SendTask_FavAndTaskerDistrict_Handler,
		},
		{
			MethodName: "FavAndTopTasker",
			Handler:    _SendTask_FavAndTopTasker_Handler,
		},
		{
			MethodName: "NewTaskToCity",
			Handler:    _SendTask_NewTaskToCity_Handler,
		},
		{
			MethodName: "ForceTasker",
			Handler:    _SendTask_ForceTasker_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sendTask.proto",
}
