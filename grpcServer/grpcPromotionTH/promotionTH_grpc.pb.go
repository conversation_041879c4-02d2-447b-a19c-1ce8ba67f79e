// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcPromotionTH

import (
	context "context"
	promotionResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotionResponse"
	signUpPromotionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/signUpPromotionRequest"
	task "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PromotionTHClient is the client API for PromotionTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PromotionTHClient interface {
	CheckPostTaskPromotion(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error)
	CheckSignUpPromotion(ctx context.Context, in *signUpPromotionRequest.SignUpPromotionRequest, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error)
}

type promotionTHClient struct {
	cc grpc.ClientConnInterface
}

func NewPromotionTHClient(cc grpc.ClientConnInterface) PromotionTHClient {
	return &promotionTHClient{cc}
}

func (c *promotionTHClient) CheckPostTaskPromotion(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error) {
	out := new(promotionResponse.PromotionResponse)
	err := c.cc.Invoke(ctx, "/grpcPromotionTH.PromotionTH/CheckPostTaskPromotion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionTHClient) CheckSignUpPromotion(ctx context.Context, in *signUpPromotionRequest.SignUpPromotionRequest, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error) {
	out := new(promotionResponse.PromotionResponse)
	err := c.cc.Invoke(ctx, "/grpcPromotionTH.PromotionTH/CheckSignUpPromotion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PromotionTHServer is the server API for PromotionTH service.
// All implementations must embed UnimplementedPromotionTHServer
// for forward compatibility
type PromotionTHServer interface {
	CheckPostTaskPromotion(context.Context, *task.Task) (*promotionResponse.PromotionResponse, error)
	CheckSignUpPromotion(context.Context, *signUpPromotionRequest.SignUpPromotionRequest) (*promotionResponse.PromotionResponse, error)
	mustEmbedUnimplementedPromotionTHServer()
}

// UnimplementedPromotionTHServer must be embedded to have forward compatible implementations.
type UnimplementedPromotionTHServer struct {
}

func (UnimplementedPromotionTHServer) CheckPostTaskPromotion(context.Context, *task.Task) (*promotionResponse.PromotionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPostTaskPromotion not implemented")
}
func (UnimplementedPromotionTHServer) CheckSignUpPromotion(context.Context, *signUpPromotionRequest.SignUpPromotionRequest) (*promotionResponse.PromotionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckSignUpPromotion not implemented")
}
func (UnimplementedPromotionTHServer) mustEmbedUnimplementedPromotionTHServer() {}

// UnsafePromotionTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PromotionTHServer will
// result in compilation errors.
type UnsafePromotionTHServer interface {
	mustEmbedUnimplementedPromotionTHServer()
}

func RegisterPromotionTHServer(s *grpc.Server, srv PromotionTHServer) {
	s.RegisterService(&_PromotionTH_serviceDesc, srv)
}

func _PromotionTH_CheckPostTaskPromotion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(task.Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionTHServer).CheckPostTaskPromotion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPromotionTH.PromotionTH/CheckPostTaskPromotion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionTHServer).CheckPostTaskPromotion(ctx, req.(*task.Task))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionTH_CheckSignUpPromotion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(signUpPromotionRequest.SignUpPromotionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionTHServer).CheckSignUpPromotion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPromotionTH.PromotionTH/CheckSignUpPromotion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionTHServer).CheckSignUpPromotion(ctx, req.(*signUpPromotionRequest.SignUpPromotionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PromotionTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcPromotionTH.PromotionTH",
	HandlerType: (*PromotionTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckPostTaskPromotion",
			Handler:    _PromotionTH_CheckPostTaskPromotion_Handler,
		},
		{
			MethodName: "CheckSignUpPromotion",
			Handler:    _PromotionTH_CheckSignUpPromotion_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "promotionTH.proto",
}
