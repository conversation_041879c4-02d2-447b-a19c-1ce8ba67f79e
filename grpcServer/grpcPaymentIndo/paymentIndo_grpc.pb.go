// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcPaymentIndo

import (
	context "context"
	danaTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/danaTransaction"
	paymentMidtransTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentMidtransTransaction"
	paymentRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentRequest"
	refundTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/refundTransaction"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	vatRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/vatRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PaymentIndoClient is the client API for PaymentIndo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PaymentIndoClient interface {
	CreateGiftByComboVoucher(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error)
	ChargePayment(ctx context.Context, in *paymentMidtransTransaction.PaymentMidtransTransaction, opts ...grpc.CallOption) (*response.Response, error)
	CreateVatRequest(ctx context.Context, in *vatRequest.VatRequest, opts ...grpc.CallOption) (*response.Response, error)
	Refund(ctx context.Context, in *refundTransaction.RefundTransaction, opts ...grpc.CallOption) (*response.Response, error)
	SubscriptionPayment(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error)
	CheckTransactionStatusMidtrans(ctx context.Context, in *paymentMidtransTransaction.PaymentMidtransTransaction, opts ...grpc.CallOption) (*response.Response, error)
	CheckTransactionStatusDANA(ctx context.Context, in *danaTransaction.DanaTransaction, opts ...grpc.CallOption) (*response.Response, error)
}

type paymentIndoClient struct {
	cc grpc.ClientConnInterface
}

func NewPaymentIndoClient(cc grpc.ClientConnInterface) PaymentIndoClient {
	return &paymentIndoClient{cc}
}

func (c *paymentIndoClient) CreateGiftByComboVoucher(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentIndo.PaymentIndo/CreateGiftByComboVoucher", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentIndoClient) ChargePayment(ctx context.Context, in *paymentMidtransTransaction.PaymentMidtransTransaction, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentIndo.PaymentIndo/ChargePayment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentIndoClient) CreateVatRequest(ctx context.Context, in *vatRequest.VatRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentIndo.PaymentIndo/CreateVatRequest", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentIndoClient) Refund(ctx context.Context, in *refundTransaction.RefundTransaction, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentIndo.PaymentIndo/Refund", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentIndoClient) SubscriptionPayment(ctx context.Context, in *paymentRequest.PaymentRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentIndo.PaymentIndo/SubscriptionPayment", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentIndoClient) CheckTransactionStatusMidtrans(ctx context.Context, in *paymentMidtransTransaction.PaymentMidtransTransaction, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentIndo.PaymentIndo/CheckTransactionStatusMidtrans", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *paymentIndoClient) CheckTransactionStatusDANA(ctx context.Context, in *danaTransaction.DanaTransaction, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcPaymentIndo.PaymentIndo/CheckTransactionStatusDANA", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PaymentIndoServer is the server API for PaymentIndo service.
// All implementations must embed UnimplementedPaymentIndoServer
// for forward compatibility
type PaymentIndoServer interface {
	CreateGiftByComboVoucher(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error)
	ChargePayment(context.Context, *paymentMidtransTransaction.PaymentMidtransTransaction) (*response.Response, error)
	CreateVatRequest(context.Context, *vatRequest.VatRequest) (*response.Response, error)
	Refund(context.Context, *refundTransaction.RefundTransaction) (*response.Response, error)
	SubscriptionPayment(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error)
	CheckTransactionStatusMidtrans(context.Context, *paymentMidtransTransaction.PaymentMidtransTransaction) (*response.Response, error)
	CheckTransactionStatusDANA(context.Context, *danaTransaction.DanaTransaction) (*response.Response, error)
	mustEmbedUnimplementedPaymentIndoServer()
}

// UnimplementedPaymentIndoServer must be embedded to have forward compatible implementations.
type UnimplementedPaymentIndoServer struct {
}

func (UnimplementedPaymentIndoServer) CreateGiftByComboVoucher(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGiftByComboVoucher not implemented")
}
func (UnimplementedPaymentIndoServer) ChargePayment(context.Context, *paymentMidtransTransaction.PaymentMidtransTransaction) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChargePayment not implemented")
}
func (UnimplementedPaymentIndoServer) CreateVatRequest(context.Context, *vatRequest.VatRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVatRequest not implemented")
}
func (UnimplementedPaymentIndoServer) Refund(context.Context, *refundTransaction.RefundTransaction) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Refund not implemented")
}
func (UnimplementedPaymentIndoServer) SubscriptionPayment(context.Context, *paymentRequest.PaymentRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SubscriptionPayment not implemented")
}
func (UnimplementedPaymentIndoServer) CheckTransactionStatusMidtrans(context.Context, *paymentMidtransTransaction.PaymentMidtransTransaction) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckTransactionStatusMidtrans not implemented")
}
func (UnimplementedPaymentIndoServer) CheckTransactionStatusDANA(context.Context, *danaTransaction.DanaTransaction) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckTransactionStatusDANA not implemented")
}
func (UnimplementedPaymentIndoServer) mustEmbedUnimplementedPaymentIndoServer() {}

// UnsafePaymentIndoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PaymentIndoServer will
// result in compilation errors.
type UnsafePaymentIndoServer interface {
	mustEmbedUnimplementedPaymentIndoServer()
}

func RegisterPaymentIndoServer(s *grpc.Server, srv PaymentIndoServer) {
	s.RegisterService(&_PaymentIndo_serviceDesc, srv)
}

func _PaymentIndo_CreateGiftByComboVoucher_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentRequest.PaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentIndoServer).CreateGiftByComboVoucher(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentIndo.PaymentIndo/CreateGiftByComboVoucher",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentIndoServer).CreateGiftByComboVoucher(ctx, req.(*paymentRequest.PaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentIndo_ChargePayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentMidtransTransaction.PaymentMidtransTransaction)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentIndoServer).ChargePayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentIndo.PaymentIndo/ChargePayment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentIndoServer).ChargePayment(ctx, req.(*paymentMidtransTransaction.PaymentMidtransTransaction))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentIndo_CreateVatRequest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(vatRequest.VatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentIndoServer).CreateVatRequest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentIndo.PaymentIndo/CreateVatRequest",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentIndoServer).CreateVatRequest(ctx, req.(*vatRequest.VatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentIndo_Refund_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(refundTransaction.RefundTransaction)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentIndoServer).Refund(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentIndo.PaymentIndo/Refund",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentIndoServer).Refund(ctx, req.(*refundTransaction.RefundTransaction))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentIndo_SubscriptionPayment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentRequest.PaymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentIndoServer).SubscriptionPayment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentIndo.PaymentIndo/SubscriptionPayment",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentIndoServer).SubscriptionPayment(ctx, req.(*paymentRequest.PaymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentIndo_CheckTransactionStatusMidtrans_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(paymentMidtransTransaction.PaymentMidtransTransaction)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentIndoServer).CheckTransactionStatusMidtrans(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentIndo.PaymentIndo/CheckTransactionStatusMidtrans",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentIndoServer).CheckTransactionStatusMidtrans(ctx, req.(*paymentMidtransTransaction.PaymentMidtransTransaction))
	}
	return interceptor(ctx, in, info, handler)
}

func _PaymentIndo_CheckTransactionStatusDANA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(danaTransaction.DanaTransaction)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PaymentIndoServer).CheckTransactionStatusDANA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPaymentIndo.PaymentIndo/CheckTransactionStatusDANA",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PaymentIndoServer).CheckTransactionStatusDANA(ctx, req.(*danaTransaction.DanaTransaction))
	}
	return interceptor(ctx, in, info, handler)
}

var _PaymentIndo_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcPaymentIndo.PaymentIndo",
	HandlerType: (*PaymentIndoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateGiftByComboVoucher",
			Handler:    _PaymentIndo_CreateGiftByComboVoucher_Handler,
		},
		{
			MethodName: "ChargePayment",
			Handler:    _PaymentIndo_ChargePayment_Handler,
		},
		{
			MethodName: "CreateVatRequest",
			Handler:    _PaymentIndo_CreateVatRequest_Handler,
		},
		{
			MethodName: "Refund",
			Handler:    _PaymentIndo_Refund_Handler,
		},
		{
			MethodName: "SubscriptionPayment",
			Handler:    _PaymentIndo_SubscriptionPayment_Handler,
		},
		{
			MethodName: "CheckTransactionStatusMidtrans",
			Handler:    _PaymentIndo_CheckTransactionStatusMidtrans_Handler,
		},
		{
			MethodName: "CheckTransactionStatusDANA",
			Handler:    _PaymentIndo_CheckTransactionStatusDANA_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "paymentIndo.proto",
}
