import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import {
  revertTimeZone,
  trackingScheduleStatus,
  postToSlack,
} from '/server/helper-function';

const runCleanUpNotification = () => {
  return;
	console.log("Start runCleanUpNotification ...");
  var message = null;
  try {
    Meteor.appCollection.Notification.remove({
      createdAt: {
        $lt: moment().subtract(30, 'days').toDate() // Filter notification older last 30 days
      },
      type: {
        $in: [
          0, // New task notification to Tasker
          3, // Task has been done, rate task message to Asker
          4, // Task has been done, thank message to Tasker
          6, // Deposit success message
          10, // Task was cancelled message
          19, // Financial account is low message to Tasker
          24, // Email has been verified message
          25, // 1 time notification
        ]
      }
    });
  } catch(ex) {
    message = ex;
    postToSlack({
      channel: 'cron-jobs-alert',
      text: `Workflow: There was an error on runCleanUpNotification. Message: ${message}`,
    });
  }
	console.log("Finished runCleanUpNotification ");
  // Tracking synced cron status
  trackingScheduleStatus({name: 'AutoCleanUpNotification', nextRunAt: SyncedCron.nextScheduledAtDate('autoCleanUpNotification'), message});
};

  Meteor.methods({
    autoCleanUpNotification() {
      SyncedCron.add({
        name: 'autoCleanUpNotification',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 1)).getHours();
          // Run at 1:30 every 7 days
          return parser.cron(`0 30 ${hourInTimeZoneServer} 1/7 * ? *`, true);
        },
        job() {
          runCleanUpNotification();
        },
      });
	    console.log("Added autoCleanUpNotification cron.");
    },
    'runNow.autoCleanUpNotification'() {
      runCleanUpNotification();
    },
  });
