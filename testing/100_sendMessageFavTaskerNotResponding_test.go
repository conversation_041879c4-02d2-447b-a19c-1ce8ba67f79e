package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"go.mongodb.org/mongo-driver/bson"
)

func TestSendMessageFavTaskerNotResponding(t *testing.T) {
	// message remind after 45 minutes
	t.Run("1", func(t *testing.T) {
		ResetData()
		apiURL := apiSyncCron + "/send-message-fav-tasker-not-responding"

		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 01",
			}, {
				"phone":     "0823456789",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker 01",
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		date := now.AddDate(0, 0, 7)
		createdAtTime := now.Add(-50 * time.Minute)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"forceTasker": map[string]interface{}{
					"taskerId": "0823456789",
					"name":     "Tasker",
				},
			},
		})
		chatIds := CreateChatConversation([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0823456789",
				"taskId":      taskIds[0],
				"askerName":   "Asker 0",
				"taskerName":  "Tasker",
				"messages": []map[string]interface{}{
					{
						"_id":       "x63db5fc8e4ca3a9d185346f4",
						"from":      "TASKER",
						"message":   "Xin chào, tôi đã nhận làm công việc của bạn. Tôi sẽ đến đúng giờ.",
						"userId":    "xxx",
						"isRead":    false,
						"createdAt": globalLib.GetCurrentTime(local.TimeZone),
					},
				},
				"members": []map[string]interface{}{
					{
						"_id": "0834567890",
					},
					{
						"_id": "0823456789",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				chatMessage, _ := pkgChatMessage.GetChatConversationWithMessages(globalConstant.ISO_CODE_VN, bson.M{"_id": chatIds[0]}, bson.M{})
				So(chatMessage.AskerId, ShouldEqual, "0834567890")
				So(chatMessage.TaskerId, ShouldEqual, "0823456789")
				So(len(chatMessage.Messages), ShouldEqual, 2)
				So(chatMessage.Messages[1].From, ShouldEqual, "SYSTEM")
				So(chatMessage.Messages[1].MessageBySystem.Title.Vi, ShouldEqual, "Tasker yêu thích chưa thể phản hồi")
				So(chatMessage.Messages[1].MessageBySystem.Text.Vi, ShouldEqual, "Có thể Tasker yêu thích của bạn đang bận hoặc đang trong ca làm việc nên chưa thể xác nhận công việc, vui lòng kiên nhẫn chờ đợi. Nếu bạn mong muốn công việc được nhận sớm, bạn có thể cân nhắc gửi yêu cầu cho Tasker khác.")
				So(chatMessage.Messages[1].TaskRequestData.ExpiredAt, ShouldBeNil)
			})
		})
	})
	// message remind after 45 minutes exists
	t.Run("2", func(t *testing.T) {
		ResetData()
		apiURL := apiSyncCron + "/send-message-fav-tasker-not-responding"

		CreateUser([]map[string]interface{}{
			{
				"phone":     "0834567890",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 01",
			}, {
				"phone":     "0823456789",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker 01",
			},
		})
		now := globalLib.GetCurrentTime(local.TimeZone)
		date := now.AddDate(0, 0, 7)
		createdAtTime := now.Add(-55 * time.Minute)
		remindCreatedAtTime := now.Add(-5 * time.Minute)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"dateOptions": []map[string]interface{}{
					{
						"date": date.Format(time.RFC3339),
					},
					{
						"date": date.Add(4 * time.Hour).Format(time.RFC3339),
					},
					{
						"date": date.Add(2 * time.Hour).Format(time.RFC3339),
					},
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  local.ISO_CODE,
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"forceTasker": map[string]interface{}{
					"taskerId":               "0823456789",
					"name":                   "Tasker",
					"remindMessageCreatedAt": time.Date(remindCreatedAtTime.Year(), remindCreatedAtTime.Month(), remindCreatedAtTime.Day(), remindCreatedAtTime.Hour(), remindCreatedAtTime.Minute(), 0, 0, local.TimeZone),
				},
			},
		})
		chatIds := CreateChatConversation([]map[string]interface{}{
			{
				"askerPhone":  "0834567890",
				"taskerPhone": "0823456789",
				"taskId":      taskIds[0],
				"askerName":   "Asker 0",
				"taskerName":  "Tasker",
				"messages": []map[string]interface{}{
					{
						"_id":  "x63db5fc8e4ca3a9d185346f4",
						"from": "TASKER",
						"messageBySystem": map[string]interface{}{
							"key": "BOOK_WITH_FAV",
							"title": map[string]interface{}{
								"vi": "Tasker yêu thích chưa thể phản hồi",
								"en": "Your favorite Tasker has not responded yet",
								"ms": "Your favorite Tasker has not responded yet",
							},
							"text": map[string]interface{}{
								"vi": "Có thể Tasker yêu thích của bạn đang bận hoặc đang trong ca làm việc nên chưa thể xác nhận công việc, vui lòng kiên nhẫn chờ đợi. Nếu bạn mong muốn công việc được nhận sớm, bạn có thể cân nhắc gửi yêu cầu cho Tasker khác.",
								"en": "Your favorite Tasker may be busy or currently on shift, so they haven't been able to confirm the task yet.",
								"ms": "Your favorite Tasker may be busy or currently on shift, so they haven't been able to confirm the task yet.",
							},
							"sendTo": "ASKER",
							"actions": []map[string]interface{}{
								{
									"type": "SECONDARY",
									"title": map[string]interface{}{
										"vi": "Gửi cho Tasker khác",
										"en": "Send to another Tasker",
										"ms": "Send to another Tasker",
									},
									"key": "SEND_TO_OTHER_TASKER",
								},
								{
									"type": "PRIMARY",
									"title": map[string]interface{}{
										"vi": "Nhắc Tasker nhận việc",
										"en": "Remind Tasker to accept the task",
										"ms": "Remind Tasker to accept the task",
									},
									"key": "REMIND_TASKER",
								},
							},
						},
						"type":   "MESSAGE_REMIND_AFTER_55_MINUTES_CREATE_TASK",
						"chatId": "x440dd79ef29af0a72c1ee46930add05a",
						"taskRequestData": map[string]interface{}{
							"taskInfo": map[string]interface{}{
								"taskId":   "x7d6b2b002a78a6b6db69ce357a5d25c5",
								"district": "Thủ Dầu Một",
								"dateOptions": []map[string]interface{}{
									{
										"date": time.Date(date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute(), 0, 0, local.TimeZone),
									},
								},
								"duration": 2.0,
								"serviceText": map[string]interface{}{
									"en": "CLEANING",
								},
							},
						},
						"createdAt": globalLib.GetCurrentTime(local.TimeZone),
					},
				},
				"members": []map[string]interface{}{
					{
						"_id": "0834567890",
					},
					{
						"_id": "0823456789",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)
				chatMessage, _ := pkgChatMessage.GetChatConversationWithMessages(globalConstant.ISO_CODE_VN, bson.M{"_id": chatIds[0]}, bson.M{})
				So(chatMessage.AskerId, ShouldEqual, "0834567890")
				So(chatMessage.TaskerId, ShouldEqual, "0823456789")
				So(len(chatMessage.Messages), ShouldEqual, 1)
			})
		})
	})
}
