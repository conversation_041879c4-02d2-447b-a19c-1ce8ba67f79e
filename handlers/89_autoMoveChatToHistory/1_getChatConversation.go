package autoMoveChatToHistory

import (
	"fmt"
	"sync"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"go.mongodb.org/mongo-driver/bson"
)

// getChatConversation retrieves chat messages based on the provided ISO code and current time.
func getChatConversation(w *sync.WaitGroup, currentTime time.Time) chan string {
	var page int64 = 1                             // Initialize page number
	var errCount int                               // Initialize error count
	chatConversationCh := make(chan string, LIMIT) // Create channel for tasker BNPL data
	go func() {
		defer w.Done()                  // Notify wait group when done
		defer close(chatConversationCh) // Close channel when done getting data

		for {
			chatConversationData, err := getDataChatConversation(currentTime, page) // Get tasks data by ISO code and current time
			if err == nil {
				for _, chat := range chatConversationData {
					chatConversationCh <- chat.XId // Send each task to the channel
				}

				if len(chatConversationData) > 0 {
					page++       // Increment the page number
					errCount = 0 // Reset error count
				} else {
					break // Break the loop when all data has been fetched
				}
			} else {
				errCount++
				if errCount >= 5 {
					errCount = 0
					msg := fmt.Sprintf("AutoMoveChatToHistory-VN: Get data chat conversation - page:%v error: %v", page, err)
					globalLib.PostToSlack(cfg.SlackToken, lib.GO_SERVICE_CHANNEL, "bTaskee System", msg) // Post error message to Slack
					break                                                                                // Break the loop when error count exceeds 5
				}
			}
		}
	}()

	return chatConversationCh // Return channel for tasker BNPL data
}

// getDataChatConversation retrieves a list of tasks based on the given parameters.
// It returns the list of tasks and an error, if any.
func getDataChatConversation(currentTime time.Time, page int64) ([]*modelChatMessage.ChatMessage, error) {
	// Define the fields to be returned in the query result
	fields := bson.M{
		"_id": 1, // Include the _id field
	}
	// Define the query to filter the tasks
	query := bson.M{
		"doneTaskAt": bson.M{"$lte": currentTime.AddDate(0, 0, -7)}, // Filter tasks with the doneTaskAt field less than or equal to the current time minus 7 days
	}
	// Retrieve the tasks from the database based on the query and paging parameters
	// chats, err := pkgChatMessage.GetChatMessagesSkipLimitSort(isoCode, query, fields, page, LIMIT, bson.M{})
	var chats []*modelChatMessage.ChatMessage
	err := globalDataAccess.GetAllByQueryPaging(globalCollection.COLLECTION_CHAT_CONVERSATION[local.ISO_CODE], query, fields, page, LIMIT, &chats)
	return chats, err // Return the tasks and the error, if any
}

// func removeChatConversation(chatIds []string) {
// 	// Define the query to filter the tasks
// 	query := bson.M{
// 		"_id": bson.M{"$in": chatIds}, // Filter tasks with the doneTaskAt field less than or equal to the current time minus 7 days
// 	}
// 	err := globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_CHAT_CONVERSATION[local.ISO_CODE], query)
// 	if err != nil {
// 		globalLib.PostToSlack(cfg.SlackToken, lib.GO_SERVICE_CHANNEL, "bTaskee System", fmt.Sprintf("AutoMoveChatToHistory-%s: Remove chatConversation - error: %v - chatIds: %v", local.ISO_CODE, err, chatIds))
// 	}
// }
