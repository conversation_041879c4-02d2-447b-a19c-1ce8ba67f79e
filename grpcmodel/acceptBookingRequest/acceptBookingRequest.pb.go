// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.7.1
// source: grpcmodel/acceptBookingRequest/acceptBookingRequest.proto

package acceptBookingRequest

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AcceptBookingRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	BookingId string `protobuf:"bytes,1,opt,name=bookingId,proto3" json:"bookingId,omitempty"`
	TaskId    string `protobuf:"bytes,2,opt,name=taskId,proto3" json:"taskId,omitempty"`
	TaskerId  string `protobuf:"bytes,3,opt,name=taskerId,proto3" json:"taskerId,omitempty"`
	From      string `protobuf:"bytes,4,opt,name=from,proto3" json:"from,omitempty"`
}

func (x *AcceptBookingRequest) Reset() {
	*x = AcceptBookingRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *AcceptBookingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AcceptBookingRequest) ProtoMessage() {}

func (x *AcceptBookingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AcceptBookingRequest.ProtoReflect.Descriptor instead.
func (*AcceptBookingRequest) Descriptor() ([]byte, []int) {
	return file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_rawDescGZIP(), []int{0}
}

func (x *AcceptBookingRequest) GetBookingId() string {
	if x != nil {
		return x.BookingId
	}
	return ""
}

func (x *AcceptBookingRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

func (x *AcceptBookingRequest) GetTaskerId() string {
	if x != nil {
		return x.TaskerId
	}
	return ""
}

func (x *AcceptBookingRequest) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

var File_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto protoreflect.FileDescriptor

var file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_rawDesc = []byte{
	0x0a, 0x39, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x61, 0x63, 0x63, 0x65,
	0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x2f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x14, 0x61, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x22, 0x7c, 0x0a, 0x14, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x6f, 0x6f,
	0x6b, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x6f,
	0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12,
	0x1a, 0x0a, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x49, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x66,
	0x72, 0x6f, 0x6d, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x42,
	0x48, 0x5a, 0x46, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74,
	0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d,
	0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x61, 0x63, 0x63, 0x65, 0x70, 0x74, 0x42, 0x6f, 0x6f, 0x6b, 0x69,
	0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_rawDescOnce sync.Once
	file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_rawDescData = file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_rawDesc
)

func file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_rawDescGZIP() []byte {
	file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_rawDescOnce.Do(func() {
		file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_rawDescData = protoimpl.X.CompressGZIP(file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_rawDescData)
	})
	return file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_rawDescData
}

var file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_goTypes = []interface{}{
	(*AcceptBookingRequest)(nil), // 0: acceptBookingRequest.AcceptBookingRequest
}
var file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_init() }
func file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_init() {
	if File_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*AcceptBookingRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_goTypes,
		DependencyIndexes: file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_depIdxs,
		MessageInfos:      file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_msgTypes,
	}.Build()
	File_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto = out.File
	file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_rawDesc = nil
	file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_goTypes = nil
	file_grpcmodel_acceptBookingRequest_acceptBookingRequest_proto_depIdxs = nil
}
