package service

import (
	"net/http"

	sendMessageFavTaskerNotResponding "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/100_sendMessageFavTaskerNotResponding"
	voucherComboSubscriptionNotify "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/101_voucherComboSubscriptionNotify"
	extendExpiredComboVoucher "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/102_expiredExtendComboVoucher"
	autoCheckTaskerWorkingPlaces "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/106_autoCheckTaskerWorkingPlaces"
	notifyTaskerGameCampaign "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/107_notiTaskerGameCampaign"
	createTaskerWorkingHistory "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/108_createTaskerWorkingHistory"
	createTaskerMonthlyReport "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/109_createTaskerMonthlyReport"
	sendNotiToRemindTaskerResponseTaskForceTasker "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/110_autoSendNotiToRemindTaskerResponseTaskForceTasker"
	cancelTaskForceTasker "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/111_cancelTaskForceTasker"
	sendMessageBeforeCancelTaskForceTasker "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/112_sendMessageBeforeCancelTaskForceTasker"
	autoReportTasksInfo "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/113_autoReportTasksInfo"
	rollbackMissingTaskActionPenalty "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/114_rollbackMissingTaskActionPenalty"
	updateTrustPoint "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/1_updateTrustPoint"
	autoCheckIncentive "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/2_autoCheckIncentive"
	autoCalculateJourneyLeaderBoard "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/73_autoCalculateJourneyLeaderBoard"
	createAskerMonthlyReport "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/86_createAskerMonthlyReport"
	autoMoveChatToHistory "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/89_autoMoveChatToHistory"
	autoFindTaskersHaveStarCanDoTestTraining "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/90_autoFindTaskersHaveStarCanDoTestTraining"
	weeklyResetLeaderBoard "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/91_weeklyResetLeaderBoard"
	businessAllocation "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/92_businessAllocation"
	autoNotifyForCommunityNotification "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/93_autoNotifyForCommunityNotification"
	autoPostNewFeedCommunity "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/94_autoPostNewFeedCommunity"
	autoUpgradeMedal "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/95_autoUpgradeMedalForCommunityUser"
	autoSendNotiAboutTestDeadline "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/96_autoSendNotiAboutTestDeadline"
	autoSendNotiAboutNewTestAndReviewToTasker "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/97_autoSendNotiAboutNewTestAndReviewToTasker"
	giveRewardTaskerDoneSpecialCampaign "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers/99_giveRewardTaskerDoneSpecialCampaign"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"go.uber.org/zap"
)

func AutoReportTasksInfo(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	result := autoReportTasksInfo.Run(reqBody)
	globalResponse.ResponseSuccess(w, result)
}

// RunReactiveAskerAfterBlocked is api for Run now sync cron
func UpdateTrustPoint(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	updateTrustPoint.Run(reqBody.Action)
	globalResponse.ResponseSuccess(w, nil)
}

func AutoCheckIncentive(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	autoCheckIncentive.Run(reqBody.Action)
	globalResponse.ResponseSuccess(w, nil)
}

func AutoMoveChatToHistory(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	autoMoveChatToHistory.Run(reqBody)
	globalResponse.ResponseSuccess(w, nil)
}

func AutoCalculateJourneyLeaderBoard(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	result := autoCalculateJourneyLeaderBoard.Run(reqBody)
	globalResponse.ResponseSuccess(w, result)
}

func WeeklyResetLeaderBoard(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	result := weeklyResetLeaderBoard.Run(reqBody)
	globalResponse.ResponseSuccess(w, result)
}

func GiveRewardTaskerDoneSpecialCampaign(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	giveRewardTaskerDoneSpecialCampaign.Run(reqBody)
	globalResponse.ResponseSuccess(w, nil)
}

func SendMessageFavTaskerNotResponding(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	sendMessageFavTaskerNotResponding.Run(reqBody)
	globalResponse.ResponseSuccess(w, nil)
}

func AutoCheckTaskerWorkingPlaces(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	result := autoCheckTaskerWorkingPlaces.Run(reqBody.Action)
	globalResponse.ResponseSuccess(w, result)
}

func NotifyTaskerGameCampaign(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	notifyTaskerGameCampaign.Run(reqBody)
	globalResponse.ResponseSuccess(w, nil)
}

func CreateTaskerWorkingHistory(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	createTaskerWorkingHistory.Run(reqBody)
	globalResponse.ResponseSuccess(w, nil)
}

func BusinessAllocation(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	businessAllocation.Run(reqBody)
	globalResponse.ResponseSuccess(w, nil)
}

func CreateAskerMonthlyReport(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	createAskerMonthlyReport.Run(reqBody)
	globalResponse.ResponseSuccess(w, nil)
}

func CreateTaskerMonthlyReport(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	createTaskerMonthlyReport.Run(reqBody)
	globalResponse.ResponseSuccess(w, nil)
}

func AutoPostNewFeedCommunity(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	autoPostNewFeedCommunity.Run(reqBody.Action)
	globalResponse.ResponseSuccess(w, nil)
}

func AutoUpgradeMedalForCommunityUser(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	autoUpgradeMedal.Run(reqBody)
	globalResponse.ResponseSuccess(w, nil)
}

func AutoNotifyForCommunityNotification(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	result := autoNotifyForCommunityNotification.Run(reqBody)
	globalResponse.ResponseSuccess(w, result)
}

func SendNotiToRemindTaskerResponseTaskForceTasker(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	sendNotiToRemindTaskerResponseTaskForceTasker.Run(reqBody)
	globalResponse.ResponseSuccess(w, nil)
}

func AutoFindTaskersHaveStarCanDoTestTraining(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	autoFindTaskersHaveStarCanDoTestTraining.Run(reqBody)
	globalResponse.ResponseSuccess(w, nil)
}
func AutoSendNotiAboutNewTestAndReviewToTasker(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	autoSendNotiAboutNewTestAndReviewToTasker.Run(reqBody)
	globalResponse.ResponseSuccess(w, nil)
}

func AutoSendNotiAboutTestDeadline(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	autoSendNotiAboutTestDeadline.Run(reqBody)
	globalResponse.ResponseSuccess(w, nil)
}

func CancelTaskForceTasker(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	cancelTaskForceTasker.Run(reqBody.Action)
	globalResponse.ResponseSuccess(w, nil)
}

func VoucherComboSubscriptionNotify(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	result := voucherComboSubscriptionNotify.Run(reqBody)
	globalResponse.ResponseSuccess(w, result)
}

func ExtendExpiredComboVoucher(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	result := extendExpiredComboVoucher.Run(reqBody)
	globalResponse.ResponseSuccess(w, result)
}
func SendMessageBeforeCancelTaskForceTasker(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	sendMessageBeforeCancelTaskForceTasker.Run(reqBody.Action)
	globalResponse.ResponseSuccess(w, nil)
}

func RollbackMissingTaskActionPenalty(w http.ResponseWriter, r *http.Request) {
	reqBody, errCode := lib.ParseBodyParams(r)
	if errCode != nil {
		local.Logger.Warn(errCode.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Any("body", reqBody),
		)
		globalResponse.ResponseError(w, *errCode)
		return
	}
	result := rollbackMissingTaskActionPenalty.RollbackMissingTaskActionPenalty(reqBody)
	globalResponse.ResponseSuccess(w, result)
}
