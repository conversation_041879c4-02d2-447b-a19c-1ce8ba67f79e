// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcWebsocketVN

import (
	context "context"
	websocketMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// WebsocketVNClient is the client API for WebsocketVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WebsocketVNClient interface {
	SendChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*emptypb.Empty, error)
	SendSocketMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type websocketVNClient struct {
	cc grpc.ClientConnInterface
}

func NewWebsocketVNClient(cc grpc.ClientConnInterface) WebsocketVNClient {
	return &websocketVNClient{cc}
}

func (c *websocketVNClient) SendChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/grpcWebsocketVN.WebsocketVN/SendChatMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *websocketVNClient) SendSocketMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/grpcWebsocketVN.WebsocketVN/SendSocketMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WebsocketVNServer is the server API for WebsocketVN service.
// All implementations must embed UnimplementedWebsocketVNServer
// for forward compatibility
type WebsocketVNServer interface {
	SendChatMessage(context.Context, *websocketMessage.WebsocketMessage) (*emptypb.Empty, error)
	SendSocketMessage(context.Context, *websocketMessage.WebsocketMessage) (*emptypb.Empty, error)
	mustEmbedUnimplementedWebsocketVNServer()
}

// UnimplementedWebsocketVNServer must be embedded to have forward compatible implementations.
type UnimplementedWebsocketVNServer struct {
}

func (UnimplementedWebsocketVNServer) SendChatMessage(context.Context, *websocketMessage.WebsocketMessage) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendChatMessage not implemented")
}
func (UnimplementedWebsocketVNServer) SendSocketMessage(context.Context, *websocketMessage.WebsocketMessage) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSocketMessage not implemented")
}
func (UnimplementedWebsocketVNServer) mustEmbedUnimplementedWebsocketVNServer() {}

// UnsafeWebsocketVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WebsocketVNServer will
// result in compilation errors.
type UnsafeWebsocketVNServer interface {
	mustEmbedUnimplementedWebsocketVNServer()
}

func RegisterWebsocketVNServer(s *grpc.Server, srv WebsocketVNServer) {
	s.RegisterService(&_WebsocketVN_serviceDesc, srv)
}

func _WebsocketVN_SendChatMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(websocketMessage.WebsocketMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebsocketVNServer).SendChatMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcWebsocketVN.WebsocketVN/SendChatMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebsocketVNServer).SendChatMessage(ctx, req.(*websocketMessage.WebsocketMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebsocketVN_SendSocketMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(websocketMessage.WebsocketMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebsocketVNServer).SendSocketMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcWebsocketVN.WebsocketVN/SendSocketMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebsocketVNServer).SendSocketMessage(ctx, req.(*websocketMessage.WebsocketMessage))
	}
	return interceptor(ctx, in, info, handler)
}

var _WebsocketVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcWebsocketVN.WebsocketVN",
	HandlerType: (*WebsocketVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendChatMessage",
			Handler:    _WebsocketVN_SendChatMessage_Handler,
		},
		{
			MethodName: "SendSocketMessage",
			Handler:    _WebsocketVN_SendSocketMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "websocketVN.proto",
}
