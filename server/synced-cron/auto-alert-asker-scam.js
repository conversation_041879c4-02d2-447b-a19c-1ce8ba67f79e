import { Meteor } from 'meteor/meteor';
import _ from 'lodash';
import moment from 'moment';
import {
  trackingScheduleStatus,
  postToSlack,
} from '/server/helper-function';

const runAlertAskerScam = () => {
	console.log("Start runAlertAskerScam ...");
  var message = null;
  const last15Mins = moment().subtract(10, 'minutes').toDate();
  // Fix location neer by scam
  const a = 10.834978;
  const b = 10.814671;
  const c = 106.567379;
  const d = 106.599391;
  const result = Meteor.appCollection.UserLocationHistory.find({
      createdAt: {$gte: last15Mins},
      'history.lat': {$gte: b, $lte: a},
      'history.lng': {$gte: c, $lte: d},
      }).map(x => {
          const exists = x.history.find(item => {
            return item.lat >= b && item.lat <= a && item.lng >= c && item.lng <= d
          });
          if ( exists ) return x.phone + ' - ' + x.name;
          }).filter( z => z);
  if ( result && result.length > 0 ) {
    postToSlack({
      channel: 'scam-warning',
      text: `Khách hàng mới nằm trong khu vực định vị nghi lừa đảo:\n${result.join('\n')}`,
    });
  }
	console.log("Finished runAlertAskerScam ");
  // Tracking
  trackingScheduleStatus({name: 'autoAlertAskerScam', nextRunAt: SyncedCron.nextScheduledAtDate('autoAlertAskerScam'), message});
};

const runAlertChatScam = () => {
	console.log("Start runAlertChatScam ...");
  var message = null;
  const last15Mins = moment().subtract(10, 'minutes').toDate();
  const keyWords = 'lấy đồ|lấy dùm|son môi|lười|tiện đường|lấy giúp|lấy hộ|thị tú|bình thành|thi tu|binh thanh|gò mây|hãy gọi|hủ kem|mua dùm|mua dum';

  const result = Meteor.appCollection.ChatMessage.find({
    messages: {
      $elemMatch: {
        createdAt: {$gte: last15Mins},
        message: {$regex: new RegExp(keyWords, 'i')},
        from: 'ASKER'
      }
    }
    }, {
      fields: {
        askerId: 1,
        messages: 1
      },
      sort: {createdAt: -1}
    }).map(x => {
        const asker = Meteor.appCollection.users.findOne({_id: x.askerId}, {fields: {phone: 1, name: 1}});
        return asker.phone + ' - ' + asker.name + ' - ' + x.messages.filter(x => x.from === 'ASKER').map(chat => chat.message).join('\n');
    });
    if ( result && result.length > 0 ) {
      postToSlack({
        channel: 'scam-warning',
        text: `Nội dung chat có keywords lừa đảo:\n${result.join('\n')}`,
      });
    }

	console.log("Finished runAlertChatScam ");
  // Tracking
  trackingScheduleStatus({name: 'autoAlertAskerScam', nextRunAt: SyncedCron.nextScheduledAtDate('autoAlertAskerScam'), message});
};

const runAlertTaskNote = () => {
	console.log("Start runAlertTaskNote ...");
  var message = null;
  const last15Mins = moment().subtract(10, 'minutes').toDate();
  const keyWords = 'lấy đồ|lấy dùm|son môi|lười|tiện đường|lấy giúp|lấy hộ|thị tú|bình thành|thi tu|binh thanh|gò mây|hãy gọi|hủ kem|mua dùm|mua dum';

  const result = Meteor.appCollection.Task.find({
    createdAt: {$gte: last15Mins},
    taskNote: {$regex: new RegExp(keyWords, 'i')},
    }, {
      fields: {
        askerId: 1,
        taskNote: 1
      },
      sort: {createdAt: -1}
    }).map(x => {
        const asker = Meteor.appCollection.users.findOne({_id: x.askerId}, {fields: {phone: 1, name: 1}});
        return asker.phone + ' - ' + asker.name + ' - ' + task.taskNote;
    });
    if ( result && result.length > 0 ) {
      postToSlack({
        channel: 'scam-warning',
        text: `Nội dung task note có keywords lừa đảo:\n${result.join('\n')}`,
      });
    }

	console.log("Finished runAlertTaskNote ");
  // Tracking
  trackingScheduleStatus({name: 'autoAlertAskerScam', nextRunAt: SyncedCron.nextScheduledAtDate('autoAlertAskerScam'), message});
};

  Meteor.methods({
    autoAlertAskerScam () {
      SyncedCron.add({
        name: 'autoAlertAskerScam',
        schedule(parser) {
          // Run every 5 minutes
          return parser.cron('0 0/5 * 1/1 * ? *', true);
        },
        job() {
          runAlertAskerScam();
          runAlertChatScam();
          runAlertTaskNote();
        },
      });
	    console.log("Added autoAlertAskerScam cron ");
    },
    'runNow.autoAlertAskerScam'() {
      runAlertAskerScam();
      runAlertChatScam();
      runAlertTaskNote();
    },
  });
