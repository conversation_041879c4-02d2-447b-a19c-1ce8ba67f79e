package redisCache

import (
	"context"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/spf13/cast"
)

// RedisCache là struct đại diện cho client Redis.
// N<PERSON> bao gồm một con trỏ đến đối tượng *redis.Client để tương tác với Redis.
type RedisCache struct {
	*redis.Client // Nhúng client Redis vào struct này
}

// NewRedisCache là hàm khởi tạo mới một instance của RedisCache.
// - configMap: Map chứa cấu hình kết nối Redis (address, password, username).
// Nếu không có address được cung cấp, nó sẽ sử dụng giá trị mặc định "localhost:6379".
// Hàm này trả về một con trỏ đến RedisCache đã được cấu hình sẵn sàng để sử dụng.
func NewRedisCache(configMap map[string]interface{}) *RedisCache {
	address := cast.ToString(configMap["address"])
	if address == "" {
		address = "localhost:6379" // Sử dụng địa chỉ mặc định nếu không có cấu hình
	}
	password := cast.ToString(configMap["password"])
	username := cast.ToString(configMap["username"])
	return &RedisCache{
		Client: redis.NewClient(&redis.Options{
			Addr:     address,  // Địa chỉ Redis (ví dụ: "localhost:6379")
			Username: username, // Tên người dùng (nếu có)
			Password: password, // Mật khẩu (nếu có)
			PoolSize: 50,       // Kích thước pool kết nối
			DB:       0,        // Database index (mặc định là 0)
		}),
	}
}

// SetValueByKey là hàm dùng để lưu trữ một key-value vào cache Redis với thời gian hết hạn.
// - ctx: Context để quản lý timeout hoặc hủy bỏ yêu cầu.
// - key: Key cần lưu trữ trong Redis.
// - value: Giá trị cần lưu trữ.
// - expiration: Thời gian sống (TTL) của key (ví dụ: 5 * time.Minute).
// Hàm này trả về lỗi (error) nếu có vấn đề xảy ra trong quá trình lưu trữ.
func (r *RedisCache) SetValueByKey(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	if r == nil {
		return nil // Trả về nil nếu RedisCache chưa được khởi tạo
	}
	// Đặt một key vào cache với thời gian hết hạn
	return r.Set(ctx, key, value, expiration).Err()
}

// GetValueByKey là hàm dùng để lấy giá trị của một key từ cache Redis.
// - ctx: Context để quản lý timeout hoặc hủy bỏ yêu cầu.
// - key: Key cần lấy giá trị.
// Hàm này trả về một đối tượng *redis.StringCmd, từ đó bạn có thể trích xuất giá trị hoặc kiểm tra lỗi.
func (r *RedisCache) GetValueByKey(ctx context.Context, key string) *redis.StringCmd {
	if r == nil {
		return nil // Trả về nil nếu RedisCache chưa được khởi tạo
	}
	// Lấy giá trị của key
	return r.Get(ctx, key)
}

// DelValueByKey là hàm dùng để xóa một key khỏi cache Redis.
// - ctx: Context để quản lý timeout hoặc hủy bỏ yêu cầu.
// - key: Key cần xóa.
// Hàm này trả về một đối tượng *redis.IntCmd, từ đó bạn có thể trích xuất số lượng key bị xóa hoặc kiểm tra lỗi.
func (r *RedisCache) DelValueByKey(ctx context.Context, key string) *redis.IntCmd {
	if r == nil {
		return nil // Trả về nil nếu RedisCache chưa được khởi tạo
	}
	// Xóa key khỏi cache
	return r.Del(ctx, key)
}

// SetNXByKey là hàm dùng để thiết lập một key-value vào cache Redis chỉ khi key đó chưa tồn tại.
// - ctx: Context để quản lý timeout hoặc hủy bỏ yêu cầu.
// - key: Key cần thiết lập.
// - value: Giá trị cần lưu trữ.
// - expiration: Thời gian sống (TTL) của key (ví dụ: 5 * time.Minute).
// Hàm này trả về một đối tượng *redis.BoolCmd, từ đó bạn có thể kiểm tra xem key đã được thiết lập thành công hay không.
func (r *RedisCache) SetNXByKey(ctx context.Context, key string, value interface{}, expiration time.Duration) (bool, error) {
	if r == nil {
		return true, nil // Trả về nil nếu RedisCache chưa được khởi tạo
	}
	// Thiết lập key-value chỉ khi key chưa tồn tại
	return r.SetNX(ctx, key, value, expiration).Result()
}
