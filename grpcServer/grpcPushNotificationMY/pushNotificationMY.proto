syntax = "proto3";
package grpcPushNotificationMY;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPushNotificationMY";

import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest/pushNotificationRequest.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response/response.proto";

service PushNotification {
  rpc Send (pushNotificationRequest.PushNotificationRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/push-notification-my/send"
      body: "*"
    };
  }
}