package autoPostNewFeedCommunity

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelCommunityPost "gitlab.com/btaskee/go-services-model-v2/grpcmodel/communityPost"
	"go.mongodb.org/mongo-driver/bson"
)

func updateStatusPosts(posts []*modelCommunityPost.CommunityPost) {
	now := globalLib.GetCurrentTime(local.TimeZone)
	for _, post := range posts {
		newStatus := globalConstant.COMMUNITY_POST_STATUS_ACTIVE
		queryUpdate := bson.M{
			"$set": bson.M{
				"status": newStatus,
			},
			"$push": bson.M{
				"changeHistories": bson.M{
					"from": globalConstant.CHANGES_HISTORY_COMMUNITY_KEY_FROM_SYSTEM,
					"key":  globalConstant.CHANGES_HISTORY_COMMUNITY_SCHEDULE_POST,
					"content": map[string]any{
						"oldStatus": post.Status,
						"newStatus": newStatus,
					},
					"createdAt": now,
				},
			},
		}
		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_COMMUNITY_POST[local.ISO_CODE], post.XId, queryUpdate)
	}
}
