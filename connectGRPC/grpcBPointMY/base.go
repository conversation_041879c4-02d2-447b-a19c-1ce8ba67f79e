package grpcBPointMY

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcBPointMY"
	"google.golang.org/grpc"
)

func ConnectGRPCBPoint(endpoint string) (grpcBPointMY.BPointClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcBPointMY.NewBPointClient(connect)
	return client, connect, err
}
