# Using lib.SendNotification for Subscription Notifications

## Overview
Updated subscription handler to use the existing `lib.SendNotification` function instead of custom gRPC implementations, following DRY principle and maintaining consistency with the rest of the codebase.

## Changes Made

### Files Updated
1. **`8_sendNotificationToAsker.go`** - Asker notifications when tasks are posted
2. **`4_postSubscriptionTask.go`** - Tasker notifications when tasks are confirmed

### Before vs After

#### Before (Custom gRPC Implementation)
```go
// Custom gRPC connection and call
client, conn, err := grpcPushNotificationVN.ConnectGRPCPushNotificationVN(cfg.GRPC_Push_Notification_URL)
if err != nil {
    log.Printf("Error connecting to push notification service: %v", err)
    return
}
defer conn.Close()

// Manual request creation
request := &pushNotificationRequest.PushNotificationRequest{
    UserIds: userIds,
    Title:   titleText,
    Body:    bodyText,
    Sound:   "default",
    Payload: payload,
}

// Manual gRPC call with timeout
ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
defer cancel()
response, err := client.Send(ctx, request)
```

#### After (Using lib.SendNotification)
```go
// Simple function call
lib.SendNotification(notiInfos, userIds, titleText, bodyText, payload, true)
```

## Function Signatures

### lib.SendNotification
```go
func SendNotification(
    arrayNotification []interface{}, 
    userIds []*modelPushNotificationRequest.PushNotificationRequestUserIds, 
    title *modelService.ServiceText, 
    body *modelService.ServiceText, 
    payload *modelPushNotificationRequest.PushNotificationRequestPayload, 
    isForceView bool
)
```

### Parameters Used

#### arrayNotification ([]interface{})
Database notifications to be stored:
```go
notiInfos := []interface{}{
    &notification.Notification{
        XId:         globalLib.GenerateObjectId(),
        UserId:      userID,
        TaskId:      taskID,
        Type:        2, // 1 for tasker, 2 for asker
        Title:       globalLib.LocalizeServiceName(lang, title),
        Description: globalLib.LocalizeServiceName(lang, text),
        CreatedAt:   timestamppb.New(globalLib.GetCurrentTime(local.TimeZone)),
        NavigateTo:  "TaskDetail",
    },
}
```

#### userIds ([]*PushNotificationRequestUserIds)
Target users for push notifications:
```go
userIds := []*pushNotificationRequest.PushNotificationRequestUserIds{
    {
        UserId:   userID,
        Language: lang,
    },
}
```

#### title & body (*service.ServiceText)
Localized notification content:
```go
titleText := &service.ServiceText{
    Vi: globalLib.LocalizeServiceName(lang, title),
}
bodyText := &service.ServiceText{
    Vi: globalLib.LocalizeServiceName(lang, text),
}
```

#### payload (*PushNotificationRequestPayload)
Navigation and metadata:
```go
payload := &pushNotificationRequest.PushNotificationRequestPayload{
    Type:       2, // 1 for tasker confirmation, 2 for asker task posted
    TaskId:     taskID,
    NavigateTo: "TaskDetail",
}
```

#### isForceView (bool)
Whether to force save notification to database: `true`

## Notification Types

### 1. Asker Notifications (Type=2)
**Trigger**: When subscription task is posted
**Function**: `sendNotificationToAsker()`
**Content**:
- Title: `NOTIFICATION_MESSAGE_ASKER_POSTED_TITLE`
- Body: `NOTIFICATION_MESSAGE_ASKER_POSTED`
- Navigation: "TaskDetail"

### 2. Tasker Notifications (Type=1)
**Trigger**: When task is force-accepted to tasker
**Function**: `sendConfirmationNotification()`
**Content**:
- Title: `NOTIFICATION_MESSAGE_TASKER_CONFIRMED_TITLE`
- Body: `NOTIFICATION_MESSAGE_TASKER_CONFIRMED`
- Navigation: "TaskDetail"

## Benefits of Using lib.SendNotification

### 1. Code Consistency
- Uses same notification system as rest of codebase
- Follows established patterns and conventions
- Maintains consistent error handling

### 2. Reduced Code Duplication
- Eliminates duplicate gRPC connection logic
- Removes redundant error handling code
- Centralizes notification functionality

### 3. Built-in Features
- **Database Storage**: Automatically saves notifications to DB
- **WebSocket Integration**: Sends real-time notifications via websocket
- **Error Handling**: Centralized error logging and handling
- **Connection Management**: Automatic connection lifecycle management

### 4. Maintainability
- Single point of change for notification logic
- Easier to update notification behavior globally
- Consistent logging and monitoring

### 5. Reliability
- Tested and proven notification system
- Proper timeout handling
- Graceful error recovery

## Implementation Details

### Language Support
Both functions support user language preferences:
```go
lang := globalConstant.LANG_VI
if language != "" {
    lang = language
}
```

### Database Integration
Notifications are automatically stored in the database:
- Collection: `COLLECTION_NOTIFICATION[local.ISO_CODE]`
- Fields: ID, UserID, TaskID, Type, Title, Description, CreatedAt, NavigateTo

### WebSocket Integration
Real-time notifications are sent via websocket:
- Function: `grpcWebsocket.SendSocketNotification()`
- Automatic integration through `lib.SendNotification`

### Error Handling
Centralized error handling in `lib.SendNotification`:
- Connection errors logged with context
- gRPC errors logged with request details
- Graceful degradation on failures

## Usage Examples

### Asker Notification
```go
sendNotificationToAsker(taskID, userID, language, serviceText)
```

### Tasker Notification
```go
sendConfirmationNotification(taskerID, taskID)
```

## Imports Required
```go
import (
    "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
    "gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
    "gitlab.com/btaskee/go-services-model-v2/globalConstant"
    "gitlab.com/btaskee/go-services-model-v2/globalLib"
    "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
    "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
    "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
    "gitlab.com/btaskee/go-services-model-v2/localization"
    "google.golang.org/protobuf/types/known/timestamppb"
)
```

## Testing
Both notification functions can be tested by:
1. Creating test subscriptions
2. Triggering task posting
3. Verifying notifications in database
4. Checking push notification delivery
5. Validating websocket messages

## Monitoring
Notifications can be monitored through:
- Database notification records
- Push notification service logs
- WebSocket connection logs
- User engagement metrics

## Future Enhancements
Using `lib.SendNotification` enables easy implementation of:
- Notification templates
- A/B testing for notification content
- Analytics and tracking
- Batch notification processing
- Advanced targeting and personalization
