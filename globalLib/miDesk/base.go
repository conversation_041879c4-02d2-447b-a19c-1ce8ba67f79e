package miDesk

import (
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
)

func SendToMiDesk(title, content, channel, priority string, asker *modelUser.Users, cfgLoginUrl, cfgCreateTicketUrl, cfgEmail, cfgPassword, cfgSlackToken string) {
	// Không còn hợp tác với midesk nữa
	// token := globalLib.GetMiDeskAccessToken(cfgLoginUrl, cfgEmail, cfgPassword, cfgSlackToken)
	// if token == "" {
	// 	return
	// }
	// var email string
	// if len(asker.GetEmails()) > 0 {
	// 	email = asker.GetEmails()[0].Address
	// }
	// globalLib.CallCreateMiDeskTicket(cfgCreateTicketUrl, token, cfgSlackToken, map[string]interface{}{
	// 	"title":    title,
	// 	"content":  content,
	// 	"channel":  channel,
	// 	"priority": priority, // 1: Khẩn cấp 2: Cao 3: Trung bình
	// 	"contact": map[string]interface{}{
	// 		"name":  asker.Name,
	// 		"email": email,
	// 		"phone": asker.Phone,
	// 	},
	// })
}
