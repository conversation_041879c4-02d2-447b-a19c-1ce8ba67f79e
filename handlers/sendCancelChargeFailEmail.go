package handlers

import (
	"errors"
	"fmt"
	"net/http"
	"time"

	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

/*
* @Description: Migrate and send receipt email
* @CreatedAt: 04/09/2020
* @Author: linhnh
* @UpdatedAt: 24/02/2021
* @UpdatedBy: ngoctb3
 */
func SendCancelChargeFailEmail(req *modelEmailSending.EmailCancelFeeRequest) (*modelEmailResponse.EmailResponse, error) {
	defer local.Logger.Sync()

	// Check server config
	resErr := CheckEmailConfig()
	if resErr != nil {
		local.Logger.Error(resErr.Message)
		return resErr, nil
	}

	if req == nil || req.TaskId == "" {
		local.Logger.Warn(lib.ERROR_EMAIL_RECEIPT_TASK_EMPTY)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_EMAIL_RECEIPT_TASK_EMPTY,
		}, nil
	}

	var task *modelTask.Task
	globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], req.TaskId, bson.M{"_id": 1, "askerId": 1, "isoCode": 1, "originCurrency": 1, "currency": 1, "date": 1, "address": 1, "payment": 1, "duration": 1, "createdAt": 1, "isTetBooking": 1, "serviceId": 1}, &task)
	if task != nil {
		asker, _ := modelUser.GetOneById(local.ISO_CODE, task.AskerId, bson.M{"emails": 1, "ignoreEmails": 1, "language": 1, "name": 1, "referralCode": 1})
		if asker != nil && asker.Emails != nil && len(asker.Emails) > 0 && asker.Emails[0].Verified && (asker.IgnoreEmails == nil || !asker.IgnoreEmails.TaskReceipt) {
			globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], task.XId, bson.M{"$set": bson.M{"emailSentReceipt": asker.Emails[0].Address}})
			emailSending, err := getCancelChargeFailEmail(req, task, asker)
			if err != nil {
				local.Logger.Error(lib.ERROR_CONTENT_NIL,
					zap.Error(err),
				)
				return &modelEmailResponse.EmailResponse{
					StatusCode: http.StatusBadRequest,
					Message:    lib.ERROR_CONTENT_NIL,
				}, err
			}
			return SendEmail(emailSending)
		}
	}
	return nil, nil
}

/*
* @Description: Get receipt email content by asker language
* @CreatedAt: 04/09/2020
* @Author: linhnh
* @UpdatedAt: 07/09/2020
* @UpdatedBy: linhnh
 */
func getCancelChargeFailEmail(req *modelEmailSending.EmailCancelFeeRequest, task *modelTask.Task, asker *modelUser.Users) (*modelEmailSending.EmailSending, error) {
	lang := globalConstant.LANG_EN
	if asker != nil && asker.Language != "" {
		lang = asker.Language
	}
	var service *modelService.Service
	globalDataAccess.GetOneById(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], task.ServiceId, bson.M{"text": 1}, &service)
	if service == nil {
		local.Logger.Warn(lib.ERROR_SERVICE_NOT_FOUND,
			zap.Any("body", req),
		)
		return nil, errors.New(lib.ERROR_SERVICE_NOT_FOUND)
	}
	serviceText := globalLib.LocalizeServiceName(lang, service.Text)
	var currencySign, currencyCode string
	if task.OriginCurrency != nil && task.OriginCurrency.Sign != "" {
		currencySign = task.OriginCurrency.Sign
	}
	if task.OriginCurrency != nil && task.OriginCurrency.Code != "" {
		currencyCode = task.OriginCurrency.Code
	} else if task.Currency != "" {
		currencyCode = task.Currency
	}
	cancelTaskLocal := globalLib.GetCurrentTime(local.TimeZone)
	cancelNoticeFree := "" +
		localization.T(lang, "CANCEL_TASK_NOTICE_FREE_TITLE_1") + " " +
		localization.T(lang, "CANCEL_TASK_NOTICE_FREE_TITLE_2") + " " +
		localization.T(lang, "CANCEL_TASK_NOTICE_FREE_TITLE_3")
	taskDateLocal := globalLib.ParseDateFromTimeStamp(task.Date, local.TimeZone)
	oneHourBeforeTaskDate := FormatTime(taskDateLocal.Add(-time.Hour))

	var cancellationReasonLocalize string
	if req.CancellationReason != globalConstant.CANCELLATION_REASON_ASKER_BUSY &&
		req.CancellationReason != globalConstant.CANCELLATION_REASON_POSTED_WRONG_DATE &&
		req.CancellationReason != globalConstant.CANCELLATION_REASON_TASKER_NOT_COME &&
		req.CancellationReason != globalConstant.CANCELLATION_REASON_ASKER_DONT_NEED_ANYMORE &&
		req.CancellationReason != globalConstant.CANCELLATION_REASON_TASK_WAS_EXPIRED &&
		req.CancellationReason != globalConstant.CANCELLATION_REASON_NO_TASKER_ACCEPT &&
		req.CancellationReason != globalConstant.CANCELLATION_REASON_TASKER_NOT_COME_WITH_ANNOUCEMENT &&
		req.CancellationReason != globalConstant.CANCELLATION_REASON_BACKEND_CANCEL {
		//Asker type the reason
		cancellationReasonLocalize = req.CancellationReason
	} else {
		//Asker choose a reason
		cancellationReasonLocalize = localization.T(lang, req.CancellationReason)
		if cancellationReasonLocalize == "" {
			cancellationReasonLocalize = req.CancellationReason
		}
	}

	// Get setting data
	var settings *modelSettings.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"cancelTaskFeeInPercentage": 1, "cancelTaskFee": 1, "referralSetting": 1}, &settings)
	if settings == nil {
		local.Logger.Warn(lib.ERROR_SETTING_NOT_FOUND,
			zap.Any("body", req),
		)
		return nil, errors.New(lib.ERROR_SETTING_NOT_FOUND)
	}

	emailData := map[string]interface{}{
		"ServiceName":                   serviceText,
		"CancelChargeFailContent":       localization.T(lang, "CHARGE_FAILED_EMAIL_CONTENT"),
		"CancelFeeTitle":                localization.T(lang, "CANCEL_EMAIL_CANCEL_FEE_TITLE"),
		"CancelFee":                     fmt.Sprintf("%s %s", globalLib.FormatMoney(req.CancelFee), currencyCode),
		"AskerNameTitle":                localization.T(lang, "CANCEL_EMAIL_ASKER_NAME_TITLE"),
		"AskerName":                     asker.Name,
		"PaymentMethodTitle":            localization.T(lang, "CANCEL_EMAIL_PAYMENT_METHOD_TITLE"),
		"PaymentMethod":                 globalLib.LocalizePaymentMethod(task.Payment.Method, lang),
		"CancelDetail":                  localization.T(lang, "EMAIL_TASK_CANCEL_DETAIL"),
		"TaskPlaceTitle":                localization.T(lang, "CANCEL_EMAIL_TASK_PLACE_TITLE"),
		"TaskPlace":                     task.Address,
		"ServiceNameTitle":              localization.T(lang, "CANCEL_EMAIL_SERVICE_TITLE"),
		"TaskDateTitle":                 localization.T(lang, "CANCEL_EMAIL_TASK_DATE_TITLE"),
		"TaskDate":                      FormatTime(taskDateLocal),
		"DurationTitle":                 localization.T(lang, "CANCEL_EMAIL_DURATION_TITLE"),
		"Duration":                      localization.T(lang, "CANCEL_EMAIL_DURATION", int(task.Duration)),
		"CreatedAtTitle":                localization.T(lang, "CANCEL_EMAIL_CREATED_AT_TITLE"),
		"CreatedAt":                     FormatTime(globalLib.ParseDateFromTimeStamp(task.CreatedAt, local.TimeZone)),
		"CanceledAtTitle":               localization.T(lang, "CANCEL_EMAIL_CANCELED_AT_TITLE"),
		"CanceledAt":                    FormatTime(cancelTaskLocal),
		"CancelationReasonTitle":        localization.T(lang, "CANCEL_EMAIL_CANCELATION_REASON_TITLE"),
		"CancelationReason":             cancellationReasonLocalize,
		"CancelatingFeeInformation":     localization.T(lang, "CANCEL_EMAIL_CANCELATING_FEE_INFORMATION"),
		"CancelNoticeFree":              cancelNoticeFree,
		"CancelNoticeFree1":             localization.T(lang, "CANCEL_TASK_NOTICE_FREE_CONDITION_1"),
		"CancelNoticeFree2":             localization.T(lang, "CANCEL_TASK_NOTICE_FREE_CONDITION_2"),
		"CancelNoticeFree3":             localization.T(lang, "CANCEL_TASK_NOTICE_FREE_CONDITION_3"),
		"CancelNoticeChargeFee":         localization.T(lang, "CANCEL_TASK_NOTICE_CHARGE_FEE_1"),
		"CancelNoticeChargeFee2":        localization.T(lang, "CANCEL_TASK_NOTICE_CHARGE_FEE_3", globalLib.FormatMoney(settings.CancelTaskFeeInPercentage), oneHourBeforeTaskDate),
		"FollowbTaskeeAt":               localization.T(lang, "FOLLOW_BTASKEE_AT"),
		"CancelTaskTetNoticeChargeFee":  localization.T(lang, "CANCEL_TASK_TET_NOTICE_CHARGE_FEE_1"),
		"CancelTaskTetNoticeChargeFee1": localization.T(lang, "CANCEL_TASK_TET_NOTICE_CHARGE_FEE_2"),
		"CancelTaskTetNoticeChargeFee2": localization.T(lang, "CANCEL_TASK_TET_NOTICE_CHARGE_FEE_3"),
	}

	if task.Payment.Method == globalConstant.PAYMENT_METHOD_CARD {
		emailData["IsPaymentByCard"] = true
		emailData["PaymentDetailTitle"] = localization.T(lang, "RECEIPT_EMAIL_PAYMENT_DETAIL")
		emailData["PaymentCardNumberTitle"] = localization.T(lang, "PAY_BY_CARD")
		cardNumber := task.Payment.CardNumber
		if cardNumber == "" && task.Payment.CardInfo != nil {
			cardNumber = task.Payment.CardInfo.Number
		}
		emailData["PaymentCardNumber"] = cardNumber
		emailData["PaymentStatusTitle"] = localization.T(lang, "CARD_PAYMENT_STATUS")
		emailData["PaymentStatus"] = localization.T(lang, "CARD_PAYMENT_STATUS_FAILED")
	} else if task.Payment.Method == globalConstant.PAYMENT_METHOD_BANK_TRANSFER {
		emailData["IsPaymentByTransfer"] = true
		emailData["PaymentDetailTitle"] = localization.T(lang, "RECEIPT_EMAIL_PAYMENT_DETAIL")
		emailData["PaymentBankNameTitle"] = localization.T(lang, "BANK_TRANSFER_METHOD")
		emailData["PaymentBankName"] = task.Payment.Bank
		emailData["PaymentStatusTitle"] = localization.T(lang, "CARD_PAYMENT_STATUS")
		emailData["PaymentStatus"] = localization.T(lang, "CARD_PAYMENT_STATUS_FAILED")
	}

	if lang == globalConstant.LANG_KO {
		emailData["CancelNoticeChargeFee1"] = localization.T(lang, "CANCEL_TASK_NOTICE_CHARGE_FEE_2", globalLib.FormatMoney(settings.CancelTaskFee)+currencySign, oneHourBeforeTaskDate)
	} else {
		emailData["CancelNoticeChargeFee1"] = localization.T(lang, "CANCEL_TASK_NOTICE_CHARGE_FEE_2", globalLib.FormatMoney(settings.CancelTaskFee), currencySign, oneHourBeforeTaskDate)
	}
	var isTetBooking bool
	if task.IsTetBooking {
		isTetBooking = true
	}
	emailData["IsTetBooking"] = isTetBooking
	// Check ivitation value
	referralValue, currencyCode := getReferralValueAndCurrency(task.IsoCode, currencyCode, settings)
	emailData["HasInvitation"] = true
	emailData["Invitation1"] = localization.T(lang, "INVITATION_1")
	emailData["Invitation2"] = localization.T(lang, "INVITATION_2")
	emailData["ReferralCode"] = asker.ReferralCode
	emailData["InvitationDescription"] = localization.T(lang, "INVITE_DESCRIPTION", globalLib.FormatMoney(referralValue), currencyCode, globalLib.FormatMoney(referralValue), currencyCode)
	emailData = globalLib.UpdateBtaskeeInfoInEmailByIsoCode(emailData, task.IsoCode)

	emailTempalte := fmt.Sprintf("%s/cancelChargeFailEmail.html", cfg.EmailTemplateURL)
	body, err := ParseTemplate(emailTempalte, emailData)

	if err != nil {
		return nil, err
	}

	// NOTE: Test write file with webhook-service "./test.sh 4 1"
	emailSending := &modelEmailSending.EmailSending{
		From: "bTaskee Receipts <<EMAIL>>",
		To:   asker.Emails[0].Address,
		// Bcc:     []string{"<EMAIL>"},
		Subject: localization.T(lang, "EMAIL_TASK_CANCEL_SUBJECT"),
		Content: body,
		ReplyTo: "<EMAIL>",
	}
	return emailSending, nil
}
