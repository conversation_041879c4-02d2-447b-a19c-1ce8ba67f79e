// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.7.1
// source: eventTH.proto

package grpcEventTH

import (
	empty "github.com/golang/protobuf/ptypes/empty"
	eventMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/eventMessage"
	users "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_eventTH_proto protoreflect.FileDescriptor

var file_eventTH_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x48, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0b, 0x67, 0x72, 0x70, 0x63, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x48, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x51, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f,
	0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d,
	0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x76, 0x65,
	0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2f, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x43, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65,
	0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f,
	0x75, 0x73, 0x65, 0x72, 0x73, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x32, 0xcd, 0x0f, 0x0a, 0x07, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x54, 0x48, 0x12, 0x6c, 0x0a,
	0x20, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54,
	0x61, 0x70, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x53, 0x75, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x12, 0x2e, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54,
	0x61, 0x70, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x71, 0x0a, 0x25, 0x54,
	0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70,
	0x50, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x45,
	0x72, 0x72, 0x6f, 0x72, 0x12, 0x2e, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76,
	0x65, 0x72, 0x54, 0x61, 0x70, 0x50, 0x61, 0x79, 0x43, 0x6f, 0x6d, 0x62, 0x6f, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x69,
	0x0a, 0x21, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72,
	0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x41, 0x63, 0x63, 0x65, 0x70, 0x74, 0x54,
	0x61, 0x73, 0x6b, 0x12, 0x2a, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65,
	0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x21, 0x54, 0x72, 0x61,
	0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61,
	0x73, 0x6b, 0x65, 0x72, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2a,
	0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54,
	0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x23, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x57,
	0x69, 0x74, 0x68, 0x64, 0x72, 0x61, 0x77, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x2a, 0x2e, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x00, 0x12, 0x67, 0x0a, 0x1f, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65,
	0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x44, 0x6f, 0x6e, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x12, 0x2a, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76,
	0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x1d, 0x54, 0x72,
	0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54,
	0x61, 0x73, 0x6b, 0x65, 0x72, 0x52, 0x61, 0x74, 0x69, 0x6e, 0x67, 0x12, 0x2c, 0x2e, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x52, 0x61, 0x74, 0x69,
	0x6e, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x00, 0x12, 0x71, 0x0a, 0x25, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43,
	0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x65, 0x72, 0x46, 0x69,
	0x6e, 0x69, 0x73, 0x68, 0x54, 0x72, 0x61, 0x69, 0x6e, 0x69, 0x6e, 0x67, 0x12, 0x2e, 0x2e, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x72, 0x61,
	0x69, 0x6e, 0x69, 0x6e, 0x67, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x87, 0x01, 0x0a, 0x2a, 0x54, 0x72, 0x61, 0x63, 0x6b,
	0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x50, 0x72, 0x65, 0x70,
	0x61, 0x79, 0x54, 0x61, 0x73, 0x6b, 0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65,
	0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x50, 0x72, 0x65, 0x70, 0x61, 0x79, 0x54, 0x61, 0x73, 0x6b,
	0x50, 0x61, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00,
	0x12, 0x49, 0x0a, 0x1f, 0x41, 0x64, 0x64, 0x41, 0x73, 0x6b, 0x65, 0x72, 0x44, 0x6f, 0x6e, 0x65,
	0x42, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x47,
	0x69, 0x66, 0x74, 0x12, 0x0c, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2e, 0x55, 0x73, 0x65, 0x72,
	0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x44, 0x0a, 0x1a, 0x41,
	0x64, 0x64, 0x41, 0x73, 0x6b, 0x65, 0x72, 0x53, 0x69, 0x67, 0x6e, 0x55, 0x70, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x61, 0x6c, 0x47, 0x69, 0x66, 0x74, 0x12, 0x0c, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x73, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x73, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x00, 0x12, 0x60, 0x0a, 0x22, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x55, 0x73, 0x65, 0x72,
	0x47, 0x61, 0x6d, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x20, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x25, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x55, 0x73,
	0x65, 0x72, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x20, 0x2e, 0x65,
	0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x1c, 0x54, 0x72, 0x61, 0x63,
	0x6b, 0x69, 0x6e, 0x67, 0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73,
	0x6b, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x64, 0x12, 0x2a, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x54, 0x72, 0x61, 0x63, 0x6b, 0x69, 0x6e, 0x67,
	0x43, 0x6c, 0x65, 0x76, 0x65, 0x72, 0x54, 0x61, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x63,
	0x0a, 0x25, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x55, 0x73, 0x65, 0x72, 0x47, 0x61, 0x6d,
	0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x61, 0x6c, 0x12, 0x20, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x00, 0x12, 0x6b, 0x0a, 0x2d, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x55, 0x73,
	0x65, 0x72, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x44, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x61, 0x6c, 0x12, 0x20, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4d,
	0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00,
	0x12, 0x6f, 0x0a, 0x25, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x55, 0x73, 0x65, 0x72, 0x47,
	0x61, 0x6d, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x6f, 0x6c, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x12, 0x20, 0x2e, 0x65, 0x76, 0x65, 0x6e,
	0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x22, 0x2e, 0x65, 0x76,
	0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74,
	0x52, 0x6f, 0x6c, 0x6c, 0x43, 0x61, 0x6c, 0x6c, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22,
	0x00, 0x12, 0x68, 0x0a, 0x2a, 0x50, 0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x55, 0x73, 0x65, 0x72,
	0x47, 0x61, 0x6d, 0x65, 0x43, 0x61, 0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x46, 0x69, 0x72, 0x73, 0x74, 0x12,
	0x20, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x45,
	0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x00, 0x12, 0x64, 0x0a, 0x26, 0x50,
	0x75, 0x62, 0x6c, 0x69, 0x73, 0x68, 0x55, 0x73, 0x65, 0x72, 0x47, 0x61, 0x6d, 0x65, 0x43, 0x61,
	0x6d, 0x70, 0x61, 0x69, 0x67, 0x6e, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x65, 0x77, 0x53,
	0x69, 0x67, 0x6e, 0x55, 0x70, 0x12, 0x20, 0x2e, 0x65, 0x76, 0x65, 0x6e, 0x74, 0x4d, 0x65, 0x73,
	0x73, 0x61, 0x67, 0x65, 0x2e, 0x45, 0x76, 0x65, 0x6e, 0x74, 0x43, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x00, 0x42, 0x40, 0x5a, 0x3e, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70,
	0x63, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x45, 0x76, 0x65, 0x6e,
	0x74, 0x54, 0x48, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_eventTH_proto_goTypes = []interface{}{
	(*eventMessage.TrackingCleverTapPayComboMessage)(nil),                  // 0: eventMessage.TrackingCleverTapPayComboMessage
	(*eventMessage.TrackingCleverTapTaskMessage)(nil),                      // 1: eventMessage.TrackingCleverTapTaskMessage
	(*eventMessage.TrackingCleverTapRatingMessage)(nil),                    // 2: eventMessage.TrackingCleverTapRatingMessage
	(*eventMessage.TrackingCleverTapTrainingMessage)(nil),                  // 3: eventMessage.TrackingCleverTapTrainingMessage
	(*eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage)(nil), // 4: eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage
	(*users.Users)(nil),                       // 5: users.Users
	(*eventMessage.EventCommonMessage)(nil),   // 6: eventMessage.EventCommonMessage
	(*empty.Empty)(nil),                       // 7: google.protobuf.Empty
	(*eventMessage.EventRollCallMessage)(nil), // 8: eventMessage.EventRollCallMessage
}
var file_eventTH_proto_depIdxs = []int32{
	0,  // 0: grpcEventTH.EventTH.TrackingCleverTapPayComboSuccess:input_type -> eventMessage.TrackingCleverTapPayComboMessage
	0,  // 1: grpcEventTH.EventTH.TrackingCleverTapPayComboPaymentError:input_type -> eventMessage.TrackingCleverTapPayComboMessage
	1,  // 2: grpcEventTH.EventTH.TrackingCleverTapTaskerAcceptTask:input_type -> eventMessage.TrackingCleverTapTaskMessage
	1,  // 3: grpcEventTH.EventTH.TrackingCleverTapTaskerCancelTask:input_type -> eventMessage.TrackingCleverTapTaskMessage
	1,  // 4: grpcEventTH.EventTH.TrackingCleverTapTaskerWithdrawTask:input_type -> eventMessage.TrackingCleverTapTaskMessage
	1,  // 5: grpcEventTH.EventTH.TrackingCleverTapTaskerDoneTask:input_type -> eventMessage.TrackingCleverTapTaskMessage
	2,  // 6: grpcEventTH.EventTH.TrackingCleverTapTaskerRating:input_type -> eventMessage.TrackingCleverTapRatingMessage
	3,  // 7: grpcEventTH.EventTH.TrackingCleverTapTaskerFinishTraining:input_type -> eventMessage.TrackingCleverTapTrainingMessage
	4,  // 8: grpcEventTH.EventTH.TrackingCleverTapPrepayTaskPaymentResponse:input_type -> eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage
	5,  // 9: grpcEventTH.EventTH.AddAskerDoneBookingReferralGift:input_type -> users.Users
	5,  // 10: grpcEventTH.EventTH.AddAskerSignUpReferralGift:input_type -> users.Users
	6,  // 11: grpcEventTH.EventTH.PublishUserGameCampaignActionLogin:input_type -> eventMessage.EventCommonMessage
	6,  // 12: grpcEventTH.EventTH.PublishUserGameCampaignActionDoneTask:input_type -> eventMessage.EventCommonMessage
	1,  // 13: grpcEventTH.EventTH.TrackingCleverTapTaskExpired:input_type -> eventMessage.TrackingCleverTapTaskMessage
	6,  // 14: grpcEventTH.EventTH.PublishUserGameCampaignActionReferral:input_type -> eventMessage.EventCommonMessage
	6,  // 15: grpcEventTH.EventTH.PublishUserGameCampaignActionDoneTaskReferral:input_type -> eventMessage.EventCommonMessage
	6,  // 16: grpcEventTH.EventTH.PublishUserGameCampaignActionRollCall:input_type -> eventMessage.EventCommonMessage
	6,  // 17: grpcEventTH.EventTH.PublishUserGameCampaignActionDoneTaskFirst:input_type -> eventMessage.EventCommonMessage
	6,  // 18: grpcEventTH.EventTH.PublishUserGameCampaignActionNewSignUp:input_type -> eventMessage.EventCommonMessage
	7,  // 19: grpcEventTH.EventTH.TrackingCleverTapPayComboSuccess:output_type -> google.protobuf.Empty
	7,  // 20: grpcEventTH.EventTH.TrackingCleverTapPayComboPaymentError:output_type -> google.protobuf.Empty
	7,  // 21: grpcEventTH.EventTH.TrackingCleverTapTaskerAcceptTask:output_type -> google.protobuf.Empty
	7,  // 22: grpcEventTH.EventTH.TrackingCleverTapTaskerCancelTask:output_type -> google.protobuf.Empty
	7,  // 23: grpcEventTH.EventTH.TrackingCleverTapTaskerWithdrawTask:output_type -> google.protobuf.Empty
	7,  // 24: grpcEventTH.EventTH.TrackingCleverTapTaskerDoneTask:output_type -> google.protobuf.Empty
	7,  // 25: grpcEventTH.EventTH.TrackingCleverTapTaskerRating:output_type -> google.protobuf.Empty
	7,  // 26: grpcEventTH.EventTH.TrackingCleverTapTaskerFinishTraining:output_type -> google.protobuf.Empty
	7,  // 27: grpcEventTH.EventTH.TrackingCleverTapPrepayTaskPaymentResponse:output_type -> google.protobuf.Empty
	7,  // 28: grpcEventTH.EventTH.AddAskerDoneBookingReferralGift:output_type -> google.protobuf.Empty
	7,  // 29: grpcEventTH.EventTH.AddAskerSignUpReferralGift:output_type -> google.protobuf.Empty
	7,  // 30: grpcEventTH.EventTH.PublishUserGameCampaignActionLogin:output_type -> google.protobuf.Empty
	7,  // 31: grpcEventTH.EventTH.PublishUserGameCampaignActionDoneTask:output_type -> google.protobuf.Empty
	7,  // 32: grpcEventTH.EventTH.TrackingCleverTapTaskExpired:output_type -> google.protobuf.Empty
	7,  // 33: grpcEventTH.EventTH.PublishUserGameCampaignActionReferral:output_type -> google.protobuf.Empty
	7,  // 34: grpcEventTH.EventTH.PublishUserGameCampaignActionDoneTaskReferral:output_type -> google.protobuf.Empty
	8,  // 35: grpcEventTH.EventTH.PublishUserGameCampaignActionRollCall:output_type -> eventMessage.EventRollCallMessage
	7,  // 36: grpcEventTH.EventTH.PublishUserGameCampaignActionDoneTaskFirst:output_type -> google.protobuf.Empty
	7,  // 37: grpcEventTH.EventTH.PublishUserGameCampaignActionNewSignUp:output_type -> google.protobuf.Empty
	19, // [19:38] is the sub-list for method output_type
	0,  // [0:19] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_eventTH_proto_init() }
func file_eventTH_proto_init() {
	if File_eventTH_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_eventTH_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_eventTH_proto_goTypes,
		DependencyIndexes: file_eventTH_proto_depIdxs,
	}.Build()
	File_eventTH_proto = out.File
	file_eventTH_proto_rawDesc = nil
	file_eventTH_proto_goTypes = nil
	file_eventTH_proto_depIdxs = nil
}
