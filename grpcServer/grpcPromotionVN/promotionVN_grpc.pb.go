// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcPromotionVN

import (
	context "context"
	promotionResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotionResponse"
	signUpPromotionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/signUpPromotionRequest"
	task "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PromotionVNClient is the client API for PromotionVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PromotionVNClient interface {
	CheckPostTaskPromotion(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error)
	CheckSignUpPromotion(ctx context.Context, in *signUpPromotionRequest.SignUpPromotionRequest, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error)
}

type promotionVNClient struct {
	cc grpc.ClientConnInterface
}

func NewPromotionVNClient(cc grpc.ClientConnInterface) PromotionVNClient {
	return &promotionVNClient{cc}
}

func (c *promotionVNClient) CheckPostTaskPromotion(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error) {
	out := new(promotionResponse.PromotionResponse)
	err := c.cc.Invoke(ctx, "/grpcPromotionVN.PromotionVN/CheckPostTaskPromotion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionVNClient) CheckSignUpPromotion(ctx context.Context, in *signUpPromotionRequest.SignUpPromotionRequest, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error) {
	out := new(promotionResponse.PromotionResponse)
	err := c.cc.Invoke(ctx, "/grpcPromotionVN.PromotionVN/CheckSignUpPromotion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PromotionVNServer is the server API for PromotionVN service.
// All implementations must embed UnimplementedPromotionVNServer
// for forward compatibility
type PromotionVNServer interface {
	CheckPostTaskPromotion(context.Context, *task.Task) (*promotionResponse.PromotionResponse, error)
	CheckSignUpPromotion(context.Context, *signUpPromotionRequest.SignUpPromotionRequest) (*promotionResponse.PromotionResponse, error)
	mustEmbedUnimplementedPromotionVNServer()
}

// UnimplementedPromotionVNServer must be embedded to have forward compatible implementations.
type UnimplementedPromotionVNServer struct {
}

func (UnimplementedPromotionVNServer) CheckPostTaskPromotion(context.Context, *task.Task) (*promotionResponse.PromotionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPostTaskPromotion not implemented")
}
func (UnimplementedPromotionVNServer) CheckSignUpPromotion(context.Context, *signUpPromotionRequest.SignUpPromotionRequest) (*promotionResponse.PromotionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckSignUpPromotion not implemented")
}
func (UnimplementedPromotionVNServer) mustEmbedUnimplementedPromotionVNServer() {}

// UnsafePromotionVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PromotionVNServer will
// result in compilation errors.
type UnsafePromotionVNServer interface {
	mustEmbedUnimplementedPromotionVNServer()
}

func RegisterPromotionVNServer(s *grpc.Server, srv PromotionVNServer) {
	s.RegisterService(&_PromotionVN_serviceDesc, srv)
}

func _PromotionVN_CheckPostTaskPromotion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(task.Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionVNServer).CheckPostTaskPromotion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPromotionVN.PromotionVN/CheckPostTaskPromotion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionVNServer).CheckPostTaskPromotion(ctx, req.(*task.Task))
	}
	return interceptor(ctx, in, info, handler)
}

func _PromotionVN_CheckSignUpPromotion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(signUpPromotionRequest.SignUpPromotionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionVNServer).CheckSignUpPromotion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPromotionVN.PromotionVN/CheckSignUpPromotion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionVNServer).CheckSignUpPromotion(ctx, req.(*signUpPromotionRequest.SignUpPromotionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PromotionVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcPromotionVN.PromotionVN",
	HandlerType: (*PromotionVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckPostTaskPromotion",
			Handler:    _PromotionVN_CheckPostTaskPromotion_Handler,
		},
		{
			MethodName: "CheckSignUpPromotion",
			Handler:    _PromotionVN_CheckSignUpPromotion_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "promotionVN.proto",
}
