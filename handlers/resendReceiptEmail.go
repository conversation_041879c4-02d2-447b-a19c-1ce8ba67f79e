package handlers

import (
	"fmt"
	"net/http"
	"time"

	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	modelFAtransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fatransaction"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func ResendReceiptEmail(reqBody *modelEmailSending.EmailRequest) (*modelEmailResponse.EmailResponse, error) {
	defer local.Logger.Sync()

	// Check server config
	resErr := CheckEmailConfig()
	if resErr != nil {
		local.Logger.Warn(resErr.Message)
		return resErr, nil
	}
	if reqBody.TaskId == "" {
		local.Logger.Warn(lib.ERROR_BOOKING_ID_REQUIRED,
			zap.Any("body", reqBody),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_BOOKING_ID_REQUIRED,
		}, nil
	}

	var task *modelTask.Task
	fields := bson.M{
		"_id":            1,
		"askerId":        1,
		"status":         1,
		"isoCode":        1,
		"originCurrency": 1,
		"address":        1,
		"createdAt":      1,
		"date":           1,
	}
	globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], reqBody.TaskId, fields, &task)
	if task == nil {
		local.Logger.Warn(lib.ERROR_BOOKING_NOT_FOUND,
			zap.Any("body", reqBody),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusNotFound,
			Message:    lib.ERROR_BOOKING_NOT_FOUND,
		}, nil
	}

	asker, _ := modelUser.GetOneById(local.ISO_CODE, task.AskerId, bson.M{"emails": 1, "language": 1, "name": 1, "referralCode": 1})
	if asker == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND,
			zap.Any("body", reqBody),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusNotFound,
			Message:    lib.ERROR_USER_NOT_FOUND,
		}, nil
	}

	if len(asker.Emails) == 0 || !asker.Emails[0].Verified {
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusOK,
		}, nil
	}

	if task.Status == globalConstant.TASK_STATUS_DONE {
		return SendReceiptEmail(task)
	}

	if task.Status == globalConstant.TASK_STATUS_CANCELED {
		var transaction *modelFAtransaction.FinancialAccountTransaction
		var setting *modelSettings.Settings
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], bson.M{"source.name": "CANCEL TASK", "source.value": task.XId}, bson.M{}, &transaction)
		globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"referralSetting": 1, "cancelTaskFee": 1, "cancelTaskFeeInPercentage": 1}, &setting)
		if transaction != nil {
			sendCancellationFeeEmail(asker, task, transaction, setting)
			return &modelEmailResponse.EmailResponse{
				StatusCode: http.StatusOK,
			}, nil
		}
	}

	return &modelEmailResponse.EmailResponse{
		StatusCode: http.StatusBadRequest,
		Message:    lib.ERROR_EMAIL_RECEIPT_TASK_EMPTY,
	}, nil

}

func sendCancellationFeeEmail(asker *modelUser.Users, task *modelTask.Task, transaction *modelFAtransaction.FinancialAccountTransaction, setting *modelSettings.Settings) {
	lang := globalConstant.LANG_EN
	if asker.Language != "" {
		lang = asker.Language
	}
	var currencyCode string
	if task != nil && task.OriginCurrency != nil {
		currencyCode = task.OriginCurrency.Code
	}
	var html = ``
	html += `<html>`
	html += `<head>`
	html += `</head>`
	html += `<body style="font-family: Roboto, sans-serif; font-size: 15px;">`
	html += `<div style="width: 90%; margin: 0 auto; padding: 20px">`
	html += `<div style="text-align: right; color: #ff611c; font-size: 35px; font-weight: bold;">bTaskee</div>`
	html += `<div style="color: black; margin-top: 20px;">` + localization.T(lang, "RECEIPT_EMAIL_HELLO", asker.Name) + `</div>`
	html += `<br/>`
	html += `<div style="line-height: 25px">`
	html += `<div>`
	html += `<div style="color: black;">` + localization.T(lang, "EMAIL_TASK_CANCEL_PLACE", task.Address) + `</div>`
	html += `<div style="color: black; margin-top: 20px">` + localization.T(lang, "CANCEL_TASK_NOTICE_FREE_TITLE_1") +
		` ` + localization.T(lang, "CANCEL_TASK_NOTICE_FREE_TITLE_2") +
		` ` + localization.T(lang, "CANCEL_TASK_NOTICE_FREE_TITLE_3") +
		`</div>`
	html += `<div style="color: black; margin-top: 20px">` + localization.T(lang, "CANCEL_TASK_NOTICE_FREE_CONDITION_1") + `</div>`
	html += `<div style="color: black; margin-top: 10px">` + localization.T(lang, "CANCEL_TASK_NOTICE_FREE_CONDITION_2") + `</div>`
	html += `<div style="color: black; margin-top: 10px">` + localization.T(lang, "CANCEL_TASK_NOTICE_FREE_CONDITION_3") + `</div>`
	html += `<div style="color: black; margin-top: 20px">` + localization.T(lang, "EMAIL_TASK_CANCEL_FEE_1") + `</div>`
	cancellationMoney := 0.0
	if setting.CancelTaskFee != 0.0 {
		cancellationMoney = setting.CancelTaskFee
	}
	html += `<div style="color: black; margin-top: 20px">` + localization.T(lang, "EMAIL_TASK_CANCEL_FEE_2", cancellationMoney, currencyCode) + `</div>`

	cancellationPercent := 0.0
	if setting.CancelTaskFeeInPercentage != 0.0 {
		cancellationPercent = setting.CancelTaskFeeInPercentage
	}
	html += `<div style="color: black; margin-top: 20px">` + localization.T(lang, "EMAIL_TASK_CANCEL_FEE_3", cancellationPercent) + `</div>`
	html += `</div>`
	html += `<br>`
	html += `<hr style="border-left-style: none">`
	html += `<div style="padding: 0 10px 0 10px;">`
	html += `<div style="color: #ff611c;">` + localization.T(lang, "EMAIL_TASK_CANCEL_DETAIL") + `</div>`
	html += `<div style="color: black; margin-top: 10px">` + localization.T(lang, "EMAIL_TASK_CANCEL_CREATED_AT", FormatTime(globalLib.ParseDateFromTimeStamp(task.CreatedAt, local.TimeZone))) + `</div>`
	html += `<div style="color: black; margin-top: 10px">` + localization.T(lang, "EMAIL_TASK_CANCEL_TASK_DATE", FormatTime(globalLib.ParseDateFromTimeStamp(task.Date, local.TimeZone))) + `</div>`
	html += `<div style="color: black; margin-top: 10px">` + localization.T(lang, "EMAIL_TASK_CANCEL_CANCEL_AT", FormatTime(globalLib.ParseDateFromTimeStamp(transaction.Date, local.TimeZone))) + `</div>`
	html += `<div style="font-weight: bold; color: black; margin-top: 10px">` + localization.T(lang, "EMAIL_TASK_CANCEL_FEE_FOR_THIS_CANCELLATION", globalLib.FormatMoney(transaction.Amount), currencyCode) + `</div>`
	html += `</div>`
	html += `<hr style="border-left-style: none">`
	html += `<div style="text-align: center; line-height: 25px; color: #ff611c; margin-top: 30px">`
	html += `<div>`
	html += localization.T(lang, "RECEIPT_EMAIL_REFERRAL")
	html += `</div>`
	html += `<div style="font-size: 24px; color: #5cb85c">`
	html += asker.ReferralCode
	html += `</div>`
	html += `<div>`

	referralValue, currencyCode := getReferralValueAndCurrency(task.IsoCode, currencyCode, setting)
	html += localization.T(lang, "INVITE_DESCRIPTION", globalLib.FormatMoney(referralValue), currencyCode, globalLib.FormatMoney(referralValue), currencyCode)

	html += `</div>`
	// html += `<a href="https://www.facebook.com/dialog/share?app_id=` + Meteor.settings.socialConfig.facebook.appId + `&display=popup&href=https://btaskee.com/download&redirect_uri=https://btaskee.com/"><img style="width: 35px; border-radius: 5px;" src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/PoPQfoY5mTWNuFpiS"/></a>`
	html += `</div>`
	html += `<br>`
	html += `<div style="color: #ff611c; font-size: 16px; font-weight: bold;">bTaskee</div>`
	html += `<div>` + localization.T(lang, "VERIFY_EMAIL_CONTENT_5") + `</div>`
	html += `<a href="https://www.facebook.com/btaskee"><img src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/ftBfYvf2zZ9JxxTCK" style="width: 36px; border-radius: 50%; margin: 10px"/></a>`
	html += `<a href="https://www.instagram.com/btaskee"><img src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/sQm7nkNNogBGZG5Ap" style="width: 36px; border-radius: 10px; margin: 10px"/></a>`
	html += `<a href="https://twitter.com/btaskee"><img src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/Nei5WAg3SSuyTtDij" style="width: 36px; border-radius: 50%; margin: 10px"/></a>`
	html += `<a href="https://www.youtube.com/channel/UC55F4JRrI9ewVpoR6DOMfkQ"><img src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/TjfaipCvFKrZvz7gQ" style="width: 50px; border-radius: 50%; margin: 0"/></a>`
	html += `</div>`
	html += `</div>`
	html += `</body>`
	html += `</html>`

	emailOption := &modelEmailSending.EmailSending{
		ReplyTo: "<EMAIL>",
		From:    "bTaskee Receipts <<EMAIL>>",
		To:      asker.Emails[0].Address,
		Bcc:     []string{"<EMAIL>"},
		Subject: localization.T(lang, "EMAIL_TASK_CANCEL_SUBJECT"),
		Content: html,
	}

	SendEmail(emailOption)
}

/*
 * @Description: Format time.Time to string (hh:mm dd/mm/yyyy)
 * @CreatedAt: 05/03/2020
 * @Author: linhnh
 */
func FormatTime(srcTime time.Time) string {
	hour := fmt.Sprintf("%d", srcTime.Hour())
	minute := fmt.Sprintf("%d", srcTime.Minute())
	day := fmt.Sprintf("%d", srcTime.Day())
	month := fmt.Sprintf("%d", srcTime.Month())
	year := fmt.Sprintf("%d", srcTime.Year())
	if len(hour) == 1 {
		hour = "0" + hour
	}
	if len(minute) == 1 {
		minute = "0" + minute
	}
	if len(day) == 1 {
		day = "0" + day
	}
	if len(month) == 1 {
		month = "0" + month
	}

	return hour + ":" + minute + " " + day + "/" + month + "/" + year
}
