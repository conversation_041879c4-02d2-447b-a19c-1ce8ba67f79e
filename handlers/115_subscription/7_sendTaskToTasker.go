package subscription

import (
	"context"
	"fmt"
	"log"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcSendTaskVN"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelPushNotificationNewTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask"
)

// sendTaskToTasker sends a task to available taskers via gRPC - STEP 7: Task Assignment
func sendTaskToTasker(taskID, serviceID string, favouriteTasker []string) {
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("Recovered from panic in sendTaskToTasker: %v", r)
			}
		}()

		// Connect to gRPC send task service
		client, connect, err := grpcSendTaskVN.ConnectGRPCSendTaskVN(cfg.GRPC_Send_Task_URL)
		if err != nil {
			globalLib.PostToSlack(
				cfg.SlackToken,
				globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE],
				globalConstant.SLACK_USER_NAME,
				fmt.Sprintf("[Sync cron vn v3] Error connecting to send task gRPC service: taskId=%s, error=%v", taskID, err),
			)
			return
		}
		defer connect.Close()

		// Create request
		request := &modelPushNotificationNewTask.NewTaskRequest{
			Service: &modelPushNotificationNewTask.NewTaskRequestService{
				XId: serviceID,
			},
			Booking: &modelPushNotificationNewTask.NewTaskRequestBooking{
				XId:     taskID,
				IsoCode: local.ISO_CODE,
			},
			FavouriteTasker: favouriteTasker,
		}

		// Create context with timeout
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		// Call NewTask gRPC method
		resp, err := client.NewTask(ctx, request)
		if err != nil {
			globalLib.PostToSlack(
				cfg.SlackToken,
				globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE],
				globalConstant.SLACK_USER_NAME,
				fmt.Sprintf("[Sync cron vn v3] Error calling NewTask gRPC: taskId=%s, error=%v", taskID, err),
			)
			return
		}

		// Check response
		if resp != nil && resp.Error != nil {
			globalLib.PostToSlack(
				cfg.SlackToken,
				globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE],
				globalConstant.SLACK_USER_NAME,
				fmt.Sprintf("[Sync cron vn v3] NewTask gRPC error: taskId=%s, error=%s", taskID, resp.Error.Code),
			)
			return
		}
	}()
}
