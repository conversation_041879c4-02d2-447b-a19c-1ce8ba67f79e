package grpcPricingIndo

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPricingIndo"
	"google.golang.org/grpc"
)

func ConnectGRPCPricing(endpoint string) (grpcPricingIndo.PricingIndoClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPricingIndo.NewPricingIndoClient(connect)
	return client, connect, err
}
