package grpcSMSIndo

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcSMSIndo"
	"google.golang.org/grpc"
)

func ConnectGRPCSMSIndo(endpoint string) (grpcSMSIndo.SMSClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcSMSIndo.NewSMSClient(connect)
	return client, connect, err
}
