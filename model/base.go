package model

import (
	"time"

	modelJourneySetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/journeySetting"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTaskerWorkingHistory "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerWorkingHistory"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
)

type RequestAction struct {
	Action                       int        `json:"action,omitempty"`
	FromDate                     *time.Time `json:"fromDate,omitempty"`
	ToDate                       *time.Time `json:"toDate,omitempty"`
	Date                         *time.Time `json:"date,omitempty"`
	IsoCode                      string     `json:"isoCode,omitempty"`
	RevokePremiumAction          int        `json:"revokePremiumAction,omitempty"`
	PaymentMethod                string     `json:"paymentMethod,omitempty"`
	TransactionIds               []string   `json:"transactionIds,omitempty"`
	PhaseResetPoint              int32      `json:"phaseResetPoint,omitempty"`
	DateStartCalculatePointAdded *time.Time `json:"dateStartCalculatePointAdded,omitempty"`
	DateResetPoint               *time.Time `json:"dateResetPoint,omitempty"`
	DateCalculatePoint           *time.Time `json:"dateCalculatePoint,omitempty"`
	NotRunUsers                  []string   `json:"notRunUsers,omitempty"`
	RunUsers                     []string   `json:"runUsers,omitempty"`
	Month                        int        `json:"month,omitempty"`
	Year                         int        `json:"year,omitempty"`
	UserType                     string     `json:"userType,omitempty"`
	TaskIds                      []string   `json:"taskIds,omitempty"`
}

type ViolateInfo struct {
	Reason        string
	Date          time.Time
	Tasker        *modelUser.Users
	ViolateData   interface{}
	TaskerWorking *modelTaskerWorkingHistory.TaskerWorkingHistoryTaskers
}

type UserPoint struct {
	User           *modelUser.Users
	LastPhasePoint float64
}

type TaskerJourneyInfo struct {
	Tasker         *modelUser.Users
	NewLevel       *modelUser.UserJourneyInfo
	OldLevel       *modelUser.UserJourneyInfo
	JourneySetting *modelJourneySetting.JourneySetting
}

type TaskerBonusInfo struct {
	Tasker      *modelUser.Users
	BonusDetail *TaskerBonusInfoBonusDetail
}

type TaskerBonusInfoBonusDetail struct {
	LevelName string
	Name      string
	Text      *modelService.ServiceText
	BPoint    float64
}

type NotiNewTestAndReviewAndDeadlineBody struct {
	CourseId    string
	CourseTitle string
	CourseType  string
	TaskerIds   []string
}
