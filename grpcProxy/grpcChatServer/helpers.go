package grpcChatServer

import (
	"context"
	"encoding/json"

	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/kafkaEvent"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage"
	"go.uber.org/zap"
)

// Send socket message with language
// Required: isoCode, endpoint, chatId, messageTo, message
// Optional: language (in case html message), task (in case update task, required task.cost, task.originCurrency, task.subscriptionId)
func SendSocketChatMessage(isoCode, language, endpoint, chatId string, receivers []*kafkaEvent.ConversationMessageReceiver, message *modelChatMessage.ChatMessageMessages, task *modelTask.Task) (*websocketMessage.WebsocketMessage, error) {
	messageMap := chatMessage.MapChatMessageMessageForTaskerByLanguage(language, message, task)
	t := globalResponse.Translate(messageMap)
	data, _ := json.Marshal(t)
	request := &websocketMessage.WebsocketMessage{
		Key:       globalConstant.WEBSOCKET_KEY_CHAT,
		Value:     chatId,
		Receivers: receivers,
		IsoCode:   isoCode,
		Data:      data,
	}

	client, connect, err := Connect(isoCode, endpoint)
	if err != nil {
		globalLib.Logger.Error("WEBSOCKET_CHAT_SERVER_ERROR",
			zap.Error(err),
			zap.String("endpoint", endpoint),
			zap.Any("receivers", receivers),
			zap.Any("data", data),
		)
		return request, err
	}
	defer connect.Close()
	_, err = client.SendSocketChatMessage(context.Background(), request)
	if err != nil {
		globalLib.Logger.Error("WEBSOCKET_CHAT_SERVER_ERROR",
			zap.Error(err),
			zap.Any("body", request),
		)
		return request, err
	}
	return request, nil
}

// Send socket message should be updated
// Required: isoCode, endpoint, chatId, messageTo, updateInfo
// updateInfo: map[string]interface{}{"key.level1.level2...": "value"}
func SendSocketUpdateChatMessage(isoCode, endpoint, chatId string, receivers []*kafkaEvent.ConversationMessageReceiver, updateInfo map[string]interface{}) (*websocketMessage.WebsocketMessage, error) {
	data, _ := json.Marshal(updateInfo)
	request := &websocketMessage.WebsocketMessage{
		Key:       globalConstant.WEBSOCKET_KEY_CHAT,
		Value:     chatId,
		Data:      data,
		Receivers: receivers,
		IsoCode:   isoCode,
		Action:    globalConstant.CHAT_MESSAGE_ACTION_UPDATE_CHAT_MESSAGE,
	}

	client, connect, err := Connect(isoCode, endpoint)
	if err != nil {
		globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
			zap.Error(err),
			zap.String("endpoint", endpoint),
			zap.Any("receivers", receivers),
			zap.Any("data", data),
		)
		return request, err
	}
	defer connect.Close()

	_, err = client.SendSocketChatMessage(context.Background(), request)
	if err != nil {
		globalLib.Logger.Error("WEBSOCKET_TASK_ERROR",
			zap.Error(err),
			zap.Any("body", request),
		)
		return request, err
	}
	return request, nil
}
