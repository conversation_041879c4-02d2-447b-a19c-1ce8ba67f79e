package voucherComboSubscriptionNotify

import (
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucher"
	modelUserComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userComboVoucher"
	"go.mongodb.org/mongo-driver/bson"
)

func getUsersToNotify() (userIds []string, mapUserComboVouchers map[string][]*modelUserComboVoucher.UserComboVoucher, mapComboVouchers map[string]*modelComboVoucher.ComboVoucher) {
	userIds = []string{}
	now := globalLib.GetCurrentTime(local.TimeZone)
	// check in day 3 from now
	timeToCheck := now.AddDate(0, 0, 3)
	startTime, endTime := globalLib.StartADay(timeToCheck), globalLib.EndADay(timeToCheck)

	var userComboVouchers []*modelUserComboVoucher.UserComboVoucher
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_USER_COMBO_VOUCHER[local.ISO_CODE],
		bson.M{
			"expiredDate":    bson.M{"$gte": startTime, "$lte": endTime.Add(1 * time.Minute)},
			"isSubscription": true,
			"status":         globalConstant.USER_COMBO_VOUCHER_STATUS_ACTIVE,
			"$or": []bson.M{
				{"isCancelled": false},
				{"isCancelled": bson.M{"$exists": false}},
			},
		},
		bson.M{"userId": 1, "comboVoucherId": 1},
		bson.M{"createdAt": 1}, &userComboVouchers)

	mapComboVouchers = getMapComboVoucherById(userComboVouchers)

	mapUserComboVouchers = map[string][]*modelUserComboVoucher.UserComboVoucher{}
	mapUserCost := map[string]float64{}
	for _, userComboVoucher := range userComboVouchers {
		comboVoucher := mapComboVouchers[userComboVoucher.ComboVoucherId]
		if comboVoucher == nil {
			continue
		}

		if comboVoucher.Price > 0 {
			mapUserCost[userComboVoucher.UserId] += comboVoucher.Price
			enoughMoney, errCode := lib.CheckEnoughMoney(userComboVoucher.UserId, mapUserCost[userComboVoucher.UserId])
			if errCode != nil {
				continue
			}

			if !enoughMoney["enough"].(bool) {
				userIds = append(userIds, userComboVoucher.UserId)
				mapUserComboVouchers[userComboVoucher.UserId] = append(mapUserComboVouchers[userComboVoucher.UserId], userComboVoucher)
			}
		}

	}

	return
}

func getMapComboVoucherById(userComboVouchers []*modelUserComboVoucher.UserComboVoucher) map[string]*modelComboVoucher.ComboVoucher {
	comboVoucherIds := []string{}
	for _, userComboVoucher := range userComboVouchers {
		comboVoucherIds = append(comboVoucherIds, userComboVoucher.ComboVoucherId)
	}
	var comboVouchers []*modelComboVoucher.ComboVoucher
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_COMBO_VOUCHER[local.ISO_CODE],
		bson.M{"_id": bson.M{"$in": comboVoucherIds}},
		bson.M{"price": 1, "title": 1}, &comboVouchers)
	mapComboVouchers := map[string]*modelComboVoucher.ComboVoucher{}
	for _, comboVoucher := range comboVouchers {
		mapComboVouchers[comboVoucher.XId] = comboVoucher
	}

	return mapComboVouchers
}
