import { Meteor } from 'meteor/meteor';
import { Accounts } from 'meteor/accounts-base';
import { Setting, History } from '../lib/collections.js';
import '../lib/i18n/vi.i18n.json';
import '../lib/i18n/en.i18n.json';
import '../lib/i18n/ko.i18n.json';
import '../lib/i18n/th.i18n.json';

  const setting = Setting.findOne({});
  if (!setting) { // init setting
    Setting.insert({
      isRunSchedule: Meteor.settings.RUN_SYNCEDCRON_SCHEDULY,
      scheduleRange: Meteor.settings.SCHEDULE_RANGE,
      runEvery: Meteor.settings.SCHEDULE_RUN_EVERY,
      forceAcceptSchedule: false,
    });
  } else {
    if (!setting.suggestSubscription) {
      Setting.update({}, { $set: {
        suggestSubscription: {
          favoriteTasker: true,
          haveSchedule: false,
        }
      } });
    }
  }
  SyncedCron.config({
    logger: (opts) => {
      History.insert({
        action: opts.message,
        createdAt: new Date(),
        data: opts,
      });
    }
  });
// Setting up admin account if none exist
  const admin = Meteor.users.findOne({username: Meteor.settings.DEFAULT.ADMIN_USERNAME});
  if (!admin) {
    Accounts.createUser({
      username: Meteor.settings.DEFAULT.ADMIN_USERNAME,
      password: Meteor.settings.DEFAULT.ADMIN_PASSWORD,
      email: Meteor.settings.DEFAULT.ADMIN_EMAIL,
    });
  }
