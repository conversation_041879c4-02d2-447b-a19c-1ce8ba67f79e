package notifyTaskerGameCampaign

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/gameCampaign"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelUserGameCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userGameCampaign"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func getDataToNotify() (userIds []string, title, description *service.ServiceText) {
	userIds = []string{}
	now := globalLib.GetCurrentTime(local.TimeZone)

	query := bson.M{
		"endDate": bson.M{"$gte": now},
		"status":  globalConstant.GAME_CAMPAIGN_STATUS_ACTIVE,
	}
	fields := bson.M{
		"_id":     1,
		"endDate": 1,
	}
	var gameCampaign *modelGameCampaign.GameCampaign
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_GAME_CAMPAIGN[local.ISO_CODE], query, fields, &gameCampaign)
	if gameCampaign == nil {
		return
	}

	startToDay := globalLib.StartADay(now)
	endGameCampaignDate := globalLib.StartADay(globalLib.ParseDateFromTimeStamp(gameCampaign.EndDate, local.TimeZone))
	dayRemain := int(endGameCampaignDate.Sub(startToDay).Hours() / 24)

	if dayRemain != 1 && dayRemain != 3 {
		return
	}

	var usersJoinCampaign []*modelUserGameCampaign.UserGameCampaign
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASKER_USER_GAME_CAMPAIGN[local.ISO_CODE],
		bson.M{
			"gameCampaignId": gameCampaign.XId,
			"isUnlockedSpin": true,
		},
		bson.M{
			"_id":    1,
			"userId": 1,
		},
		&usersJoinCampaign,
	)
	for _, user := range usersJoinCampaign {
		userIds = append(userIds, user.UserId)
	}

	title = localization.GetLocalizeObject("LUCKY_DRAW_NOTIFICATION_END_DATE", dayRemain)
	description = localization.GetLocalizeObject("LUCKY_DRAW_NOTIFICATION_END_DATE", dayRemain)

	return
}
