package createTaskerMonthlyReport

import (
	"fmt"
	"strings"
	"sync"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelRating "gitlab.com/btaskee/go-services-model-v2/grpcmodel/rating"
	"go.mongodb.org/mongo-driver/bson"
)

// getReviewReport returns a channel of TaskerYearEndReport objects.
// It takes in a WaitGroup pointer, a channel of TaskerYearEndReport objects,
// and startYear and endYear time.Time values as parameters.
// The returned channel contains TaskerYearEndReport objects with updated
// TotalBPoint and TotalMonthlyRewardAmount values.
func getReviewReport(w *sync.WaitGroup, taskCh chan *modelReport, startAMonth, endAMonth time.Time) chan *modelReport {
	// Create a channel to store the updated TaskerYearEndReport objects
	outC := make(chan *modelReport, 100)

	// Start a goroutine to process the TaskerYearEndReport objects
	go func() {
		defer w.Done()
		// Close channel khi channel taskCh đã đóng
		defer close(outC)
		// Đọc data từ channel (taskerReportData)
		for rp := range taskCh {
			// If normal tasker and doesn't have task done last month - 1, skip
			if !rp.IsCompanyReport && rp.LastDoneTask.Before(startAMonth.AddDate(0, 0, -8)) {
				// Đưa data vào trong channel
				outC <- rp
				continue
			}
			reviews := getReviews(rp, startAMonth, endAMonth)
			if rp.MonthlyReportInfo != nil {
				// Ghi data vào channel mới với thêm data field reviews
				rp.MonthlyReportInfo.Reviews = reviews
				// Count good rating in year
				rp.MonthlyReportInfo.NumberOfGoodRating = countGoodRating(rp, startAMonth, endAMonth)
			}
			// Đưa data report đầy đủ vào channel
			outC <- rp
		}
	}()

	// Return the output channel
	return outC
}

func getReviews(report *modelReport, startAMonth, endAMonth time.Time) []string {
	taskerIds := []string{report.TaskerId}
	if report.IsCompanyReport {
		taskerIds = report.EmployeeIds
	}
	return getReviewsByTaskerId(taskerIds, startAMonth, endAMonth)
}

// countGoodRating counts the number of good ratings for a given tasker within a specified time range.
// It takes the taskerId, startAMonth, endAMonth as input parameters and returns the count as a float64.
func countGoodRating(report *modelReport, startAMonth, endAMonth time.Time) float64 {
	// Define the query to filter ratings based on taskerId, isoCode, rate, and createdAt.
	query := bson.M{
		"taskerId":  report.TaskerId,
		"rate":      5,
		"createdAt": bson.M{"$gte": startAMonth, "$lte": endAMonth},
	}
	if report.IsCompanyReport {
		query["taskerId"] = bson.M{"$in": report.EmployeeIds}
	}

	// Count the number of ratings that match the query.
	numberGoodRating, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_RATING[local.ISO_CODE], query)
	// Convert the count to float64 and return it.
	return float64(numberGoodRating)
}

/*
 * @Description: Get list review of tasker id
 * @CreatedAt: 28/12/2021
 * @Author: vinhnt
 */
func getReviewsByTaskerId(taskerIds []string, startYear, endYear time.Time) []string {
	var ratings []*modelRating.Rating
	query := bson.M{
		"taskerId":  bson.M{"$in": taskerIds}, // Có index
		"rate":      5,
		"review":    bson.M{"$exists": true, "$ne": ""},
		"createdAt": bson.M{"$gte": startYear, "$lte": endYear},
	}
	if len(taskerIds) == 1 {
		query["taskerId"] = taskerIds[0]
	}
	err := globalDataAccess.GetAllByQueryPaging(globalCollection.COLLECTION_RATING[local.ISO_CODE],
		query,
		bson.M{"review": 1},
		1,
		20, // Giảm limit xuống vì cũng kg cần thiết lấy 50 để random 20 review | bỏ sort vì cũng không cần thiết
		&ratings,
	)
	if err != nil {
		msg := fmt.Sprintf("CreateTaskerMonthlyReportVN Get data ratings for tasker report - taskerIds:%v error: %v", strings.Join(taskerIds, ","), err)
		globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
	}
	var reviews []string
	// Get all reviews
	for _, v := range ratings {
		reviews = append(reviews, v.Review)
	}
	return reviews
}
