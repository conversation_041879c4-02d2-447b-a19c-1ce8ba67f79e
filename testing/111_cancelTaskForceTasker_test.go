package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	modelPromotionHistory "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotionhistory"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func TestCancelTaskForceTasker(t *testing.T) {
	// Check case cancel task force tasker
	t.Run("1", func(t *testing.T) {
		ResetData()
		apiURL := apiSyncCron + "/cancel-task-force-tasker"

		CreateUser([]map[string]interface{}{
			{
				"phone":     "0823456720",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 0",
			},
			{
				"phone":     "0823456721",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 1",
			},
			{
				"phone":     "0823456728",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker",
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		date := now.AddDate(0, 0, 3)
		createdAtTime := now.Add(-7 * time.Hour)
		taskIds := CreateTask([]map[string]interface{}{
			{ // 0. normalTask + date -> EXPIRED
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"forceTasker": map[string]interface{}{
					"taskerId": "0823456728",
					"name":     "Tasker",
				},
			},
			{ // 1. normalTask + dateOptions -> EXPIRED
				"askerPhone":  "0823456721",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour()+4, date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"dateOptions": []map[string]interface{}{
					{
						"date": time.Date(date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute(), 0, 0, local.TimeZone),
					},
					{
						"date": time.Date(date.Year(), date.Month(), date.Day(), date.Hour()+2, date.Minute(), 0, 0, local.TimeZone),
					},
					{
						"date": time.Date(date.Year(), date.Month(), date.Day(), date.Hour()+4, date.Minute(), 0, 0, local.TimeZone),
					},
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"forceTasker": map[string]interface{}{
					"taskerId": "0823456728",
					"name":     "Tasker",
				},
			},
			{ // 3. normalTask + date -> NOT EXPIRED yet
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour()+5, createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"forceTasker": map[string]interface{}{
					"taskerId": "0823456728",
					"name":     "Tasker",
				},
			},
		})

		chatIds := CreateChatConversation([]map[string]interface{}{
			{
				"askerPhone":  "0823456720",
				"taskerPhone": "0823456728",
				"taskId":      taskIds[0],
				"askerName":   "Asker 0",
				"taskerName":  "Tasker",
				"messages": []map[string]interface{}{
					{
						"_id":       "x63db5fc8e4ca3a9d185346f4",
						"from":      "TASKER",
						"message":   "Xin chào, tôi đã nhận làm công việc của bạn. Tôi sẽ đến đúng giờ.",
						"userId":    "xxx",
						"isRead":    false,
						"createdAt": globalLib.GetCurrentTime(local.TimeZone),
					},
				},
				"members": []map[string]interface{}{
					{
						"_id": "0823456720",
					},
					{
						"_id": "0823456728",
					},
				},
			},
			{
				"askerPhone":  "0823456721",
				"taskerPhone": "0823456728",
				"taskId":      taskIds[0],
				"askerName":   "Asker 0",
				"taskerName":  "Tasker",
				"messages": []map[string]interface{}{
					{
						"_id":       "x63db5fc8e4ca3a9d185346f5",
						"from":      "TASKER",
						"message":   "Xin chào, tôi đã nhận làm công việc của bạn. Tôi sẽ đến đúng giờ.",
						"userId":    "xxx",
						"isRead":    false,
						"createdAt": globalLib.GetCurrentTime(local.TimeZone),
					},
				},
				"members": []map[string]interface{}{
					{
						"_id": "0823456721",
					},
					{
						"_id": "0823456728",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the database", func() {
					var tasks []map[string]interface{}
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[globalConstant.ISO_CODE_VN], bson.M{"status": globalConstant.TASK_STATUS_CANCELED}, bson.M{}, &tasks)
					So(len(tasks), ShouldEqual, 2)

					var promotionHistory []*modelPromotionHistory.PromotionHistory
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[globalConstant.ISO_CODE_VN], bson.M{"promotionCode": "1234", "userId": "0823456720"}, bson.M{}, &promotionHistory)
					So(len(promotionHistory), ShouldEqual, 0)

					time.Sleep(1 * time.Second)

					chatMessage1, _ := pkgChatMessage.GetChatConversationWithMessages(globalConstant.ISO_CODE_VN, bson.M{"_id": chatIds[0]}, bson.M{})
					So(chatMessage1.AskerId, ShouldEqual, "0823456720")
					So(chatMessage1.TaskerId, ShouldEqual, "0823456728")
					So(len(chatMessage1.Messages), ShouldEqual, 3)
					So(chatMessage1.Messages[1].From, ShouldEqual, "SYSTEM")
					So(chatMessage1.Messages[1].MessageBySystem.Title.Vi, ShouldBeIn, []string{"Công việc của bạn đã được hủy", "Công việc đã bị hủy bởi hệ thống"})
					So(chatMessage1.Messages[1].MessageBySystem.Text.Vi, ShouldBeIn, []string{"Cuộc hội thoại cho yêu cầu công việc này đã hoàn tất. Nếu có yêu cầu khác, bạn vui lòng tạo công việc mới.", "Công việc dành riêng cho bạn đã bị huỷ, do đó cuộc hội thoại về yêu cầu công việc này sẽ kết thúc. Xin cảm ơn!"})

					chatMessage2, _ := pkgChatMessage.GetChatConversationWithMessages(globalConstant.ISO_CODE_VN, bson.M{"_id": chatIds[1]}, bson.M{})
					So(chatMessage2.AskerId, ShouldEqual, "0823456721")
					So(chatMessage2.TaskerId, ShouldEqual, "0823456728")
					So(len(chatMessage2.Messages), ShouldEqual, 3)
					So(chatMessage1.Messages[1].From, ShouldEqual, "SYSTEM")
					So(chatMessage1.Messages[1].MessageBySystem.Title.Vi, ShouldBeIn, []string{"Công việc của bạn đã được hủy", "Công việc đã bị hủy bởi hệ thống"})
					So(chatMessage1.Messages[1].MessageBySystem.Text.Vi, ShouldBeIn, []string{"Cuộc hội thoại cho yêu cầu công việc này đã hoàn tất. Nếu có yêu cầu khác, bạn vui lòng tạo công việc mới.", "Công việc dành riêng cho bạn đã bị huỷ, do đó cuộc hội thoại về yêu cầu công việc này sẽ kết thúc. Xin cảm ơn!"})
				})
			})
		})
	})

	// Check case cancel task force tasker after tasker response
	t.Run("2", func(t *testing.T) {
		ResetData()
		apiURL := apiSyncCron + "/cancel-task-force-tasker"

		CreateUser([]map[string]interface{}{
			{
				"phone":     "0823456720",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 0",
			},
			{
				"phone":     "0823456721",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 1",
			},
			{
				"phone":     "0823456728",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker",
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		date := now.AddDate(0, 0, 3)
		createdAtTime := now.Add(-7 * time.Hour)
		taskIds := CreateTask([]map[string]interface{}{
			{ // 0. normalTask + createdTime after 6 hours but tasker response 1 hour before -> not EXPIRED
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"forceTasker": map[string]interface{}{
					"taskerId": "0823456728",
					"name":     "Tasker",
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":       globalConstant.CHANGES_HISTORY_KEY_RESEND_FORCE_TASKER,
						"createdAt": time.Date(now.Year(), now.Month(), now.Day(), now.Hour()-2, now.Minute(), 0, 0, local.TimeZone),
					},
					{
						"key":       globalConstant.CHAT_HISTORY_KEY_TASKER_REJECT,
						"createdAt": time.Date(now.Year(), now.Month(), now.Day(), now.Hour()-1, now.Minute(), 0, 0, local.TimeZone),
					},
				},
			},
			{ // 1. normalTask + createdTime after 3 hours but tasker response 2 hour before -> EXPIRED
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour()+4, createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"forceTasker": map[string]interface{}{
					"taskerId": "0823456728",
					"name":     "Tasker",
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":       globalConstant.CHANGES_HISTORY_KEY_RESEND_FORCE_TASKER,
						"createdAt": time.Date(now.Year(), now.Month(), now.Day(), now.Hour()-3, now.Minute(), 0, 0, local.TimeZone),
					},
					{
						"key":       globalConstant.CHAT_HISTORY_KEY_TASKER_REJECT,
						"createdAt": time.Date(now.Year(), now.Month(), now.Day(), now.Hour()-2, now.Minute(), 0, 0, local.TimeZone),
					},
				},
			},
			{ // 2. normalTask + createdTime after 3 hours but tasker response 1 hour before -> not EXPIRED
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour()+4, createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"forceTasker": map[string]interface{}{
					"taskerId": "0823456728",
					"name":     "Tasker",
				},
				"changesHistory": []map[string]interface{}{
					{
						"key":       globalConstant.CHANGES_HISTORY_KEY_RESEND_FORCE_TASKER,
						"createdAt": time.Date(now.Year(), now.Month(), now.Day(), now.Hour()-3, now.Minute(), 0, 0, local.TimeZone),
					},
					{
						"key":       globalConstant.CHAT_HISTORY_KEY_TASKER_REJECT,
						"createdAt": time.Date(now.Year(), now.Month(), now.Day(), now.Hour()-1, now.Minute(), 0, 0, local.TimeZone),
					},
				},
			},
		})

		chatIds := CreateChatConversation([]map[string]interface{}{
			{
				"askerPhone":  "0823456720",
				"taskerPhone": "0823456728",
				"taskId":      taskIds[0],
				"askerName":   "Asker 0",
				"taskerName":  "Tasker",
				"messages": []map[string]interface{}{
					{
						"_id":       "x63db5fc8e4ca3a9d185346f4",
						"from":      "TASKER",
						"message":   "Xin chào, tôi đã nhận làm công việc của bạn. Tôi sẽ đến đúng giờ.",
						"userId":    "xxx",
						"isRead":    false,
						"createdAt": globalLib.GetCurrentTime(local.TimeZone),
					},
				},
				"members": []map[string]interface{}{
					{
						"_id": "0823456720",
					},
					{
						"_id": "0823456728",
					},
				},
			},
			{
				"askerPhone":  "0823456721",
				"taskerPhone": "0823456728",
				"taskId":      taskIds[0],
				"askerName":   "Asker 0",
				"taskerName":  "Tasker",
				"messages": []map[string]interface{}{
					{
						"_id":       "x63db5fc8e4ca3a9d185346f5",
						"from":      "TASKER",
						"message":   "Xin chào, tôi đã nhận làm công việc của bạn. Tôi sẽ đến đúng giờ.",
						"userId":    "xxx",
						"isRead":    false,
						"createdAt": globalLib.GetCurrentTime(local.TimeZone),
					},
				},
				"members": []map[string]interface{}{
					{
						"_id": "0823456721",
					},
					{
						"_id": "0823456728",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the database", func() {
					var tasks []map[string]interface{}
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[globalConstant.ISO_CODE_VN], bson.M{"status": globalConstant.TASK_STATUS_CANCELED}, bson.M{}, &tasks)
					So(len(tasks), ShouldEqual, 1)

					time.Sleep(1 * time.Second)

					chatMessage1, _ := pkgChatMessage.GetChatConversationWithMessages(globalConstant.ISO_CODE_VN, bson.M{"_id": chatIds[0]}, bson.M{})
					So(chatMessage1.AskerId, ShouldEqual, "0823456720")
					So(chatMessage1.TaskerId, ShouldEqual, "0823456728")
					So(len(chatMessage1.Messages), ShouldEqual, 3)
					So(chatMessage1.Messages[1].From, ShouldEqual, "SYSTEM")
					So(chatMessage1.Messages[1].MessageBySystem.Title.Vi, ShouldBeIn, []string{"Công việc của bạn đã được hủy", "Công việc đã bị hủy bởi hệ thống"})
					So(chatMessage1.Messages[1].MessageBySystem.Text.Vi, ShouldBeIn, []string{"Cuộc hội thoại cho yêu cầu công việc này đã hoàn tất. Nếu có yêu cầu khác, bạn vui lòng tạo công việc mới.", "Công việc dành riêng cho bạn đã bị huỷ, do đó cuộc hội thoại về yêu cầu công việc này sẽ kết thúc. Xin cảm ơn!"})
				})
			})
		})
	})

	// Check case cancel task - task resent
	t.Run("3", func(t *testing.T) {
		ResetData()
		apiURL := apiSyncCron + "/cancel-task-force-tasker"

		CreateUser([]map[string]interface{}{
			{
				"phone":           "0823456720",
				"type":            globalConstant.USER_TYPE_ASKER,
				"askerName":       "Asker 0",
				"favouriteTasker": []string{"0834567891", "0834567892"},
			},
			{
				"phone":     "0823456721",
				"type":      globalConstant.USER_TYPE_ASKER,
				"askerName": "Asker 1",
			},
			{
				"phone":     "0823456728",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker",
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
			},
			{
				"phone":     "0834567891",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker",
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
			},
			{
				"phone":     "0834567892",
				"type":      globalConstant.USER_TYPE_TASKER,
				"askerName": "Tasker",
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		date := now.AddDate(0, 0, 3)
		createdAtTime := now.Add(-7 * time.Hour)
		taskIds := CreateTask([]map[string]interface{}{
			{ // 0. normalTask + date -> EXPIRED
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", date.Year(), date.Month(), date.Day(), date.Hour(), date.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				},
				"promotion": map[string]interface{}{
					"code": "1234",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"forceTasker": map[string]interface{}{
					"taskerId": "0823456728",
					"name":     "Tasker",
					"isResent": true,
				},
			},
		})

		chatIds := CreateChatConversation([]map[string]interface{}{
			{
				"askerPhone":  "0823456720",
				"taskerPhone": "0823456728",
				"taskId":      taskIds[0],
				"askerName":   "Asker 0",
				"taskerName":  "Tasker",
				"messages": []map[string]interface{}{
					{
						"_id":       "x63db5fc8e4ca3a9d185346f4",
						"from":      "TASKER",
						"message":   "Xin chào, tôi đã nhận làm công việc của bạn. Tôi sẽ đến đúng giờ.",
						"userId":    "xxx",
						"isRead":    false,
						"createdAt": globalLib.GetCurrentTime(local.TimeZone),
					},
				},
				"members": []map[string]interface{}{
					{
						"_id": "0823456720",
					},
					{
						"_id": "0823456728",
					},
				},
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action": lib.RUN_NOW,
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the database", func() {
					var task *modelTask.Task
					globalDataAccess.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskIds[0], bson.M{}, &task)
					So(task, ShouldNotBeNil)
					So(task.Status, ShouldEqual, "POSTED")
					So(task.DateOptions, ShouldBeNil)
					So(task.ForceTasker, ShouldBeNil)
					So(task.Visibility, ShouldEqual, 1)
					// NOTE: Em comment test này lại do không biết vì sao test local (GO111MODULE=on) giống như ci lại bị lỗi.
					// Test này cũng đã được test bên cancel-task-vn, và phần này call GRPC qua cancel-task-vn nên không cần phải check lại
					// So(len(task.ViewedTaskers), ShouldEqual, 2)
					// for _, v := range task.ViewedTaskers {
					// 	So(v, ShouldBeIn, []string{"0834567891", "0834567892"})
					// }

					time.Sleep(1 * time.Second)

					chatMessage, _ := pkgChatMessage.GetChatConversationWithMessages(globalConstant.ISO_CODE_VN, bson.M{"_id": chatIds[0]}, bson.M{})
					So(chatMessage.AskerId, ShouldEqual, "0823456720")
					So(chatMessage.TaskerId, ShouldEqual, "0823456728")
					So(len(chatMessage.Messages), ShouldEqual, 3)
					So(chatMessage.Messages[1].From, ShouldEqual, "SYSTEM")
					So(chatMessage.Messages[1].MessageBySystem.Title.Vi, ShouldBeIn, []string{"Công việc của bạn đã được gửi đến Tasker khác", "Công việc đã bị hủy bởi Khách hàng"})
					So(chatMessage.Messages[1].MessageBySystem.Text.Vi, ShouldBeIn, []string{"Cuộc hồi thoại cho yêu cầu công việc này đã hoàn tất, vui lòng theo dõi công việc tại mục [**Chờ làm**](). Nếu có yêu cầu khác, bạn vui lòng tạo công việc mới.", "Công việc dành riêng cho bạn đã bị hủy, do đó cuộc hội thoại về yêu cầu công việc này sẽ kết thúc. Xin cảm ơn!"})
				})
			})
		})
	})

}
