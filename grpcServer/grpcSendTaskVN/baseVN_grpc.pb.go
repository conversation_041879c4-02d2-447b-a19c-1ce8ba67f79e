// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcSendTaskVN

import (
	context "context"
	pushNotificationNewTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PushNotificationNewTaskVNClient is the client API for PushNotificationNewTaskVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PushNotificationNewTaskVNClient interface {
	NewTask(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	TopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	NewTaskToDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	FavTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	Normal(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	FavAndTaskerDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	FavAndTopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
	NewTaskToCity(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error)
}

type pushNotificationNewTaskVNClient struct {
	cc grpc.ClientConnInterface
}

func NewPushNotificationNewTaskVNClient(cc grpc.ClientConnInterface) PushNotificationNewTaskVNClient {
	return &pushNotificationNewTaskVNClient{cc}
}

func (c *pushNotificationNewTaskVNClient) NewTask(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskVN.PushNotificationNewTaskVN/NewTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationNewTaskVNClient) TopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskVN.PushNotificationNewTaskVN/TopTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationNewTaskVNClient) NewTaskToDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskVN.PushNotificationNewTaskVN/NewTaskToDistrict", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationNewTaskVNClient) FavTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskVN.PushNotificationNewTaskVN/FavTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationNewTaskVNClient) Normal(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskVN.PushNotificationNewTaskVN/Normal", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationNewTaskVNClient) FavAndTaskerDistrict(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskVN.PushNotificationNewTaskVN/FavAndTaskerDistrict", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationNewTaskVNClient) FavAndTopTasker(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskVN.PushNotificationNewTaskVN/FavAndTopTasker", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *pushNotificationNewTaskVNClient) NewTaskToCity(ctx context.Context, in *pushNotificationNewTask.NewTaskRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcSendTaskVN.PushNotificationNewTaskVN/NewTaskToCity", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PushNotificationNewTaskVNServer is the server API for PushNotificationNewTaskVN service.
// All implementations must embed UnimplementedPushNotificationNewTaskVNServer
// for forward compatibility
type PushNotificationNewTaskVNServer interface {
	NewTask(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	TopTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	NewTaskToDistrict(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	FavTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	Normal(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	FavAndTaskerDistrict(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	FavAndTopTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	NewTaskToCity(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error)
	mustEmbedUnimplementedPushNotificationNewTaskVNServer()
}

// UnimplementedPushNotificationNewTaskVNServer must be embedded to have forward compatible implementations.
type UnimplementedPushNotificationNewTaskVNServer struct {
}

func (UnimplementedPushNotificationNewTaskVNServer) NewTask(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewTask not implemented")
}
func (UnimplementedPushNotificationNewTaskVNServer) TopTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TopTasker not implemented")
}
func (UnimplementedPushNotificationNewTaskVNServer) NewTaskToDistrict(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewTaskToDistrict not implemented")
}
func (UnimplementedPushNotificationNewTaskVNServer) FavTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FavTasker not implemented")
}
func (UnimplementedPushNotificationNewTaskVNServer) Normal(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Normal not implemented")
}
func (UnimplementedPushNotificationNewTaskVNServer) FavAndTaskerDistrict(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FavAndTaskerDistrict not implemented")
}
func (UnimplementedPushNotificationNewTaskVNServer) FavAndTopTasker(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FavAndTopTasker not implemented")
}
func (UnimplementedPushNotificationNewTaskVNServer) NewTaskToCity(context.Context, *pushNotificationNewTask.NewTaskRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method NewTaskToCity not implemented")
}
func (UnimplementedPushNotificationNewTaskVNServer) mustEmbedUnimplementedPushNotificationNewTaskVNServer() {
}

// UnsafePushNotificationNewTaskVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PushNotificationNewTaskVNServer will
// result in compilation errors.
type UnsafePushNotificationNewTaskVNServer interface {
	mustEmbedUnimplementedPushNotificationNewTaskVNServer()
}

func RegisterPushNotificationNewTaskVNServer(s *grpc.Server, srv PushNotificationNewTaskVNServer) {
	s.RegisterService(&_PushNotificationNewTaskVN_serviceDesc, srv)
}

func _PushNotificationNewTaskVN_NewTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationNewTaskVNServer).NewTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskVN.PushNotificationNewTaskVN/NewTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationNewTaskVNServer).NewTask(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotificationNewTaskVN_TopTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationNewTaskVNServer).TopTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskVN.PushNotificationNewTaskVN/TopTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationNewTaskVNServer).TopTasker(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotificationNewTaskVN_NewTaskToDistrict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationNewTaskVNServer).NewTaskToDistrict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskVN.PushNotificationNewTaskVN/NewTaskToDistrict",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationNewTaskVNServer).NewTaskToDistrict(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotificationNewTaskVN_FavTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationNewTaskVNServer).FavTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskVN.PushNotificationNewTaskVN/FavTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationNewTaskVNServer).FavTasker(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotificationNewTaskVN_Normal_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationNewTaskVNServer).Normal(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskVN.PushNotificationNewTaskVN/Normal",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationNewTaskVNServer).Normal(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotificationNewTaskVN_FavAndTaskerDistrict_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationNewTaskVNServer).FavAndTaskerDistrict(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskVN.PushNotificationNewTaskVN/FavAndTaskerDistrict",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationNewTaskVNServer).FavAndTaskerDistrict(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotificationNewTaskVN_FavAndTopTasker_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationNewTaskVNServer).FavAndTopTasker(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskVN.PushNotificationNewTaskVN/FavAndTopTasker",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationNewTaskVNServer).FavAndTopTasker(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PushNotificationNewTaskVN_NewTaskToCity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pushNotificationNewTask.NewTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PushNotificationNewTaskVNServer).NewTaskToCity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSendTaskVN.PushNotificationNewTaskVN/NewTaskToCity",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PushNotificationNewTaskVNServer).NewTaskToCity(ctx, req.(*pushNotificationNewTask.NewTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _PushNotificationNewTaskVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcSendTaskVN.PushNotificationNewTaskVN",
	HandlerType: (*PushNotificationNewTaskVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "NewTask",
			Handler:    _PushNotificationNewTaskVN_NewTask_Handler,
		},
		{
			MethodName: "TopTasker",
			Handler:    _PushNotificationNewTaskVN_TopTasker_Handler,
		},
		{
			MethodName: "NewTaskToDistrict",
			Handler:    _PushNotificationNewTaskVN_NewTaskToDistrict_Handler,
		},
		{
			MethodName: "FavTasker",
			Handler:    _PushNotificationNewTaskVN_FavTasker_Handler,
		},
		{
			MethodName: "Normal",
			Handler:    _PushNotificationNewTaskVN_Normal_Handler,
		},
		{
			MethodName: "FavAndTaskerDistrict",
			Handler:    _PushNotificationNewTaskVN_FavAndTaskerDistrict_Handler,
		},
		{
			MethodName: "FavAndTopTasker",
			Handler:    _PushNotificationNewTaskVN_FavAndTopTasker_Handler,
		},
		{
			MethodName: "NewTaskToCity",
			Handler:    _PushNotificationNewTaskVN_NewTaskToCity_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "baseVN.proto",
}
