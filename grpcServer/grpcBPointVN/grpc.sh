printf "\n\n\x1b[33m================== Generate gRPC bPoint VN v3 \x1b[0m \n\n"

protoc -I . \
  -I$GOPATH/src \
  -I$GOPATH/src/github.com/grpc-ecosystem/grpc-gateway/third_party/googleapis \
  --go_out . --go_opt paths=source_relative \
  --go-grpc_out . --go-grpc_opt paths=source_relative \
  bPointVN.proto

printf "\n\n\x1b[33m================== Generate reverse-proxy \x1b[0m \n\n"

protoc -I . \
  -I$GOPATH/src \
  -I$GOPATH/src/github.com/grpc-ecosystem/grpc-gateway/third_party/googleapis \
  --grpc-gateway_out . \
  --grpc-gateway_opt logtostderr=true \
  --grpc-gateway_opt paths=source_relative \
  --grpc-gateway_opt generate_unbound_methods=true \
  bPointVN.proto