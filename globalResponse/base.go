package globalResponse

import (
	"encoding/json"
	"net/http"
	"strconv"

	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// type JWTPayload struct {
// 	UserId string `json:"userId,omitempty"`
// 	Date   string `json:"date,omitempty"`
// 	jwt.StandardClaims
// }

type ResponseErrorCode struct {
	StatusCode int
	ErrorCode  string                    `json:"code,omitempty"`
	Message    string                    `json:"message,omitempty"`
	Data       map[string]interface{}    `json:"data,omitempty"`
	ErrorText  *modelService.ServiceText `json:"errorText,omitempty"`
}

// ================= Functions

func ResponseSuccess(w http.ResponseWriter, data interface{}) {
	t := Translate(data)

	jsonRes, _ := json.Marshal(t)
	w.Header().Set("Content-Type", "application/json")
	w.<PERSON>er().Set("Content-Length", strconv.Itoa(len(jsonRes)))
	w.WriteHeader(http.StatusOK)
	w.Write(jsonRes)
}

func ResponseError(w http.ResponseWriter, data ResponseErrorCode) {
	res := map[string]interface{}{
		"error": map[string]interface{}{
			"code":      data.ErrorCode,
			"message":   data.Message,
			"data":      data.Data,
			"errorText": data.ErrorText,
		},
	}
	jsonRes, _ := json.Marshal(res)
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Content-Length", strconv.Itoa(len(jsonRes)))
	w.WriteHeader(data.StatusCode)
	w.Write(jsonRes)
}

func ResponseHTML(w http.ResponseWriter, body string) {
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Header().Set("Content-Length", strconv.Itoa(len(body)))
	w.WriteHeader(http.StatusOK)
	w.Write([]byte(body))
}

func ResponseRawMessage(w http.ResponseWriter, body string, statusCode int) {
	w.Header().Set("Content-Type", "text/html; charset=utf-8")
	w.Header().Set("Content-Length", strconv.Itoa(len(body)))
	w.WriteHeader(statusCode)
	w.Write([]byte(body))
}

func (r *ResponseErrorCode) ToGrpcError() error {
	err := status.New(codes.Internal, r.ErrorCode)
	customErr, _ := err.WithDetails(&response.ResponseError{ErrorText: r.ErrorText})
	return customErr.Err()
}
