/*
* @File: 51_createAskerYearEndReportVN_test.go
* @Description: Handler function, case test
 * @CreatedAt: 31/12/2021
 * @Author: vinhnt
*/
package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"log"
	"net/http/httptest"
	"testing"
	"time"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelAskerMonthlyReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/askerMonthlyReport"
	"go.mongodb.org/mongo-driver/bson"
)

func TestCreateAskerMonthlyReport(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		log.Println("==================================== Unit Test build AskerMonthlyReport VN")
		apiURL := apiSyncCron + "/create-asker-monthly-report"
		ResetData()
		//Create User
		CreateUser([]map[string]interface{}{
			{
				"phone": "0823456720",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
				"rankInfo": map[string]interface{}{
					"text": map[string]interface{}{
						"vi": "Thành viên",
						"en": "Member",
						"ko": "회원",
						"th": "สมาชิก",
					},
					"point":    0.0,
					"rankName": "MEMBER",
				},
				"resetRankHistory": []map[string]interface{}{
					{
						"rankInfo": map[string]interface{}{
							"rankName": "PLATINUM",
							"text": map[string]interface{}{
								"vi": "Bạch kim",
								"en": "Platinum",
								"ko": "플래티넘",
								"th": "ระดับ Platinum",
							},
							"point": 0.0,
						},
						"point":              21.0,
						"phase":              2,
						"isResetRank_phase2": true,
					},
				},
			}, {
				"phone": "0823456721",
				"name":  "Asker 02",
				"type":  globalConstant.USER_TYPE_ASKER,
				"rankInfo": map[string]interface{}{
					"text": map[string]interface{}{
						"vi": "Bạc",
						"en": "Member",
						"ko": "회원",
						"th": "สมาชิก",
					},
					"point":    0.0,
					"rankName": "SILVER",
				},
				"resetRankHistory": []map[string]interface{}{
					{
						"rankInfo": map[string]interface{}{
							"text": map[string]interface{}{
								"vi": "Thành viên",
								"en": "Member",
								"ko": "회원",
								"th": "สมาชิก",
							},
							"rankName": "MEMBER",
							"point":    0.0,
						},
						"point":              21.0,
						"phase":              2,
						"isResetRank_phase2": true,
					},
				},
			}, {
				"phone":      "0823456722",
				"name":       "Asker 03",
				"type":       globalConstant.USER_TYPE_ASKER,
				"friendCode": "ASKER0",
			}, {
				"phone":     "0823456789",
				"name":      "Tasker 01",
				"type":      globalConstant.USER_TYPE_TASKER,
				"score":     62,
				"scoreRate": 2,
				"taskDone":  3,
			}, {
				"phone":     "0823456710",
				"name":      "Tasker 012",
				"type":      globalConstant.USER_TYPE_TASKER,
				"score":     70,
				"scoreRate": 3,
				"taskDone":  35,
			}, {
				"phone":     "0823456713",
				"name":      "Tasker 013",
				"type":      globalConstant.USER_TYPE_TASKER,
				"score":     62,
				"scoreRate": 1,
				"taskDone":  52,
			},
		})
		var listTasker []map[string]interface{}
		for i := 0; i < 500; i++ {
			tasker := map[string]interface{}{
				"phone":     fmt.Sprintf("%s%d", "0823456720", i),
				"name":      "Asker 013",
				"type":      globalConstant.USER_TYPE_ASKER,
				"score":     62,
				"scoreRate": 1,
				"taskDone":  52,
			}
			if i%10 == 0 {
				tasker["friendCode"] = "ASKER0"
			}
			listTasker = append(listTasker, tasker)
		}
		CreateUser(listTasker)
		createdAtTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, -1, 0)
		var listTask []map[string]interface{}
		for i := 0; i < 500; i++ {
			task := map[string]interface{}{
				"askerPhone":  fmt.Sprintf("%s%d", "0823456720", i),
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"cost": globalLib.RandomIntInRange(200000, 400000),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456710",
					},
				},
			}
			listTask = append(listTask, task)
		}
		for i := 0; i < 50; i++ {
			dateTime := time.Date(createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute(), createdAtTime.Second(), 0, local.TimeZone)
			task := map[string]interface{}{
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), 1, dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
			}
			listTask = append(listTask, task)
		}
		for i := 0; i < 100; i++ {
			dateTime := time.Date(createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute(), createdAtTime.Second(), 0, local.TimeZone)
			task := map[string]interface{}{
				"askerPhone":  "0823456721",
				"serviceName": globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), 1, dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
			}
			listTask = append(listTask, task)
		}
		CreateTask(listTask)
		dateTime := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, -1, 0)
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), dateTime.Day(), dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"visibility": 3,
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
			}, {
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), dateTime.Day(), dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_HOME_CLEANING,
				},
				"subscriptionId": "123abc",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
			}, {
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), dateTime.Day(), dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_HOME_CLEANING,
				},
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456789",
					},
				},
			}, {
				"askerPhone":  "0823456720",
				"serviceName": globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING,
				"description": "Desc Test 1",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"rated":       true,
				"date":        fmt.Sprintf("%d,%d,%d,%d,%d", dateTime.Year(), dateTime.Month(), dateTime.Day(), dateTime.Hour(), dateTime.Minute()),
				"createdAt":   fmt.Sprintf("%d,%d,%d,%d,%d", createdAtTime.Year(), createdAtTime.Month(), createdAtTime.Day(), createdAtTime.Hour(), createdAtTime.Minute()),
				"serviceText": map[string]interface{}{
					"en": globalConstant.SERVICE_NAME_DEEP_CLEANING,
				},
				"subscriptionId": "123abc",
				"taskPlace": map[string]interface{}{
					"lat":      10.9824936,
					"lng":      106.673495,
					"country":  "VN",
					"city":     "Bình Dương",
					"district": "Thủ Dầu Một",
					"address":  "191 Phú Lợi, Thủ Dầu Một, Bình Dương, Việt Nam",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0823456710",
					},
				},
			},
		})
		CreatePointTransaction([]map[string]interface{}{
			{
				"userId":    "0823456720",
				"point":     60.0,
				"type":      "C",
				"createdAt": createdAtTime,
				"isoCode":   globalConstant.ISO_CODE_VN,
			}, {
				"userId":    "0823456720",
				"point":     30.0,
				"type":      "C",
				"createdAt": createdAtTime,
				"isoCode":   globalConstant.ISO_CODE_VN,
			}, {
				"userId":    "0823456720",
				"point":     10.0,
				"type":      "D",
				"createdAt": createdAtTime,
				"isoCode":   globalConstant.ISO_CODE_VN,
			}, {
				"userId":    "0823456720",
				"point":     20.0,
				"type":      "D",
				"createdAt": createdAtTime,
				"isoCode":   globalConstant.ISO_CODE_VN,
			}, {
				"userId":    "0823456720",
				"point":     50.0,
				"type":      "D",
				"createdAt": createdAtTime,
				"isoCode":   globalConstant.ISO_CODE_VN,
			}, {
				"userId":    "0823456720",
				"point":     30.0,
				"type":      "D",
				"createdAt": createdAtTime,
				"isoCode":   globalConstant.ISO_CODE_VN,
			},
		})

		Convey(fmt.Sprintf("Given a HTTP request for %s", apiURL), t, func() {
			body := map[string]interface{}{
				"action":  lib.RUN_NOW,
				"isoCode": "VN",
			}
			reqBody, _ := json.Marshal(body)
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			resp := httptest.NewRecorder()

			Convey("When the request is handled by the Router", func() {
				service.NewRouter().ServeHTTP(resp, req)
				So(resp.Code, ShouldEqual, 200)
				// Remove taskerYearEndReport
				count, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_ASKER_MONTHLY_REPORT[local.ISO_CODE], bson.M{})
				So(count, ShouldEqual, 503)
				var askerReportData []*modelAskerMonthlyReport.AskerMonthlyReport
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_ASKER_MONTHLY_REPORT[local.ISO_CODE], bson.M{"userId": bson.M{"$in": []string{"0823456720", "0823456721"}}}, bson.M{}, &askerReportData)
				for _, v := range askerReportData {
					tRp := v.MonthlyReport[0]
					if v.UserId == "0823456720" {
						So(tRp.TotalTaskDone, ShouldEqual, 54)
						So(tRp.TotalTaskDuration, ShouldEqual, 108)
						for _, v := range tRp.NumberOfUsedOnService {
							if v.ServiceName == globalConstant.SERVICE_KEY_NAME_HOME_CLEANING {
								So(v.NumberOfUsed, ShouldEqual, 53)
							} else if v.ServiceName == globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING {
								So(v.NumberOfUsed, ShouldEqual, 1)
							}
						}
						So(tRp.TotalUsedbPoint, ShouldEqual, 90)
						So(tRp.TotalAccumulatedbPoint, ShouldEqual, 110.0)
					} else if v.UserId == "0823456721" {
						So(tRp.TotalTaskDone, ShouldEqual, 100)
						So(tRp.TotalTaskDuration, ShouldEqual, 200)
						for _, v := range tRp.NumberOfUsedOnService {
							if v.ServiceName == globalConstant.SERVICE_KEY_NAME_DEEP_CLEANING {
								So(v.NumberOfUsed, ShouldEqual, 100)
							}
						}
					}
				}
			})
		})
	})

}
