package globalRepo

import (
	"context"

	"gitlab.com/btaskee/go-services-model-v2/dbConfig"
)

func InsertOne(collectionName string, data interface{}) error {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	_, err := collection.InsertOne(ctx, data)
	return err
}

func InsertAll(collectionName string, data []interface{}) error {
	var ctx, cancel = context.WithTimeout(context.Background(), TIMEOUT)
	defer cancel()
	collection := dbConfig.GetCollection(collectionName)
	_, err := collection.InsertMany(ctx, data)
	return err
}

func InsertOneWithContext(ctx context.Context, collectionName string, data interface{}) error {
	collection := dbConfig.GetCollection(collectionName)
	_, err := collection.InsertOne(ctx, data)
	return err
}

func InsertAllWithContext(ctx context.Context, collectionName string, data []interface{}) error {
	collection := dbConfig.GetCollection(collectionName)
	_, err := collection.InsertMany(ctx, data)
	return err
}
