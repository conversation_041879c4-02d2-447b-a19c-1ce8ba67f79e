package handlers

import (
	"fmt"
	"net/http"

	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	modelSettingCountry "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settingCountry"
	modelSettings "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelSubscriptionSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscriptionSetting"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func SendSubscriptionSuggestionEmail(req *emailSending.EmailRequest) (*modelEmailResponse.EmailResponse, error) {
	defer local.Logger.Sync()

	// Check server config
	resErr := CheckEmailConfig()
	if resErr != nil {
		local.Logger.Warn(resErr.Message)
		return resErr, nil
	}

	// Validate
	if req.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_USER_ID_REQUIRED,
		}, nil
	}

	// Get user
	user, _ := modelUser.GetOneById(local.ISO_CODE, req.UserId, bson.M{"emails": 1, "language": 1, "name": 1, "referralCode": 1})

	if user == nil {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_USER_NOT_FOUND,
		}, nil
	}
	if len(user.Emails) == 0 || !user.Emails[0].Verified {
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusOK,
		}, nil
	}

	// Get setting
	var setting *modelSettings.Settings
	var settingCountry *modelSettingCountry.SettingCountry
	var subscriptionSetting *modelSubscriptionSetting.SubscriptionSetting
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"referralSetting": 1}, &setting)
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_COUNTRY[local.ISO_CODE], bson.M{"isoCode": local.ISO_CODE}, bson.M{"currency": 1}, &settingCountry)
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SUBSCRIPTION_SETTINGS[local.ISO_CODE], bson.M{}, bson.M{"discount": 1}, &subscriptionSetting)

	lang := globalConstant.LANG_EN
	if user.Language != "" {
		lang = user.Language
	}

	userName := user.Name
	if lang == globalConstant.LANG_KO {
		userName = fmt.Sprintf("%s님", userName)
	}

	emailData := map[string]interface{}{
		"SubscriptionTitle1":                                  localization.T(lang, "SUBSCRIPTION_TITLE_1"),
		"SubscriptionTitle2":                                  localization.T(lang, "SUBSCRIPTION_TITLE_2"),
		"SubscriptionTitle3":                                  localization.T(lang, "SUBSCRIPTION_TITLE_3"),
		"EmailHello":                                          localization.T(lang, "EMAIL_HELLO"),
		"Username":                                            userName,
		"EmailThankyou":                                       localization.T(lang, "EMAIL_THANK_YOU"),
		"SubscriptionSuggestDescription1":                     localization.T(lang, "SUBSCRIPTION_SUGGESTION_DESCRIPTION_1"),
		"ServiceName":                                         localization.T(lang, "SUBSCRIPTION_CLEANING_SERVICE_NAME"),
		"SubscriptionSuggestDescription2":                     localization.T(lang, "SUBSCRIPTION_SUGGESTION_DESCRIPTION_2"),
		"SubscriptionPrivileges":                              localization.T(lang, "SUBSCRIPTION_PRIVILEGES"),
		"SubscriptionPrivilegesSaveTime":                      localization.T(lang, "SUBSCRIPTION_PRIVILEGES_SAVE_TIME"),
		"SubscriptionPrivilegesSaveTimeDescription":           localization.T(lang, "SUBSCRIPTION_PRIVILEGES_SAVE_TIME_DESCRIPTION"),
		"SubscriptionPrivilegesQualityHousekeeper":            localization.T(lang, "SUBSCRIPTION_PRIVILEGES_QUALITY_HOUSEKEEPER"),
		"SubscriptionPrivilegesQualityHousekeeperDescription": localization.T(lang, "SUBSCRIPTION_PRIVILEGES_QUALITY_HOUSEKEEPER_DESCRIPTION"),
		"SubscriptionPrivilegesFlexibleSchedule":              localization.T(lang, "SUBSCRIPTION_PRIVILEGES_FLEXIBLE_SCHEDULE"),
		"SubscriptionPrivilegesFlexibleScheduleDescription":   localization.T(lang, "SUBSCRIPTION_PRIVILEGES_FLEXIBLE_SCHEDULE_DESCRIPTION"),
		"SubscriptionPrivilegesChangeLocation":                localization.T(lang, "SUBSCRIPTION_PRIVILEGES_CHANGE_LOCATION"),
		"SubscriptionPrivilegesChangeLocationDescription":     localization.T(lang, "SUBSCRIPTION_PRIVILEGES_CHANGE_LOCATION_DESCRIPTION"),
		"SubscriptionBookingInstruction":                      localization.T(lang, "SUBSCRIPTION_BOOKING_INSTRUCTION"),
		"SubscriptionBookingInstructionStep1":                 localization.T(lang, "SUBSCRIPTION_BOOKING_INSTRUCTION_STEP_1"),
		"SubscriptionBookingInstructionStep1Description":      localization.T(lang, "SUBSCRIPTION_BOOKING_INSTRUCTION_STEP_1_DESCRIPTION"),
		"SubscriptionBookingInstructionStep2":                 localization.T(lang, "SUBSCRIPTION_BOOKING_INSTRUCTION_STEP_2"),
		"SubscriptionBookingInstructionStep2Description":      localization.T(lang, "SUBSCRIPTION_BOOKING_INSTRUCTION_STEP_2_DESCRIPTION"),
		"SubscriptionBookingInstructionStep3":                 localization.T(lang, "SUBSCRIPTION_BOOKING_INSTRUCTION_STEP_3"),
		"SubscriptionBookingInstructionStep3Description":      localization.T(lang, "SUBSCRIPTION_BOOKING_INSTRUCTION_STEP_3_DESCRIPTION"),
		"BookingPaymentMethod":                                localization.T(lang, "BOOKING_PAYMENT_METHOD"),
		"BookingPaymentMethodBankTransfer1":                   localization.T(lang, "BOOKING_PAYMENT_METHOD_BANK_TRANSFER_1"),
		"BookingPaymentMethodBankTransfer2":                   localization.T(lang, "BOOKING_PAYMENT_METHOD_BANK_TRANSFER_2"),
		"BookingPaymentMethodCard1":                           localization.T(lang, "BOOKINP_PAYMENT_METHOD_CARD_1"),
		"BookingPaymentMethodCard2":                           localization.T(lang, "BOOKING_PAYMENT_METHOD_CARD_2"),
		"BookingPaymentMethodWallet1":                         localization.T(lang, "BOOKING_PAYMENT_METHOD_WALLET_1"),
		"BookingPaymentMethodWallet2":                         localization.T(lang, "BOOKING_PAYMENT_METHOD_WALLET_2"),
		"SubscriptionNotice":                                  localization.T(lang, "SUBSCRIPTION_NOTICE"),
		"SubscriptionNotice1":                                 localization.T(lang, "SUBSCRIPTION_NOTICE_1"),
		"SubscriptionNotice2":                                 localization.T(lang, "SUBSCRIPTION_NOTICE_2"),
		"SubscriptionNotice3":                                 localization.T(lang, "SUBSCRIPTION_NOTICE_3"),
		"SubscriptionNotice4":                                 localization.T(lang, "SUBSCRIPTION_NOTICE_4"),
		"SubscriptionRefundPolicy":                            localization.T(lang, "SUBSCRIPTION_REFUND_POLICY"),
		"SubscriptionRefundTask":                              localization.T(lang, "SUBSCRIPTION_REFUND_TASK"),
		"SubscriptionRefundTask1":                             localization.T(lang, "SUBSCRIPTION_REFUND_TASK_1"),
		"SubscriptionRefundTask2":                             localization.T(lang, "SUBSCRIPTION_REFUND_TASK_2"),
		"SubscriptionRefundTask3":                             localization.T(lang, "SUBSCRIPTION_REFUND_TASK_3"),
		"SubscriptionRefundTask4":                             localization.T(lang, "SUBSCRIPTION_REFUND_TASK_4"),
		"SubscriptionRefundSubscription":                      localization.T(lang, "SUBSCRIPTION_REFUND_SUBSCRIPTION"),
		"SubscriptionRefundSubscription1":                     localization.T(lang, "SUBSCRIPTION_REFUND_SUBSCRIPTION_1"),
		"SubscriptionRefundSubscription2":                     localization.T(lang, "SUBSCRIPTION_REFUND_SUBSCRIPTION_2"),
		"BookingSubscriptionButtonText":                       localization.T(lang, "BOOKING_SUBSCRIPTION_BUTTON_TEXT"),
		"FollowbTaskeeAt":                                     localization.T(lang, "FOLLOW_BTASKEE_AT"),
		"SubscriptionDiscount":                                localization.T(lang, "SUBSCRIPTION_CLEANING_DISCOUNT"),
	}

	// Subscription Discount
	subscriptionDiscountCityList := []map[string]interface{}{}
	// Subscription Discount
	for _, city := range subscriptionSetting.Discount {
		subscriptionDiscountCityByMonth := []map[string]interface{}{}
		for _, month := range city.DiscountByMonth {
			if month.Percentage > 0 {
				discountValue := fmt.Sprintf(`<span style="color: #ff8228;"><strong>%d%%</strong></span>`, int(month.Percentage))
				if lang == globalConstant.LANG_KO {
					subscriptionDiscountCityByMonth = append(subscriptionDiscountCityByMonth, map[string]interface{}{
						"SubscriptionDiscountCityReduce": localization.T(lang, "SUBSCRIPTION_CLEANING_DISCOUNT_BY_MONTH_DESCRIPTION", month.Month, discountValue),
					})
				} else {
					subscriptionDiscountCityByMonth = append(subscriptionDiscountCityByMonth, map[string]interface{}{
						"SubscriptionDiscountCityReduce": localization.T(lang, "SUBSCRIPTION_CLEANING_DISCOUNT_BY_MONTH_DESCRIPTION", discountValue, month.Month),
					})
				}
			}
		}
		if len(subscriptionDiscountCityByMonth) > 0 {
			subscriptionDiscountCityList = append(subscriptionDiscountCityList, map[string]interface{}{
				"SubscriptionDiscountCity":        city.City,
				"SubscriptionDiscountCityByMonth": subscriptionDiscountCityByMonth,
			})
		}
	}
	if len(subscriptionDiscountCityList) > 0 {
		emailData["SubscriptionDiscountList"] = subscriptionDiscountCityList
	}

	// Invitation
	if settingCountry != nil && settingCountry.Currency != nil && settingCountry.Currency.Code != "" {
		referralValue, referralSignCode := getReferralValueAndCurrency(local.ISO_CODE, settingCountry.Currency.Code, setting)
		emailData["HasInvitation"] = true
		emailData["Invitation1"] = localization.T(lang, "INVITATION_1")
		emailData["Invitation2"] = localization.T(lang, "INVITATION_2")
		emailData["ReferralCode"] = user.ReferralCode
		emailData["InvitationDescription"] = localization.T(lang, "INVITE_DESCRIPTION", globalLib.FormatMoney(referralValue), referralSignCode, globalLib.FormatMoney(referralValue), referralSignCode)
	}

	emailData = globalLib.UpdateBtaskeeInfoInEmailByIsoCode(emailData, local.ISO_CODE)

	t := fmt.Sprintf("%s/subscription-suggest-cleaning.html", cfg.EmailTemplateURL)
	body, err := ParseTemplate(t, emailData)
	if body == "" || err != nil {
		return nil, err
	}

	// TODO: Test with "./test.sh 19 1"
	option := &emailSending.EmailSending{
		ReplyTo: "<EMAIL>",
		From:    "No Reply <<EMAIL>>",
		To:      user.Emails[0].Address,
		Subject: "bTaskee cleaning subscription",
		Content: body,
	}

	return SendEmail(option)
}
