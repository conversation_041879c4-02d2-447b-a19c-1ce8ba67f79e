import { Meteor } from 'meteor/meteor';
import { check } from 'meteor/check';
import { HTTP } from 'meteor/http';

/*
Params:
- ids: Array String
- language: String
- title: Object {vi: String, en: String, ko: String, th: String}
- message: Object {vi: String, en: String, ko: String, th: String}
- payload: Object {type: Int, taskId: String}
*/
export const callToPushNotificationGoService = (ids, title, message, payload, options) => {
  const arrayUserIdsObject = Meteor.appCollection.users.find({_id: {$in: ids}}).map(function(user) {return {userId: user._id, language: user.language || "vi"}});
  if (arrayUserIdsObject && arrayUserIdsObject.length > 0) {
    var reqData = {
      userIds: arrayUserIdsObject,
      title: title,
      body: message,
    };
    if (payload) {
      reqData.payload = payload;
    }
    var apiUrl = "/v3/push-notification-vn/send";
    if (options && options.isoCode === "TH") {
      apiUrl = "/v3/push-notification-th/send";
    }
    HTTP.post(Meteor.settings.GO_SERVICE.API_URL + apiUrl, {
      headers: {
        'Content-Type': 'application/json',
        'accessKey': Meteor.settings.GO_SERVICE.API_KEY
      },
      data: reqData,
    }, (error, result) => {
      if (error) {
        console.log("callToPushNotificationGoService Error");
        console.log(error);
      }
    });
  }
};

export const getUserLanguage = (userId) => {
  const userObject = Meteor.appCollection.users.findOne({ _id: userId }, { fields: { language: 1 } });
  if ( userObject ) {
    return userObject.language || 'vi';
  }
  return null;
};

export const sendNotificationByIds = (data) => {
  check(data, {
    from: String,
    userIds: [String],
    message: {
      title: { vi: String, en: String, ko: String },
      text: { vi: String, en: String, ko: String }
    },
    payload: Object,
  });
  callToPushNotificationGoService(data.userIds, data.message.title, data.message.text, data.payload);
  // data.userIds.forEach((userId) => {
  //   const language = getUserLanguage(userId);
  //   if ( language ) {
  //     Push.send({
  //       from: data.from,
  //       title: data.message.title[language],
  //       text: data.message.text[language],
  //       apn: {
  //         sound: Meteor.settings.PushNotification.sound.apn
  //       },
  //       gcm: {
  //         sound: Meteor.settings.PushNotification.sound.fcm // fcm = gcm
  //       },
  //       payload: data.payload,
  //       query: {
  //         userId,
  //       },
  //     });
  //   }
  // });
};
