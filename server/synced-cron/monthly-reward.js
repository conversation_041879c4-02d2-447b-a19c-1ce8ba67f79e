import { Meteor } from 'meteor/meteor';
import _ from 'lodash';
import accounting from 'accounting';
import {
  sendNotificationByIds,
  trackingScheduleStatus,
  postToSlack,
  sendEmailFunction,
  revertTimeZone
} from '/server/helper-function';
import moment from 'moment';

const depositAward = (tasker, award, rewardId) => {
  Meteor.appCollection.FinancialAccount.update({ _id: tasker.fAccountId }, { $inc: { Promotion: award.gift } });
  Meteor.appCollection.Reward.update({ _id: rewardId }, { $set: { isGiven: true } });
  Meteor.appCollection.FATransaction.insert({
    userId: tasker._id,
    accountType: 'P',
    type: 'D',
    source: {
      name: 'TASKER MONTHLY REWARD',
      value: '0000',
      tranferType: 'cash',
      depositDate: new Date(),
      cashierId: 'System',
      cashierName: 'System',
      rewardId,
    },
    amount: award.gift,
    date: new Date(),
  });
  sendNotificationByIds([tasker._id], {
    title: {
      vi: `<PERSON><PERSON> chú<PERSON> mừ<PERSON>, bạn đã hoàn thành ${tasker.nDoneTaskInMonth} giờ làm việc trong tháng trước. Bạn đã được tặng ${accounting.formatNumber(award.gift)} VND vào tài khoản khuyến mãi.`,
      en: `Congratulation! you have done ${tasker.nDoneTaskInMonth} hours last month. As a reward, ${accounting.formatNumber(award.gift)} VND has been added to your promotion account.`,
      ko: `Congratulation! you have done ${tasker.nDoneTaskInMonth} hours last month. As a reward, ${accounting.formatNumber(award.gift)} VND has been added to your promotion account.`,
      th: `Congratulation! you have done ${tasker.nDoneTaskInMonth} hours last month. As a reward, ${accounting.formatNumber(award.gift)} VND has been added to your promotion account.`,
    },
    text: {
      vi: `Xin chúc mừng, bạn đã hoàn thành ${tasker.nDoneTaskInMonth} giờ làm việc trong tháng trước. Bạn đã được tặng ${accounting.formatNumber(award.gift)} VND vào tài khoản khuyến mãi.`,
      en: `Congratulation! you have done ${tasker.nDoneTaskInMonth} hours last month. As a reward, ${accounting.formatNumber(award.gift)} VND has been added to your promotion account.`,
      ko: `Congratulation! you have done ${tasker.nDoneTaskInMonth} hours last month. As a reward, ${accounting.formatNumber(award.gift)} VND has been added to your promotion account.`,
      th: `Congratulation! you have done ${tasker.nDoneTaskInMonth} hours last month. As a reward, ${accounting.formatNumber(award.gift)} VND has been added to your promotion account.`,
    },
    payload: {},
  }, {
    isForceView: true,
    type: 23
  });
};

const rewardTasker = () => {
  const appSetting = Meteor.appCollection.SettingSystem.findOne({});
  const awards = _.get(
    appSetting,
    'taskerMonthlyAwardByHour',
    [
      {
       "hours" : 70,
        "gift" : 150000
      }, 
      {
        "hours" : 95,
        "gift" : 200000
      }, 
      {
        "hours" : 115,
        "gift" : 250000
      }, 
      {
        "hours" : 130,
        "gift" : 300000
      }, 
      {
        "hours" : 150,
        "gift" : 350000
      }
    ]);
  let countTasker = 0;
  let totalAmount = 0;
  let detailLog = '';
  
  const ignoreServiceIds = Meteor.appCollection.Service.find({
    'text.en': {$in: ['Laundry', 'Air-conditioner Service']}
  }, {fields: {_id: 1}}).map(service => service._id);
  
  let ignoreTaskerIds = Meteor.appCollection.ServiceChannel.find({
    serviceId: {$in: ignoreServiceIds},
  }).fetch().reduce((a, b) => {return a.concat(b.taskerList)}, []);

  let violateDateStart = moment().subtract(1, 'day').startOf('month').toDate();
  let violateDateEnd = moment(violateDateStart).endOf('month').toDate();
  violateDateStart = revertTimeZone(violateDateStart);
  violateDateEnd = revertTimeZone(violateDateEnd);
  
  
  
  const violateTaskerIds = Meteor.appCollection.TaskerViolate.find({
    violateDate: {$gte: violateDateStart, $lte: violateDateEnd}
  }, {fields: {taskerId: 1}}).map(violate => violate.taskerId);

  ignoreTaskerIds = ignoreTaskerIds.concat(violateTaskerIds);
  
  Meteor.appCollection.users.find({
    type: 'TASKER',
    status: 'ACTIVE',
    avgRating: {$gte: 4.8},
    nDoneTaskInMonth: {$gt: 0},
  }, {fields: {
    nDoneTaskInMonth: 1,
    fAccountId: 1,
    phone: 1,
    avgRating: 1,
  }}).forEach((tasker) => {
    if ( ignoreTaskerIds.indexOf(tasker._id ) < 0) {
      let b = true;
      Array.from(awards).reverse().forEach((award) => {
        if (tasker.nDoneTaskInMonth >= award.hours && b) { // reward a
          const rewardId = Meteor.appCollection.Reward.insert({
            userId: tasker._id,
            type: 'TASKER_MONTHLY_REWARD',
            award: award.gift,
            data: {
              nTaskDone: tasker.nDoneTaskInMonth,
              avgRating: tasker.avgRating,
            },
            isShow: true,
            isViewed: false,
            isGiven: false,
            createdAt: new Date(),
          });
          Meteor.appCollection.users.update({_id: tasker._id}, { $set: { nDoneTaskInMonth: 0 } });
          depositAward(tasker, award, rewardId);
          countTasker += 1;
          totalAmount += award.gift;
          detailLog += `${tasker.phone} ${tasker.nDoneTaskInMonth}  ${award.gift}\n`
          b = false;
        }
      });
    }
  });
  
  // Send to slack for Tasker team follow and check the rewards
  const text = `
    Đã hoàn tất việc thưởng Tasker hàng tháng. Tổng số Taskers: ${accounting.formatNumber(countTasker)}. Tổng tiền thưởng: ${accounting.formatNumber(totalAmount)}
  `;
  
  postToSlack({
    channel: 'tasker-reward',
    text,
  });
};

const resetMonthlyReward = () => {
  Meteor.appCollection.users.update({}, { $set: { nDoneTaskInMonth: 0 } }, { multi: true });
};

const runReward = () => {
  return;
  var message = null;
  try {
    // rewardAsker();
    rewardTasker();
    resetMonthlyReward();
  } catch (e) {
    message = e;
  }
  // Tracking
  trackingScheduleStatus({name: 'MonthlyReward', nextRunAt: SyncedCron.nextScheduledAtDate('monthlyReward'), message});
};

export default function () {
  Meteor.methods({
    monthlyReward() {
      SyncedCron.add({
        name: 'monthlyReward',
        schedule(parser) {
          // at 00:15 at first day of the month, server time zone
          return parser.cron('0 15 0 1 1/1 ? *', true);
        },
        job() {
          runReward();
        }
      });
    },
    runRewardNow() {
      runReward();
    },
  });
}
