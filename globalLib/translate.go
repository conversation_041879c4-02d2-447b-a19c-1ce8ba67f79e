package globalLib

import (
	"context"
	"os"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcTranslate"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/translateMessage"
)

var GRPC_TRANSLATE_URL = map[string]string{
	"local": "localhost:14101",
	"dev":   "go-translate.kong.svc.cluster.local:81",
	"test":  "localhost:14101",
	"prod":  "go-translate.kong.svc.cluster.local:81",
	"ci":    "go-translate:14101",
}

func TranslateFromGoogle(text string, language string, _ string) (map[string]interface{}, error) {
	// Get mode
	mode := os.Getenv("APPLICATION_MODE")

	result := make(map[string]interface{})
	client, connect, err := grpcTranslate.ConnectGRPCTranslate(GRPC_TRANSLATE_URL[mode])
	if err != nil {
		return result, err
	}
	defer connect.Close()

	translateRequest := &translateMessage.TranslateRequest{
		Text:     text,
		Language: language,
	}
	translateResponse, err := client.Translate(context.Background(), translateRequest)
	if err != nil {
		return nil, err
	}

	result["translatedText"] = translateResponse.GetTranslatedText()
	return result, err
}

func DetectLanguage(text string, _ string) (map[string]interface{}, error) {
	// Get mode
	mode := os.Getenv("APPLICATION_MODE")

	result := make(map[string]interface{})
	client, connect, err := grpcTranslate.ConnectGRPCTranslate(GRPC_TRANSLATE_URL[mode])
	if err != nil {
		return result, err
	}
	defer connect.Close()

	detectLanguageRequest := &translateMessage.DetectLanguageRequest{
		Text: text,
	}
	detectLanguageResponse, err := client.DetectLanguage(context.Background(), detectLanguageRequest)
	if err != nil {
		return result, err
	}

	result["language"] = detectLanguageResponse.GetLanguage()
	result["confidence"] = detectLanguageResponse.GetConfidence()
	return result, err
}
