// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcPromotionMY

import (
	context "context"
	promotionResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/promotionResponse"
	signUpPromotionRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/signUpPromotionRequest"
	task "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// PromotionClient is the client API for Promotion service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PromotionClient interface {
	CheckPostTaskPromotion(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error)
	CheckSignUpPromotion(ctx context.Context, in *signUpPromotionRequest.SignUpPromotionRequest, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error)
}

type promotionClient struct {
	cc grpc.ClientConnInterface
}

func NewPromotionClient(cc grpc.ClientConnInterface) PromotionClient {
	return &promotionClient{cc}
}

func (c *promotionClient) CheckPostTaskPromotion(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error) {
	out := new(promotionResponse.PromotionResponse)
	err := c.cc.Invoke(ctx, "/grpcPromotionMY.Promotion/CheckPostTaskPromotion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *promotionClient) CheckSignUpPromotion(ctx context.Context, in *signUpPromotionRequest.SignUpPromotionRequest, opts ...grpc.CallOption) (*promotionResponse.PromotionResponse, error) {
	out := new(promotionResponse.PromotionResponse)
	err := c.cc.Invoke(ctx, "/grpcPromotionMY.Promotion/CheckSignUpPromotion", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PromotionServer is the server API for Promotion service.
// All implementations must embed UnimplementedPromotionServer
// for forward compatibility
type PromotionServer interface {
	CheckPostTaskPromotion(context.Context, *task.Task) (*promotionResponse.PromotionResponse, error)
	CheckSignUpPromotion(context.Context, *signUpPromotionRequest.SignUpPromotionRequest) (*promotionResponse.PromotionResponse, error)
	mustEmbedUnimplementedPromotionServer()
}

// UnimplementedPromotionServer must be embedded to have forward compatible implementations.
type UnimplementedPromotionServer struct {
}

func (UnimplementedPromotionServer) CheckPostTaskPromotion(context.Context, *task.Task) (*promotionResponse.PromotionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckPostTaskPromotion not implemented")
}
func (UnimplementedPromotionServer) CheckSignUpPromotion(context.Context, *signUpPromotionRequest.SignUpPromotionRequest) (*promotionResponse.PromotionResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckSignUpPromotion not implemented")
}
func (UnimplementedPromotionServer) mustEmbedUnimplementedPromotionServer() {}

// UnsafePromotionServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PromotionServer will
// result in compilation errors.
type UnsafePromotionServer interface {
	mustEmbedUnimplementedPromotionServer()
}

func RegisterPromotionServer(s *grpc.Server, srv PromotionServer) {
	s.RegisterService(&_Promotion_serviceDesc, srv)
}

func _Promotion_CheckPostTaskPromotion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(task.Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionServer).CheckPostTaskPromotion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPromotionMY.Promotion/CheckPostTaskPromotion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionServer).CheckPostTaskPromotion(ctx, req.(*task.Task))
	}
	return interceptor(ctx, in, info, handler)
}

func _Promotion_CheckSignUpPromotion_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(signUpPromotionRequest.SignUpPromotionRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PromotionServer).CheckSignUpPromotion(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcPromotionMY.Promotion/CheckSignUpPromotion",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PromotionServer).CheckSignUpPromotion(ctx, req.(*signUpPromotionRequest.SignUpPromotionRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Promotion_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcPromotionMY.Promotion",
	HandlerType: (*PromotionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CheckPostTaskPromotion",
			Handler:    _Promotion_CheckPostTaskPromotion_Handler,
		},
		{
			MethodName: "CheckSignUpPromotion",
			Handler:    _Promotion_CheckSignUpPromotion_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "promotionMY.proto",
}
