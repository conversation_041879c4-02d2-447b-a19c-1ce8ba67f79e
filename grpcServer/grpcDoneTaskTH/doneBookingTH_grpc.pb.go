// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcDoneTaskTH

import (
	context "context"
	doneBookingRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/doneBookingRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// DoneBookingTHClient is the client API for DoneBookingTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DoneBookingTHClient interface {
	PartnerDone(ctx context.Context, in *doneBookingRequest.DoneBookingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type doneBookingTHClient struct {
	cc grpc.ClientConnInterface
}

func NewDoneBookingTHClient(cc grpc.ClientConnInterface) DoneBookingTHClient {
	return &doneBookingTHClient{cc}
}

func (c *doneBookingTHClient) PartnerDone(ctx context.Context, in *doneBookingRequest.DoneBookingRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/grpcDoneTaskTH.DoneBookingTH/PartnerDone", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DoneBookingTHServer is the server API for DoneBookingTH service.
// All implementations must embed UnimplementedDoneBookingTHServer
// for forward compatibility
type DoneBookingTHServer interface {
	PartnerDone(context.Context, *doneBookingRequest.DoneBookingRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedDoneBookingTHServer()
}

// UnimplementedDoneBookingTHServer must be embedded to have forward compatible implementations.
type UnimplementedDoneBookingTHServer struct {
}

func (UnimplementedDoneBookingTHServer) PartnerDone(context.Context, *doneBookingRequest.DoneBookingRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PartnerDone not implemented")
}
func (UnimplementedDoneBookingTHServer) mustEmbedUnimplementedDoneBookingTHServer() {}

// UnsafeDoneBookingTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DoneBookingTHServer will
// result in compilation errors.
type UnsafeDoneBookingTHServer interface {
	mustEmbedUnimplementedDoneBookingTHServer()
}

func RegisterDoneBookingTHServer(s *grpc.Server, srv DoneBookingTHServer) {
	s.RegisterService(&_DoneBookingTH_serviceDesc, srv)
}

func _DoneBookingTH_PartnerDone_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(doneBookingRequest.DoneBookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DoneBookingTHServer).PartnerDone(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDoneTaskTH.DoneBookingTH/PartnerDone",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DoneBookingTHServer).PartnerDone(ctx, req.(*doneBookingRequest.DoneBookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _DoneBookingTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcDoneTaskTH.DoneBookingTH",
	HandlerType: (*DoneBookingTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PartnerDone",
			Handler:    _DoneBookingTH_PartnerDone_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "doneBookingTH.proto",
}
