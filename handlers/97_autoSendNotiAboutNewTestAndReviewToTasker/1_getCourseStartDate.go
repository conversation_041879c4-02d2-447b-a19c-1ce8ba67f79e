package autoSendNotiAboutNewTestAndReviewToTasker

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTrainingV2 "gitlab.com/btaskee/go-services-model-v2/grpcmodel/trainingTaskerV2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

// get all record ob course start date which is inserted yesterday to send notification about new test
func getCoursesStartDate() []*modelTrainingV2.CourseStartDate {
	var coursesStartDate []*modelTrainingV2.CourseStartDate
	yesterday := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1)
	startOfDay := globalLib.StartADay(yesterday)
	endOfDay := globalLib.EndADay(yesterday)
	query := bson.M{
		"startDate": bson.M{
			"$gte": startOfDay,
			"$lte": endOfDay,
		},
		"isUnblocked": bson.M{"$exists": false},
		"$or": []bson.M{
			{"isSentNotiNewCourse": bson.M{"$exists": false}},
			{"isSentNotiNewCourse": false},
		},
	}
	fields := bson.M{
		"courseId": 1,
		"taskerId": 1,
	}
	coursesStartDate, err := handlers.GetCoursesStartDate(query, fields)
	if err != nil && err != mongo.ErrNoDocuments {
		text := fmt.Sprintf("AutoSendNotiAboutNewTestAndReviewToAsker %s: Lỗi call API SendNotiAboutNewTestAndReviewToAsker , err: %s ", local.ISO_CODE, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], text)
		return nil
	}
	return coursesStartDate
}
