# gRPC Send Task Integration

## 📋 Overview
Successfully integrated gRPC call to `go-send-task-vn-v3` service's `NewTask` function in the subscription handler, replacing the placeholder implementation with a real gRPC client call.

## 🔄 Implementation Changes

### Before (Placeholder)
```go
func sendTaskToTasker(taskID, serviceID string, favouriteTasker []string) {
    go func() {
        // Simplified version - in real implementation, this would call the gRPC service
        log.Printf("Sending task %s (service: %s) to taskers. Favourite taskers: %v", taskID, serviceID, favouriteTasker)

        // In the real implementation, you would:
        // 1. Connect to the send task gRPC service
        // 2. Send the task to available taskers
        // 3. Handle the response
    }()
}
```

### After (Real gRPC Implementation)
```go
func sendTaskToTasker(taskID, serviceID string, favouriteTasker []string) {
    go func() {
        defer func() {
            if r := recover(); r != nil {
                log.Printf("Recovered from panic in sendTaskToTasker: %v", r)
            }
        }()

        log.Printf("Sending task %s (service: %s) to taskers. Favourite taskers: %v", taskID, serviceID, favouriteTasker)

        // Get config
        cfg := config.GetConfig()

        // Connect to gRPC send task service
        client, connect, err := grpcSendTaskVN.ConnectGRPCSendTaskVN(cfg.GRPC_Send_Task_URL)
        if err != nil {
            log.Printf("Error connecting to send task gRPC service: %v", err)
            globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, 
                fmt.Sprintf("Error connecting to send task gRPC service: taskId=%s, error=%v", taskID, err))
            return
        }
        defer connect.Close()

        // Create request
        request := &modelPushNotificationNewTask.NewTaskRequest{
            Service: &modelPushNotificationNewTask.NewTaskRequestService{
                XId: serviceID,
            },
            Booking: &modelPushNotificationNewTask.NewTaskRequestBooking{
                XId:     taskID,
                IsoCode: local.ISO_CODE,
            },
            FavouriteTasker: favouriteTasker,
        }

        // Create context with timeout
        ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
        defer cancel()

        // Call NewTask gRPC method
        resp, err := client.NewTask(ctx, request)
        if err != nil {
            log.Printf("Error calling NewTask gRPC: %v", err)
            globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME,
                fmt.Sprintf("Error calling NewTask gRPC: taskId=%s, error=%v", taskID, err))
            return
        }

        // Check response
        if resp != nil && resp.Error != nil {
            log.Printf("NewTask gRPC returned error: %s", resp.Error.Code)
            globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME,
                fmt.Sprintf("NewTask gRPC error: taskId=%s, error=%s", taskID, resp.Error.Code))
            return
        }

        log.Printf("Successfully sent task %s to taskers via gRPC", taskID)
    }()
}
```

## 🎯 Key Features

### 1. **gRPC Service Integration**
- **Service**: `PushNotificationNewTaskVN`
- **Method**: `NewTask`
- **Endpoint**: Configured via `GRPC_Send_Task_URL`
- **Repository**: `go-send-task-vn-v3`

### 2. **Request Structure**
```go
type NewTaskRequest struct {
    Service         *NewTaskRequestService  // Service ID
    Booking         *NewTaskRequestBooking  // Task ID + ISO Code
    FavouriteTasker []string               // Favourite tasker list
}
```

### 3. **Connection Management**
```go
// Connect to gRPC service
client, connect, err := grpcSendTaskVN.ConnectGRPCSendTaskVN(cfg.GRPC_Send_Task_URL)
if err != nil {
    // Handle connection error
}
defer connect.Close() // Ensure connection cleanup
```

### 4. **Timeout Handling**
```go
// 30-second timeout for gRPC calls
ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
defer cancel()

resp, err := client.NewTask(ctx, request)
```

### 5. **Error Handling & Monitoring**
- **Connection Errors**: Logged and posted to Slack
- **gRPC Call Errors**: Logged and posted to Slack  
- **Response Errors**: Checked and handled appropriately
- **Panic Recovery**: Prevents crashes in goroutine

## 🔧 Configuration

### Environment-Specific Endpoints
| Environment | Endpoint |
|-------------|----------|
| **Local** | `localhost:21101` |
| **Test** | `localhost:21101` |
| **CI** | `go-send-task-vn-v3:21101` |
| **Dev** | `go-send-task-vn-v3.kong.svc.cluster.local:81` |
| **Prod** | `go-send-task-vn-v3.kong.svc.cluster.local:81` |

### Config Key
```yaml
grpc_send_task_vn_v3_url: <endpoint>
```

## 📊 Request/Response Flow

### 1. **Request Creation**
```go
request := &modelPushNotificationNewTask.NewTaskRequest{
    Service: &modelPushNotificationNewTask.NewTaskRequestService{
        XId: serviceID, // e.g., "60f1b2c3d4e5f6789abcdef0"
    },
    Booking: &modelPushNotificationNewTask.NewTaskRequestBooking{
        XId:     taskID,     // e.g., "60f1b2c3d4e5f6789abcdef1"
        IsoCode: "VN",       // Always VN for this service
    },
    FavouriteTasker: favouriteTasker, // e.g., ["tasker1", "tasker2"]
}
```

### 2. **gRPC Call**
```go
resp, err := client.NewTask(ctx, request)
```

### 3. **Response Handling**
```go
if resp != nil && resp.Error != nil {
    // Handle business logic errors
    log.Printf("NewTask gRPC returned error: %s", resp.Error.Code)
} else {
    // Success
    log.Printf("Successfully sent task %s to taskers via gRPC", taskID)
}
```

## 🛡️ Error Handling Strategy

### 1. **Connection Errors**
```go
if err != nil {
    log.Printf("Error connecting to send task gRPC service: %v", err)
    globalLib.PostToSlack(/* Slack notification */)
    return // Exit gracefully
}
```

### 2. **gRPC Call Errors**
```go
if err != nil {
    log.Printf("Error calling NewTask gRPC: %v", err)
    globalLib.PostToSlack(/* Slack notification */)
    return // Exit gracefully
}
```

### 3. **Business Logic Errors**
```go
if resp != nil && resp.Error != nil {
    log.Printf("NewTask gRPC returned error: %s", resp.Error.Code)
    globalLib.PostToSlack(/* Slack notification */)
    return // Exit gracefully
}
```

### 4. **Panic Recovery**
```go
defer func() {
    if r := recover(); r != nil {
        log.Printf("Recovered from panic in sendTaskToTasker: %v", r)
    }
}()
```

## 📈 Monitoring & Logging

### 1. **Success Logging**
```go
log.Printf("Successfully sent task %s to taskers via gRPC", taskID)
```

### 2. **Error Logging**
```go
log.Printf("Error connecting to send task gRPC service: %v", err)
log.Printf("Error calling NewTask gRPC: %v", err)
log.Printf("NewTask gRPC returned error: %s", resp.Error.Code)
```

### 3. **Slack Notifications**
All errors are automatically posted to Slack for immediate CS team awareness:
```go
globalLib.PostToSlack(
    cfg.SlackToken,
    globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE],
    globalConstant.SLACK_USER_NAME,
    fmt.Sprintf("Error message with context: taskId=%s, error=%v", taskID, err),
)
```

## 🔄 Integration Points

### 1. **Called From**
```go
// In handleTaskerAssignment function
if !user.IsBlacklist {
    sendTaskToTasker(taskID, subs.ServiceId, user.FavouriteTasker)
}
```

### 2. **Dependencies**
- **Config**: `config.GetConfig()` for endpoint
- **gRPC Client**: `grpcSendTaskVN.ConnectGRPCSendTaskVN()`
- **Models**: `modelPushNotificationNewTask.NewTaskRequest`
- **Monitoring**: `globalLib.PostToSlack()` for error notifications

### 3. **Async Execution**
Function runs in a goroutine to avoid blocking the main subscription processing flow:
```go
go func() {
    // gRPC call implementation
}()
```

## ✅ Benefits

### 1. **Real Integration**
- Actual gRPC calls to production service
- No more placeholder implementations
- Full end-to-end task distribution

### 2. **Reliability**
- Proper error handling and recovery
- Connection management with cleanup
- Timeout protection (30 seconds)

### 3. **Monitoring**
- Comprehensive logging for debugging
- Slack notifications for immediate error awareness
- Panic recovery prevents service crashes

### 4. **Performance**
- Asynchronous execution (non-blocking)
- Efficient connection management
- Proper resource cleanup

## 🎯 Usage Example

### Subscription Task Flow
1. **Task Created**: Subscription handler creates a new task
2. **Tasker Assignment**: Determines if task should be sent to taskers
3. **gRPC Call**: `sendTaskToTasker()` calls `go-send-task-vn-v3`
4. **Task Distribution**: Service distributes task to available taskers
5. **Notifications**: Taskers receive push notifications about new task

### Sample Call
```go
sendTaskToTasker(
    "60f1b2c3d4e5f6789abcdef1",           // taskID
    "60f1b2c3d4e5f6789abcdef0",           // serviceID  
    []string{"tasker1", "tasker2"},        // favouriteTasker
)
```

The gRPC integration is now complete and provides robust task distribution functionality for subscription-based tasks!
