package autoUpgradeMedalForCommunityUser

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.mongodb.org/mongo-driver/bson"
)

func countNumberOrderService(askerId string) (count int64) {
	now := globalLib.GetCurrentTime(local.TimeZone)
	sixMonthAgo := now.AddDate(0, -6, 0)
	query := bson.M{
		"askerId": askerId,
		"isoCode": local.ISO_CODE,
		"status":  globalConstant.TASK_STATUS_DONE,
		"date":    bson.M{"$gte": sixMonthAgo},
	}

	count, err := globalDataAccess.CountByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], query)
	if err != nil {
		textErr := fmt.Sprintf("[Sync Cron VN v3] AutoUpgradeMedalForCommunityUser %s: can not count task, err: %s ", local.ISO_CODE, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], textErr)
		return 0
	}
	return count
}
