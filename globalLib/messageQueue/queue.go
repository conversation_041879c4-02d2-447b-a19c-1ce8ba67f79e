package messageQueue

import (
	"errors"

	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/messageQueue/bkafka"
)

type Queue interface {
	NewProducer(configMap map[string]interface{}) error
	ProduceMessage(topic string, message []byte) error
	NewConsumerGroup(topic string, handler, retryHandler func([]byte) error, configMap map[string]interface{}) error
}

func NewMessageQueue(queueType string) (Queue, error) {
	switch queueType {
	case globalConstant.MESSAGE_QUEUE_TYPE_KAFKA:
		return bkafka.NewKafkaMQ()
	default:
		return nil, errors.New("invalid message queue type")
	}
}
