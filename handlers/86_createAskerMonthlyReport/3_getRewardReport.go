package createAskerMonthlyReport

import (
	"sync"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelPointTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pointTransaction"
	"go.mongodb.org/mongo-driver/bson"
)

// getAskerbPoint returns a channel of AskerYearEndReport objects.
// It takes in a WaitGroup pointer, a channel of TaskerYearEndReport objects,
// and startYear and endYear time.Time values as parameters.
// The returned channel contains TaskerYearEndReport objects with updated
// TotalBPoint and TotalMonthlyRewardAmount values.
func getAskerbPoint(w *sync.WaitGroup, dataChan chan *modelReport, startAMonth, endAMonth time.Time) chan *modelReport {
	// Create a channel to store the updated TaskerYearEndReport objects
	outc := make(chan *modelReport, 100)

	// Start a goroutine to process the TaskerYearEndReport objects
	go func() {
		defer w.Done()
		// Close the output channel when the input channel is closed
		defer close(outc)

		// Process each TaskerYearEndReport object from the input channel
		for v := range dataChan {
			// Update the TotalBPoint value of the TaskerYearEndReport object
			if v.MonthlyReportInfo != nil {
				v.MonthlyReportInfo.TotalAccumulatedbPoint, v.MonthlyReportInfo.TotalUsedbPoint = getTotalbPointByAsker(v.AskerId, startAMonth, endAMonth)
			}

			// Send the updated TaskerYearEndReport object to the output channel
			outc <- v
		}
	}()

	// Return the output channel
	return outc
}

// getTotalBPointByTasker calculates the total BPoint earned by a tasker within a given time period.
// It takes the tasker ID, start year, and end year as input parameters.
// It returns the total BPoint earned by the tasker.
func getTotalbPointByAsker(askerId string, startAMonth, endAMonth time.Time) (float64, float64) {
	// Define the query to filter the point transactions.
	query := bson.M{
		"userId":    askerId,
		"createdAt": bson.M{"$gte": startAMonth, "$lte": endAMonth},
	}
	// Retrieve the point transactions that match the query.
	var pointTrans []*modelPointTransaction.PointTransaction
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_POINT_TRANSACTION[local.ISO_CODE], query, bson.M{"point": 1, "type": 1}, &pointTrans)
	// Calculate the total point earned by summing up the points from each transaction.
	var totalAccumulatedbPoint, totalUsedbPoint float64 = 0, 0
	for _, tran := range pointTrans {
		if tran.Type == "C" {
			totalUsedbPoint += tran.Point
		} else {
			totalAccumulatedbPoint += tran.Point
		}
	}

	return totalAccumulatedbPoint, totalUsedbPoint
}
