package autoCheckIncentive

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelIncentive "gitlab.com/btaskee/go-services-model-v2/grpcmodel/incentive"
	"go.mongodb.org/mongo-driver/bson"
)

func getGiftNeedInactiveFromIncentive(itemsNeedUpdateStatus []UrBoxGift) ([]string, []string) {
	// Get gift need update status
	giftIds := []string{}
	for _, gift := range itemsNeedUpdateStatus {
		giftIds = append(giftIds, gift.ID)
	}
	// Get incentives
	var incentives []*modelIncentive.Incentive
	globalDataAccess.GetAllByQuery(
		globalCollection.COLLECTION_INCENTIVE[local.ISO_CODE],
		bson.M{
			"from":        globalConstant.GIFT_SOURCE_SYSTEM_WITH_PARTNER,
			"partner":     "UR_BOX",
			"status":      "ACTIVE",
			"giftInfo.id": bson.M{"$in": giftIds},
		},
		bson.M{"status": 1, "giftInfo": 1},
		&incentives,
	)
	// Get list gift id need inactive
	listGiftIdNeedInactive := []string{}
	listIncentiveIdNeedInactive := []string{}
	for _, v := range incentives {
		if globalLib.FindStringInSlice(giftIds, v.GiftInfo.Id) >= 0 {
			listGiftIdNeedInactive = append(listGiftIdNeedInactive, v.GiftInfo.Id)
			listIncentiveIdNeedInactive = append(listIncentiveIdNeedInactive, v.XId)
		}
	}
	// return
	return listGiftIdNeedInactive, listIncentiveIdNeedInactive
}
