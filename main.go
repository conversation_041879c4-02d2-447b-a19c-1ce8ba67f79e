package main

import (
	"fmt"
	"log"
	"os"
	"os/signal"
	"syscall"

	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/dbConfig"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"google.golang.org/grpc"
)

var cfg = config.GetConfig()

func main() {
	// Init Mongo
	cfg.MongoDriver.Get("btaskee").InitDriver(true)

	// Create new server
	grpcServer := service.NewGRPCServer()

	// Start GRPC server
	go func() {
		err := service.StartGRPCServer(grpcServer, cfg.GRPC_Port)
		if err != nil {
			log.Println(err.Error())
		}
	}()

	// Start HTTP Server
	go func() {
		err := service.StartRESTServer(cfg.REST_Port, cfg.GRPC_Port)
		if err != nil {
			log.Println(err.Error())
		}
	}()

	// Graceful shutdown
	handleSigterm(grpcServer)
}

func handleSigterm(grpcServer *grpc.Server) {
	globalLib.PostToSlack(cfg.SlackToken, "go-services", globalConstant.SLACK_USER_NAME, fmt.Sprintf("[SERVICE RUNNING] - %s", local.SERVICE_NAME))
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt)
	signal.Notify(c, syscall.SIGTERM)
	<-c

	// Shutdown GRPC server
	grpcServer.GracefulStop()

	dbConfig.DisconnectDB(globalConstant.ISO_CODE_VN, cfg.SlackToken, local.SERVICE_NAME)

	close(c)
}
