package grpcPushNotificationVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPushNotificationVN"
	"google.golang.org/grpc"
)

func ConnectGRPCPushNotificationVN(endpoint string) (grpcPushNotificationVN.PushNotificationVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPushNotificationVN.NewPushNotificationVNClient(connect)
	return client, connect, err
}
