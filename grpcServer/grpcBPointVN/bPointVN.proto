syntax = "proto3";
package grpcBPointVN;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcBPointVN";

import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response/response.proto";

message BPointRequest {
  string taskId = 1;
  string taskerId = 2;
  double point = 3;
  string specialCampaignId = 4;
  string userId = 5;
  string sourceName = 6;
}

service BPointVN {
  rpc AccumulatePoints (BPointRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/bpoint-vn/accumulate"
      body: "*"
    };
  }

  rpc TaskerAccumulatePoints (BPointRequest) returns (response.Response) {}

  rpc GivePointTaskerDoneSpecialCampaign (BPointRequest) returns (response.Response) {}
  
  rpc AddUserPoint (BPointRequest) returns (response.Response) {}
}