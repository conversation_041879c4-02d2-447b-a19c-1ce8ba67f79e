package grpcPaymentTH

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcPaymentTH"
	"google.golang.org/grpc"
)

func ConnectGRPCPaymentTH(endpoint string) (grpcPaymentTH.PaymentTHClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPaymentTH.NewPaymentTHClient(connect)
	return client, connect, err
}
