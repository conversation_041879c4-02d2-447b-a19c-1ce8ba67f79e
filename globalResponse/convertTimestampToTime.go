package globalResponse

import (
	"encoding/json"
	"reflect"
	"time"
)
func Translate(obj interface{}) interface{} {
	// typeObj := reflect.TypeOf(obj)
	// if typeObj.String() == "[]map[string]interface {}" || typeObj.String() == "map[string]interface {}" {
	// 	return obj
	// }

	b, _ := json.Marshal(obj)

	// Wrap the original in a reflect.Value
	original := reflect.ValueOf(obj)
	switch original.Kind() {
	case reflect.Slice:
		var array []map[string]interface{}
		json.Unmarshal(b, &array)

		original := reflect.ValueOf(array)

		typeArray := reflect.TypeOf(array)
		copy := reflect.New(typeArray).Elem()

		translateRecursive(copy, original)

		// Remove the reflection wrapper
		return copy.Interface()
	case reflect.Map, reflect.Interface, reflect.Ptr:
		var item map[string]interface{}
		json.Unmarshal(b, &item)

		original := reflect.ValueOf(item)

		typeItem := reflect.TypeOf(item)
		copy := reflect.New(typeItem).Elem()

		translateRecursive(copy, original)

		// Remove the reflection wrapper
		return copy.Interface()
	default:
		return obj
	}
}

func translateRecursive(copy, original reflect.Value) {
	switch original.Kind() {
	// The first cases handle nested structures and translate them recursively

	// If it is a pointer we need to unwrap and call once again
	case reflect.Ptr:
		// To get the actual value of the original we have to call Elem()
		// At the same time this unwraps the pointer so we don't end up in
		// an infinite recursion
		originalValue := original.Elem()
		// Check if the pointer is nil
		if !originalValue.IsValid() {
			return
		}
		// Allocate a new object and set the pointer to it
		copy.Set(reflect.New(originalValue.Type()))
		// Unwrap the newly created pointer
		translateRecursive(copy.Elem(), originalValue)

	// If it is an interface (which is very similar to a pointer), do basically the
	// same as for the pointer. Though a pointer is not the same as an interface so
	// note that we have to call Elem() after creating a new object because otherwise
	// we would end up with an actual pointer
	case reflect.Interface:
		// Get rid of the wrapping interface
		originalValue := original.Elem()
		// Check if the pointer is nil
		if !originalValue.IsValid() {
			return
		}
		var isDate bool
		if originalValue.Kind() == reflect.Map {
			for _, key := range originalValue.MapKeys() {
				newOriginal := originalValue.MapIndex(key)
				if key.String() == "seconds" {
					if v, ok := newOriginal.Interface().(float64); ok {
						isDate = true
						strTime := time.Unix(int64(v), 0).Format(time.RFC3339)
						newTime, _ := time.Parse(time.RFC3339, strTime)
						copy.Set(reflect.ValueOf(newTime))
						break
					}
				}
			}
		}
		if !isDate {
			// Create a new object. Now new gives us a pointer, but we want the value it
			// points to, so we have to call Elem() to unwrap it
			copyValue := reflect.New(originalValue.Type()).Elem()
			translateRecursive(copyValue, originalValue)
			copy.Set(copyValue)
		}

	// If it is a struct we translate each field
	case reflect.Struct:
		for i := 0; i < original.NumField(); i += 1 {
			field := original.Field(i)
			if field.CanSet() {
				translateRecursive(copy.Field(i), field)
			}
		}

	// If it is a slice we create a new slice and translate each element
	case reflect.Slice:
		copy.Set(reflect.MakeSlice(original.Type(), original.Len(), original.Cap()))
		for i := 0; i < original.Len(); i += 1 {
			translateRecursive(copy.Index(i), original.Index(i))
		}

	// If it is a map we create a new map and translate each value
	case reflect.Map:
		if original.Len() == 0 {
			copy.Set(reflect.Zero(original.Type()))
		} else {
			copy.Set(reflect.MakeMap(original.Type()))
			for _, key := range original.MapKeys() {
				originalValue := original.MapIndex(key)
				// New gives us a pointer, but again we want the value
				copyValue := reflect.New(originalValue.Type()).Elem()
				translateRecursive(copyValue, originalValue)
				copy.SetMapIndex(key, copyValue)
			}
		}

	// Otherwise we cannot traverse anywhere so this finishes the the recursion

	// If it is a string translate it (yay finally we're doing what we came for)
	// case reflect.String:
	// 	translatedString := "ABC"
	// 	copy.SetString(translatedString)

	// And everything else will simply be taken from the original
	default:
		copy.Set(original)
	}
}

// ======================================

// func translate(obj interface{}) interface{} {
// 	// Wrap the original in a reflect.Value
// 	original := reflect.ValueOf(obj)

// 	copy := reflect.New(original.Type()).Elem()
// 	translateRecursive(copy, original)

// 	// Remove the reflection wrapper
// 	return copy.Interface()
// }

// func translateRecursive(copy, original reflect.Value) {
// 	switch original.Kind() {
// 	// The first cases handle nested structures and translate them recursively

// 	// If it is a pointer we need to unwrap and call once again
// 	case reflect.Ptr:
// 		// To get the actual value of the original we have to call Elem()
// 		// At the same time this unwraps the pointer so we don't end up in
// 		// an infinite recursion
// 		originalValue := original.Elem()
// 		// Check if the pointer is nil
// 		if !originalValue.IsValid() {
// 			return
// 		}
// 		// Allocate a new object and set the pointer to it
// 		copy.Set(reflect.New(originalValue.Type()))
// 		// Unwrap the newly created pointer
// 		translateRecursive(copy.Elem(), originalValue)

// 	// If it is an interface (which is very similar to a pointer), do basically the
// 	// same as for the pointer. Though a pointer is not the same as an interface so
// 	// note that we have to call Elem() after creating a new object because otherwise
// 	// we would end up with an actual pointer
// 	case reflect.Interface:
// 		// Get rid of the wrapping interface
// 		originalValue := original.Elem()
// 		// Create a new object. Now new gives us a pointer, but we want the value it
// 		// points to, so we have to call Elem() to unwrap it
// 		copyValue := reflect.New(originalValue.Type()).Elem()
// 		translateRecursive(copyValue, originalValue)
// 		copy.Set(copyValue)

// 	// If it is a struct we translate each field
// 	case reflect.Struct:
// 		for i := 0; i < original.NumField(); i += 1 {
// 			translateRecursive(copy.Field(i), original.Field(i))
// 		}

// 	// If it is a slice we create a new slice and translate each element
// 	case reflect.Slice:
// 		copy.Set(reflect.MakeSlice(original.Type(), original.Len(), original.Cap()))
// 		for i := 0; i < original.Len(); i += 1 {
// 			translateRecursive(copy.Index(i), original.Index(i))
// 		}

// 	// If it is a map we create a new map and translate each value
// 	case reflect.Map:
// 		copy.Set(reflect.MakeMap(original.Type()))
// 		for _, key := range original.MapKeys() {
// 			originalValue := original.MapIndex(key)
// 			// New gives us a pointer, but again we want the value
// 			copyValue := reflect.New(originalValue.Type()).Elem()
// 			translateRecursive(copyValue, originalValue)
// 			copy.SetMapIndex(key, copyValue)
// 		}

// 	// Otherwise we cannot traverse anywhere so this finishes the the recursion

// 	// If it is a string translate it (yay finally we're doing what we came for)
// 	case reflect.String:
// 		translatedString := dict[original.Interface().(string)]
// 		copy.SetString(translatedString)

// 	// And everything else will simply be taken from the original
// 	default:
// 		copy.Set(original)
// 	}

// }
