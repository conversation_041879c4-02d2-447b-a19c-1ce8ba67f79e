package sendBusinessReportEmail

import (
	"github.com/xuri/excelize/v2"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessTransaction"
	"gitlab.com/btaskee/go-services-model-v2/localization"
)

func creditTransactionSheet(language string, f *excelize.File, creditTransactions []*businessTransaction.BusinessTransaction,
	mapMemberDetail map[string]*MemberInfo, businessName string, month, year int32) {
	sheetName := "Sheet3" // from template
	defaultHeight := 25.0
	setDefaultCellCreditSheet(language, f, sheetName, businessName, month, year)
	noColumn, dateColumn, nameColumn, phoneColumn, groupColumn, amountColumn := 1, 2, 3, 4, 5, 6 // Column index: A, B, C, D, E
	startRow := 4

	totalAmount := float64(0)
	for i, transaction := range creditTransactions {
		member := mapMemberDetail[transaction.MemberId]
		if member == nil {
			member = &MemberInfo{}
		}
		// No. column A
		cell, _ := excelize.CoordinatesToCellName(noColumn, i+startRow)
		f.SetCellValue(sheetName, cell, i+1)
		// Date column B
		cell, _ = excelize.CoordinatesToCellName(dateColumn, i+startRow)
		f.SetCellValue(sheetName, cell, transaction.GetCreatedAt().AsTime().In(local.TimeZone).Format(formatTime))
		// Name column C
		cell, _ = excelize.CoordinatesToCellName(nameColumn, i+startRow)
		f.SetCellValue(sheetName, cell, member.Name)
		// Phone column D
		cell, _ = excelize.CoordinatesToCellName(phoneColumn, i+startRow)
		f.SetCellValue(sheetName, cell, member.Phone)
		// Reason column E
		cell, _ = excelize.CoordinatesToCellName(groupColumn, i+startRow)
		f.SetCellValue(sheetName, cell, transaction.GetLevelInfo().GetLevelName())
		// Amount column F
		cell, _ = excelize.CoordinatesToCellName(amountColumn, i+startRow)
		f.SetCellValue(sheetName, cell, globalLib.FormatMoney(transaction.Amount))
		totalAmount += transaction.Amount

		f.SetRowHeight(sheetName, i+startRow, defaultHeight) // default height of template
	}

	// border
	startCell, _ := excelize.CoordinatesToCellName(noColumn, startRow)
	endCell, _ := excelize.CoordinatesToCellName(amountColumn, len(creditTransactions)+startRow)
	setBorderStyle(f, sheetName, startCell, endCell, false)

	// total amount in, bold
	lastLineNo, _ := excelize.CoordinatesToCellName(noColumn, len(creditTransactions)+startRow)
	f.SetCellValue(sheetName, lastLineNo, localization.T(language, "BUSINESS_REPORT_SHEET_3_TOTAL_AMOUNT_OUT"))
	lastLineAmount, _ := excelize.CoordinatesToCellName(amountColumn, len(creditTransactions)+startRow)
	f.SetCellValue(sheetName, lastLineAmount, globalLib.FormatMoney(totalAmount))
	setBorderStyle(f, sheetName, lastLineNo, lastLineAmount, true)
	f.SetRowHeight(sheetName, len(creditTransactions)+startRow, defaultHeight) // default height of template

}

func setDefaultCellCreditSheet(language string, f *excelize.File, sheetName string, businessName string, month, year int32) {
	f.SetCellValue(sheetName, "A1", localization.T(language, "BUSINESS_REPORT_SHEET_BTASKEE_TITLE"))
	f.SetCellValue(sheetName, "A2", localization.T(language, "BUSINESS_REPORT_SHEET_BUSINESS_NAME", businessName))
	f.SetCellValue(sheetName, "C2", localization.T(language, "BUSINESS_REPORT_SHEET_DATE", month, year))
	f.SetCellValue(sheetName, "A3", localization.T(language, "BUSINESS_REPORT_SHEET_HEADER_NO"))
	f.SetCellValue(sheetName, "B3", localization.T(language, "BUSINESS_REPORT_SHEET_HEADER_TRANSACTION_DATE"))
	f.SetCellValue(sheetName, "C3", localization.T(language, "BUSINESS_REPORT_SHEET_3_RECEIVER_NAME"))
	f.SetCellValue(sheetName, "D3", localization.T(language, "BUSINESS_REPORT_SHEET_HEADER_PHONE"))
	f.SetCellValue(sheetName, "E3", localization.T(language, "BUSINESS_REPORT_SHEET_HEADER_GROUP"))
	f.SetCellValue(sheetName, "F3", localization.T(language, "BUSINESS_REPORT_SHEET_HEADER_TRANSACTION_AMOUNT"))
}
