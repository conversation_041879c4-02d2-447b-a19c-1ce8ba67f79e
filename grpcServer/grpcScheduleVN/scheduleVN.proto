syntax = "proto3";
package grpcScheduleVN;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcScheduleVN";

import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response/response.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskSchedule/taskSchedule.proto";

service ScheduleVN {
  rpc AddHomeCleaningSchedule (taskSchedule.TaskSchedule) returns (response.Response) {}
  rpc AddHomeCookingSchedule (taskSchedule.TaskSchedule) returns (response.Response) {}
  rpc AddHousekeepingSchedule (taskSchedule.TaskSchedule) returns (response.Response) {}
  rpc AddOfficeCleaningSchedule (taskSchedule.TaskSchedule) returns (response.Response) {}
}