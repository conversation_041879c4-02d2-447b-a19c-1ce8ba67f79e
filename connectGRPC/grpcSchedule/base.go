package grpcSchedule

import (
	"gitlab.com/btaskee/**********************/grpcSchedule"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"google.golang.org/grpc"
)

func ConnectGRPCSchedule(endpoint string) (grpcSchedule.ScheduleClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcSchedule.NewScheduleClient(connect)
	return client, connect, err
}
