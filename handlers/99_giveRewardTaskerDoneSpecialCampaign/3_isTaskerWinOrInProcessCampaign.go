package giveRewardTaskerDoneSpecialCampaign

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelSpecialCampaignTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/specialCampaignTransaction"
	"go.mongodb.org/mongo-driver/bson"
)

func isTaskerWinOrInProcessCampaign(taskerId string, campaignId string) (bool, bool, error) {
	var isWin, isInProcess bool
	specialCampaignTransaction, err := getSpecialCampaignTransaction(bson.M{
		"taskerId":   taskerId,
		"campaignId": campaignId,
	}, bson.M{"status": 1})

	if specialCampaignTransaction != nil && globalLib.FindStringInSlice([]string{globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_REWARDED}, specialCampaignTransaction.Status) > 0 {
		isWin = true
	} else if specialCampaignTransaction != nil && globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_INPROCESS == specialCampaignTransaction.Status {
		isInProcess = true
	} else if specialCampaignTransaction != nil {
		// các trường hợp còn lại sẽ skip
		isWin = true
	}

	return isWin, isInProcess, err
}

func getSpecialCampaignTransaction(query, field bson.M) (specialCampaignTransaction *modelSpecialCampaignTransaction.SpecialCampaignTransaction, err error) {
	err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], query, field, &specialCampaignTransaction)
	return
}
