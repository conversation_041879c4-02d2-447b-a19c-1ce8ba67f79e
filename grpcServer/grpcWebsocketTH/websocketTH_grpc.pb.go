// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcWebsocketTH

import (
	context "context"
	websocketMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// WebsocketTHClient is the client API for WebsocketTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WebsocketTHClient interface {
	SendChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*emptypb.Empty, error)
	SendSocketMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type websocketTHClient struct {
	cc grpc.ClientConnInterface
}

func NewWebsocketTHClient(cc grpc.ClientConnInterface) WebsocketTHClient {
	return &websocketTHClient{cc}
}

func (c *websocketTHClient) SendChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/grpcWebsocketTH.WebsocketTH/SendChatMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *websocketTHClient) SendSocketMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/grpcWebsocketTH.WebsocketTH/SendSocketMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WebsocketTHServer is the server API for WebsocketTH service.
// All implementations must embed UnimplementedWebsocketTHServer
// for forward compatibility
type WebsocketTHServer interface {
	SendChatMessage(context.Context, *websocketMessage.WebsocketMessage) (*emptypb.Empty, error)
	SendSocketMessage(context.Context, *websocketMessage.WebsocketMessage) (*emptypb.Empty, error)
	mustEmbedUnimplementedWebsocketTHServer()
}

// UnimplementedWebsocketTHServer must be embedded to have forward compatible implementations.
type UnimplementedWebsocketTHServer struct {
}

func (UnimplementedWebsocketTHServer) SendChatMessage(context.Context, *websocketMessage.WebsocketMessage) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendChatMessage not implemented")
}
func (UnimplementedWebsocketTHServer) SendSocketMessage(context.Context, *websocketMessage.WebsocketMessage) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSocketMessage not implemented")
}
func (UnimplementedWebsocketTHServer) mustEmbedUnimplementedWebsocketTHServer() {}

// UnsafeWebsocketTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WebsocketTHServer will
// result in compilation errors.
type UnsafeWebsocketTHServer interface {
	mustEmbedUnimplementedWebsocketTHServer()
}

func RegisterWebsocketTHServer(s *grpc.Server, srv WebsocketTHServer) {
	s.RegisterService(&_WebsocketTH_serviceDesc, srv)
}

func _WebsocketTH_SendChatMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(websocketMessage.WebsocketMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebsocketTHServer).SendChatMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcWebsocketTH.WebsocketTH/SendChatMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebsocketTHServer).SendChatMessage(ctx, req.(*websocketMessage.WebsocketMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _WebsocketTH_SendSocketMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(websocketMessage.WebsocketMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebsocketTHServer).SendSocketMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcWebsocketTH.WebsocketTH/SendSocketMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebsocketTHServer).SendSocketMessage(ctx, req.(*websocketMessage.WebsocketMessage))
	}
	return interceptor(ctx, in, info, handler)
}

var _WebsocketTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcWebsocketTH.WebsocketTH",
	HandlerType: (*WebsocketTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendChatMessage",
			Handler:    _WebsocketTH_SendChatMessage_Handler,
		},
		{
			MethodName: "SendSocketMessage",
			Handler:    _WebsocketTH_SendSocketMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "websocketTH.proto",
}
