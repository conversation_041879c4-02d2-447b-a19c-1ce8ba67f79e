syntax = "proto3";
package grpcEmailTH;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcEmailTH";

import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse/email-response.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending/email-sending.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task/task.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users/users.proto";

service EmailTH {
  rpc SendEmail (emailSending.EmailSending) returns (emailResponse.EmailResponse) {}
  rpc SendReceiptEmail (task.Task) returns (emailResponse.EmailResponse) {
    option (google.api.http) = {
      post: "/api/v3/email-th/receipt"
      body: "*"
    };
  }
  rpc SendVerifyEmail (users.Users) returns (emailResponse.EmailResponse) {}
  rpc SendRenewSubscriptionEmail (emailSending.EmailRequest) returns (emailResponse.EmailResponse) {
    option (google.api.http) = {
      post: "/api/v3/email-th/renew-subscription"
      body: "*"
    };
  }
  rpc ResendReceiptEmail (emailSending.EmailRequest) returns (emailResponse.EmailResponse) {}
  rpc ResendSubscriptionOrderEmail (emailSending.EmailRequest) returns (emailResponse.EmailResponse) {
    option (google.api.http) = {
      post: "/api/v3/email-th/subscription-order"
      body: "*"
    };
  }
  rpc SendReceiptSubscriptionEmail (emailSending.EmailRequest) returns (emailResponse.EmailResponse) {
    option (google.api.http) = {
      post: "/api/v3/email-th/receipt-subscription"
      body: "*"
    };
  }
  rpc SendSubscriptionSuggestionEmail (emailSending.EmailRequest) returns (emailResponse.EmailResponse) {}
  rpc SendCancelFeeEmail (emailSending.EmailCancelFeeRequest) returns (emailResponse.EmailResponse) {}
  rpc SendSubscriptionRenewedEmail (emailSending.EmailRequest) returns (emailResponse.EmailResponse) {}
  rpc SendTopUpSuccessEmail (emailSending.EmailRequest) returns (emailResponse.EmailResponse) {}
  rpc SendCancelChargeFailEmail (emailSending.EmailCancelFeeRequest) returns (emailResponse.EmailResponse) {}
  rpc SendChargeFailEmail (task.Task) returns (emailResponse.EmailResponse) {}
  rpc SendChangeToCashScheduleEmail (emailSending.EmailChangeToTaskCashScheduleRequest) returns (emailResponse.EmailResponse) {}
}