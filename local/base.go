/*
 * @File: base.go
 * @Description: Define timeZone constant
 * @CreatedAt: 04/09/2020
 * @Author: linhnh
 */
package local

import (
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.uber.org/zap"
)

const (
	ISO_CODE     = globalConstant.ISO_CODE_VN
	SERVICE_NAME = "Email VN v3"
)

var (
	TimeZone  = globalLib.GetTimeZone()
	Logger, _ = zap.NewProduction()
)
