import { Meteor } from 'meteor/meteor';
import { HTTP } from 'meteor/http';
import _ from 'lodash';
import moment from 'moment';
import {
  trackingScheduleStatus,
  postToSlack,
  changeTimeZone
} from '/server/helper-function';

const runAlertPostedTask = () => {
  return;
	console.log("Start runAlertPostedTask ...");
  const hourInVN = changeTimeZone(new Date()).getHours();
  
  if ( hourInVN >= 18 || hourInVN < 8 ) {
    return;
  }

  var message = null;

  let taskArray = [];
  let taskIds = [];

  const last15Minutes = moment().subtract(15, 'minutes').toDate();

  Meteor.appCollection.Task.find({
    date: {$gte: new Date()},
    status: 'POSTED',
    createdAt: {$lt: last15Minutes}
  }, {
    fields: {
      taskPlace: 1,
      serviceText: 1,
      phone: 1,
      lastPostedAlertAt: 1
    },
    sort: {
      'taskPlace.city': 1
    }
  }).forEach((task, index) => {
    if ( task.lastPostedAlertAt ) {
      const timeToAlert = moment(task.lastPostedAlertAt).add(1, 'hour').toDate();
      if ( timeToAlert < new Date() ) {
        taskArray.push(`${task.serviceText.vi} - ${task.taskPlace.city} - ${task.taskPlace.district} - https://ops.btaskee.com/cs/task/detail/${task._id}`)
        taskIds.push(task._id);
      }
    } else {
      taskArray.push(`${task.serviceText.vi} - ${task.taskPlace.city} - ${task.taskPlace.district} - https://ops.btaskee.com/cs/task/detail/${task._id}`)
      taskIds.push(task._id);
    }
  });
  if ( taskArray.length > 0 ) { 
    postToSlack({
      channel: 'operation',
      text: `Các công việc đã POSTED hơn 15 phút:\n${taskArray.join('\n')}`,
    });
    Meteor.appCollection.Task.update({
      _id: {$in: taskIds}
    }, {
      $set: {
        lastPostedAlertAt: new Date()
      }
    }, {
      multi: true
    });
  }

	console.log("Finished runAlertPostedTask ");
  // Tracking
  trackingScheduleStatus({name: 'autoAlertPostedTask', nextRunAt: SyncedCron.nextScheduledAtDate('autoAlertPostedTask'), message});
};

  Meteor.methods({
    autoAlertPostedTask() {
      SyncedCron.add({
        name: 'autoAlertPostedTask',
        schedule(parser) {
          // Run every 10 minutes
          return parser.cron('0 0/10 * 1/1 * ? *', true);
        },
        job() {
          runAlertPostedTask();
        },
      });
	    console.log("Added autoAlertPostedTask cron");
    },
    'runNow.autoAlertPostedTask'() {
      runAlertPostedTask();
    },
  });
