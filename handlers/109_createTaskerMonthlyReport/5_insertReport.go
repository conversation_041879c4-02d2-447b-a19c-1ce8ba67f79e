package createTaskerMonthlyReport

import (
	"fmt"
	"sync"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTaskerMonthlyReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerMonthlyReport"
	"go.mongodb.org/mongo-driver/bson"
)

func processInsertDB(w *sync.WaitGroup, dataChan chan *modelReport, startAMonth time.Time) {
	go func() {
		defer w.Done()
		taskerIds := []string{}
		mapDataByTaskerId := make(map[string]*modelReport)
		mapTaskerHasMonthlyReport := make(map[string]bool)
		for data := range dataChan {
			taskerIds = append(taskerIds, data.TaskerId)
			mapDataByTaskerId[data.TaskerId] = data
			if len(taskerIds) >= 100 {
				processUpsertReport(taskerIds, startAMonth, mapDataByTaskerId, mapTaskerHasMonthlyReport)
				taskerIds = []string{}
			}
		}
		if len(taskerIds) > 0 {
			processUpsertReport(taskerIds, startAMonth, mapDataByTaskerId, mapTaskerHasMonthlyReport)
		}
	}()
}

func processUpsertReport(taskerIds []string, startAMonth time.Time, mapDataByTaskerId map[string]*modelReport, mapTaskerHasMonthlyReport map[string]bool) {
	// Get data report by askerId
	var taskerReports []*modelTaskerMonthlyReport.TaskerMonthlyReport
	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASKER_MONTHLY_REPORT[local.ISO_CODE], bson.M{
		"userId": bson.M{"$in": taskerIds}, "year": int32(startAMonth.Year()),
	}, bson.M{}, &taskerReports)
	// Post slack if err
	if err != nil {
		msg := fmt.Sprintf("CreateTaskerMonthlyReportVN Get data report err: %v, taskerIds: %v", err, taskerIds)
		globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
	}
	// Check data report map by askerId
	for _, taskerReport := range taskerReports {
		mapTaskerHasMonthlyReport[taskerReport.UserId] = false // Đã có report của năm
		for _, v := range taskerReport.MonthlyReport {
			if v.Month == int32(startAMonth.Month()) {
				mapTaskerHasMonthlyReport[taskerReport.UserId] = true // Đã có report của năm / tháng
			}
		}
	}

	insertData := []interface{}{}
	// Upsert data report monthly for asker
	for _, taskerId := range taskerIds {
		hasMonthlyReport, hasYearReport := mapTaskerHasMonthlyReport[taskerId]
		dataTaskerReport, ok := mapDataByTaskerId[taskerId]
		if !ok {
			msg := fmt.Sprintf("CreateTaskerMonthlyReportVN Error dataTaskerReport is nil - data: %v, taskerId: %s", dataTaskerReport, taskerId)
			globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
			continue
		}
		if hasMonthlyReport {
			continue
		}
		if !hasYearReport {
			insertData = append(insertData, &modelTaskerMonthlyReport.TaskerMonthlyReport{
				XId:    globalLib.GenerateObjectId(),
				UserId: taskerId,
				Year:   dataTaskerReport.Year,
				MonthlyReport: []*modelTaskerMonthlyReport.TaskerMonthlyReportMonthlyReport{
					dataTaskerReport.MonthlyReportInfo,
				},
				CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
			})
			delete(mapDataByTaskerId, taskerId)
		}
		if hasYearReport && !hasMonthlyReport {
			updateDataReportByTaskerId(taskerId, dataTaskerReport)
			delete(mapDataByTaskerId, taskerId)
			delete(mapTaskerHasMonthlyReport, taskerId)
		}
	}

	if len(insertData) > 0 {
		insertMonthlyReportData(insertData)
	}
}

func updateDataReportByTaskerId(taskerId string, data *modelReport) {
	// Update data report by askerId
	_, err := globalDataAccess.UpdateOneByQuery(
		globalCollection.COLLECTION_TASKER_MONTHLY_REPORT[local.ISO_CODE],
		bson.M{"userId": taskerId, "year": data.Year},
		bson.M{
			"$push": bson.M{"monthlyReport": data.MonthlyReportInfo},
		},
	)
	if err != nil {
		msg := fmt.Sprintf("CreateTaskerMonthlyReportVN Update data report err: %v, taskerId: %v", err, taskerId)
		globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
	}
}

func insertMonthlyReportData(data []interface{}) {
	err := globalDataAccess.InsertAll(globalCollection.COLLECTION_TASKER_MONTHLY_REPORT[local.ISO_CODE], data)
	if err != nil {
		msg := fmt.Sprintf("CreateTaskerMonthlyReportVN Insert data report err: %v, data: %v", err, data)
		globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
	}
}
