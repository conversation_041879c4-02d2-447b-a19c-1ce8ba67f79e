import { Meteor } from 'meteor/meteor';
import { HTTP } from 'meteor/http';
import _ from 'lodash';
import moment from 'moment';
import {
  trackingScheduleStatus,
  postToSlack,
} from '/server/helper-function';

const runCheckTaskPrice = () => {
  return;
	console.log("Start runCheckTaskPrice ...");
  var message = null;
  let taskArray = [];
  const url = Meteor.settings.GO_SERVICE.API_URL + '/v2/pricing/home-cleaning';
  const headers = {
    'content-type': 'application/json',
    'accessKey': Meteor.settings.GO_SERVICE.API_KEY,
  };

  Meteor.appCollection.Task.find({
    date: {$gte: new Date()},
    status: {$nin: ['CANCELED', 'EXPIRED']},
    'requirements.type': 3,
    'changesHistory.key': 'UPDATE_DATE_TIME'
  }, {
    fields: {
      cost: 1,
      changesHistory: 1,
      phone: 1,
      contactName: 1,
      date: 1,
      'serviceText.en': 1,
      serviceId: 1,
      duration: 1,
      autoChooseTasker: 1,
      taskPlace: 1,
      homeType: 1,
      tip: 1,
      requirements: 1,
      isoCode: 1,
      askerId: 1,
      isPremium: 1,
      'promotion.code': 1,
    }
  }).forEach((task, index) => {
    // Delay 2s to call calculate task price api
    if (index !== 0) { Meteor.wrapAsync(cb => { Meteor.setTimeout(() => { cb(); }, 2000); })(); }

    const taskPriceCalculate = {
      date: moment(task.date).utc().format(),
      duration: task.duration,
      autoChooseTasker: Boolean(task.autoChooseTasker),
      taskPlace: task.taskPlace,
      homeType: task.homeType,
      askerId: task.askerId,
      isPremium: task.isPremium,
    };
    if (task.promotion && task.promotion.code) {
      taskPriceCalculate.promotion = {
        code: task.promotion.code,
      };
    }
    if (task.tip) {
      taskPriceCalculate.tip = task.tip;
    }
    if (task.requirements && task.requirements.length > 0) {
      taskPriceCalculate.requirements = task.requirements.map((e) => { return { type: e.type } });
    }
    const priceCalculate = {
      service: {_id: task.serviceId},
      task: taskPriceCalculate,
      isoCode: task.isoCode,
    };
    const result = HTTP.post(url, {headers: headers, data: priceCalculate});

    cost = _.get(result, 'data.cost', null);
    if ( cost !== task.cost ) {
      const isHasChangeDate = task.changesHistory.find(item => item.key === 'UPDATE_DATE_TIME');
      if ( Math.abs(cost - task.cost) === 20000 && isHasChangeDate ) {
        // Exclude case, do nothing
      } else {
        taskArray.push(`${task.phone} - ${task.contactName} - Công việc lúc: ${moment(task.date).tz("Asia/Ho_Chi_Minh").format('HH:mm DD/MM/YYYY`')} - Giá ${task.cost}, giá đúng: ${cost}`);
      }

    }

  });
  if ( taskArray.length > 0 ) {
    postToSlack({
      channel: 'task-price-checking',
      text: `Kiểm tra giá các công việc sau:\n${taskArray.join('\n')}`,
    });
  }

	console.log("Finished runCheckTaskPrice ");
  // Tracking
  trackingScheduleStatus({name: 'autoCheckTaskPrice', nextRunAt: SyncedCron.nextScheduledAtDate('autoCheckTaskPrice'), message});
};

  Meteor.methods({
    autoCheckTaskPrice() {
      SyncedCron.add({
        name: 'autoCheckTaskPrice',
        schedule(parser) {
          // Run every 30 minutes
          return parser.cron('0 0/30 * 1/1 * ? *', true);
        },
        job() {
          runCheckTaskPrice();
        },
      });
	    console.log("Added autoCheckTaskPrice cron");
    },
    'runNow.autoCheckTaskPrice'() {
      runCheckTaskPrice();
    },
  });
