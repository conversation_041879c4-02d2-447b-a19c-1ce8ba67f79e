package autoCalculateJourneyLeaderBoard

import (
	"sort"

	modelJourneyLeaderBoard "gitlab.com/btaskee/go-services-model-v2/grpcmodel/journeyLeaderBoard"
	modeJourneySetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/journeySetting"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
)

type ModelGroupTaskerByLevel map[string][]*modelJourneyLeaderBoard.JourneyLeaderBoard

func calculateNewLeaderBoard(cityName string, levelSetting, defaultLevelSetting map[string]*modeJourneySetting.JourneySettingLevel, mapPointByTaskerId map[string]int, taskersChangedLevelInDay map[string]*modelUser.Users, mapTaskerInLeaderBoard map[string]*modelJourneyLeaderBoard.JourneyLeaderBoard, mapNewTaskersHasPointInDay map[string]*modelUser.Users, startLevelInCity string) ModelGroupTaskerByLevel {
	// 1. Add new tasker to leader board and update old tasker level
	updateTaskerHasNewLevel(cityName, taskersChangedLevelInDay, mapTaskerInLeaderBoard)

	// 2. Add new tasker to leader board and update old tasker level
	updateNewTaskersHasPointInDay(cityName, mapNewTaskersHasPointInDay, mapTaskerInLeaderBoard, startLevelInCity)

	// 3. Increate tasker point
	increaseTaskerPoint(mapTaskerInLeaderBoard, mapPointByTaskerId)

	// 4. Update user point and groupByCityAndLevel
	taskersGroupByLevel := groupTaskerInfoByLevel(mapTaskerInLeaderBoard, levelSetting, defaultLevelSetting)

	// 5. Sort by point and update rank
	sortByPoint(taskersGroupByLevel)

	// 6. Update field rankChange: "UP", "DOWN", "UNCHANGED"
	setNewRank(taskersGroupByLevel)

	return taskersGroupByLevel
}

// 1
func updateTaskerHasNewLevel(cityName string, taskersChangedLevelInDay map[string]*modelUser.Users, mapTaskerInLeaderBoard map[string]*modelJourneyLeaderBoard.JourneyLeaderBoard) {
	for _, tasker := range taskersChangedLevelInDay {
		// If tasker not exists -> Init a new info
		if mapTaskerInLeaderBoard[tasker.XId] == nil {
			mapTaskerInLeaderBoard[tasker.XId] = &modelJourneyLeaderBoard.JourneyLeaderBoard{
				XId:      tasker.XId,
				CityName: cityName,
				Name:     tasker.Name,
				Avatar:   tasker.Avatar,
				Level:    tasker.GetJourneyInfo().GetLevel(),
				Rank:     -1,
			}
		}
		// Update avatar info from list tasker if tasker avatar is empty
		mapTaskerInLeaderBoard[tasker.XId].Avatar = tasker.Avatar
	}
}

// 2
func updateNewTaskersHasPointInDay(cityName string, mapNewTaskersHasPointInDay map[string]*modelUser.Users, mapTaskerInLeaderBoard map[string]*modelJourneyLeaderBoard.JourneyLeaderBoard, startLevelInCity string) {
	for _, tasker := range mapNewTaskersHasPointInDay {
		// If tasker not exists -> Init a new info
		if mapTaskerInLeaderBoard[tasker.XId] == nil {
			mapTaskerInLeaderBoard[tasker.XId] = &modelJourneyLeaderBoard.JourneyLeaderBoard{
				XId:      tasker.XId,
				CityName: cityName,
				Name:     tasker.Name,
				Avatar:   tasker.Avatar,
				Level:    startLevelInCity,
				Rank:     -1,
			}
		}
		mapTaskerInLeaderBoard[tasker.XId].Avatar = tasker.Avatar
	}
}

// 3
func increaseTaskerPoint(mapTaskerInLeaderBoard map[string]*modelJourneyLeaderBoard.JourneyLeaderBoard, mapPointByTaskerId map[string]int) {
	for taskerId, point := range mapPointByTaskerId {
		if mapTaskerInLeaderBoard[taskerId] != nil {
			mapTaskerInLeaderBoard[taskerId].Point += int32(point)
		}
	}
}

// 4
func groupTaskerInfoByLevel(mapTaskerInLeaderBoard map[string]*modelJourneyLeaderBoard.JourneyLeaderBoard, levelSetting, defaultLevelSetting map[string]*modeJourneySetting.JourneySettingLevel) ModelGroupTaskerByLevel {

	result := make(ModelGroupTaskerByLevel)
	for _, v := range mapTaskerInLeaderBoard {
		if result[v.Level] == nil {
			result[v.Level] = []*modelJourneyLeaderBoard.JourneyLeaderBoard{}
		}
		if levelSetting[v.Level] == nil {
			levelSetting[v.Level] = defaultLevelSetting[v.Level]
		}
		if levelSetting[v.Level] != nil {
			v.Icon = levelSetting[v.Level].Icon
			v.Title = levelSetting[v.Level].Title
			v.Text = levelSetting[v.Level].Text
			result[v.Level] = append(result[v.Level], v)
		}
	}

	return result
}

// 5
func sortByPoint(taskersByLevel ModelGroupTaskerByLevel) {
	for _, level := range taskersByLevel {
		sort.Slice(level, func(i, j int) bool {
			return level[i].Point > level[j].Point
		})
	}
}

// 6
func setNewRank(taskersByLevel ModelGroupTaskerByLevel) {
	for _, leaderBoard := range taskersByLevel {
		for rank, tasker := range leaderBoard {
			oldRank := tasker.Rank
			newRank := int32(rank) + 1

			// 1. Check if tasker up level
			// if taskersChangedLevelInDay[tasker.XId] != nil {
			if oldRank == -1 {
				tasker.RankChange = "UP"
				tasker.Rank = newRank
				continue
			}

			// 2. check tasker not uplevel
			tasker.Rank = newRank
			if oldRank > newRank {
				tasker.RankChange = "UP"
			} else if oldRank < newRank {
				tasker.RankChange = "DOWN"
			} else {
				tasker.RankChange = "UNCHANGED"
			}
		}
	}
}
