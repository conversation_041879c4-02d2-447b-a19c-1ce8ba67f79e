package lib

import "go.mongodb.org/mongo-driver/bson"

var IMPORTANT_FIELDS_OF_TASK = bson.M{
	"_id":                         1,
	"acceptedTasker.avatar":       1,
	"acceptedTasker.isLeader":     1,
	"acceptedTasker.name":         1,
	"acceptedTasker.taskerId":     1,
	"askerId":                     1,
	"blackList":                   1,
	"collectionDate":              1,
	"cookingDetail.haveFruit":     1,
	"cookingDetail.isGoMarket":    1,
	"cost":                        1,
	"costDetail.cost":             1,
	"costDetail.decreasedReasons": 1,
	"costDetail.finalCost":        1,
	"costDetail.newFinalCost":     1,
	"createdAt":                   1,
	"date":                        1,
	"description":                 1,
	"detailDeepCleaning":          1,
	"detailLaundry.isReceived":    1,
	"duration":                    1,
	"isoCode":                     1,
	"isPremium":                   1,
	"isTetBooking":                1,
	"newCostDetail.cost":          1,
	"newCostDetail.finalCost":     1,
	"originCurrency":              1,
	"pet":                         1,
	"requirements":                1,
	"serviceName":                 1,
	"serviceText":                 1,
	"status":                      1,
	"subscriptionId":              1,
	"taskPlace":                   1,
	"viewedTaskers":               1,
	"serviceId":                   1,
	"payment.method":              1,
}
