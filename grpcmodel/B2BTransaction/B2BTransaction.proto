syntax = "proto3";
package B2BTransaction;

import "google/protobuf/timestamp.proto";

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcmodel/B2BTransaction";

message B2BTransaction {
  // @inject_tag: bson:"_id,omitempty"
  string _id = 1;
  // @inject_tag: bson:"from,omitempty"
  string from = 2;
  // @inject_tag: bson:"to,omitempty"
  string to = 3;
  // @inject_tag: bson:"amount,omitempty"
  double amount = 4;
  // @inject_tag: bson:"createdAt,omitempty"
  google.protobuf.Timestamp createdAt = 5;
}