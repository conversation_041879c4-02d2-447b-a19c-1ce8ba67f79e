import { Meteor } from 'meteor/meteor';
import {
  revertTimeZone,
  trackingScheduleStatus,
  sendNotificationByIds,
  postToSlack,
} from '/server/helper-function';
import _ from 'lodash';
import moment from 'moment';

const minAvgRating = 4.4;

const lockTaskerLowAvgRating = () => {
  return;
  var message = null;
  try {
    const settings = Meteor.appCollection.SettingSystem.findOne({}, {fields: { tester: 1 }});
    const testers = _.get(settings, 'tester', []);
    const last7Days = moment().subtract(7, 'days').toDate();
    
    let taskerIds = [];

    const findCondition = {
      type: "TASKER",
      status: "ACTIVE",
      employeeIds: null,
      company: null,
      phone: {$nin: testers},
      avgRating: {$lte: minAvgRating},
      $or: [
        { lastActiveAt: null },
        {
          lastActiveAt: {$lte: last7Days},
        }
      ]
    };
    
    const taskers = Meteor.appCollection.users.find(findCondition, {fields: {phone: 1, name: 1, workingPlaces: 1}}).map((tasker) => {
      taskerIds.push(tasker._id);
      return tasker;
    });

    if ( taskerIds && taskerIds.length > 0 ) {
      // Update Taskers's status are LOCKED
      Meteor.appCollection.users.update({ _id: {$in: taskerIds} }, {
        $set: {
          status: 'LOCKED',
          updatedAt: new Date(),
        }
      }, {multi: true});
      // Send notification with force view
      sendNotificationByIds(taskerIds, {
        title: {
          vi: 'Tài khoản của bạn bị tạm khóa.',
          en: 'Your account has been blocked.',
          ko: '계정이 일시적으로 잠겨습니다.',
          th: 'บัญชีของคุณถูกล็อกการใช้งาน',
        },
        text: {
          vi: `Tài khoản của bạn bị tạm khóa theo qui định của bTaskee về chất lượng dịch vụ. Cộng tác viên của bTaskee cần duy trì trung bình đánh giá trên ${minAvgRating}. Vui lòng liên hệ bTaskee trong giờ hành chính nếu bạn cần hỗ trợ.`,
          en: `Your account is temporarily locked according to bTaskee's regulations on service quality. bTaskers need to maintain a rating average above ${minAvgRating}. Please contact bTaskee within office hours if you need support.`,
          ko: `bTaskee의 서비스 품질 규정에 따라 계정이 일시적으로 잠깁니다. bTaskee의 파트너가 평균 별점을 ${minAvgRating}점 이상으로 유지해야합니다. 도움이 필요한 경우 근무 시간 중에 bTaskee에 문의하십시오.`,
          th: `บัญชีของคุณถูกล็อกการใช้งานจากคุณภาพของการให้บริการที่ทางบริษัทกำหนด เนื่องจากบีทัสเกอร์ (แม่บ้าน) ต้องรักษาระดับให้สูงกว่า ${minAvgRating} ดาว โปรดติดต่อบริษัทในเวลาทำการเพื่อรับการช่วยเหลือ`,
        },
        payload: {},
      }, { isForceView: true, type: 30 });
      
      // Insert UserActionHistory
      taskers.forEach((tasker) => {
        const taskerCity = tasker.workingPlaces && tasker.workingPlaces.length > 0 ? tasker.workingPlaces[0].city : '';
        Meteor.appCollection.UserActionHistory.insert({
          phone: tasker.phone,
          name: tasker.name,
          action: 'BLOCK_TASKER',
          data: {
            taskerId: tasker._id,
            reason: `Trung bình đánh giá từ ${minAvgRating} trở xuống.`,
            city: taskerCity,
            supporter: 'Auto System'
          },
          createdAt: new Date(),
        });

        postToSlack({
          channel: 'tasker-blocked',
          text: `[ĐÃ KHÓA] Tasker có trung bình sao từ ${minAvgRating} trở xuống. ${taskerCity} - ${tasker.phone} - ${tasker.name}.`,
        });
      });
    }
  } catch(ex) {
    message = ex;
    // Tracking
    trackingScheduleStatus({name: 'autoLockTaskerLowAvgRating', nextRunAt: SyncedCron.nextScheduledAtDate('autoLockTaskerLowAvgRating'), message});
  }
  
};

export default function () {
  Meteor.methods({
    // Auto lock the Taskers have low avg Rating: <= 4.6
    autoLockTaskerLowAvgRating() {
      SyncedCron.add({
        name: 'autoLockTaskerLowAvgRating',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 8)).getHours();
          // Run at 08:00 +7GMT everyday
          return parser.cron(`0 0 ${hourInTimeZoneServer} 1/1 * ? *`, true);
        },
        job() {
          lockTaskerLowAvgRating();
        },
      });
    },
    'runNow.autoLockTaskerLowAvgRating'() {
      lockTaskerLowAvgRating();
    },
  });
}
