package giveRewardTaskerDoneSpecialCampaign

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getTasker() (users []*modelUser.Users) {
	query := bson.M{
		"isoCode":     local.ISO_CODE,
		"countryCode": globalConstant.COUNTRY_CODE_VN,
		"type":        globalConstant.USER_TYPE_TASKER,
		"status":      globalConstant.USER_STATUS_ACTIVE,
		"companyInfo": bson.M{"$exists": false}, // loại tasker là đối tác thì không được tham gia vào campaign
	}

	field := bson.M{
		"_id":               1,
		"name":              1,
		"referralCode":      1,
		"status":            1,
		"fAccountId":        1,
		"language":          1,
		"phone":             1,
		"journeyInfo.level": 1,
		"companyInfo":       1,
	}

	users, _ = modelUser.GetAll(local.ISO_CODE, query, field)
	return
}
