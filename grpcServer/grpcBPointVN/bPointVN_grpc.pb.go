// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcBPointVN

import (
	context "context"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// BPointVNClient is the client API for BPointVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BPointVNClient interface {
	AccumulatePoints(ctx context.Context, in *BPointRequest, opts ...grpc.CallOption) (*response.Response, error)
	TaskerAccumulatePoints(ctx context.Context, in *BPointRequest, opts ...grpc.CallOption) (*response.Response, error)
	GivePointTaskerDoneSpecialCampaign(ctx context.Context, in *BPointRequest, opts ...grpc.CallOption) (*response.Response, error)
	AddUserPoint(ctx context.Context, in *BPointRequest, opts ...grpc.CallOption) (*response.Response, error)
}

type bPointVNClient struct {
	cc grpc.ClientConnInterface
}

func NewBPointVNClient(cc grpc.ClientConnInterface) BPointVNClient {
	return &bPointVNClient{cc}
}

func (c *bPointVNClient) AccumulatePoints(ctx context.Context, in *BPointRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcBPointVN.BPointVN/AccumulatePoints", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bPointVNClient) TaskerAccumulatePoints(ctx context.Context, in *BPointRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcBPointVN.BPointVN/TaskerAccumulatePoints", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bPointVNClient) GivePointTaskerDoneSpecialCampaign(ctx context.Context, in *BPointRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcBPointVN.BPointVN/GivePointTaskerDoneSpecialCampaign", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bPointVNClient) AddUserPoint(ctx context.Context, in *BPointRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcBPointVN.BPointVN/AddUserPoint", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BPointVNServer is the server API for BPointVN service.
// All implementations must embed UnimplementedBPointVNServer
// for forward compatibility
type BPointVNServer interface {
	AccumulatePoints(context.Context, *BPointRequest) (*response.Response, error)
	TaskerAccumulatePoints(context.Context, *BPointRequest) (*response.Response, error)
	GivePointTaskerDoneSpecialCampaign(context.Context, *BPointRequest) (*response.Response, error)
	AddUserPoint(context.Context, *BPointRequest) (*response.Response, error)
	mustEmbedUnimplementedBPointVNServer()
}

// UnimplementedBPointVNServer must be embedded to have forward compatible implementations.
type UnimplementedBPointVNServer struct {
}

func (UnimplementedBPointVNServer) AccumulatePoints(context.Context, *BPointRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AccumulatePoints not implemented")
}
func (UnimplementedBPointVNServer) TaskerAccumulatePoints(context.Context, *BPointRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskerAccumulatePoints not implemented")
}
func (UnimplementedBPointVNServer) GivePointTaskerDoneSpecialCampaign(context.Context, *BPointRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GivePointTaskerDoneSpecialCampaign not implemented")
}
func (UnimplementedBPointVNServer) AddUserPoint(context.Context, *BPointRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddUserPoint not implemented")
}
func (UnimplementedBPointVNServer) mustEmbedUnimplementedBPointVNServer() {}

// UnsafeBPointVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BPointVNServer will
// result in compilation errors.
type UnsafeBPointVNServer interface {
	mustEmbedUnimplementedBPointVNServer()
}

func RegisterBPointVNServer(s *grpc.Server, srv BPointVNServer) {
	s.RegisterService(&_BPointVN_serviceDesc, srv)
}

func _BPointVN_AccumulatePoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BPointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BPointVNServer).AccumulatePoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBPointVN.BPointVN/AccumulatePoints",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BPointVNServer).AccumulatePoints(ctx, req.(*BPointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BPointVN_TaskerAccumulatePoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BPointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BPointVNServer).TaskerAccumulatePoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBPointVN.BPointVN/TaskerAccumulatePoints",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BPointVNServer).TaskerAccumulatePoints(ctx, req.(*BPointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BPointVN_GivePointTaskerDoneSpecialCampaign_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BPointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BPointVNServer).GivePointTaskerDoneSpecialCampaign(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBPointVN.BPointVN/GivePointTaskerDoneSpecialCampaign",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BPointVNServer).GivePointTaskerDoneSpecialCampaign(ctx, req.(*BPointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BPointVN_AddUserPoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BPointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BPointVNServer).AddUserPoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBPointVN.BPointVN/AddUserPoint",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BPointVNServer).AddUserPoint(ctx, req.(*BPointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _BPointVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcBPointVN.BPointVN",
	HandlerType: (*BPointVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AccumulatePoints",
			Handler:    _BPointVN_AccumulatePoints_Handler,
		},
		{
			MethodName: "TaskerAccumulatePoints",
			Handler:    _BPointVN_TaskerAccumulatePoints_Handler,
		},
		{
			MethodName: "GivePointTaskerDoneSpecialCampaign",
			Handler:    _BPointVN_GivePointTaskerDoneSpecialCampaign_Handler,
		},
		{
			MethodName: "AddUserPoint",
			Handler:    _BPointVN_AddUserPoint_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "bPointVN.proto",
}
