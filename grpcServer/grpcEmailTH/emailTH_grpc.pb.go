// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcEmailTH

import (
	context "context"
	emailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	emailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	task "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	users "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// EmailTHClient is the client API for EmailTH service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EmailTHClient interface {
	SendEmail(ctx context.Context, in *emailSending.EmailSending, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendReceiptEmail(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendVerifyEmail(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendRenewSubscriptionEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	ResendReceiptEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	ResendSubscriptionOrderEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendReceiptSubscriptionEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendSubscriptionSuggestionEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendCancelFeeEmail(ctx context.Context, in *emailSending.EmailCancelFeeRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendSubscriptionRenewedEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendTopUpSuccessEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendCancelChargeFailEmail(ctx context.Context, in *emailSending.EmailCancelFeeRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendChargeFailEmail(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendChangeToCashScheduleEmail(ctx context.Context, in *emailSending.EmailChangeToTaskCashScheduleRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
}

type emailTHClient struct {
	cc grpc.ClientConnInterface
}

func NewEmailTHClient(cc grpc.ClientConnInterface) EmailTHClient {
	return &emailTHClient{cc}
}

func (c *emailTHClient) SendEmail(ctx context.Context, in *emailSending.EmailSending, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailTH.EmailTH/SendEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailTHClient) SendReceiptEmail(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailTH.EmailTH/SendReceiptEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailTHClient) SendVerifyEmail(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailTH.EmailTH/SendVerifyEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailTHClient) SendRenewSubscriptionEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailTH.EmailTH/SendRenewSubscriptionEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailTHClient) ResendReceiptEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailTH.EmailTH/ResendReceiptEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailTHClient) ResendSubscriptionOrderEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailTH.EmailTH/ResendSubscriptionOrderEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailTHClient) SendReceiptSubscriptionEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailTH.EmailTH/SendReceiptSubscriptionEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailTHClient) SendSubscriptionSuggestionEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailTH.EmailTH/SendSubscriptionSuggestionEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailTHClient) SendCancelFeeEmail(ctx context.Context, in *emailSending.EmailCancelFeeRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailTH.EmailTH/SendCancelFeeEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailTHClient) SendSubscriptionRenewedEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailTH.EmailTH/SendSubscriptionRenewedEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailTHClient) SendTopUpSuccessEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailTH.EmailTH/SendTopUpSuccessEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailTHClient) SendCancelChargeFailEmail(ctx context.Context, in *emailSending.EmailCancelFeeRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailTH.EmailTH/SendCancelChargeFailEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailTHClient) SendChargeFailEmail(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailTH.EmailTH/SendChargeFailEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailTHClient) SendChangeToCashScheduleEmail(ctx context.Context, in *emailSending.EmailChangeToTaskCashScheduleRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailTH.EmailTH/SendChangeToCashScheduleEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EmailTHServer is the server API for EmailTH service.
// All implementations must embed UnimplementedEmailTHServer
// for forward compatibility
type EmailTHServer interface {
	SendEmail(context.Context, *emailSending.EmailSending) (*emailResponse.EmailResponse, error)
	SendReceiptEmail(context.Context, *task.Task) (*emailResponse.EmailResponse, error)
	SendVerifyEmail(context.Context, *users.Users) (*emailResponse.EmailResponse, error)
	SendRenewSubscriptionEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error)
	ResendReceiptEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error)
	ResendSubscriptionOrderEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error)
	SendReceiptSubscriptionEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error)
	SendSubscriptionSuggestionEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error)
	SendCancelFeeEmail(context.Context, *emailSending.EmailCancelFeeRequest) (*emailResponse.EmailResponse, error)
	SendSubscriptionRenewedEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error)
	SendTopUpSuccessEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error)
	SendCancelChargeFailEmail(context.Context, *emailSending.EmailCancelFeeRequest) (*emailResponse.EmailResponse, error)
	SendChargeFailEmail(context.Context, *task.Task) (*emailResponse.EmailResponse, error)
	SendChangeToCashScheduleEmail(context.Context, *emailSending.EmailChangeToTaskCashScheduleRequest) (*emailResponse.EmailResponse, error)
	mustEmbedUnimplementedEmailTHServer()
}

// UnimplementedEmailTHServer must be embedded to have forward compatible implementations.
type UnimplementedEmailTHServer struct {
}

func (UnimplementedEmailTHServer) SendEmail(context.Context, *emailSending.EmailSending) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendEmail not implemented")
}
func (UnimplementedEmailTHServer) SendReceiptEmail(context.Context, *task.Task) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendReceiptEmail not implemented")
}
func (UnimplementedEmailTHServer) SendVerifyEmail(context.Context, *users.Users) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendVerifyEmail not implemented")
}
func (UnimplementedEmailTHServer) SendRenewSubscriptionEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendRenewSubscriptionEmail not implemented")
}
func (UnimplementedEmailTHServer) ResendReceiptEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResendReceiptEmail not implemented")
}
func (UnimplementedEmailTHServer) ResendSubscriptionOrderEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResendSubscriptionOrderEmail not implemented")
}
func (UnimplementedEmailTHServer) SendReceiptSubscriptionEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendReceiptSubscriptionEmail not implemented")
}
func (UnimplementedEmailTHServer) SendSubscriptionSuggestionEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSubscriptionSuggestionEmail not implemented")
}
func (UnimplementedEmailTHServer) SendCancelFeeEmail(context.Context, *emailSending.EmailCancelFeeRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendCancelFeeEmail not implemented")
}
func (UnimplementedEmailTHServer) SendSubscriptionRenewedEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSubscriptionRenewedEmail not implemented")
}
func (UnimplementedEmailTHServer) SendTopUpSuccessEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendTopUpSuccessEmail not implemented")
}
func (UnimplementedEmailTHServer) SendCancelChargeFailEmail(context.Context, *emailSending.EmailCancelFeeRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendCancelChargeFailEmail not implemented")
}
func (UnimplementedEmailTHServer) SendChargeFailEmail(context.Context, *task.Task) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendChargeFailEmail not implemented")
}
func (UnimplementedEmailTHServer) SendChangeToCashScheduleEmail(context.Context, *emailSending.EmailChangeToTaskCashScheduleRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendChangeToCashScheduleEmail not implemented")
}
func (UnimplementedEmailTHServer) mustEmbedUnimplementedEmailTHServer() {}

// UnsafeEmailTHServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EmailTHServer will
// result in compilation errors.
type UnsafeEmailTHServer interface {
	mustEmbedUnimplementedEmailTHServer()
}

func RegisterEmailTHServer(s *grpc.Server, srv EmailTHServer) {
	s.RegisterService(&_EmailTH_serviceDesc, srv)
}

func _EmailTH_SendEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailSending)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailTHServer).SendEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailTH.EmailTH/SendEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailTHServer).SendEmail(ctx, req.(*emailSending.EmailSending))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailTH_SendReceiptEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(task.Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailTHServer).SendReceiptEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailTH.EmailTH/SendReceiptEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailTHServer).SendReceiptEmail(ctx, req.(*task.Task))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailTH_SendVerifyEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(users.Users)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailTHServer).SendVerifyEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailTH.EmailTH/SendVerifyEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailTHServer).SendVerifyEmail(ctx, req.(*users.Users))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailTH_SendRenewSubscriptionEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailTHServer).SendRenewSubscriptionEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailTH.EmailTH/SendRenewSubscriptionEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailTHServer).SendRenewSubscriptionEmail(ctx, req.(*emailSending.EmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailTH_ResendReceiptEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailTHServer).ResendReceiptEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailTH.EmailTH/ResendReceiptEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailTHServer).ResendReceiptEmail(ctx, req.(*emailSending.EmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailTH_ResendSubscriptionOrderEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailTHServer).ResendSubscriptionOrderEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailTH.EmailTH/ResendSubscriptionOrderEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailTHServer).ResendSubscriptionOrderEmail(ctx, req.(*emailSending.EmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailTH_SendReceiptSubscriptionEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailTHServer).SendReceiptSubscriptionEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailTH.EmailTH/SendReceiptSubscriptionEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailTHServer).SendReceiptSubscriptionEmail(ctx, req.(*emailSending.EmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailTH_SendSubscriptionSuggestionEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailTHServer).SendSubscriptionSuggestionEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailTH.EmailTH/SendSubscriptionSuggestionEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailTHServer).SendSubscriptionSuggestionEmail(ctx, req.(*emailSending.EmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailTH_SendCancelFeeEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailCancelFeeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailTHServer).SendCancelFeeEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailTH.EmailTH/SendCancelFeeEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailTHServer).SendCancelFeeEmail(ctx, req.(*emailSending.EmailCancelFeeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailTH_SendSubscriptionRenewedEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailTHServer).SendSubscriptionRenewedEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailTH.EmailTH/SendSubscriptionRenewedEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailTHServer).SendSubscriptionRenewedEmail(ctx, req.(*emailSending.EmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailTH_SendTopUpSuccessEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailTHServer).SendTopUpSuccessEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailTH.EmailTH/SendTopUpSuccessEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailTHServer).SendTopUpSuccessEmail(ctx, req.(*emailSending.EmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailTH_SendCancelChargeFailEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailCancelFeeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailTHServer).SendCancelChargeFailEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailTH.EmailTH/SendCancelChargeFailEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailTHServer).SendCancelChargeFailEmail(ctx, req.(*emailSending.EmailCancelFeeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailTH_SendChargeFailEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(task.Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailTHServer).SendChargeFailEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailTH.EmailTH/SendChargeFailEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailTHServer).SendChargeFailEmail(ctx, req.(*task.Task))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailTH_SendChangeToCashScheduleEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailChangeToTaskCashScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailTHServer).SendChangeToCashScheduleEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailTH.EmailTH/SendChangeToCashScheduleEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailTHServer).SendChangeToCashScheduleEmail(ctx, req.(*emailSending.EmailChangeToTaskCashScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _EmailTH_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcEmailTH.EmailTH",
	HandlerType: (*EmailTHServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendEmail",
			Handler:    _EmailTH_SendEmail_Handler,
		},
		{
			MethodName: "SendReceiptEmail",
			Handler:    _EmailTH_SendReceiptEmail_Handler,
		},
		{
			MethodName: "SendVerifyEmail",
			Handler:    _EmailTH_SendVerifyEmail_Handler,
		},
		{
			MethodName: "SendRenewSubscriptionEmail",
			Handler:    _EmailTH_SendRenewSubscriptionEmail_Handler,
		},
		{
			MethodName: "ResendReceiptEmail",
			Handler:    _EmailTH_ResendReceiptEmail_Handler,
		},
		{
			MethodName: "ResendSubscriptionOrderEmail",
			Handler:    _EmailTH_ResendSubscriptionOrderEmail_Handler,
		},
		{
			MethodName: "SendReceiptSubscriptionEmail",
			Handler:    _EmailTH_SendReceiptSubscriptionEmail_Handler,
		},
		{
			MethodName: "SendSubscriptionSuggestionEmail",
			Handler:    _EmailTH_SendSubscriptionSuggestionEmail_Handler,
		},
		{
			MethodName: "SendCancelFeeEmail",
			Handler:    _EmailTH_SendCancelFeeEmail_Handler,
		},
		{
			MethodName: "SendSubscriptionRenewedEmail",
			Handler:    _EmailTH_SendSubscriptionRenewedEmail_Handler,
		},
		{
			MethodName: "SendTopUpSuccessEmail",
			Handler:    _EmailTH_SendTopUpSuccessEmail_Handler,
		},
		{
			MethodName: "SendCancelChargeFailEmail",
			Handler:    _EmailTH_SendCancelChargeFailEmail_Handler,
		},
		{
			MethodName: "SendChargeFailEmail",
			Handler:    _EmailTH_SendChargeFailEmail_Handler,
		},
		{
			MethodName: "SendChangeToCashScheduleEmail",
			Handler:    _EmailTH_SendChangeToCashScheduleEmail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "emailTH.proto",
}
