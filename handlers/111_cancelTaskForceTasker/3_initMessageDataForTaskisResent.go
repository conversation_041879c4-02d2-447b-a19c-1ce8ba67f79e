package cancelTaskForceTasker

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func initMessageDataForFavChat_TaskResent(tasks []*modelTask.Task) {
	for _, task := range tasks {
		// Check if chatmessage is exists
		query := bson.M{
			"members":     bson.M{"$size": 2}, // Check the array size is 2
			"members._id": bson.M{"$all": []string{task.AskerId, task.ForceTasker.TaskerId}},
		}
		fields := bson.M{"_id": 1}
		chatMessage, err := pkgChatMessage.GetChatConversation(task.IsoCode, query, fields)
		if err != nil && err != mongo.ErrNoDocuments {
			msg := fmt.Sprintf("[go-sync-cron-vn-v3] Get Chat conversation failed: tasjId %s. Error: %s", task.XId, err.Error())
			globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
			return
		}
		if chatMessage == nil {
			return
		}
		chatId := chatMessage.XId

		var tasker, asker *modelUsers.Users
		err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USERS, bson.M{"_id": task.ForceTasker.TaskerId}, bson.M{"name": 1, "language": 1}, &tasker)
		if err != nil {
			msg := fmt.Sprintf("[go-sync-cron-vn-v3] Can not get user: userId %s. Error: %s", task.ForceTasker.TaskerId, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
			return
		}
		err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USERS, bson.M{"_id": task.AskerId}, bson.M{"name": 1, "language": 1}, &asker)
		if err != nil {
			msg := fmt.Sprintf("[go-sync-cron-vn-v3] Can not get user: userId %s. Error: %s", task.AskerId, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
			return
		}

		lib.SetDisabledMessage(chatId, task.XId, globalConstant.KEY_BOOK_WITH_FAV, tasker.XId, asker.XId)
		lib.RemoveCountDownAndStatusInMessage(chatId, task.XId, task.AskerId)
		go initMessageForChatAsker_TaskResent(task, asker, chatId)
		go initMessageForChatTasker_TaskResent(task, tasker, chatId)
	}
}

func initMessageForChatAsker_TaskResent(task *modelTask.Task, asker *modelUsers.Users, chatId string) {
	//create chat message
	message := &modelChatMessage.ChatMessageMessages{
		XId:    globalLib.GenerateObjectId(),
		From:   globalConstant.CHAT_MESSAGE_FROM_SYSTEM,
		ChatId: chatId,
		TaskRequestData: &modelChatMessage.ChatMessageMessagesTaskRequestData{
			TaskInfo: &modelChatMessage.ChatMessageMessagesTaskRequestDataInfo{
				TaskId: task.XId,
			},
		},
		MessageBySystem: &modelChatMessage.ChatMessageMessagesMessageBySystem{
			Title:  localization.GetLocalizeObject("BOOK_WITH_FAV_ASKER_SEND_TASK_TO_OTHERS_TITLE"),
			Text:   localization.GetLocalizeObject("BOOK_WITH_FAV_ASKER_SEND_TASK_TO_OTHERS_BODY"),
			SendTo: globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_ASKER,
			Key:    globalConstant.KEY_VIEW_TASK,
		},
		CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
	}

	// Insert new message
	err := pkgChatMessage.SendMessageToConversation(task.IsoCode, chatId, []*modelChatMessage.ChatMessageMessages{message})
	if err != nil {
		msg := fmt.Sprintf("[go-sync-cron-vn-v3] Send Chat Message to conversation failed: taskId %s. Error: %s", task.XId, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
		return
	}
	go lib.SendChatMessageViaSocket(nil, chatId, []string{asker.XId}, message)
	// set asker language
	langAsker := globalConstant.LANG_VI
	if asker != nil && asker.Language != "" {
		langAsker = asker.Language
	}

	reqNoti := globalLib.FavChatNotiRequest{
		ChatId:       chatId,
		UserId:       asker.XId,
		UserLanguage: langAsker,
		IsoCode:      local.ISO_CODE,
		TimeZone:     local.TimeZone,
	}
	notiResponse := globalLib.NotiForFavChat(reqNoti)
	if notiResponse != nil {
		go lib.SendNotification(notiResponse.ArrayNotification, notiResponse.UserIds, notiResponse.Title, notiResponse.Body, notiResponse.Payload, true)
	}
}

func initMessageForChatTasker_TaskResent(task *modelTask.Task, tasker *modelUsers.Users, chatId string) {
	//create chat message
	message := &modelChatMessage.ChatMessageMessages{
		XId:    globalLib.GenerateObjectId(),
		From:   globalConstant.CHAT_MESSAGE_FROM_SYSTEM,
		ChatId: chatId,
		MessageBySystem: &modelChatMessage.ChatMessageMessagesMessageBySystem{
			Title:  localization.GetLocalizeObject("BOOK_WITH_FAV_ASKER_SEND_TASK_TO_OTHERS_OR_CANCEL_TASK_TITLE"),
			Text:   localization.GetLocalizeObject("BOOK_WITH_FAV_ASKER_SEND_TASK_TO_OTHERS_OR_CANCEL_TASK_BODY"),
			SendTo: globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_TASKER,
		},
		CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
	}

	// Insert new message
	err := pkgChatMessage.SendMessageToConversation(task.IsoCode, chatId, []*modelChatMessage.ChatMessageMessages{message})
	if err != nil {
		msg := fmt.Sprintf("[go-sync-cron-vn-v3] Send Chat Message to conversation failed: taskId %s. Error: %s", task.XId, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
		return
	}

	go lib.SendChatMessageViaSocket(nil, chatId, []string{tasker.XId}, message)
	// set asker language
	langTasker := globalConstant.LANG_VI
	if tasker != nil && tasker.Language != "" {
		langTasker = tasker.Language
	}

	reqNoti := globalLib.FavChatNotiRequest{
		ChatId:       chatId,
		UserId:       tasker.XId,
		UserLanguage: langTasker,
		IsoCode:      local.ISO_CODE,
		TimeZone:     local.TimeZone,
	}
	notiResponse := globalLib.NotiForFavChat(reqNoti)
	if notiResponse != nil {
		go lib.SendNotification(notiResponse.ArrayNotification, notiResponse.UserIds, notiResponse.Title, notiResponse.Body, notiResponse.Payload, true)
	}
}
