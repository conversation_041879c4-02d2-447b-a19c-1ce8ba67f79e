// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcBookingVN

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	bookingRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/bookingRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// BookingVNClient is the client API for BookingVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BookingVNClient interface {
	BookFirstTaskInSubscription(ctx context.Context, in *bookingRequest.BookingRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type bookingVNClient struct {
	cc grpc.ClientConnInterface
}

func NewBookingVNClient(cc grpc.ClientConnInterface) BookingVNClient {
	return &bookingVNClient{cc}
}

func (c *bookingVNClient) BookFirstTaskInSubscription(ctx context.Context, in *bookingRequest.BookingRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcBookingVN.BookingVN/BookFirstTaskInSubscription", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BookingVNServer is the server API for BookingVN service.
// All implementations must embed UnimplementedBookingVNServer
// for forward compatibility
type BookingVNServer interface {
	BookFirstTaskInSubscription(context.Context, *bookingRequest.BookingRequest) (*empty.Empty, error)
	mustEmbedUnimplementedBookingVNServer()
}

// UnimplementedBookingVNServer must be embedded to have forward compatible implementations.
type UnimplementedBookingVNServer struct {
}

func (UnimplementedBookingVNServer) BookFirstTaskInSubscription(context.Context, *bookingRequest.BookingRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BookFirstTaskInSubscription not implemented")
}
func (UnimplementedBookingVNServer) mustEmbedUnimplementedBookingVNServer() {}

// UnsafeBookingVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BookingVNServer will
// result in compilation errors.
type UnsafeBookingVNServer interface {
	mustEmbedUnimplementedBookingVNServer()
}

func RegisterBookingVNServer(s *grpc.Server, srv BookingVNServer) {
	s.RegisterService(&_BookingVN_serviceDesc, srv)
}

func _BookingVN_BookFirstTaskInSubscription_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(bookingRequest.BookingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BookingVNServer).BookFirstTaskInSubscription(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBookingVN.BookingVN/BookFirstTaskInSubscription",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BookingVNServer).BookFirstTaskInSubscription(ctx, req.(*bookingRequest.BookingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _BookingVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcBookingVN.BookingVN",
	HandlerType: (*BookingVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "BookFirstTaskInSubscription",
			Handler:    _BookingVN_BookFirstTaskInSubscription_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "bookingVN.proto",
}
