package autoCalculateJourneyLeaderBoard

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelServiceChannel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/serviceChannel"
	"go.mongodb.org/mongo-driver/bson"
)

func getTaskersInServiceChannel() ([]string, error) {
	var services []*modelService.Service
	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{}, bson.M{"_id": 1, "name": 1}, &services)
	if err != nil {
		err = fmt.Errorf("getServiceNotSPJourney: %v", err)
		return nil, err
	}
	var serviceNotSpIds, serviceSpIds []string
	for _, v := range services {
		if globalLib.FindStringInSlice(globalConstant.SERVICES_NOT_SUPPORT_JOURNEY, v.Name) >= 0 {
			serviceNotSpIds = append(serviceNotSpIds, v.XId)
		} else {
			serviceSpIds = append(serviceSpIds, v.XId)
		}
	}
	var serviceChannelsNotSP []*modelServiceChannel.ServiceChannel
	err = globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": bson.M{"$in": serviceNotSpIds}}, bson.M{"taskerList": 1}, &serviceChannelsNotSP)
	if err != nil {
		err = fmt.Errorf("getTaskersInServiceChannelsNotSPJourney: %v", err)
		return nil, err
	}
	taskerListNotSP := []string{}
	for _, v := range serviceChannelsNotSP {
		taskerListNotSP = append(taskerListNotSP, v.TaskerList...)
	}
	var serviceChannelsSP []*modelServiceChannel.ServiceChannel
	err = globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": bson.M{"$in": serviceSpIds}}, bson.M{"taskerList": 1}, &serviceChannelsSP)
	if err != nil {
		err = fmt.Errorf("getTaskersInServiceChannelsSPJourney: %v", err)
		return nil, err
	}
	taskerList := []string{}
	for _, v := range serviceChannelsSP {
		taskerList = append(taskerList, v.TaskerList...)
	}
	taskerList = globalLib.RemoveTaskersFromList(taskerList, taskerListNotSP)
	return globalLib.UniqString(taskerList), nil
}
