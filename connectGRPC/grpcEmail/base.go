package grpcEmail

import (
	"gitlab.com/btaskee/go-email-service-v2/grpcEmail"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"google.golang.org/grpc"
)

func ConnectGRPCEmail(endpoint string) (grpcEmail.EmailClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcEmail.NewEmailClient(connect)
	return client, connect, err
}
