package grpcLocking

import (
	"context"
	"fmt"
	"os"

	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/lockingMessage"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var GRPC_LOCKING_SERVICE_URL = map[string]string{
	"local": "127.0.0.1:35101",
	"test":  "127.0.0.1:35101",
	"ci":    "go-locking-service:35101",
	"dev":   "go-locking-service.kong.svc.cluster.local:81",
	"prod":  "go-locking-service.kong.svc.cluster.local:81",
}

var MODE = os.Getenv("APPLICATION_MODE")
var slackToken = os.Getenv("SLACK_TOKEN_VN")

func ValidateDuplicate(ctx context.Context, req *lockingMessage.ValidateDuplicateRequest) error {
	client, connect, err := ConnectGRPCLocking(GRPC_LOCKING_SERVICE_URL[MODE])
	if err != nil {
		// This case is the error came from transport layer
		msg := fmt.Sprintf("Failed to connect to locking service. Request: %v. Error: %v", req, err)
		globalLib.PostToSlack(slackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[globalConstant.ISO_CODE_VN], globalConstant.SLACK_USER_NAME, msg)
		return nil
	}
	defer connect.Close()

	_, err = client.ValidateDuplicate(ctx, req)
	if err != nil {
		status, ok := status.FromError(err)
		// This case is the error came from server
		if ok && status.Code().String() != codes.Unavailable.String() {
			return err
		}

		// This case is the error came from transport layer
		msg := fmt.Sprintf("Failed to validate duplicate. Request: %v. Error: %v", req, err)
		globalLib.PostToSlack(slackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[globalConstant.ISO_CODE_VN], globalConstant.SLACK_USER_NAME, msg)
		return nil
	}
	return nil
}
