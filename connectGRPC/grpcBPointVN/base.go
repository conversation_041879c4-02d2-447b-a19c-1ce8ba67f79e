package grpcBPointVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcBPointVN"
	"google.golang.org/grpc"
)

func ConnectGRPCBPointVN(endpoint string) (grpcBPointVN.BPointVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcBPointVN.NewBPointVNClient(connect)
	return client, connect, err
}
