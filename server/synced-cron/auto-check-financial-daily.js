import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import _ from 'lodash';
import {
  revertTimeZone,
  trackingScheduleStatus,
  sendEmailFunction,
  postToSlack,
} from '/server/helper-function';

//TODO: fix this, use Task rate not hardcode
const TASK_RATE = 0.20;

const calculateFinancialTaskFee = (costOfTask, requirements, tip, taskRate, isoCode) => {
  // Exclude requirements fee and tips
  let excludeFee = tip || 0;
  if ( requirements && requirements.length > 0 ) {
    requirements.forEach((requirement) => {
      excludeFee += requirement.cost || 0;
    });
  }
  // In case value of service.ServiceFeeLeaderTasker > 1 (exp: 20, 30)
  // Make sure that fee is always 0 < fee < 1
  if (taskRate > 1) {
    taskRate = taskRate / 100;
  }
  // minimum 100 VND, 1 THB
  let roundMoney = 100;
  if (isoCode === "TH") {
    roundMoney = 1;
  }
  return Math.ceil(((costOfTask - excludeFee ) * taskRate) / roundMoney) * roundMoney;
};

const getTaskServiceFee = (task, acceptedTasker, service) => {
  // Get task rate
  let taskRate = TASK_RATE;
  if (task.taskPlace && task.taskPlace.city && service && service.city && service.city.length > 0) {
    _.forEach(service.city, function(c) {
      if (c.name === task.taskPlace.city && c.taskRate > 0) {
        taskRate = c.taskRate;
        return false;
      }
    });
  }
  let taskCost = task.cost;
  // If this service is Deep Cleaning, get cost from detail
  const isDeepCleaningService = task.detailDeepCleaning && task.detailDeepCleaning.numberOfTaskersDeepCleaning;
  if (isDeepCleaningService) {
    if (acceptedTasker.isLeader) {
      const value = _.get(task, 'detailDeepCleaning.costPerLeaderTasker.total', null);
      taskCost = value || taskCost;
      // Get fee for leader
      if (service && service.serviceFeeLeaderTasker > 0) {
        taskRate = service.serviceFeeLeaderTasker;
      }
    } else {
      const value = _.get(task, 'detailDeepCleaning.costPerTasker.total', null);
      taskCost = value || taskCost;
    }
  }
  if ( task.goMarketDetail && task.goMarketDetail.depositMoney ) {
    taskCost = task.cost - task.goMarketDetail.depositMoney;
  }
  return calculateFinancialTaskFee(taskCost, task.requirements, task.tip, taskRate, task.isoCode);
};

const getMoneyTopupTasker = (task, acceptedTasker) => {
  // let main = _.get(task, 'promotion.decreasedCost', task.cost);
  // let promotion = task.cost - main > 0 ? task.cost - main : 0;
  let main = task.cost;
  if (task.costDetail && task.costDetail.finalCost) {
    main = task.costDetail.finalCost;
  }
  let promotion = 0;
  if (task.costDetail && task.costDetail.cost > task.costDetail.finalCost) {
    promotion = task.costDetail.cost - task.costDetail.finalCost;
  }

  const isDeepCleaningService = task.detailDeepCleaning && task.detailDeepCleaning.numberOfTaskersDeepCleaning;
  if ( isDeepCleaningService ) {
    if (acceptedTasker.isLeader) {
      main = _.get(task, 'detailDeepCleaning.costPerLeaderTasker.main', 0);
      promotion = _.get(task, 'detailDeepCleaning.costPerLeaderTasker.promotion', 0);
    } else {
      main = _.get(task, 'detailDeepCleaning.costPerTasker.main', 0);
      promotion = _.get(task, 'detailDeepCleaning.costPerTasker.promotion', 0);
    }
  }
  return {
    main,
    promotion
  };
}

const runCheckFinancial = () => {
  return;
	console.log("Start runCheckFinancial ...");
  var message = null;
  let taskIds = [];
  let cardTaskSourceValue = [];
  let bPayTaskTaskerSourceValue = [];
  let bPayTaskAskerSourceValue = [];
  let subscriptionTaskSouceValue = [];
  let promotionTaskSourceValue = [];


  let result = [];

  try {
    const last24Hour = moment().subtract(24, 'hours').toDate();
    const tasks = Meteor.appCollection.Task.find({
      date: {$gte: last24Hour, $lt: new Date()},
      status: 'DONE',
      isoCode: 'VN',
    }, {
      fields: {
        _id: 1,
        'payment.method': 1,
        'acceptedTasker.taskerId': 1,
        'acceptedTasker.isLeader': 1,
        'acceptedTasker.companyId': 1,
        cost: 1,
        'promotion.decreasedCost': 1,
        'promotion.code': 1,
        'serviceText.en': 1,
        detailDeepCleaning: 1,
        'requirements.cost': 1,
        tip: 1,
        date: 1,
        askerId: 1,
        status: 1,
        goMarketDetail: 1,
        serviceId: 1,
        taskPlace: 1,
        isoCode: 1,
        'costDetail.cost': 1,
        'costDetail.finalCost': 1,
      }
    }).map(task => {
      taskIds.push(task._id);
      switch ( _.get(task, 'payment.method', null) ) {
        case 'CARD':
          cardTaskSourceValue.push(task._id);
          break;
        case 'CREDIT':
          // `Task pay by credit account. TaskId: ${task._id}`
          bPayTaskTaskerSourceValue.push(`Task was paid by credit account. TaskId: ${task._id}`);
          bPayTaskAskerSourceValue.push(`Task pay by credit account. TaskId: ${task._id}`);
          break;
        case 'BANK_TRANSFER':
          subscriptionTaskSouceValue.push(`TaskId: ${task._id}. Done task in subscription`);
          break;
        default: break;
      }
      if ( task.promotion ) {
        if ( task.promotion.code ) {
          // Have 2 content for this transaction type.
          promotionTaskSourceValue.push(`TaskId: ${task._id} Promotion code: ${task.promotion.code}`);
          promotionTaskSourceValue.push(`TaskId: ${task._id}. Promotion code: ${task.promotion.code}`);
        } else {
          result.push(`${task._id} miss task.promotion.code`);
        }
      }
      return task;
    });

    // Find transactions to check 15%
    const transactions = Meteor.appCollection.FATransaction.find({
      date: {$gte: last24Hour},
      'source.name': 'TASK',
      'source.value': {$in: taskIds},
    }, {
      fields: {
        userId: 1,
        type: 1,
        accountType: 1,
        amount: 1,
        source: 1
      }
    }).fetch();


    // Find transactions to check Task pay by bPay
    const bPayTransactions = Meteor.appCollection.FATransaction.find({
      date: {$gte: last24Hour},
      'source.name': 'TASK',
      'source.value': {$in: bPayTaskTaskerSourceValue.concat(bPayTaskAskerSourceValue)},
    }, {
      fields: {
        userId: 1,
        type: 1,
        accountType: 1,
        amount: 1,
        source: 1
      }
    }).fetch();

    // Find transactions to check Task pay by CARD
    const cardTransactions = Meteor.appCollection.FATransaction.find({
      date: {$gte: last24Hour},
      'source.name': 'CARD_TASK',
      'source.value': {$in: cardTaskSourceValue},
    }, {
      fields: {
        userId: 1,
        type: 1,
        accountType: 1,
        amount: 1,
        source: 1
      }
    }).fetch();

    // Find transactions to check Task pay by BANK_TRANSFER (SUBSCRIPTION)
    const subsTaskTransactions = Meteor.appCollection.FATransaction.find({
      date: {$gte: last24Hour},
      'source.name': 'TASK',
      'source.value': {$in: subscriptionTaskSouceValue},
    }, {
      fields: {
        userId: 1,
        type: 1,
        accountType: 1,
        amount: 1,
        source: 1
      }
    }).fetch();

    // Find transactions to check promotion tasks
    const taskPromotionTaskerTransactions = Meteor.appCollection.FATransaction.find({
      date: {$gte: last24Hour},
      'source.name': 'TASK',
      'source.value': {$in: promotionTaskSourceValue},
    }, {
      fields: {
        userId: 1,
        type: 1,
        accountType: 1,
        amount: 1,
        source: 1
      }
    }).fetch();

    tasks.forEach(task => {
      // On each task: check the service fee
      var service = Meteor.appCollection.Service.findOne({_id: task.serviceId}, {fields: {city: 1, serviceFeeLeaderTasker: 1}});
      // Check fee of each Taskers
      task.acceptedTasker.forEach(tasker => {
        const chargeTaskerId = tasker.companyId || tasker.taskerId;
        const totalTransAmount = transactions.filter(trans => trans.source.value === task._id && trans.userId === chargeTaskerId).reduce((a, b) => {return a + b.amount}, 0);

        const taskerServiceFee = getTaskServiceFee(task, tasker, service);
        
        if ( totalTransAmount !== taskerServiceFee ) {
          result.push(`${task._id} 15% service fee not match`);
        }
      });

      const chargeAskerCost = _.get(task, 'promotion.decreasedCost', task.cost);
      // const promotionCost = task.cost - chargeAskerCost > 0 ? task.cost - chargeAskerCost : 0;

      // If task pay by pPay:
      if ( _.get(task, 'payment.method', null) === 'CREDIT' ) {
        // 1. Check decrease Asker bPay
        const askerChargeTransaction = bPayTransactions.filter(trans => trans.userId === task.askerId
          && ( trans.source.value === `Task was paid by credit account. TaskId: ${task._id}` || trans.source.value === `Task pay by credit account. TaskId: ${task._id}` )
          && trans.type === 'C'
          && trans.accountType === 'M'
          && trans.amount === chargeAskerCost);
        if ( !askerChargeTransaction || askerChargeTransaction.length !== 1 ) {
          result.push(`${task._id} asker decrease bPay not match`);
        }
        // 2. Check increase Tasker bPay
        task.acceptedTasker.forEach(tasker => {
          const chargeTaskerId = tasker.companyId || tasker.taskerId;
          const taskerTopupAmount = getMoneyTopupTasker(task, tasker);
          const taskerTopUpTransaction = bPayTransactions.filter(trans => trans.userId === chargeTaskerId
            && (trans.source.value === `Task was paid by credit account. TaskId: ${task._id}` || trans.source.value === `Task pay by credit account. TaskId: ${task._id}` )
            && trans.type === 'D'
            && trans.accountType === 'M'
            && trans.amount === taskerTopupAmount.main);
          if ( !taskerTopUpTransaction || taskerTopUpTransaction.length !== 1 ) {
            result.push(`${task._id} tasker increase bPay not match (bPay Task)`);
          }
        });
      }
      


      // If Task pay by CARD: Check increase Tasker bPay
      if ( _.get(task, 'payment.method', null) === 'CARD' ) {
        task.acceptedTasker.forEach(tasker => {
          const chargeTaskerId = tasker.companyId || tasker.taskerId;
          const taskerTopupAmount = getMoneyTopupTasker(task, tasker);
          const taskerTopUpTransaction = cardTransactions.filter(trans => trans.userId === chargeTaskerId
            && trans.source.value === task._id
            && trans.type === 'D'
            && trans.accountType === 'M'
            && trans.amount === taskerTopupAmount.main);
          if ( !taskerTopUpTransaction || taskerTopUpTransaction.length !== 1 ) {
            result.push(`${task._id} tasker increase bPay not match (CARD Task)`);
          }
        });
      }


      // If Task pay by BANK_TRANSFER (SUBSCRIPTION): Check increase Tasker bPay
      if ( _.get(task, 'payment.method', null) === 'BANK_TRANSFER' ) {
        task.acceptedTasker.forEach(tasker => {
          const chargeTaskerId = tasker.companyId || tasker.taskerId;
          const taskerTopupAmount = getMoneyTopupTasker(task, tasker);
          const taskerTopUpTransaction = subsTaskTransactions.filter(trans => trans.userId === chargeTaskerId
            && trans.source.value === `TaskId: ${task._id}. Done task in subscription`
            && trans.type === 'D'
            && trans.accountType === 'M'
            && trans.amount === taskerTopupAmount.main);
          if ( !taskerTopUpTransaction || taskerTopUpTransaction.length !== 1 ) {
            result.push(`${task._id} tasker increase bPay not match (SUBSCRIPTION Task)`);
          }
        });
      }

      // If Task has promotion: check the promotion amount top-up to Tasker
      if ( task.promotion && task.promotion.code ) {
        task.acceptedTasker.forEach(tasker => {
          const chargeTaskerId = tasker.companyId || tasker.taskerId;
          const taskerTopupAmount = getMoneyTopupTasker(task, tasker);
          const taskerTopUpTransaction = taskPromotionTaskerTransactions.filter(trans => trans.userId === chargeTaskerId
            && trans.source.value.indexOf(task._id) >= 0
            && trans.source.value.indexOf(task.promotion.code) >= 0
            && trans.type === 'D'
            && trans.accountType === 'P'
            && trans.amount === taskerTopupAmount.promotion);
          if ( !taskerTopUpTransaction || taskerTopUpTransaction.length !== 1 ) {
            result.push(`${task._id} tasker increase promotion not match (Promotion Task)`);
          }
        });
      }
    });
    result = _.uniq(result);

    if ( result.length > 0 ) {
      const option = {
        from: 'No Reply <<EMAIL>>',
        to: '<EMAIL>',
        subject: 'financial-error-alert',
        text: `Kiểm tra financial các Tasks sau:\n${result.join('\n')}`,
      };
      sendEmailFunction(option);
    }

  } catch (e) {
    console.log('==============ERROR======================');
    console.log(e);
    console.log('====================================');
    message = e;
  }
	console.log("Finished runCheckFinancial ");
  // Tracking
  trackingScheduleStatus({name: 'autoCheckFinancialDaily', nextRunAt: SyncedCron.nextScheduledAtDate('autoCheckFinancialDaily'), message});
};

  Meteor.methods({
    autoCheckFinancialDaily() {
      SyncedCron.add({
        name: 'autoCheckFinancialDaily',
        schedule(parser) {

      const option = {
        from: 'No Reply <<EMAIL>>',
        to: '<EMAIL>',
        subject: 'financial-error-alert',
        text: 'Kiểm tra financial các Tasks sau:',
      };
      sendEmailFunction(option);
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 2)).getHours();
          // Run at 2:20AM every day with timezone GMT+7.
          return parser.cron(`0 20 ${hourInTimeZoneServer} 1/1 * ? *`, true);
        },
        job() {
          runCheckFinancial();
        },
      });
	    console.log("Added autoCheckFinancial cron");
    },
    'runNow.autoCheckFinancialDaily'() {
      runCheckFinancial();
    },
  });
