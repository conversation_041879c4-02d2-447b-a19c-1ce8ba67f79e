package grpcPricing

import (
	"gitlab.com/btaskee/go-pricing-service-v2/grpcPricing"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"google.golang.org/grpc"
)

func ConnectGRPCPricing(endpoint string) (grpcPricing.PricingClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPricing.NewPricingClient(connect)
	return client, connect, err
}
