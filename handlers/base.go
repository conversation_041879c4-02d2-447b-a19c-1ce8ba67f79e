package handlers

import (
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/lib"
	libUser "gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/lib/user"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccessV2"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelServiceChannel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/serviceChannel"
	modelSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

var cfg = config.GetConfig()

func getFavTaskers(serviceChannel *modelServiceChannel.ServiceChannel, asker *modelUser.Users, task *modelTask.Task, busyTaskers, taskExcludedTaskers []string) []*modelUser.Users {
	// Match fav.Taskers and Taskers in service channel.
	var favTaskersInChannel []string
	if serviceChannel != nil {
		favTaskersInChannel = globalLib.IntersectionArray(asker.FavouriteTasker, serviceChannel.TaskerList)
	}

	// Remove Taskers in blackList
	if len(task.BlackList) > 0 {
		favTaskersInChannel = globalLib.RemoveTaskersFromList(favTaskersInChannel, task.BlackList)
	}
	if len(busyTaskers) > 0 {
		favTaskersInChannel = globalLib.RemoveTaskersFromList(favTaskersInChannel, busyTaskers)
	}
	// Remove taskers in excludedTaskers
	favTaskersInChannel = globalLib.RemoveTaskersFromList(favTaskersInChannel, taskExcludedTaskers)
	// NOTE: Because when opening services in a new city, tasks with the payment method 'cash' will not be accepted by any tasker,
	// we will allow taskers using BNPL to take on cash tasks.
	// Remove Taskers in taskerHasBNPL
	// if len(taskerHasBNPL) > 0 {
	// 	favTaskersInChannel = globalLib.RemoveTaskersFromList(favTaskersInChannel, taskerHasBNPL)
	// }

	findCondition := bson.M{
		"_id":    bson.M{"$in": favTaskersInChannel},
		"status": globalConstant.USER_STATUS_ACTIVE,
	}
	if task != nil && task.TaskPlace != nil && task.TaskPlace.City != "" {
		findCondition["workingPlaces.city"] = task.TaskPlace.City
	}
	if task.IsPremium {
		findCondition["isPremiumTasker"] = true
	}
	favList, _ := libUser.GetAll(findCondition, bson.M{"_id": 1, "language": 1, "company": 1})
	return favList
}

func getTaskersDistrict(serviceChannel *modelServiceChannel.ServiceChannel, asker *modelUser.Users, task *modelTask.Task, busyTaskers, taskExcludedTaskers []string) []*modelUser.Users {
	var taskerList []string
	if serviceChannel != nil {
		taskerList = lib.CopyArrayString(serviceChannel.TaskerList)
		taskerList = globalLib.RemoveTaskersFromList(taskerList, task.BlackList)
	}
	if asker != nil && len(asker.FavouriteTasker) > 0 {
		taskerList = globalLib.RemoveTaskersFromList(taskerList, asker.FavouriteTasker)
	}
	if len(busyTaskers) > 0 {
		taskerList = globalLib.RemoveTaskersFromList(taskerList, busyTaskers)
	}
	// Remove taskers in excludedTaskers
	taskerList = globalLib.RemoveTaskersFromList(taskerList, taskExcludedTaskers)
	// NOTE: Because when opening services in a new city, tasks with the payment method 'cash' will not be accepted by any tasker,
	// we will allow taskers using BNPL to take on cash tasks.
	// Remove Taskers in taskerHasBNPL
	// if len(taskerHasBNPL) > 0 {
	// 	taskerList = globalLib.RemoveTaskersFromList(taskerList, taskerHasBNPL)
	// }
	// Find Taskers
	findTaskerCondition := bson.M{
		"_id":                    bson.M{"$in": taskerList},
		"type":                   globalConstant.USER_TYPE_TASKER,
		"status":                 globalConstant.USER_STATUS_ACTIVE,
		"workingPlaces.country":  task.TaskPlace.Country,
		"workingPlaces.city":     task.TaskPlace.City,
		"workingPlaces.district": task.TaskPlace.District,
	}
	if task.IsPremium {
		findTaskerCondition["isPremiumTasker"] = true
	}
	taskersDistrict, _ := libUser.GetAll(findTaskerCondition, bson.M{"language": 1, "company": 1})
	return taskersDistrict
}

func getTopTaskers(serviceChannel *modelServiceChannel.ServiceChannel, asker *modelUser.Users, task *modelTask.Task, settings *modelSetting.Settings, busyTaskers, taskExcludedTaskers []string) []*modelUser.Users {
	var taskerList []string
	if serviceChannel != nil {
		taskerList = lib.CopyArrayString(serviceChannel.TaskerList)
		taskerList = globalLib.RemoveTaskersFromList(taskerList, task.BlackList)
	}
	if asker != nil && len(asker.FavouriteTasker) > 0 {
		taskerList = globalLib.RemoveTaskersFromList(taskerList, asker.FavouriteTasker)
	}
	inactiveTaskers := lib.GetInactiveTaskerInWork(taskerList)
	taskerList = globalLib.RemoveTaskersFromList(taskerList, busyTaskers)
	taskerList = globalLib.RemoveTaskersFromList(taskerList, inactiveTaskers)
	// Remove taskers in excludedTaskers
	taskerList = globalLib.RemoveTaskersFromList(taskerList, taskExcludedTaskers)
	// NOTE: Because when opening services in a new city, tasks with the payment method 'cash' will not be accepted by any tasker,
	// we will allow taskers using BNPL to take on cash tasks.
	// Remove Taskers in taskerHasBNPL
	// taskerList = globalLib.RemoveTaskersFromList(taskerList, taskerHasBNPL)

	findTaskerCondition := bson.M{
		"workingPlaces.district": task.TaskPlace.District,
		"workingPlaces.city":     task.TaskPlace.City,
		"workingPlaces.country":  task.TaskPlace.Country,
		"_id":                    bson.M{"$in": taskerList},
		"type":                   globalConstant.USER_TYPE_TASKER,
		"status":                 globalConstant.USER_STATUS_ACTIVE,
	}
	// Premium task need premium tasker
	if task.IsPremium {
		findTaskerCondition["isPremiumTasker"] = true
	}
	var topTaskersPrioritize int64 = 10
	if settings != nil && settings.TopTaskersPriorityScore > 0 {
		topTaskersPrioritize = int64(settings.TopTaskersPriorityScore)
	}
	taskers, _ := libUser.GetAll(findTaskerCondition, bson.M{"language": 1, "company": 1}, &globalDataAccessV2.QueryOptions{Limit: topTaskersPrioritize, Sort: bson.M{"score": -1}})
	return taskers

}
