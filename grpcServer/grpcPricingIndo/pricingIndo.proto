syntax = "proto3";
package grpcPricingIndo;

option go_package = "gitlab.com/btaskee/go-pricing-indo-v3/grpcPricingIndo";

import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingrequest/pricing-request.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingresponse/pricing-response.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionRequest/pricing-subscription-request.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pricingSubscriptionResponse/pricing-subscription-response.proto";

service PricingIndo {
  rpc GetPricingAirConditioner (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/air-conditioner"
      body: "*"
    };
  }
  rpc GetPricingHomeCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/home-cleaning"
      body: "*"
    };
  }
  rpc GetPricingDeepCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/deep-cleaning"
      body: "*"
    };
  }
  rpc RePricingHomeCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/re-pricing-home-cleaning"
      body: "*"
    };
  }
  rpc RePricingAirConditioner (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/re-pricing-air-conditioner"
      body: "*"
    };
  }
  rpc RePricingDeepCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/re-pricing-deep-cleaning"
      body: "*"
    };
  }
  rpc ReCalculatePricing (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/re-calculate"
      body: "*"
    };
  }
  rpc GetPricingOfficeCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {}
  rpc APIGetPricingOfficeCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/office-cleaning"
      body: "*"
    };
  }
  rpc RePricingOfficeCleaning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/re-pricing-office-cleaning"
      body: "*"
    };
  }
  rpc GetPricingSubscription (pricingSubscriptionRequest.PricingSubscriptionRequest) returns (pricingSubscriptionResponse.PricingSubscriptionResponse) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/subscription"
      body: "*"
    };
  }
  rpc GetPricingWashingMachine (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/washing-machine"
      body: "*"
    };
  }
  rpc RePricingWashingMachine (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/re-pricing-washing-machine"
      body: "*"
    };
  }
  rpc GetPricingSofa (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/sofa"
      body: "*"
    };
  }
  rpc RePricingSofa (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/re-pricing-sofa"
      body: "*"
    };
  }
  rpc GetPricingDisinfection (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/disinfection"
      body: "*"
    };
  }
  rpc RePricingDisinfection (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/re-pricing-disinfection"
      body: "*"
    };
  }
  rpc GetPricingMassage (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/massage"
      body: "*"
    };
  }
  rpc RePricingMassage (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/re-pricing-massage"
      body: "*"
    };
  }
  rpc GetPricingHomeMoving (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/home-moving"
      body: "*"
    };
  }
  rpc RePricingHomeMoving (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/re-home-moving"
      body: "*"
    };
  }
  rpc GetPricingAirConditionerV2 (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/air-conditioner-v2"
      body: "*"
    };
  }
  rpc GetPricingLaundry (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/laundry"
      body: "*"
    };
  }
  rpc RePricingLaundry (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/re-pricing-laundry"
      body: "*"
    };
  }
  rpc ReCalculatePricingForIncreaseDuration (pricingrequest.PricingIncreaseDurationRequest) returns (pricingresponse.CostResult) {}
  rpc APIGetPricingTaskDateOptions (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/task-date-options"
      body: "*"
    };
  }
  rpc GetPricingHousekeepingV2 (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/housekeeping-v2"
      body: "*"
    };
  }
  rpc GetPricingWaterHeater (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v3/pricing-indo/water-heater"
      body: "*"
    };
  }
  rpc GetPricingIroning (pricingrequest.PricingRequest) returns (pricingresponse.CostResult) {
    option (google.api.http) = {
      post: "/api/v5/pricing-indo/ironing"
      body: "*"
    };
  }
}