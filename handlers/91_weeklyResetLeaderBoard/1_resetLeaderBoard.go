package weeklyResetLeaderBoard

import (
	"fmt"
	"runtime/debug"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func resetLeaderBoard(taskerList []string) error {
	defer local.Logger.Sync()

	// handle recover error
	defer func() {
		if r := recover(); r != nil {
			msg := fmt.Sprintf("ResetLeaderBoard_%s: recover panic error: %v. debug stack: %v", local.ISO_CODE, r, string(debug.Stack()[:1000]))
			globalLib.PostToSlack(cfg.SlackToken, "go-journey-tasker", globalConstant.SLACK_USER_NAME, msg)
			local.Logger.Warn("ResetLeaderBoard",
				zap.String("isoCode", local.ISO_CODE),
				zap.String("stack", string(debug.Stack())),
			)
			return
		}
	}()

	var err error
	defer func() {
		msg := fmt.Sprintf("Done ResetLeaderBoard_%s. error: %v", local.ISO_CODE, err)
		globalLib.PostToSlack(cfg.SlackToken, "go-journey-tasker", globalConstant.SLACK_USER_NAME, msg)
	}()

	taskersLeaderBoard, err := getTaskersInLeaderBoard(taskerList)
	if err != nil {
		returnErr := fmt.Errorf("failed to get taskers in leader board - err: %s", err.Error())
		return returnErr
	}
	err = insertDataToWeeklyReport(taskersLeaderBoard)
	if err != nil {
		returnErr := fmt.Errorf("failed to insert date to weekly report - err: %s", err.Error())
		return returnErr
	}
	err = globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_JOURNEY_LEADER_BOARD[local.ISO_CODE], bson.M{})
	if err != nil {
		returnErr := fmt.Errorf("failed to delete all taskers in leader board - err: %s", err.Error())
		return returnErr
	}
	return nil
}
