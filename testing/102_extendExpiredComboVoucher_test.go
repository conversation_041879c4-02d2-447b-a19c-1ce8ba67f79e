package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelUserComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userComboVoucher"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

func TestExtendExpiredComboVoucher(t *testing.T) {
	apiUrl := "/api/v3/sync-cron-vn/extend-expired-combo-voucher"
	t.Run("1", func(t *testing.T) {
		ResetData()

		fAIds := CreateFinancialAccount([]map[string]interface{}{
			{
				"FMainAccount": 90000,
			}, {
				"FMainAccount": 1000,
			}, {
				"FMainAccount": 1000000,
			},
		})
		CreateUser([]map[string]interface{}{
			{
				"phone":      "**********",
				"name":       "Asker 01",
				"type":       globalConstant.USER_TYPE_ASKER,
				"fAccountId": fAIds[0],
			},
			{
				"phone":      "**********",
				"name":       "Asker 02",
				"type":       globalConstant.USER_TYPE_ASKER,
				"fAccountId": fAIds[1],
			},
			{
				"phone":      "**********",
				"name":       "Asker 03",
				"type":       globalConstant.USER_TYPE_ASKER,
				"fAccountId": fAIds[2],
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		ids := CreateComboVoucher([]map[string]interface{}{
			{
				"status": "ACTIVE",
				"cost":   100000,
				"price":  20000,
				"title": map[string]interface{}{
					"vi": "Title1",
					"en": "Title1",
				},
				"content": map[string]interface{}{
					"vi": "Tiết kiệm tới 20000 vnđ",
					"en": "Tiết kiệm tới 20000 vnđ",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, 1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"vouchers": []map[string]interface{}{
					{
						"_id":   "voucher1",
						"image": "combo1-1",
						"title": map[string]interface{}{
							"vi": "title-voucher1",
							"en": "title-voucher1",
							"ko": "title-voucher1",
							"th": "title-voucher1",
							"id": "title-voucher1",
						},
						"promotion": map[string]interface{}{
							"type":               "PERCENTAGE",
							"value":              0.2,
							"maxValue":           30000,
							"numberOfDayDueDate": 30,
							"prefix":             "ABC123",
						},
						"from":     "SYSTEM",
						"quantity": 2,
					}, {
						"_id":   "voucher2",
						"image": "xxx",
						"title": map[string]interface{}{
							"en": "title-voucher2",
						},
						"promotion": map[string]interface{}{
							"type":               "MONEY",
							"value":              30000,
							"numberOfDayDueDate": 30,
						},
						"applyFor": map[string]interface{}{
							"cities":        []string{},
							"services":      []string{"pcZRQ6PqmjrAPe5gt"},
							"isAllUsers":    false,
							"isSharePublic": false,
						},
						"from":               "SYSTEM",
						"quantity":           1,
						"numberOfDayDueDate": 30,
					},
				},
				"isSubscription":     true,
				"numberOfDayDueDate": 30,
			}, {
				"status": "ACTIVE",
				"cost":   100000,
				"price":  80000,
				"title": map[string]interface{}{
					"vi": "Title2",
					"en": "Title2",
				},
				"content": map[string]interface{}{
					"vi": "Tiết kiệm tới 20000 vnđ",
					"en": "Tiết kiệm tới 20000 vnđ",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, 1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"vouchers": []map[string]interface{}{
					{
						"_id":   "voucher1",
						"image": "combo1-1",
						"title": map[string]interface{}{
							"vi": "title-voucher1",
							"en": "title-voucher1",
							"ko": "title-voucher1",
							"th": "title-voucher1",
							"id": "title-voucher1",
						},
						"promotion": map[string]interface{}{
							"type":               "PERCENTAGE",
							"value":              0.2,
							"maxValue":           30000,
							"numberOfDayDueDate": 30,
							"prefix":             "ABC123",
						},
						"from":     "SYSTEM",
						"quantity": 2,
					}, {
						"_id":   "voucher2",
						"image": "xxx",
						"title": map[string]interface{}{
							"en": "title-voucher2",
						},
						"promotion": map[string]interface{}{
							"type":               "MONEY",
							"value":              30000,
							"numberOfDayDueDate": 30,
						},
						"applyFor": map[string]interface{}{
							"cities":        []string{},
							"services":      []string{"pcZRQ6PqmjrAPe5gt"},
							"isAllUsers":    false,
							"isSharePublic": false,
						},
						"from":               "SYSTEM",
						"quantity":           1,
						"numberOfDayDueDate": 30,
					},
				},
				"isSubscription":     true,
				"numberOfDayDueDate": 30,
			}, {
				"status": "ACTIVE",
				"cost":   100000,
				"price":  140000,
				"title": map[string]interface{}{
					"vi": "Title3",
					"en": "Title3",
				},
				"content": map[string]interface{}{
					"vi": "Tiết kiệm tới 20000 vnđ",
					"en": "Tiết kiệm tới 20000 vnđ",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, 1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"vouchers": []map[string]interface{}{
					{
						"_id":   "voucher1",
						"image": "combo1-1",
						"title": map[string]interface{}{
							"vi": "title-voucher1",
							"en": "title-voucher1",
							"ko": "title-voucher1",
							"th": "title-voucher1",
							"id": "title-voucher1",
						},
						"promotion": map[string]interface{}{
							"type":               "PERCENTAGE",
							"value":              0.2,
							"maxValue":           30000,
							"numberOfDayDueDate": 30,
							"prefix":             "ABC123",
						},
						"from":     "SYSTEM",
						"quantity": 2,
					}, {
						"_id":   "voucher2",
						"image": "xxx",
						"title": map[string]interface{}{
							"en": "title-voucher2",
						},
						"promotion": map[string]interface{}{
							"type":               "MONEY",
							"value":              30000,
							"numberOfDayDueDate": 30,
						},
						"applyFor": map[string]interface{}{
							"cities":        []string{},
							"services":      []string{"pcZRQ6PqmjrAPe5gt"},
							"isAllUsers":    false,
							"isSharePublic": false,
						},
						"from":               "SYSTEM",
						"quantity":           1,
						"numberOfDayDueDate": 30,
					},
				},
				"numberOfDayDueDate": 30,
			},
		})

		endDate := globalLib.EndADay(currentTime.AddDate(0, 0, -1))
		CreateUserComboVoucher([]map[string]interface{}{
			{
				"userId":         "**********",
				"comboVoucherId": ids[0],
				"isSubscription": true,
				"status":         "ACTIVE",
				"expiredDate":    endDate,
				"createdAt":      currentTime.AddDate(0, 0, -30),
				"vouchers":       []map[string]interface{}{},
			}, {
				"userId":         "**********",
				"comboVoucherId": ids[1],
				"isSubscription": true,
				"status":         "ACTIVE",
				"expiredDate":    endDate,
				"createdAt":      currentTime.AddDate(0, 0, -31),
				"vouchers":       []map[string]interface{}{},
			}, {
				"userId":         "**********",
				"comboVoucherId": ids[1],
				"isSubscription": true,
				"status":         "ACTIVE",
				"expiredDate":    endDate,
				"createdAt":      currentTime.AddDate(0, 0, -32),
				"vouchers":       []map[string]interface{}{},
			}, {
				"userId":         "**********",
				"comboVoucherId": ids[0],
				"isSubscription": true,
				"status":         "ACTIVE",
				"expiredDate":    endDate,
				"createdAt":      currentTime.AddDate(0, 0, -33),
				"vouchers":       []map[string]interface{}{},
			}, {
				"userId":         "**********",
				"comboVoucherId": ids[1],
				"isSubscription": true,
				"status":         "ACTIVE",
				"expiredDate":    endDate,
				"createdAt":      currentTime.AddDate(0, 0, -34),
				"vouchers":       []map[string]interface{}{},
				"isCancelled":    true,
			}, {
				"userId":         "**********",
				"comboVoucherId": ids[2],
				"status":         "ACTIVE",
				"expiredDate":    endDate,
				"createdAt":      currentTime.AddDate(0, 0, -35),
				"vouchers":       []map[string]interface{}{},
			},
		})

		body := map[string]interface{}{
			"action": lib.RUN_NOW,
		}

		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("Given HTTP request for api %s", apiUrl), t, func() {
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			Convey("Check the response and database", func() {
				// Check data
				So(res.Code, ShouldEqual, 200)

				// user 1 buy subscription 2 before 1 + enough bPay for 2 and not enough bPay for 1 => 2 active, 1 expire
				// user 2 buy subscription 2 + not enough bPay for 2 => 2 expire
				// user 3 buy subscription 1, 2 and combo voucher 3 + cancel 2 => 1 active, 2 expire, 3 expire

				var userComboVouchers []*modelUserComboVoucher.UserComboVoucher
				globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_USER_COMBO_VOUCHER[local.ISO_CODE], bson.M{}, bson.M{}, bson.M{"createdAt": -1}, &userComboVouchers)

				tested := map[int]bool{}
				for i, userComboVoucher := range userComboVouchers {
					switch i {
					case 0, 2, 4, 5:
						So(userComboVoucher.Status, ShouldEqual, globalConstant.USER_COMBO_VOUCHER_STATUS_EXPIRED)
						expiredDate := globalLib.ParseDateFromTimeStamp(userComboVoucher.ExpiredDate, local.TimeZone)
						So(expiredDate.String()[:19], ShouldEqual, globalLib.EndADay(endDate).String()[:19])
						tested[i] = true
					case 1, 3:
						So(userComboVoucher.Status, ShouldEqual, globalConstant.USER_COMBO_VOUCHER_STATUS_ACTIVE)
						So(userComboVoucher.Vouchers, ShouldNotBeNil)
						So(len(userComboVoucher.ExtendedHistories), ShouldEqual, 1)
						expiredDate := globalLib.ParseDateFromTimeStamp(userComboVoucher.ExpiredDate, local.TimeZone)
						So(expiredDate.String()[:19], ShouldEqual, globalLib.EndADay(endDate).AddDate(0, 0, 30).String()[:19])

						var userComboMap map[string]interface{}
						globalDataAccess.GetOneById(globalCollection.COLLECTION_USER_COMBO_VOUCHER[local.ISO_CODE], userComboVoucher.XId, bson.M{"changeHistories": 1}, &userComboMap)

						So(userComboMap["changeHistories"], ShouldNotBeNil)
						changeHistories := userComboMap["changeHistories"].(primitive.A)
						changeHistory := changeHistories[0].(map[string]interface{})
						So(changeHistory["from"], ShouldEqual, globalConstant.CHANGES_HISTORY_FROM_SYNC_CRON)
						So(changeHistory["key"], ShouldEqual, globalConstant.CHANGES_HISTORY_USER_COMBO_VOUCHER_EXTEND)
						So(changeHistory["createdBy"], ShouldEqual, globalConstant.CHANGES_HISTORY_FROM_SYNC_CRON)
						So(changeHistory["createdAt"], ShouldNotBeNil)
						changeHistoryContent := cast.ToStringMap(changeHistory["content"])
						So(changeHistoryContent["oldExpiredDate"], ShouldNotBeNil)
						So(changeHistoryContent["newExpiredDate"], ShouldNotBeNil)
						So(changeHistoryContent["newVouchers"], ShouldNotBeNil)
						tested[i] = true
					}
				}
				So(len(tested), ShouldEqual, 6)
			})
		})
	})
}
