package testing

import (
	"context"
	"fmt"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/local"
	svc "gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalRepo"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

/*
* <PERSON>ô tả:
	- Gửi notification tới tasker:
		+ Active tasker
	  + In service channel
	  + Have workingPlaces in city. (Đã bao gồm favTasker in city)
* Kết quả:
	- Cập nhật task.Visibility = 3, viewedTaskers
*/

func TestCity(t *testing.T) {
	// Validate
	t.Run("1", func(t *testing.T) {
		ResetData()

		Convey("Validate request params if request body is nil", t, func() {
			var req *pushNotificationNewTask.NewTaskRequest
			s := svc.GRPCServer{}
			resp, _ := s.NewTaskToCity(context.Background(), req)

			Convey("Check the response", func() {
				So(resp.StatusCode, ShouldEqual, 400)
				So(resp.Error.Code, ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})

		Convey("Validate request params if service is empty", t, func() {
			req := &pushNotificationNewTask.NewTaskRequest{
				Service: &pushNotificationNewTask.NewTaskRequestService{
					XId: "",
				}}
			s := svc.GRPCServer{}
			resp, _ := s.NewTaskToCity(context.Background(), req)

			Convey("Check the response", func() {
				So(resp.StatusCode, ShouldEqual, 400)
				So(resp.Error.Code, ShouldEqual, lib.ERROR_SERVICE_ID_REQUIRED.ErrorCode)
			})
		})

		Convey("Validate request params if Booking.XId is empty", t, func() {
			req := &pushNotificationNewTask.NewTaskRequest{
				Service: &pushNotificationNewTask.NewTaskRequestService{
					XId: "1111",
				},
				Booking: &pushNotificationNewTask.NewTaskRequestBooking{
					XId: "",
				},
			}
			s := svc.GRPCServer{}
			resp, _ := s.NewTaskToCity(context.Background(), req)

			Convey("Check the response", func() {
				So(resp.StatusCode, ShouldEqual, 400)
				So(resp.Error.Code, ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			})
		})

		Convey("Validate request params if Booking.IsoCode is empty", t, func() {
			req := &pushNotificationNewTask.NewTaskRequest{
				Service: &pushNotificationNewTask.NewTaskRequestService{
					XId: "1111",
				},
				Booking: &pushNotificationNewTask.NewTaskRequestBooking{
					XId:     "111",
					IsoCode: "",
				},
			}
			s := svc.GRPCServer{}
			resp, _ := s.NewTaskToCity(context.Background(), req)

			Convey("Check the response", func() {
				So(resp.StatusCode, ShouldEqual, 400)
				So(resp.Error.Code, ShouldEqual, lib.ERROR_BOOKING_ISOCODE_REQUIRED.ErrorCode)
			})
		})
	})

	// Send task normal to city
	t.Run("2", func(t *testing.T) {
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0981234567",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker01",
			},
			{
				"phone": "0981234500",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quận 3",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
			},
			{
				"phone": "0981234501",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quận 2",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
			},
			{
				"phone": "0981234502",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quận 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
			},
			{
				"phone": "0981234505",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker05",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  "VN",
					},
				},
			},
		})
		date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "0981234567",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_POSTED,
				"date":           fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
		})
		CreateTaskMetadata([]map[string]interface{}{
			{
				"_id":             taskIds[0],
				"excludedTaskers": []string{"0981234505"},
			},
		})
		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"$set": bson.M{"timeInBetweenTask": 15}})
		var sv *modelService.Service
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1}, &sv)

		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE],
			bson.M{"serviceId": sv.XId},
			bson.M{"$set": bson.M{
				"taskerList": []string{
					"0981234500",
					"0981234501",
					"0981234502",
					"0981234505",
				},
			}},
		)

		req := &pushNotificationNewTask.NewTaskRequest{
			Service: &pushNotificationNewTask.NewTaskRequestService{
				XId: sv.XId,
			},
			Booking: &pushNotificationNewTask.NewTaskRequestBooking{
				XId:     taskIds[0],
				IsoCode: local.ISO_CODE,
			},
		}

		Convey("When the service handle the request", t, func() {
			s := svc.GRPCServer{}
			_, err := s.NewTaskToCity(context.Background(), req)

			Convey("Check the database", func() {
				So(err, ShouldBeNil)
				var task *modelTask.Task
				globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskIds[0], bson.M{}, &task)
				So(len(task.ViewedTaskers), ShouldEqual, 3)
				for _, v := range task.ViewedTaskers {
					So(v, ShouldBeIn, []string{"0981234500", "0981234501", "0981234502"})
				}
			})
		})
	})

	// Send task premium to city
	t.Run("3", func(t *testing.T) {
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0981234567",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker01",
			},
			{
				"phone": "0981234500",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quận 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
			},
			{
				"phone": "0981234501",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quận 2",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"isPremiumTasker": true,
			},
			{
				"phone": "0981234502",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quận 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
			},
			{
				"phone": "0981234503",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quận 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"isPremiumTasker": true,
			},
		})
		date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "0981234567",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_POSTED,
				"date":           fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quan 1",
				},
				"isPremium": true,
			},
		})
		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"$set": bson.M{"timeInBetweenTask": 15}})
		var sv *modelService.Service
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1}, &sv)

		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE],
			bson.M{"serviceId": sv.XId},
			bson.M{"$set": bson.M{
				"taskerList": []string{
					"0981234500",
					"0981234501",
					"0981234502",
					"0981234503",
				},
			}},
		)

		req := &pushNotificationNewTask.NewTaskRequest{
			Service: &pushNotificationNewTask.NewTaskRequestService{
				XId: sv.XId,
			},
			Booking: &pushNotificationNewTask.NewTaskRequestBooking{
				XId:     taskIds[0],
				IsoCode: local.ISO_CODE,
			},
		}

		Convey("When the service handle the request", t, func() {
			s := svc.GRPCServer{}
			_, err := s.NewTaskToCity(context.Background(), req)

			Convey("Check the database", func() {
				So(err, ShouldBeNil)
				var task *modelTask.Task
				globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskIds[0], bson.M{}, &task)
				So(len(task.ViewedTaskers), ShouldEqual, 2)
				for _, v := range task.ViewedTaskers {
					So(v, ShouldBeIn, []string{"0981234501", "0981234503"})
				}
			})
		})
	})

	// Check trường hợp company bị conflict task nhưng vẫn sẽ send cho campany bình thường
	t.Run("4", func(t *testing.T) {
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0981234567",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker01",
			},
			{
				"phone": "0981234500",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quận 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
			},
			{
				"phone": "0981234501",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Company 01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quận 2",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"employeeIds": []string{"0981234500"},
			},
		})
		date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0981234567",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"date":        fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quan 1",
				},
			}, {
				"askerPhone":  "0981234567",
				"serviceName": globalConstant.SERVICE_NAME_AIR_CONDITIONER,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_CONFIRMED,
				"date":        fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quan 1",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0981234501",
						"companyId": "0981234501",
						"avatar":    "http://avatar0",
						"name":      "Tasker 00",
					},
				},
			},
		})
		var sv *modelService.Service
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"text.en": globalConstant.SERVICE_NAME_AIR_CONDITIONER}, bson.M{"_id": 1}, &sv)

		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE],
			bson.M{"serviceId": sv.XId},
			bson.M{"$set": bson.M{
				"taskerList": []string{
					"0981234500",
					"0981234501",
				},
			}},
		)

		req := &pushNotificationNewTask.NewTaskRequest{
			Service: &pushNotificationNewTask.NewTaskRequestService{
				XId: sv.XId,
			},
			Booking: &pushNotificationNewTask.NewTaskRequestBooking{
				XId:     taskIds[0],
				IsoCode: local.ISO_CODE,
			},
		}

		Convey("When the service handle the request", t, func() {
			s := svc.GRPCServer{}
			_, err := s.NewTaskToCity(context.Background(), req)

			Convey("Check the database", func() {
				So(err, ShouldBeNil)
				var task *modelTask.Task
				globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskIds[0], bson.M{}, &task)
				So(len(task.ViewedTaskers), ShouldEqual, 2)
				for _, v := range task.ViewedTaskers {
					So(v, ShouldBeIn, []string{"0981234501", "0981234500"})
				}
			})
		})
	})
	// Send task normal to city
	t.Run("5", func(t *testing.T) {
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0981234567",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker01",
			},
			{
				"phone": "0981234500",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quận 3",
						"city":     "Hồ Chí Minh",
						"country":  "VN",
					},
				},
			},
			{
				"phone": "0981234501",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quận 2",
						"city":     "Hồ Chí Minh",
						"country":  "VN",
					},
				},
			},
			{
				"phone": "0981234502",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quận 1",
						"city":     "Hồ Chí Minh",
						"country":  "VN",
					},
				},
			},
		})
		date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "0981234567",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_POSTED,
				"date":           fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
			},
		})

		CreateTaskerBNPLProcess([]map[string]interface{}{
			{
				"taskerId":        "0981234500",
				"amount":          800000,
				"remainingAmount": 600000,
				"status":          "PAYING",
				"moneyBNPLOnTask": 50000,
			},
		})

		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"$set": bson.M{"timeInBetweenTask": 15}})
		var sv *modelService.Service
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1}, &sv)

		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE],
			bson.M{"serviceId": sv.XId},
			bson.M{"$set": bson.M{
				"taskerList": []string{
					"0981234500",
					"0981234501",
					"0981234502",
				},
			}},
		)

		req := &pushNotificationNewTask.NewTaskRequest{
			Service: &pushNotificationNewTask.NewTaskRequestService{
				XId: sv.XId,
			},
			Booking: &pushNotificationNewTask.NewTaskRequestBooking{
				XId:     taskIds[0],
				IsoCode: globalConstant.ISO_CODE_VN,
			},
		}

		Convey("When the service handle the request", t, func() {
			s := svc.GRPCServer{}
			_, err := s.NewTaskToCity(context.Background(), req)

			Convey("Check the database", func() {
				So(err, ShouldBeNil)
				var task *modelTask.Task
				globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskIds[0], bson.M{}, &task)
				So(len(task.ViewedTaskers), ShouldEqual, 3)
				for _, v := range task.ViewedTaskers {
					So(v, ShouldBeIn, []string{"0981234501", "0981234502", "0981234500"}) // include BNPL tasker
				}
			})
		})
	})

	// Send task normal to city with limit accept task in day
	t.Run("6", func(t *testing.T) {
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone": "0981234567",
				"type":  globalConstant.USER_TYPE_ASKER,
				"name":  "Asker01",
			},
			{
				"phone": "0981234500",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quận 3",
						"city":     "Hồ Chí Minh",
						"country":  "VN",
					},
				},
			},
			{
				"phone": "0981234501",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quận 2",
						"city":     "Hồ Chí Minh",
						"country":  "VN",
					},
				},
			},
			{
				"phone": "0981234502",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quận 1",
						"city":     "Hồ Chí Minh",
						"country":  "VN",
					},
				},
			},
			{
				"phone": "0981234503",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker03",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quận 1",
						"city":     "Hồ Chí Minh",
						"country":  "VN",
					},
				},
			},
		})
		date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, 1)
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":     "0981234567",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_POSTED,
				"date":           fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"paymentMethod": "CREDIT",
			}, {
				"askerPhone":     "0981234567",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_CONFIRMED,
				"date":           fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0981234501",
						"companyId": "0981234501",
						"avatar":    "http://avatar0",
						"name":      "Tasker 00",
					},
				},
			}, {
				"askerPhone":     "0981234567",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_CONFIRMED,
				"date":           fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0981234501",
						"companyId": "0981234501",
						"avatar":    "http://avatar0",
						"name":      "Tasker 00",
					},
				},
			}, {
				"askerPhone":     "0981234567",
				"serviceKeyName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description":    "Desc Test",
				"address":        "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":         globalConstant.TASK_STATUS_CONFIRMED,
				"date":           fmt.Sprintf("%d,%d,%d,10,0", date.Year(), date.Month(), date.Day()),
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId":  "0981234503",
						"companyId": "0981234503",
						"avatar":    "http://avatar0",
						"name":      "Tasker 00",
					},
				},
			},
		})

		CreateTaskerBNPLProcess([]map[string]interface{}{
			{
				"taskerId":        "0981234500",
				"amount":          800000,
				"remainingAmount": 600000,
				"status":          "PAYING",
				"moneyBNPLOnTask": 50000,
			},
		})

		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"$set": bson.M{"timeInBetweenTask": 15, "limitNumberAcceptTaskInDay": 2}})
		var sv *modelService.Service
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1}, &sv)

		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE],
			bson.M{"serviceId": sv.XId},
			bson.M{"$set": bson.M{
				"taskerList": []string{
					"0981234500",
					"0981234501",
					"0981234502",
					"0981234503",
				},
			}},
		)

		req := &pushNotificationNewTask.NewTaskRequest{
			Service: &pushNotificationNewTask.NewTaskRequestService{
				XId: sv.XId,
			},
			Booking: &pushNotificationNewTask.NewTaskRequestBooking{
				XId:     taskIds[0],
				IsoCode: globalConstant.ISO_CODE_VN,
			},
		}

		Convey("When the service handle the request", t, func() {
			s := svc.GRPCServer{}
			_, err := s.NewTaskToCity(context.Background(), req)

			Convey("Check the database", func() {
				So(err, ShouldBeNil)
				var task *modelTask.Task
				globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskIds[0], bson.M{}, &task)
				So(len(task.ViewedTaskers), ShouldEqual, 2)
				for _, v := range task.ViewedTaskers {
					So(v, ShouldBeIn, []string{"0981234500", "0981234502"})
				}
			})
		})
	})
}
