package autoNotifyForCommunityNotification

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalCommunityConstant "gitlab.com/btaskee/go-services-model-v2/globalConstant/community"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func sendNotification(userIds []string) {
	var users []*modelUser.Users
	var notiInfos []interface{}
	var notiUserIds []*modelPushNotificationRequest.PushNotificationRequestUserIds
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_USERS, bson.M{"_id": bson.M{"$in": userIds}}, bson.M{"_id": 1, "language": 1}, &users)
	if len(users) == 0 {
		return
	}

	for _, user := range users {
		lang := globalConstant.LANG_VI
		if user.Language != "" {
			lang = user.Language
		}
		notiInfos = append(notiInfos, &modelNotification.Notification{
			XId:         globalLib.GenerateObjectId(),
			UserId:      user.XId,
			Type:        25,
			Description: localization.T(lang, "NOTIFICATION_NEW_NOTIFICATION_FROM_COMMUNITY_TITLE"),
			NavigateTo:  globalCommunityConstant.PAYLOAD_NAVIGATE_TO_COMMUNITY_NOTIFICATION,
			CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
		})

		notiUserIds = append(notiUserIds, &modelPushNotificationRequest.PushNotificationRequestUserIds{
			UserId:   user.XId,
			Language: lang,
		})
	}
	if len(notiInfos) == 0 || len(notiUserIds) == 0 {
		return
	}

	title := localization.GetLocalizeObject("DIALOG_TITLE_INFORMATION")
	text := localization.GetLocalizeObject("NOTIFICATION_NEW_NOTIFICATION_FROM_COMMUNITY_TITLE")
	payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
		Type:       25,
		NavigateTo: globalCommunityConstant.PAYLOAD_NAVIGATE_TO_COMMUNITY_NOTIFICATION,
	}
	lib.SendNotification(notiInfos, notiUserIds, title, text, payload, true)
}
