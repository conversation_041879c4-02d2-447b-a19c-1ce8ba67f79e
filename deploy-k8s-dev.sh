GOOS=linux GOARCH=amd64 go build -o send-task-vn-v3-linux-amd64

export IMAGE_DOCKER=linhnhdocker/go-push-notification-new-task-service:vn-3.0.0

docker build --platform=linux/amd64 --no-cache=true \
      --build-arg ELASTIC_APM_SERVICE_NAME="push-notification-new-task-service-v2" \
      --build-arg ELASTIC_APM_SERVER_URL="http://apm-server:8200" \
      --build-arg MODE="dev" \
      -t $IMAGE_DOCKER .

docker push $IMAGE_DOCKER

kubectl apply -f deploy/k8s/deployment_dev.yml