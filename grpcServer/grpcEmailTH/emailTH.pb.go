// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.21.9
// source: emailTH.proto

package grpcEmailTH

import (
	emailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	emailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	task "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	users "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_emailTH_proto protoreflect.FileDescriptor

var file_emailTH_proto_rawDesc = []byte{
	0x0a, 0x0d, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x48, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x0b, 0x67, 0x72, 0x70, 0x63, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x48, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x54, 0x67, 0x69, 0x74, 0x6c,
	0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67,
	0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x2d, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x52, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61,
	0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2d, 0x73, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x41, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72,
	0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x74, 0x61, 0x73,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x43, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32,
	0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x73,
	0x2f, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xee, 0x0a, 0x0a,
	0x07, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x48, 0x12, 0x47, 0x0a, 0x09, 0x53, 0x65, 0x6e, 0x64,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x1a, 0x1c, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x61, 0x0a, 0x10, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x0a, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x1a, 0x1c, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x23, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1d, 0x22, 0x18, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33,
	0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2d, 0x74, 0x68, 0x2f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x70,
	0x74, 0x3a, 0x01, 0x2a, 0x12, 0x3f, 0x0a, 0x0f, 0x53, 0x65, 0x6e, 0x64, 0x56, 0x65, 0x72, 0x69,
	0x66, 0x79, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x0c, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x73, 0x2e,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x1a, 0x1c, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x86, 0x01, 0x0a, 0x1a, 0x53, 0x65, 0x6e, 0x64, 0x52, 0x65,
	0x6e, 0x65, 0x77, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1c, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2e,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x28, 0x22, 0x23, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2d, 0x74, 0x68, 0x2f, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x2d, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x3a, 0x01, 0x2a, 0x12, 0x50,
	0x0a, 0x12, 0x52, 0x65, 0x73, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x1c, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x88, 0x01, 0x0a, 0x1c, 0x52, 0x65, 0x73, 0x65, 0x6e, 0x64, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x12, 0x1a, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x2e, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x28, 0x22, 0x23, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x65, 0x6d, 0x61,
	0x69, 0x6c, 0x2d, 0x74, 0x68, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x2d, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x3a, 0x01, 0x2a, 0x12, 0x8a, 0x01, 0x0a, 0x1c,
	0x53, 0x65, 0x6e, 0x64, 0x52, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x53, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x2e, 0x65,
	0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x30, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x2a, 0x22, 0x25,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x33, 0x2f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x2d, 0x74, 0x68,
	0x2f, 0x72, 0x65, 0x63, 0x65, 0x69, 0x70, 0x74, 0x2d, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x3a, 0x01, 0x2a, 0x12, 0x5d, 0x0a, 0x1f, 0x53, 0x65, 0x6e, 0x64,
	0x53, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x75, 0x67, 0x67,
	0x65, 0x73, 0x74, 0x69, 0x6f, 0x6e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x2e, 0x65, 0x6d,
	0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x12, 0x53, 0x65, 0x6e, 0x64, 0x43,
	0x61, 0x6e, 0x63, 0x65, 0x6c, 0x46, 0x65, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x23, 0x2e,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x46, 0x65, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x5a, 0x0a, 0x1c, 0x53, 0x65, 0x6e, 0x64, 0x53, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x6e, 0x65, 0x77, 0x65, 0x64, 0x45, 0x6d, 0x61,
	0x69, 0x6c, 0x12, 0x1a, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c,
	0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45,
	0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x53,
	0x0a, 0x15, 0x53, 0x65, 0x6e, 0x64, 0x54, 0x6f, 0x70, 0x55, 0x70, 0x53, 0x75, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53,
	0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x60, 0x0a, 0x19, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x43, 0x68, 0x61, 0x72, 0x67, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x45, 0x6d, 0x61, 0x69, 0x6c,
	0x12, 0x23, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e,
	0x45, 0x6d, 0x61, 0x69, 0x6c, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x46, 0x65, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x41, 0x0a, 0x13, 0x53, 0x65, 0x6e, 0x64, 0x43, 0x68, 0x61,
	0x72, 0x67, 0x65, 0x46, 0x61, 0x69, 0x6c, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x0a, 0x2e, 0x74,
	0x61, 0x73, 0x6b, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x1a, 0x1c, 0x2e, 0x65, 0x6d, 0x61, 0x69, 0x6c,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x73, 0x0a, 0x1d, 0x53, 0x65, 0x6e, 0x64,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x54, 0x6f, 0x43, 0x61, 0x73, 0x68, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x65, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x12, 0x32, 0x2e, 0x65, 0x6d, 0x61, 0x69,
	0x6c, 0x53, 0x65, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x2e, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x54, 0x6f, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x61, 0x73, 0x68, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1c, 0x2e,
	0x65, 0x6d, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x45, 0x6d,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x40, 0x5a,
	0x3e, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73,
	0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x45, 0x6d, 0x61, 0x69, 0x6c, 0x54, 0x48, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_emailTH_proto_goTypes = []interface{}{
	(*emailSending.EmailSending)(nil),                         // 0: emailSending.EmailSending
	(*task.Task)(nil),                                         // 1: task.Task
	(*users.Users)(nil),                                       // 2: users.Users
	(*emailSending.EmailRequest)(nil),                         // 3: emailSending.EmailRequest
	(*emailSending.EmailCancelFeeRequest)(nil),                // 4: emailSending.EmailCancelFeeRequest
	(*emailSending.EmailChangeToTaskCashScheduleRequest)(nil), // 5: emailSending.EmailChangeToTaskCashScheduleRequest
	(*emailResponse.EmailResponse)(nil),                       // 6: emailResponse.EmailResponse
}
var file_emailTH_proto_depIdxs = []int32{
	0,  // 0: grpcEmailTH.EmailTH.SendEmail:input_type -> emailSending.EmailSending
	1,  // 1: grpcEmailTH.EmailTH.SendReceiptEmail:input_type -> task.Task
	2,  // 2: grpcEmailTH.EmailTH.SendVerifyEmail:input_type -> users.Users
	3,  // 3: grpcEmailTH.EmailTH.SendRenewSubscriptionEmail:input_type -> emailSending.EmailRequest
	3,  // 4: grpcEmailTH.EmailTH.ResendReceiptEmail:input_type -> emailSending.EmailRequest
	3,  // 5: grpcEmailTH.EmailTH.ResendSubscriptionOrderEmail:input_type -> emailSending.EmailRequest
	3,  // 6: grpcEmailTH.EmailTH.SendReceiptSubscriptionEmail:input_type -> emailSending.EmailRequest
	3,  // 7: grpcEmailTH.EmailTH.SendSubscriptionSuggestionEmail:input_type -> emailSending.EmailRequest
	4,  // 8: grpcEmailTH.EmailTH.SendCancelFeeEmail:input_type -> emailSending.EmailCancelFeeRequest
	3,  // 9: grpcEmailTH.EmailTH.SendSubscriptionRenewedEmail:input_type -> emailSending.EmailRequest
	3,  // 10: grpcEmailTH.EmailTH.SendTopUpSuccessEmail:input_type -> emailSending.EmailRequest
	4,  // 11: grpcEmailTH.EmailTH.SendCancelChargeFailEmail:input_type -> emailSending.EmailCancelFeeRequest
	1,  // 12: grpcEmailTH.EmailTH.SendChargeFailEmail:input_type -> task.Task
	5,  // 13: grpcEmailTH.EmailTH.SendChangeToCashScheduleEmail:input_type -> emailSending.EmailChangeToTaskCashScheduleRequest
	6,  // 14: grpcEmailTH.EmailTH.SendEmail:output_type -> emailResponse.EmailResponse
	6,  // 15: grpcEmailTH.EmailTH.SendReceiptEmail:output_type -> emailResponse.EmailResponse
	6,  // 16: grpcEmailTH.EmailTH.SendVerifyEmail:output_type -> emailResponse.EmailResponse
	6,  // 17: grpcEmailTH.EmailTH.SendRenewSubscriptionEmail:output_type -> emailResponse.EmailResponse
	6,  // 18: grpcEmailTH.EmailTH.ResendReceiptEmail:output_type -> emailResponse.EmailResponse
	6,  // 19: grpcEmailTH.EmailTH.ResendSubscriptionOrderEmail:output_type -> emailResponse.EmailResponse
	6,  // 20: grpcEmailTH.EmailTH.SendReceiptSubscriptionEmail:output_type -> emailResponse.EmailResponse
	6,  // 21: grpcEmailTH.EmailTH.SendSubscriptionSuggestionEmail:output_type -> emailResponse.EmailResponse
	6,  // 22: grpcEmailTH.EmailTH.SendCancelFeeEmail:output_type -> emailResponse.EmailResponse
	6,  // 23: grpcEmailTH.EmailTH.SendSubscriptionRenewedEmail:output_type -> emailResponse.EmailResponse
	6,  // 24: grpcEmailTH.EmailTH.SendTopUpSuccessEmail:output_type -> emailResponse.EmailResponse
	6,  // 25: grpcEmailTH.EmailTH.SendCancelChargeFailEmail:output_type -> emailResponse.EmailResponse
	6,  // 26: grpcEmailTH.EmailTH.SendChargeFailEmail:output_type -> emailResponse.EmailResponse
	6,  // 27: grpcEmailTH.EmailTH.SendChangeToCashScheduleEmail:output_type -> emailResponse.EmailResponse
	14, // [14:28] is the sub-list for method output_type
	0,  // [0:14] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_emailTH_proto_init() }
func file_emailTH_proto_init() {
	if File_emailTH_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_emailTH_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_emailTH_proto_goTypes,
		DependencyIndexes: file_emailTH_proto_depIdxs,
	}.Build()
	File_emailTH_proto = out.File
	file_emailTH_proto_rawDesc = nil
	file_emailTH_proto_goTypes = nil
	file_emailTH_proto_depIdxs = nil
}
