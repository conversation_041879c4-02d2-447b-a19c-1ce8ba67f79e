// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcBPointMY

import (
	context "context"
	bPointRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/bPointRequest"
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// BPointClient is the client API for BPoint service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BPointClient interface {
	AccumulatePoints(ctx context.Context, in *bPointRequest.BPointRequest, opts ...grpc.CallOption) (*response.Response, error)
	TaskerAccumulatePoints(ctx context.Context, in *bPointRequest.BPointRequest, opts ...grpc.CallOption) (*response.Response, error)
}

type bPointClient struct {
	cc grpc.ClientConnInterface
}

func NewBPointClient(cc grpc.ClientConnInterface) BPointClient {
	return &bPointClient{cc}
}

func (c *bPointClient) AccumulatePoints(ctx context.Context, in *bPointRequest.BPointRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcBPointMY.BPoint/AccumulatePoints", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *bPointClient) TaskerAccumulatePoints(ctx context.Context, in *bPointRequest.BPointRequest, opts ...grpc.CallOption) (*response.Response, error) {
	out := new(response.Response)
	err := c.cc.Invoke(ctx, "/grpcBPointMY.BPoint/TaskerAccumulatePoints", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BPointServer is the server API for BPoint service.
// All implementations must embed UnimplementedBPointServer
// for forward compatibility
type BPointServer interface {
	AccumulatePoints(context.Context, *bPointRequest.BPointRequest) (*response.Response, error)
	TaskerAccumulatePoints(context.Context, *bPointRequest.BPointRequest) (*response.Response, error)
	mustEmbedUnimplementedBPointServer()
}

// UnimplementedBPointServer must be embedded to have forward compatible implementations.
type UnimplementedBPointServer struct {
}

func (UnimplementedBPointServer) AccumulatePoints(context.Context, *bPointRequest.BPointRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AccumulatePoints not implemented")
}
func (UnimplementedBPointServer) TaskerAccumulatePoints(context.Context, *bPointRequest.BPointRequest) (*response.Response, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TaskerAccumulatePoints not implemented")
}
func (UnimplementedBPointServer) mustEmbedUnimplementedBPointServer() {}

// UnsafeBPointServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BPointServer will
// result in compilation errors.
type UnsafeBPointServer interface {
	mustEmbedUnimplementedBPointServer()
}

func RegisterBPointServer(s *grpc.Server, srv BPointServer) {
	s.RegisterService(&_BPoint_serviceDesc, srv)
}

func _BPoint_AccumulatePoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(bPointRequest.BPointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BPointServer).AccumulatePoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBPointMY.BPoint/AccumulatePoints",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BPointServer).AccumulatePoints(ctx, req.(*bPointRequest.BPointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _BPoint_TaskerAccumulatePoints_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(bPointRequest.BPointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BPointServer).TaskerAccumulatePoints(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBPointMY.BPoint/TaskerAccumulatePoints",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BPointServer).TaskerAccumulatePoints(ctx, req.(*bPointRequest.BPointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _BPoint_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcBPointMY.BPoint",
	HandlerType: (*BPointServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "AccumulatePoints",
			Handler:    _BPoint_AccumulatePoints_Handler,
		},
		{
			MethodName: "TaskerAccumulatePoints",
			Handler:    _BPoint_TaskerAccumulatePoints_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "bPoint.proto",
}
