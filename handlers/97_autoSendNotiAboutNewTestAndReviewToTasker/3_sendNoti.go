package autoSendNotiAboutNewTestAndReviewToTasker

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

func sendNotification(notiBodies []*model.NotiNewTestAndReviewAndDeadlineBody) {
	for _, notiBody := range notiBodies {
		if notiBody.CourseType == globalConstant.TRAINING_COURSE_TYPE_TEST {
			var listNotification []interface{}
			var userIds []*modelPushNotificationRequest.PushNotificationRequestUserIds
			for _, taskerId := range notiBody.TaskerIds {
				notify := &modelNotification.Notification{
					XId:         globalLib.GenerateObjectId(),
					UserId:      taskerId,
					Type:        25,
					Description: localization.T(globalConstant.LANG_VI, "NOTIFICATION_ABOUT_NEW_TEST", notiBody.CourseTitle),
					CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
					CourseId:    notiBody.CourseId,
					CourseTitle: notiBody.CourseTitle,
					NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_TRAINING_PROGRAM_LIST,
				}
				listNotification = append(listNotification, notify)

				userId := modelPushNotificationRequest.PushNotificationRequestUserIds{
					UserId:   taskerId,
					Language: globalConstant.LANG_VI,
				}
				userIds = append(userIds, &userId)
			}
			payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
				Type:        25,
				CourseId:    notiBody.CourseId,
				CourseTitle: notiBody.CourseTitle,
				NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_TRAINING_PROGRAM_LIST,
			}
			text := localization.GetLocalizeObject("NOTIFICATION_ABOUT_NEW_TEST", notiBody.CourseTitle)
			lib.SendNotification(listNotification, userIds, nil, text, payload, true)

			//set field isSentNotiNewCourse to ignore resending notification
			query := bson.M{
				"courseId": notiBody.CourseId,
				"taskerId": bson.M{"$in": notiBody.TaskerIds},
			}
			updateCourseStartDateQuery := bson.M{
				"$set": bson.M{
					"isSentNotiNewCourse": true,
				},
			}
			err := handlers.UpdateCoursesStartDateByQuery(query, updateCourseStartDateQuery)
			if err != nil {
				text := fmt.Sprintf("AutoSendNotiAboutNewTestAndReviewToAsker %s: Lỗi call API SendNotiAboutNewTestAndReviewToAsker , err: %s ", local.ISO_CODE, err.Error())
				globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], text)
			}

		} else if notiBody.CourseType == globalConstant.TRAINING_COURSE_TYPE_REVIEW {
			var listNotification []interface{}
			var userIds []*modelPushNotificationRequest.PushNotificationRequestUserIds
			for _, taskerId := range notiBody.TaskerIds {
				notify := &modelNotification.Notification{
					XId:         globalLib.GenerateObjectId(),
					UserId:      taskerId,
					Type:        25,
					Description: localization.T(globalConstant.LANG_VI, "NOTIFICATION_ABOUT_NEW_REVIEW", notiBody.CourseTitle),
					CreatedAt:   globalLib.GetCurrentTimestamp(local.TimeZone),
					CourseId:    notiBody.CourseId,
					CourseTitle: notiBody.CourseTitle,
					NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_REVIEW_COLLECTION_LIST,
				}
				listNotification = append(listNotification, notify)

				userId := modelPushNotificationRequest.PushNotificationRequestUserIds{
					UserId:   taskerId,
					Language: globalConstant.LANG_VI,
				}
				userIds = append(userIds, &userId)
			}
			payload := &modelPushNotificationRequest.PushNotificationRequestPayload{
				Type:        25,
				CourseId:    notiBody.CourseId,
				CourseTitle: notiBody.CourseTitle,
				NavigateTo:  globalConstant.PAYLOAD_NAVIGATE_TO_REVIEW_COLLECTION_LIST,
			}
			text := localization.GetLocalizeObject("NOTIFICATION_ABOUT_NEW_REVIEW", notiBody.CourseTitle)
			lib.SendNotification(listNotification, userIds, nil, text, payload, true)
			//set field isSentNotiNewCourse to ignore resending notification
			query := bson.M{
				"courseId": notiBody.CourseId,
				"taskerId": bson.M{"$in": notiBody.TaskerIds},
			}
			updateCourseStartDateQuery := bson.M{
				"$set": bson.M{
					"isSentNotiNewCourse": true,
				},
			}
			err := handlers.UpdateCoursesStartDateByQuery(query, updateCourseStartDateQuery)
			if err != nil {
				text := fmt.Sprintf("AutoSendNotiAboutNewTestAndReviewToAsker %s: Lỗi call API SendNotiAboutNewTestAndReviewToAsker , err: %s ", local.ISO_CODE, err.Error())
				globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], text)
			}
		}
	}
}
