package sendBusinessReportEmail

import (
	"github.com/xuri/excelize/v2"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessTransaction"
	"gitlab.com/btaskee/go-services-model-v2/localization"
)

func depositTransactionSheet(language string, f *excelize.File, depositTransactions []*businessTransaction.BusinessTransaction,
	mapMemberDetail map[string]*MemberInfo, businessName string, month, year int32) {
	sheetName := "Sheet2" // from template
	defaultHeight := 30.0
	setDefaultCellDepositSheet(language, f, sheetName, businessName, month, year)
	noColumn, dateColumn, codeColumn, sourceColumn, reasonColumn, amountColumn := 1, 2, 3, 4, 5, 6 // Column index: A, B, C, D, E
	startRow := 4

	totalAmount := float64(0)
	for i, transaction := range depositTransactions {
		// No. column A
		cell, _ := excelize.CoordinatesToCellName(noColumn, i+startRow)
		f.SetCellValue(sheetName, cell, i+1)
		// Date column B
		cell, _ = excelize.CoordinatesToCellName(dateColumn, i+startRow)
		f.SetCellValue(sheetName, cell, transaction.GetCreatedAt().AsTime().In(local.TimeZone).Format(formatTime))
		// Code column C
		cell, _ = excelize.CoordinatesToCellName(codeColumn, i+startRow)
		f.SetCellValue(sheetName, cell, "") // TODO: transaction code when be done
		// Source column D
		cell, _ = excelize.CoordinatesToCellName(sourceColumn, i+startRow)
		f.SetCellValue(sheetName, cell, getDepositTransactionSource(language, transaction.Name, transaction.MemberId, mapMemberDetail))
		// Reason column E
		cell, _ = excelize.CoordinatesToCellName(reasonColumn, i+startRow)
		f.SetCellValue(sheetName, cell, localization.T(language, globalConstant.BUSINESS_TRANSACTION_NAME_PREFIX+transaction.Name))
		// Amount column F
		cell, _ = excelize.CoordinatesToCellName(amountColumn, i+startRow)
		f.SetCellValue(sheetName, cell, globalLib.FormatMoney(transaction.Amount))
		totalAmount += transaction.Amount

		f.SetRowHeight(sheetName, i+startRow, defaultHeight) // default height of template
	}

	// border
	startCell, _ := excelize.CoordinatesToCellName(noColumn, startRow)
	endCell, _ := excelize.CoordinatesToCellName(amountColumn, len(depositTransactions)+startRow)
	setBorderStyle(f, sheetName, startCell, endCell, false)

	// total amount in, bold
	lastLineNo, _ := excelize.CoordinatesToCellName(noColumn, len(depositTransactions)+startRow)
	f.SetCellValue(sheetName, lastLineNo, localization.T(language, "BUSINESS_REPORT_SHEET_2_TOTAL_AMOUNT_IN"))
	lastLineAmount, _ := excelize.CoordinatesToCellName(amountColumn, len(depositTransactions)+startRow)
	f.SetCellValue(sheetName, lastLineAmount, globalLib.FormatMoney(totalAmount))
	setBorderStyle(f, sheetName, lastLineNo, lastLineAmount, true)
	f.SetRowHeight(sheetName, len(depositTransactions)+startRow, defaultHeight) // default height of template

}

func setDefaultCellDepositSheet(language string, f *excelize.File, sheetName string, businessName string, month, year int32) {
	f.SetCellValue(sheetName, "A1", localization.T(language, "BUSINESS_REPORT_SHEET_BTASKEE_TITLE"))
	f.SetCellValue(sheetName, "A2", localization.T(language, "BUSINESS_REPORT_SHEET_BUSINESS_NAME", businessName))
	f.SetCellValue(sheetName, "C2", localization.T(language, "BUSINESS_REPORT_SHEET_DATE", month, year))
	f.SetCellValue(sheetName, "A3", localization.T(language, "BUSINESS_REPORT_SHEET_HEADER_NO"))
	f.SetCellValue(sheetName, "B3", localization.T(language, "BUSINESS_REPORT_SHEET_HEADER_TRANSACTION_DATE"))
	f.SetCellValue(sheetName, "C3", localization.T(language, "BUSINESS_REPORT_SHEET_2_HEADER_TRANSACTION_CODE"))
	f.SetCellValue(sheetName, "D3", localization.T(language, "BUSINESS_REPORT_SHEET_2_HEADER_TRANSACTION_SOURCE"))
	f.SetCellValue(sheetName, "E3", localization.T(language, "BUSINESS_REPORT_SHEET_2_HEADER_TRANSACTION_REASON"))
	f.SetCellValue(sheetName, "F3", localization.T(language, "BUSINESS_REPORT_SHEET_HEADER_TRANSACTION_AMOUNT"))
}

func getDepositTransactionSource(language, transactionName, memberId string, mapMemberDetail map[string]*MemberInfo) string {
	switch transactionName {
	case globalConstant.BUSINESS_TRANSACTION_NAME_TOP_UP_BPAY_BY_BTASKEE:
		return localization.T(language, "BUSINESS_REPORT_SHEET_2_CONTENT_TRANSFER")
	case globalConstant.BUSINESS_TRANSACTION_NAME_REVOKE_BPAY_MEMBER:
		member, ok := mapMemberDetail[memberId]
		if !ok {
			return ""
		}
		return member.Phone
	default:
		return ""
	}
}
