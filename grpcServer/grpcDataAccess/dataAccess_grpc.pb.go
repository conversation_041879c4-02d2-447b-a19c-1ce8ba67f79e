// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcDataAccess

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	dataAccess "gitlab.com/btaskee/go-services-model-v2/grpcmodel/dataAccess"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// DataAccessClient is the client API for DataAccess service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DataAccessClient interface {
	IsExistById(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	IsExistByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	CountByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	GetOneById(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	GetOneByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	GetOneByQueryMap(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	GetOneByQuerySort(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	GetAllByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	GetAllByQueryPaging(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	GetAllByQuerySort(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	GetAllByQueryPagingSort(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	GetAllByQueryLimitSort(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	GetAllByQuerySkipLimitSort(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	GetDistinctByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	Aggregate(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	InsertOne(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	InsertAll(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	UpdateOneById(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	UpdateOneByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	UpdateAllByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error)
	UpsertOneById(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	UpsertOneByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	DeleteOneById(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	DeleteOneByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	DeleteAllByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type dataAccessClient struct {
	cc grpc.ClientConnInterface
}

func NewDataAccessClient(cc grpc.ClientConnInterface) DataAccessClient {
	return &dataAccessClient{cc}
}

func (c *dataAccessClient) IsExistById(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/IsExistById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) IsExistByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/IsExistByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) CountByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/CountByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) GetOneById(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/GetOneById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) GetOneByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/GetOneByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) GetOneByQueryMap(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/GetOneByQueryMap", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) GetOneByQuerySort(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/GetOneByQuerySort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) GetAllByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/GetAllByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) GetAllByQueryPaging(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/GetAllByQueryPaging", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) GetAllByQuerySort(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/GetAllByQuerySort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) GetAllByQueryPagingSort(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/GetAllByQueryPagingSort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) GetAllByQueryLimitSort(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/GetAllByQueryLimitSort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) GetAllByQuerySkipLimitSort(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/GetAllByQuerySkipLimitSort", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) GetDistinctByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/GetDistinctByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) Aggregate(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/Aggregate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) InsertOne(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/InsertOne", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) InsertAll(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/InsertAll", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) UpdateOneById(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/UpdateOneById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) UpdateOneByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/UpdateOneByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) UpdateAllByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*dataAccess.DataAccessResponse, error) {
	out := new(dataAccess.DataAccessResponse)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/UpdateAllByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) UpsertOneById(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/UpsertOneById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) UpsertOneByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/UpsertOneByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) DeleteOneById(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/DeleteOneById", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) DeleteOneByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/DeleteOneByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataAccessClient) DeleteAllByQuery(ctx context.Context, in *dataAccess.DataAccessRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcDataAccess.DataAccess/DeleteAllByQuery", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DataAccessServer is the server API for DataAccess service.
// All implementations must embed UnimplementedDataAccessServer
// for forward compatibility
type DataAccessServer interface {
	IsExistById(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	IsExistByQuery(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	CountByQuery(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	GetOneById(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	GetOneByQuery(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	GetOneByQueryMap(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	GetOneByQuerySort(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	GetAllByQuery(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	GetAllByQueryPaging(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	GetAllByQuerySort(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	GetAllByQueryPagingSort(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	GetAllByQueryLimitSort(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	GetAllByQuerySkipLimitSort(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	GetDistinctByQuery(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	Aggregate(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	InsertOne(context.Context, *dataAccess.DataAccessRequest) (*empty.Empty, error)
	InsertAll(context.Context, *dataAccess.DataAccessRequest) (*empty.Empty, error)
	UpdateOneById(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	UpdateOneByQuery(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	UpdateAllByQuery(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error)
	UpsertOneById(context.Context, *dataAccess.DataAccessRequest) (*empty.Empty, error)
	UpsertOneByQuery(context.Context, *dataAccess.DataAccessRequest) (*empty.Empty, error)
	DeleteOneById(context.Context, *dataAccess.DataAccessRequest) (*empty.Empty, error)
	DeleteOneByQuery(context.Context, *dataAccess.DataAccessRequest) (*empty.Empty, error)
	DeleteAllByQuery(context.Context, *dataAccess.DataAccessRequest) (*empty.Empty, error)
	mustEmbedUnimplementedDataAccessServer()
}

// UnimplementedDataAccessServer must be embedded to have forward compatible implementations.
type UnimplementedDataAccessServer struct {
}

func (UnimplementedDataAccessServer) IsExistById(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsExistById not implemented")
}
func (UnimplementedDataAccessServer) IsExistByQuery(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsExistByQuery not implemented")
}
func (UnimplementedDataAccessServer) CountByQuery(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CountByQuery not implemented")
}
func (UnimplementedDataAccessServer) GetOneById(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOneById not implemented")
}
func (UnimplementedDataAccessServer) GetOneByQuery(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOneByQuery not implemented")
}
func (UnimplementedDataAccessServer) GetOneByQueryMap(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOneByQueryMap not implemented")
}
func (UnimplementedDataAccessServer) GetOneByQuerySort(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOneByQuerySort not implemented")
}
func (UnimplementedDataAccessServer) GetAllByQuery(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllByQuery not implemented")
}
func (UnimplementedDataAccessServer) GetAllByQueryPaging(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllByQueryPaging not implemented")
}
func (UnimplementedDataAccessServer) GetAllByQuerySort(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllByQuerySort not implemented")
}
func (UnimplementedDataAccessServer) GetAllByQueryPagingSort(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllByQueryPagingSort not implemented")
}
func (UnimplementedDataAccessServer) GetAllByQueryLimitSort(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllByQueryLimitSort not implemented")
}
func (UnimplementedDataAccessServer) GetAllByQuerySkipLimitSort(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetAllByQuerySkipLimitSort not implemented")
}
func (UnimplementedDataAccessServer) GetDistinctByQuery(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDistinctByQuery not implemented")
}
func (UnimplementedDataAccessServer) Aggregate(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Aggregate not implemented")
}
func (UnimplementedDataAccessServer) InsertOne(context.Context, *dataAccess.DataAccessRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InsertOne not implemented")
}
func (UnimplementedDataAccessServer) InsertAll(context.Context, *dataAccess.DataAccessRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InsertAll not implemented")
}
func (UnimplementedDataAccessServer) UpdateOneById(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOneById not implemented")
}
func (UnimplementedDataAccessServer) UpdateOneByQuery(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateOneByQuery not implemented")
}
func (UnimplementedDataAccessServer) UpdateAllByQuery(context.Context, *dataAccess.DataAccessRequest) (*dataAccess.DataAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateAllByQuery not implemented")
}
func (UnimplementedDataAccessServer) UpsertOneById(context.Context, *dataAccess.DataAccessRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertOneById not implemented")
}
func (UnimplementedDataAccessServer) UpsertOneByQuery(context.Context, *dataAccess.DataAccessRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpsertOneByQuery not implemented")
}
func (UnimplementedDataAccessServer) DeleteOneById(context.Context, *dataAccess.DataAccessRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteOneById not implemented")
}
func (UnimplementedDataAccessServer) DeleteOneByQuery(context.Context, *dataAccess.DataAccessRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteOneByQuery not implemented")
}
func (UnimplementedDataAccessServer) DeleteAllByQuery(context.Context, *dataAccess.DataAccessRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAllByQuery not implemented")
}
func (UnimplementedDataAccessServer) mustEmbedUnimplementedDataAccessServer() {}

// UnsafeDataAccessServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DataAccessServer will
// result in compilation errors.
type UnsafeDataAccessServer interface {
	mustEmbedUnimplementedDataAccessServer()
}

func RegisterDataAccessServer(s *grpc.Server, srv DataAccessServer) {
	s.RegisterService(&_DataAccess_serviceDesc, srv)
}

func _DataAccess_IsExistById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).IsExistById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/IsExistById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).IsExistById(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_IsExistByQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).IsExistByQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/IsExistByQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).IsExistByQuery(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_CountByQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).CountByQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/CountByQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).CountByQuery(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_GetOneById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).GetOneById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/GetOneById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).GetOneById(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_GetOneByQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).GetOneByQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/GetOneByQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).GetOneByQuery(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_GetOneByQueryMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).GetOneByQueryMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/GetOneByQueryMap",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).GetOneByQueryMap(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_GetOneByQuerySort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).GetOneByQuerySort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/GetOneByQuerySort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).GetOneByQuerySort(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_GetAllByQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).GetAllByQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/GetAllByQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).GetAllByQuery(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_GetAllByQueryPaging_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).GetAllByQueryPaging(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/GetAllByQueryPaging",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).GetAllByQueryPaging(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_GetAllByQuerySort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).GetAllByQuerySort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/GetAllByQuerySort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).GetAllByQuerySort(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_GetAllByQueryPagingSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).GetAllByQueryPagingSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/GetAllByQueryPagingSort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).GetAllByQueryPagingSort(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_GetAllByQueryLimitSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).GetAllByQueryLimitSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/GetAllByQueryLimitSort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).GetAllByQueryLimitSort(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_GetAllByQuerySkipLimitSort_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).GetAllByQuerySkipLimitSort(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/GetAllByQuerySkipLimitSort",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).GetAllByQuerySkipLimitSort(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_GetDistinctByQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).GetDistinctByQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/GetDistinctByQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).GetDistinctByQuery(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_Aggregate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).Aggregate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/Aggregate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).Aggregate(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_InsertOne_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).InsertOne(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/InsertOne",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).InsertOne(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_InsertAll_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).InsertAll(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/InsertAll",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).InsertAll(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_UpdateOneById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).UpdateOneById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/UpdateOneById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).UpdateOneById(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_UpdateOneByQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).UpdateOneByQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/UpdateOneByQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).UpdateOneByQuery(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_UpdateAllByQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).UpdateAllByQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/UpdateAllByQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).UpdateAllByQuery(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_UpsertOneById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).UpsertOneById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/UpsertOneById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).UpsertOneById(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_UpsertOneByQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).UpsertOneByQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/UpsertOneByQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).UpsertOneByQuery(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_DeleteOneById_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).DeleteOneById(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/DeleteOneById",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).DeleteOneById(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_DeleteOneByQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).DeleteOneByQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/DeleteOneByQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).DeleteOneByQuery(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataAccess_DeleteAllByQuery_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataAccess.DataAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataAccessServer).DeleteAllByQuery(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataAccess.DataAccess/DeleteAllByQuery",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataAccessServer).DeleteAllByQuery(ctx, req.(*dataAccess.DataAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _DataAccess_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcDataAccess.DataAccess",
	HandlerType: (*DataAccessServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "IsExistById",
			Handler:    _DataAccess_IsExistById_Handler,
		},
		{
			MethodName: "IsExistByQuery",
			Handler:    _DataAccess_IsExistByQuery_Handler,
		},
		{
			MethodName: "CountByQuery",
			Handler:    _DataAccess_CountByQuery_Handler,
		},
		{
			MethodName: "GetOneById",
			Handler:    _DataAccess_GetOneById_Handler,
		},
		{
			MethodName: "GetOneByQuery",
			Handler:    _DataAccess_GetOneByQuery_Handler,
		},
		{
			MethodName: "GetOneByQueryMap",
			Handler:    _DataAccess_GetOneByQueryMap_Handler,
		},
		{
			MethodName: "GetOneByQuerySort",
			Handler:    _DataAccess_GetOneByQuerySort_Handler,
		},
		{
			MethodName: "GetAllByQuery",
			Handler:    _DataAccess_GetAllByQuery_Handler,
		},
		{
			MethodName: "GetAllByQueryPaging",
			Handler:    _DataAccess_GetAllByQueryPaging_Handler,
		},
		{
			MethodName: "GetAllByQuerySort",
			Handler:    _DataAccess_GetAllByQuerySort_Handler,
		},
		{
			MethodName: "GetAllByQueryPagingSort",
			Handler:    _DataAccess_GetAllByQueryPagingSort_Handler,
		},
		{
			MethodName: "GetAllByQueryLimitSort",
			Handler:    _DataAccess_GetAllByQueryLimitSort_Handler,
		},
		{
			MethodName: "GetAllByQuerySkipLimitSort",
			Handler:    _DataAccess_GetAllByQuerySkipLimitSort_Handler,
		},
		{
			MethodName: "GetDistinctByQuery",
			Handler:    _DataAccess_GetDistinctByQuery_Handler,
		},
		{
			MethodName: "Aggregate",
			Handler:    _DataAccess_Aggregate_Handler,
		},
		{
			MethodName: "InsertOne",
			Handler:    _DataAccess_InsertOne_Handler,
		},
		{
			MethodName: "InsertAll",
			Handler:    _DataAccess_InsertAll_Handler,
		},
		{
			MethodName: "UpdateOneById",
			Handler:    _DataAccess_UpdateOneById_Handler,
		},
		{
			MethodName: "UpdateOneByQuery",
			Handler:    _DataAccess_UpdateOneByQuery_Handler,
		},
		{
			MethodName: "UpdateAllByQuery",
			Handler:    _DataAccess_UpdateAllByQuery_Handler,
		},
		{
			MethodName: "UpsertOneById",
			Handler:    _DataAccess_UpsertOneById_Handler,
		},
		{
			MethodName: "UpsertOneByQuery",
			Handler:    _DataAccess_UpsertOneByQuery_Handler,
		},
		{
			MethodName: "DeleteOneById",
			Handler:    _DataAccess_DeleteOneById_Handler,
		},
		{
			MethodName: "DeleteOneByQuery",
			Handler:    _DataAccess_DeleteOneByQuery_Handler,
		},
		{
			MethodName: "DeleteAllByQuery",
			Handler:    _DataAccess_DeleteAllByQuery_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dataAccess.proto",
}
