// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcChatServerVN

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	websocketMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// ChatServerVNClient is the client API for ChatServerVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ChatServerVNClient interface {
	SendSocketChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error)
}

type chatServerVNClient struct {
	cc grpc.ClientConnInterface
}

func NewChatServerVNClient(cc grpc.ClientConnInterface) ChatServerVNClient {
	return &chatServerVNClient{cc}
}

func (c *chatServerVNClient) SendSocketChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcChatServerVN.ChatServerVN/SendSocketChatMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ChatServerVNServer is the server API for ChatServerVN service.
// All implementations must embed UnimplementedChatServerVNServer
// for forward compatibility
type ChatServerVNServer interface {
	SendSocketChatMessage(context.Context, *websocketMessage.WebsocketMessage) (*empty.Empty, error)
	mustEmbedUnimplementedChatServerVNServer()
}

// UnimplementedChatServerVNServer must be embedded to have forward compatible implementations.
type UnimplementedChatServerVNServer struct {
}

func (UnimplementedChatServerVNServer) SendSocketChatMessage(context.Context, *websocketMessage.WebsocketMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSocketChatMessage not implemented")
}
func (UnimplementedChatServerVNServer) mustEmbedUnimplementedChatServerVNServer() {}

// UnsafeChatServerVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ChatServerVNServer will
// result in compilation errors.
type UnsafeChatServerVNServer interface {
	mustEmbedUnimplementedChatServerVNServer()
}

func RegisterChatServerVNServer(s *grpc.Server, srv ChatServerVNServer) {
	s.RegisterService(&_ChatServerVN_serviceDesc, srv)
}

func _ChatServerVN_SendSocketChatMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(websocketMessage.WebsocketMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ChatServerVNServer).SendSocketChatMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcChatServerVN.ChatServerVN/SendSocketChatMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ChatServerVNServer).SendSocketChatMessage(ctx, req.(*websocketMessage.WebsocketMessage))
	}
	return interceptor(ctx, in, info, handler)
}

var _ChatServerVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcChatServerVN.ChatServerVN",
	HandlerType: (*ChatServerVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendSocketChatMessage",
			Handler:    _ChatServerVN_SendSocketChatMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "chatServerVN.proto",
}
