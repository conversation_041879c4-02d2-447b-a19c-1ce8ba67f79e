// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcEmailVN

import (
	context "context"
	emailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	emailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	task "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	users "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// EmailVNClient is the client API for EmailVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EmailVNClient interface {
	SendEmail(ctx context.Context, in *emailSending.EmailSending, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendReceiptEmail(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendVerifyEmail(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendRenewSubscriptionEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	ResendReceiptEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	ResendSubscriptionOrderEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendReceiptSubscriptionEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendSubscriptionSuggestionEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendCancelFeeEmail(ctx context.Context, in *emailSending.EmailCancelFeeRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendSubscriptionRenewedEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendTopUpSuccessEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendCancelChargeFailEmail(ctx context.Context, in *emailSending.EmailCancelFeeRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendChargeFailEmail(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendChangeToCashScheduleEmail(ctx context.Context, in *emailSending.EmailChangeToTaskCashScheduleRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
	SendBusinessReportEmail(ctx context.Context, in *emailSending.EmailSendBusinessReportRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error)
}

type emailVNClient struct {
	cc grpc.ClientConnInterface
}

func NewEmailVNClient(cc grpc.ClientConnInterface) EmailVNClient {
	return &emailVNClient{cc}
}

func (c *emailVNClient) SendEmail(ctx context.Context, in *emailSending.EmailSending, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailVN.EmailVN/SendEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailVNClient) SendReceiptEmail(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailVN.EmailVN/SendReceiptEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailVNClient) SendVerifyEmail(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailVN.EmailVN/SendVerifyEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailVNClient) SendRenewSubscriptionEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailVN.EmailVN/SendRenewSubscriptionEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailVNClient) ResendReceiptEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailVN.EmailVN/ResendReceiptEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailVNClient) ResendSubscriptionOrderEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailVN.EmailVN/ResendSubscriptionOrderEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailVNClient) SendReceiptSubscriptionEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailVN.EmailVN/SendReceiptSubscriptionEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailVNClient) SendSubscriptionSuggestionEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailVN.EmailVN/SendSubscriptionSuggestionEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailVNClient) SendCancelFeeEmail(ctx context.Context, in *emailSending.EmailCancelFeeRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailVN.EmailVN/SendCancelFeeEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailVNClient) SendSubscriptionRenewedEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailVN.EmailVN/SendSubscriptionRenewedEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailVNClient) SendTopUpSuccessEmail(ctx context.Context, in *emailSending.EmailRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailVN.EmailVN/SendTopUpSuccessEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailVNClient) SendCancelChargeFailEmail(ctx context.Context, in *emailSending.EmailCancelFeeRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailVN.EmailVN/SendCancelChargeFailEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailVNClient) SendChargeFailEmail(ctx context.Context, in *task.Task, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailVN.EmailVN/SendChargeFailEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailVNClient) SendChangeToCashScheduleEmail(ctx context.Context, in *emailSending.EmailChangeToTaskCashScheduleRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailVN.EmailVN/SendChangeToCashScheduleEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *emailVNClient) SendBusinessReportEmail(ctx context.Context, in *emailSending.EmailSendBusinessReportRequest, opts ...grpc.CallOption) (*emailResponse.EmailResponse, error) {
	out := new(emailResponse.EmailResponse)
	err := c.cc.Invoke(ctx, "/grpcEmailVN.EmailVN/SendBusinessReportEmail", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EmailVNServer is the server API for EmailVN service.
// All implementations must embed UnimplementedEmailVNServer
// for forward compatibility
type EmailVNServer interface {
	SendEmail(context.Context, *emailSending.EmailSending) (*emailResponse.EmailResponse, error)
	SendReceiptEmail(context.Context, *task.Task) (*emailResponse.EmailResponse, error)
	SendVerifyEmail(context.Context, *users.Users) (*emailResponse.EmailResponse, error)
	SendRenewSubscriptionEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error)
	ResendReceiptEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error)
	ResendSubscriptionOrderEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error)
	SendReceiptSubscriptionEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error)
	SendSubscriptionSuggestionEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error)
	SendCancelFeeEmail(context.Context, *emailSending.EmailCancelFeeRequest) (*emailResponse.EmailResponse, error)
	SendSubscriptionRenewedEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error)
	SendTopUpSuccessEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error)
	SendCancelChargeFailEmail(context.Context, *emailSending.EmailCancelFeeRequest) (*emailResponse.EmailResponse, error)
	SendChargeFailEmail(context.Context, *task.Task) (*emailResponse.EmailResponse, error)
	SendChangeToCashScheduleEmail(context.Context, *emailSending.EmailChangeToTaskCashScheduleRequest) (*emailResponse.EmailResponse, error)
	SendBusinessReportEmail(context.Context, *emailSending.EmailSendBusinessReportRequest) (*emailResponse.EmailResponse, error)
	mustEmbedUnimplementedEmailVNServer()
}

// UnimplementedEmailVNServer must be embedded to have forward compatible implementations.
type UnimplementedEmailVNServer struct {
}

func (UnimplementedEmailVNServer) SendEmail(context.Context, *emailSending.EmailSending) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendEmail not implemented")
}
func (UnimplementedEmailVNServer) SendReceiptEmail(context.Context, *task.Task) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendReceiptEmail not implemented")
}
func (UnimplementedEmailVNServer) SendVerifyEmail(context.Context, *users.Users) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendVerifyEmail not implemented")
}
func (UnimplementedEmailVNServer) SendRenewSubscriptionEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendRenewSubscriptionEmail not implemented")
}
func (UnimplementedEmailVNServer) ResendReceiptEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResendReceiptEmail not implemented")
}
func (UnimplementedEmailVNServer) ResendSubscriptionOrderEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResendSubscriptionOrderEmail not implemented")
}
func (UnimplementedEmailVNServer) SendReceiptSubscriptionEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendReceiptSubscriptionEmail not implemented")
}
func (UnimplementedEmailVNServer) SendSubscriptionSuggestionEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSubscriptionSuggestionEmail not implemented")
}
func (UnimplementedEmailVNServer) SendCancelFeeEmail(context.Context, *emailSending.EmailCancelFeeRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendCancelFeeEmail not implemented")
}
func (UnimplementedEmailVNServer) SendSubscriptionRenewedEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSubscriptionRenewedEmail not implemented")
}
func (UnimplementedEmailVNServer) SendTopUpSuccessEmail(context.Context, *emailSending.EmailRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendTopUpSuccessEmail not implemented")
}
func (UnimplementedEmailVNServer) SendCancelChargeFailEmail(context.Context, *emailSending.EmailCancelFeeRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendCancelChargeFailEmail not implemented")
}
func (UnimplementedEmailVNServer) SendChargeFailEmail(context.Context, *task.Task) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendChargeFailEmail not implemented")
}
func (UnimplementedEmailVNServer) SendChangeToCashScheduleEmail(context.Context, *emailSending.EmailChangeToTaskCashScheduleRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendChangeToCashScheduleEmail not implemented")
}
func (UnimplementedEmailVNServer) SendBusinessReportEmail(context.Context, *emailSending.EmailSendBusinessReportRequest) (*emailResponse.EmailResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendBusinessReportEmail not implemented")
}
func (UnimplementedEmailVNServer) mustEmbedUnimplementedEmailVNServer() {}

// UnsafeEmailVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EmailVNServer will
// result in compilation errors.
type UnsafeEmailVNServer interface {
	mustEmbedUnimplementedEmailVNServer()
}

func RegisterEmailVNServer(s *grpc.Server, srv EmailVNServer) {
	s.RegisterService(&_EmailVN_serviceDesc, srv)
}

func _EmailVN_SendEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailSending)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailVNServer).SendEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailVN.EmailVN/SendEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailVNServer).SendEmail(ctx, req.(*emailSending.EmailSending))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailVN_SendReceiptEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(task.Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailVNServer).SendReceiptEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailVN.EmailVN/SendReceiptEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailVNServer).SendReceiptEmail(ctx, req.(*task.Task))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailVN_SendVerifyEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(users.Users)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailVNServer).SendVerifyEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailVN.EmailVN/SendVerifyEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailVNServer).SendVerifyEmail(ctx, req.(*users.Users))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailVN_SendRenewSubscriptionEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailVNServer).SendRenewSubscriptionEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailVN.EmailVN/SendRenewSubscriptionEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailVNServer).SendRenewSubscriptionEmail(ctx, req.(*emailSending.EmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailVN_ResendReceiptEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailVNServer).ResendReceiptEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailVN.EmailVN/ResendReceiptEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailVNServer).ResendReceiptEmail(ctx, req.(*emailSending.EmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailVN_ResendSubscriptionOrderEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailVNServer).ResendSubscriptionOrderEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailVN.EmailVN/ResendSubscriptionOrderEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailVNServer).ResendSubscriptionOrderEmail(ctx, req.(*emailSending.EmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailVN_SendReceiptSubscriptionEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailVNServer).SendReceiptSubscriptionEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailVN.EmailVN/SendReceiptSubscriptionEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailVNServer).SendReceiptSubscriptionEmail(ctx, req.(*emailSending.EmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailVN_SendSubscriptionSuggestionEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailVNServer).SendSubscriptionSuggestionEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailVN.EmailVN/SendSubscriptionSuggestionEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailVNServer).SendSubscriptionSuggestionEmail(ctx, req.(*emailSending.EmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailVN_SendCancelFeeEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailCancelFeeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailVNServer).SendCancelFeeEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailVN.EmailVN/SendCancelFeeEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailVNServer).SendCancelFeeEmail(ctx, req.(*emailSending.EmailCancelFeeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailVN_SendSubscriptionRenewedEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailVNServer).SendSubscriptionRenewedEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailVN.EmailVN/SendSubscriptionRenewedEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailVNServer).SendSubscriptionRenewedEmail(ctx, req.(*emailSending.EmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailVN_SendTopUpSuccessEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailVNServer).SendTopUpSuccessEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailVN.EmailVN/SendTopUpSuccessEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailVNServer).SendTopUpSuccessEmail(ctx, req.(*emailSending.EmailRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailVN_SendCancelChargeFailEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailCancelFeeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailVNServer).SendCancelChargeFailEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailVN.EmailVN/SendCancelChargeFailEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailVNServer).SendCancelChargeFailEmail(ctx, req.(*emailSending.EmailCancelFeeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailVN_SendChargeFailEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(task.Task)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailVNServer).SendChargeFailEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailVN.EmailVN/SendChargeFailEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailVNServer).SendChargeFailEmail(ctx, req.(*task.Task))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailVN_SendChangeToCashScheduleEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailChangeToTaskCashScheduleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailVNServer).SendChangeToCashScheduleEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailVN.EmailVN/SendChangeToCashScheduleEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailVNServer).SendChangeToCashScheduleEmail(ctx, req.(*emailSending.EmailChangeToTaskCashScheduleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EmailVN_SendBusinessReportEmail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(emailSending.EmailSendBusinessReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EmailVNServer).SendBusinessReportEmail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEmailVN.EmailVN/SendBusinessReportEmail",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EmailVNServer).SendBusinessReportEmail(ctx, req.(*emailSending.EmailSendBusinessReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _EmailVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcEmailVN.EmailVN",
	HandlerType: (*EmailVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendEmail",
			Handler:    _EmailVN_SendEmail_Handler,
		},
		{
			MethodName: "SendReceiptEmail",
			Handler:    _EmailVN_SendReceiptEmail_Handler,
		},
		{
			MethodName: "SendVerifyEmail",
			Handler:    _EmailVN_SendVerifyEmail_Handler,
		},
		{
			MethodName: "SendRenewSubscriptionEmail",
			Handler:    _EmailVN_SendRenewSubscriptionEmail_Handler,
		},
		{
			MethodName: "ResendReceiptEmail",
			Handler:    _EmailVN_ResendReceiptEmail_Handler,
		},
		{
			MethodName: "ResendSubscriptionOrderEmail",
			Handler:    _EmailVN_ResendSubscriptionOrderEmail_Handler,
		},
		{
			MethodName: "SendReceiptSubscriptionEmail",
			Handler:    _EmailVN_SendReceiptSubscriptionEmail_Handler,
		},
		{
			MethodName: "SendSubscriptionSuggestionEmail",
			Handler:    _EmailVN_SendSubscriptionSuggestionEmail_Handler,
		},
		{
			MethodName: "SendCancelFeeEmail",
			Handler:    _EmailVN_SendCancelFeeEmail_Handler,
		},
		{
			MethodName: "SendSubscriptionRenewedEmail",
			Handler:    _EmailVN_SendSubscriptionRenewedEmail_Handler,
		},
		{
			MethodName: "SendTopUpSuccessEmail",
			Handler:    _EmailVN_SendTopUpSuccessEmail_Handler,
		},
		{
			MethodName: "SendCancelChargeFailEmail",
			Handler:    _EmailVN_SendCancelChargeFailEmail_Handler,
		},
		{
			MethodName: "SendChargeFailEmail",
			Handler:    _EmailVN_SendChargeFailEmail_Handler,
		},
		{
			MethodName: "SendChangeToCashScheduleEmail",
			Handler:    _EmailVN_SendChangeToCashScheduleEmail_Handler,
		},
		{
			MethodName: "SendBusinessReportEmail",
			Handler:    _EmailVN_SendBusinessReportEmail_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "emailVN.proto",
}
