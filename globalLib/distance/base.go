package distance

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"sort"
	"time"

	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/globalLib/redisCache"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
)

type DistanceData struct {
	Rows         []DistanceDataRow `json:"rows,omitempty"`
	Status       string            `json:"status,omitempty"`
	ErrorMessage string            `json:"error_message,omitempty"`
}
type DistanceDataRow struct {
	Elements []DistanceDataRowElement `json:"elements,omitempty"`
}

type DistanceDataRowElement struct {
	Distance DistanceDataRowElementDistance `json:"distance,omitempty"`
	Duration DistanceDataRowElementDuration `json:"duration,omitempty"`
}

type DistanceDataRowElementDistance struct {
	Text  string  `json:"text,omitempty"`
	Value float64 `json:"value,omitempty"`
}

type DistanceDataRowElementDuration struct {
	Text  string  `json:"text,omitempty"`
	Value float64 `json:"value,omitempty"`
}

func GetDistanceMatrix(isoCode string, oldLat, oldLng, newLat, newLng float64, slackToken, googleMapApiKey string, redisCache *redisCache.RedisCache) (float64, bool) {
	var ctx = context.Background()
	keyCache := fmt.Sprintf("%f-%f-%f-%f", oldLat, oldLng, newLat, newLng)
	if redisCache != nil {
		if distance, err := redisCache.Get(ctx, keyCache).Float64(); err == nil && distance > 0 {
			return distance, true
		}
	}
	if googleMapApiKey == "" {
		msg := fmt.Sprintf("Can not get distance from %f,%f to %f,%f - error: %s", oldLat, oldLng, newLat, newLng, "googleMapApiKey is empty")
		globalLib.PostToSlack(slackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[isoCode], globalConstant.SLACK_USER_NAME, msg)
		return 0, false
	}
	urlDistance := fmt.Sprintf("https://maps.googleapis.com/maps/api/distancematrix/json?origins=%f,%f&destinations=%f,%f&travelMode=DRIVING&key=%s", oldLat, oldLng, newLat, newLng, googleMapApiKey)
	req, err := http.NewRequest("GET", urlDistance, nil)
	req.Header.Set("Content-Type", "application/json")
	if err != nil {
		msg := fmt.Sprintf("Can not get distance from %f,%f to %f,%f - error: %s", oldLat, oldLng, newLat, newLng, err.Error())
		globalLib.PostToSlack(slackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[isoCode], globalConstant.SLACK_USER_NAME, msg)
	} else {
		client := &http.Client{}
		resp, err := client.Do(req)
		if err != nil {
			msg := fmt.Sprintf("Can not get distance from %f,%f to %f,%f - error: %s", oldLat, oldLng, newLat, newLng, err.Error())
			globalLib.PostToSlack(slackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[isoCode], globalConstant.SLACK_USER_NAME, msg)
			return 0, false
		} else {
			defer resp.Body.Close()
			byteData, _ := io.ReadAll(resp.Body)
			var distanceData DistanceData
			json.Unmarshal(byteData, &distanceData)
			if distanceData.Status == "OK" {
				distance := distanceData.Rows[0].Elements[0].Distance.Value / 1000
				if redisCache != nil {
					redisCache.Set(ctx, keyCache, distance, (1 * time.Hour)) // set timeout = 1h
				}
				return distance, true
			} else {
				msg := fmt.Sprintf("Can not get distance from %f,%f to %f,%f - status: %s - error: %s", oldLat, oldLng, newLat, newLng, distanceData.Status, distanceData.ErrorMessage)
				globalLib.PostToSlack(slackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[isoCode], globalConstant.SLACK_USER_NAME, msg)
				return 0, false
			}
		}
	}
	return 0, true
}

func GetDistancePriceHomeMoving(distance float64, detailCity *modelService.ServiceDetailServiceHomeMovingCity, vehicleType string, vehicleQuantity float64) float64 {
	if distance <= 0 {
		return 0
	}
	extraKms := detailCity.ExtraKm
	// sort extra kms giam dan theo from
	sort.Slice(extraKms, func(i, j int) bool {
		return extraKms[i].From > extraKms[j].From
	})
	var distancePrice float64
	// check extra kms
	for _, extraKm := range extraKms {
		// check from km - nếu số km lớn hơn mức đang check thì tính giá tiền vận chuyển với số km trong mức: vd 120km => 20km from 101-... | 50km from 51-100 | 40km from 11-50
		if distance >= extraKm.From && len(extraKm.Prices) > 0 {
			newDistance := distance - extraKm.From + 1
			for _, price := range extraKm.Prices {
				if price.VehicleType == vehicleType && price.Price > 0 {
					distancePrice += price.Price * vehicleQuantity * newDistance
				}
			}
			// Tính số km còn lại sau khi đã tính mức
			distance = distance - newDistance
		}
	}
	return distancePrice
}
