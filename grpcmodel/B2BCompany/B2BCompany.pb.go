// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.7.1
// source: grpcmodel/B2BCompany/B2BCompany.proto

package B2BCompany

import (
	timestamp "github.com/golang/protobuf/ptypes/timestamp"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type B2BCompany struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// @inject_tag: bson:"_id,omitempty"
	XId string `protobuf:"bytes,1,opt,name=_id,json=Id,proto3" json:"_id,omitempty" bson:"_id,omitempty"`
	// @inject_tag: bson:"name,omitempty"
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty" bson:"name,omitempty"`
	// @inject_tag: bson:"phone,omitempty"
	Phone string `protobuf:"bytes,3,opt,name=phone,proto3" json:"phone,omitempty" bson:"phone,omitempty"`
	// @inject_tag: bson:"listEmployee,omitempty"
	ListEmployee []string `protobuf:"bytes,4,rep,name=listEmployee,proto3" json:"listEmployee,omitempty" bson:"listEmployee,omitempty"`
	// @inject_tag: bson:"monthlySubsidies,omitempty"
	MonthlySubsidies float64 `protobuf:"fixed64,5,opt,name=monthlySubsidies,proto3" json:"monthlySubsidies,omitempty" bson:"monthlySubsidies,omitempty"`
	// @inject_tag: bson:"status,omitempty"
	Status string `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty" bson:"status,omitempty"`
	// @inject_tag: bson:"balance,omitempty"
	Balance float64 `protobuf:"fixed64,7,opt,name=balance,proto3" json:"balance,omitempty" bson:"balance,omitempty"`
	// @inject_tag: bson:"createdAt,omitempty"
	CreatedAt *timestamp.Timestamp `protobuf:"bytes,8,opt,name=createdAt,proto3" json:"createdAt,omitempty" bson:"createdAt,omitempty"`
}

func (x *B2BCompany) Reset() {
	*x = B2BCompany{}
	if protoimpl.UnsafeEnabled {
		mi := &file_grpcmodel_B2BCompany_B2BCompany_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *B2BCompany) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*B2BCompany) ProtoMessage() {}

func (x *B2BCompany) ProtoReflect() protoreflect.Message {
	mi := &file_grpcmodel_B2BCompany_B2BCompany_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use B2BCompany.ProtoReflect.Descriptor instead.
func (*B2BCompany) Descriptor() ([]byte, []int) {
	return file_grpcmodel_B2BCompany_B2BCompany_proto_rawDescGZIP(), []int{0}
}

func (x *B2BCompany) GetXId() string {
	if x != nil {
		return x.XId
	}
	return ""
}

func (x *B2BCompany) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *B2BCompany) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *B2BCompany) GetListEmployee() []string {
	if x != nil {
		return x.ListEmployee
	}
	return nil
}

func (x *B2BCompany) GetMonthlySubsidies() float64 {
	if x != nil {
		return x.MonthlySubsidies
	}
	return 0
}

func (x *B2BCompany) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *B2BCompany) GetBalance() float64 {
	if x != nil {
		return x.Balance
	}
	return 0
}

func (x *B2BCompany) GetCreatedAt() *timestamp.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

var File_grpcmodel_B2BCompany_B2BCompany_proto protoreflect.FileDescriptor

var file_grpcmodel_B2BCompany_B2BCompany_proto_rawDesc = []byte{
	0x0a, 0x25, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x42, 0x32, 0x42, 0x43,
	0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x2f, 0x42, 0x32, 0x42, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0a, 0x42, 0x32, 0x42, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x22, 0x83, 0x02, 0x0a, 0x0a, 0x42, 0x32, 0x42, 0x43, 0x6f, 0x6d, 0x70,
	0x61, 0x6e, 0x79, 0x12, 0x0f, 0x0a, 0x03, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x22,
	0x0a, 0x0c, 0x6c, 0x69, 0x73, 0x74, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79, 0x65, 0x65, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x6c, 0x69, 0x73, 0x74, 0x45, 0x6d, 0x70, 0x6c, 0x6f, 0x79,
	0x65, 0x65, 0x12, 0x2a, 0x0a, 0x10, 0x6d, 0x6f, 0x6e, 0x74, 0x68, 0x6c, 0x79, 0x53, 0x75, 0x62,
	0x73, 0x69, 0x64, 0x69, 0x65, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x01, 0x52, 0x10, 0x6d, 0x6f,
	0x6e, 0x74, 0x68, 0x6c, 0x79, 0x53, 0x75, 0x62, 0x73, 0x69, 0x64, 0x69, 0x65, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63,
	0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x01, 0x52, 0x07, 0x62, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x38, 0x0a, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x3e, 0x5a, 0x3c, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65,
	0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64,
	0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f,
	0x42, 0x32, 0x42, 0x43, 0x6f, 0x6d, 0x70, 0x61, 0x6e, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x33,
}

var (
	file_grpcmodel_B2BCompany_B2BCompany_proto_rawDescOnce sync.Once
	file_grpcmodel_B2BCompany_B2BCompany_proto_rawDescData = file_grpcmodel_B2BCompany_B2BCompany_proto_rawDesc
)

func file_grpcmodel_B2BCompany_B2BCompany_proto_rawDescGZIP() []byte {
	file_grpcmodel_B2BCompany_B2BCompany_proto_rawDescOnce.Do(func() {
		file_grpcmodel_B2BCompany_B2BCompany_proto_rawDescData = protoimpl.X.CompressGZIP(file_grpcmodel_B2BCompany_B2BCompany_proto_rawDescData)
	})
	return file_grpcmodel_B2BCompany_B2BCompany_proto_rawDescData
}

var file_grpcmodel_B2BCompany_B2BCompany_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_grpcmodel_B2BCompany_B2BCompany_proto_goTypes = []interface{}{
	(*B2BCompany)(nil),          // 0: B2BCompany.B2BCompany
	(*timestamp.Timestamp)(nil), // 1: google.protobuf.Timestamp
}
var file_grpcmodel_B2BCompany_B2BCompany_proto_depIdxs = []int32{
	1, // 0: B2BCompany.B2BCompany.createdAt:type_name -> google.protobuf.Timestamp
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_grpcmodel_B2BCompany_B2BCompany_proto_init() }
func file_grpcmodel_B2BCompany_B2BCompany_proto_init() {
	if File_grpcmodel_B2BCompany_B2BCompany_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_grpcmodel_B2BCompany_B2BCompany_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*B2BCompany); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_grpcmodel_B2BCompany_B2BCompany_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_grpcmodel_B2BCompany_B2BCompany_proto_goTypes,
		DependencyIndexes: file_grpcmodel_B2BCompany_B2BCompany_proto_depIdxs,
		MessageInfos:      file_grpcmodel_B2BCompany_B2BCompany_proto_msgTypes,
	}.Build()
	File_grpcmodel_B2BCompany_B2BCompany_proto = out.File
	file_grpcmodel_B2BCompany_B2BCompany_proto_rawDesc = nil
	file_grpcmodel_B2BCompany_B2BCompany_proto_goTypes = nil
	file_grpcmodel_B2BCompany_B2BCompany_proto_depIdxs = nil
}
