package grpcBackendMY

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcBackendMY"
	"google.golang.org/grpc"
)

func ConnectGRPCBackend(endpoint string) (grpcBackendMY.BackendClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcBackendMY.NewBackendClient(connect)
	return client, connect, err
}
