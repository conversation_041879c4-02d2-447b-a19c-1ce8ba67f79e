package subscription

import (
	"context"
	"fmt"
	"log"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPushNotificationVN"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
)

// SubscriptionData represents the subscription data structure
type SubscriptionData struct {
	ID                  string                 `bson:"_id" json:"_id"`
	TaskServiceID       string                 `bson:"taskServiceId,omitempty" json:"taskServiceId,omitempty"`
	ServiceID           string                 `bson:"serviceId" json:"serviceId"`
	Duration            int                    `bson:"duration" json:"duration"`
	Address             string                 `bson:"address" json:"address"`
	Location            Location               `bson:"location" json:"location"`
	UserID              string                 `bson:"userId" json:"userId"`
	TaskPlace           string                 `bson:"taskPlace,omitempty" json:"taskPlace,omitempty"`
	Description         string                 `bson:"description,omitempty" json:"description,omitempty"`
	CountryCode         string                 `bson:"countryCode,omitempty" json:"countryCode,omitempty"`
	ServiceName         string                 `bson:"serviceName,omitempty" json:"serviceName,omitempty"`
	TaskNote            string                 `bson:"taskNote,omitempty" json:"taskNote,omitempty"`
	CookingDetail       map[string]interface{} `bson:"cookingDetail,omitempty" json:"cookingDetail,omitempty"`
	DetailChildCare     map[string]interface{} `bson:"detailChildCare,omitempty" json:"detailChildCare,omitempty"`
	HomeType            string                 `bson:"homeType,omitempty" json:"homeType,omitempty"`
	IsPremium           bool                   `bson:"isPremium,omitempty" json:"isPremium,omitempty"`
	IsEco               bool                   `bson:"isEco,omitempty" json:"isEco,omitempty"`
	Addons              []string               `bson:"addons,omitempty" json:"addons,omitempty"`
	Pet                 []string               `bson:"pet,omitempty" json:"pet,omitempty"`
	ServiceText         string                 `bson:"serviceText,omitempty" json:"serviceText,omitempty"`
	ForceAcceptTaskerID string                 `bson:"forceAcceptTaskerId,omitempty" json:"forceAcceptTaskerId,omitempty"`
	Price               float64                `bson:"price" json:"price"`
	Discount            float64                `bson:"discount" json:"discount"`
	Schedule            []time.Time            `bson:"schedule" json:"schedule"`
	History             []time.Time            `bson:"history,omitempty" json:"history,omitempty"`
	TaskIDs             []string               `bson:"taskIds,omitempty" json:"taskIds,omitempty"`
	Status              string                 `bson:"status" json:"status"`
}

type Location struct {
	Lat float64 `bson:"lat" json:"lat"`
	Lng float64 `bson:"lng" json:"lng"`
}

// postSubscriptionTask creates and posts a new task from subscription
func postSubscriptionTask(subs *SubscriptionData, nextDate time.Time) float64 {
	createdAt := globalLib.GetCurrentTime(local.TimeZone)

	// Create basic task data as map for insertion
	taskData := bson.M{
		"_id":              globalLib.GenerateObjectId(),
		"serviceId":        getServiceID(subs),
		"date":             nextDate,
		"duration":         float64(subs.Duration),
		"address":          subs.Address,
		"lat":              subs.Location.Lat,
		"lng":              subs.Location.Lng,
		"askerId":          subs.UserID,
		"status":           globalConstant.TASK_STATUS_POSTED,
		"createdAt":        createdAt,
		"rated":            false,
		"subscriptionId":   subs.ID,
		"autoChooseTasker": true,
		"payment": bson.M{
			"method": globalConstant.PAYMENT_METHOD_BANK_TRANSFER,
		},
		"description": subs.Description,
		"isoCode":     "VN",
		"countryCode": getCountryCode(subs),
		"originCurrency": bson.M{
			"sign": "₫",
			"code": "VND",
		},
	}

	// Set service name if available
	if subs.ServiceName != "" {
		taskData["serviceName"] = mapServiceName(subs.ServiceName)
	}

	// Set task note if available
	if subs.TaskNote != "" {
		taskData["taskNote"] = subs.TaskNote
	}

	// Set cooking detail if available
	if subs.CookingDetail != nil {
		cookingDetail := subs.CookingDetail
		// Set eating time
		eatingTime := nextDate.Add(time.Duration(subs.Duration) * time.Hour)
		if cookingDetail == nil {
			cookingDetail = make(map[string]interface{})
		}
		cookingDetail["eatingTime"] = eatingTime
		taskData["cookingDetail"] = cookingDetail
	}

	// Set child care detail if available
	if subs.DetailChildCare != nil {
		taskData["detailChildCare"] = subs.DetailChildCare
	}

	// Calculate price
	priceOfTask := priceOfTaskInSubscription(&Subscription{
		Price:    subs.Price,
		Duration: subs.Duration,
		Discount: subs.Discount,
		Schedule: subs.Schedule,
	}, nextDate)

	taskData["cost"] = priceOfTask.CostDetail.Cost
	taskPrice := priceOfTask.CostDetail.Cost

	if priceOfTask.CostDetail.FinalCost > 0 {
		taskPrice = priceOfTask.CostDetail.FinalCost
	}

	// Get user information
	user, err := getUserInfo(subs.UserID)
	if err != nil {
		log.Printf("Error getting user info: %v", err)
		return 0
	}

	// Set user-related fields
	setUserRelatedFields(taskData, user, subs)

	// Fill task description if empty
	if taskData["description"] == "" {
		description := fillTaskDescription(user.Phone)
		if description != "" {
			taskData["description"] = description
		}
	}

	// Set additional fields
	setAdditionalFields(taskData, subs)

	// Get service text if not available
	if subs.ServiceText == "" {
		serviceText := getServiceText(subs.ServiceID)
		if serviceText != "" {
			taskData["serviceText"] = serviceText
		}
	} else {
		taskData["serviceText"] = subs.ServiceText
	}

	// Insert task
	taskID, err := insertTask(taskData)
	if err != nil {
		log.Printf("Error inserting task: %v", err)
		return 0
	}

	// Update user's last posted task
	updateUserLastPostedTask(subs.UserID, createdAt)

	// Update subscription history
	updateSubscriptionHistory(subs.ID, taskID, nextDate)

	// Send notification to asker
	serviceTextStr := ""
	if serviceText, ok := taskData["serviceText"].(string); ok {
		serviceTextStr = serviceText
	}
	sendNotificationToAsker(taskID, subs.UserID, user.Language, serviceTextStr)

	// Handle force accept tasker or send to available taskers
	handleTaskerAssignment(subs, taskData, taskID, user)

	// Check if subscription is finished
	checkSubscriptionCompletion(subs, taskID)

	return taskPrice
}

// Helper functions

func getServiceID(subs *SubscriptionData) string {
	if subs.TaskServiceID != "" {
		return subs.TaskServiceID
	}
	return subs.ServiceID
}

func getCountryCode(subs *SubscriptionData) string {
	if subs.CountryCode != "" {
		return subs.CountryCode
	}
	return "+84"
}

func getUserInfo(userID string) (*users.Users, error) {
	var user users.Users
	query := bson.M{"_id": userID}
	fields := bson.M{
		"phone":           1,
		"name":            1,
		"taskNote":        1,
		"blackList":       1,
		"language":        1,
		"favouriteTasker": 1,
		"isBlacklist":     1,
	}

	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USERS, query, fields, &user)
	return &user, err
}

func setUserRelatedFields(taskData bson.M, user *users.Users, subs *SubscriptionData) {
	if user.BlackList != nil && len(user.BlackList) > 0 {
		taskData["blackList"] = user.BlackList
	}
	taskData["phone"] = user.Phone
	taskData["contactName"] = user.Name
	if user.TaskNote != "" {
		taskData["taskNote"] = user.TaskNote
	}
}

func setAdditionalFields(taskData bson.M, subs *SubscriptionData) {
	if subs.HomeType != "" {
		taskData["homeType"] = subs.HomeType
	}
	if subs.IsPremium {
		taskData["isPremium"] = subs.IsPremium
	}
	if subs.IsEco {
		taskData["isEco"] = subs.IsEco
	}
	if subs.Addons != nil && len(subs.Addons) > 0 {
		taskData["addons"] = subs.Addons
	}
	if subs.Pet != nil && len(subs.Pet) > 0 {
		taskData["pet"] = subs.Pet
	}
}

func getServiceText(serviceID string) string {
	var service bson.M
	query := bson.M{"_id": serviceID}
	fields := bson.M{"text": 1}

	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], query, fields, &service)
	if err != nil {
		return ""
	}
	if text, ok := service["text"].(string); ok {
		return text
	}
	return ""
}

func insertTask(taskData bson.M) (string, error) {
	taskID := taskData["_id"].(string)
	err := globalDataAccess.InsertOne(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskData)
	return taskID, err
}

func updateUserLastPostedTask(userID string, createdAt time.Time) {
	query := bson.M{"_id": userID}
	update := bson.M{"$set": bson.M{"lastPostedTask": createdAt}}
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_USERS, query, update)
}

func updateSubscriptionHistory(subsID, taskID string, nextDate time.Time) {
	query := bson.M{"_id": subsID}
	update := bson.M{
		"$push": bson.M{
			"taskIds": taskID,
			"history": nextDate,
		},
	}
	globalDataAccess.UpdateOneByQuery(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], query, update)
}

func handleTaskerAssignment(subs *SubscriptionData, taskData bson.M, taskID string, user *users.Users) {
	if subs.ForceAcceptTaskerID != "" {
		// Handle force accept tasker logic
		handleForceAcceptTasker(subs, taskData, taskID, user)
	} else {
		// Send task to available taskers
		if !user.IsBlacklist {
			sendTaskToTasker(taskID, subs.ServiceID, user.FavouriteTasker)
		}
	}
}

func handleForceAcceptTasker(subs *SubscriptionData, taskData bson.M, taskID string, user *users.Users) {
	// Get tasker information
	var tasker users.Users
	query := bson.M{
		"_id":    subs.ForceAcceptTaskerID,
		"status": bson.M{"$in": []string{"ACTIVE", "LOCKED"}},
	}
	fields := bson.M{
		"avatar":            1,
		"name":              1,
		"avgRating":         1,
		"taskDone":          1,
		"phone":             1,
		"status":            1,
		"notComeLockNumber": 1,
	}

	err := globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USERS, query, fields, &tasker)
	if err != nil || !canTaskerAccept(&tasker) {
		// Post to Slack about tasker not available
		message := fmt.Sprintf("Cần CS xử lý: Công việc của Subscription đã đăng và KHÔNG confirm được do Tasker %s đã bị khóa. CV của Asker: %s", tasker.Phone, user.Phone)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
		return
	}

	// Check for conflicts with other tasks
	if hasTaskConflict(subs.ForceAcceptTaskerID, taskData) {
		message := fmt.Sprintf("Cần CS xử lý: Công việc của Subscription đã đăng và KHÔNG confirm được do Tasker %s - %s trùng giờ làm việc. CV của Asker: %s", tasker.Phone, tasker.Name, user.Phone)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
		return
	}

	// Force accept the task
	acceptedTasker := bson.M{
		"taskerId":   subs.ForceAcceptTaskerID,
		"avatar":     tasker.Avatar,
		"name":       tasker.Name,
		"avgRating":  tasker.AvgRating,
		"taskDone":   tasker.TaskDone,
		"acceptedAt": globalLib.GetCurrentTime(local.TimeZone),
	}

	update := bson.M{
		"$set": bson.M{
			"acceptedTasker": []bson.M{acceptedTasker},
			"status":         globalConstant.TASK_STATUS_CONFIRMED,
			"updatedAt":      globalLib.GetCurrentTime(local.TimeZone),
		},
	}

	globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskID, update)

	// Send notification to tasker
	sendConfirmationNotification(subs.ForceAcceptTaskerID, taskID)

	// Alert if tasker status is not ACTIVE
	if tasker.Status != "ACTIVE" {
		message := fmt.Sprintf("CS lưu ý: Tasker %s status %s đã được thêm vào subscription task (Asker: %s)", tasker.Phone, tasker.Status, user.Phone)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
	}
}

func hasTaskConflict(taskerID string, taskData bson.M) bool {
	// Check for conflicting tasks - simplified version
	var confirmedTasks []bson.M
	query := bson.M{
		"status":                  globalConstant.TASK_STATUS_CONFIRMED,
		"acceptedTasker.taskerId": taskerID,
	}

	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], query, bson.M{}, &confirmedTasks)
	if err != nil {
		return false
	}

	// Simple conflict check - in real implementation, you'd check time overlaps
	return len(confirmedTasks) > 0
}

func sendConfirmationNotification(taskerID, taskID string) {
	// Send confirmation notification to tasker via gRPC
	go func() {
		// Connect to push notification service
		client, conn, err := grpcPushNotificationVN.ConnectGRPCPushNotificationVN(cfg.GRPC_Push_Notification_URL)
		if err != nil {
			log.Printf("Error connecting to push notification service: %v", err)
			return
		}
		defer conn.Close()

		// Get tasker language (default to Vietnamese)
		var tasker users.Users
		query := bson.M{"_id": taskerID}
		fields := bson.M{"language": 1}
		err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_USERS, query, fields, &tasker)

		lang := globalConstant.LANG_VI
		if err == nil && tasker.Language != "" {
			lang = tasker.Language
		}

		// Create notification data
		title := localization.GetLocalizeObject("NOTIFICATION_MESSAGE_TASKER_CONFIRMED_TITLE", "")
		text := localization.GetLocalizeObject("NOTIFICATION_MESSAGE_TASKER_CONFIRMED", "")

		// Create user IDs for notification
		userIds := []*pushNotificationRequest.PushNotificationRequestUserIds{
			{
				UserId:   taskerID,
				Language: lang,
			},
		}

		// Create title and body with localization
		titleText := &service.ServiceText{
			Vi: globalLib.LocalizeServiceName(lang, title),
		}
		bodyText := &service.ServiceText{
			Vi: globalLib.LocalizeServiceName(lang, text),
		}

		// Create payload
		payload := &pushNotificationRequest.PushNotificationRequestPayload{
			Type:       1, // Task confirmation notification type
			TaskId:     taskID,
			NavigateTo: "TaskDetail",
		}

		// Create push notification request
		request := &pushNotificationRequest.PushNotificationRequest{
			UserIds: userIds,
			Title:   titleText,
			Body:    bodyText,
			Sound:   "default",
			Payload: payload,
		}

		// Send notification via gRPC
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		response, err := client.Send(ctx, request)
		if err != nil {
			log.Printf("Error sending confirmation notification to tasker %s: %v", taskerID, err)
			return
		}

		if response.StatusCode == 200 {
			log.Printf("Successfully sent confirmation notification to tasker %s for task %s", taskerID, taskID)
		} else {
			log.Printf("Failed to send confirmation notification to tasker %s: %s (status: %d)", taskerID, response.Message, response.StatusCode)
		}
	}()
}

func checkSubscriptionCompletion(subs *SubscriptionData, taskID string) {
	// Use existing subscription data instead of querying DB again
	if len(subs.Schedule) == 0 {
		return
	}

	// Calculate current progress
	// After adding this task to history, check if all tasks are completed
	totalTasks := len(subs.Schedule)
	completedTasks := len(subs.History) + 1 // +1 for the task we just added

	// Check if this is the last task in the subscription
	if completedTasks >= totalTasks {
		// Mark subscription as DONE
		update := bson.M{"$set": bson.M{
			"status":    "DONE",
			"updatedAt": globalLib.GetCurrentTime(local.TimeZone),
		}}
		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], subs.ID, update)

		// Mark task as final subscription task
		taskUpdate := bson.M{"$set": bson.M{
			"finalSubscriptionTask": true,
			"updatedAt":             globalLib.GetCurrentTime(local.TimeZone),
		}}
		globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskID, taskUpdate)

		log.Printf("Subscription %s completed. Final task: %s (completed %d/%d tasks)",
			subs.ID, taskID, completedTasks, totalTasks)
	} else {
		log.Printf("Subscription %s progress: %d/%d tasks completed",
			subs.ID, completedTasks, totalTasks)
	}
}

// Additional helper functions for subscription processing
