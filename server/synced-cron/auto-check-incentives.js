/*
  @Description: Synced cron auto check code_quantity, inactive gift
  @Author: AnhPD
*/

import { Meteor } from 'meteor/meteor';
import _ from 'lodash';
import {HTTP} from 'meteor/http';
import {
  trackingScheduleStatus,
  postToSlack,
  revertTimeZone
} from '/server/helper-function';
import { Setting } from '/server/lib/collections';

const CODE_QUANTITY_ALERT = 10;
const CODE_QUANTITY_WILL_INACTIVE = 2;

const getGiftFromIncentive = (giftIds) => {
  return Meteor.appCollection.Incentive.find({
    from: 'SYSTEM_WITH_PARTNER',
    partner: 'UR_BOX',
    status: 'ACTIVE',
    'giftInfo.id': { $in: giftIds },
  }, {
    fields: {
      status: 1,
      giftInfo: 1,
    },
  }).map((e) => { return e.giftInfo.id });
}

const getListGiftsFromURBOX = () => {
  const { app_id, app_secret, urlGetListGift } = Meteor.settings.URBOX;
  const urlUrBox = urlGetListGift;
  const query = `app_id=${app_id}&app_secret=${app_secret}&per_page=200`;
  const response = HTTP.get(urlUrBox, {timeout: 10000, query: query});
  const result = JSON.parse(response.content);
  return _.get(result, 'data.items', []) || [];
}

const getGiftsNeedUpdateQuantity = (gifts) => {
  return gifts.filter((e) => { return e.code_quantity <= CODE_QUANTITY_ALERT});
}

const getGiftsNeedUpdateStatus = (gifts) => {
  return gifts.filter((e) => { return e.code_quantity <= CODE_QUANTITY_WILL_INACTIVE});
}

const updateGifts = (giftIds) => {
  // update gifts
  if (giftIds.length > 0) {
    const objUpdate = {
      isSkipAutoInactive: null,
      status: 'INACTIVE',
      updatedAt: new Date(),
      updatedBy: 'SYNCEDRON'
    };
    Meteor.appCollection.Incentive.update({
      'giftInfo.id': { $in: giftIds },
      status: 'ACTIVE',
      from: 'SYSTEM_WITH_PARTNER',
      partner: 'UR_BOX',
    }, {
      $set: objUpdate
    }, {
      "multi" : true,
    })
  }
}

const autoCheckIncentive = () => {
  return;
	console.log("Start autoCheckIncentive ...");
  let message = null;
  try {
    const listGiftIdWillInactive = [];
    const listGiftsFromURBOX = getListGiftsFromURBOX();

    // get items have code_quantity <= CODE_QUANTITY_ALERT;
    const newItemsNeedUpdateQuantity = getGiftsNeedUpdateQuantity(listGiftsFromURBOX);

    const itemsNeedUpdateStatus = getGiftsNeedUpdateStatus(listGiftsFromURBOX);

    const giftIds = itemsNeedUpdateStatus.map((e) => { return e.id });

    // get list gift from incentive <= CODE_QUANTITY_WILL_INACTIVE, will inactive 
    const itemsFromIncentiveCollectionActive = getGiftFromIncentive(giftIds);

    // get gift need update status
    itemsFromIncentiveCollectionActive.forEach(giftId => {
      if (giftIds.indexOf(giftId) !== -1) {
        listGiftIdWillInactive.push(giftId);
      }
    });
    
    // update status gifts
    updateGifts(listGiftIdWillInactive);

    // send to slack
    if (newItemsNeedUpdateQuantity.length > 0 || listGiftIdWillInactive.length > 0) {
      // get title GiftNeedUpdateQuantity
      const textGiftNeedUpdateQuantity = newItemsNeedUpdateQuantity.map((e, index) => { return `${index + 1}. ${e.title}. Số lượng: ${e.code_quantity}` }).join('\n');
      let textGiftInactive = '';
      const tempTitleGiftInactive = [];
      // get title GiftInactive
      listGiftIdWillInactive.forEach(giftId => {
        const tempGift = itemsNeedUpdateStatus.find((e) => { return e.id === giftId });
        if (tempGift) {
          tempTitleGiftInactive.push(tempGift.title);
        }
      });
      textGiftInactive = tempTitleGiftInactive.map((e, index) => { return `${index + 1}. ${e}` }).join('\n');
      if (textGiftInactive) {
        textGiftInactive = `\nDanh sách ưu đãi INACTIVE:\n
        ${textGiftInactive}
        `
      }
      postToSlack({
        channel: 'cron-jobs-alert',
        text: `Danh sách ưu đãi cần bổ sung số lượng từ URBOX:\n
        ${textGiftNeedUpdateQuantity}
        ${textGiftInactive}
        `,
      });
    }
  } catch (error) {
    message = error;
    // Send to slack if synced cron has error
    postToSlack({
      channel: 'cron-jobs-alert',
      text: `There was a problem with the SCHEDULE autoCheckIncentive. Run at: ${new Date()}. Error: ${JSON.parse(error)}.`,
    });
  }
	console.log("Finished autoCheckIncentive ");
  // Tracking
  trackingScheduleStatus({name: 'autoCheckIncentive', nextRunAt: SyncedCron.nextScheduledAtDate('autoCheckIncentive'), message});
}

  Meteor.methods({
    autoCheckIncentive() {
      SyncedCron.add({
        name: 'autoCheckIncentive',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 7)).getHours();
          // Run at 7 AM, 7PM every setting days
          return parser.cron(`0 0 ${hourInTimeZoneServer}/12 1/1 * ? *`, true);
        },
        job() {
          autoCheckIncentive();
        },
      });
	    console.log("Added autoCheckIncentive cron");
    },
    'runNow.autoCheckIncentive'() {
      autoCheckIncentive();
    },
  });
