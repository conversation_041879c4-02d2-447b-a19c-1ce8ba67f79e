syntax = "proto3";
package grpcSendTaskTH;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcSendTaskTH";

import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask/pushNotificationNewTask.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response/response.proto";

service PushNotificationNewTaskTH {
  rpc NewTask (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-th/new-task"
      body: "*"
    };
  }
  rpc TopTasker (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-th/top-tasker"
      body: "*"
    };
  }
  rpc NewTaskToDistrict (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-th/district"
      body: "*"
    };
  }
  rpc FavTasker (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-th/favorite-tasker"
      body: "*"
    };
  }
  rpc Normal (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-th/normal"
      body: "*"
    };
  }
  rpc FavAndTaskerDistrict (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-th/favorite-and-tasker-district"
      body: "*"
    };
  }
  rpc FavAndTopTasker (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-th/favorite-and-top-tasker"
      body: "*"
    };
  }
  rpc NewTaskToCity (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-th/city"
      body: "*"
    };
  }
}