package th

import (
	"context"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcDataAccess"
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccess"
	modelDataAccess "gitlab.com/btaskee/go-services-model-v2/grpcmodel/dataAccess"
	"go.mongodb.org/mongo-driver/bson"
)

func UpsertOneById(collectionName, id string, data interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	b, err := globalDataAccess.EncodeData(data)
	if err != nil {
		return err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Id: id, Data: b}
	_, err = client.UpsertOneById(context.Background(), request)
	return err
}

func UpsertOneByQuery(collectionName string, query, data interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_TH[globalDataAccess.MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return err
	}
	byteData, err := globalDataAccess.EncodeData(data)
	if err != nil {
		return err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Data: byteData}
	_, err = client.UpsertOneByQuery(context.Background(), request)
	return err
}
