// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcSMSIndo

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	smsSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/smsSending"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// SMSClient is the client API for SMS service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SMSClient interface {
	Send(ctx context.Context, in *smsSending.SmsRequest, opts ...grpc.CallOption) (*empty.Empty, error)
	SendViaTwillo(ctx context.Context, in *smsSending.SmsRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type sMSClient struct {
	cc grpc.ClientConnInterface
}

func NewSMSClient(cc grpc.ClientConnInterface) SMSClient {
	return &sMSClient{cc}
}

func (c *sMSClient) Send(ctx context.Context, in *smsSending.SmsRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcSMSIndo.SMS/Send", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sMSClient) SendViaTwillo(ctx context.Context, in *smsSending.SmsRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcSMSIndo.SMS/SendViaTwillo", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SMSServer is the server API for SMS service.
// All implementations must embed UnimplementedSMSServer
// for forward compatibility
type SMSServer interface {
	Send(context.Context, *smsSending.SmsRequest) (*empty.Empty, error)
	SendViaTwillo(context.Context, *smsSending.SmsRequest) (*empty.Empty, error)
	mustEmbedUnimplementedSMSServer()
}

// UnimplementedSMSServer must be embedded to have forward compatible implementations.
type UnimplementedSMSServer struct {
}

func (UnimplementedSMSServer) Send(context.Context, *smsSending.SmsRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Send not implemented")
}
func (UnimplementedSMSServer) SendViaTwillo(context.Context, *smsSending.SmsRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendViaTwillo not implemented")
}
func (UnimplementedSMSServer) mustEmbedUnimplementedSMSServer() {}

// UnsafeSMSServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SMSServer will
// result in compilation errors.
type UnsafeSMSServer interface {
	mustEmbedUnimplementedSMSServer()
}

func RegisterSMSServer(s *grpc.Server, srv SMSServer) {
	s.RegisterService(&_SMS_serviceDesc, srv)
}

func _SMS_Send_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(smsSending.SmsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SMSServer).Send(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSMSIndo.SMS/Send",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SMSServer).Send(ctx, req.(*smsSending.SmsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SMS_SendViaTwillo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(smsSending.SmsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SMSServer).SendViaTwillo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcSMSIndo.SMS/SendViaTwillo",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SMSServer).SendViaTwillo(ctx, req.(*smsSending.SmsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _SMS_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcSMSIndo.SMS",
	HandlerType: (*SMSServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Send",
			Handler:    _SMS_Send_Handler,
		},
		{
			MethodName: "SendViaTwillo",
			Handler:    _SMS_SendViaTwillo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "sms.proto",
}
