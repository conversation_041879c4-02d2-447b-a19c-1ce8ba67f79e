package subscription

import (
	"context"
	"log"
	"time"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPushNotificationVN"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"gitlab.com/btaskee/go-services-model-v2/localization"
)

// sendNotificationToAsker sends notification to asker when task is posted
func sendNotificationToAsker(taskID, userID, language, serviceText string) {
	lang := globalConstant.LANG_VI
	if language != "" {
		lang = language
	}

	// Create notification data
	title := localization.GetLocalizeObject("NOTIFICATION_MESSAGE_ASKER_POSTED_TITLE", serviceText)
	text := localization.GetLocalizeObject("NOTIFICATION_MESSAGE_ASKER_POSTED", serviceText)

	// Prepare gRPC request
	go func() {
		// Connect to push notification service
		client, conn, err := grpcPushNotificationVN.ConnectGRPCPushNotificationVN(cfg.GRPC_Push_Notification_URL)
		if err != nil {
			log.Printf("Error connecting to push notification service: %v", err)
			return
		}
		defer conn.Close()

		// Create user IDs for notification
		userIds := []*pushNotificationRequest.PushNotificationRequestUserIds{
			{
				UserId:   userID,
				Language: lang,
			},
		}

		// Create title and body with localization
		titleText := &service.ServiceText{
			Vi: globalLib.LocalizeServiceName(lang, title),
		}
		bodyText := &service.ServiceText{
			Vi: globalLib.LocalizeServiceName(lang, text),
		}

		// Create payload
		payload := &pushNotificationRequest.PushNotificationRequestPayload{
			Type:       2, // Task notification type
			TaskId:     taskID,
			NavigateTo: "TaskDetail",
		}

		// Create push notification request
		request := &pushNotificationRequest.PushNotificationRequest{
			UserIds: userIds,
			Title:   titleText,
			Body:    bodyText,
			Sound:   "default",
			Payload: payload,
		}

		// Send notification via gRPC
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		response, err := client.Send(ctx, request)
		if err != nil {
			log.Printf("Error sending notification to asker %s: %v", userID, err)
			return
		}

		if response.StatusCode == 200 {
			log.Printf("Successfully sent notification to asker %s for task %s", userID, taskID)
		} else {
			log.Printf("Failed to send notification to asker %s: %s (status: %d)", userID, response.Message, response.StatusCode)
		}
	}()
}
