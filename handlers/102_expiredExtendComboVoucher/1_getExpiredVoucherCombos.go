package extendExpiredComboVoucher

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucher"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/userComboVoucher"
	"go.mongodb.org/mongo-driver/bson"
)

func getExpiredVoucherCombos() (comboVoucherToExpire, comboVoucherToExtend []*userComboVoucher.UserComboVoucher, mapComboVouchers map[string]*modelComboVoucher.ComboVoucher) {
	comboVoucherToExpire = make([]*userComboVoucher.UserComboVoucher, 0)
	comboVoucherToExtend = make([]*userComboVoucher.UserComboVoucher, 0)

	now := globalLib.GetCurrentTime(local.TimeZone)
	query := bson.M{
		"expiredDate": bson.M{"$lt": now},
		"status":      globalConstant.USER_COMBO_VOUCHER_STATUS_ACTIVE,
	}
	var userComboVouchers []*userComboVoucher.UserComboVoucher
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_USER_COMBO_VOUCHER[local.ISO_CODE], query, bson.M{}, bson.M{"createdAt": 1}, &userComboVouchers)

	comboVoucherIds := []string{}
	for _, userComboVoucher := range userComboVouchers {
		comboVoucherIds = append(comboVoucherIds, userComboVoucher.ComboVoucherId)
	}

	var comboVouchers []*modelComboVoucher.ComboVoucher
	globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_COMBO_VOUCHER[local.ISO_CODE],
		bson.M{"_id": bson.M{"$in": comboVoucherIds}},
		bson.M{"price": 1}, &comboVouchers)
	mapComboVouchers = map[string]*modelComboVoucher.ComboVoucher{}
	for _, comboVoucher := range comboVouchers {
		mapComboVouchers[comboVoucher.XId] = comboVoucher
	}

	for _, userComboVoucher := range userComboVouchers {
		if !userComboVoucher.IsSubscription ||
			(userComboVoucher.IsSubscription && userComboVoucher.IsCancelled) {
			comboVoucherToExpire = append(comboVoucherToExpire, userComboVoucher)
		} else if userComboVoucher.IsSubscription && !userComboVoucher.IsCancelled {
			comboVoucherToExtend = append(comboVoucherToExtend, userComboVoucher)
		}
	}

	return
}
