package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"sort"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelFATransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/fatransaction"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/financialAccountVN"
	"go.mongodb.org/mongo-driver/bson"
)

func TestRollbackMissingTaskActionPenalty(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		ResetData()
		apiURL := apiSyncCron + "/rollback-missing-task-action-penalty"

		fAccountIds := CreateFinancialAccount([]map[string]interface{}{
			{
				"FMainAccount": 100000,
			},
			{
				"FMainAccount": 200000,
			},
			{
				"FMainAccount": 300000,
			},
		})

		CreateUser([]map[string]interface{}{
			{
				"phone":      "**********",
				"type":       globalConstant.USER_TYPE_TASKER,
				"name":       "Tasker 0",
				"fAccountId": fAccountIds[0],
			},
			{
				"phone":      "**********",
				"type":       globalConstant.USER_TYPE_TASKER,
				"name":       "Tasker 1",
				"fAccountId": fAccountIds[1],
			},
			{
				"phone":      "**********",
				"type":       globalConstant.USER_TYPE_TASKER,
				"name":       "Tasker 2",
				"fAccountId": fAccountIds[2],
			},
		})

		transactionIds := CreateFATransaction([]map[string]interface{}{
			{ // Rollback success
				"userId":      "**********",
				"accountType": "M",
				"type":        "C",
				"source": map[string]interface{}{
					"name":  "PENALTY_TASKER_MISSING_START_ACTION",
					"value": "taskId1",
					"reasonText": map[string]interface{}{
						"vi": "Phí phạt không bấm bắt đầu/kết thúc",
						"en": "Penalty for not pressing Start/End",
						"ko": "작업 시작/종료 버튼을 누르지 않은 경우 벌금",
						"th": "ค่าปรับกรณีไม่กดเริ่ม/สิ้นสุด",
						"id": "Denda karena tidak menekan Mulai/Selesai",
						"ms": "Denda kerana tidak menekan Mula/Tamat",
					},
				},
				"amount":  20000.0,
				"isoCode": "VN",
			},
			{ // Rollback success
				"userId":      "**********",
				"accountType": "M",
				"type":        "C",
				"source": map[string]interface{}{
					"name":  "PENALTY_TASKER_MISSING_DONE_ACTION",
					"value": "taskId2",
					"reasonText": map[string]interface{}{
						"vi": "Phí phạt không bấm bắt đầu/kết thúc",
						"en": "Penalty for not pressing Start/End",
						"ko": "작업 시작/종료 버튼을 누르지 않은 경우 벌금",
						"th": "ค่าปรับกรณีไม่กดเริ่ม/สิ้นสุด",
						"id": "Denda karena tidak menekan Mulai/Selesai",
						"ms": "Denda kerana tidak menekan Mula/Tamat",
					},
				},
				"amount":  20000.0,
				"isoCode": "VN",
			},
			{ // Rollback fail
				"userId":      "**********",
				"accountType": "M",
				"type":        "C",
				"source": map[string]interface{}{
					"value": "taskId2",
					"reasonText": map[string]interface{}{
						"vi": "Phí phạt không bấm bắt đầu/kết thúc",
						"en": "Penalty for not pressing Start/End",
						"ko": "작업 시작/종료 버튼을 누르지 않은 경우 벌금",
						"th": "ค่าปรับกรณีไม่กดเริ่ม/สิ้นสุด",
						"id": "Denda karena tidak menekan Mulai/Selesai",
						"ms": "Denda kerana tidak menekan Mula/Tamat",
					},
				},
				"amount":  20000.0,
				"isoCode": "VN",
			},
		})

		body := map[string]interface{}{
			"transactionIds": transactionIds,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					var result map[string]interface{}
					json.Unmarshal(res.Body.Bytes(), &result)
					So(len(cast.ToStringSlice(result["successIds"])), ShouldEqual, 2)

					// Check data in database
					var FATransactions []*modelFATransaction.FinancialAccountTransaction
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_FATRANSACTION[local.ISO_CODE], bson.M{"_id": bson.M{"$nin": transactionIds}}, bson.M{}, &FATransactions)
					So(len(FATransactions), ShouldEqual, 2)

					expectedSuccessResult := []string{}
					for _, transaction := range FATransactions {
						So(transaction.GetAmount(), ShouldEqual, 20000.0)
						So(transaction.GetAccountType(), ShouldEqual, "M")
						So(transaction.GetType(), ShouldEqual, "D")
						So(transaction.GetIsoCode(), ShouldEqual, "VN")
						So(transaction.GetSource().GetReasonText().GetVi(), ShouldEqual, "Hoàn phí Không bấm BĐ/KT")

						if transaction.GetUserId() == "**********" {
							So(transaction.GetSource().GetValue(), ShouldEqual, "taskId1")
							expectedSuccessResult = append(expectedSuccessResult, fmt.Sprintf("%s-%s", transactionIds[0], transaction.GetXId()))
						} else {
							So(transaction.GetUserId(), ShouldEqual, "**********")
							So(transaction.GetSource().GetValue(), ShouldEqual, "taskId2")
							expectedSuccessResult = append(expectedSuccessResult, fmt.Sprintf("%s-%s", transactionIds[1], transaction.GetXId()))
						}
					}

					// Check data in database
					var fAccounts []*financialAccountVN.FinancialAccountVN
					globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_FINANCIAL_ACCOUNT[local.ISO_CODE], bson.M{"_id": bson.M{"$in": fAccountIds}}, bson.M{}, &fAccounts)
					So(len(fAccounts), ShouldEqual, 3)
					for _, fAccount := range fAccounts {
						if fAccount.GetXId() == fAccountIds[0] {
							So(fAccount.GetFMainAccount(), ShouldEqual, 100000.0+20000.0)
						} else if fAccount.GetXId() == fAccountIds[1] {
							So(fAccount.GetFMainAccount(), ShouldEqual, 200000.0+20000.0)
						} else {
							So(fAccount.GetXId(), ShouldEqual, fAccountIds[2])
							So(fAccount.GetFMainAccount(), ShouldEqual, 300000.0)
						}
					}

					// Check response. Sort result and expected result before compare
					sort.Strings(cast.ToStringSlice(result["successIds"]))
					sort.Strings(expectedSuccessResult)
					So(cast.ToStringSlice(result["successIds"]), ShouldResemble, expectedSuccessResult)

					// Check response. Sort result and expected result before compare
					sort.Strings(cast.ToStringSlice(result["errorIds"]))

					expectedErrorResult := []string{fmt.Sprintf("%s-%v", transactionIds[2], "sourceName empty")}
					sort.Strings(expectedErrorResult)
					So(cast.ToStringSlice(result["errorIds"]), ShouldResemble, expectedErrorResult)
				})
			})
		})
	})
}
