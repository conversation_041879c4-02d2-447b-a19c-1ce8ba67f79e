package autoReportTasksInfo

import (
	"fmt"
	"log"

	"github.com/robfig/cron/v3"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

var (
	c            *cron.Cron
	cfg          = config.GetConfig()
	isRunning    = false
	paramsEnough = true
	runAt        string
)

func init() {
	var cronConfig map[string]interface{}
	if cfg != nil {
		cronConfig = cfg.ScheduleSyncCron["autoreporttasksinfo"]
	}

	// Check if params enough
	if cronConfig["run_at"] == nil {
		paramsEnough = false
		lib.PostToSlackNotStart("AutoReportTasksInfo")
		return
	}

	// Get runAt string for sync cron
	runAt = fmt.Sprintf(cronConfig["run_at"].(string))

	// Check if config has is_run = true
	if cronConfig["is_run"] == nil || !cronConfig["is_run"].(bool) {
		lib.PostToSlackNotStart("AutoReportTasksInfo")
		return
	}

	StartCron()
}

func StartCron() {
	c := cron.New(cron.WithLocation(local.TimeZone))
	c.AddFunc(runAt, AutoReportTasksInfo)
	c.Start()
	isRunning = true
}

func Run(reqBody *model.RequestAction) interface{} {
	// action == 1 -> Run sync cron once now
	if reqBody.Action == lib.RUN_NOW {
		return processAutoReportTasksInfo(reqBody)
	}
	// action == 2 -> Start sync cron with schedule config
	if reqBody.Action == lib.START && paramsEnough && !isRunning {
		StartCron()
	}
	// action == 3 -> Stop running sync cron
	if reqBody.Action == lib.STOP && isRunning {
		c.Stop()
		c = nil
		isRunning = false
	}
	return nil
}

func AutoReportTasksInfo() {
	log.Println("Start AutoReportTasksInfo Process")
	defer log.Println("Finish AutoReportTasksInfo Process")
	processAutoReportTasksInfo(nil)
}

func processAutoReportTasksInfo(reqBody *model.RequestAction) interface{} {
	// calculate fromDate and toDate
	now := globalLib.GetCurrentTime(local.TimeZone)
	dateToCheck := now.AddDate(0, 0, -1)
	fromDate := globalLib.StartADay(dateToCheck)
	toDate := globalLib.EndADay(dateToCheck)
	if reqBody != nil && reqBody.FromDate != nil {
		fromDate = *reqBody.FromDate
	}
	if reqBody != nil && reqBody.ToDate != nil {
		toDate = *reqBody.ToDate
	}

	// 1. get tasks
	tasks, err := getTasks(fromDate, toDate)
	if err != nil {
		// Post to slack
		msg := fmt.Sprintf("[go-sync-cron-vn-v3] processAutoReportTasksInfo. Error get tasks: %v", err)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, msg)
		return nil
	}

	// 2. process tasks
	taskReportData, slackMessage := createReportData(tasks)
	if slackMessage != "" {
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.TASKER_CLICK_START_END_BUTTON_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, slackMessage)
	}
	return taskReportData
}
