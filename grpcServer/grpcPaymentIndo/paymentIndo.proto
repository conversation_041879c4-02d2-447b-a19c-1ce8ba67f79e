syntax = "proto3";
package grpcPaymentIndo;

option go_package = "gitlab.com/btaskee/go-payment-indo-v3/grpcPaymentIndo";

import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentRequest/payment-request.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response/response.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentMidtransTransaction/paymentMidtransTransaction.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/vatRequest/vatRequest.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/refundTransaction/refundTransaction.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/danaTransaction/danaTransaction.proto";

service PaymentIndo {
  rpc CreateGiftByComboVoucher (paymentRequest.PaymentRequest) returns (response.Response) {}
  rpc ChargePayment (paymentMidtransTransaction.PaymentMidtransTransaction) returns (response.Response) {}
  rpc CreateVatRequest (vatRequest.VatRequest) returns (response.Response) {}
  rpc Refund (refundTransaction.RefundTransaction) returns (response.Response) {}
  rpc SubscriptionPayment (paymentRequest.PaymentRequest) returns (response.Response) {}
  rpc CheckTransactionStatusMidtrans (paymentMidtransTransaction.PaymentMidtransTransaction) returns (response.Response) {}
  rpc CheckTransactionStatusDANA (danaTransaction.DanaTransaction) returns (response.Response) {}
}
