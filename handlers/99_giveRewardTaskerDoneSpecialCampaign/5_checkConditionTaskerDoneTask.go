package giveRewardTaskerDoneSpecialCampaign

import (
	"time"

	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	modelSpecialCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/specialCampaign"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func checkConditionTaskerDoneTask(taskerId string, startDate, endDate time.Time, campaign *modelSpecialCampaign.SpecialCampaign) int32 {
	query := bson.M{
		"isoCode":                 local.ISO_CODE,
		"status":                  globalConstant.TASK_STATUS_DONE,
		"acceptedTasker.taskerId": taskerId,
		"date": bson.M{
			"$gte": startDate,
			"$lte": endDate,
		},
	}
	// add condition city if set
	if len(campaign.City) > 0 {
		query["taskPlace.city"] = bson.M{"$in": campaign.City}
	}

	// add condition services if set
	if len(campaign.ApplyForServices) > 0 {
		query["serviceId"] = bson.M{"$in": campaign.ApplyForServices}
	}
	var tasksDone []*modelTask.Task
	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE], query, bson.M{"_id": 1}, &tasksDone)
	if err != nil {
		local.Logger.Info("[SYN CRON VN V3 - error giveRewardTaskerDoneSpecialCampaign] get all task", zap.Error(err))
		return 0
	}

	if len(tasksDone) == 0 {
		return 0
	}

	var taskDoneIds []string
	for _, task := range tasksDone {
		taskDoneIds = append(taskDoneIds, task.GetXId())
	}

	count := cast.ToInt64(len(tasksDone))
	// add condition rate task if set
	if campaign.MinRateTask > 0 {
		isExistTaskRate, err := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_RATING[local.ISO_CODE], bson.M{"taskId": bson.M{"$in": taskDoneIds}, "taskerId": taskerId, "rate": bson.M{"$lt": campaign.MinRateTask}})
		if err != nil {
			local.Logger.Info("[SYN CRON VN V3 - giveRewardTaskerDoneSpecialCampaign] check rating task", zap.Error(err))
			return 0
		}

		if isExistTaskRate {
			// nếu tồn tại 1 rating có số sao thấp hơn minRateTask thì set count về 0
			count = 0
		}
	}
	return cast.ToInt32(count)
}
