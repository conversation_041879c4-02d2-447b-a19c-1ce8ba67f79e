// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcBNPLVN

import (
	context "context"
	taskerBNPLRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerBNPLRequest"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// BNPLVNClient is the client API for BNPLVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BNPLVNClient interface {
	ChargeTaskerBNPLFee(ctx context.Context, in *taskerBNPLRequest.TaskerBNPLRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type bNPLVNClient struct {
	cc grpc.ClientConnInterface
}

func NewBNPLVNClient(cc grpc.ClientConnInterface) BNPLVNClient {
	return &bNPLVNClient{cc}
}

func (c *bNPLVNClient) ChargeTaskerBNPLFee(ctx context.Context, in *taskerBNPLRequest.TaskerBNPLRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/grpcBNPLVN.BNPLVN/ChargeTaskerBNPLFee", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BNPLVNServer is the server API for BNPLVN service.
// All implementations must embed UnimplementedBNPLVNServer
// for forward compatibility
type BNPLVNServer interface {
	ChargeTaskerBNPLFee(context.Context, *taskerBNPLRequest.TaskerBNPLRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedBNPLVNServer()
}

// UnimplementedBNPLVNServer must be embedded to have forward compatible implementations.
type UnimplementedBNPLVNServer struct {
}

func (UnimplementedBNPLVNServer) ChargeTaskerBNPLFee(context.Context, *taskerBNPLRequest.TaskerBNPLRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChargeTaskerBNPLFee not implemented")
}
func (UnimplementedBNPLVNServer) mustEmbedUnimplementedBNPLVNServer() {}

// UnsafeBNPLVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BNPLVNServer will
// result in compilation errors.
type UnsafeBNPLVNServer interface {
	mustEmbedUnimplementedBNPLVNServer()
}

func RegisterBNPLVNServer(s *grpc.Server, srv BNPLVNServer) {
	s.RegisterService(&_BNPLVN_serviceDesc, srv)
}

func _BNPLVN_ChargeTaskerBNPLFee_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(taskerBNPLRequest.TaskerBNPLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BNPLVNServer).ChargeTaskerBNPLFee(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcBNPLVN.BNPLVN/ChargeTaskerBNPLFee",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BNPLVNServer).ChargeTaskerBNPLFee(ctx, req.(*taskerBNPLRequest.TaskerBNPLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _BNPLVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcBNPLVN.BNPLVN",
	HandlerType: (*BNPLVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ChargeTaskerBNPLFee",
			Handler:    _BNPLVN_ChargeTaskerBNPLFee_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "BNPLVN.proto",
}
