package extendExpiredComboVoucher

import (
	"context"
	"fmt"
	"log"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPaymentVN"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/comboVoucher"
	modelPaymentRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentRequest"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/transaction"
	modelUserComboVoucher "gitlab.com/btaskee/go-services-model-v2/grpcmodel/userComboVoucher"
	"go.mongodb.org/mongo-driver/bson"
)

func extendComboVoucher(comboVoucherToExtend []*modelUserComboVoucher.UserComboVoucher, mapComboVouchers map[string]*comboVoucher.ComboVoucher) (comboVoucherExtendFailed []*modelUserComboVoucher.UserComboVoucher) {
	comboVoucherExtendFailed = []*modelUserComboVoucher.UserComboVoucher{}
	client, connect, err := grpcPaymentVN.ConnectGRPCPaymentVN(cfg.GrpcPaymentPort)
	defer connect.Close()
	if err != nil {
		message := fmt.Sprintf("Sync cron v3: Lỗi connect payment service để tạo voucher. Vui lòng thông báo DEV xử lý, err: %v", err)
		globalLib.PostToSlack(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], globalConstant.SLACK_USER_NAME, message)
		comboVoucherExtendFailed = append(comboVoucherExtendFailed, comboVoucherToExtend...)
		return
	}
	for _, userComboVoucher := range comboVoucherToExtend {
		comboVoucher := mapComboVouchers[userComboVoucher.ComboVoucherId]
		if comboVoucher == nil {
			comboVoucherExtendFailed = append(comboVoucherExtendFailed, userComboVoucher)
			continue
		}

		enoughMoney, errCode := lib.CheckEnoughMoney(userComboVoucher.UserId, comboVoucher.Price)
		if errCode != nil || !enoughMoney["enough"].(bool) {
			comboVoucherExtendFailed = append(comboVoucherExtendFailed, userComboVoucher)
			continue
		}

		_, err = client.PayComboVoucherVN(context.Background(), &modelPaymentRequest.PaymentRequest{
			AskerId:            userComboVoucher.UserId,
			ComboVoucherId:     userComboVoucher.ComboVoucherId,
			UserComboVoucherId: userComboVoucher.XId,
			PaymentMethod: &transaction.TransactionPayment{
				Method: globalConstant.PAYMENT_METHOD_CREDIT,
			},
		})
		if err != nil {
			log.Printf("Sync cron v3: Lỗi khi pay combo voucher. userComboVoucherId: %v, err: %v", userComboVoucher.XId, err)
			comboVoucherExtendFailed = append(comboVoucherExtendFailed, userComboVoucher)
			continue
		}

		updateHistoryExtendComboVoucher(userComboVoucher)
	}

	return
}

func updateHistoryExtendComboVoucher(userComboVoucher *modelUserComboVoucher.UserComboVoucher) {
	// update history
	var newUserComboVoucher *modelUserComboVoucher.UserComboVoucher
	globalDataAccess.GetOneById(globalCollection.COLLECTION_USER_COMBO_VOUCHER[local.ISO_CODE], userComboVoucher.XId, bson.M{"expiredDate": 1, "vouchers": 1}, &newUserComboVoucher)
	queryPush := bson.M{
		"changeHistories": bson.M{
			"from": globalConstant.CHANGES_HISTORY_FROM_SYNC_CRON,
			"key":  globalConstant.CHANGES_HISTORY_USER_COMBO_VOUCHER_EXTEND,
			"content": bson.M{
				"oldExpiredDate": userComboVoucher.ExpiredDate,
				"newExpiredDate": newUserComboVoucher.GetExpiredDate(),
				"oldVouchers":    userComboVoucher.Vouchers,
				"newVouchers":    newUserComboVoucher.GetVouchers(),
			},
			"createdBy": globalConstant.CHANGES_HISTORY_FROM_SYNC_CRON,
			"createdAt": globalLib.GetCurrentTime(local.TimeZone),
		},
		"extendedHistories": globalLib.GetCurrentTime(local.TimeZone),
	}
	_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_USER_COMBO_VOUCHER[local.ISO_CODE], userComboVoucher.XId,
		bson.M{
			"$push": queryPush,
		})
	if err != nil {
		log.Printf("Sync cron v3: Lỗi khi update userComboVoucher sau khi tạo combo voucher. userComboVoucherId: %v, err: %v", userComboVoucher.XId, err)
	}
}
