// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcLocking

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	lockingMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/lockingMessage"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// LockingClient is the client API for Locking service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type LockingClient interface {
	ValidateDuplicate(ctx context.Context, in *lockingMessage.ValidateDuplicateRequest, opts ...grpc.CallOption) (*empty.Empty, error)
}

type lockingClient struct {
	cc grpc.ClientConnInterface
}

func NewLockingClient(cc grpc.ClientConnInterface) LockingClient {
	return &lockingClient{cc}
}

func (c *lockingClient) ValidateDuplicate(ctx context.Context, in *lockingMessage.ValidateDuplicateRequest, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcLocking.Locking/ValidateDuplicate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// LockingServer is the server API for Locking service.
// All implementations must embed UnimplementedLockingServer
// for forward compatibility
type LockingServer interface {
	ValidateDuplicate(context.Context, *lockingMessage.ValidateDuplicateRequest) (*empty.Empty, error)
	mustEmbedUnimplementedLockingServer()
}

// UnimplementedLockingServer must be embedded to have forward compatible implementations.
type UnimplementedLockingServer struct {
}

func (UnimplementedLockingServer) ValidateDuplicate(context.Context, *lockingMessage.ValidateDuplicateRequest) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateDuplicate not implemented")
}
func (UnimplementedLockingServer) mustEmbedUnimplementedLockingServer() {}

// UnsafeLockingServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to LockingServer will
// result in compilation errors.
type UnsafeLockingServer interface {
	mustEmbedUnimplementedLockingServer()
}

func RegisterLockingServer(s *grpc.Server, srv LockingServer) {
	s.RegisterService(&_Locking_serviceDesc, srv)
}

func _Locking_ValidateDuplicate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(lockingMessage.ValidateDuplicateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(LockingServer).ValidateDuplicate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcLocking.Locking/ValidateDuplicate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(LockingServer).ValidateDuplicate(ctx, req.(*lockingMessage.ValidateDuplicateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Locking_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcLocking.Locking",
	HandlerType: (*LockingServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ValidateDuplicate",
			Handler:    _Locking_ValidateDuplicate_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "locking.proto",
}
