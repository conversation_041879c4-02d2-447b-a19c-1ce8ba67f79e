package globalDataAccessV2

import (
	"context"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcDataAccess"
	modelDataAccess "gitlab.com/btaskee/go-services-model-v2/grpcmodel/dataAccess"
	"go.mongodb.org/mongo-driver/bson"
)

func (d *DataAccess) DeleteOneById(collectionName string, id string) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(DATA_ACCESS_URL[d.isoCode][MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Id: id}
	_, err = client.DeleteOneById(context.Background(), request)
	return err
}

func (d *DataAccess) DeleteOneByQuery(collectionName string, query interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(DATA_ACCESS_URL[d.isoCode][MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(REGISTRY, query)
	if err != nil {
		return err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery}
	_, err = client.DeleteOneByQuery(context.Background(), request)
	return err
}

func (d *DataAccess) DeleteAllByQuery(collectionName string, query interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(DATA_ACCESS_URL[d.isoCode][MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(REGISTRY, query)
	if err != nil {
		return err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery}
	_, err = client.DeleteAllByQuery(context.Background(), request)
	return err
}
