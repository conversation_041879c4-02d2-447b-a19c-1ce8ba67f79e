package grpcBPoint

import (
	"gitlab.com/btaskee/********************/grpcBPoint"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"google.golang.org/grpc"
)

func ConnectGRPCBPoint(endpoint string) (grpcBPoint.BPointClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcBPoint.NewBPointClient(connect)
	return client, connect, err
}
