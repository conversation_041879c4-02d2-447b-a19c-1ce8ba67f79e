package grpcEmailVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcEmailVN"
	"google.golang.org/grpc"
)

func ConnectGRPCEmailVN(endpoint string) (grpcEmailVN.EmailVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcEmailVN.NewEmailVNClient(connect)
	return client, connect, err
}
