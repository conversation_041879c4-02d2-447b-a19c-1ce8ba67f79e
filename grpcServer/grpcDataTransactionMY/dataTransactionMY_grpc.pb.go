// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcDataTransactionMY

import (
	context "context"
	dataTransactionMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/dataTransactionMessage"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// DataTransactionClient is the client API for DataTransaction service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DataTransactionClient interface {
	DoneTask(ctx context.Context, in *dataTransactionMessage.DoneTaskRequest, opts ...grpc.CallOption) (*dataTransactionMessage.DoneTaskResponse, error)
	UpdateFinancial(ctx context.Context, in *dataTransactionMessage.UpdateFinancialRequest, opts ...grpc.CallOption) (*dataTransactionMessage.UpdateFinancialResponse, error)
}

type dataTransactionClient struct {
	cc grpc.ClientConnInterface
}

func NewDataTransactionClient(cc grpc.ClientConnInterface) DataTransactionClient {
	return &dataTransactionClient{cc}
}

func (c *dataTransactionClient) DoneTask(ctx context.Context, in *dataTransactionMessage.DoneTaskRequest, opts ...grpc.CallOption) (*dataTransactionMessage.DoneTaskResponse, error) {
	out := new(dataTransactionMessage.DoneTaskResponse)
	err := c.cc.Invoke(ctx, "/grpcDataTransactionMY.DataTransaction/DoneTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dataTransactionClient) UpdateFinancial(ctx context.Context, in *dataTransactionMessage.UpdateFinancialRequest, opts ...grpc.CallOption) (*dataTransactionMessage.UpdateFinancialResponse, error) {
	out := new(dataTransactionMessage.UpdateFinancialResponse)
	err := c.cc.Invoke(ctx, "/grpcDataTransactionMY.DataTransaction/UpdateFinancial", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DataTransactionServer is the server API for DataTransaction service.
// All implementations must embed UnimplementedDataTransactionServer
// for forward compatibility
type DataTransactionServer interface {
	DoneTask(context.Context, *dataTransactionMessage.DoneTaskRequest) (*dataTransactionMessage.DoneTaskResponse, error)
	UpdateFinancial(context.Context, *dataTransactionMessage.UpdateFinancialRequest) (*dataTransactionMessage.UpdateFinancialResponse, error)
	mustEmbedUnimplementedDataTransactionServer()
}

// UnimplementedDataTransactionServer must be embedded to have forward compatible implementations.
type UnimplementedDataTransactionServer struct {
}

func (UnimplementedDataTransactionServer) DoneTask(context.Context, *dataTransactionMessage.DoneTaskRequest) (*dataTransactionMessage.DoneTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DoneTask not implemented")
}
func (UnimplementedDataTransactionServer) UpdateFinancial(context.Context, *dataTransactionMessage.UpdateFinancialRequest) (*dataTransactionMessage.UpdateFinancialResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateFinancial not implemented")
}
func (UnimplementedDataTransactionServer) mustEmbedUnimplementedDataTransactionServer() {}

// UnsafeDataTransactionServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DataTransactionServer will
// result in compilation errors.
type UnsafeDataTransactionServer interface {
	mustEmbedUnimplementedDataTransactionServer()
}

func RegisterDataTransactionServer(s *grpc.Server, srv DataTransactionServer) {
	s.RegisterService(&_DataTransaction_serviceDesc, srv)
}

func _DataTransaction_DoneTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataTransactionMessage.DoneTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataTransactionServer).DoneTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataTransactionMY.DataTransaction/DoneTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataTransactionServer).DoneTask(ctx, req.(*dataTransactionMessage.DoneTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DataTransaction_UpdateFinancial_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(dataTransactionMessage.UpdateFinancialRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DataTransactionServer).UpdateFinancial(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcDataTransactionMY.DataTransaction/UpdateFinancial",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DataTransactionServer).UpdateFinancial(ctx, req.(*dataTransactionMessage.UpdateFinancialRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _DataTransaction_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcDataTransactionMY.DataTransaction",
	HandlerType: (*DataTransactionServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "DoneTask",
			Handler:    _DataTransaction_DoneTask_Handler,
		},
		{
			MethodName: "UpdateFinancial",
			Handler:    _DataTransaction_UpdateFinancial_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dataTransactionMY.proto",
}
