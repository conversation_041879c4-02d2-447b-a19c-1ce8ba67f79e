// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.25.0-devel
// 	protoc        v3.21.9
// source: scheduleVN.proto

package grpcScheduleVN

import (
	response "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response"
	taskSchedule "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskSchedule"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_scheduleVN_proto protoreflect.FileDescriptor

var file_scheduleVN_proto_rawDesc = []byte{
	0x0a, 0x10, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x56, 0x4e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0e, 0x67, 0x72, 0x70, 0x63, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65,
	0x56, 0x4e, 0x1a, 0x49, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62,
	0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63,
	0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2f, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2f, 0x72,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x51, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65,
	0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f,
	0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x6d, 0x6f, 0x64, 0x65, 0x6c,
	0x2f, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x2f, 0x74, 0x61,
	0x73, 0x6b, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x32, 0xc1, 0x02, 0x0a, 0x0a, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x56, 0x4e, 0x12,
	0x4b, 0x0a, 0x17, 0x41, 0x64, 0x64, 0x48, 0x6f, 0x6d, 0x65, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69,
	0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x1a, 0x2e, 0x74, 0x61, 0x73,
	0x6b, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4a, 0x0a, 0x16,
	0x41, 0x64, 0x64, 0x48, 0x6f, 0x6d, 0x65, 0x43, 0x6f, 0x6f, 0x6b, 0x69, 0x6e, 0x67, 0x53, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x12, 0x1a, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x1a, 0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4b, 0x0a, 0x17, 0x41, 0x64, 0x64, 0x48,
	0x6f, 0x75, 0x73, 0x65, 0x6b, 0x65, 0x65, 0x70, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x64,
	0x75, 0x6c, 0x65, 0x12, 0x1a, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x1a,
	0x12, 0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x4d, 0x0a, 0x19, 0x41, 0x64, 0x64, 0x4f, 0x66, 0x66, 0x69,
	0x63, 0x65, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75,
	0x6c, 0x65, 0x12, 0x1a, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x65, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x1a, 0x12,
	0x2e, 0x72, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x42, 0x43, 0x5a, 0x41, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x65, 0x65, 0x2f, 0x67, 0x6f, 0x2d, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2d, 0x6d, 0x6f, 0x64, 0x65, 0x6c, 0x2d, 0x76, 0x32, 0x2f,
	0x67, 0x72, 0x70, 0x63, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x2f, 0x67, 0x72, 0x70, 0x63, 0x53,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x56, 0x4e, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var file_scheduleVN_proto_goTypes = []interface{}{
	(*taskSchedule.TaskSchedule)(nil), // 0: taskSchedule.TaskSchedule
	(*response.Response)(nil),         // 1: response.Response
}
var file_scheduleVN_proto_depIdxs = []int32{
	0, // 0: grpcScheduleVN.ScheduleVN.AddHomeCleaningSchedule:input_type -> taskSchedule.TaskSchedule
	0, // 1: grpcScheduleVN.ScheduleVN.AddHomeCookingSchedule:input_type -> taskSchedule.TaskSchedule
	0, // 2: grpcScheduleVN.ScheduleVN.AddHousekeepingSchedule:input_type -> taskSchedule.TaskSchedule
	0, // 3: grpcScheduleVN.ScheduleVN.AddOfficeCleaningSchedule:input_type -> taskSchedule.TaskSchedule
	1, // 4: grpcScheduleVN.ScheduleVN.AddHomeCleaningSchedule:output_type -> response.Response
	1, // 5: grpcScheduleVN.ScheduleVN.AddHomeCookingSchedule:output_type -> response.Response
	1, // 6: grpcScheduleVN.ScheduleVN.AddHousekeepingSchedule:output_type -> response.Response
	1, // 7: grpcScheduleVN.ScheduleVN.AddOfficeCleaningSchedule:output_type -> response.Response
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_scheduleVN_proto_init() }
func file_scheduleVN_proto_init() {
	if File_scheduleVN_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_scheduleVN_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_scheduleVN_proto_goTypes,
		DependencyIndexes: file_scheduleVN_proto_depIdxs,
	}.Build()
	File_scheduleVN_proto = out.File
	file_scheduleVN_proto_rawDesc = nil
	file_scheduleVN_proto_goTypes = nil
	file_scheduleVN_proto_depIdxs = nil
}
