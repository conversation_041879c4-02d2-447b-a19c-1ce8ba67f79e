package grpcWebsocketVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcWebsocketVN"
	"google.golang.org/grpc"
)

func ConnectGRPCWebsocketVN(endpoint string) (grpcWebsocketVN.WebsocketVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcWebsocketVN.NewWebsocketVNClient(connect)
	return client, connect, err
}
