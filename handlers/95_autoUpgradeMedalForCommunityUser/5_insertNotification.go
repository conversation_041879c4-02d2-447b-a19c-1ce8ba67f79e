package autoUpgradeMedalForCommunityUser

import (
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalCommunityConstant "gitlab.com/btaskee/go-services-model-v2/globalConstant/community"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelCommunityNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/communityNotification"
	modelCommunityUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/communityUser"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	"gitlab.com/btaskee/go-services-model-v2/localization"
)

func insertNotification(userCommunity *modelCommunityUsers.CommunityUser, medalText *modelService.ServiceText, medalId string) {
	if userCommunity != nil {
		msg := &modelService.ServiceText{
			Vi: localization.T(globalConstant.LANG_VI, "NOTIFICATION_FROM_MEDAL", globalLib.LocalizeServiceName(globalConstant.LANG_VI, medalText)),
			En: localization.T(globalConstant.LANG_EN, "NOTIFICATION_FROM_MEDAL", globalLib.LocalizeServiceName(globalConstant.LANG_EN, medalText)),
			Th: localization.T(globalConstant.LANG_TH, "NOTIFICATION_FROM_MEDAL", globalLib.LocalizeServiceName(globalConstant.LANG_TH, medalText)),
			Id: localization.T(globalConstant.LANG_ID, "NOTIFICATION_FROM_MEDAL", globalLib.LocalizeServiceName(globalConstant.LANG_ID, medalText)),
			Ko: localization.T(globalConstant.LANG_KO, "NOTIFICATION_FROM_MEDAL", globalLib.LocalizeServiceName(globalConstant.LANG_KO, medalText)),
		}

		notification := &modelCommunityNotification.CommunityNotification{
			XId:     globalLib.GenerateObjectId(),
			UserId:  userCommunity.XId,
			Message: msg,
			Icon:    globalCommunityConstant.COMMUNITY_ICON_BADGE,
			From: &modelCommunityNotification.CommunityNotificationFrom{
				UserId: userCommunity.XId,
				Avatar: userCommunity.Avatar,
			},
			NavigateTo: globalCommunityConstant.PAYLOAD_NAVIGATE_TO_COMMUNITY_PROFILE_UPDATE,
			CreatedAt:  globalLib.GetCurrentTimestamp(local.TimeZone),
			MedalId:    medalId,
		}
		globalDataAccess.InsertOne(globalCollection.COLLECTION_COMMUNITY_NOTIFICATION[local.ISO_CODE], notification)
	}
}
