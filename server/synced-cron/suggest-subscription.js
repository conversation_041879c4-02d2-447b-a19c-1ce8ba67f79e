import { Meteor } from 'meteor/meteor';
import accounting from 'accounting';
import _ from 'lodash';
import { Setting } from '/server/lib/collections';
import {
  revertTimeZone,
  sendNotificationByIds,
  sendEmailFunction,
  trackingScheduleStatus,
} from '/server/helper-function';

const getSubscriptionDiscount = function (language) {
  const subscriptionSetting = Meteor.appCollection.SubscriptionSettings.findOne({}, {fields: {discount: 1}});

  const discount = _.get(subscriptionSetting, 'discount', []);
  let discountObject = {};
  let result = '';
  discount.forEach((city) => {
    if ( city && city.discountByMonth && city.discountByMonth.length > 0 ) {
      discountObject[city.city] = city.discountByMonth.filter((month) => {
        return month.percentage > 0;
      });
      if ( discountObject[city.city].length === 0 ) {
        delete discountObject[city.city];
      }
    }
  });
  if ( _.keys(discountObject).length > 0 ) {
    _.keys(discountObject).forEach((city) => {
      switch (language) {
        case 'vi':
          result += `
            <div>
              <div style="margin-left: 10px;">Giảm giá tại ${city}</div>
              ${discountObject[city].map((discount) => {
                return `<div style="margin-left: 20px;">Giảm ${discount.percentage * 100}% cho gói ${discount.month} tháng</div>`;
              }).join('')}
            </div>
          `;
          break;
        case 'en':
          result += `
            <div>
              <div style="margin-left: 10px;">Discount for cleaning with subscription in ${city}</div>
              ${discountObject[city].map((discount) => {
                return `<div style="margin-left: 20px;">Discount ${discount.percentage * 100}% for ${discount.month} month(s) subscription</div>`;
              }).join('')}
            </div>
          `;
          break;
        case 'ko':
          result += `
            <div>
              <div style="margin-left: 10px;">${city} 청소 구독 서비스 할인 안내</div>
              ${discountObject[city].map((discount) => {
                return `<div style="margin-left: 20px;">${discount.month}개월 구독 서비스 ${discount.percentage * 100}% 할인</div>`;
              }).join('')}
            </div>
          `;
          break;
        default: break;
      }
      
    });
  }
  return result;
  
};

const sendSubscriptionSuggestionEmail = function (user, setting) {
  
  if (!user.emails || user.emails.length === 0) return;
  var html = '<html>';
  html += '<head>';
  html += '<link href="https://fonts.googleapis.com/css?family=Roboto" rel="stylesheet">';
  html += '</head>';
  html += '<body style="max-width: 600px; padding: 20px; font-family: Roboto, sans-serif; line-height: 22px; margin: 0 auto;">';
  html += '<div style="text-align: right; color: #FF611C; font-size: 22px; font-weight: bold; margin-bottom: 30px;">bTaskee</div>';
  html += '<div style="font-size: 16px;">';
  html += '<div style="width: 100%; text-align: center;">(Scroll down for English version)</div>';
  html += '<div style="width: 100%; text-align: center;">(한국어 버전을 보려면 아래로 스크롤하세요)</div>';
  html += '<br/>';
  // Begin Vietnamese version
  html += '<div style="font-size: 18px; font-weight: bold;">Xin chào ' + user.name + ',</div>';
  html += '<br/>';
  html += '<div>Cảm ơn quý khách hàng đã sử dụng bTaskee trong thời gian qua!</div>';
  html += '<div>';
  html += '<span>bTaskee vừa ra mắt </span>';
  html += '<span style="font-weight: bold; color: #43A047; font-size: 15px;">Gói dịch vụ dọn dẹp nhà - Thanh toán 1 lần, sử dụng 1 tháng.</span>';
  html += '</div>';
  html += '<br/>';
  html += '<div style="font-weight: bold; color: #FF611C">Các điểm nổi bậc của gói dịch vụ:</div>';
  html += '<div>- Tiết kiệm chi phí và thời gian hơn đăng mỗi công việc khi cần.</div>';
  html += getSubscriptionDiscount('vi');
  html += '<div></div>';
  html += '<div>- Thanh toán 1 lần với bTaskee, người làm đến hoàn thành công việc và ra về. Bạn không phải trả tiền mặt cho người làm.</div>';
  html += '<div>- Người làm luôn là người ưa thích, đã từng làm tốt công việc của bạn.</div>';
  html += '<div>- Được hỗ trợ đổi địa điểm ở những thành phố mà bTaskee có mặt (Trong trường hợp quý khách chuyển nhà, chuyển công tác, ...).</div>';
  html += '<div>- Đặt lịch làm việc cho cả tháng khi mua gói dịch vụ. Đặt 1 giờ cố định và các ngày trong tuần do bạn chọn. Công việc sẽ được hệ thống đăng tự động trước 2 ngày.</div>';
  html += '<div style="color: #919191;">Ví dụ lịch làm việc như sau: 1 buổi làm 3 tiếng, vào lúc 9 giờ sáng các ngày: Thứ Hai, Thứ Tư, Thứ Sáu hàng tuần.</div>';
  html += '<div>- Nếu vào lịch làm việc mà bạn bận đột xuất, bạn có thể đổi sang giờ làm khác.</div>';
  html += '<br/>';
  html += '<div style="font-weight: bold; color: #FF611C">Hình thức thanh toán:</div>';
  html += '<div>- Thời điểm hiện tại bTaskee chỉ hỗ trợ thanh toán bằng hình thức chuyển khoản ngân hàng.</div>';
  html += '<div>- Ngay sau khi bạn thực hiện mua gói dịch vụ dọn dẹp nhà trên ứng dụng. Trên ứng dụng sẽ cho bạn biết mã đơn hàng và hướng dẫn thanh toán, đồng thời ứng dụng cũng gửi 1 email thông báo đơn đặt hàng đến bạn.</div>';
  html += '<div>- Trong thời gian làm việc, 8 giờ sáng đến 18 giờ chiều từ Thứ Hai đến Thứ Bảy hàng tuần. Ngay khi bTaskee nhận được thông tin chuyển khoản của bạn sẽ kích hoạt gói dịch vụ và gửi email thông báo thanh toán thành công.</div>';
  html += '<div>- Thời hạn của mỗi đơn hàng là 3 ngày từ ngày đặt hàng. Nếu sau 3 ngày quý khách chưa thanh toán đơn hàng sẽ tự huỷ.</div>';
  html += '<br/>';
  html += '<div style="font-weight: bold; color: #FF611C">Một vài lưu ý về gói dịch vụ:</div>';
  html += '<div>- Khi bạn huỷ công việc với bất kỳ lý do gì, chúng tôi sẽ hỗ trợ hoàn tiền cho 2 lần huỷ công việc đầu tiên.</div>';
  html += '<div>- Tuỳ vào thời gian bạn huỷ là sớm hay muộn hoặc cận kề so với giờ làm, chúng tôi sẽ hoàn trả 100%, 50% giá trị công việc bị huỷ, hoặc có thể không hoàn tiền.</div>';
  html += '<div>- Quy định hủy gói đã thanh toán và hoàn tiền: bTaskee hỗ trợ 1 trong 2 hình thức hoàn tiền bên dưới</div>';
  html += '<div>1. Hoàn tiền qua bPay: bTaskee hoàn lại tổng số tiền của những buổi chưa sử dụng.</div>';
  html += '<div>2. Hoàn tiền qua chuyển khoản ngân hàng: bTaskee hoàn lại tổng số tiền của những buổi chưa sử dụng trừ đi 20% giá trị của gói ban đầu.</div>';

  html += '<br/>';
  // Begin email footer
  html += '<hr style="margin: 30px 0 0 0; border-left-style: none"/>';
  html += '<div>';
  html += '<div style="text-align: center; line-height: 25px; color: #ff611c;">';
  html += '<div>Giới thiệu bạn bè sử dụng bTaskee với mã giới thiệu</div>';
  html += '<div style="font-size: 24px; color: #5cb85c">';
  html += user.referralCode;
  html += '</div>';
  html += '<div>';
  html += 'Bạn nhận được ' + accounting.formatNumber(user.referralValue || setting.referralValue) + 'VND trong tài khoản khuyến mãi và bạn của bạn cũng vậy, sau khi công việc đầu tiên được hoàn thành.';
  html += '</div>';
  html += '<a href="https://www.facebook.com/dialog/share?app_id=' + Meteor.settings.socialConfig.facebook.appId + '&display=popup&href=https://btaskee.com/download&redirect_uri=https://btaskee.com/"><img style="width: 35px; border-radius: 5px;" src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/PoPQfoY5mTWNuFpiS"/></a>';
  html += '</div>';
  html += '<br/>';
  html += '<div style="color: #ff611c; font-size: 16px; font-weight: bold;">bTaskee</div>';
  html += '<div>Ứng dụng giúp việc nhà nhanh chóng, hiệu quả</div>';
  html += '<a href="https://www.facebook.com/btaskee"><img src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/ftBfYvf2zZ9JxxTCK" style="width: 36px; border-radius: 50%; margin: 10px"/></a>';
  html += '<a href="https://www.instagram.com/btaskee"><img src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/sQm7nkNNogBGZG5Ap" style="width: 36px; border-radius: 10px; margin: 10px"/></a>';
  html += '<a href="https://twitter.com/btaskee"><img src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/Nei5WAg3SSuyTtDij" style="width: 36px; border-radius: 50%; margin: 10px"/></a>';
  html += '<a href="https://www.youtube.com/channel/UC55F4JRrI9ewVpoR6DOMfkQ"><img src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/TjfaipCvFKrZvz7gQ" style="width: 50px; border-radius: 50%; margin: 0"/></a>';
  html += '</div>';
  // End Vietnamese version
  // Begin English version
  html += '<hr style="margin: 30px 0 0 0; border-left-style: none"/>';
  html += '<br/>';
  html += '<div style="font-size: 18px; font-weight: bold;">Hello ' + user.name + ',</div>';
  html += '<br/>';
  html += '<div>Thank you for using bTaskee in past times!</div>';
  html += '<div>';
  html += '<span>bTaskee has been launched </span>';
  html += '<span style="font-weight: bold; color: #43A047; font-size: 15px;">Home cleaning subscription - Pay 1 time, use 1 month.</span>';
  html += '</div>';
  html += '<br/>';
  html += '<div style="font-weight: bold; color: #FF611C">The highlights of the subscription:</div>';
  html += '<div>- You save more time and money than posting each task.</div>';
  html += getSubscriptionDiscount('en');
  html += '<div>- You just need pay 1 time for bTaskee, the tasker come to your house to finish the task and leave. You do not have to pay cash for the tasker.</div>';
  html += '<div>- The taskers are in the favourites always.</div>';
  html += '<div>- You are supported change working place in the cities where bTaskee is launching.</div>';
  html += '<div>- Book the schedule for 1 month when you buy the subscription.</div>';
  html += '<div style="color: #919191;">Example for schedule: 1 task spend 3 hours, start at 9AM in Monday, Wednesday, Friday weekly</div>';
  html += '<div>- If you busy suddenly on next task, you can change the working time of that task to another.</div>';
  html += '<br/>';
  html += '<div style="font-weight: bold; color: #FF611C">Payment method:</div>';
  html += '<div>- At now bTaskee support Bank Transfer only.</div>';
  html += '<div>- After buying the subscription in bTaskee app. The app will show you the order code, payment informations. At the same time the app will send to you an order detail email.</div>';
  html += '<div>- During office hours, 8AM - 18PM from Monday to Saturday. When bTaskee is received your payment, bTaskee will active your subscription and send to you a paid email.</div>';
  html += '<div>- After 3 days from your buying, if you still not pay, that subscription will be expired. You have to buy another when you need.</div>';
  html += '<br/>';
  html += '<div style="font-weight: bold; color: #FF611C">Some notes for the subscription:</div>';
  html += '<div>- When you cancel the tasks with any reason, we will refund to you 2 first tasks.</div>';
  html += '<div>- We will refund 100%, 50%, or not refund, that depend on your cancellation time is soon, late or close to working time</div>';
  html += '<div>- Regulations of cancellation and refund of paid subscription: bTaskee supports one of two refund options belows:</div>';
  html += '<div>1. Refund via bPay: bTaskee will refund the total amount of unused tasks.</div>';
  html += '<div>2. Refund via transfer internet banking: bTaskee will refund the total amount of unused tasks minus 20% of the total invoice.</div>';
  html += '<br/>';
  // Begin email footer
  html += '<hr style="margin: 30px 0 0 0; border-left-style: none"/>';
  html += '<div>';
  html += '<div style="text-align: center; line-height: 25px; color: #ff611c;">';
  html += '<div>Invite friends to use bTaskee with referral code</div>';
  html += '<div style="font-size: 24px; color: #5cb85c">';
  html += user.referralCode;
  html += '</div>';
  html += '<div>';
  html += 'You will receive ' + accounting.formatNumber(user.referralValue || setting.referralValue) + 'VND in promotion account and your friends too, after their first task is done.';
  html += '</div>';
  html += '<a href="https://www.facebook.com/dialog/share?app_id=' + Meteor.settings.socialConfig.facebook.appId + '&display=popup&href=https://btaskee.com/download&redirect_uri=https://btaskee.com/"><img style="width: 35px; border-radius: 5px;" src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/PoPQfoY5mTWNuFpiS"/></a>';
  html += '</div>';
  html += '<br/>';
  html += '<div style="color: #ff611c; font-size: 16px; font-weight: bold;">bTaskee</div>';
  html += '<div>On-demand home cleaning service.</div>';
  html += '<a href="https://www.facebook.com/btaskee"><img src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/ftBfYvf2zZ9JxxTCK" style="width: 36px; border-radius: 50%; margin: 10px"/></a>';
  html += '<a href="https://www.instagram.com/btaskee"><img src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/sQm7nkNNogBGZG5Ap" style="width: 36px; border-radius: 10px; margin: 10px"/></a>';
  html += '<a href="https://twitter.com/btaskee"><img src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/Nei5WAg3SSuyTtDij" style="width: 36px; border-radius: 50%; margin: 10px"/></a>';
  html += '<a href="https://www.youtube.com/channel/UC55F4JRrI9ewVpoR6DOMfkQ"><img src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/TjfaipCvFKrZvz7gQ" style="width: 50px; border-radius: 50%; margin: 0"/></a>';
  html += '</div>';

  // Begin Korean version
  html += '<hr style="margin: 30px 0 0 0; border-left-style: none"/>';
  html += '<br/>';
  html += '<div style="font-size: 18px; font-weight: bold;">안녕하세요 ' + user.name + ',</div>';
  html += '<br/>';
  html += '<div>bTaskee 사용해줘서 감사합니다!</div>';
  html += '<div>';
  html += '<span>btaskee는 서비스팩을 출시합니다 </span>';
  html += '<span style="font-weight: bold; color: #43A047; font-size: 15px;">한번만 지불, 한 달에 사용</span>';
  html += '</div>';
  html += '<br/>';
  html += '<div style="font-weight: bold; color: #FF611C">서비스 팩의 우대:</div>';
  html += '<div>- 매일 신청하는 것보다  서비스 팩은 시간과 비용을 더 절약 합니다.</div>';
  html += getSubscriptionDiscount('ko');
  html += '<div>- 고객님은 btaskee 회사에게 한번만 계산하시면 됩니다. 도우미에게 지불할게 필요 없습니다.</div>';
  html += '<div>- 도우미는 좋아하시는 도우미나 잘하는 도우미입니다.</div>';
  html += '<div>- btaskee 서비스가 있는 도시들에서 주소를 변경할수있습니다.</div>';
  html += '<div>- 서비스 팩을 사실때 1달에 신청 합니다. 선택하시는 고정시간에 따라 신청합니다. 시스템은 서비스를 자동으로 이틀전에 신청하겠합니다.</div>';
  html += '<div style="color: #919191;">보기 : 매주반복 월,수,목  3시간동안 9시부터 시작합니다.</div>';
  html += '<div>- 갑자기 바쁘시면 다른시간을 변경 하실 수있습니다.</div>';
  html += '<br/>';
  html += '<div style="font-weight: bold; color: #FF611C">계산 방법:</div>';
  html += '<div>- 현재 유일한 계산은  은행으로 계좌이체는것입니다.</div>';
  html += '<div>- 앱에서 서비스 팩을 사기는후. 앱은 계산코드 및 계산 안내방법을 알려드리겠습니다. 동시에 서비스 팩을 사는것에 대해 이메일을 보내드리겠습니다.</div>';
  html += '<div>- 근무시간에 월요일-토요일 오전8시부터 오후6시까지 은행으로 계좌이체는 것에 대해 통보를 받을때 서비스 팩을 시동하고 고객님께 이메일을 보내드리겠습니다.</div>';
  html += '<div>- 서비스 팩의 기한은 삼일입니다. 삼일후 계산 아직 안하시면 서비스 팩을 자동 취소하겠습니다.</div>';
  html += '<br/>';
  html += '<div style="font-weight: bold; color: #FF611C">서비스 팩에 유의:</div>';
  html += '<div>- 무슨이유로 처음 서비스를 2번 취소하시면 환불 하게됩니다.</div>';
  html += '<div>- 취소 시간에 따라 일찍이나 늦게 취소하시면 서비스 비용의 100%, 50% 환불하게 됩니다.아마도 환불 안 합니다.</div>';
  html += '<div>- 구독 취소 및 환불 규정 : bTaskee는 다음 두 가지 환불 옵션 중 하나를 지원합니다.</div>';
  html += '<div>1. bPay를 통한 환불 : bTaskee는 이용되지 않은 작업의 총액을 환불합니다.</div>';
  html += '<div>2. 인터넷 뱅킹을 통한 환불 : bTaskee는 이용되지 않은 작업의 총액에서 20 %를 제외하고  환불합니다.</div>';
  html += '<br/>';
  // Begin email footer
  html += '<hr style="margin: 30px 0 0 0; border-left-style: none"/>';
  html += '<div>';
  html += '<div style="text-align: center; line-height: 25px; color: #ff611c;">';
  html += '<div>친구에게 추천코드를 소개하세요</div>';
  html += '<div style="font-size: 24px; color: #5cb85c">';
  html += user.referralCode;
  html += '</div>';
  html += '<div>';
  html += 'btaskee의 사용자가 앱을 사용했을때 귀하의 추천코드를 입력했으니까 사용자와 귀하에게 ' + accounting.formatNumber(user.referralValue || setting.referralValue) + 'vnd를 드립니다 (첫일을 완성한 후).';
  html += '</div>';
  html += '<a href="https://www.facebook.com/dialog/share?app_id=' + Meteor.settings.socialConfig.facebook.appId + '&display=popup&href=https://btaskee.com/download&redirect_uri=https://btaskee.com/"><img style="width: 35px; border-radius: 5px;" src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/PoPQfoY5mTWNuFpiS"/></a>';
  html += '</div>';
  html += '<br/>';
  html += '<div style="color: #ff611c; font-size: 16px; font-weight: bold;">bTaskee</div>';
  html += '<div>홈클리닝 앱, 빠르고 편합니다.</div>';
  html += '<a href="https://www.facebook.com/btaskee"><img src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/ftBfYvf2zZ9JxxTCK" style="width: 36px; border-radius: 50%; margin: 10px"/></a>';
  html += '<a href="https://www.instagram.com/btaskee"><img src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/sQm7nkNNogBGZG5Ap" style="width: 36px; border-radius: 10px; margin: 10px"/></a>';
  html += '<a href="https://twitter.com/btaskee"><img src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/Nei5WAg3SSuyTtDij" style="width: 36px; border-radius: 50%; margin: 10px"/></a>';
  html += '<a href="https://www.youtube.com/channel/UC55F4JRrI9ewVpoR6DOMfkQ"><img src="https://btaskee-stag.s3-ap-southeast-1.amazonaws.com/avatars/TjfaipCvFKrZvz7gQ" style="width: 50px; border-radius: 50%; margin: 0"/></a>';
  html += '</div>';
  // End Korean version
  html += '</div>';
  // End email content

  html += '</body>';
  html += '</html>';
  const option = {
    'h:reply-to': '<EMAIL>',
    from: 'No Reply <<EMAIL>>',
    to: user.emails[0].address,
    subject: 'bTaskee cleaning subscription',
    html: html,
  };
  sendEmailFunction(option);
};

const suggestSubscription = function () {
  return;
	console.log("Start suggestSubscription ...");
  var message = null;
  try {
    let askerIds = [];
    const wfsetting = Setting.findOne();
    if (_.get(wfsetting, 'suggestSubscription.favoriteTasker', false)) {
      askerIds = askerIds.concat(
        Meteor.appCollection.users.find({
          type: 'ASKER',
          taskDone: { $gte: 5 },
          'favouriteTasker.2': { $exists: true },
          suggestSubscription: { $exists: false },
        }).map(user => (user._id))
      );
    }
    if (_.get(wfsetting, 'suggestSubscription.haveSchedule', false)) {
      const askerList1 = Meteor.appCollection.TaskSchedule.find({}).map(schedule => (schedule.askerId));
      const askerList2 = Meteor.appCollection.users.find({ type: 'ASKER', taskDone: { $gte: 5 } }).map(user => (user._id));
      askerIds = askerIds.concat(_.intersection(askerList1, askerList2));
    }
    if (askerIds.length > 0) {
      // Send Notification
      sendNotificationByIds(askerIds, {
        title: {
          vi: 'Thông báo',
          en: 'Announcement',
          ko: '메시지',
          th: 'Announcement',
        },
        text: {
          vi: 'bTaskee ra mắt gói dịch vụ dọn dẹp nhà - Thanh toán 1 lần, sử dụng 1 tháng.',
          en: 'bTaskee also provides subscription package for house cleaning service.',
          ko: 'btaskee는 서비스팩을 출시합니다 한번만 지불, 한 달에 사용.',
          th: 'bTaskee also provides subscription package for house cleaning service.',
        },
        payload: {},
      }, { isForceView: true, url: '/asker/subscription' });
      // Send Email.
      const setting = Meteor.appCollection.SettingSystem.findOne({}, { fields: { referralValue: 1 } });
      Meteor.appCollection.users.find({ _id: { $in: askerIds } }).forEach((user) => {
        sendSubscriptionSuggestionEmail(user, setting);
      });
    }
    // Update suggestSubscription
    Meteor.appCollection.users.update({ _id: { $in: askerIds } }, { $set: { suggestSubscription: true } }, { multi: true });
  } catch (e) {
    message = e;
  }
	console.log("Finished suggestSubscription ...");
  // Tracking
  trackingScheduleStatus({name: 'SuggestAskerSubscription', nextRunAt: SyncedCron.nextScheduledAtDate('suggestSubscription'), message});
};

  Meteor.methods({
    // Suggest Subscription
    suggestSubscription() {
      SyncedCron.add({
        name: 'suggestSubscription',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 9)).getHours();
          // Run at 9:30 AM every day
          return parser.cron(`0 30 ${hourInTimeZoneServer} 1/1 * ? *`, true);
        },
        job() {
          suggestSubscription();
        },
      });
	    console.log("Added suggestSubscription cron");
    },
    'runNow.suggestSubscription'() {
      suggestSubscription();
    },
  });
