package handlers

import (
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"time"

	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-email-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	modelEmailSending "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending"
	modelPaymentTransaction "gitlab.com/btaskee/go-services-model-v2/grpcmodel/paymentTransaction"
	modelSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/settings"
	modelSubscription "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscription"
	modelSubscriptionSetting "gitlab.com/btaskee/go-services-model-v2/grpcmodel/subscriptionSetting"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"gitlab.com/btaskee/go-services-model-v2/localization"
	"go.mongodb.org/mongo-driver/bson"
	"go.uber.org/zap"
)

func SendReceiptSubscriptionEmail(req *modelEmailSending.EmailRequest) (*modelEmailResponse.EmailResponse, error) {
	// Check server config
	resErr := CheckEmailConfig()
	if resErr != nil {
		local.Logger.Warn(resErr.Message)
		return resErr, nil
	}

	if req.SubscriptionId == "" {
		local.Logger.Warn(lib.ERROR_SUBSCRIPTION_ID_REQUIRED,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_SUBSCRIPTION_ID_REQUIRED,
		}, nil
	}

	if req.UserId == "" {
		local.Logger.Warn(lib.ERROR_USER_ID_REQUIRED,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_USER_ID_REQUIRED,
		}, nil
	}

	user, _ := modelUser.GetOneById(local.ISO_CODE, req.UserId, bson.M{"emails": 1, "language": 1, "referralCode": 1, "name": 1})
	if user == nil || len(user.Emails) == 0 {
		local.Logger.Warn(lib.ERROR_USER_NOT_FOUND,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusNotFound,
			Message:    lib.ERROR_USER_NOT_FOUND,
		}, nil
	}

	if len(user.Emails) == 0 || !user.Emails[0].Verified {
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusOK,
		}, nil
	}

	var subscription *modelSubscription.Subscription
	globalDataAccess.GetOneById(globalCollection.COLLECTION_SUBSCRIPTION[local.ISO_CODE], req.SubscriptionId,
		bson.M{"serviceText": 1, "taskPlace": 1, "schedule": 1, "price": 1, "weekday": 1, "currency": 1, "isoCode": 1, "discountMoney": 1, "startDate": 1, "endDate": 1, "duration": 1, "orderId": 1, "address": 1, "phone": 1, "payment": 1},
		&subscription,
	)

	if subscription == nil {
		local.Logger.Warn(lib.ERROR_SUBSCRIPTION_NOT_FOUND,
			zap.Any("body", req),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusNotFound,
			Message:    lib.ERROR_SUBSCRIPTION_NOT_FOUND,
		}, nil
	}

	option, err := subscriptionReceiptEmail(user, subscription)
	if err != nil {
		local.Logger.Warn(lib.ERROR_CONTENT_NIL,
			zap.Error(err),
		)
		return &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusBadRequest,
			Message:    lib.ERROR_CONTENT_NIL,
		}, err
	}
	return SendEmail(option)
}

func subscriptionReceiptEmail(user *modelUser.Users, data *modelSubscription.Subscription) (*modelEmailSending.EmailSending, error) {
	var subscriptionSetting *modelSubscriptionSetting.SubscriptionSetting
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SUBSCRIPTION_SETTINGS[local.ISO_CODE], bson.M{}, bson.M{"limitCancelTask": 1, "cancelTaskFee": 1}, &subscriptionSetting)

	language := globalConstant.LANG_EN
	if user.Language != "" {
		language = user.Language
	}
	fomartTime := "03:04 PM"
	fomartDate := "01/02/2006"
	if language == globalConstant.LANG_VI {
		fomartTime = "15:04"
		fomartDate = "02/01/2006"
	}

	emailData := &model.EmailSubsRecepitData{}

	// startTime:= getsubs.Schedule[0]
	var startTime, endTime string
	if len(data.Schedule) > 0 {
		startTime = globalLib.ParseDateFromTimeStamp(data.Schedule[0], local.TimeZone).Format(fomartTime)
		endTime = globalLib.ParseDateFromTimeStamp(data.Schedule[0], local.TimeZone).Add(time.Duration(data.Duration) * time.Hour).Format(fomartTime)
	}

	startDate := globalLib.ParseDateFromTimeStamp(data.StartDate, local.TimeZone).Format(fomartDate)
	endDate := globalLib.ParseDateFromTimeStamp(data.EndDate, local.TimeZone).Format(fomartDate)
	totalPrice := data.Price - data.DiscountMoney

	var serviceText string
	if data != nil && data.ServiceText != nil {
		serviceText = data.ServiceText.En
		switch language {
		case globalConstant.LANG_VI:
			serviceText = data.ServiceText.Vi
		case globalConstant.LANG_KO:
			serviceText = data.ServiceText.Ko
		case globalConstant.LANG_TH:
			serviceText = data.ServiceText.Th
		}
	}

	currency := globalConstant.CURRENCY_VN
	if data.Currency != nil && data.Currency.Code != "" {
		currency = data.Currency.Code
	}

	Weekday := lib.GetDaysFromDateTimestamp(data.Schedule)
	sort.SliceStable(Weekday, func(i, j int) bool {
		return Weekday[i] < Weekday[j]
	})
	days := lib.GetIsoWeekDays(Weekday)
	days = lib.DaysByLang(days, language)
	emailData.Name = localization.T(language, "RECEIPT_SUBS_EMAIL_SUBS_NAME")
	emailData.Hello = localization.T(language, "RECEIPT_SUBS_EMAIL_WELCOME")
	emailData.Dear = localization.T(language, "EMAIL_HELLO")
	emailData.Thank = localization.T(language, "SUBSCRIPTION_EMAIL_CONTENT")
	emailData.OrderNumberTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_OEDER_NUMBER")
	emailData.AskerNameTitle = localization.T(language, "CANCEL_EMAIL_ASKER_NAME_TITLE")
	emailData.PaymentMethodTitle = localization.T(language, "CANCEL_EMAIL_PAYMENT_METHOD_TITLE")
	emailData.NumberSessionTitle = localization.T(language, "RECEIPT_SUBS_NUMBER_SESSION")
	emailData.SubsDetailTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_SUBS_DETAIL_TITLE")
	emailData.SubsPlaceTitle = localization.T(language, "CANCEL_EMAIL_TASK_PLACE_TITLE")
	emailData.SubsServiceTitle = localization.T(language, "CANCEL_EMAIL_SERVICE_TITLE")
	emailData.SubsBeginAtTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_START_TITLE")
	emailData.SubsDurationTitle = localization.T(language, "CANCEL_EMAIL_DURATION_TITLE")
	emailData.SubsDaysTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_DAYS_TITLE")
	emailData.SubsDateFromTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_DURATION_TITLE")
	emailData.SubsPackValueTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_PACK_VALUE")
	emailData.SubsBasePriceTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_COST_OF_TASK_TITLE")
	emailData.PaymentTotalText = localization.T(language, "RECEIPT_EMAIL_PAYMENT_TOTAL")
	emailData.TransCodeTitle = localization.T(language, "RECEIPT_SUBS_EMAIL_TRANS_CODE_TITLE")
	emailData.ReferralTitle = localization.T(language, "EMAIL_REFERRAL_TITLE")
	emailData.FollowBtaskeeAt = localization.T(language, "FOLLOW_BTASKEE_AT")
	emailData.SubsDuration = localization.T(language, "RECEIPT_SUBS_DURATION", data.Duration)

	var limitCancelTask int32 = 2
	if subscriptionSetting != nil {
		limitCancelTask = subscriptionSetting.LimitCancelTask
	}
	var startHours int32 = 12
	var endHours int32 = 6
	var cancelFee float64 = 0.5
	if subscriptionSetting != nil && subscriptionSetting.CancelTaskFee != nil {
		startHours = subscriptionSetting.CancelTaskFee.StartHours
		endHours = subscriptionSetting.CancelTaskFee.EndHours
		cancelFee = subscriptionSetting.CancelTaskFee.Fee
	}
	if cancelFee < 1 {
		cancelFee *= 100
	}
	emailData.SubscriptionIntroduce = localization.T(language, "SUBSCRIPTION_INTRODUCE")
	emailData.SubscriptionIntroduce1 = localization.T(language, "SUBSCRIPTION_INTRODUCE_1")
	emailData.SubscriptionIntroduce2 = localization.T(language, "SUBSCRIPTION_INTRODUCE_2", limitCancelTask)
	emailData.SubscriptionIntroduce3 = localization.T(language, "SUBSCRIPTION_INTRODUCE_3", startHours)
	emailData.SubscriptionIntroduce4 = localization.T(language, "SUBSCRIPTION_INTRODUCE_4", startHours, cancelFee)
	emailData.SubscriptionIntroduce5 = localization.T(language, "SUBSCRIPTION_INTRODUCE_5", endHours)
	emailData.SubscriptionRefundNoteTitle = localization.T(language, "SUBSCRIPTION_REFUND_NOTE_TITLE")
	emailData.SubscriptionRefundbPayNote = localization.T(language, "SUBSCRIPTION_REFUND_BPAY_NOTE")
	emailData.SubscriptionRefundMoneyNote = localization.T(language, "SUBSCRIPTION_REFUND_MONEY_NOTE")

	emailData.AskerName = user.Name
	emailData.PaymentMethod = globalLib.LocalizePaymentMethod(data.Payment.Method, language)
	emailData.NumberSession = fmt.Sprintf("%d", len(data.Schedule))
	emailData.SubsOrderId = data.OrderId
	emailData.SubsAddress = data.Address
	emailData.SubsName = serviceText
	emailData.SubsBeginAt = fmt.Sprintf("%s %s %s %s", localization.T(language, "FROM_TIME"), startTime, localization.T(language, "TO_TIME"), endTime)
	emailData.SubsDays = strings.Join(days, ", ")
	emailData.SubsDateFrom = fmt.Sprintf("%s %s %s %s", localization.T(language, "FROM_DATE"), startDate, localization.T(language, "TO_DATE"), endDate)
	if data.DiscountMoney > 0 {
		emailData.PromotionDiscount = fmt.Sprintf("-%s %s", globalLib.FormatMoney(data.DiscountMoney), currency)
	}
	emailData.SubsBasePrice = fmt.Sprintf("%s %s", globalLib.FormatMoney(data.Price), currency)
	emailData.TotalPrice = fmt.Sprintf("%s %s", globalLib.FormatMoney(totalPrice), currency)
	if data.Payment.Method == globalConstant.PAYMENT_METHOD_CARD {
		emailData.IsPaymentByCard = true
		emailData.PaymentCardNumberTitle = localization.T(language, "PAY_BY_CARD")
		emailData.PaymentCardNumber = data.Payment.CardNumber
		emailData.PaymentStatusTitle = localization.T(language, "CARD_PAYMENT_STATUS")
		emailData.PaymentStatus = localization.T(language, "CARD_PAYMENT_STATUS_SUCCESS")
	} else if data.Payment.Method == globalConstant.PAYMENT_METHOD_BANK_TRANSFER {
		emailData.IsPaymentByTransfer = true
		emailData.PaymentBankNameTitle = localization.T(language, "BANK_TRANSFER_METHOD")
		emailData.PaymentBankName = data.Payment.Bank
		emailData.PaymentStatusTitle = localization.T(language, "CARD_PAYMENT_STATUS")
		emailData.PaymentStatus = localization.T(language, "CARD_PAYMENT_STATUS_SUCCESS")
	}

	emailData.Referral = strings.ToUpper(user.ReferralCode)
	var settings *modelSetting.Settings
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_SETTING_SYSTEM[local.ISO_CODE], bson.M{}, bson.M{"referralSetting": 1}, &settings)
	value, currency := getReferralValueAndCurrency(data.IsoCode, currency, settings)
	emailData.ReferralText = localization.T(language, "EMAIL_REFERRAL_TEXT", globalLib.FormatMoney(value), currency, globalLib.FormatMoney(value), currency)
	var transactionCode string
	var paymentTran *modelPaymentTransaction.PaymentTransaction
	globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_PAYMENT_TRANSACTION,
		bson.M{"data.subscriptionId": data.XId, "charged": true},
		bson.M{"pspReference": 1},
		&paymentTran,
	)
	if paymentTran != nil {
		transactionCode = paymentTran.PspReference
	}
	emailData.SubsTransCode = transactionCode

	// convert to map to use function updateBtaskeeInfo
	emailDataJson, _ := json.Marshal(emailData)
	emailDataMap := make(map[string]interface{})
	json.Unmarshal(emailDataJson, &emailDataMap)
	emailDataMap = globalLib.UpdateBtaskeeInfoInEmailByIsoCode(emailDataMap, local.ISO_CODE)

	// Parse email template
	emailTempalte := fmt.Sprintf("%s/receiptSubscription.html", cfg.EmailTemplateURL)

	body, err := ParseTemplate(emailTempalte, emailDataMap)
	if err != nil {
		return nil, err
	}

	// NOTE: Test with webhook-service "./test.sh 8 4"
	return &modelEmailSending.EmailSending{
		From:    "bTaskee Receipts <<EMAIL>>",
		To:      user.Emails[0].Address,
		Bcc:     []string{"<EMAIL>"},
		ReplyTo: "<EMAIL>",
		Subject: localization.T(language, "SUBSCRIPTION_EMAIL_SUBJECT"),
		Content: body,
	}, nil
}
