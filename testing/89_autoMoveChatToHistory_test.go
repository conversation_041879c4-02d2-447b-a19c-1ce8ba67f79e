package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http/httptest"
	"strconv"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	"go.mongodb.org/mongo-driver/bson"
)

func TestAutoMoveChatToHistory(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		apiURL := "/api/v3/sync-cron-vn/auto-move-chat-to-history"
		ResetData()

		CreateUser([]map[string]interface{}{
			{
				"phone": "0834567880",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
			},
			{
				"phone": "0834567890",
				"name":  "Asker 01",
				"type":  globalConstant.USER_TYPE_ASKER,
			},
		})

		now := globalLib.GetCurrentTime(local.TimeZone)
		chatConversations := []map[string]interface{}{}
		for i := 0; i < 1000; i++ {
			doneTaskAt := now.AddDate(0, 0, -i)
			chatConversation := map[string]interface{}{ // UPDATE STATUS and Send New CHAT
				"askerPhone":  "0834567890",
				"taskerPhone": "0834567880",
				"askerName":   "Asker 01",
				"taskerName":  "Tasker 01",
				"doneTaskAt":  doneTaskAt,
				"messages": []map[string]interface{}{
					{
						"_id":     globalLib.GenerateObjectId(),
						"from":    "ASKER",
						"userId":  "0834567890",
						"isRead":  false,
						"message": "ABC" + strconv.Itoa(i),
					},
				},
			}
			chatConversations = append(chatConversations, chatConversation)
		}
		CreateChatConversation(chatConversations)
		body := map[string]interface{}{
			"action":  lib.RUN_NOW,
			"isoCode": local.ISO_CODE,
		}
		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("When give a http request to %s", apiURL), t, func() {
			req := httptest.NewRequest("POST", apiURL, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()

			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]string{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)
					countHCM, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_HISTORY_CHAT_MESSAGES[local.ISO_CODE], bson.M{})
					So(countHCM, ShouldEqual, 993)
					countHCCV, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_HISTORY_CHAT_CONVERSATIONS[local.ISO_CODE], bson.M{})
					So(countHCCV, ShouldEqual, 993)
					countCM, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_CHAT_MESSAGE[local.ISO_CODE], bson.M{})
					// So(countCM, ShouldEqual, 7)
					So(countCM, ShouldEqual, 1000)
					countCCV, _ := globalDataAccess.CountByQuery(globalCollection.COLLECTION_CHAT_CONVERSATION[local.ISO_CODE], bson.M{})
					// So(countCCV, ShouldEqual, 7)
					So(countCCV, ShouldEqual, 1000)
					existOldChat, _ := globalDataAccess.IsExistByQuery(globalCollection.COLLECTION_CHAT_CONVERSATION[local.ISO_CODE], bson.M{"doneTaskAt": bson.M{"$lte": now.AddDate(0, 0, -7)}})
					// So(existOldChat, ShouldEqual, false)
					So(existOldChat, ShouldEqual, true)
				})
			})
		})
	})
}
