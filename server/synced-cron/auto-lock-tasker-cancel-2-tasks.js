import { Meteor } from "meteor/meteor";
import moment from "moment";
import _ from 'lodash';
import {
  trackingScheduleStatus,
  sendNotificationByIds,
  postToSlack,
} from "/server/helper-function";


// Find the Tasker cancelled at least 2 tasks / 7 days
const getTaskerNeedToLock = () => {
  const minDate = moment().subtract(7, 'days').toDate();
  return Meteor.appCollection.users.aggregate([
      {
        $match: {
          type: "TASKER",
          status: "ACTIVE",
          "taskCancelByTasker.cancelDate": {
            $gte: minDate
          },
          isoCode: "VN",
        }
      },
      {
        $project: {
          _id: "$_id",
          phone: "$phone",
          name: "$name",
          workingPlaces: "$workingPlaces",
          lastActiveAt: "$lastActiveAt",
          lastLockedAt: "$lastLockedAt",
          notComeLockNumber: "$notComeLockNumber",
          taskCancelByTasker: {
            $filter: {
              input: "$taskCancelByTasker",
              as: "taskCancelByTasker",
              cond: {
                $gte: [
                  "$$taskCancelByTasker.cancelDate",
                  minDate
                ]
              }
            }
          }
        }
      }
    ])
    .map(tasker => tasker)
    .filter(tasker => tasker.taskCancelByTasker.length >= 2);
};

const runLockTasker = () => {
  return;
	console.log("Start runLockTasker ...");
  var message = null;
  try {
    const taskers = getTaskerNeedToLock();
    taskers.forEach(tasker => {
      lockATasker(tasker);
    })
  } catch (e) {
    message = e;
  }
	console.log("Finished runLockTasker ...");
  // Tracking
  trackingScheduleStatus({
    name: "autoLockTaskerCancel2Task",
    nextRunAt: SyncedCron.nextScheduledAtDate("autoLockTaskerCancel2Task"),
    message
  });
};

const getReactiveDays = (tasker) => {
  const setting = Meteor.appCollection.SettingSystem.findOne({}, {fields: {autoLockTaskerNotCome: 1}});
  const lockDays = _.get(setting, 'autoLockTaskerNotCome', {
    firstLockDays: 1,
    secondLockDays: 3,
  });
  switch (tasker.notComeLockNumber) {
    case 1: return lockDays.firstLockDays;
    case 2: return lockDays.secondLockDays;
    default: return null;
  }
};

const sendLockingNotification = (tasker, reactiveDays) => {
  // Title of notification
  const title = {
    vi: 'Tài khoản của bạn đã bị khóa',
    en: 'Your account has been blocked',
    ko: 'Your account has been blocked',
    th: 'Your account has been blocked',
  };
  // Default text of notification
  let text = {
    vi: `Bạn đã hủy việc 2 lần trong vòng 7 ngày. Tài khoản của bạn bị tạm khóa không thời hạn, vui lòng liên hệ bTaskee trong giờ hành chính để được hỗ trợ.`,
    en: `Your account will be blocked for not coming twice this week. Please contact bTaskee in office hours.`,
    ko: `Your account will be blocked for not coming twice this week. Please contact bTaskee in office hours.`,
    th: `Your account will be blocked for not coming twice this week. Please contact bTaskee in office hours.`,
  };
  // Text with reactiveDays
  if ( reactiveDays && reactiveDays > 0 ) {
    text = {
      vi: `Bạn đã hủy việc 2 lần trong vòng 7 ngày. Tài khoản của bạn bị tạm khóa trong vòng ${reactiveDays} ngày. Hết thời gian khóa hệ thống tự mở lại`,
      en: `Your account will be blocked in ${reactiveDays} days for not coming twice this week.`,
      ko: `Your account will be blocked in ${reactiveDays} days for not coming twice this week.`,
      th: `Your account will be blocked in ${reactiveDays} days for not coming twice this week.`,
    };
  }
  sendNotificationByIds([tasker._id], {
    title,
    text,
    payload: {},
  }, {
    isForceView: true,
    type: 30,
  });
};

/** Save locking history and send notification to Tasker
 * 
 * @param {} tasker 
 */
const lockingHistoryAndNotification = (tasker) => {
  let lockDate = moment();
  const reactiveDays = getReactiveDays(tasker);
  const lockHistoryData = {
    taskerId: tasker._id,
    createdAt: lockDate.toDate(),
    type: 'LOCK',
    status: 'LOCKED',
    cancelSession: 2,
    notComeLockNumber: tasker.notComeLockNumber
  };
  if ( reactiveDays ) {
    lockHistoryData.reactiveDate = lockDate.add(reactiveDays, 'days').toDate();
  }
  Meteor.appCollection.NotComeLockHistory.insert(lockHistoryData);
  sendLockingNotification(tasker, reactiveDays);
};

const lockATasker = (tasker) => {
  try {
    if ( tasker.lastActiveAt ) {
      const lastCancelDate = _.get(tasker, `taskCancelByTasker[${tasker.taskCancelByTasker.length - 1}].cancelDate`, null);
      if ( lastCancelDate && moment(lastCancelDate).isBefore(moment(tasker.lastActiveAt)) ) {
        return;
      }
    }
  
    tasker.notComeLockNumber = tasker.notComeLockNumber || 0; // Init undefined notComeLockNumber
  
    // Change Tasker status
  
    Meteor.appCollection.users.update({
      _id: tasker._id
    }, {
      $set: {
        status: 'LOCKED',
        updatedAt: new Date(),
        lastLockedAt: new Date(),
      },
      $inc: {
        notComeLockNumber: 1
      }
    });
    tasker.notComeLockNumber += 1;
  
    // Save user action history
  
    const taskerCity = tasker.workingPlaces && tasker.workingPlaces.length > 0 ? tasker.workingPlaces[0].city : '';
    Meteor.appCollection.UserActionHistory.insert({
      phone: tasker.phone,
      name: tasker.name,
      action: 'BLOCK_TASKER',
      data: {
        taskerId: tasker._id,
        reason: 'Hủy việc 2 lần trong 7 ngày.',
        city: taskerCity,
        supporter: 'SYSTEM',
      },
      createdAt: new Date(),
    });
  
    // Save lock history
    // Send notification
  
    lockingHistoryAndNotification(tasker);
  
	  //TODO: fix msg
    postToSlack({
      channel: 'tasker-blocked',
      text: `[ĐÃ KHÓA] Tasker hủy việc >= 2 lần trong 7 ngày. ${taskerCity} - ${tasker.phone} - ${tasker.name}. Khóa lần thứ ${tasker.notComeLockNumber}. Theo dõi tại https://sp.btaskee.com/not-come-lock-history`,
    });
  } catch (e) {
    console.log('================lockATasker====================');
    console.log(e);
    console.log('====================================');
  }
}

  Meteor.methods({
    autoLockTaskerCancel2Task() {
      SyncedCron.add({
        name: "autoLockTaskerCancel2Task",
        schedule(parser) {
          // Run every 2 hours
          return parser.cron(`0 0 0/2 1/1 * ? *`, true);
        },
        job() {
          runLockTasker();
        }
      });
	    console.log("Added autoLockTaskerCancel2Task cron");
    },
    "runNow.autoLockTaskerCancel2Task"() {
      runLockTasker();
    }
  });
