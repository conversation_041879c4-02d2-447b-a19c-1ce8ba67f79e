package autoUpgradeMedalForCommunityUser

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelCommunityUsers "gitlab.com/btaskee/go-services-model-v2/grpcmodel/communityUser"
	"go.mongodb.org/mongo-driver/bson"
)

func GetCommunityUserInfo() (users []*modelCommunityUsers.CommunityUser) {
	query := bson.M{
		"status": globalConstant.COMMUNITY_USER_STATUS_ACTIVE,
	}
	field := bson.M{
		"numberOfLikes":  1,
		"numberOfPosts":  1,
		"numberOfShares": 1,
		"_id":            1,
		"avatar":         1,
	}
	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_COMMUNITY_USER[local.ISO_CODE], query, field, &users)
	if err != nil {
		textErr := fmt.Sprintf("[Sync Cron VN v3] AutoUpgradeMedalForCommunityUser %s: can not get user community , err: %s ", local.ISO_CODE, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], textErr)
		return nil

	}
	return
}
