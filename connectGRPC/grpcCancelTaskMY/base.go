package grpcCancelTaskMY

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcCancelTaskMY"
	"google.golang.org/grpc"
)

func ConnectGRPCCancelTask(endpoint string) (grpcCancelTaskMY.CancelTaskClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcCancelTaskMY.NewCancelTaskClient(connect)
	return client, connect, err
}
