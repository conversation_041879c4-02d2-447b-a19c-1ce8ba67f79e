/*
  @Description: Synced cron auto calculate Asker / Tasker avg rating in week
    base on rating, askerRating were created in week
  @Author: ToanPP
*/

import { Meteor } from 'meteor/meteor';
import moment from 'moment';
import _ from 'lodash';
import {
  revertTimeZone,
  trackingScheduleStatus,
} from '/server/helper-function';
import { Setting } from '/server/lib/collections';

const calculateTaskerAvgRating = (taskerId) => {
  let count = 0;
  const ratings = Meteor.appCollection.Rating.find({taskerId}, {fields: {rate: 1}, sort: {createdAt: -1}, limit: 100});
  if (ratings && ratings.count() > 0) {
    // Calculate avg rating
    var sum = 0;
    ratings.forEach((rating) => {
      count += 1;
      sum += rating.rate;
    });
    if (count > 100) {
      console.log('Wrong rating count' + count);
      count = 100;
    }
    return sum / count;
  }
  // Default rating is 5
  return 5;
};

const calculateAskerAvgRating = ( askerId ) => {
  var avg = 0;
  const ratings = Meteor.appCollection.AskerRating.find({askerId}, {fields: {rate: 1}, sort: {createdAt: -1}, litmit: 100});
  if (ratings && ratings.count() > 0) {
    // Calculate avg rating
    var sum = 0;
    ratings.forEach((rating) => {
      sum += rating.rate;
    });
    return sum / ratings.count();
  }
  return avg;
};

const calAVGRatingTasker = () => {
  const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 3)).getHours();
  let fromDate = moment().startOf('week').hour(hourInTimeZoneServer).toDate(); // Result of startOf('week') is Sunday
  const setting = Setting.findOne({});
  const cronSetting = _.get(setting, 'autoCalculateAVGRating', null);
  if ( cronSetting && cronSetting.daily && cronSetting.everyDays ) {
    fromDate = moment().subtract(cronSetting.everyDays, 'days').startOf('day').hour(hourInTimeZoneServer).toDate();
  }
  const distinct = Meteor.wrapAsync(Meteor.appCollection.Rating.rawCollection().distinct, Meteor.appCollection.Rating.rawCollection());
  const taskerIds = distinct('taskerId', {
    createdAt: {$gte: fromDate}
  });
  taskerIds.forEach((taskerId) => {
    const avgRating = calculateTaskerAvgRating(taskerId);
    if ( avgRating > 0 && avgRating <= 5 ) {
      Meteor.appCollection.users.update({
        _id: taskerId,
      }, {
        $set: {
          avgRating: avgRating,
        }
      });
    }
  });

};

const calAVGRatingAsker = () => {
  const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 3)).getHours();
  let fromDate = moment().startOf('week').hour(hourInTimeZoneServer).toDate(); // Result of startOf('week') is Sunday
  const setting = Setting.findOne({});
  const cronSetting = _.get(setting, 'autoCalculateAVGRating', null);
  if ( cronSetting && cronSetting.daily && cronSetting.everyDays ) {
    fromDate = moment().subtract(cronSetting.everyDays, 'days').startOf('day').hour(hourInTimeZoneServer).toDate();
  }
  const distinct = Meteor.wrapAsync(Meteor.appCollection.AskerRating.rawCollection().distinct, Meteor.appCollection.AskerRating.rawCollection());
  const askerIds = distinct('askerId', {
    createdAt: {$gte: fromDate},
    taskerId: {$ne: 'DEFAULT_RATING'},
  });
  askerIds.forEach((askerId) => {
    const avgRating = calculateAskerAvgRating(askerId);
    if ( avgRating > 0 && avgRating <= 5 ) {
      Meteor.appCollection.users.update({
        _id: askerId,
      }, {
        $set: {
          avgRating: avgRating,
        }
      });
    }
  });
};

const calculateAVGRating = () => {
  return;
	console.log("Start calculateAVGRating ...");
  var message = null;
  try {
    calAVGRatingTasker();
    calAVGRatingAsker();
  } catch (ex) {
    message = ex;
  }

	console.log("Finished calculateAVGRating ");
  // Tracking
  trackingScheduleStatus({name: 'autoCalculateAVGRating', nextRunAt: SyncedCron.nextScheduledAtDate('autoCalculateAVGRating'), message});
};

  Meteor.methods({
    autoCalculateAVGRating() {
      const setting = Setting.findOne({});
      const cronSetting = _.get(setting, 'autoCalculateAVGRating', null);
      SyncedCron.add({
        name: 'autoCalculateAVGRating',
        schedule(parser) {
          const hourInTimeZoneServer = revertTimeZone(new Date(2016, 1, 1, 3)).getHours();
          if ( cronSetting && cronSetting.daily && cronSetting.everyDays > 0 ) {
            // Run at 3:30 AM every setting days
            return parser.cron(`0 30 ${hourInTimeZoneServer} 1/${cronSetting.everyDays} * ? *`, true);
          }
          // Run at 3:30 AM Sunday weekly
          return parser.cron(`0 30 ${hourInTimeZoneServer} ? * SUN *`, true);
        },
        job() {
          calculateAVGRating();
        },
      });
	    console.log("Added autoCalculateAVGRating cron");
    },
    'runNow.autoCalculateAVGRating'() {
      calculateAVGRating();
    },
  });
