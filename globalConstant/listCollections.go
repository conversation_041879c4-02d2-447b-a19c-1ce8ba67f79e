package globalConstant

// const (
// COLLECTION_INCENTIVE                          = "incentive"
// COLLECTION_WORKINGPLACES                      = "workingPlaces"
// COLLECTION_WEEKLYPAYOUT = "weeklyPayout"
// COLLECTION_USERS = "users"
// COLLECTION_CARD_PAYMENT_CONFIG                = "cardPaymentConfig"
// COLLECTION_FATRANSACTION = "FATransaction"
// COLLECTION_FINANCIAL_ACCOUNT = "financialAccount"
// COLLECTION_USER_LOCATION_HISTORY              = "userLocationHistory"
// COLLECTION_GIFT                               = "gift"
// COLLECTION_HOUSEKEEPING                       = "hostel"
// COLLECTION_USER_ACTIVATION     = "userActivation"
// COLLECTION_USER_ACTION_HISTORY = "userActionHistory"
// COLLECTION_MARKETING_CAMPAIGN                 = "marketingCampaign"
// COLLECTION_NOTIFICATION = "notification"
// COLLECTION_PAYMENT_CARD                       = "paymentCard"
// COLLECTION_PAYMENT_TRANSACTION = "paymentTransaction"
// COLLECTION_POINT_TRANSACTION                  = "pointTransaction"
// COLLECTION_PROMOTION_CODE                     = "promotionCode"
// COLLECTION_PROMOTION_HISTORY                  = "promotionHistory"
// COLLECTION_RATING     = "rating"
// COLLECTION_SERVICE    = "service"
// COLLECTION_TH_SERVICE = "th_service"
// COLLECTION_TASK       = "task"
// COLLECTION_SUBSCRIPTION_REQUEST               = "subscriptionRequest"
// COLLECTION_TASKER_TASK_HISTORY  = "taskerTaskHistory"
// COLLECTION_PURCHASE_ORDER       = "purchaseOrder"
// COLLECTION_RAIX_PUSH_APP_TOKENS = "_raix_push_app_tokens"
// COLLECTION_TRANSACTION = "transaction"
// COLLECTION_REDEEM_GIFT_TRANSACTION            = "redeemGiftTransaction"
// COLLECTION_TH_TASKER_SETTINGS = "th_taskerSettings"
// COLLECTION_REGISTER_SERVICE                   = "registerService"
// COLLECTION_TH_SETTING_SYSTEM = "th_settingSystem"
// COLLECTION_REWARD = "reward"
// COLLECTION_SERVICE_CHANNEL   = "serviceChannel"
// COLLECTION_TASK_SCHEDULE = "taskSchedule"
// COLLECTION_TASK_REPORT_TASKER = "taskReportTasker"
// COLLECTION_SETTING_COUNTRY = "settingCountry"
// COLLECTION_SETTING_RATING                     = "settingRating"
// COLLECTION_SETTING_SYSTEM = "settingSystem"
// COLLECTION_SMS_VIET_GUYS  = "smsVietGuys"
// COLLECTION_SUBSCRIPTION             = "subscription"
// COLLECTION_SUBSCRIPTION_SETTINGS    = "subscriptionSettings"
// COLLECTION_TH_SUBSCRIPTION_SETTINGS = "th_subscriptionSettings"
// COLLECTION_TASKER_SETTINGS          = "taskerSettings"
// COLLECTION_TASKER_TRAINING          = "taskerTraining"
// COLLECTION_FEEDBACK                           = "feedback"
// COLLECTION_OUTSTANDING_PAYMENT = "outstandingPayment"
// COLLECTION_BANNED_REFERRAL_CODE               = "bannedReferralCode"
// COLLECTION_ASKER_RATING = "askerRating"
// COLLECTION_POINT_CAMPAIGN                     = "pointCampaign"
// COLLECTION_POINT_CAMPAIGN_HISTORY             = "pointCampaignHistory"
// COLLECTION_ASKER_BLACK_LIST_HISTORY           = "askerBlacklistHistory"
// COLLECTION_HISTORY_TASKS = "history_tasks"
// COLLECTION_MOMO_TRANSACTION = "momoTransaction"
// COLLECTION_REMOVED_PAYMENT_CARD               = "removedPaymentCard"
// COLLECTION_ZALOPAY_TRANSACTION = "zaloPayTransaction"
// COLLECTION_ASKER_SETTINGS    = "askerSetting"
// COLLECTION_TH_ASKER_SETTINGS = "th_askerSetting"
// COLLECTION_REPORT_TRANSACTION  = "reportTransaction"
// COLLECTION_PAYMENT_PROCESS = "paymentProcess"
// COLLECTION_NOT_COME_LOCK_HISTORY              = "notComeLockHistory"
// COLLECTION_TASKER_REFERRAL                    = "taskerReferral"
// COLLECTION_TRUST_POINT_HISTORY                = "trustPointHistory"
// COLLECTION_BEMPLOYEE      = "bEmployee"
// COLLECTION_TH_BEMPLOYEE   = "th_bEmployee"
// COLLECTION_TASKER_VIOLATE = "taskerViolate"
// COLLECTION_PAYMENT_2C2P_TRANSACTION   = "payment2C2PTransaction"
// COLLECTION_TH_FATRANSACTION          = "th_FATransaction"
// COLLECTION_TASKER_NOT_VIOLATE        = "taskerNotViolate"
// COLLECTION_STORE_GROCERY_ASSISTANT   = "storeGroceryAssistant"
// COLLECTION_PRODUCT_GROCERY_ASSISTANT = "productGroceryAssistant"
// COLLECTION_TH_TASK_SCHEDULE          = "th_taskSchedule"
// COLLECTION_TH_SUBSCRIPTION            = "th_subscription"
// COLLECTION_TH_PURCHASE_ORDER     = "th_purchaseOrder"
// COLLECTION_SHOPPING_CART = "shoppingCartGroceryAssistant"
// COLLECTION_WALLET_TRUE_MONEY     = "walletTrueMoney"
// COLLECTION_TH_REFUND_REQUEST = "th_refundRequest"
// COLLECTION_REFUND_REQUEST        = "refundRequest"
// COLLECTION_REFUND_TRANSACTION    = "refundTransaction"
// COLLECTION_TH_REFUND_TRANSACTION = "th_refundTransaction"
// COLLECTION_PROMOTION_PAYMENT_METHOD           = "promotionPaymentMethod"
// COLLECTION_VN_NEXT_EXPANSION = "vn_NextExpansion"
// COLLECTION_TH_NEXT_EXPANSION = "th_NextExpansion"
// COLLECTION_WORLD_NEXT_EXPANSION               = "world_NextExpansion"
// COLLECTION_TH_REWARD                = "th_reward"
// COLLECTION_TH_TASKER_NOT_VIOLATE = "th_taskerNotViolate"
// COLLECTION_TH_TASKER_VIOLATE        = "th_taskerViolate"
// COLLECTION_SHOPEEPAY_TRANSACTION    = "shopeePayTransaction"
// COLLECTION_VN_SHOPEEPAY_TRANSACTION = "vn_shopeePayTransaction"
// COLLECTION_GAME_CAMPAIGN            = "gameCampaign"
// COLLECTION_GAME_CAMPAIGN_HISTORY = "gameCampaignHistory"
// COLLECTION_LUCKY_DRAW                         = "luckyDraw"
// COLLECTION_LUCKY_DRAW_HISTORY                 = "luckyDrawHistory"
// COLLECTION_TASKER_YEAR_END_REPORT    = "taskerYearEndReport_"
// COLLECTION_TH_TASKER_YEAR_END_REPORT = "th_taskerYearEndReport_"
// COLLECTION_ASKER_YEAR_END_REPORT    = "askerYearEndReport_"
// COLLECTION_TH_ASKER_YEAR_END_REPORT = "th_askerYearEndReport_"
// COLLECTION_OPERATION                          = "operation"
// COLLECTION_TIKI_MINI_APP_TRANSACTION = "tikiMiniAppTransaction"
// COLLECTION_VN_PAY_TRANSACTION        = "vnPayTransaction"
// COLLECTION_SETTING_SYNC_CRON                  = "settingSyncCron"
// COLLECTION_QUIZ_FOR_TASKER_PREMIUM            = "quizForTaskerPremium"
// COLLECTION_QUIZ_FOR_TASKER_PREMIUM_HISTORY    = "quizForTaskerPremiumHistory"
// COLLECTION_TH_USER_COMPLAINT = "th_userComplaint"
// COLLECTION_USER_COMPLAINT    = "userComplaint"
// COLLECTION_COMPENSATION                       = "compensation"
// COLLECTION_USERS_DELETED = "users_deleted"
// COLLECTION_APP_USER_HISTORY                   = "appUserHistory"
// COLLECTION_TRAINING_TASKER                    = "trainingTasker"
// COLLECTION_TRAINING_TASKER_HISTORY = "trainingTaskerHistory"
// COLLECTION_QUIZZES_TRAINING_TASKER = "quizzesTrainingTasker"
// COLLECTION_TH_TRAINING_TASKER                 = "th_trainingTasker"
// COLLECTION_TH_TRAINING_TASKER_HISTORY         = "th_trainingTaskerHistory"
// COLLECTION_TH_QUIZZES_TRAINING_TASKER         = "th_quizzesTrainingTasker"
// COLLECTION_VN_COMBO_VOUCHER                   = "vn_comboVoucher"
// COLLECTION_VN_COMBO_VOUCHER_TRANSACTION       = "vn_comboVoucherTransaction"
// COLLECTION_INDO_SETTING_SYSTEM = "id_settingSystem"
// COLLECTION_VN_PAYOUT_HISTORY                  = "vn_payoutHistory"
// COLLECTION_TH_PAYOUT_HISTORY                  = "th_payoutHistory"
// COLLECTION_VN_TASKER_WORKING_HISTORY          = "vn_taskerWorkingHistory"
// COLLECTION_TH_TASKER_WORKING_HISTORY          = "th_taskerWorkingHistory"
// COLLECTION_TASKER_PROFILE                     = "taskerProfile"
// COLLECTION_TH_TASKER_PROFILE                  = "th_taskerProfile"
// COLLECTION_INDO_TASKER_PROFILE                = "id_taskerProfile"
// COLLECTION_VN_FINANCIAL_ACCOUNT_HISTORY       = "vn_financialAccountHistory"
// COLLECTION_TH_FINANCIAL_ACCOUNT_HISTORY       = "th_financialAccountHistory"
// COLLECTION_VN_ASKER_FINANCIAL_ACCOUNT_HISTORY = "vn_askerFinancialAccountHistory"
// COLLECTION_TH_ASKER_FINANCIAL_ACCOUNT_HISTORY = "th_askerFinancialAccountHistory"
// COLLECTION_TASKER_WORKED_FOR_ASKERS           = "taskerWorkedForAskers"
// COLLECTION_TH_COMBO_VOUCHER                  = "th_comboVoucher"
// COLLECTION_TH_COMBO_VOUCHER_TRANSACTION      = "th_comboVoucherTransaction"
// COLLECTION_VN_JOURNEY_SETTING                = "vn_journeySetting"
// COLLECTION_VN_JOURNEY_HISTORY      = "vn_journeyHistory"
// COLLECTION_VN_JOURNEY_LEADER_BOARD = "vn_journeyLeaderBoard"
// COLLECTION_TH_JOURNEY_SETTING                = "th_journeySetting"
// COLLECTION_TH_JOURNEY_HISTORY                = "th_journeyHistory"
// COLLECTION_TH_JOURNEY_LEADER_BOARD           = "th_journeyLeaderBoard"
// COLLECTION_VN_TASKER_INCENTIVE               = "vn_taskerIncentive"
// COLLECTION_VN_TASKER_GIFT                    = "vn_taskerGift"
// COLLECTION_VN_TASKER_REDEEM_GIFT_TRANSACTION = "vn_taskerRedeemGiftTransaction"
// COLLECTION_VN_TASKER_POINT_TRANSACTION = "vn_taskerPointTransaction"
// COLLECTION_VN_VAT_REQUEST = "vn_vatRequest"
// COLLECTION_TH_TASKER_INCENTIVE               = "th_taskerIncentive"
// COLLECTION_TH_TASKER_GIFT                    = "th_taskerGift"
// COLLECTION_TH_TASKER_REDEEM_GIFT_TRANSACTION = "th_taskerRedeemGiftTransaction"
// COLLECTION_TH_TASKER_POINT_TRANSACTION = "th_taskerPointTransaction"
// COLLECTION_TH_VAT_REQUEST = "th_vatRequest"
// COLLECTION_PARTNER_MINI_APP_SETTINGS         = "partnerMiniAppSettings"
// COLLECTION_SUPPORT_NOTIFICATION              = "supportNotification"
// COLLECTION_VN_ASKER_FLASH_SALE_INCENTIVE   = "vn_askerFlashSaleIncentive"
// COLLECTION_TH_ASKER_FLASH_SALE_INCENTIVE   = "th_askerFlashSaleIncentive"
// COLLECTION_VN_TRAINING_JOURNEY             = "vn_trainingJourney"
// COLLECTION_VN_TRAINING_JOURNEY_HISTORY = "vn_trainingJourneyHistory"
// COLLECTION_VN_QUIZZES_TRAINING_JOURNEY = "vn_quizzesTrainingJourney"
// COLLECTION_TH_TRAINING_JOURNEY             = "th_trainingJourney"
// COLLECTION_TH_TRAINING_JOURNEY_HISTORY     = "th_trainingJourneyHistory"
// COLLECTION_TH_QUIZZES_TRAINING_JOURNEY     = "th_quizzesTrainingJourney"
// COLLECTION_VN_ASKER_REFERRAL_CAMPAIGN      = "vn_askerReferralCampaign"
// COLLECTION_TH_ASKER_REFERRAL_CAMPAIGN      = "th_askerReferralCampaign"
// COLLECTION_VN_ASKER_REFERRAL_SETTING       = "vn_askerReferralSetting"
// COLLECTION_TH_ASKER_REFERRAL_SETTING       = "th_askerReferralSetting"
// COLLECTION_VN_TASKER_MARKETING_CAMPAIGN    = "vn_taskerMarketingCampaign"
// COLLECTION_TH_TASKER_MARKETING_CAMPAIGN    = "th_taskerMarketingCampaign"
// COLLECTION_VN_PAYMENT_TOOL_KIT_TRANSACTION = "vn_paymentToolKitTransaction"
// COLLECTION_VN_TASKER_BNPL_PROCESS          = "vn_taskerBNPLProcess"
// COLLECTION_VN_TASKER_BNPL_TRANSACTION = "vn_taskerBNPLTransaction"
// COLLECTION_TH_TASKER_BNPL_PROCESS          = "th_taskerBNPLProcess"
// COLLECTION_VN_SERVICE_GROUP                = "vn_serviceGroup"
// COLLECTION_TH_SERVICE_GROUP                = "th_serviceGroup"
// COLLECTION_ID_SERVICE_GROUP                = "id_serviceGroup"
// COLLECTION_VN_TASKER_GAME_CAMPAIGN         = "vn_taskerGameCampaign"
// COLLECTION_VN_TASKER_GAME_CAMPAIGN_HISTORY = "vn_taskerGameCampaignHistory"
// COLLECTION_VN_TASKER_USER_GAME_CAMPAIGN = "vn_taskerUserGameCampaign"
// COLLECTION_VN_PAYMENT_MB_TRANSACTION = "vn_paymentMBTransaction"
// COLLECTION_VN_THINGS_TO_KNOW     = "vn_thingsToKnow"
// COLLECTION_VN_CS_OUTBOUND_REPORT = "vn_csOutboundReport"
// COLLECTION_VN_SURVEY_SETTING    = "vn_surveySetting"
// COLLECTION_VN_SURVEY_REPORT = "vn_surveyReport"
// COLLECTION_TH_SURVEY_SETTING    = "th_surveySetting"
// COLLECTION_TH_SURVEY_REPORT     = "th_surveyReport"
// COLLECTION_HISTORY_CHAT_MESSAGE = "history_chatMessage"
// )
