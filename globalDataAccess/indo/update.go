package indo

import (
	"context"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcDataAccess"
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccess"
	modelDataAccess "gitlab.com/btaskee/go-services-model-v2/grpcmodel/dataAccess"
	"go.mongodb.org/mongo-driver/bson"
)

func UpdateOneById(collectionName string, id string, data interface{}) (int64, error) {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_ID[globalDataAccess.MODE])
	if err != nil {
		return 0, err
	}
	defer connect.Close()

	b, err := globalDataAccess.EncodeData(data)
	if err != nil {
		return 0, err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Id: id, Data: b}
	response, err := client.UpdateOneById(context.Background(), request)
	if err != nil {
		return 0, err
	}
	return response.MatchedCount, err
}

func UpdateOneByQuery(collectionName string, query interface{}, data interface{}) (int64, error) {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_ID[globalDataAccess.MODE])
	if err != nil {
		return 0, err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return 0, err
	}
	byteData, err := globalDataAccess.EncodeData(data)
	if err != nil {
		return 0, err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Data: byteData}
	response, err := client.UpdateOneByQuery(context.Background(), request)
	if err != nil {
		return 0, err
	}
	return response.MatchedCount, err
}

func UpdateAllByQuery(collectionName string, query interface{}, data interface{}) (int64, error) {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_ID[globalDataAccess.MODE])
	if err != nil {
		return 0, err
	}
	defer connect.Close()

	byteQuery, err := bson.MarshalWithRegistry(globalDataAccess.REGISTRY, query)
	if err != nil {
		return 0, err
	}
	byteData, err := globalDataAccess.EncodeData(data)
	if err != nil {
		return 0, err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Query: byteQuery, Data: byteData}
	response, err := client.UpdateAllByQuery(context.Background(), request)
	if err != nil {
		return 0, err
	}
	return response.MatchedCount, err
}
