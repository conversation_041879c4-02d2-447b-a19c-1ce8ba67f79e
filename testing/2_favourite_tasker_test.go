package testing

import (
	"context"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/local"
	svc "gitlab.com/btaskee/btaskee-go/go-send-task-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalRepo"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

/*
* <PERSON><PERSON> tả:
  - Gửi notification new task tới tasker:
  - Nằm trong user.FavouriteTaskers
  - Nằm trong serviceChannel.TaskerLists
  - Có status = ACTIVE
  - Có workingPlaces.city = task.TaskPlace.City
  - Không nằm trong task.BlackList
  - Nếu tasker.Company.CompanyId != "" -> Send tới company + tasker
  - Nếu asker không có favouriteTasker:
  - Nếu settings.UseDistanceToSendNotification == true -> Send to TopTasker
  - Nếu settings.UseDistanceToSendNotification == false -> Send Normal

* Kết quả:
  - Cập nhật task.Visibility = 1, viewedTaskers
*/
func TestFavouriteTasker(t *testing.T) {
	t.Run("1", func(t *testing.T) {
		ResetData()

		Convey("Validate request params if request body is nil", t, func() {
			var req *pushNotificationNewTask.NewTaskRequest
			s := svc.GRPCServer{}
			resp, _ := s.FavTasker(context.Background(), req)

			Convey("Check the response", func() {
				So(resp.StatusCode, ShouldEqual, 400)
				So(resp.Error.Code, ShouldEqual, lib.ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode)
			})
		})

		Convey("Validate request params if service is empty", t, func() {
			req := &pushNotificationNewTask.NewTaskRequest{
				Service: &pushNotificationNewTask.NewTaskRequestService{
					XId: "",
				}}
			s := svc.GRPCServer{}
			resp, _ := s.FavTasker(context.Background(), req)

			Convey("Check the response", func() {
				So(resp.StatusCode, ShouldEqual, 400)
				So(resp.Error.Code, ShouldEqual, lib.ERROR_SERVICE_ID_REQUIRED.ErrorCode)
			})
		})

		Convey("Validate request params if Booking.XId is empty", t, func() {
			req := &pushNotificationNewTask.NewTaskRequest{
				Service: &pushNotificationNewTask.NewTaskRequestService{
					XId: "1111",
				},
				Booking: &pushNotificationNewTask.NewTaskRequestBooking{
					XId: "",
				},
			}
			s := svc.GRPCServer{}
			resp, _ := s.FavTasker(context.Background(), req)

			Convey("Check the response", func() {
				So(resp.StatusCode, ShouldEqual, 400)
				So(resp.Error.Code, ShouldEqual, lib.ERROR_BOOKING_ID_REQUIRED.ErrorCode)
			})
		})

		Convey("Validate request params if Booking.IsoCode is empty", t, func() {
			req := &pushNotificationNewTask.NewTaskRequest{
				Service: &pushNotificationNewTask.NewTaskRequestService{
					XId: "1111",
				},
				Booking: &pushNotificationNewTask.NewTaskRequestBooking{
					XId:     "111",
					IsoCode: "",
				},
			}
			s := svc.GRPCServer{}
			resp, _ := s.FavTasker(context.Background(), req)

			Convey("Check the response", func() {
				So(resp.StatusCode, ShouldEqual, 400)
				So(resp.Error.Code, ShouldEqual, lib.ERROR_BOOKING_ISOCODE_REQUIRED.ErrorCode)
			})
		})
	})

	t.Run("2", func(t *testing.T) {
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":           "0981234567",
				"type":            globalConstant.USER_TYPE_ASKER,
				"name":            "Asker01",
				"favouriteTasker": []string{"0981234502", "0981234503"},
			},
			{
				"phone": "0981234500",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
			},
			{
				"phone": "0981234501",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
			},
			{
				"phone": "0981234502",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"company": map[string]interface{}{
					"companyId": "123123",
				},
			},
			{
				"phone": "0981234503",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker03",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  "VN",
					},
				},
			},
		})
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0981234567",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quan 1",
				},
			},
		})
		CreateTaskMetadata([]map[string]interface{}{
			{
				"_id":             taskIds[0],
				"excludedTaskers": []string{"0981234503"},
			},
		})
		var sv *modelService.Service
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"text.en": globalConstant.SERVICE_NAME_LAUNDRY}, bson.M{"_id": 1}, &sv)
		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": sv.XId}, bson.M{"$set": bson.M{"taskerList": []string{"0981234500", "0981234502"}}})

		req := &pushNotificationNewTask.NewTaskRequest{
			Service: &pushNotificationNewTask.NewTaskRequestService{
				XId: sv.XId,
			},
			Booking: &pushNotificationNewTask.NewTaskRequestBooking{
				XId:     taskIds[0],
				IsoCode: local.ISO_CODE,
			},
			FavouriteTasker: []string{
				"0981234502",
				"0981234503",
			},
		}

		Convey("When the service handle the request", t, func() {
			s := svc.GRPCServer{}
			_, err := s.FavTasker(context.Background(), req)

			Convey("Check the database", func() {
				So(err, ShouldBeNil)
				var task *modelTask.Task
				globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskIds[0], bson.M{}, &task)
				So(len(task.ViewedTaskers), ShouldEqual, 2)
				So(task.ViewedTaskers[0], ShouldBeIn, []string{"0981234502", "123123"})
				So(task.ViewedTaskers[1], ShouldBeIn, []string{"0981234502", "123123"})
			})
		})
	})

	// Push notification premium task
	t.Run("3", func(t *testing.T) {
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":           "0981234567",
				"type":            globalConstant.USER_TYPE_ASKER,
				"name":            "Asker01",
				"favouriteTasker": []string{"0981234502", "0981234503"},
			},
			{
				"phone": "0981234500",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
			},
			{
				"phone": "0981234501",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
			},
			{
				"phone": "0981234502",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"company": map[string]interface{}{
					"companyId": "123123",
				},
			},
			{
				"phone": "0981234503",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"isPremiumTasker": true,
			},
		})
		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0981234567",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quan 1",
				},
				"isPremium": true,
			},
		})
		var sv *modelService.Service
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"text.en": globalConstant.SERVICE_NAME_LAUNDRY}, bson.M{"_id": 1}, &sv)
		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": sv.XId}, bson.M{"$set": bson.M{"taskerList": []string{"0981234500", "0981234502", "0981234503"}}})

		req := &pushNotificationNewTask.NewTaskRequest{
			Service: &pushNotificationNewTask.NewTaskRequestService{
				XId: sv.XId,
			},
			Booking: &pushNotificationNewTask.NewTaskRequestBooking{
				XId:     taskIds[0],
				IsoCode: local.ISO_CODE,
			},
			FavouriteTasker: []string{
				"0981234502",
				"0981234503",
			},
		}

		Convey("When the service handle the request", t, func() {
			s := svc.GRPCServer{}
			_, err := s.FavTasker(context.Background(), req)

			Convey("Check the database", func() {
				So(err, ShouldBeNil)
				var task *modelTask.Task
				globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskIds[0], bson.M{}, &task)
				So(len(task.ViewedTaskers), ShouldEqual, 1)
				So(task.ViewedTaskers[0], ShouldBeIn, []string{"0981234503"})
			})
		})
	})

	// Push notification to tasker not in service channel of subscription but in normal service
	t.Run("4", func(t *testing.T) {
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":           "0981234567",
				"type":            globalConstant.USER_TYPE_ASKER,
				"name":            "Asker01",
				"favouriteTasker": []string{"0981234502"},
			},
			{
				"phone": "0981234500",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
			},
			{
				"phone": "0981234501",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
			},
			{
				"phone": "0981234502",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  local.ISO_CODE,
					},
				},
				"company": map[string]interface{}{
					"companyId": "123123",
				},
			},
		})
		taskIds := CreateTaskSubscription([]map[string]interface{}{
			{
				"askerPhone":  "0981234567",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"taskPlace": map[string]interface{}{
					"country":  local.ISO_CODE,
					"city":     "Hồ Chí Minh",
					"district": "Quan 1",
				},
			},
		})
		var sv *modelService.Service
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}, bson.M{"_id": 1}, &sv)
		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": sv.XId}, bson.M{"$set": bson.M{"taskerList": []string{"0981234500", "0981234502"}}})

		var svSub *modelService.Service
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"name": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING_SUBSCRIPTION}, bson.M{"_id": 1}, &svSub)
		req := &pushNotificationNewTask.NewTaskRequest{
			Service: &pushNotificationNewTask.NewTaskRequestService{
				XId: svSub.XId,
			},
			Booking: &pushNotificationNewTask.NewTaskRequestBooking{
				XId:     taskIds[0],
				IsoCode: local.ISO_CODE,
			},
			FavouriteTasker: []string{
				"0981234502",
			},
		}

		Convey("When the service handle the request", t, func() {
			s := svc.GRPCServer{}
			_, err := s.FavTasker(context.Background(), req)

			Convey("Check the database", func() {
				So(err, ShouldBeNil)
				var task *modelTask.Task
				globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskIds[0], bson.M{}, &task)
				So(len(task.ViewedTaskers), ShouldEqual, 2)
				So(task.ViewedTaskers[0], ShouldBeIn, []string{"0981234502", "123123"})
				So(task.ViewedTaskers[1], ShouldBeIn, []string{"0981234502", "123123"})
			})
		})
	})

	t.Run("5", func(t *testing.T) {
		ResetData()
		CreateUser([]map[string]interface{}{
			{
				"phone":           "0981234567",
				"type":            globalConstant.USER_TYPE_ASKER,
				"name":            "Asker01",
				"favouriteTasker": []string{"0981234501", "0981234502"},
			},
			{
				"phone": "0981234500",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  "VN",
					},
				},
			},
			{
				"phone": "0981234501",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker01",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  "VN",
					},
				},
			},
			{
				"phone": "0981234502",
				"type":  globalConstant.USER_TYPE_TASKER,
				"name":  "Tasker02",
				"workingPlaces": []map[string]interface{}{
					{
						"district": "Quan 1",
						"city":     "Hồ Chí Minh",
						"country":  "VN",
					},
				},
			},
		})

		CreateTaskerBNPLProcess([]map[string]interface{}{
			{
				"taskerId":        "0981234501",
				"amount":          800000,
				"remainingAmount": 600000,
				"status":          "PAYING",
				"moneyBNPLOnTask": 50000,
			},
		})

		taskIds := CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0981234567",
				"serviceName": globalConstant.SERVICE_NAME_LAUNDRY,
				"description": "Desc Test",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_POSTED,
				"taskPlace": map[string]interface{}{
					"country":  "VN",
					"city":     "Hồ Chí Minh",
					"district": "Quan 1",
				},
				"paymentMethod": "CASH",
			},
		})
		var sv *modelService.Service
		globalRepo.GetOneByQuery(globalCollection.COLLECTION_SERVICE[local.ISO_CODE], bson.M{"text.en": globalConstant.SERVICE_NAME_LAUNDRY}, bson.M{"_id": 1}, &sv)
		globalRepo.UpdateOneByQuery(globalCollection.COLLECTION_SERVICE_CHANNEL[local.ISO_CODE], bson.M{"serviceId": sv.XId}, bson.M{"$set": bson.M{"taskerList": []string{"0981234500", "0981234501", "0981234502"}}})

		req := &pushNotificationNewTask.NewTaskRequest{
			Service: &pushNotificationNewTask.NewTaskRequestService{
				XId: sv.XId,
			},
			Booking: &pushNotificationNewTask.NewTaskRequestBooking{
				XId:     taskIds[0],
				IsoCode: globalConstant.ISO_CODE_VN,
			},
		}

		Convey("When the service handle the request", t, func() {
			s := svc.GRPCServer{}
			_, err := s.FavTasker(context.Background(), req)

			Convey("Check the database", func() {
				So(err, ShouldBeNil)
				var task *modelTask.Task
				globalRepo.GetOneById(globalCollection.COLLECTION_TASK[local.ISO_CODE], taskIds[0], bson.M{}, &task)
				So(len(task.ViewedTaskers), ShouldEqual, 2)
				So(task.ViewedTaskers[0], ShouldBeIn, []string{"0981234502", "0981234501"}) // include BNPL tasker
				So(task.ChangesHistory[0].From, ShouldEqual, "SYSTEM")
				So(task.ChangesHistory[0].Key, ShouldEqual, "RESEND_FAV")
				So(task.ChangesHistory[0].CreatedAt, ShouldNotBeNil)
			})
		})
	})

}
