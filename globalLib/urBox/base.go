package urBox

import (
	"crypto"
	"crypto/rand"
	"crypto/rsa"
	"crypto/sha256"
	"crypto/x509"
	"encoding/base64"
	"encoding/json"
	"encoding/pem"
	"fmt"
	"sort"
)

// Function to parse the private key from a byte array
func parsePrivateKey(privateKeyBytes []byte) (*rsa.PrivateKey, error) {
	block, _ := pem.Decode(privateKeyBytes)
	if block == nil || block.Type != "RSA PRIVATE KEY" {
		return nil, fmt.Errorf("failed to decode PEM block containing private key")
	}
	privateKey, err := x509.ParsePKCS1PrivateKey(block.Bytes)
	if err != nil {
		return nil, err
	}
	return privateKey, nil
}

// Function to sort a map
func sortObject(data map[string]interface{}) map[string]interface{} {
	keys := make([]string, 0, len(data))
	for key := range data {
		keys = append(keys, key)
	}
	sort.Strings(keys)

	sorted := make(map[string]interface{})
	for _, key := range keys {
		sorted[key] = data[key]
	}
	return sorted
}

// Function to generate a signature
func GenerateUrBoxSignature(params map[string]interface{}, privateKeyByte []byte) (string, error) {
	privateKey, err := parsePrivateKey(privateKeyByte)
	if err != nil {
		return "", err
	}

	neededParams := map[string]interface{}{
		"app_id":         params["app_id"],
		"app_secret":     params["app_secret"],
		"campaign_code":  params["campaign_code"],
		"dataBuy":        params["dataBuy"],
		"isSendSms":      params["isSendSms"],
		"site_user_id":   params["site_user_id"],
		"transaction_id": params["transaction_id"],
	}

	sortedParams := sortObject(neededParams)
	encoded, err := json.Marshal(sortedParams)
	if err != nil {
		return "", err
	}

	hash := sha256.New()
	_, err = hash.Write(encoded)
	if err != nil {
		return "", err
	}
	hashed := hash.Sum(nil)

	signature, err := rsa.SignPKCS1v15(rand.Reader, privateKey, crypto.SHA256, hashed)
	if err != nil {
		return "", err
	}

	encodedSignature := base64.StdEncoding.EncodeToString(signature)
	return encodedSignature, nil
}
