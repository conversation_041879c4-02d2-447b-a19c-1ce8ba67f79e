package autoSendNotiToRemindTaskerResponseTaskForceTasker

import (
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	"go.mongodb.org/mongo-driver/bson"
)

func getTasks() ([]*modelTask.Task, error) {
	// 2. Get list all task need to be expired
	var tasks []*modelTask.Task
	fields := bson.M{
		"_id":                   1,
		"isPrepayTask":          1,
		"payment.status":        1,
		"payment.method":        1,
		"payment.transactionId": 1,
		"payment.isPayOff":      1,
		"askerId":               1,
		"date":                  1,
		"fromPartner":           1,
		"contactName":           1,
		"phone":                 1,
		"isoCode":               1,
		"costDetail.finalCost":  1,
		"subscriptionId":        1,
		"promotion.code":        1,
		"status":                1,
		"serviceText":           1,
		"originCurrency":        1,
		"currency":              1,
		"address":               1,
		"duration":              1,
		"promotion":             1,
		"serviceId":             1,
		"costDetail.cost":       1,
		"newCostDetail.cost":    1,
		"forceTasker":           1,
		"dateOptions":           1,
		"serviceName":           1,
	}
	fields = globalLib.MergeQuerySocketTask(fields)
	timeToNoti := globalLib.GetCurrentTime(local.TimeZone).Add(-1 * time.Hour)
	query := bson.M{
		"status":      globalConstant.TASK_STATUS_POSTED,
		"forceTasker": bson.M{"$exists": true},
		"createdAt":   bson.M{"$lte": timeToNoti},
	}
	err := globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
		query,
		fields,
		&tasks,
	)
	return tasks, err
}
