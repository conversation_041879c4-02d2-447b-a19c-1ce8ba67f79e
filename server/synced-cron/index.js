import generateTaskSchedule from './task-schedule.js';
//// import monthlyReward from './monthly-reward';
import autoDone from './auto-done';
//// import autoRate from './auto-rate';
import calculateScore from './calculate-score';
//import dailyNotification from './daily-notification';
//import estimateRushHour from './estimate-rush-hour';
import remindAskerConfirm from './remind-asker-confirm';
import calculateTaskerLevel from './calculate-tasker-level';
import generateSubscription from './subscription';
import suggestSubscription from './suggest-subscription';
//// import weeklyPayout from './weekly-payout';
// import weeklyReport from './weekly-report';
import autoDisableTaskerRating from './disable-tasker-rate-old-task';
import runAutoSetExpiredlTask from './auto-set-expired-task-posted-long-time';
import reactiveAskerAfterBlocked from './reactive-asker-after-blocked';
import autoVerifyDatabase from './auto-verify-database';
import updateTrustPoint from './trust-point';
import taskerReferrals from './tasker-referrals';
import autoUnlockTaskerNotCome from './auto-unlock-tasker-not-come';
//// import autoLockTaskerInactiveOneMonth from './auto-lock-tasker-inactive-one-month';
import autoCleanUpNotification from './auto-clean-up-notification';
//// import autoDisableAskerRating from './auto-disable-asker-rating-old-task';
import autoCalculateAVGRating from './auto-calculate-avg-rating';
//import autoSendPromotionNewAsker from './auto-send-promotion-new-asker';
//import autoRefundCardIntegrationFee from './auto-refund-card-integration-fee';
import autoAccumulatePoint from './auto-re-accumulate-point';
import autoRemindCardExpired from './auto-remind-card-expired';
import autoLockTaskerCancel2Tasks from './auto-lock-tasker-cancel-2-tasks';
//import autoSendNotificationQuickPostTask from './auto-send-notification-quick-post-task';
import autoCheckConfirmedTasks from './auto-check-confirmed-tasks';
import autoResetLocking from './auto-reset-not-come-locking';
import autoCheckIncentive from './auto-check-incentives';
//import autoRemoveOldRaixPushNotification from './auto-remove-raixpush-notification';
//// import autoLockTaskerLowAvgRating from './auto-lock-tasker-low-avg-rating';
import autoCheckTaskPrice from './auto-check-price-task';
import autoAlertPostedTask from './auto-alert-posted-task';
import runAutoCheckOrderZaloPay from './auto-check-order-zalo-pay';
import autoCheckInvalidTaskStatus from './auto-check-invalid-task-status';
import autoCheckTaskNeedSupport from './auto-check-task-need-support';
import autoRemindTaskerTopup from './auto-remind-tasker-top-up';
import autoCheckFinancialDaily from './auto-check-financial-daily';
//import autoDepositBEmployee from './auto-top-up-bTaskee-employee';
//import autoWithdrawBEmployee from './auto-withdraw-bTaskee-employee';
//import autoAlertScamming from './auto-alert-asker-scam';
import autoRateSubscriptionTask from './auto-rate-subscription-task';
import autoAlertLaundryNotCollect from './auto-alert-laundry-collecting';
//import autoSendLuckdyDraw from './auto-send-lucky-draw-xmas-event';
//
//export default function () {
//  generateTaskSchedule();
//  // monthlyReward();
//  autoDone();
//  // autoRate();
//  calculateScore();
//  dailyNotification();
//  estimateRushHour();
//  remindAskerConfirm();
//  calculateTaskerLevel();
//  generateSubscription();
//  suggestSubscription();
//  // weeklyPayout();
//  weeklyReport();
//  autoDisableTaskerRating();
//  runAutoSetExpiredlTask();
//  reactiveAskerAfterBlocked();
//  autoVerifyDatabase();
//  updateTrustPoint();
//  taskerReferrals();
//  autoUnlockTaskerNotCome();
//  // autoLockTaskerInactiveOneMonth();
//  autoCleanUpNotification();
//  // autoDisableAskerRating();
//  autoCalculateAVGRating();
//  autoSendPromotionNewAsker();
//  autoRefundCardIntegrationFee();
//  autoAccumulatePoint();
//  autoRemindCardExpired();
//  autoLockTaskerCancel2Tasks();
//  autoSendNotificationQuickPostTask();
//  autoCheckConfirmedTasks();
//  autoResetLocking();
//  autoCheckIncentive();
//  autoRemoveOldRaixPushNotification();
//  // autoLockTaskerLowAvgRating();
//  autoCheckTaskPrice();
//  autoAlertPostedTask();
//  runAutoCheckOrderZaloPay();
//  autoCheckInvalidTaskStatus();
//  autoCheckTaskNeedSupport();
//  autoRemindTaskerTopup();
//  autoCheckFinancialDaily();
//  autoDepositBEmployee();
//  autoWithdrawBEmployee();
//  autoAlertScamming();
//  autoRateSubscriptionTask();
//  autoAlertLaundryNotCollect();
//  autoSendLuckdyDraw();
//}
