package businessAllocation

import (
	"fmt"
	"log"
	"time"

	"github.com/robfig/cron/v3"
	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelBusiness "gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	modelBusinessLevel "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessLevel"
	modelBusinessMember "gitlab.com/btaskee/go-services-model-v2/grpcmodel/businessMember"
	"go.mongodb.org/mongo-driver/bson"
)

var c *cron.Cron
var cfg = config.GetConfig()
var isRunning = false
var runAt string
var CHUNK_SIZE_BUSINESS int64 = 100

/*
1. Lấy danh sách Business need topup
2. Lấy danh sách nhân viên Company và số tiền thưởng
3. Cộng tiền thưởng cho nhân viên và trừ tiền công ty
*/
func init() {
	var cronConfig map[string]interface{}
	if cfg != nil {
		cronConfig = cfg.ScheduleSyncCron["businessallocation"]
	}

	// Check if params enough
	if cronConfig["run_at"] == nil {
		lib.PostToSlackNotStart("BusinessAllocation")
		return
	}

	// Get runAt string for sync cron
	runAt = fmt.Sprintf(cronConfig["run_at"].(string))

	// Check if config has is_run = true
	if cronConfig["is_run"] == nil || !cronConfig["is_run"].(bool) {
		lib.PostToSlackNotStart("BusinessTopUpbPay")
		return
	}

	StartCron()
}

func StartCron() {
	c := cron.New(cron.WithLocation(local.TimeZone))
	c.AddFunc(runAt, main)
	c.Start()
	isRunning = true
}

func Run(reqBody *model.RequestAction) {
	// action == 1 -> Run sync cron once now
	if reqBody.Action == lib.RUN_NOW {
		main()
	}
	// action == 2 -> Start sync cron with schedule config
	if reqBody.Action == lib.START && !isRunning {
		StartCron()
	}
	// action == 3 -> Stop running sync cron
	if reqBody.Action == lib.STOP && isRunning {
		c.Stop()
		c = nil
		isRunning = false
	}
}

func main() {
	log.Println("Start BusinessAllocation Process")
	defer log.Println("Finish BusinessAllocation Process")
	// Get business
	business := getBusinessByChunk()
	if len(business) == 0 {
		return
	}
	currentTime := globalLib.GetCurrentTime(local.TimeZone)
	for _, b := range business {
		if b.GetRevokeSetting().GetStatus() == globalConstant.BUSINESS_STATUS_ACTIVE {
			nextTime := globalLib.ParseDateFromTimeStamp(b.GetRevokeSetting().GetNextTime(), local.TimeZone)
			if currentTime.After(nextTime) {
				// revoke
				revokeMembers(b)
			}
		}
		if b.GetTopUpSetting().GetStatus() == globalConstant.BUSINESS_STATUS_ACTIVE {
			nextTime := globalLib.ParseDateFromTimeStamp(b.GetTopUpSetting().GetNextTime(), local.TimeZone)
			if nextTime.Before(currentTime.AddDate(0, 0, 10)) && nextTime.After(currentTime.AddDate(0, 0, 1)) {
				if checkMoneyNeedTopup(b) < 0 {
					sendNotiWarningTopup(b)
				}
			}
			if currentTime.After(nextTime) {
				topUpbPayToMembers(b)
			}
		}
	}
}

func getNextTimeForUpdate(currentTime time.Time, dayInMonth string, period int) time.Time {
	var nextTimeUpdate time.Time
	switch dayInMonth {
	case "FIRST_DAY_IN_MONTH":
		nextTimeUpdate = time.Date(currentTime.Year(), currentTime.Month(), 1, 0, 0, 0, 0, local.TimeZone).AddDate(0, period, 0)
	case "LAST_DAY_IN_MONTH":
		nextTimeUpdate = time.Date(currentTime.Year(), currentTime.Month(), 1, 0, 0, 0, 0, local.TimeZone).AddDate(0, period+1, -1)
	default:
		d := cast.ToInt(dayInMonth)
		nextTimeUpdate = time.Date(currentTime.Year(), currentTime.Month(), d, 0, 0, 0, 0, local.TimeZone).AddDate(0, period, 0)
	}
	return nextTimeUpdate
}

func getMembersByBusinessId(businessId string) []*modelBusinessMember.BusinessMember {
	// Get member
	members, _ := modelBusinessMember.GetAll(local.ISO_CODE, bson.M{"businessId": businessId}, bson.M{"_id": 1, "bPay": 1})
	return members
}

func getLevelInfo(businessId string) []*modelBusinessLevel.BusinessLevel {
	// Get level info
	levelInfos, _ := modelBusinessLevel.GetAll(local.ISO_CODE, bson.M{"businessId": businessId, "status": globalConstant.BUSINESS_LEVEL_STATUS_ACTIVE}, bson.M{"_id": 1, "name": 1, "amount": 1})
	if len(levelInfos) == 0 {
		return nil
	}
	return levelInfos
}

func getMembersByLevel(levelIds []string) map[string][]*modelBusinessMember.BusinessMember {
	// Get members by level
	members, _ := modelBusinessMember.GetAll(local.ISO_CODE, bson.M{"levelId": bson.M{"$in": levelIds}, "status": globalConstant.BUSINESS_MEMBER_STATUS_ACTIVE}, bson.M{"_id": 1, "levelId": 1})
	// return membersData, nil
	mapMembersByLevelId := map[string][]*modelBusinessMember.BusinessMember{}
	for _, v := range members {
		if _, ok := mapMembersByLevelId[v.LevelId]; !ok {
			mapMembersByLevelId[v.LevelId] = []*modelBusinessMember.BusinessMember{}
		}
		mapMembersByLevelId[v.LevelId] = append(mapMembersByLevelId[v.LevelId], v)
	}

	return mapMembersByLevelId
}

func getBusinessByChunk() []*modelBusiness.Business {
	var result []*modelBusiness.Business
	var page int64 = 1
	var limit int64 = CHUNK_SIZE_BUSINESS
	for {
		var chunk []*modelBusiness.Business
		err := globalDataAccess.GetAllByQueryPaging(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE], bson.M{"status": globalConstant.BUSINESS_STATUS_ACTIVE}, bson.M{"topUpSetting": 1, "revokeSetting": 1, "bPay": 1}, page, limit, &chunk)

		if err != nil {
			return nil
		}
		if len(chunk) == 0 {
			break
		}
		result = append(result, chunk...)
		page++
	}

	return result
}
