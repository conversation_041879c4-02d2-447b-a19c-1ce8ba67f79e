package globalMiddleware

import (
	"bytes"
	"encoding/json"
	"io"
	"net/http"
	"strings"

	"github.com/golang-jwt/jwt/v5"
)

var MAP_PATH_PREFIX_TO_FIELD = map[string]string{
	"/api/v5/booking/":      "askerId",
	"/api/v5/booking-th/":   "askerId",
	"/api/v5/booking-indo/": "askerId",
	"/api/v5/booking-my/":   "askerId",
}

var MAP_PATH_TO_FIELD = map[string]string{
	"/api/v3/api-asker-vn/get-tasker-reviews":   "askerId",
	"/api/v3/api-asker-th/get-tasker-reviews":   "askerId",
	"/api/v3/api-asker-indo/get-tasker-reviews": "askerId",
	"/api/v3/api-asker-my/get-tasker-reviews":   "askerId",
}

func getFieldFromPath(path string) string {
	// Check exact match
	if vField, ok := MAP_PATH_TO_FIELD[path]; ok {
		return vField
	}

	// Check prefix match
	for vPrefix, vField := range MAP_PATH_PREFIX_TO_FIELD {
		if strings.HasPrefix(path, vPrefix) {
			return vField
		}
	}

	// Default field
	return "userId"
}

// JWTMiddleware wraps an HTTP handler to extract userId from JWT and add it to request body params.
func HTTPJWTMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		tokenString := r.Header.Get("Authorization")
		if tokenString == "" {
			next.ServeHTTP(w, r)
			return
		}

		// Remove 'Bearer ' prefix if present
		tokenString = strings.TrimPrefix(tokenString, "Bearer ")
		token, _ := jwt.Parse(tokenString, func(token *jwt.Token) (interface{}, error) {
			return nil, nil
		})

		claims, ok := token.Claims.(jwt.MapClaims)
		if !ok {
			next.ServeHTTP(w, r)
			return
		}

		userId, ok := claims["uid"].(string)
		if !ok {
			next.ServeHTTP(w, r)
			return
		}

		// Add userId to request body if it exists
		if r.Body != nil {
			var body map[string]interface{}
			err := json.NewDecoder(r.Body).Decode(&body)
			if err == nil {
				userIdFieldName := getFieldFromPath(r.URL.Path)
				body[userIdFieldName] = userId
				jsonBody, err := json.Marshal(body)
				if err == nil {
					r.Body = io.NopCloser(bytes.NewBuffer(jsonBody))
				}
			}
		}

		next.ServeHTTP(w, r)
	})
}
