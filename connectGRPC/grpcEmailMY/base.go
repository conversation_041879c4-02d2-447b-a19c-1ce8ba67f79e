package grpcEmailMY

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcEmailMY"
	"google.golang.org/grpc"
)

func ConnectGRPCEmail(endpoint string) (grpcEmailMY.EmailClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcEmailMY.NewEmailClient(connect)
	return client, connect, err
}
