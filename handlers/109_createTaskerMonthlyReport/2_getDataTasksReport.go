package createTaskerMonthlyReport

import (
	"fmt"
	"sync"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelTaskerMonthlyReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/taskerMonthlyReport"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getDataTaskReport(w *sync.WaitGroup, taskerCh chan *modelUser.Users, startAMonth, endAMonth time.Time) chan *modelReport {
	outC := make(chan *modelReport, LIMIT)
	limitRoutineCh := make(chan struct{}, 3)
	go func() {
		defer w.Done()
		// Close channel khi chạy hết vòng lặp askers
		defer close(limitRoutineCh)
		defer close(outC)
		wUser := &sync.WaitGroup{}
		for tasker := range taskerCh {
			wUser.Add(1)
			limitRoutineCh <- struct{}{}
			go func(tasker *modelUser.Users) {
				defer wUser.Done()
				// get tasks done
				var totalTaskCost, totalTaskDuration float64
				var totalTaskDone int32
				var isHaveReport, isError bool
				var numberOfUsedOnService []*modelTaskerMonthlyReport.TaskerReportMonthlyReportNumberOfUsedOnService
				isTaskerCompanyOwner := isTaskerCompanyOwner(tasker)
				// If normal tasker and doesn't have task done last month, skip
				if tasker.Company == nil && globalLib.ParseDateFromTimeStamp(tasker.LastDoneTask, local.TimeZone).Before(startAMonth) {
					// Đưa data vào trong channel
					outC <- createResponseData(tasker, startAMonth, isTaskerCompanyOwner, &modelTaskerMonthlyReport.TaskerMonthlyReportMonthlyReport{
						Month:     int32(startAMonth.Month()),
						CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
					})
					<-limitRoutineCh
					return
				}
				if isTaskerCompanyOwner {
					totalTaskCost, totalTaskDuration, totalTaskDone, numberOfUsedOnService, isHaveReport, isError = getDataReportForCompany(tasker, startAMonth, endAMonth)
				} else {
					totalTaskCost, totalTaskDuration, totalTaskDone, numberOfUsedOnService, isHaveReport, isError = getDataReportForTasker(tasker, startAMonth, endAMonth)
				}
				if isError {
					msg := fmt.Sprintf("CreateTaskerMonthlyReportVN Get data task in month for tasker Error - taskerId: %s", tasker.XId)
					globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
					<-limitRoutineCh
					return
				}
				if !isHaveReport {
					// Đưa data vào trong channel
					outC <- createResponseData(tasker, startAMonth, isTaskerCompanyOwner, &modelTaskerMonthlyReport.TaskerMonthlyReportMonthlyReport{
						Month:     int32(startAMonth.Month()),
						CreatedAt: globalLib.GetCurrentTimestamp(local.TimeZone),
					})
					<-limitRoutineCh
					return
				}
				// Create data report by month
				// Đưa data vào trong channel
				outC <- createResponseData(tasker, startAMonth, isTaskerCompanyOwner, &modelTaskerMonthlyReport.TaskerMonthlyReportMonthlyReport{
					Month:                 int32(startAMonth.Month()),
					TotalTaskDone:         totalTaskDone,
					TotalTaskDuration:     totalTaskDuration,
					TotalTaskCost:         totalTaskCost,
					NumberOfUsedOnService: numberOfUsedOnService,
					CreatedAt:             globalLib.GetCurrentTimestamp(local.TimeZone),
				})
				<-limitRoutineCh
			}(tasker)
		}
		wUser.Wait()
	}()
	return outC
}

func createResponseData(tasker *modelUser.Users, startAMonth time.Time, isTaskerCompanyOwner bool, monthlyReportInfo *modelTaskerMonthlyReport.TaskerMonthlyReportMonthlyReport) *modelReport {
	return &modelReport{
		TaskerId:          tasker.XId,
		Year:              int32(startAMonth.Year()),
		MonthlyReportInfo: monthlyReportInfo,
		IsCompanyReport:   isTaskerCompanyOwner,
		EmployeeIds:       tasker.EmployeeIds,
		LastDoneTask:      globalLib.ParseDateFromTimeStamp(tasker.LastDoneTask, local.TimeZone),
		LastOnline:        globalLib.ParseDateFromTimeStamp(tasker.LastOnline, local.TimeZone),
	}
}

func getDataReportForCompany(tasker *modelUser.Users, startAMonth, endAMonth time.Time) (totalTaskCost, totalTaskDuration float64, totalTaskDone int32, numberOfUsedOnService []*modelTaskerMonthlyReport.TaskerReportMonthlyReportNumberOfUsedOnService, isHaveReport, isError bool) {
	serviceCountMap := make(map[string]int32)
	isHaveReport = false
	isError = false
	for _, employeeId := range tasker.EmployeeIds {
		tasks, iHasTaskerError := getAllTaskByTasker(employeeId, startAMonth, endAMonth)
		if len(tasks) == 0 {
			continue
		}
		if iHasTaskerError {
			isError = true
			return
		}
		isHaveReport = true
		totalTaskCostByEmployee, totalTaskDurationByEmployee, totalTaskDoneByEmployee, serviceCountMapByEmployee := calculateReportDataByTasks(tasks, employeeId)
		totalTaskCost += totalTaskCostByEmployee
		totalTaskDuration += totalTaskDurationByEmployee
		totalTaskDone += totalTaskDoneByEmployee
		for serviceName, count := range serviceCountMapByEmployee {
			serviceCountMap[serviceName] += count
		}
	}

	if !isHaveReport {
		return
	}

	for serviceName, value := range serviceCountMap {
		numberOfUsedOnService = append(numberOfUsedOnService, &modelTaskerMonthlyReport.TaskerReportMonthlyReportNumberOfUsedOnService{
			ServiceName:  serviceName,
			NumberOfUsed: value,
		})
	}
	return
}

func getDataReportForTasker(tasker *modelUser.Users, startAMonth, endAMonth time.Time) (totalTaskCost, totalTaskDuration float64, totalTaskDone int32, numberOfUsedOnService []*modelTaskerMonthlyReport.TaskerReportMonthlyReportNumberOfUsedOnService, isHaveReport, isError bool) {
	tasks, isError := getAllTaskByTasker(tasker.XId, startAMonth, endAMonth)
	if len(tasks) == 0 || isError {
		isHaveReport = false
		return
	}

	isHaveReport = true
	totalTaskCost, totalTaskDuration, totalTaskDone, serviceCountMap := calculateReportDataByTasks(tasks, tasker.XId)
	// Find the service with the highest count
	for serviceName, value := range serviceCountMap {
		numberOfUsedOnService = append(numberOfUsedOnService, &modelTaskerMonthlyReport.TaskerReportMonthlyReportNumberOfUsedOnService{
			ServiceName:  serviceName,
			NumberOfUsed: value,
		})
	}
	return
}

func getAllTaskByTasker(taskerId string, startAMonth, endAMonth time.Time) ([]*modelTask.Task, bool) {
	var listTask []*modelTask.Task
	var page, errCount, limit int64 = 1, 0, 200
	var isError bool
	for {
		tasks, err := getTasks(taskerId, startAMonth, endAMonth, page, limit)
		if err == nil {
			listTask = append(listTask, tasks...)
			// tăng số page lên +1
			page++
			errCount = 0
			if len(tasks) < int(limit) {
				break
			}
		} else {
			errCount++
			if errCount >= 5 {
				isError = true
				break
			}
		}
	}
	return listTask, isError
}

func calculateReportDataByTasks(tasks []*modelTask.Task, taskerId string) (totalTaskCost, totalTaskDuration float64, totalTaskDone int32, serviceCountMap map[string]int32) {
	// Create a map to store the count of each service
	serviceCountMap = make(map[string]int32)
	// Iterate over the tasks and perform calculations
	for _, task := range tasks {
		// Increment the count for the corresponding service
		serviceCountMap[task.ServiceName]++
		// Increment the total task count
		totalTaskDone++
		// Calculate the task cost
		taskCost, _, taskNewCost, _ := globalLib.GetTaskCostV2(task, taskerId)
		if taskNewCost > 0 {
			taskCost = taskNewCost
		}
		// Update the total task cost
		totalTaskCost += taskCost
		// Update the total task duration
		totalTaskDuration += globalLib.GetTaskDurationByTasker(task, taskerId)
	}
	return
}

func getTasks(taskerId string, startAMonth, endAMonth time.Time, page, limit int64) ([]*modelTask.Task, error) {
	query := bson.M{
		"acceptedTasker.taskerId": taskerId,                                       // Filter tasks by tasker ID
		"date":                    bson.M{"$gte": startAMonth, "$lte": endAMonth}, // Filter tasks by date range
		"status":                  globalConstant.TASK_STATUS_DONE,                // Filter tasks by status (done)
	}

	// get tasks done
	var tasks []*modelTask.Task
	err := globalDataAccess.GetAllByQueryPaging(
		globalCollection.COLLECTION_TASK[local.ISO_CODE],
		query,
		bson.M{"_id": 1, "cost": 1, "duration": 1, "serviceName": 1, "serviceId": 1, "acceptedTasker.taskerId": 1, "acceptedTasker.isLeader": 1, "costDetail.finalCost": 1, "costDetail.newFinalCost": 1,
			"detailDeepCleaning.costPerLeaderTasker.total": 1, "detailDeepCleaning.costPerTasker.total": 1, "detailDeepCleaning.newCostPerLeaderTasker.total": 1, "detailDeepCleaning.newCostPerTasker.total": 1,
			"detailOfficeCleaning.costPerLeaderTasker.total": 1, "detailOfficeCleaning.costPerTasker.total": 1, "detailOfficeCleaning.newCostPerLeaderTasker.total": 1, "detailOfficeCleaning.newCostPerTasker.total": 1,
			"increaseDurationData": 1, "payment.method": 1,
		},
		page,
		limit,
		&tasks,
	)
	if err != nil {
		msg := fmt.Sprintf("CreateTaskerMonthlyReportVN Get data task for tasker monthly report - query:%v - paging:%v - error: %v", query, page, err)
		globalLib.PostToSlack(cfg.SlackToken, "go-reset-bpoint", "bTaskee System", msg)
	}
	return tasks, err
}
