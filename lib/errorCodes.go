package lib

import (
	"net/http"

	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
)

var ERROR_CODE_CLIENT = http.StatusBadRequest
var ERROR_CODE_SERVER = http.StatusInternalServerError
var ERROR_CODE_NOT_FOUND = http.StatusNotFound

var ERROR_CAN_NOT_PARSE_API_PARAMS = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "CAN_NOT_PARSE_API_PARAMS", Message: "Server can not parse api parameters"}
var ERROR_WEBSOCKET_ERROR = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "WEBSOCKET_ERROR", Message: "Websocket error"}
var ERROR_TASKER_CAN_NOT_ACCEPT_TASK_PREMIUM = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "TASKER_CAN_NOT_ACCEPT_TASK_PREMIUM", Message: "Tasker cannot accept task premium"}
var ERROR_SERVICE_NOT_SUPPORT_IN_AREA = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "SERVICE_NOT_SUPPORT_IN_AREA", Message: "Service's not supported in area"}
var ERROR_SERVICE_NOT_STARTED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "SERVICE_NOT_STARTED", Message: "Service's not started."}
var ERROR_USER_STATUS_DISABLED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "USER_STATUS_DISABLED", Message: "User's status is disabled."}
var ERROR_SERVICE_DISABLED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "SERVICE_DISABLED", Message: "Service is disabled"}
var ERROR_NUMBER_TASK_NOT_ENOUGH = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "NUMBER_TASK_NOT_ENOUGH", Message: "Number of task not enough"}
var ERROR_CAN_NOT_GET_PRICE = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "CAN_NOT_GET_PRICE", Message: "Can not get price"}
var ERROR_CREATE_SUBSCRIPTION = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "CREATE_SUBSCRIPTION_ERROR", Message: "Create subscription error"}
var ERROR_CREATE_PURCHASE_ORDER = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "CREATE_PURCHASE_ORDER_ERROR", Message: "Create purchase order error"}
var ERROR_SERVICE_UNKNOWN = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "SERVICE_UNKNOWN", Message: "Service unknown"}
var ERROR_CAN_NOT_CHECK_TRANSACTION_STATUS = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "CAN_NOT_CHECK_TRANSACTION_STATUS", Message: "Can not call api to check transaction status"}
var ERROR_PAYMENT_NOT_SUCCESS = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_SERVER, ErrorCode: "PAYMENT_NOT_SUCCESS", Message: "Payment not success"}
var ERROR_JOURNEY_SETTING_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "JOURNEY_SETTING_NOT_FOUND", Message: "Journey setting is not found"}

var ERROR_SETTING_COUNTRY_CURRENCY_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "SETTING_COUNTRY_CURRENCY_NOT_FOUND", Message: "Currency in setting country not found"}
var ERROR_SERVICE_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "SERVICE_NOT_FOUND", Message: "Service not found"}
var ERROR_USER_NOT_FOUND = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_NOT_FOUND, ErrorCode: "USER_NOT_FOUND", Message: "User not found"}
var ERROR_USER_ID_REQUIRED = globalResponse.ResponseErrorCode{StatusCode: ERROR_CODE_CLIENT, ErrorCode: "USER_ID_REQUIRED", Message: "User id required"}
