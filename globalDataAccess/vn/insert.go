package vn

import (
	"context"

	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcDataAccess"
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccess"
	modelDataAccess "gitlab.com/btaskee/go-services-model-v2/grpcmodel/dataAccess"
)

func InsertOne(collectionName string, data interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_VN[globalDataAccess.MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	b, err := globalDataAccess.EncodeData(data)
	if err != nil {
		return err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Data: b}
	_, err = client.InsertOne(context.Background(), request)
	return err
}

func InsertAll(collectionName string, data []interface{}) error {
	client, connect, err := grpcDataAccess.ConnectGRPCDataAccess(globalDataAccess.DATA_ACCESS_URL_VN[globalDataAccess.MODE])
	if err != nil {
		return err
	}
	defer connect.Close()

	b, err := globalDataAccess.EncodeListData(data)
	if err != nil {
		return err
	}
	request := &modelDataAccess.DataAccessRequest{CollectionName: collectionName, Data: b}
	_, err = client.InsertAll(context.Background(), request)
	return err
}
