package pushNotificationNewTask

import (
	"gitlab.com/btaskee/go-push-notification-new-task-service-v2/grpcPushNotificationNewTask"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"google.golang.org/grpc"
)

func ConnectGRPCPushNotificationNewTask(endpoint string) (grpcPushNotificationNewTask.PushNotificationNewTaskClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcPushNotificationNewTask.NewPushNotificationNewTaskClient(connect)
	return client, connect, err
}
