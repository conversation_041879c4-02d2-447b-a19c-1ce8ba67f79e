package grpcSendTaskMY

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcSendTaskMY"
	"google.golang.org/grpc"
)

func ConnectGRPCSendTask(endpoint string) (grpcSendTaskMY.SendTaskClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcSendTaskMY.NewSendTaskClient(connect)
	return client, connect, err
}
