package accountingJournalEntry

import (
	"fmt"

	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalDataAccessV2"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
)

func InsertOne(isoCode, slackToken string, data *AccountingJournalEntry) (err error) {
	func() {
		defer func() {
			if err != nil {
				slackChannel := "vn-money-tracking"
				msg := fmt.Sprintf("Insert data AccountingJournalEntry - isoCode: %v - Error data: %v - error: %v", isoCode, data, err)
				globalLib.PostToSlack(slackToken, slackChannel, globalConstant.SLACK_USER_NAME, msg)
			}
		}()
		err = checkDataInfo(data)
		if err != nil {
			return
		}
		switch data.Key {
		case globalConstant.ACCOUNTING_JOURNAL_ENTRY_KEY_PAY_TASK:
			err = checkDataTask(data)
		case globalConstant.ACCOUNTING_JOURNAL_ENTRY_KEY_PAY_SUBSCRIPTION:
			err = checkDataSubscription(data)
		case globalConstant.ACCOUNTING_JOURNAL_ENTRY_KEY_CANCEL_TASK_FEE:
			err = checkDataTask(data)
		case globalConstant.ACCOUNTING_JOURNAL_ENTRY_KEY_PAY_COMBO_VOUCHER:
			err = checkDataComboVoucher(data)
		case globalConstant.ACCOUNTING_JOURNAL_ENTRY_KEY_REFUND_CANCEL_SUBSCRIPTION:
			err = checkDataSubscription(data)
		case globalConstant.ACCOUNTING_JOURNAL_ENTRY_KEY_REFUND_EXPIRED_TASK:
			err = checkDataTask(data)
		case globalConstant.ACCOUNTING_JOURNAL_ENTRY_KEY_REFUND_UPDATE_SUBSCRIPTION:
			err = checkDataSubscription(data)
		case globalConstant.ACCOUNTING_JOURNAL_ENTRY_KEY_CHARGE_UPDATE_TASK:
			err = checkDataTask(data)
		case globalConstant.ACCOUNTING_JOURNAL_ENTRY_KEY_REFUND_CANCEL_TASK:
			err = checkDataTask(data)
		case globalConstant.ACCOUNTING_JOURNAL_ENTRY_KEY_PAY_OUTSTANDING_PAYMENT:
			err = checkDataOutstandingPayment(data)
		case globalConstant.ACCOUNTING_JOURNAL_ENTRY_KEY_REFUND:
			err = checkDataRefund(data)
		case globalConstant.ACCOUNTING_JOURNAL_ENTRY_KEY_RATING_TIP:
			err = checkDataTask(data)
		case globalConstant.ACCOUNTING_JOURNAL_ENTRY_KEY_CHARGE_BNPL:
			err = checkDataBNPL(data)
		}
		if err != nil {
			return
		}
		data.CreatedAt = globalLib.GetCurrentTimestamp(globalLib.GetTimeZone())
		d := globalDataAccessV2.New(isoCode)
		err = d.InsertOne(globalCollection.COLLECTION_ACCOUNTING_JOURNAL_ENTRY[isoCode], data)
	}()
	return
}

func checkDataInfo(data *AccountingJournalEntry) (err error) {
	if data.UserId == "" {
		err = fmt.Errorf("UserId is required")
		return
	}
	if data.Amount <= 0 {
		err = fmt.Errorf("amount is invalid")
		return
	}
	if data.Type == "" {
		err = fmt.Errorf("type is required")
		return
	}
	if data.UserType == "" {
		err = fmt.Errorf("userType is required")
		return
	}
	if data.AccountType == "" {
		err = fmt.Errorf("accountType is required")
		return
	}
	if data.Payment == nil {
		err = fmt.Errorf("payment is required")
		return
	}
	return
}

func checkDataTask(data *AccountingJournalEntry) (err error) {
	if data.TaskId == "" {
		err = fmt.Errorf("TaskId is required")
	}
	return
}

func checkDataSubscription(data *AccountingJournalEntry) (err error) {
	if data.SubscriptionId == "" {
		err = fmt.Errorf("SubscriptionId is required")
	}
	return
}

func checkDataComboVoucher(data *AccountingJournalEntry) (err error) {
	if data.ComboVoucherId == "" {
		err = fmt.Errorf("ComboVoucherId is required")
	}
	return
}

func checkDataOutstandingPayment(data *AccountingJournalEntry) (err error) {
	if data.OutstandingPaymentId == "" {
		err = fmt.Errorf("OutstandingPaymentId is required")
	}
	return
}

func checkDataRefund(data *AccountingJournalEntry) (err error) {
	if data.TaskId == "" && data.SubscriptionId == "" {
		err = fmt.Errorf("refund data is required")
	}
	return
}

func checkDataBNPL(data *AccountingJournalEntry) (err error) {
	if data.BNPLId == "" {
		err = fmt.Errorf("BNPLId is required")
	}
	return
}
