//Package lib file helpers
/*
 * @File: helpers.go
 * @Description: Func helpers
 * @CreatedAt: 24/04/2021
 * @Author: vinhnt
 */
package lib

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"net/http"
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/config"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/model"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcPushNotificationVN"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcSendTaskVN"
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC/grpcWebsocket"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	pkgChatMessage "gitlab.com/btaskee/go-services-model-v2/globalLib/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/globalResponse"
	"gitlab.com/btaskee/go-services-model-v2/grpcProxy/grpcChatServer"
	modelChatMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/chatMessage"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/kafkaEvent"
	modelPushNotificationNewTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask"
	modelPushNotificationRequest "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationRequest"
	modelService "gitlab.com/btaskee/go-services-model-v2/grpcmodel/service"
	modelTask "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
)

var cfg = config.GetConfig()

func PostToSlackNotStart(name string) {
	// text := fmt.Sprintf("Sync cron %s can't start", name)
	// globalLib.PostToSlack(cfg.SlackToken, "go-services", GO_SERVICE_SYNC_CRON_USER_NAME, text)
}

func ParseBodyParams(r *http.Request) (*model.RequestAction, *globalResponse.ResponseErrorCode) {
	defer local.Logger.Sync()
	// Decode request body
	decoder := json.NewDecoder(r.Body)
	var reqBody model.RequestAction
	err := decoder.Decode(&reqBody)
	if err != nil {
		local.Logger.Warn(ERROR_CAN_NOT_PARSE_API_PARAMS.ErrorCode,
			zap.String("url", r.RequestURI),
			zap.Error(err),
			zap.Any("body", reqBody),
		)
		return nil, &ERROR_CAN_NOT_PARSE_API_PARAMS
	}
	return &reqBody, nil
}

func GetIncreaseDurationNotForTasker(taskIncreaseDurationData []*modelTask.TaskIncreaseDurationData, taskerId string) float64 {
	var totalDuration float64
	for _, data := range taskIncreaseDurationData {
		isExists := false
		for _, v := range data.TaskerMoney {
			if v.TaskerId == taskerId {
				isExists = true
				break
			}
		}
		if !isExists {
			totalDuration += data.IncreaseDuration
		}
	}
	return totalDuration
}

func IsHalf(total, value int32) bool {
	return total >= value/2
}

func SendNotification(arrayNotification []interface{}, userIds []*modelPushNotificationRequest.PushNotificationRequestUserIds, title *modelService.ServiceText, body *modelService.ServiceText, payload *modelPushNotificationRequest.PushNotificationRequestPayload, isForceView bool) {
	if isForceView && len(arrayNotification) > 0 {
		globalDataAccess.InsertAll(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], arrayNotification)
	}
	if len(userIds) > 0 {
		logger, _ := zap.NewProduction()
		defer logger.Sync()

		client, connect, err := grpcPushNotificationVN.ConnectGRPCPushNotificationVN(cfg.GRPC_Push_Notification_URL)
		if err != nil {
			logger.Info("Connect GRPC Push Notification error",
				zap.Error(err),
				zap.Any("title", title),
				zap.Any("body", body),
				zap.Any("payload", payload),
			)
			return
		}
		defer connect.Close()

		reqNotify := &modelPushNotificationRequest.PushNotificationRequest{
			UserIds: userIds,
			Title:   title,
			Body:    body,
			Payload: payload,
		}
		_, err = client.Send(context.Background(), reqNotify)
		if err != nil {
			logger.Info("Push Notification error",
				zap.Error(err),
				zap.Any("title", title),
				zap.Any("body", body),
				zap.Any("payload", payload),
			)
		}
		// Send notification in app
		grpcWebsocket.SendSocketNotification(arrayNotification, cfg.GRPC_Websocket_Service_V2_URL, userIds, payload.Type, title, body)
	}
}

func SendChatMessageViaSocket(task *modelTask.Task, chatId string, sendTo []string, message *modelChatMessage.ChatMessageMessages) error {
	receivers := []*kafkaEvent.ConversationMessageReceiver{}
	for _, v := range sendTo {
		receivers = append(receivers, &kafkaEvent.ConversationMessageReceiver{
			UserId: v,
		})
	}
	_, err := grpcChatServer.SendSocketChatMessage(local.ISO_CODE, "", cfg.GRPC_Chat_Server_VN_V3_URL, chatId, receivers, message, task)
	return err
}

func FindEarliestOptionInDateOptions(taskDateOptions []*modelTask.TaskDateOptions) *timestamppb.Timestamp {
	var earliest time.Time
	for _, v := range taskDateOptions {
		parseTime := globalLib.ParseDateFromTimeStamp(v.Date, local.TimeZone)
		if earliest.IsZero() || parseTime.Before(earliest) {
			earliest = parseTime
		}
	}
	return globalLib.ParseTimestampFromDate(earliest)
}

func SetDisabledMessage(chatId, taskId, key, taskerId, askerId string) {
	// Get message
	queryMessage := bson.M{
		"chatId":                          chatId,
		"taskRequestData.taskInfo.taskId": taskId,
		"messageBySystem.key":             key,
	}
	messages, err := pkgChatMessage.GetChatMessages(local.ISO_CODE, queryMessage, bson.M{})
	if err != nil && err != mongo.ErrNoDocuments {
		msg := fmt.Sprintf("[go-accept-task-vn-v3] Cannot get chat message: chatId %s, taskId %s. Error: %s", chatId, taskId, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
		return
	}
	if messages == nil {
		return
	}

	// Check if the message ID matches and the message has system actions
	for _, message := range messages {
		updateData := bson.M{}
		// Loop through the actions in the message
		for i := range message.MessageBySystem.Actions {
			// Check if the action is disabled
			if !message.MessageBySystem.Actions[i].IsDisabled {
				updateData[fmt.Sprintf("messageBySystem.actions.%d.isDisabled", i)] = true
			}
		}
		if len(updateData) == 0 {
			continue
		}
		// Update the database with the new 'isDisabled' values
		_, err := pkgChatMessage.UpdateChatMessage(local.ISO_CODE, bson.M{"_id": message.XId}, bson.M{"$set": updateData})
		// Return error if the update fails
		if err != nil {
			msg := fmt.Sprintf("[go-sync-service] Cannot update chat message: chatId %s, taskId %s. Error: %s", chatId, taskId, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
			return
		}
		// Set the 'isDisabled' field to true for each action
		for _, action := range message.MessageBySystem.Actions {
			action.IsDisabled = true
		}
		var sendTo []string
		switch message.MessageBySystem.SendTo {
		case globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_ASKER:
			sendTo = []string{askerId}
		case globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_TASKER:
			sendTo = []string{taskerId}
		default:
			sendTo = []string{askerId, taskerId}
		}
		// Send the updated message to the chat participants via socket
		SendChatMessageViaSocket(nil, chatId, sendTo, message)
	}
}
func RemoveCountDownAndStatusInMessage(chatId string, taskId string, askerId string) {
	query := bson.M{
		"chatId": chatId,
		"from":   globalConstant.CHAT_MESSAGE_FROM_SYSTEM,
		"$or": []bson.M{
			{"taskRequestData.status": bson.M{"$exists": true}},
			{"taskRequestData.expiredAt": bson.M{"$exists": true}},
		},
		"taskRequestData.taskInfo.taskId": taskId,
		"messageBySystem.sendTo":          globalConstant.CHAT_MESSAGE_FROM_ASKER,
	}
	//Get message to send socket
	messages, err := pkgChatMessage.GetChatMessages(local.ISO_CODE, query, bson.M{})
	if err != nil && err != mongo.ErrNoDocuments {
		msg := fmt.Sprintf("[go-sync-cron-vn-v3] Cannot get chat message: chatId %s, taskId %s. Error: %s", chatId, taskId, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
		return
	}
	if len(messages) == 0 {
		return
	}
	var messageIds []string
	for _, v := range messages {
		messageIds = append(messageIds, v.XId)
	}

	//Update message
	pkgChatMessage.UpdateChatMessages(
		local.ISO_CODE,
		bson.M{"_id": bson.M{"$in": messageIds}},
		bson.M{"$unset": bson.M{
			"taskRequestData.status":    1,
			"taskRequestData.expiredAt": 1,
		}},
	)

	for _, message := range messages {
		if message.TaskRequestData != nil {
			message.TaskRequestData.Status = ""
			message.TaskRequestData.ExpiredAt = nil
		}
		SendChatMessageViaSocket(nil, chatId, []string{askerId}, message)
	}
}

func SendMessageToFavChat(task *modelTask.Task, tasker, asker *modelUser.Users, titleMessageAsker, textMessageAsker, titleMessageTasker, textMessageTasker *modelService.ServiceText, actionsForAskerMessage, actionsForTaskerMessage []*modelChatMessage.ChatMessageMessagesMessageBySystemActions, isDisabledAction bool) string {
	query := bson.M{
		"members":     bson.M{"$size": 2}, // Check the array size is 2
		"members._id": bson.M{"$all": []string{task.AskerId, task.ForceTasker.TaskerId}},
	}
	fields := bson.M{"_id": 1}
	chatMessage, err := pkgChatMessage.GetChatConversation(local.ISO_CODE, query, fields)
	if err != nil && err != mongo.ErrNoDocuments {
		msg := fmt.Sprintf("[go-sync-cron-vn-v3] Can not get chat conversation. Error: %s", err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
		return ""
	}

	if chatMessage == nil {
		return ""
	}

	params := pkgChatMessage.FavMessageParams{
		IsoCode:  local.ISO_CODE,
		TimeZone: local.TimeZone,
		ChatId:   chatMessage.XId,
		Task:     task,
	}

	if titleMessageAsker != nil && textMessageAsker != nil {
		params.Title = titleMessageAsker
		params.Text = textMessageAsker
		params.SendTo = globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_ASKER
		if len(actionsForAskerMessage) > 0 {
			params.Actions = actionsForAskerMessage
		}
		messageForAsker, err := pkgChatMessage.SendMessageToFavChat(params)
		if err != nil {
			msg := fmt.Sprintf("[go-sync-cron-vn] Send Chat Message to fav chat failed: taskId %s. Error: %s", task.XId, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
		}
		go SendChatMessageViaSocket(task, chatMessage.XId, []string{asker.XId}, messageForAsker)
	}

	if titleMessageTasker != nil && textMessageTasker != nil {
		params.Title = titleMessageTasker
		params.Text = textMessageTasker
		params.SendTo = globalConstant.CHAT_MESSAGE_BY_SYSTEM_SEND_TO_TASKER
		if len(actionsForTaskerMessage) > 0 {
			params.Actions = actionsForTaskerMessage
		}
		messageForTasker, err := pkgChatMessage.SendMessageToFavChat(params)
		if err != nil {
			msg := fmt.Sprintf("[go-sync-cron-vn] Send Chat Message to fav chat failed: taskId %s. Error: %s", task.XId, err.Error())
			globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], msg)
		}
		go SendChatMessageViaSocket(task, chatMessage.XId, []string{tasker.XId}, messageForTasker)
	}
	go SetDisabledMessage(chatMessage.XId, task.XId, globalConstant.KEY_BOOK_WITH_FAV, tasker.XId, asker.XId)
	go RemoveCountDownAndStatusInMessage(chatMessage.XId, task.XId, asker.XId)
	return chatMessage.XId
}

func CheckEnoughMoney(askerId string, taskCost float64) (map[string]interface{}, *globalResponse.ResponseErrorCode) {
	if askerId == "" {
		return nil, &ERROR_USER_ID_REQUIRED
	}
	result := map[string]interface{}{
		"enough": false,
	}
	var users *modelUser.Users
	globalDataAccess.GetOneById(globalCollection.COLLECTION_USERS, askerId, bson.M{"_id": 1, "fAccountId": 1}, &users)
	if users != nil {
		date := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -LIMIT_TASK_DATE)
		status := [3]string{globalConstant.TASK_STATUS_POSTED, globalConstant.TASK_STATUS_WAITING, globalConstant.TASK_STATUS_CONFIRMED}
		var tasks []*modelTask.Task
		globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_TASK[local.ISO_CODE],
			bson.M{
				"date":           bson.M{"$gte": date},
				"askerId":        askerId,
				"status":         bson.M{"$in": status},
				"payment.method": globalConstant.PAYMENT_METHOD_CREDIT,
				"isoCode":        local.ISO_CODE,
			},
			bson.M{"cost": 1, "costDetail": 1},
			&tasks,
		)

		var creditMoneyUsed float64
		for _, vTask := range tasks {
			cost := vTask.Cost
			if vTask.CostDetail != nil && vTask.CostDetail.FinalCost > 0 {
				cost = vTask.CostDetail.FinalCost
				if vTask.CostDetail.TotalCost > vTask.CostDetail.FinalCost {
					cost = vTask.CostDetail.TotalCost
				}
			}
			creditMoneyUsed += cost
		}
		main, _ := globalLib.GetFAccountByIsoCode(users.FAccountId, local.ISO_CODE)
		balance := main - creditMoneyUsed - taskCost
		if balance >= 0 {
			result["enough"] = true
		} else {
			result["enough"] = false
			result["amount"] = math.Abs(balance)
		}
	} else {
		return nil, &ERROR_USER_NOT_FOUND
	}
	return result, nil
}
func ResendTaskToFavTasker(taskId string, askerId string, serviceId string, isoCode string) error {
	globalDataAccess.DeleteAllByQuery(globalCollection.COLLECTION_NOTIFICATION[local.ISO_CODE], bson.M{"taskId": taskId})
	asker, err := modelUser.GetOneById(local.ISO_CODE, askerId, bson.M{"isBlacklist": 1})
	if err != nil {
		return err
	}
	// Send as new task notifications to Taskers
	if asker != nil && !asker.IsBlacklist {
		client, connect, err := grpcSendTaskVN.ConnectGRPCSendTaskVN(cfg.GRPC_Send_Task_URL)
		if err != nil {
			return err
		}
		defer connect.Close()
		request := &modelPushNotificationNewTask.NewTaskRequest{
			Service: &modelPushNotificationNewTask.NewTaskRequestService{
				XId: serviceId,
			},
			Booking: &modelPushNotificationNewTask.NewTaskRequestBooking{
				XId:     taskId,
				IsoCode: isoCode,
			},
		}
		resp, err := client.FavTasker(context.Background(), request)
		if resp != nil && resp.Error != nil {
			return errors.New(resp.Error.Code)
		} else if err != nil {
			return err
		}
	}
	return nil
}
