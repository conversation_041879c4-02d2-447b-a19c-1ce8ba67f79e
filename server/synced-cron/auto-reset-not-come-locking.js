import { Meteor } from "meteor/meteor";
import moment from "moment";
import _ from 'lodash';
import {
  trackingScheduleStatus,
  postToSlack,
} from "/server/helper-function";


const getTaskerNeedReset = () => {
  return Meteor.appCollection.users.find({
    type: 'TASKER',
    status: 'ACTIVE',
    notComeLockNumber: {$gte: 1},
    lastActiveAt: {
      $lte: moment().subtract(30, 'days').toDate(),
    },
    lastLockedAt: {
      $lte: moment().subtract(30, 'days').toDate(),
    }
  }, {fields: {
    lastLockedAt: 1,
    lastActiveAt: 1,
    notComeLockNumber: 1,
  }}).fetch();
};

const resetNotComeLocking = (tasker) => {
  Meteor.appCollection.users.update({
    _id: tasker._id
  }, {
    $unset: {
      notComeLockNumber: 1
    },
    $push: {
      resetLockingHistory: {
        date: new Date(),
        notComeLockNumber: tasker.notComeLockNumber,
        lastLockedAt: tasker.lastLockedAt,
        lastActiveAt: tasker.lastActiveAt,
      }
    }
  });
};

const runReset = () => {
  return;
	console.log("Start runReset Tasker locked ...");
  var message = null;
  try {
    const taskers = getTaskerNeedReset();
    taskers.forEach(tasker => {
      resetNotComeLocking(tasker);
    });
  } catch (e) {
    message = e;
  }
	console.log("Finished runReset Tasker locked ...");
  // Tracking
  trackingScheduleStatus({
    name: "autoResetNotComeLocking",
    nextRunAt: SyncedCron.nextScheduledAtDate("autoResetNotComeLocking"),
    message
  });
};

  Meteor.methods({
    autoResetNotComeLocking() {
      SyncedCron.add({
        name: "autoResetNotComeLocking",
        schedule(parser) {
          // Run every 8 hours
          return parser.cron(`0 0 0/8 1/1 * ? *`, true);
        },
        job() {
          runReset();
        }
      });
	    console.log("Added autoResetNotComeLocking cron");
    },

    "runNow.autoResetNotComeLocking"() {
      runReset();
    }
  });
