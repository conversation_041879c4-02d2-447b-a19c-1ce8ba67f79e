package sendBusinessReportEmail

import (
	"net/http"

	"gitlab.com/btaskee/go-email-vn-v3/lib"
	"gitlab.com/btaskee/go-email-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/grpcmodel/business"
	modelEmailResponse "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func getBusinessInfo(businessId string) (business *business.Business, language string, user *modelUser.Users, resErr *modelEmailResponse.EmailResponse, err error) {
	err = globalDataAccess.GetOneByQuery(globalCollection.COLLECTION_BUSINESS[local.ISO_CODE],
		bson.M{"_id": businessId, "status": globalConstant.BUSINESS_STATUS_ACTIVE}, bson.M{"name": 1, "email": 1}, &business)
	if err != nil || business == nil {
		return nil, "", nil, &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusInternalServerError,
			Message:    lib.ERROR_BUSINESS_NOT_FOUND,
		}, err
	}

	user, err = modelUser.GetOneById(local.ISO_CODE, businessId, bson.M{"language": 1, "name": 1})
	if err != nil || user == nil {
		return nil, "", nil, &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusInternalServerError,
			Message:    lib.ERROR_USER_NOT_FOUND,
		}, err
	}

	if business.Email == "" {
		return nil, "", nil, &modelEmailResponse.EmailResponse{
			StatusCode: http.StatusOK,
		}, nil
	}

	language = globalConstant.LANG_VI
	if user.Language != "" {
		language = user.Language
	}

	return business, language, user, nil, nil
}
