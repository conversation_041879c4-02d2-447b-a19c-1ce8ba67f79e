package weeklyResetLeaderBoard

import (
	"time"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelJourneyLeaderBoard "gitlab.com/btaskee/go-services-model-v2/grpcmodel/journeyLeaderBoard"
	modelJourneyLeaderBoardWeeklyReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/journeyLeaderBoardWeeklyReport"
)

func insertDataToWeeklyReport(taskersLeaderBoard []*modelJourneyLeaderBoard.JourneyLeaderBoard) error {
	taskersWeeklyReport := []interface{}{}
	var endLastWeek time.Time
	currentTime := globalLib.GetCurrentTime(local.TimeZone)

	if wd := currentTime.Weekday(); wd == time.Sunday {
		endLastWeek = currentTime.AddDate(0, 0, -7)
	} else {
		endLastWeek = currentTime.AddDate(0, 0, -int(wd))
	}
	endLastWeek = time.Date(endLastWeek.Year(), endLastWeek.Month(), endLastWeek.Day()+1, 0, 0, 0, 0, local.TimeZone).Add(-1 * time.Second)
	startLastWeek := time.Date(endLastWeek.Year(), endLastWeek.Month(), endLastWeek.Day(), 0, 0, 0, 0, local.TimeZone).AddDate(0, 0, -6)
	for _, taskerLeaderBoard := range taskersLeaderBoard {
		taskerWeeklyReport := &modelJourneyLeaderBoardWeeklyReport.JourneyLeaderBoardWeeklyReport{
			XId:       globalLib.GenerateObjectId(),
			TaskerId:  taskerLeaderBoard.XId,
			CityName:  taskerLeaderBoard.CityName,
			Name:      taskerLeaderBoard.Name,
			Avatar:    taskerLeaderBoard.Avatar,
			Rank:      taskerLeaderBoard.Rank,
			Point:     taskerLeaderBoard.Point,
			Level:     taskerLeaderBoard.Level,
			Icon:      taskerLeaderBoard.Icon,
			Title:     taskerLeaderBoard.Title,
			Text:      taskerLeaderBoard.Text,
			FromDate:  globalLib.ParseTimestampFromDate(startLastWeek),
			ToDate:    globalLib.ParseTimestampFromDate(endLastWeek),
			CreatedAt: globalLib.ParseTimestampFromDate(currentTime),
		}
		taskersWeeklyReport = append(taskersWeeklyReport, taskerWeeklyReport)
		if len(taskersWeeklyReport) >= 500 {
			err := globalDataAccess.InsertAll(globalCollection.COLLECTION_JOURNEY_LEADER_BOARD_WEEKLY_REPORT[local.ISO_CODE], taskersWeeklyReport)
			if err == nil {
				taskersWeeklyReport = []interface{}{}
			}
		}
	}
	if len(taskersWeeklyReport) > 0 {
		err := globalDataAccess.InsertAll(globalCollection.COLLECTION_JOURNEY_LEADER_BOARD_WEEKLY_REPORT[local.ISO_CODE], taskersWeeklyReport)
		if err != nil {
			return err
		}
	}
	return nil
}
