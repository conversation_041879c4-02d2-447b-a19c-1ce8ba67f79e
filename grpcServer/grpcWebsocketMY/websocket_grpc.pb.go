// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcWebsocketMY

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	websocketMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/websocketMessage"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// WebsocketClient is the client API for Websocket service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type WebsocketClient interface {
	SendChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	SendSocketMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error)
}

type websocketClient struct {
	cc grpc.ClientConnInterface
}

func NewWebsocketClient(cc grpc.ClientConnInterface) WebsocketClient {
	return &websocketClient{cc}
}

func (c *websocketClient) SendChatMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcWebsocketMY.Websocket/SendChatMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *websocketClient) SendSocketMessage(ctx context.Context, in *websocketMessage.WebsocketMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcWebsocketMY.Websocket/SendSocketMessage", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// WebsocketServer is the server API for Websocket service.
// All implementations must embed UnimplementedWebsocketServer
// for forward compatibility
type WebsocketServer interface {
	SendChatMessage(context.Context, *websocketMessage.WebsocketMessage) (*empty.Empty, error)
	SendSocketMessage(context.Context, *websocketMessage.WebsocketMessage) (*empty.Empty, error)
	mustEmbedUnimplementedWebsocketServer()
}

// UnimplementedWebsocketServer must be embedded to have forward compatible implementations.
type UnimplementedWebsocketServer struct {
}

func (UnimplementedWebsocketServer) SendChatMessage(context.Context, *websocketMessage.WebsocketMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendChatMessage not implemented")
}
func (UnimplementedWebsocketServer) SendSocketMessage(context.Context, *websocketMessage.WebsocketMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendSocketMessage not implemented")
}
func (UnimplementedWebsocketServer) mustEmbedUnimplementedWebsocketServer() {}

// UnsafeWebsocketServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to WebsocketServer will
// result in compilation errors.
type UnsafeWebsocketServer interface {
	mustEmbedUnimplementedWebsocketServer()
}

func RegisterWebsocketServer(s *grpc.Server, srv WebsocketServer) {
	s.RegisterService(&_Websocket_serviceDesc, srv)
}

func _Websocket_SendChatMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(websocketMessage.WebsocketMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebsocketServer).SendChatMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcWebsocketMY.Websocket/SendChatMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebsocketServer).SendChatMessage(ctx, req.(*websocketMessage.WebsocketMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _Websocket_SendSocketMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(websocketMessage.WebsocketMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(WebsocketServer).SendSocketMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcWebsocketMY.Websocket/SendSocketMessage",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(WebsocketServer).SendSocketMessage(ctx, req.(*websocketMessage.WebsocketMessage))
	}
	return interceptor(ctx, in, info, handler)
}

var _Websocket_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcWebsocketMY.Websocket",
	HandlerType: (*WebsocketServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendChatMessage",
			Handler:    _Websocket_SendChatMessage_Handler,
		},
		{
			MethodName: "SendSocketMessage",
			Handler:    _Websocket_SendSocketMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "websocket.proto",
}
