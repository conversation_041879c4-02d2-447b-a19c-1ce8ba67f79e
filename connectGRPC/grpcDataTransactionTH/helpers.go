package grpcDataTransactionTH

import (
	"context"
	"os"

	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelDataTransactionMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/dataTransactionMessage"
	"go.uber.org/zap"
)

var GRPC_DATA_TRANSACTION_URL = map[string]string{
	"local": "127.0.0.1:29201",
	"test":  "127.0.0.1:29201",
	"ci":    "go-data-transaction-th:29201",
	"dev":   "go-data-transaction-th.kong.svc.cluster.local:81",
	"prod":  "go-data-transaction-th.kong.svc.cluster.local:81",
}

var MODE = os.Getenv("APPLICATION_MODE")

func DoneTaskFinancial(ctx context.Context, doneTaskFinancialRequest *modelDataTransactionMessage.DoneTaskRequest) (*modelDataTransactionMessage.DoneTaskResponse, error) {
	defer globalLib.Logger.Sync()

	client, connect, err := ConnectGRPCDataTransaction(GRPC_DATA_TRANSACTION_URL[MODE])
	if err != nil {
		globalLib.Logger.Warn("Connect GRPC Data Transaction error",
			zap.Error(err),
			zap.Any("doneTaskFinancialRequest", doneTaskFinancialRequest),
		)
		return nil, err
	}
	defer connect.Close()

	response, err := client.DoneTask(ctx, doneTaskFinancialRequest)
	if err != nil {
		globalLib.Logger.Warn("Done Task Financial error",
			zap.Error(err),
			zap.Any("doneTaskFinancialRequest", doneTaskFinancialRequest),
		)
		return nil, err
	}

	return response, nil
}

func UpdateFinancial(ctx context.Context, updateFinancialRequest *modelDataTransactionMessage.UpdateFinancialRequest) (*modelDataTransactionMessage.UpdateFinancialResponse, error) {
	defer globalLib.Logger.Sync()

	client, connect, err := ConnectGRPCDataTransaction(GRPC_DATA_TRANSACTION_URL[MODE])
	if err != nil {
		globalLib.Logger.Warn("Connect GRPC Data Transaction error",
			zap.Error(err),
			zap.Any("updateFinancialRequest", updateFinancialRequest),
		)
		return nil, err
	}
	defer connect.Close()

	response, err := client.UpdateFinancial(ctx, updateFinancialRequest)
	if err != nil {
		globalLib.Logger.Warn("Update Financial error",
			zap.Error(err),
			zap.Any("updateFinancialRequest", updateFinancialRequest),
		)
		return nil, err
	}

	return response, nil
}
