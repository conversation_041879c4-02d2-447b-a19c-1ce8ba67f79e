// Code generated by protoc-gen-go-grpc. DO NOT EDIT.

package grpcEventVN

import (
	context "context"
	empty "github.com/golang/protobuf/ptypes/empty"
	eventMessage "gitlab.com/btaskee/go-services-model-v2/grpcmodel/eventMessage"
	users "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion7

// EventVNClient is the client API for EventVN service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EventVNClient interface {
	TrackingCleverTapPayComboSuccess(ctx context.Context, in *eventMessage.TrackingCleverTapPayComboMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapPayComboPaymentError(ctx context.Context, in *eventMessage.TrackingCleverTapPayComboMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerAcceptTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerCancelTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerWithdrawTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerDoneTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerRating(ctx context.Context, in *eventMessage.TrackingCleverTapRatingMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskerFinishTraining(ctx context.Context, in *eventMessage.TrackingCleverTapTrainingMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapPrepayTaskPaymentResponse(ctx context.Context, in *eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	AddAskerDoneBookingReferralGift(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*empty.Empty, error)
	AddAskerSignUpReferralGift(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionLogin(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionDoneTask(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionTaskerDoneTask(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionReferral(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	TrackingCleverTapTaskExpired(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionDoneTaskReferral(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionRollCall(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionRollCallAtNoel(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionDoneTaskFirst(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
	PublishUserGameCampaignActionNewSignUp(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error)
}

type eventVNClient struct {
	cc grpc.ClientConnInterface
}

func NewEventVNClient(cc grpc.ClientConnInterface) EventVNClient {
	return &eventVNClient{cc}
}

func (c *eventVNClient) TrackingCleverTapPayComboSuccess(ctx context.Context, in *eventMessage.TrackingCleverTapPayComboMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/TrackingCleverTapPayComboSuccess", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) TrackingCleverTapPayComboPaymentError(ctx context.Context, in *eventMessage.TrackingCleverTapPayComboMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/TrackingCleverTapPayComboPaymentError", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) TrackingCleverTapTaskerAcceptTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/TrackingCleverTapTaskerAcceptTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) TrackingCleverTapTaskerCancelTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/TrackingCleverTapTaskerCancelTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) TrackingCleverTapTaskerWithdrawTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/TrackingCleverTapTaskerWithdrawTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) TrackingCleverTapTaskerDoneTask(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/TrackingCleverTapTaskerDoneTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) TrackingCleverTapTaskerRating(ctx context.Context, in *eventMessage.TrackingCleverTapRatingMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/TrackingCleverTapTaskerRating", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) TrackingCleverTapTaskerFinishTraining(ctx context.Context, in *eventMessage.TrackingCleverTapTrainingMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/TrackingCleverTapTaskerFinishTraining", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) TrackingCleverTapPrepayTaskPaymentResponse(ctx context.Context, in *eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/TrackingCleverTapPrepayTaskPaymentResponse", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) AddAskerDoneBookingReferralGift(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/AddAskerDoneBookingReferralGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) AddAskerSignUpReferralGift(ctx context.Context, in *users.Users, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/AddAskerSignUpReferralGift", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) PublishUserGameCampaignActionLogin(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/PublishUserGameCampaignActionLogin", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) PublishUserGameCampaignActionDoneTask(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/PublishUserGameCampaignActionDoneTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) PublishUserGameCampaignActionTaskerDoneTask(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/PublishUserGameCampaignActionTaskerDoneTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) PublishUserGameCampaignActionReferral(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/PublishUserGameCampaignActionReferral", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) TrackingCleverTapTaskExpired(ctx context.Context, in *eventMessage.TrackingCleverTapTaskMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/TrackingCleverTapTaskExpired", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) PublishUserGameCampaignActionDoneTaskReferral(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/PublishUserGameCampaignActionDoneTaskReferral", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) PublishUserGameCampaignActionRollCall(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/PublishUserGameCampaignActionRollCall", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) PublishUserGameCampaignActionRollCallAtNoel(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/PublishUserGameCampaignActionRollCallAtNoel", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) PublishUserGameCampaignActionDoneTaskFirst(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/PublishUserGameCampaignActionDoneTaskFirst", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eventVNClient) PublishUserGameCampaignActionNewSignUp(ctx context.Context, in *eventMessage.EventCommonMessage, opts ...grpc.CallOption) (*empty.Empty, error) {
	out := new(empty.Empty)
	err := c.cc.Invoke(ctx, "/grpcEventVN.EventVN/PublishUserGameCampaignActionNewSignUp", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EventVNServer is the server API for EventVN service.
// All implementations must embed UnimplementedEventVNServer
// for forward compatibility
type EventVNServer interface {
	TrackingCleverTapPayComboSuccess(context.Context, *eventMessage.TrackingCleverTapPayComboMessage) (*empty.Empty, error)
	TrackingCleverTapPayComboPaymentError(context.Context, *eventMessage.TrackingCleverTapPayComboMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerAcceptTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerCancelTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerWithdrawTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerDoneTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerRating(context.Context, *eventMessage.TrackingCleverTapRatingMessage) (*empty.Empty, error)
	TrackingCleverTapTaskerFinishTraining(context.Context, *eventMessage.TrackingCleverTapTrainingMessage) (*empty.Empty, error)
	TrackingCleverTapPrepayTaskPaymentResponse(context.Context, *eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage) (*empty.Empty, error)
	AddAskerDoneBookingReferralGift(context.Context, *users.Users) (*empty.Empty, error)
	AddAskerSignUpReferralGift(context.Context, *users.Users) (*empty.Empty, error)
	PublishUserGameCampaignActionLogin(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionDoneTask(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionTaskerDoneTask(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionReferral(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	TrackingCleverTapTaskExpired(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionDoneTaskReferral(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionRollCall(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionRollCallAtNoel(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionDoneTaskFirst(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	PublishUserGameCampaignActionNewSignUp(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error)
	mustEmbedUnimplementedEventVNServer()
}

// UnimplementedEventVNServer must be embedded to have forward compatible implementations.
type UnimplementedEventVNServer struct {
}

func (UnimplementedEventVNServer) TrackingCleverTapPayComboSuccess(context.Context, *eventMessage.TrackingCleverTapPayComboMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapPayComboSuccess not implemented")
}
func (UnimplementedEventVNServer) TrackingCleverTapPayComboPaymentError(context.Context, *eventMessage.TrackingCleverTapPayComboMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapPayComboPaymentError not implemented")
}
func (UnimplementedEventVNServer) TrackingCleverTapTaskerAcceptTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerAcceptTask not implemented")
}
func (UnimplementedEventVNServer) TrackingCleverTapTaskerCancelTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerCancelTask not implemented")
}
func (UnimplementedEventVNServer) TrackingCleverTapTaskerWithdrawTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerWithdrawTask not implemented")
}
func (UnimplementedEventVNServer) TrackingCleverTapTaskerDoneTask(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerDoneTask not implemented")
}
func (UnimplementedEventVNServer) TrackingCleverTapTaskerRating(context.Context, *eventMessage.TrackingCleverTapRatingMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerRating not implemented")
}
func (UnimplementedEventVNServer) TrackingCleverTapTaskerFinishTraining(context.Context, *eventMessage.TrackingCleverTapTrainingMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskerFinishTraining not implemented")
}
func (UnimplementedEventVNServer) TrackingCleverTapPrepayTaskPaymentResponse(context.Context, *eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapPrepayTaskPaymentResponse not implemented")
}
func (UnimplementedEventVNServer) AddAskerDoneBookingReferralGift(context.Context, *users.Users) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAskerDoneBookingReferralGift not implemented")
}
func (UnimplementedEventVNServer) AddAskerSignUpReferralGift(context.Context, *users.Users) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AddAskerSignUpReferralGift not implemented")
}
func (UnimplementedEventVNServer) PublishUserGameCampaignActionLogin(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionLogin not implemented")
}
func (UnimplementedEventVNServer) PublishUserGameCampaignActionDoneTask(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionDoneTask not implemented")
}
func (UnimplementedEventVNServer) PublishUserGameCampaignActionTaskerDoneTask(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionTaskerDoneTask not implemented")
}
func (UnimplementedEventVNServer) PublishUserGameCampaignActionReferral(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionReferral not implemented")
}
func (UnimplementedEventVNServer) TrackingCleverTapTaskExpired(context.Context, *eventMessage.TrackingCleverTapTaskMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TrackingCleverTapTaskExpired not implemented")
}
func (UnimplementedEventVNServer) PublishUserGameCampaignActionDoneTaskReferral(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionDoneTaskReferral not implemented")
}
func (UnimplementedEventVNServer) PublishUserGameCampaignActionRollCall(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionRollCall not implemented")
}
func (UnimplementedEventVNServer) PublishUserGameCampaignActionRollCallAtNoel(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionRollCallAtNoel not implemented")
}
func (UnimplementedEventVNServer) PublishUserGameCampaignActionDoneTaskFirst(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionDoneTaskFirst not implemented")
}
func (UnimplementedEventVNServer) PublishUserGameCampaignActionNewSignUp(context.Context, *eventMessage.EventCommonMessage) (*empty.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PublishUserGameCampaignActionNewSignUp not implemented")
}
func (UnimplementedEventVNServer) mustEmbedUnimplementedEventVNServer() {}

// UnsafeEventVNServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EventVNServer will
// result in compilation errors.
type UnsafeEventVNServer interface {
	mustEmbedUnimplementedEventVNServer()
}

func RegisterEventVNServer(s *grpc.Server, srv EventVNServer) {
	s.RegisterService(&_EventVN_serviceDesc, srv)
}

func _EventVN_TrackingCleverTapPayComboSuccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapPayComboMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).TrackingCleverTapPayComboSuccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/TrackingCleverTapPayComboSuccess",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).TrackingCleverTapPayComboSuccess(ctx, req.(*eventMessage.TrackingCleverTapPayComboMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_TrackingCleverTapPayComboPaymentError_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapPayComboMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).TrackingCleverTapPayComboPaymentError(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/TrackingCleverTapPayComboPaymentError",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).TrackingCleverTapPayComboPaymentError(ctx, req.(*eventMessage.TrackingCleverTapPayComboMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_TrackingCleverTapTaskerAcceptTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).TrackingCleverTapTaskerAcceptTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/TrackingCleverTapTaskerAcceptTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).TrackingCleverTapTaskerAcceptTask(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_TrackingCleverTapTaskerCancelTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).TrackingCleverTapTaskerCancelTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/TrackingCleverTapTaskerCancelTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).TrackingCleverTapTaskerCancelTask(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_TrackingCleverTapTaskerWithdrawTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).TrackingCleverTapTaskerWithdrawTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/TrackingCleverTapTaskerWithdrawTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).TrackingCleverTapTaskerWithdrawTask(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_TrackingCleverTapTaskerDoneTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).TrackingCleverTapTaskerDoneTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/TrackingCleverTapTaskerDoneTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).TrackingCleverTapTaskerDoneTask(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_TrackingCleverTapTaskerRating_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapRatingMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).TrackingCleverTapTaskerRating(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/TrackingCleverTapTaskerRating",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).TrackingCleverTapTaskerRating(ctx, req.(*eventMessage.TrackingCleverTapRatingMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_TrackingCleverTapTaskerFinishTraining_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTrainingMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).TrackingCleverTapTaskerFinishTraining(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/TrackingCleverTapTaskerFinishTraining",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).TrackingCleverTapTaskerFinishTraining(ctx, req.(*eventMessage.TrackingCleverTapTrainingMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_TrackingCleverTapPrepayTaskPaymentResponse_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).TrackingCleverTapPrepayTaskPaymentResponse(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/TrackingCleverTapPrepayTaskPaymentResponse",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).TrackingCleverTapPrepayTaskPaymentResponse(ctx, req.(*eventMessage.TrackingCleverTapPrepayTaskPaymentResponseMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_AddAskerDoneBookingReferralGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(users.Users)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).AddAskerDoneBookingReferralGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/AddAskerDoneBookingReferralGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).AddAskerDoneBookingReferralGift(ctx, req.(*users.Users))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_AddAskerSignUpReferralGift_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(users.Users)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).AddAskerSignUpReferralGift(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/AddAskerSignUpReferralGift",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).AddAskerSignUpReferralGift(ctx, req.(*users.Users))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_PublishUserGameCampaignActionLogin_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).PublishUserGameCampaignActionLogin(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/PublishUserGameCampaignActionLogin",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).PublishUserGameCampaignActionLogin(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_PublishUserGameCampaignActionDoneTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).PublishUserGameCampaignActionDoneTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/PublishUserGameCampaignActionDoneTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).PublishUserGameCampaignActionDoneTask(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_PublishUserGameCampaignActionTaskerDoneTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).PublishUserGameCampaignActionTaskerDoneTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/PublishUserGameCampaignActionTaskerDoneTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).PublishUserGameCampaignActionTaskerDoneTask(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_PublishUserGameCampaignActionReferral_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).PublishUserGameCampaignActionReferral(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/PublishUserGameCampaignActionReferral",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).PublishUserGameCampaignActionReferral(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_TrackingCleverTapTaskExpired_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.TrackingCleverTapTaskMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).TrackingCleverTapTaskExpired(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/TrackingCleverTapTaskExpired",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).TrackingCleverTapTaskExpired(ctx, req.(*eventMessage.TrackingCleverTapTaskMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_PublishUserGameCampaignActionDoneTaskReferral_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).PublishUserGameCampaignActionDoneTaskReferral(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/PublishUserGameCampaignActionDoneTaskReferral",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).PublishUserGameCampaignActionDoneTaskReferral(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_PublishUserGameCampaignActionRollCall_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).PublishUserGameCampaignActionRollCall(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/PublishUserGameCampaignActionRollCall",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).PublishUserGameCampaignActionRollCall(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_PublishUserGameCampaignActionRollCallAtNoel_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).PublishUserGameCampaignActionRollCallAtNoel(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/PublishUserGameCampaignActionRollCallAtNoel",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).PublishUserGameCampaignActionRollCallAtNoel(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_PublishUserGameCampaignActionDoneTaskFirst_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).PublishUserGameCampaignActionDoneTaskFirst(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/PublishUserGameCampaignActionDoneTaskFirst",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).PublishUserGameCampaignActionDoneTaskFirst(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

func _EventVN_PublishUserGameCampaignActionNewSignUp_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(eventMessage.EventCommonMessage)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EventVNServer).PublishUserGameCampaignActionNewSignUp(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/grpcEventVN.EventVN/PublishUserGameCampaignActionNewSignUp",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EventVNServer).PublishUserGameCampaignActionNewSignUp(ctx, req.(*eventMessage.EventCommonMessage))
	}
	return interceptor(ctx, in, info, handler)
}

var _EventVN_serviceDesc = grpc.ServiceDesc{
	ServiceName: "grpcEventVN.EventVN",
	HandlerType: (*EventVNServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "TrackingCleverTapPayComboSuccess",
			Handler:    _EventVN_TrackingCleverTapPayComboSuccess_Handler,
		},
		{
			MethodName: "TrackingCleverTapPayComboPaymentError",
			Handler:    _EventVN_TrackingCleverTapPayComboPaymentError_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerAcceptTask",
			Handler:    _EventVN_TrackingCleverTapTaskerAcceptTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerCancelTask",
			Handler:    _EventVN_TrackingCleverTapTaskerCancelTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerWithdrawTask",
			Handler:    _EventVN_TrackingCleverTapTaskerWithdrawTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerDoneTask",
			Handler:    _EventVN_TrackingCleverTapTaskerDoneTask_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerRating",
			Handler:    _EventVN_TrackingCleverTapTaskerRating_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskerFinishTraining",
			Handler:    _EventVN_TrackingCleverTapTaskerFinishTraining_Handler,
		},
		{
			MethodName: "TrackingCleverTapPrepayTaskPaymentResponse",
			Handler:    _EventVN_TrackingCleverTapPrepayTaskPaymentResponse_Handler,
		},
		{
			MethodName: "AddAskerDoneBookingReferralGift",
			Handler:    _EventVN_AddAskerDoneBookingReferralGift_Handler,
		},
		{
			MethodName: "AddAskerSignUpReferralGift",
			Handler:    _EventVN_AddAskerSignUpReferralGift_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionLogin",
			Handler:    _EventVN_PublishUserGameCampaignActionLogin_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionDoneTask",
			Handler:    _EventVN_PublishUserGameCampaignActionDoneTask_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionTaskerDoneTask",
			Handler:    _EventVN_PublishUserGameCampaignActionTaskerDoneTask_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionReferral",
			Handler:    _EventVN_PublishUserGameCampaignActionReferral_Handler,
		},
		{
			MethodName: "TrackingCleverTapTaskExpired",
			Handler:    _EventVN_TrackingCleverTapTaskExpired_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionDoneTaskReferral",
			Handler:    _EventVN_PublishUserGameCampaignActionDoneTaskReferral_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionRollCall",
			Handler:    _EventVN_PublishUserGameCampaignActionRollCall_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionRollCallAtNoel",
			Handler:    _EventVN_PublishUserGameCampaignActionRollCallAtNoel_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionDoneTaskFirst",
			Handler:    _EventVN_PublishUserGameCampaignActionDoneTaskFirst_Handler,
		},
		{
			MethodName: "PublishUserGameCampaignActionNewSignUp",
			Handler:    _EventVN_PublishUserGameCampaignActionNewSignUp_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "eventVN.proto",
}
