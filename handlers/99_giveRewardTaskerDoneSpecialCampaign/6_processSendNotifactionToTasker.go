package giveRewardTaskerDoneSpecialCampaign

import (
	"github.com/spf13/cast"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelSpecialCampaign "gitlab.com/btaskee/go-services-model-v2/grpcmodel/specialCampaign"
	modelUser "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users"
	"go.mongodb.org/mongo-driver/bson"
)

func processMarkDoneAndNotiToTasker(tasker *modelUser.Users, specialCampaign *modelSpecialCampaign.SpecialCampaign, value int32, isInProcess bool) error {
	if !isInProcess {
		// trường hợp tasker chưa tham gia game campaign
		if value >= specialCampaign.Value {
			_, err := insertSpecialCampaignTransaction(tasker.XId, tasker.Phone, specialCampaign.XId, specialCampaign.Name, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED, lib.PERCENT_COMPLETE_PROCESS)
			if err != nil {
				return err
			}
		} else if isEqualOrGreaterThanHalf(value, specialCampaign.Value) {
			_, err := insertSpecialCampaignTransaction(tasker.XId, tasker.Phone, specialCampaign.XId, specialCampaign.Name, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_INPROCESS, lib.PERCENT_HALF_PROCESS)
			if err != nil {
				return err
			}
			// send noti
			sendNotification(tasker, "NOTIFICATION_MESSAGE_DONE_HALF_SPECIAL_CAMPAIGN_TITLE", "NOTIFICATION_MESSAGE_DONE_HALF_SPECIAL_CAMPAIGN_BODY", globalConstant.PAYLOAD_NAVIGATE_TO_NOTIFICATION_DETAIL, 25)
		} else if isEqualOrGreaterThanOneThird(value, specialCampaign.Value) {
			_, err := insertSpecialCampaignTransaction(tasker.XId, tasker.Phone, specialCampaign.XId, specialCampaign.Name, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_INPROCESS, lib.PERCENT_ONE_THIRD_PROCESS)
			if err != nil {
				return err
			}
			// send noti
			sendNotification(tasker, "NOTIFICATION_MESSAGE_DONE_ONE_THIRD_SPECIAL_CAMPAIGN_TITLE", "NOTIFICATION_MESSAGE_DONE_ONE_THIRD_SPECIAL_CAMPAIGN_BODY", globalConstant.PAYLOAD_NAVIGATE_TO_NOTIFICATION_DETAIL, 25)
		}
	} else if isInProcess {
		specialCampaignTransaction, err := getSpecialCampaignTransaction(bson.M{"taskerId": tasker.XId, "campaignId": specialCampaign.XId}, bson.M{"_id": 1, "status": 1, "percentProcess": 1})
		if specialCampaignTransaction == nil || err != nil {
			return err
		}

		// trường hợp tasker đã tham gia campaign
		if value >= specialCampaign.Value {
			err = updateStatusSpecialCampaignTransaction(specialCampaignTransaction.XId, globalConstant.SPECIAL_CAMPAIGN_TRANSACTION_STATUS_COMPLETED, globalConstant.CHANGES_HISTORY_KEY_REMIND_TASKER_PROCESS_SPECIAL_CAMPAIGN, specialCampaignTransaction, lib.PERCENT_COMPLETE_PROCESS)
			if err != nil {
				return err
			}
		} else if isEqualOrGreaterThanHalf(value, specialCampaign.Value) && specialCampaignTransaction.PercentProcess == lib.PERCENT_ONE_THIRD_PROCESS {
			//send noti
			sendNotification(tasker, "NOTIFICATION_MESSAGE_DONE_HALF_SPECIAL_CAMPAIGN_TITLE", "NOTIFICATION_MESSAGE_DONE_HALF_SPECIAL_CAMPAIGN_BODY", globalConstant.PAYLOAD_NAVIGATE_TO_NOTIFICATION_DETAIL, 25)
			query := bson.M{
				"$set": bson.M{
					"percentProcess": lib.PERCENT_HALF_PROCESS,
				},
				"$push": bson.M{
					"changeHistory": bson.M{
						"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						"content": bson.M{
							"oldPercentProcess": specialCampaignTransaction.PercentProcess,
							"newPercentProcess": lib.PERCENT_HALF_PROCESS,
						},
						"from": globalConstant.CHANGES_HISTORY_FROM_SYSTEM,
						"key":  globalConstant.CHANGES_HISTORY_KEY_REMIND_TASKER_PROCESS_SPECIAL_CAMPAIGN,
					},
				},
			}
			_, err := globalDataAccess.UpdateOneById(globalCollection.COLLECTION_TASKER_SPECIAL_CAMPAIGN_TRANSACTION[local.ISO_CODE], specialCampaignTransaction.XId, query)
			if err != nil {
				return err
			}
		}
	}

	return nil
}

func isEqualOrGreaterThanHalf(total, value int32) bool {
	return cast.ToFloat64(total) >= cast.ToFloat64(value)/2
}

func isEqualOrGreaterThanOneThird(total, value int32) bool {
	return cast.ToFloat64(total) >= cast.ToFloat64(value)/3
}
