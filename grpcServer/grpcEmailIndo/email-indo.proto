syntax = "proto3";
package grpcEmailIndo;

option go_package = "gitlab.com/btaskee/grpcServer/grpcEmailIndo";

import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailResponse/email-response.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/emailSending/email-sending.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/task/task.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/users/users.proto";

service EmailIndo {
  rpc EmailTest (emailSending.EmailSending) returns (emailResponse.EmailResponse) {
    option (google.api.http) = {
      get: "/api/v3/email-indo/test"
    };
  }
  rpc SendEmail (emailSending.EmailSending) returns (emailResponse.EmailResponse) {}
  rpc SendReceiptEmail (task.Task) returns (emailResponse.EmailResponse) {
    option (google.api.http) = {
      post: "/api/v3/email-indo/receipt"
      body: "*"
    };
  }
  rpc SendCancelFeeEmail (emailSending.EmailCancelFeeRequest) returns (emailResponse.EmailResponse) {}
  rpc SendRenewSubscriptionEmail (emailSending.EmailRequest) returns (emailResponse.EmailResponse) {
    option (google.api.http) = {
      post: "/api/v3/email-indo/renew-subscription"
      body: "*"
    };
  }
  rpc SendVerifyEmail (users.Users) returns (emailResponse.EmailResponse) {}
}