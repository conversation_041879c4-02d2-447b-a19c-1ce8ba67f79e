package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelJourneyLeaderBoardWeeklyReport "gitlab.com/btaskee/go-services-model-v2/grpcmodel/journeyLeaderBoardWeeklyReport"
	"go.mongodb.org/mongo-driver/bson"
)

func TestWeeklyResetLeaderboard(t *testing.T) {
	runLeaderBoard := apiSyncCron + "/auto-calculate-journey-leader-board"
	apiUrl := apiSyncCron + "/weekly-reset-leader-board"

	t.Run("1", func(t *testing.T) {
		ResetData()
		initJourneySettingTest()
		lastDate := globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1)

		// ================= Hồ Chí Minh =================== 1 - > 9
		CreateUser([]map[string]interface{}{
			{ // Asker
				"phone":  "0834567000",
				"name":   "Asker 00",
				"type":   globalConstant.USER_TYPE_ASKER,
				"avatar": "ava_0834567000.jpg",
			},
			{ // 00. tasker not in service channel
				"phone": "0834567800",
				"name":  "Tasker 00",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"avatar": "ava_0834567800.jpg",
			},
			{ // 01. tasker not in city support journey
				"phone": "0834567801",
				"name":  "Tasker 01",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Đắk Lắk",
					},
					{
						"city": "Hồ Chí Minh",
					},
				},
				"avatar": "ava_0834567801.jpg",
			},
			{ // 02. tasker not have point in day
				"phone": "0834567802",
				"name":  "Tasker 02",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"avatar": "ava_0834567802.jpg",
			},
			{ // 03. tasker upgrade to first level in day (TASKER JUST ACTIVE, NOT IN LEADER_BOARD YET)
				"phone": "0834567803",
				"name":  "Tasker 03",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV0",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV1",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
				"avatar": "ava_0834567803.jpg",
			},
			{ // 04. tasker upgrade to high level in day (HAVE BEEN IN LEADER_BOARD YET)
				"phone": "0834567804",
				"name":  "Tasker 04",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV2",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
				"avatar": "ava_0834567804.jpg",
			},
			{ // 05. tasker upgrade to first level in day (TASKER JUST ACTIVE, NOT IN LEADER_BOARD YET) + have point in day
				"phone": "0834567805",
				"name":  "Tasker 05",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV1",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV1",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
				"avatar": "ava_default.jpg",
			},
			{ // 06. tasker upgrade to high level in day (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567806",
				"name":  "Tasker 06",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV2",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
				"avatar": "ava_default.jpg",
			},
			{ // 07. tasker (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567807",
				"name":  "Tasker 07",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV1",
				},
				"avatar": "ava_default.jpg",
			},
			{ // 08. tasker (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567808",
				"name":  "Tasker 08",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
				},
				"avatar": "ava_default.jpg",
			},
			{ // 09. tasker (HAVE NOT BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567809",
				"name":  "Tasker 09",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"avatar": "ava_default.jpg",
			},
		})

		CreateJourneyLeaderBoard([]map[string]interface{}{
			{
				"_id":      "0834567804",
				"level":    "LV1",
				"rank":     1,
				"point":    19,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 04",
			},
			{
				"_id":      "0834567806",
				"level":    "LV1",
				"rank":     4,
				"point":    7,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 06",
			},
			{
				"_id":      "0834567807",
				"level":    "LV1",
				"rank":     2,
				"point":    9,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 07",
			},
			{
				"_id":      "0834567802",
				"level":    "LV1",
				"rank":     3,
				"point":    8,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 02",
			},
			{
				"_id":      "0834567808",
				"level":    "LV2",
				"rank":     1,
				"point":    2,
				"cityName": "Hồ Chí Minh",
				"name":     "Tasker 08",
			},
		})

		// Tasker 02: point = (1 + 1) + (2 + 2) = 6
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567802",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567802",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 05: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567805",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567805",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 06: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567806",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567806",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 07: point = (1 + 1 + 1) + (2 + 4 + 2) + (15 + 15 - 5) = 46
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567807",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567807",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567807",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})
		CreateRating([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567807",
				"rate":        5,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567807",
				"rate":        5,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567807",
				"rate":        3,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
		})

		// Tasker 08: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567808",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567808",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 09: point = (1) + (2) = 3
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567809",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// ================= Hà Nội =================== 10 -> 17
		CreateUser([]map[string]interface{}{
			{ // 10. tasker not have point in day
				"phone": "0834567810",
				"name":  "Tasker 10",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"avatar": "ava_default.jpg",
			},
			{ // 11. tasker upgrade to first level in day (TASKER JUST ACTIVE, NOT IN LEADER_BOARD YET)
				"phone": "0834567811",
				"name":  "Tasker 11",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV0",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV1",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
				"avatar": "ava_default.jpg",
			},
			{ // 12. tasker upgrade to high level in day (HAVE BEEN IN LEADER_BOARD YET)
				"phone": "0834567812",
				"name":  "Tasker 12",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV2",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
				"avatar": "ava_default.jpg",
			},
			{ // 13. tasker upgrade to first level in day (TASKER JUST ACTIVE, NOT IN LEADER_BOARD YET) + have point in day
				"phone": "0834567813",
				"name":  "Tasker 13",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV1",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV1",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
				"avatar": "ava_default.jpg",
			},
			{ // 14. tasker upgrade to high level in day (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567814",
				"name":  "Tasker 14",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
					"levelHistories": []map[string]interface{}{
						{
							"newLevel":  "LV2",
							"createdAt": globalLib.GetCurrentTime(local.TimeZone),
						},
					},
				},
				"avatar": "ava_default.jpg",
			},
			{ // 15. tasker (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567815",
				"name":  "Tasker 15",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV1",
				},
				"avatar": "ava_default.jpg",
			},
			{ // 16. tasker (HAVE BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567816",
				"name":  "Tasker 16",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"journeyInfo": map[string]interface{}{
					"level": "LV2",
				},
				"avatar": "ava_default.jpg",
			},
			{ // 17. tasker (HAVE NOT BEEN IN LEADER_BOARD YET) + have point in day
				"phone": "0834567817",
				"name":  "Tasker 17",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"avatar": "ava_default.jpg",
			},
		})

		CreateJourneyLeaderBoard([]map[string]interface{}{
			{
				"_id":      "0834567812",
				"level":    "LV1",
				"rank":     1,
				"point":    19,
				"cityName": "Hà Nội",
				"name":     "Tasker 12",
			},
			{
				"_id":      "0834567814",
				"level":    "LV1",
				"rank":     4,
				"point":    7,
				"cityName": "Hà Nội",
				"name":     "Tasker 14",
			},
			{
				"_id":      "0834567815",
				"level":    "LV1",
				"rank":     2,
				"point":    9,
				"cityName": "Hà Nội",
				"name":     "Tasker 15",
			},
			{
				"_id":      "0834567810",
				"level":    "LV1",
				"rank":     3,
				"point":    8,
				"cityName": "Hà Nội",
				"name":     "Tasker 10",
			},
			{
				"_id":      "0834567816",
				"level":    "LV2",
				"rank":     1,
				"point":    2,
				"cityName": "Hà Nội",
				"name":     "Tasker 16",
			},
			{
				"_id":       "0834567834",
				"level":     "LV2",
				"rank":      1,
				"point":     2,
				"cityName":  "Hà Nội",
				"name":      "Tasker 34",
				"createdAt": globalLib.GetCurrentTime(local.TimeZone).AddDate(0, 0, -1),
			},
		})

		// Tasker 10: point = (1 + 1) + (2 + 2) = 6
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567810",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567810",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 13: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567813",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567813",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 14: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567814",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567814",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 15: point = (1 + 1 + 1) + (2 + 4 + 2) + (5 + 5 - 3) = 18
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567815",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567815",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567815",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})
		CreateRating([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567815",
				"rate":        5,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567815",
				"rate":        5,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
			{
				"askerPhone":  "0834567000",
				"taskerPhone": "0834567815",
				"rate":        3,
				"createdAt":   fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
			},
		})

		// Tasker 16: point = (1 + 1) + (2 + 4) = 8
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567816",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567816",
					},
				},
				"duration": 4,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		// Tasker 17: point = (1) + (2) = 3
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567817",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})

		CreateUser([]map[string]interface{}{
			{ // 34. tasker not have point in day
				"phone": "0834567834",
				"name":  "Tasker 34",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hà Nội",
					},
				},
				"avatar": "ava_default.jpg",
			}, { // 35. tasker not have point in day
				"phone": "0834567835",
				"name":  "Tasker 35",
				"type":  globalConstant.USER_TYPE_TASKER,
				"workingPlaces": []map[string]interface{}{
					{
						"city": "Hồ Chí Minh",
					},
				},
				"avatar": "ava_default.jpg",
			},
		})

		// Tasker 34: point = (1) + (2) = 3
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567834",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hà Nội",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})
		// Tasker 34: point = (1) + (2) = 3
		CreateTask([]map[string]interface{}{
			{
				"askerPhone":  "0834567000",
				"serviceName": globalConstant.SERVICE_KEY_NAME_HOME_CLEANING,
				"description": "Desc Test %d",
				"address":     "Quận 1, Hồ Chí Minh, Việt Nam",
				"status":      globalConstant.TASK_STATUS_DONE,
				"date":        fmt.Sprintf("%d,%d,%d,0,0", lastDate.Year(), lastDate.Month(), lastDate.Day()),
				"acceptedTasker": []map[string]interface{}{
					{
						"taskerId": "0834567835",
					},
				},
				"duration": 2,
				"payment": map[string]interface{}{
					"method": globalConstant.PAYMENT_METHOD_CREDIT,
				},
				"cost": 400000,
				"costDetail": map[string]interface{}{
					"cost":         400000,
					"finalCost":    350000,
					"depositMoney": 200000,
					"promotionBy":  "TASKER",
				},
				"taskPlace": map[string]interface{}{
					"city":     "Hồ Chí Minh",
					"district": "Quận 1",
				},
				"isoCode": globalConstant.ISO_CODE_VN,
			},
		})
		// Add tasker to service channel VN
		taskerInServiceChannel := []string{
			"0834567801", "0834567802", "0834567803", "0834567804", "0834567805", "0834567806", "0834567807", "0834567808", "0834567809", // Hồ Chí Minh
			"0834567810", "0834567811", "0834567812", "0834567813", "0834567814", "0834567815", "0834567816", "0834567817", // Hà Nội
		}
		serviceSupportJourney := []string{globalConstant.SERVICE_KEY_NAME_HOME_CLEANING}
		AddTaskersToServiceChannel(globalConstant.ISO_CODE_VN, serviceSupportJourney, taskerInServiceChannel)

		AddTaskersToServiceChannel(globalConstant.ISO_CODE_VN, globalConstant.SERVICES_NOT_SUPPORT_JOURNEY, []string{"0834567834", "0834567835"})
		// Expect result
		/*
			Hồ Chí Minh
				LV1:
				1  |  0834567807  |  90.0 // 3 task 8 duration
				2  |  0834567806  |  51.0 // Journey LV2 but in leaderBoard LV1 => still LV1
				3  |  0834567805  |  44 // 2 task 6 duration
				4  |  0834567802  |  42 // 2 task 4 duration + 8
				5  |  0834567804  |  19 // old point
				6  |  0834567809  |  17.0 // 1 task 2 duration

				LV2:
				1  |  0834567808  |  46 // 2 task 6 duration 44 + 2

			Hà Nội
				LV1:
				1  |  0834567815  |  90.0 // 3 task 8 duration
				2  |  0834567814  |  51.0 // Journey LV2 but in leaderBoard LV1 => still LV1
				3  |  0834567813  |  44 // 2 task 6 duration
				4  |  0834567810  |  42 // 2 task 4 duration + 8
				5  |  0834567812  |  19 // old point
				6  |  0834567817  |  17.0 // 1 task 2 duration

				LV2:
				1  |  0834567816  |  46 // 2 task 6 duration 44 + 2

		*/

		Convey(fmt.Sprintf("When give a http request to %s isoCode VN", runLeaderBoard), t, func() {
			body := map[string]interface{}{
				"action":  lib.RUN_NOW,
				"isoCode": globalConstant.ISO_CODE_VN,
			}
			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", runLeaderBoard, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			Convey("When service handle the request", func() {
				service.NewRouter().ServeHTTP(res, req)

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// Check Hồ Chí Minh LV1
					cityName := "Hồ Chí Minh"
					level := "LV1"
					hcmLv1 := getLeaderBoardVN(cityName, level)
					So(len(hcmLv1), ShouldEqual, 6)
					So(hcmLv1[0].XId, ShouldEqual, "0834567807")
					So(hcmLv1[0].Point, ShouldEqual, 90)
					So(hcmLv1[0].RankChange, ShouldEqual, "UP")
					So(hcmLv1[1].XId, ShouldEqual, "0834567806")
					So(hcmLv1[1].Point, ShouldEqual, 51)
					So(hcmLv1[1].RankChange, ShouldEqual, "UP")
					So(hcmLv1[2].XId, ShouldEqual, "0834567805")
					So(hcmLv1[2].Point, ShouldEqual, 44)
					So(hcmLv1[2].RankChange, ShouldEqual, "UP")
					So(hcmLv1[3].XId, ShouldEqual, "0834567802")
					So(hcmLv1[3].Point, ShouldEqual, 42)
					So(hcmLv1[3].Avatar, ShouldEqual, "ava_0834567802.jpg")
					So(hcmLv1[3].RankChange, ShouldEqual, "DOWN")
					So(hcmLv1[4].XId, ShouldEqual, "0834567804")
					So(hcmLv1[4].Point, ShouldEqual, 19)
					So(hcmLv1[4].RankChange, ShouldEqual, "DOWN")
					So(hcmLv1[5].XId, ShouldEqual, "0834567809")
					So(hcmLv1[5].Point, ShouldEqual, 17)
					So(hcmLv1[5].RankChange, ShouldEqual, "UP")

					for _, v := range hcmLv1 {
						So(v.XId, ShouldNotBeIn, []string{"0834567834", "0834567835"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 1")
						So(v.Text.En, ShouldEqual, "Young Bee")
					}

					// Check Hồ Chí Minh LV2
					level = "LV2"
					hcmLv2 := getLeaderBoardVN(cityName, level)
					So(len(hcmLv2), ShouldEqual, 1)
					So(hcmLv2[0].XId, ShouldEqual, "0834567808")
					So(hcmLv2[0].Point, ShouldEqual, 46)
					So(hcmLv2[0].RankChange, ShouldEqual, "UNCHANGED")
					for _, v := range hcmLv2 {
						So(v.XId, ShouldNotBeIn, []string{"0834567834", "0834567835"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 2")
						So(v.Text.En, ShouldEqual, "Mature Bee")
						So(v.Avatar, ShouldNotBeEmpty)
					}

					// Check Hà Nội LV1
					cityName = "Hà Nội"
					level = "LV1"
					hnLv1 := getLeaderBoardVN(cityName, level)
					So(hnLv1[0].XId, ShouldEqual, "0834567815")
					So(hnLv1[0].Point, ShouldEqual, 90)
					So(hnLv1[0].RankChange, ShouldEqual, "UP")
					So(hnLv1[1].XId, ShouldEqual, "0834567814")
					So(hnLv1[1].Point, ShouldEqual, 51)
					So(hnLv1[1].RankChange, ShouldEqual, "UP")
					So(hnLv1[2].XId, ShouldEqual, "0834567813")
					So(hnLv1[2].Point, ShouldEqual, 44)
					So(hnLv1[2].RankChange, ShouldEqual, "UP")
					So(hnLv1[3].XId, ShouldEqual, "0834567810")
					So(hnLv1[3].Point, ShouldEqual, 42)
					So(hnLv1[3].RankChange, ShouldEqual, "DOWN")
					So(hnLv1[4].XId, ShouldEqual, "0834567812")
					So(hnLv1[4].Point, ShouldEqual, 19)
					So(hnLv1[4].RankChange, ShouldEqual, "DOWN")
					So(hnLv1[5].XId, ShouldEqual, "0834567817")
					So(hnLv1[5].Point, ShouldEqual, 17)
					So(hnLv1[5].RankChange, ShouldEqual, "UP")
					for _, v := range hnLv1 {
						So(v.XId, ShouldNotBeIn, []string{"0834567834", "0834567835"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 1")
						So(v.Text.En, ShouldEqual, "Young Bee")
						So(v.Avatar, ShouldNotBeEmpty)
					}

					// Check Ha Noi LV2
					level = "LV2"
					hnLv2 := getLeaderBoardVN(cityName, level)
					So(hnLv2[0].XId, ShouldEqual, "0834567816")
					So(hnLv2[0].Point, ShouldEqual, 46)
					So(hnLv2[0].RankChange, ShouldEqual, "UNCHANGED")
					for _, v := range hnLv2 {
						So(v.XId, ShouldNotBeIn, []string{"0834567834", "0834567835"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 2")
						So(v.Text.En, ShouldEqual, "Mature Bee")
						So(v.Avatar, ShouldNotBeEmpty)
					}
				})
			})
		})

		Convey(fmt.Sprintf("When give a http request to %s isoCode VN", apiUrl), t, func() {
			body := map[string]interface{}{
				"action":  lib.RUN_NOW,
				"isoCode": globalConstant.ISO_CODE_VN,
			}
			reqBody, _ := json.Marshal(body)

			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)

			Convey("When service handle the request", func() {

				Convey("Check the response", func() {
					So(res.Code, ShouldEqual, 200)
					result := map[string][]map[string]interface{}{}
					b, _ := ioutil.ReadAll(res.Body)
					json.Unmarshal(b, &result)

					// Check Hồ Chí Minh LV1
					cityName := "Hồ Chí Minh"
					level := "LV1"
					hcmLv1 := getLeaderBoardWeeklyReport(cityName, level)
					So(len(hcmLv1), ShouldEqual, 6)
					So(hcmLv1[0].TaskerId, ShouldEqual, "0834567807")
					So(hcmLv1[0].Point, ShouldEqual, 90)
					So(hcmLv1[1].TaskerId, ShouldEqual, "0834567806")
					So(hcmLv1[1].Point, ShouldEqual, 51)
					So(hcmLv1[2].TaskerId, ShouldEqual, "0834567805")
					So(hcmLv1[2].Point, ShouldEqual, 44)
					So(hcmLv1[3].TaskerId, ShouldEqual, "0834567802")
					So(hcmLv1[3].Point, ShouldEqual, 42)
					So(hcmLv1[3].Avatar, ShouldEqual, "ava_0834567802.jpg")
					So(hcmLv1[4].TaskerId, ShouldEqual, "0834567804")
					So(hcmLv1[4].Point, ShouldEqual, 19)
					So(hcmLv1[5].TaskerId, ShouldEqual, "0834567809")
					So(hcmLv1[5].Point, ShouldEqual, 17)

					for _, v := range hcmLv1 {
						So(v.TaskerId, ShouldNotBeIn, []string{"0834567834", "0834567835"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 1")
						So(v.Text.En, ShouldEqual, "Young Bee")
						So(v.Avatar, ShouldNotBeEmpty)
					}

					// Check Hồ Chí Minh LV2
					level = "LV2"
					hcmLv2 := getLeaderBoardWeeklyReport(cityName, level)
					So(len(hcmLv2), ShouldEqual, 1)
					So(hcmLv2[0].TaskerId, ShouldEqual, "0834567808")
					So(hcmLv2[0].Point, ShouldEqual, 46)
					for _, v := range hcmLv2 {
						So(v.TaskerId, ShouldNotBeIn, []string{"0834567834", "0834567835"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 2")
						So(v.Text.En, ShouldEqual, "Mature Bee")
						So(v.Avatar, ShouldNotBeEmpty)
					}

					// Check Hà Nội LV1
					cityName = "Hà Nội"
					level = "LV1"
					hnLv1 := getLeaderBoardWeeklyReport(cityName, level)
					So(hnLv1[0].TaskerId, ShouldEqual, "0834567815")
					So(hnLv1[0].Point, ShouldEqual, 90)
					So(hnLv1[1].TaskerId, ShouldEqual, "0834567814")
					So(hnLv1[1].Point, ShouldEqual, 51)
					So(hnLv1[2].TaskerId, ShouldEqual, "0834567813")
					So(hnLv1[2].Point, ShouldEqual, 44)
					So(hnLv1[3].TaskerId, ShouldEqual, "0834567810")
					So(hnLv1[3].Point, ShouldEqual, 42)
					So(hnLv1[4].TaskerId, ShouldEqual, "0834567812")
					So(hnLv1[4].Point, ShouldEqual, 19)
					So(hnLv1[5].TaskerId, ShouldEqual, "0834567817")
					So(hnLv1[5].Point, ShouldEqual, 17)
					for _, v := range hnLv1 {
						So(v.TaskerId, ShouldNotBeIn, []string{"0834567834", "0834567835"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 1")
						So(v.Text.En, ShouldEqual, "Young Bee")
						So(v.Avatar, ShouldNotBeEmpty)
					}

					// Check Ha Noi LV2
					level = "LV2"
					hnLv2 := getLeaderBoardWeeklyReport(cityName, level)
					So(hnLv2[0].TaskerId, ShouldEqual, "0834567816")
					So(hnLv2[0].Point, ShouldEqual, 46)
					for _, v := range hnLv2 {
						So(v.TaskerId, ShouldNotBeIn, []string{"0834567834", "0834567835"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 2")
						So(v.Text.En, ShouldEqual, "Mature Bee")
						So(v.Avatar, ShouldNotBeEmpty)
					}

					// Check Leader board
					cityName = "Hồ Chí Minh"
					level = "LV1"
					hcmLv1LB := getLeaderBoardVN(cityName, level)
					So(len(hcmLv1LB), ShouldEqual, 5)
					for _, v := range hcmLv1LB {
						So(v.XId, ShouldBeIn, []string{"0834567800", "0834567802", "0834567805", "0834567807", "0834567809"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 1")
						So(v.Text.En, ShouldEqual, "Young Bee")
						So(v.Point, ShouldEqual, 0)
						So(v.Rank, ShouldEqual, -1)
						So(v.Avatar, ShouldNotBeEmpty)
					}
					cityName = "Hồ Chí Minh"
					level = "LV2"
					hcmLv2LB := getLeaderBoardVN(cityName, level)
					So(len(hcmLv2LB), ShouldEqual, 3)
					for _, v := range hcmLv2LB {
						So(v.XId, ShouldBeIn, []string{"0834567804", "0834567806", "0834567808"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 2")
						So(v.Point, ShouldEqual, 0)
						So(v.Rank, ShouldEqual, -1)
						So(v.Avatar, ShouldNotBeEmpty)
					}

					// Check Leader board
					cityName = "Hà Nội"
					level = "LV1"
					hnv1LB := getLeaderBoardVN(cityName, level)
					So(len(hnv1LB), ShouldEqual, 4)
					for _, v := range hnv1LB {
						So(v.XId, ShouldBeIn, []string{"0834567810", "0834567813", "0834567815", "0834567817"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 1")
						So(v.Text.En, ShouldEqual, "Young Bee")
						So(v.Point, ShouldEqual, 0)
						So(v.Rank, ShouldEqual, -1)
						So(v.Avatar, ShouldNotBeEmpty)
					}
					cityName = "Hà Nội"
					level = "LV2"
					hnLv2LB := getLeaderBoardVN(cityName, level)
					So(len(hnLv2LB), ShouldEqual, 3)
					for _, v := range hnLv2LB {
						So(v.XId, ShouldBeIn, []string{"0834567812", "0834567814", "0834567816"})
						So(v.CityName, ShouldEqual, cityName)
						So(v.Level, ShouldEqual, level)
						So(v.Icon, ShouldNotBeEmpty)
						So(v.Name, ShouldNotBeEmpty)
						So(v.Title.En, ShouldEqual, "Level 2")
						So(v.Point, ShouldEqual, 0)
						So(v.Rank, ShouldEqual, -1)
						So(v.Avatar, ShouldNotBeEmpty)
					}
				})
			})
		})
	})
}

func getLeaderBoardWeeklyReport(cityName, level string) (result []*modelJourneyLeaderBoardWeeklyReport.JourneyLeaderBoardWeeklyReport) {
	globalDataAccess.GetAllByQuerySort(globalCollection.COLLECTION_JOURNEY_LEADER_BOARD_WEEKLY_REPORT[local.ISO_CODE], bson.M{"cityName": cityName, "level": level}, bson.M{}, bson.M{"rank": 1}, &result)
	return result
}
