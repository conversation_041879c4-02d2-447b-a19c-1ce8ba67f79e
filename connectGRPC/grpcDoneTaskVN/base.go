package grpcDoneTaskVN

import (
	"gitlab.com/btaskee/go-services-model-v2/connectGRPC"
	"gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcDoneTaskVN"
	"google.golang.org/grpc"
)

func ConnectGRPCDoneTaskVN(endpoint string) (grpcDoneTaskVN.DoneBookingVNClient, *grpc.ClientConn, error) {
	connect, err := connectGRPC.InitGRPC(endpoint)
	if err != nil {
		return nil, nil, err
	}
	client := grpcDoneTaskVN.NewDoneBookingVNClient(connect)
	return client, connect, err
}
