package testing

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http/httptest"
	"testing"

	. "github.com/smartystreets/goconvey/convey"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/lib"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/service"
	"gitlab.com/btaskee/go-services-model-v2/globalCollection"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	globalDataAccess "gitlab.com/btaskee/go-services-model-v2/globalDataAccess/vn"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelNotification "gitlab.com/btaskee/go-services-model-v2/grpcmodel/notification"
	"go.mongodb.org/mongo-driver/bson"
)

func TestVoucherComboSubscriptionNotify(t *testing.T) {
	apiUrl := "/api/v3/sync-cron-vn/voucher-combo-subscription-notify"
	t.Run("1", func(t *testing.T) {
		ResetData()

		fAIds := CreateFinancialAccount([]map[string]interface{}{
			{
				"FMainAccount": 90000,
			}, {
				"FMainAccount": 1000,
			}, {
				"FMainAccount": 1000000,
			},
		})
		CreateUser([]map[string]interface{}{
			{
				"phone":      "**********",
				"name":       "Asker 01",
				"type":       globalConstant.USER_TYPE_ASKER,
				"fAccountId": fAIds[0],
			},
			{
				"phone":      "**********",
				"name":       "Asker 02",
				"type":       globalConstant.USER_TYPE_ASKER,
				"fAccountId": fAIds[1],
			},
			{
				"phone":      "**********",
				"name":       "Asker 03",
				"type":       globalConstant.USER_TYPE_ASKER,
				"fAccountId": fAIds[2],
			},
		})
		currentTime := globalLib.GetCurrentTime(local.TimeZone)
		ids := CreateComboVoucher([]map[string]interface{}{
			{
				"status": "ACTIVE",
				"cost":   100000,
				"price":  20000,
				"title": map[string]interface{}{
					"vi": "Title1",
					"en": "Title1",
				},
				"content": map[string]interface{}{
					"vi": "Tiết kiệm tới 20000 vnđ",
					"en": "Tiết kiệm tới 20000 vnđ",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, 1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"vouchers": []map[string]interface{}{
					{
						"_id":   "voucher1",
						"image": "combo1-1",
						"title": map[string]interface{}{
							"vi": "title-voucher1",
							"en": "title-voucher1",
							"ko": "title-voucher1",
							"th": "title-voucher1",
							"id": "title-voucher1",
						},
						"promotion": map[string]interface{}{
							"type":               "PERCENTAGE",
							"value":              0.2,
							"maxValue":           30000,
							"numberOfDayDueDate": 30,
							"prefix":             "ABC123",
						},
						"from":     "SYSTEM",
						"quantity": 2,
					}, {
						"_id":   "voucher2",
						"image": "xxx",
						"title": map[string]interface{}{
							"en": "title-voucher2",
						},
						"promotion": map[string]interface{}{
							"type":               "MONEY",
							"value":              30000,
							"numberOfDayDueDate": 30,
						},
						"applyFor": map[string]interface{}{
							"cities":        []string{},
							"services":      []string{"pcZRQ6PqmjrAPe5gt"},
							"isAllUsers":    false,
							"isSharePublic": false,
						},
						"from":               "SYSTEM",
						"quantity":           1,
						"numberOfDayDueDate": 30,
					},
				},
				"isSubscription":     true,
				"numberOfDayDueDate": 30,
			},
			{
				"status": "ACTIVE",
				"cost":   100000,
				"price":  80000,
				"title": map[string]interface{}{
					"vi": "Title2",
					"en": "Title2",
				},
				"content": map[string]interface{}{
					"vi": "Tiết kiệm tới 20000 vnđ",
					"en": "Tiết kiệm tới 20000 vnđ",
				},
				"createdAt": currentTime,
				"startDate": currentTime.AddDate(0, 0, 1),
				"endDate":   currentTime.AddDate(0, 0, 31),
				"vouchers": []map[string]interface{}{
					{
						"_id":   "voucher1",
						"image": "combo1-1",
						"title": map[string]interface{}{
							"vi": "title-voucher1",
							"en": "title-voucher1",
							"ko": "title-voucher1",
							"th": "title-voucher1",
							"id": "title-voucher1",
						},
						"promotion": map[string]interface{}{
							"type":               "PERCENTAGE",
							"value":              0.2,
							"maxValue":           30000,
							"numberOfDayDueDate": 30,
							"prefix":             "ABC123",
						},
						"from":     "SYSTEM",
						"quantity": 2,
					}, {
						"_id":   "voucher2",
						"image": "xxx",
						"title": map[string]interface{}{
							"en": "title-voucher2",
						},
						"promotion": map[string]interface{}{
							"type":               "MONEY",
							"value":              30000,
							"numberOfDayDueDate": 30,
						},
						"applyFor": map[string]interface{}{
							"cities":        []string{},
							"services":      []string{"pcZRQ6PqmjrAPe5gt"},
							"isAllUsers":    false,
							"isSharePublic": false,
						},
						"from":               "SYSTEM",
						"quantity":           1,
						"numberOfDayDueDate": 30,
					},
				},
				"isSubscription":     true,
				"numberOfDayDueDate": 30,
			},
		})

		endDate := globalLib.EndADay(currentTime.AddDate(0, 0, 3))
		CreateUserComboVoucher([]map[string]interface{}{
			{
				"userId":         "**********",
				"comboVoucherId": ids[0],
				"isSubscription": true,
				"status":         "ACTIVE",
				"expiredDate":    endDate,
				"createdAt":      currentTime.AddDate(0, 0, -30),
			}, {
				"userId":         "**********",
				"comboVoucherId": ids[1],
				"isSubscription": true,
				"status":         "ACTIVE",
				"expiredDate":    endDate,
				"createdAt":      currentTime.AddDate(0, 0, -31),
			}, {
				"userId":         "**********",
				"comboVoucherId": ids[1],
				"isSubscription": true,
				"status":         "ACTIVE",
				"expiredDate":    endDate,
				"createdAt":      currentTime.AddDate(0, 0, -31),
			}, {
				"userId":         "**********",
				"comboVoucherId": ids[0],
				"isSubscription": true,
				"status":         "ACTIVE",
				"expiredDate":    endDate,
				"createdAt":      currentTime.AddDate(0, 0, -31),
			},
		})

		body := map[string]interface{}{
			"action": lib.RUN_NOW,
		}

		reqBody, _ := json.Marshal(body)
		Convey(fmt.Sprintf("Given HTTP request for api %s", apiUrl), t, func() {
			req := httptest.NewRequest("POST", apiUrl, bytes.NewBuffer(reqBody))
			res := httptest.NewRecorder()
			service.NewRouter().ServeHTTP(res, req)
			Convey("Check the response and database", func() {
				// Check data
				So(res.Code, ShouldEqual, 200)

				var notifications []*modelNotification.Notification
				globalDataAccess.GetAllByQuery(globalCollection.COLLECTION_NOTIFICATION[globalConstant.ISO_CODE_VN], bson.M{}, bson.M{}, &notifications)

				So(len(notifications), ShouldEqual, 2)
				tested := map[string]bool{}
				for _, noti := range notifications {
					switch noti.UserId {
					case "**********":
						So(noti.Title, ShouldEqual, "Gói ưu đãi [Title1] của bạn sắp hết hạn!")
						So(noti.Description, ShouldEqual, "Gói [Title1] của bạn sắp đến thời điểm gia hạn! Nạp bPay ngay để tiếp tục sử dụng các ưu đãi hấp dẫn.")
						So(noti.NavigateTo, ShouldEqual, globalConstant.PAYLOAD_NAVIGATE_TO_USER_BPAY)
						tested[noti.UserId] = true
					case "**********":
						So(noti.Title, ShouldEqual, "Gói ưu đãi [Title2] của bạn sắp hết hạn!")
						So(noti.Description, ShouldEqual, "Gói [Title2] của bạn sắp đến thời điểm gia hạn! Nạp bPay ngay để tiếp tục sử dụng các ưu đãi hấp dẫn.")
						So(noti.NavigateTo, ShouldEqual, globalConstant.PAYLOAD_NAVIGATE_TO_USER_BPAY)
						tested[noti.UserId] = true
					}
				}

				So(tested["**********"], ShouldBeTrue)
				So(tested["**********"], ShouldBeTrue)
			})
		})
	})
}
