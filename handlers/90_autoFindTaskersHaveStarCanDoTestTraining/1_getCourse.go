package autoFindTaskersHaveStarCanDoTestTraining

import (
	"fmt"

	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/handlers"
	"gitlab.com/btaskee/btaskee-go/go-sync-cron-vn-v3/local"
	"gitlab.com/btaskee/go-services-model-v2/globalConstant"
	"gitlab.com/btaskee/go-services-model-v2/globalLib"
	modelTrainingV2 "gitlab.com/btaskee/go-services-model-v2/grpcmodel/trainingTaskerV2"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

func getCourses() []*modelTrainingV2.Course {
	var trainingTest []*modelTrainingV2.Course
	query := bson.M{
		"status": globalConstant.TRAINING_COURSE_STATUS_ACTIVE,
		"$or": []bson.M{
			{"condition.byTasker.minimumStar": bson.M{"$exists": true}},
			{"condition": bson.M{"$exists": false}},
		},
	}
	fields := bson.M{
		"_id":                     1,
		"title":                   1,
		"timeToCompleteByMinutes": 1,
		"quizCollections":         1,
		"maximumNumberOfRetries":  1,
		"relatedServices":         1,
		"condition":               1,
		"deadlineIn":              1,
		"type":                    1,
		"cities":                  1,
	}
	trainingTest, err := handlers.GetCourses(query, fields)
	if err != nil && err != mongo.ErrNoDocuments {
		text := fmt.Sprintf("AutoFindTaskersHaveStarCanDoTestTraining %s: Lỗi getCourses, err: %s ", local.ISO_CODE, err.Error())
		globalLib.PostToSlackViaBot(cfg.SlackToken, globalConstant.GO_SERVICES_SLACK_CHANNEL[local.ISO_CODE], text)
		return nil
	}
	return trainingTest
}
