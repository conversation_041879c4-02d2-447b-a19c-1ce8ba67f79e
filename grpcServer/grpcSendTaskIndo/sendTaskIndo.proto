syntax = "proto3";
package grpcSendTaskIndo;

option go_package = "gitlab.com/btaskee/go-services-model-v2/grpcServer/grpcSendTaskIndo";

import "google/api/annotations.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/pushNotificationNewTask/pushNotificationNewTask.proto";
import "gitlab.com/btaskee/go-services-model-v2/grpcmodel/response/response.proto";

service SendTaskIndo {
  rpc NewTask (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-indo/new-task"
      body: "*"
    };
  }
  rpc TopTasker (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-indo/top-tasker"
      body: "*"
    };
  }
  rpc NewTaskToDistrict (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-indo/district"
      body: "*"
    };
  }
  rpc FavTasker (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-indo/favorite-tasker"
      body: "*"
    };
  }
  rpc Normal (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-indo/normal"
      body: "*"
    };
  }
  rpc FavAndTaskerDistrict (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-indo/favorite-and-tasker-district"
      body: "*"
    };
  }
  rpc FavAndTopTasker (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-indo/favorite-and-top-tasker"
      body: "*"
    };
  }
  rpc NewTaskToCity (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-indo/city"
      body: "*"
    };
  }
  rpc ForceTasker (pushNotificationNewTask.NewTaskRequest) returns (response.Response) {
    option (google.api.http) = {
      post: "/api/v3/send-task-indo/force-tasker"
      body: "*"
    };
  }
}